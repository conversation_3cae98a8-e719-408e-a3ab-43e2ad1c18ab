/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/render'], function(https, render) {

    function onRequest(context) {
        poId = context.request.parameters.custpage_po_id;

        log.debug({
            title: 'Purchase Order ID',
            details: poId
        });

        var test = render.transaction({
            entityId: 2854,
            printMode: render.PrintMode.PDF
        });

        
        context.response.writeFile({
            file: test,
            isInline: true
        });

    }

    return {
        onRequest: onRequest
    }
});
