/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function beforeLoad(context) {
        var newRecObj = context.newRecord;
        var createdFromID = newRecObj.getValue({ fieldId: 'createdfrom' });
        if(createdFromID != '') {
            var createdFromObj = record.load({
                type: record.Type.SALES_ORDER,
                id: createdFromID,
                isDynamic: true
            });

            var lineCount = newRecObj.getLineCount({
                sublistId: 'item'
            });

            for(var i = 0; i < lineCount; i++) {
                // var itemId = newRecObj.getSublistValue({
                //     sublistId: 'item',
                //     fieldId: 'item',
                //     line: i
                // });

                // var line = createdFromObj.findSublistLineWithValue({
                //     sublistId: 'item',
                //     fieldId: 'item',
                //     value: itemId
                // });
                
                var quantity = createdFromObj.getSublistValue({
                    sublistId: 'item',
                    fieldId: 'quantity',
                    line: i
                });

                newRecObj.setSublistValue({
                    sublistId: 'item',
                    fieldId: 'quantity',
                    line: i,
                    value: quantity
                });
            }

        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
