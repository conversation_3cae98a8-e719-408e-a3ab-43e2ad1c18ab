/**
 * Copyright (c) 1998-2019 NetSuite, Inc. 2955 Campus Drive, Suite 100, San
 * Mateo, CA, USA 94403-2511 All Rights Reserved.
 *
 * This software is the confidential and proprietary information of NetSuite,
 * Inc. ("Confidential Information"). You shall not disclose such Confidential
 * Information and shall use it only in accordance with the terms of the license
 * agreement you entered into with NetSuite.
 */

/**
 * Description:
 * This suitelet is used to search for customers item fulfillmets(IF), that are
 * shown in sublist. IF with marked check box are printed using advanced PDF
 * template
 *
 * Version   Date          Author        Remarks
 * 1.00      Feb 15, 2018  shradela
 * 1.01      Feb 19, 2018  mkandar       post method - printing pdf
 * 1.02      May 01, 2018  mkandar       post method to get method
 * 1.10      May 31, 2019  lastalos      improved UI, printing logic in library
 * 1.20      June 06, 2019  mkandar      transfer order added
 */

/**
 * @NApiVersion 2.0
 * @NScriptType Suitelet
 * @NModuleScope SameAccount
 */
 define(['N/record', 'N/runtime', 'N/search', 'N/ui/serverWidget', 'N/format', 'N/render', 'N/file', 'N/xml', 'N/task', 'N/url', './bol_lib'],
 /**
  * @param {record}
  *            record
  * @param {runtime}
  *            runtime
  * @param {search}
  *            search
  * @param {serverWidget}
  *            serverWidget
  */
 function(record, runtime, search, serverWidget, format, render, file, xml, task, url, bol) {

     // limit of selected IFs, if selected more IFs than SSLIMIT, Schedule script will be triggered
     var SSLIMIT = 70;

     /**
      * Definition of the Suitelet script trigger point.
      *
      * @param {Object}
      *            context
      * @param {ServerRequest}
      *            context.request - Encapsulation of the incoming request
      * @param {ServerResponse}
      *            context.response - Encapsulation of the Suitelet response
      * @Since 2015.2
      */
     function onRequest(context) {
         try {

             // Load request and response
             var objRequest = context.request;
             var objResponse = context.response;
             // var objRequest = context.request;

             var objScript = runtime.getCurrentScript();

             var intBolFileIdParam = objScript.getParameter({
                 name: "custscript_file_cab_id"
             });
             var intBolNumber = objScript.getParameter({
                 name: "custscript_ns_master_bol_numbering"
             });

             var intSLDepIdParam = objScript.getParameter({
                 name: "custscript_ns_sl_deployment_id"
             });
             var stSalesOrder = objScript.getParameter({
               name: "custscript_sales_order_dropdown"
             });
             var stItemFulfillment = objScript.getParameter({
               name: "custscript_item_fulfillment_dropdown"
             });
             var intPrint = context.request.parameters.weightunit;
             var bTransferOrder = objRequest.parameters.bTransferOrder;
             var printBoL = objRequest.parameters.printBoL;
             var strPrintingOption = objRequest.parameters.printradio;
             var bSaveToCabinet = objRequest.parameters.savetofilecabinet;
             var bMasterBol = objRequest.parameters.masterbol;
             var strMasterNotes = objRequest.parameters.masterbolnotes;
             var intWeightUnit = objRequest.parameters.weightunit;
             var strWeightUnit = objRequest.parameters.strweightunit;
             var arrSelectedIf = objRequest.parameters.selectedIFs
             if (arrSelectedIf) {
                 arrSelectedIf = arrSelectedIf.split(",")
             };
             // get client id from suitelet field
             var intCustId = objRequest.parameters.customerID;
               var intIFId = objRequest.parameters.intIFId; // Added by Manoj
               var intSOId = objRequest.parameters.intSOId; // Added by Manoj
             var bReprint = objRequest.parameters.reprint;
             var strStatus = objRequest.parameters.status;
             var bMasterBol = objRequest.parameters.masterbol;
             // load begin and end date from request parameters
             var beginDate = objRequest.parameters.beginDate;
             var endDate = objRequest.parameters.endDate;
             var stWeightUnit = objRequest.parameters.weightunit;

             // GET method part
             if (context.request.method === 'GET') {

                 if (printBoL == "T") {
                     log.debug("BoL", "PRINT BOL");

                     var strToPrint = "";
                     var intCounter = 0;
                     var intarrSelectedIfLen = arrSelectedIf.length;
                     var arrMasters = [];

                     // if intarrSelectedIfLen > 70 craete schedule script and save file to file cabinet
                     if (intarrSelectedIfLen > SSLIMIT) {

                         // create BoL and save to File Cabinet
                         var objScheduleScriptTask = task.create({
                             taskType: task.TaskType.SCHEDULED_SCRIPT
                         });
                         objScheduleScriptTask.scriptId = "customscript_ns_ss_print_bol";
                         objScheduleScriptTask.deploymentId = "customdeploy_ns_ss_print_bol";
                         objScheduleScriptTask.params = {
                             "custscript_bol_master_type": bMasterBol,
                             "custscript_ns_bol_selected_ifs": arrSelectedIf.toString(),
                             "custscript_ns_bol_master_notes": strMasterNotes,
                             "custscript_ns_bol_weight_units": intWeightUnit,
                             "custscript_ns_bol_weight_unit_str": strWeightUnit,
                             "custscript_ns_bol_master_num": intBolNumber,
                             "custscript_ss_file_cab_id": intBolFileIdParam,
                             "custscript_ns_intid_sl_deploy": intSLDepIdParam

                         };
                         var objScheduleScriptTaskID = objScheduleScriptTask.submit();

                     } else {
                         // this setting will be used later to downlod file or print on screen
                         if (strPrintingOption == "print") {
                             var bPrintingOption = true;
                         } else {
                             bPrintingOption = false;
                         }
                         // If true == Master Bol creation starts
                         if (bMasterBol != "standalonebol") {
                             // /////master start/////
                             log.debug("Master Start");
                             // --- lib start----
                             strToPrint += bol.createMasterBol(arrSelectedIf, arrMasters, strMasterNotes, intCounter, intWeightUnit,
                                 strWeightUnit, bMasterBol, intBolNumber, intSLDepIdParam);
                             // --- lib end----

                             strToPrint += "</pdfset>";
                             log.debug('strToPrint', JSON.stringify(strToPrint));
                             var pdfFile = render.xmlToPdf({
                                 xmlString: strToPrint
                             });
                             var strCurrentdate = new Date().toISOString();
                             var strPdfName = "Master_BoL_" + strCurrentdate + ".pdf";

                             pdfFile.name = strPdfName;

                             try {
                                 if (bSaveToCabinet == "true") {
                                     pdfFile.folder = intBolFileIdParam; // 748
                                     var intFileId = pdfFile.save();
                                 }
                             } catch (e) {
                                 customLog(e);
                                 var fileObj = file.create({
                                     name: 'BoL_error.txt',
                                     fileType: file.Type.PLAINTEXT,
                                     contents: e.message,
                                     description: 'Error message during BoL saving',
                                     encoding: file.Encoding.UTF8,
                                     folder: intBolFileIdParam
                                 });
                                 fileObj.save();
                                 log.error('Error Submit IF reprint', e.message);
                             }

                             try {
                                 for (var i = 0; i < intarrSelectedIfLen; i++) {

                                     record.submitFields({
                                         type: record.Type.ITEM_FULFILLMENT,
                                         id: arrSelectedIf[i],
                                         values: {
                                             custbody_ns_bol_printed: true
                                         }
                                     });

                                 }

                             } catch (e) {
                                 customLog(e);
                                 log.error('Error Submit IF reprint', e.message);
                             }

                             context.response.writeFile({
                                 file: pdfFile,
                                 isInline: bPrintingOption
                             });

                             // master end///
                         } else {

                             /*
                                 Print Standalone Bols ONLY
                             */

                             var strToPrint = "";
                             var intCounter = 0;
                             for (var i = 0; i < intarrSelectedIfLen; i++) {
                                 strToPrint += bol.createSingleBol(arrSelectedIf[i], intCounter);
                                 intCounter++;
                             } // for
                             strToPrint += "</pdfset>";
                             var pdfFile = render.xmlToPdf({
                                 xmlString: strToPrint
                             });
                             var strCurrentdate = new Date().toISOString();
                             var strPdfName = "BoL_" + strCurrentdate + ".pdf";
                             pdfFile.name = strPdfName;

                             try {
                                 if (bSaveToCabinet == "true") {
                                     pdfFile.folder = intBolFileIdParam; // 748
                                     var intFileId = pdfFile.save();
                                 }
                             } catch (e) {
                                 customLog(e);
                                 var fileObj = file.create({
                                     name: 'BoL_error.txt',
                                     fileType: file.Type.PLAINTEXT,
                                     contents: e.message,
                                     description: 'Error message during BoL saving',
                                     encoding: file.Encoding.UTF8,
                                     folder: intBolFileIdParam
                                 });
                                 fileObj.save();
                                 log.error('Error Submit IF reprint', e.message);
                             }

                             try {
                                 for (var i = 0; i < intarrSelectedIfLen; i++) {

                                     record.submitFields({
                                         type: record.Type.ITEM_FULFILLMENT,
                                         id: arrSelectedIf[i],
                                         values: {
                                             custbody_ns_bol_printed: true
                                         }
                                     });
                                 }

                             } catch (e) {
                                 customLog(e);
                                 log.error('Error Submit IF reprint', e.message);
                             }

                             context.response.writeFile({
                                 file: pdfFile,
                                 isInline: bPrintingOption
                             });
                         }
                     }
                 } else {

                     /*
                            SL Ui search and print options
                     */
                     log.debug("BoL", "Search Bol");
                     var objForm = serverWidget.createForm({
                         title: 'Print Bill of Lading'
                     });

                     var stClientScriptId = objScript.getParameter({
                         name: "custscript_ns_client_for_sl_id"
                     });
                     log.debug("intClientScriptId", intClientScriptId);

                     // create search to find client script
                     var searchScript = search.create({
                         type: 'script',
                         filters: [
                             ['scriptid', 'startswith', stClientScriptId]
                         ],
                         columns: [search.createColumn({
                             name: 'name',
                             sort: search.Sort.ASC
                         }), 'scriptid', 'scriptfile']
                     });

                     // get search result
                     var arrResults = searchScript.run().getRange({
                         start: 0,
                         end: 1
                     });

                     // get client script ID
                     var intClientScriptId = parseInt(arrResults[0].getValue({
                         name: 'scriptfile'
                     }));

                     // attach client script to suitelet
                     objForm.clientScriptFileId = intClientScriptId;

                     objForm.addButton({
                         id: 'custpage_searchButton',
                         label: 'Print',
                         functionName: 'printBoL'
                         // functionName : "saveRecord"
                     });

                     // add search button
                     objForm.addButton({
                         id: 'custpage_searchButton',
                         label: 'Search',
                         functionName: 'openSuitelet'
                     });

                     var fieldgroup = objForm.addFieldGroup({
                         id: 'searchgroupid',
                         label: 'Search Options'
                     });

                     // add SELECT field containing customers
                     var fieldCustomers = objForm.addField({
                         id: 'custpage_customerlistfield',
                         type: serverWidget.FieldType.SELECT,
                         label: 'Select Customer',
                         source: 'CUSTOMER',
                         container: "searchgroupid"
                     });

                     // update layout position for CUSTOMERS field
                     fieldCustomers.updateLayoutType({
                         layoutType: serverWidget.FieldLayoutType.OUTSIDE
                     });

                     // set CUSTOMERS field as mandatory
                     if (bTransferOrder == "false") {
                         //fieldCustomers.isMandatory = true;
                     }
                   
                    
                   
                     fieldCustomers.updateDisplayType({
                         displayType : serverWidget.FieldDisplayType.HIDDEN
                     });
                     //************************************* SALES ORDER */
                     // add MULTI SELECT field containing SALES ORDER
                     var fieldSalesOrders = objForm.addField({
                         id: 'custpage_salesorderlistfield',
                         type: serverWidget.FieldType.MULTISELECT,
                         label: 'Select Sales Order',
                     //	source: 'SALESORDER',
                         container: "searchgroupid"
                     });

                     // update layout position for CUSTOMERS field
                     fieldCustomers.updateLayoutType({
                         layoutType: serverWidget.FieldLayoutType.OUTSIDE
                     });
                     //***********END */

                     var searchSalesOrder = search.load({ 
                         id: stSalesOrder 
                     });

                     var searchSalesOrderResult = searchSalesOrder.run().getRange({
                         start: 0,
                         end: 1000
                     });

                     for (var j = 0; j < searchSalesOrderResult.length; j++) 
                     {
                         var strCustomerID = searchSalesOrderResult[j].getValue(searchSalesOrder.columns[0]);
                         var strCustomerName = searchSalesOrderResult[j].getValue(searchSalesOrder.columns[1]);

                         if (j==0) 
                         {
                             fieldSalesOrders.addSelectOption({
                                 value: '',
                                 text: ''
                             });
                         }

                         if ((strCustomerID) && (strCustomerName)) 
                         {
                             fieldSalesOrders.addSelectOption({
                                 value: strCustomerID,
                                 text: strCustomerName
                             });
                         }
                     }
             
                   
                     //************************************* ITEM FULFILLMENT */
                     // add MULTI SELECT field containing SALES ORDER
                     var fieldItemFulfillment = objForm.addField({
                         id: 'custpage_itemfulfillmentlistfield',
                         type: serverWidget.FieldType.MULTISELECT,
                         label: 'Select Item Fulfillment',
                         //source: 'ITEMFULFILLMENT',
                         container: "searchgroupid"
                     });

                     // update layout position for CUSTOMERS field
                     fieldCustomers.updateLayoutType({
                         layoutType: serverWidget.FieldLayoutType.OUTSIDE
                     });
                     //***********END */

                   
                     var searchItemFulfillment = search.load({ 
                         id: stItemFulfillment 
                     });

                     var searchItemFulfillmentResult = searchItemFulfillment.run().getRange({
                         start: 0,
                         end: 1000
                     });

                     for (var j = 0; j < searchItemFulfillmentResult.length; j++) 
                     {
                         var strCustomerID = searchItemFulfillmentResult[j].getValue(searchItemFulfillment.columns[0]);
                         var strCustomerName = searchItemFulfillmentResult[j].getValue(searchItemFulfillment.columns[1]);

                         if (j==0) 
                         {
                             fieldItemFulfillment.addSelectOption({
                                 value: '',
                                 text: ''
                             });
                         }

                         if ((strCustomerID) && (strCustomerName)) 
                         {
                             fieldItemFulfillment.addSelectOption({
                                 value: strCustomerID,
                                 text: strCustomerName
                             });
                         }
                     }
                     // Include transfer Order
                     var fieldTransferOrder = objForm.addField({
                         id: 'custpage_transferorder',
                         type: serverWidget.FieldType.CHECKBOX,
                         label: 'Include Transfer Orders',
                         container: 'searchgroupid'
                     });
                     fieldTransferOrder.setHelpText({
                         help: "Check this field if you want to include Item fulfilments created from Transfer Orders in the list. In case this checkbox is checked, customer field is not mandatory."
                     });
                     if (bTransferOrder == "true") {
                         fieldTransferOrder.defaultValue = "T";
                     } else {
                         fieldTransferOrder.defaultValue = "F";
                     }

                     // add BEGIN DATE field
                     var fieldDateFrom = objForm.addField({
                         id: 'custpage_datebegin',
                         type: serverWidget.FieldType.DATE,
                         label: 'From',
                         container: "searchgroupid"
                     });
                     // add END DATE field
                     var fieldDateTo = objForm.addField({
                         id: 'custpage_dateend',
                         type: serverWidget.FieldType.DATE,
                         label: 'To',
                         container: "searchgroupid"
                     });

                     if (strStatus == undefined) {
                         strStatus = "ItemShip:A";
                     }

                     var fieldStatus = objForm.addField({
                         id: 'custpage_status',
                         type: serverWidget.FieldType.MULTISELECT,
                         label: 'Status',
                         container: "searchgroupid"
                     });

                     if (strStatus.indexOf("ItemShip:A") > -1) {
                         var bSelected = true
                     } else {
                         var bSelected = false
                     }

                     fieldStatus.addSelectOption({
                         text: 'Picked',
                         value: 'ItemShip:A',
                         isSelected: bSelected,
                     });

                     if (strStatus.indexOf("ItemShip:B") > -1) {
                         var bSelected = true
                     } else {
                         var bSelected = false
                     }

                     fieldStatus.addSelectOption({
                         text: 'Packed',
                         value: 'ItemShip:B',
                         isSelected: bSelected,
                     });

                     if (strStatus.indexOf("ItemShip:C") > -1) {
                         var bSelected = true
                     } else {
                         var bSelected = false
                     }

                     fieldStatus.addSelectOption({
                         text: 'Shipped',
                         value: 'ItemShip:C',
                         isSelected: bSelected,
                     });
                     fieldStatus.updateDisplaySize({
                         height: 3,
                         width: 100
                     });
                     fieldStatus.updateBreakType({
                         breakType: serverWidget.FieldBreakType.STARTCOL
                     });

                     var fieldAllowReprint = objForm.addField({
                         id: 'custpage_reprint',
                         type: serverWidget.FieldType.CHECKBOX,
                         label: 'Allow reprint',
                         container: "searchgroupid"
                     });

                     fieldAllowReprint.setHelpText({
                         help: "Check this field if you want to search for IFs that were already printed."
                     });

                     if (bReprint != undefined) {

                         if (bReprint == "true") {
                             fieldAllowReprint.defaultValue = "T";
                         } else {
                             fieldAllowReprint.defaultValue = "F";
                         }

                     }

                     // update layout position for BEGIN DATE field
                     fieldDateFrom.updateLayoutType({
                         layoutType: serverWidget.FieldLayoutType.OUTSIDE
                     });
                     fieldDateFrom.updateBreakType({
                         breakType: serverWidget.FieldBreakType.STARTROW
                     });
                     // update layout position for END DATE field
                     fieldDateTo.updateLayoutType({
                         layoutType: serverWidget.FieldLayoutType.OUTSIDE
                     });
                     fieldDateTo.updateBreakType({
                         breakType: serverWidget.FieldBreakType.STARTCOL
                     });

                     fieldStatus.updateLayoutType({
                         layoutType: serverWidget.FieldLayoutType.OUTSIDE
                     });
                     fieldStatus.updateBreakType({
                         breakType: serverWidget.FieldBreakType.STARTROW
                     });

                     fieldTransferOrder.updateLayoutType({
                         layoutType: serverWidget.FieldLayoutType.OUTSIDE
                     });
                     fieldTransferOrder.updateBreakType({
                         breakType: serverWidget.FieldBreakType.STARTROW
                     });

                     fieldAllowReprint.updateLayoutType({
                         layoutType: serverWidget.FieldLayoutType.OUTSIDE
                     });
                     fieldAllowReprint.updateBreakType({
                         breakType: serverWidget.FieldBreakType.STARTROW
                     });

                     var fieldgroup = objForm.addFieldGroup({
                         id: 'fieldgroupid',
                         label: 'Print Options'
                     });

                     var fieldPrintScreen = objForm.addField({
                         id: 'custpage_printradio',
                         type: serverWidget.FieldType.RADIO,
                         label: 'Print On Screen',
                         source: "print",
                         container: 'fieldgroupid'
                     });
                     var fieldDownloadtoPC = objForm.addField({
                         id: 'custpage_printradio',
                         type: serverWidget.FieldType.RADIO,
                         label: 'Download to computer',
                         source: "download",
                         container: 'fieldgroupid'
                     });
                     fieldPrintScreen.defaultValue = 'print';
                     fieldDownloadtoPC.setHelpText({
                         help: "Check this field if you want to download created PDF file directly to your computer."
                     });
                     fieldPrintScreen.setHelpText({
                         help: "Check this field if you want to print created PDF file on your screen."
                     });

                     var fieldSaveToFCabin = objForm.addField({
                         id: 'custpage_savetofilecabinet',
                         type: serverWidget.FieldType.CHECKBOX,
                         label: 'Save to file Cabinet',
                         container: 'fieldgroupid'
                     });
                     fieldSaveToFCabin.setHelpText({
                         help: "Check this field if you want to save created PDF file to the file cabinet."
                     });
                     fieldSaveToFCabin.updateLayoutType({
                         layoutType: serverWidget.FieldLayoutType.ENDROW
                     });

                     var fieldSingleBol = objForm.addField({
                         id: 'custpage_masterbol',
                         type: serverWidget.FieldType.RADIO,
                         label: 'Standalone Bills of Lading',
                         source: "standalonebol",
                         container: 'fieldgroupid'
                     });

                     var fieldMasterOnly = objForm.addField({
                         id: 'custpage_masterbol',
                         type: serverWidget.FieldType.RADIO,
                         label: 'Master Bol Only',
                         source: "masterbolonly",
                         container: 'fieldgroupid'
                     });
                     var fieldMasterAndSingleBol = objForm.addField({
                         id: 'custpage_masterbol',
                         type: serverWidget.FieldType.RADIO,
                         label: 'Master BoL and associated Bills of Lading',
                         source: "masterandsingle",
                         container: 'fieldgroupid'
                     });

                     switch (bMasterBol) {
                         case 'standalonebol':
                             fieldSingleBol.defaultValue = 'standalonebol';
                             break;
                         case 'masterbolonly':
                             fieldMasterOnly.defaultValue = 'masterbolonly';
                             break;
                         case 'masterandsingle':
                             fieldMasterAndSingleBol.defaultValue = 'masterandsingle';
                             break;
                         default:
                             fieldMasterAndSingleBol.defaultValue = 'masterandsingle';
                     }
                     fieldSingleBol.updateBreakType({
                         breakType: serverWidget.FieldBreakType.STARTCOL
                     });
                     var fieldMasterBolNotes = objForm.addField({
                         id: 'custpage_masterbolnotes',
                         type: serverWidget.FieldType.TEXTAREA,
                         label: 'Master BoL Notes',
                         container: 'fieldgroupid'
                     });
                     fieldMasterBolNotes.updateBreakType({
                         breakType: serverWidget.FieldBreakType.STARTCOL
                     });

                     fieldMasterBolNotes.setHelpText({
                         help: "Notes to be shown on Master Bol"
                     });

                     var fieldWeightUnit = objForm.addField({
                         id: 'custpage_weightunit',
                         type: serverWidget.FieldType.SELECT,
                         label: "Total weight Units on Master BoL",
                         container: 'fieldgroupid'
                     });

                     fieldWeightUnit.setHelpText({
                         help: "Select weight unit for Master Bol Total table."
                     });
                     fieldWeightUnit.addSelectOption({
                         text: 'lb',
                         value: 3,
                         isSelected: stWeightUnit == '3'
                     });
                     fieldWeightUnit.addSelectOption({
                         text: 'oz',
                         value: 4,
                         isSelected: stWeightUnit == '4'
                     });
                     fieldWeightUnit.addSelectOption({
                         text: 'kg',
                         value: 1,
                         isSelected: stWeightUnit == '1'
                     });
                     fieldWeightUnit.addSelectOption({
                         text: 'g',
                         value: 2,
                         isSelected: stWeightUnit == '2'
                     });

                     fieldWeightUnit.updateDisplaySize({
                         height: 3,
                         width: 100
                     });

                     if (intCustId == undefined && bTransferOrder != true && !fieldItemFulfillment && !fieldSalesOrders) {
                         // if customer id is not provided load form without sublist
                         context.response.writePage(objForm);

                     } else {

                         var arrStatus = strStatus.split(",");
                         var filtersBoL = [];
                       if (intIFId)
                          {
                            log.debug("fieldItemFulfillment", fieldItemFulfillment);
                          }
                       if (intSOId == true)
                          {
                            log.debug("fieldSalesOrders", fieldSalesOrders);
                          }
                       if (intIFId && intSOId)
                          {
                            filtersBoL = [
                                 ["type", "anyof", "ItemShip"],
                                 "AND", ["mainline", "is", "T"],
                                 "AND", [
                                     ["createdfrom.type", "anyof", "SalesOrd"], "AND", [["internalid", "anyof", intIFId.split(',')], "OR", ["createdfrom", "anyof", intSOId.split(',')]]
                                 ],
                                 "OR", [
                                     ["createdfrom.type", "anyof", "TrnfrOrd"], "AND", ["mainline", "is", "T"]
                                 ],
                             ]
                          }
                        else if (intIFId) {
                          filtersBoL = [
                                 ["type", "anyof", "ItemShip"],
                                 "AND", ["mainline", "is", "T"],
                                 "AND", [
                                     ["createdfrom.type", "anyof", "SalesOrd"], "AND",["internalid", "anyof", intIFId.split(',')]
                                 ],
                                 "OR", [
                                     ["createdfrom.type", "anyof", "TrnfrOrd"], "AND", ["mainline", "is", "T"]
                                 ],
                             ]
                        }
                        else if (intSOId) {
                           filtersBoL = [
                                 ["type", "anyof", "ItemShip"],
                                 "AND", ["mainline", "is", "T"],
                                 "AND", [
                                     ["createdfrom.type", "anyof", "SalesOrd"], "AND", ["createdfrom", "anyof", intSOId.split(',')]
                                 ],
                                 "OR", [
                                     ["createdfrom.type", "anyof", "TrnfrOrd"], "AND", ["mainline", "is", "T"]
                                 ],
                             ]
                         } else if (intCustId && bTransferOrder == "true") {
                             log.debug("FILTER:BOTH");
                            
                             filtersBoL = [
                                 ["type", "anyof", "ItemShip"],
                                 "AND", ["mainline", "is", "T"],
                                 "AND", [
                                     ["createdfrom.type", "anyof", "SalesOrd"], "AND", ["name", "anyof", intCustId]
                                 ],
                                 "OR", [
                                     ["createdfrom.type", "anyof", "TrnfrOrd"], "AND", ["mainline", "is", "T"]
                                 ],
                             ]
                         } else if (bTransferOrder == "true") {
                             log.debug("FILTER:TRANSFER");
                             filtersBoL = [
                                 ["type", "anyof", "ItemShip"],
                                 "AND", ["mainline", "is", "T"],
                                 "AND", ["createdfrom.type", "anyof", "TrnfrOrd"]
                             ]
                         } else if (intCustId){
                             log.debug("FILTER:ELSE");
                             filtersBoL = [
                                 ["type", "anyof", "ItemShip"],
                                 "AND", ["mainline", "is", "T"],
                                 "AND", ["createdfrom.type", "anyof", "SalesOrd"],
                                 "AND", ["name", "anyof", intCustId]
                             ]
                         }
                        else {
                             log.debug("FILTER:ELSE");
                             filtersBoL = [
                                 ["type", "anyof", "ItemShip"],
                                 "AND", ["mainline", "is", "T"],
                                 "AND", ["createdfrom.type", "anyof", "SalesOrd"]
                             ]
                         }
                         // create ITEM FULFILLMENT search
                         var itemfulfillmentSearchObj = search.create({
                             type: "itemfulfillment",
                             filters: filtersBoL,
                             columns: ["mainline",
                                 "name",
                                 "trandate",
                                 "asofdate",
                                 "postingperiod",
                                 "taxperiod",
                                 "type",
                                 "tranid",
                                 "entity",
                                 "account",
                                 "memo",
                                 "amount",
                                 "createdfrom",
                                 "internalid",
                                 "status"
                             ]
                         });

                         if (bReprint == "false") {
                             var searchFilterRePrint = search.createFilter({
                                 name: 'custbody_ns_bol_printed',
                                 operator: search.Operator.IS,
                                 values: false
                             });
                             //  log.debug("bReprint22",bReprint);
                             itemfulfillmentSearchObj.filters.push(searchFilterRePrint);
                         }

                         var searchFilterStatus = search.createFilter({
                             name: 'status',
                             operator: search.Operator.ANYOF,
                             values: arrStatus

                         });

                         itemfulfillmentSearchObj.filters.push(searchFilterStatus);

                         // set CUSTOMER field default value
                         fieldCustomers.defaultValue = intCustId;
                         fieldItemFulfillment.defaultValue = intIFId ? intIFId.split(',') : '';
                         fieldSalesOrders.defaultValue = intSOId ? intSOId.split(',') : '';
                       
                         log.debug("beginDate",JSON.stringify(beginDate));
                           log.debug("endDate",JSON.stringify(endDate));
                           
                       
                         // begin + end date are set
                         //if ((beginDate != '') && (endDate != '')) {
                         if ((beginDate) && (endDate)) {

                             var parsedBeginDate = parseDate(beginDate);
                             var parsedEndDate = parseDate(endDate);

                             // add filter
                             var searchFilter = search.createFilter({
                                 name: 'trandate',
                                 operator: search.Operator.WITHIN,
                                 values: [parsedBeginDate, parsedEndDate]
                             });

                             // push filter
                             itemfulfillmentSearchObj.filters.push(searchFilter);

                             // set default values to date fields
                             fieldDateFrom.defaultValue = parsedBeginDate;
                             fieldDateTo.defaultValue = parsedEndDate;

                             // only END DATE is set
                         } else if ((beginDate == '') && (endDate != '')) {

                             var parsedEndDate = parseDate(endDate);

                             // create filter
                             var searchFilter = search.createFilter({
                                 name: 'trandate',
                                 operator: search.Operator.ONORBEFORE,
                                 values: parsedEndDate
                             });

                             // push filter
                             itemfulfillmentSearchObj.filters.push(searchFilter);

                             // set default value to END DATE field
                             fieldDateTo.defaultValue = parsedEndDate;

                             // only BEGIN DATE is set
                         } else if ((beginDate != '') && (endDate == '')) {

                             var parsedBeginDate = parseDate(beginDate);

                             // create filter
                             var searchFilter = search.createFilter({
                                 name: 'trandate',
                                 operator: search.Operator.ONORAFTER,
                                 values: parsedBeginDate
                             });

                             // set default value to BEGIN DATE field
                             fieldDateFrom.defaultValue = parsedBeginDate;

                             // push filter
                             itemfulfillmentSearchObj.filters.push(searchFilter);
                         }
                       
                        log.debug('search Filter : ', JSON.stringify(itemfulfillmentSearchObj));
                       
                        // Added by Manoj to include the IF and Sales Order search
                        // 
                        // 
                        // 
                        // End of Addtion

                         // fun search on item fulfillments and store results
                         var arrSearchIFResults = itemfulfillmentSearchObj.run().getRange({
                             start: 0,
                             end: 1000
                         });

                         // add item fulfillments sublist
                         var sublistItemFulfillments = objForm.addSublist({
                             id: 'custpage_item_fullfillment_sublist',
                             type: serverWidget.SublistType.LIST,
                             label: 'Item Fulfillments(' + arrSearchIFResults.length + ')'
                         });

                         // add buttons Un/Mark All
                         sublistItemFulfillments.addMarkAllButtons();

                         // add check box field to sublist
                         var fldCheck = sublistItemFulfillments.addField({
                             id: 'custpage_check',
                             type: serverWidget.FieldType.CHECKBOX,
                             label: 'Select'
                         });

                         var fldClick = sublistItemFulfillments.addField({
                             id: 'custpage_clickif',
                             type: serverWidget.FieldType.URL,
                             label: 'View IF',
                         });

                         var fldCheck = sublistItemFulfillments.addField({
                             id: 'custpage_if_internalid',
                             type: serverWidget.FieldType.INTEGER,
                             label: 'ID'
                         });

                         // add transaction id to sublist
                         var fldTranId = sublistItemFulfillments.addField({
                             id: 'custpage_tranid',
                             type: serverWidget.FieldType.TEXT,
                             label: 'Transaction ID'
                         });

                         // if status

                         var fldStatus = sublistItemFulfillments.addField({
                             id: 'custpage_status',
                             type: serverWidget.FieldType.TEXT,
                             label: 'IF Status'
                         });

                         // add transaction date to sublist
                         var fldTranDate = sublistItemFulfillments.addField({
                             id: 'custpage_trandate',
                             type: serverWidget.FieldType.DATE,
                             label: 'Transaction Date'
                         });

                         // add company name to sublist
                         var fldCompName = sublistItemFulfillments.addField({
                             id: 'custpage_name',
                             type: serverWidget.FieldType.TEXT,
                             label: 'Company Name'
                         });

                         var fldCompName = sublistItemFulfillments.addField({
                             id: 'custpage_createdfrom',
                             type: serverWidget.FieldType.TEXT,
                             label: 'From Transaction'
                         });

                         // add search results to to sublist
                         for (var i = 0; i < arrSearchIFResults.length; i++) {
                             sublistItemFulfillments.setSublistValue({
                                 id: 'custpage_tranid',
                                 line: i,
                                 value: arrSearchIFResults[i].getValue({
                                     name: 'tranid'
                                 })
                             });

                             sublistItemFulfillments.setSublistValue({
                                 id: 'custpage_status',
                                 line: i,
                                 value: arrSearchIFResults[i].getValue({
                                     name: 'status'
                                 })
                             });

                             var urlIF = url.resolveRecord({
                                 recordType: 'itemfulfillment',
                                 recordId: parseInt(arrSearchIFResults[i].getValue({
                                     name: 'internalid'
                                 })),
                                 isEditMode: false
                             });

                             sublistItemFulfillments.setSublistValue({
                                 id: 'custpage_clickif',
                                 line: i,
                                 value: urlIF
                             });

                             fldClick.linkText = "View";

                             sublistItemFulfillments.setSublistValue({
                                 id: 'custpage_if_internalid',
                                 line: i,
                                 value: arrSearchIFResults[i].getValue({
                                     name: 'internalid'
                                 })

                             });
                             sublistItemFulfillments.setSublistValue({
                                 id: 'custpage_trandate',
                                 line: i,
                                 value: arrSearchIFResults[i].getValue({
                                     name: 'trandate'
                                 })
                             });

                             var strCustomer = arrSearchIFResults[i].getText({
                                 name: 'name'
                             });
                             // Set Compnay name to '-' in case of transfer order
                             if (!strCustomer) {
                                 var strCustomer = "-";
                             }

                             sublistItemFulfillments.setSublistValue({
                                 id: 'custpage_name',
                                 line: i,
                                 value: strCustomer
                             });

                             sublistItemFulfillments.setSublistValue({
                                 id: 'custpage_createdfrom',
                                 line: i,
                                 value: arrSearchIFResults[i].getText({
                                     name: 'createdfrom'
                                 })
                             });
                         }

                         // create form
                         context.response.writePage(objForm);
                     }
                 }

             }
         } catch (e) {
             log.error("SL Error", e.message);
             customLog(e);
             throw "Error occured when creating BoL from SL.\n\nDetails: " + e.message +
                 "\n\nContact your administrator or check suitelet script for BoL.";

         }
     }

     // parse date function
     function parseDate(date) {

         var dateParsed = format.parse({
             value: date,
             type: format.Type.TEXT
         });

         var parsedDate = format.format({
             value: dateParsed,
             type: format.Type.DATETIME
         });

         return parsedDate;

     }



     function customLog(e, title) {
         if (e instanceof Error) {
             log.error({
                 title: title + "| JavaScript ERROR",
                 details: "Name: " + e.name + ", Message: " + e.message + ", Stack: " + e.stack + ", File Name: " + (e.fileName || "N/A") +
                     ", Line Number: " + (e.lineNumber || "N/A")
             });
         } else {
             log.error({
                 title: title + "| SuiteScript ERROR",
                 details: "Name: " + e.name + ", Message: " + e.message + ", Stack: " + e.stack + ", ID: " + (e.id || "N/A") + ", Cause: " +
                     (JSON.stringify(e.cause) || "N/A")
             });
         }
     }

     return {
         onRequest: onRequest
     };

 });