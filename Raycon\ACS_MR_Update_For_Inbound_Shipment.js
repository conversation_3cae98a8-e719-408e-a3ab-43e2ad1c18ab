/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record'], function(search, record) {

    function getInputData() {
        var mySearch = search.load({
            id: 'customsearch8601'
        });
        return mySearch;
    }

    function map(context) {
        var keyFromInput = context.key;
        var valueFromInput = context.value;

        var recordObj = JSON.parse(valueFromInput);

        var newRecObj = record.create({
            type: 'customrecord_inb_shipment_external_doc_n',
            isDynamic: true
        });

        newRecObj.setValue({ fieldId: 'name', value: recordObj.values.externaldocumentnumber });
        newRecObj.setValue({ fieldId: 'custrecord_inbound_shipment', value: recordObj.id });

        try {
            var savedId =  newRecObj.save();
            log.debug("Success", "Successfully saved custom record with ID: " + savedId);
        } catch (e) {
            log.debug("Error", e);
        }
        
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        summarize: summarize
    }
});
