/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function beforeLoad(context) {

        if(context.type == context.UserEventType.PRINT) {
            record.submitFields({
                type: context.newRecord.type,
                id: context.newRecord.id,
                values: {
                    custbody_date_printed: new Date
                }
            });
        }
        
    }

    return {
        beforeLoad: beforeLoad,
    }
});
