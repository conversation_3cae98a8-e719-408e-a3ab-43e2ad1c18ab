/**
 * Copyright (c) 2017, Oracle and/or its affiliates. All rights reserved.
 *
 * @NApiVersion 2.0
 * @NScriptType MapReduceScript
 */

 define([
    '../../lib/wrapper/9997_NsWrapperRuntime',
    '../../data/9997_PFADAO',
    '../../lib/8858_MRErrorCompiler',
    '../../app/8859_EmailNotificationDataSource',
    '../../app/8859_EmailNotificationProcessor',
    '../../app/8858_ScriptScheduler',
    '../../app/8859_MapResultsProcessor'],

function(runtime, pfaDAO, errorCompiler, notificationDataSource, notificationProcessor, scriptScheduler, mapResultsProcessor) {

    var PFA_ID_FIELD = 'custscript_8859_email_notification_pfa';
    var PFA_STATUS_PROCESSED = '4';
    var PFA_STATUS_PROCESSED_WITH_ERRORS = '8';
    var PFA_STATUS_FAILED = '9';
    var PFA_SENDING_EMAIL_NOTIFS = '7';
    var PFA_STATUS_FIELD = 'custrecord_2663_file_processed';
    var PFA_NOTIFY_LIST_FIELD = 'custrecord_2663_payments_for_notify';
    var PFA_PRIORITY_FIELD = 'custrecord_ep_priority';
    var PFA_PROCESSING_ERRORS_FIELD = 'custrecord_2663_processing_errors';

    function getInputData() {
        var pfaId = runtime.getCurrentScript().getParameter(PFA_ID_FIELD);
        var pfa = pfaDAO.retrieve(pfaId);

        var updateParams = {};
        updateParams[PFA_STATUS_FIELD] = PFA_SENDING_EMAIL_NOTIFS;
        updatePFA(pfaId, updateParams);

        return notificationDataSource.getNotifications(pfa);
    }


    function sendEmail(context) {
        var notification = JSON.parse(context.value);

        try {
            notificationProcessor.process(notification);
            context.write(notification.pfaId, {
                isSuccess: true
            });
        } catch (e) {
            context.write(notification.pfaId, {
                isSuccess: false,
                message: (e.name || e.message) ?  (e.name || '') + ': ' + (e.message || '') : e,
                errorCode: e.code,
                details: e.details
            });
            log.error('8859_email_notification_mr', JSON.stringify(e));
            throw e;
        }
    }

    function reduce(context) {
        var results = mapResultsProcessor.compileResults(context.values);
        var updateParams = {};
        updateParams[PFA_NOTIFY_LIST_FIELD] = '';

        updateParams[PFA_PROCESSING_ERRORS_FIELD] =  mapResultsProcessor.composeProcessingErrorMessage(context.values, {
            successCount : results.successCount,
            failureCount : results.failureCount
        });

        // There are successful items processed
        if(results.successCount > 0) {
            // Some items failed
            if(results.failureCount > 0) {
                updateParams[PFA_STATUS_FIELD] = PFA_STATUS_PROCESSED_WITH_ERRORS;
            }
            // Nothing failed
            else {
                updateParams[PFA_STATUS_FIELD] = PFA_STATUS_PROCESSED;
            }
        }
        // All items failed
        else {
            updateParams[PFA_STATUS_FIELD] = PFA_STATUS_FAILED;
        }

        updatePFA(context.key, updateParams);
    }


    function summarize(context) {
        var inputSummary = context.inputSummary;
        var mapSummary = context.mapSummary;
        var reduceSummary = context.reduceSummary;

        var inputError = inputSummary.error;
        var mapErrors = errorCompiler.compile(mapSummary);
        var reduceErrors = errorCompiler.compile(reduceSummary);

        if (inputError) {
            log.error('getInputData Errors', inputError);
        }
        if (mapErrors.length > 0) {
            log.error('map Errors', mapErrors.join('\n'));
        }
        if (reduceErrors.length > 0) {
            log.error('reduce Errors', reduceErrors.join('\n'));
        }

        scriptScheduler.startNewProcess();
    }

    function updatePFA(id, updateValues) {
        try {
            log.debug('Updating PFA', id + JSON.stringify(updateValues));
            pfaDAO.updateFields(id, updateValues);
        } catch (e) {
            log.error('failed update' + id, e);
        }
    }

    return {
        getInputData: getInputData,
        map: sendEmail,
        reduce: reduce,
        summarize: summarize
    };
});