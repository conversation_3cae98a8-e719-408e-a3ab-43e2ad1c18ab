/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
 define(['N/ui/dialog'], function(dialog) {

    function pageInit(context) {
        
    }

    function fieldChanged(context) {

        if(context.fieldId == 'custbody_nsacs_brazil_payment_ref'){
            var paymentType = context.currentRecord.getValue({ fieldId: 'custbody_nsacs_brazil_payment_type' });
            if(paymentType){
                var paymentRef = context.currentRecord.getValue({ fieldId: 'custbody_nsacs_brazil_payment_ref' });
                var length = paymentRef.length
                if(paymentType == 1 && length != 47) {

                    dialog.alert({
                        title: "Notice",
                        message: "Boleto Payment Type should have 47 digit reference! The length of the payment reference you entered is " + length
                    });
                    context.currentRecord.setValue({ fieldId: 'custbody_nsacs_brazil_payment_ref', value: '', ignoreFieldChange: true });

                } else if(paymentType == 2 && length != 48) {

                    dialog.alert({
                        title: "Notice",
                        message: "Tax and Utility Payment Type should have 48 digit reference! The length of the payment reference you entered is " + length
                    });
                    context.currentRecord.setValue({ fieldId: 'custbody_nsacs_brazil_payment_ref', value: '', ignoreFieldChange: true });
                    
                } else if(paymentType == 3) {
                    
                    var subsidiary = context.currentRecord.getValue({ fieldId: 'subsidiary' });
                    if((parseInt(subsidiary) == 15 || subsidiary == 28) && length == 0) {

                        dialog.alert({
                            title: "Notice",
                            message: "Payment Reference should have Value!"
                        });

                    } else if (parseInt(subsidiary) == 14 && (3 > length || 25 < length)) {

                        dialog.alert({
                            title: "Notice",
                            message: "You have entered an invalid Payment Reference! Payment Reference's length for this subsidiary should be between 3 and 25-digit reference! The length of the payment reference you entered is " + length
                        });

                    } else if (parseInt(subsidiary) == 18 && (3 > length || 30 < length)) {
                        
                        dialog.alert({
                            title: "Notice",
                            message: "You have entered an invalid Payment Reference! Payment Reference's length for this subsidiary should be between 3 and 30-digit reference! The length of the payment reference you entered is " + length
                        });

                    }
                }
            } else {
                
                dialog.alert({
                    title: "Notice",
                    message: "Please select a Payment Type first before entering the payment reference"
                });
                context.currentRecord.setValue({ fieldId: 'custbody_nsacs_brazil_payment_ref', value: '', ignoreFieldChange: true });

            }
        } else if (context.fieldId == 'custbody_nsacs_brazil_payment_type') {
            context.currentRecord.setValue({ fieldId: 'custbody_nsacs_brazil_payment_ref', value: '', ignoreFieldChange: true });

            var paymentType = context.currentRecord.getValue({ fieldId: 'custbody_nsacs_brazil_payment_type' });
            var subsidiary = context.currentRecord.getValue({ fieldId: 'subsidiary' });

            if(paymentType == 3) {                
                if ([15, 28, 14, 18].indexOf(parseInt(subsidiary)) === -1) {

                    console.log(subsidiary);
                    console.log([15, 28, 14, 18].indexOf(parseInt(subsidiary)));

                    dialog.alert({
                        title: "Notice",
                        message: "The DT PAYMENT TYPE (OCR Number) you selected is not valid for this subsidiary."
                    });
                    
                    context.currentRecord.setValue({ fieldId: 'custbody_nsacs_brazil_payment_type', value: '', ignoreFieldChange: true });

                }
            }
        }
    }

    
    function saveRecord(context) {

        var paymentType = context.currentRecord.getValue({ fieldId: 'custbody_nsacs_brazil_payment_type'});
        var paymentRef = context.currentRecord.getValue({ fieldId: 'custbody_nsacs_brazil_payment_ref'});
        
        if(paymentType && (paymentRef == '')){
            dialog.alert({
                title: "Notice",
                message: "You have not entered a payment reference"
            });

            return false;
        } else if ((paymentType == '') && (paymentRef != '')) {
            dialog.alert({
                title: "Notice",
                message: "You have entered a payment reference but did not select any payment type"
            });

            return false;
        }

        return true;
    }

    return {
        pageInit: pageInit,
        fieldChanged: fieldChanged,
        saveRecord: saveRecord
    }
});