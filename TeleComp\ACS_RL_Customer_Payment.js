/**
 *@NApiVersion 2.x
 *@NScriptType Restlet
 */
define(['N/search', 'N/record'], function(search, record) {

    function _get(context) {
        
        // create saved search based on date range
        try {
            var paymentId = context.customerpaymentid;

            var paymentObj = record.load({
                id: Number(paymentId),
                type: record.Type.CUSTOMER_PAYMENT,
                isDynamic: true
            });

            var appliedCount = paymentObj.getLineCount({ sublistId: 'apply' });
            var appliedInvoices = [];

            for(var i = 0; i < appliedCount; i++) {
                var isApplied = paymentObj.getSublistValue({ fieldId: 'apply', sublistId: 'apply', line: i });
                if(!isApplied) continue;

                appliedInvoices.push({
                    internal_id: paymentObj.getSublistValue({ fieldId: 'internalid', sublistId: 'apply', line: i }),
                    doc: paymentObj.getSublistValue({ fieldId: 'doc', sublistId: 'apply', line: i }),
                    refnum: paymentObj.getSublistValue({ fieldId: 'refnum', sublistId: 'apply', line: i }),
                    amount: Number(paymentObj.getSublistValue({ fieldId: 'amount', sublistId: 'apply', line: i })),
                });

            }


            return {
                success: 1,
                count: appliedInvoices.length,
                result: appliedInvoices
            }
        } catch (e) {
            log.error('Error occured', e);
            return {
                success: 0,
                message: e.message
            }
        }

    }

    function _post(context) {
        
    }

    function _put(context) {
        
    }

    function _delete(context) {
        
    }

    return {
        get: _get,
        post: _post,
        put: _put,
        delete: _delete
    }
});
