/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/render', 'N/record', 'N/email', 'N/format', 'N/format/i18n'], function(search, render, record, email, format, formatCurr) {

    function getInputData() {
        var mySearch = search.load({
            id: 'customsearch_cust_overdue_inv'
        });

        return mySearch;
    }
    function map(context) {
        
        // accept data from previous stage
        var keyFromInput = context.key;
        var valueFromInput = context.value;
        var recordObj = JSON.parse(valueFromInput);
        try {
            
            // place amount values to a separate variable
            var amount = parseFloat(recordObj.values.amount);
            var amountPaid = parseFloat(recordObj.values.amountpaid);
            var amountRemaining = parseFloat(recordObj.values.amountremaining);

            // overwrite variables if currency is not USD
            if(recordObj.values.currency.value != '1'){
                amount = parseFloat(recordObj.values.fxamount);
                amountPaid = parseFloat(recordObj.values.fxamountpaid);
                amountRemaining = parseFloat(recordObj.values.fxamountremaining);                
            }

            // format amount and amount paid
            // amountRemaining is not formatted as it is used for further computation of data down in reduce
            var currFormatter = formatCurr.getCurrencyFormatter({
                currency: recordObj.values.currency.text
            });
            
            // overwrite variables again for formatted data
            amount = currFormatter.format({ number:  amount });
            amountPaid = currFormatter.format({ number:  amountPaid });

            context.write({
                key: recordObj.values.entity.value,
                value: {
                    type: recordObj.values.type.text,
                    trandate: recordObj.values.trandate,
                    tranid: recordObj.values.tranid,
                    entity: recordObj.values.entity.text,
                    duedate: recordObj.values.duedate,
                    otherrefnum: recordObj.values.otherrefnum,
                    createdfrom: recordObj.values.createdfrom.text,
                    currency: recordObj.values.currency.text,
                    amount: amount,
                    amountpaid: amountPaid,
                    amountremaining: amountRemaining
                }
            });        

        } catch (errorObj) {
            log.debug({
                title: 'map 1',
                details: errorObj
            });
        }
    }
    
    function reduce(context) {

        try {
            var reduceKey = context.key;
            var reduceValues = context.values;
            
            var customerObj = record.load({
                type: record.Type.CUSTOMER,
                id: reduceKey
            });

            var customerAddress = customerObj.getValue({ fieldId: 'defaultaddress' });

            // create PDF
            var renderer = render.create();
            
            // select template to be used via script ID
            renderer.setTemplateByScriptId({
                scriptId: "CUSTTMPL_ACS_STATEMENT_PDF"
            });

            // prepare invoice data to be included in PDF
            var invoices = [];
            var totalInvoicesAmt = 0.00;
            var aging = {
                one: 0.00,
                two: 0.00,
                three: 0.00,
                four: 0.00,
                five: 0.00
            };
            var running_bal = 0.00;
            for(x = 0; x < reduceValues.length; x++){
                var parsedValue = JSON.parse(reduceValues[x]);
                var parsedAmountRemaining = parseFloat(parsedValue.amountremaining);
                
                // add amount remaining to total
                totalInvoicesAmt = totalInvoicesAmt + parsedAmountRemaining;

                // add to running balance
                parsedValue.runningbal = totalInvoicesAmt;
                
                // get aging table data
                var tranDateObj = new Date(parsedValue.trandate);
                var currDate = new Date();
                var one_day=1000*60*60*24;
                var dateDiff = currDate - tranDateObj;
                var age = Math.floor(dateDiff/one_day);
                if(age == 0) {
                    aging.one = aging.one + parsedAmountRemaining;
                } else if (age >= 1 && age <= 30) {
                    aging.two = aging.two + parsedAmountRemaining;
                } else if (age >= 31 && age <= 60) {
                    aging.three = aging.three + parsedAmountRemaining;
                } else if (age >= 61 && age <= 90) {
                    aging.four = aging.four + parsedAmountRemaining;
                } else if (age >= 91) {
                    aging.five = aging.five + parsedAmountRemaining;
                }
                
                // format currencies
                var currFormatter = formatCurr.getCurrencyFormatter({
                    currency: parsedValue.currency
                });

                parsedValue.amountremaining = currFormatter.format({ number: (parsedValue.amountremaining) ? parsedValue.amountremaining : 0.00 });
                parsedValue.runningbal = currFormatter.format({ number: parsedValue.runningbal });
                
                // push to invoices array
                invoices.push(parsedValue);

            }

            totalInvoicesAmt = currFormatter.format({ number: totalInvoicesAmt });
            aging.one = currFormatter.format({ number: (aging.one) ? aging.one : 0.00 });
            aging.two = currFormatter.format({ number: (aging.two) ? aging.two : 0.00 });
            aging.three = currFormatter.format({ number: (aging.three) ? aging.three : 0.00 });
            aging.four = currFormatter.format({ number: (aging.four) ? aging.four : 0.00 });
            aging.five = currFormatter.format({ number: (aging.five) ? aging.five : 0.00 });
           

            // format dates
            var formattedDateNow = format.format({ value: currDate, type: format.Type.MMYYDATE });
            formattedDateNow = formattedDateNow.replace('/', '_');

            var custObj = {
                invoices: invoices,
                aging: aging,
                total_amt: totalInvoicesAmt,
                cust_address: customerAddress
            };
            
            renderer.addCustomDataSource({
                format: render.DataSource.OBJECT,
                alias: "cust_invoices",
                data: custObj
            });

            renderer.addRecord({
                templateName: 'record',
                record: customerObj
            });

            var statementPDF = renderer.renderAsPdf();

            statementPDF.name = formattedDateNow + '_statement.pdf';

            // invoke email template to be used
            var mergeResult = render.mergeEmail({
                templateId: 6,
                entity: {
                    type: 'customer',
                    id: customerObj.id
                },
                recipient: null,
                supportCaseId: null,
                transactionId: null,
                customRecord: null
            });

            var emailSubject = mergeResult.subject;
            var emailBody = mergeResult.body;
            // CHANGE BASED ON WHAT CUSTOMER WILL SAY
            // TODO: CHANGE RECEPIENT BACK TO customerObj.id AFTER CUSTOMER GAVE GO SIGNAL
            email.send({
                author: 536810,
                recipients: '<EMAIL>',
                subject: emailSubject,
                body: emailBody,
                attachments: [statementPDF]
            });
        } catch (errorObj) {
            log.debug({
                title: 'reduce 1',
                details: errorObj
            });
        }
        
    }
    
    function summarize(context){

    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
