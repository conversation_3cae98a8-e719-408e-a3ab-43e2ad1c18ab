/**
 *@NApiVersion 2.1
 *@NScriptType Suitelet
 */
 define(['N/ui/serverWidget','N/runtime', 'N/search', 'N/task'], function(serverWidget,runtime,search, task) {
    // '<b>Unbilled Time : </b>  $' || {custevent_nx_case_timeunbilled}|| '<br>' ||'<b>Unbilled SO : </b>    $' || {custevent_nx_case_transaction_unbilled} || '<br>' || '<b>Unbilled Expenses : </b>$' ||{custevent_nx_case_expensesunbilled}||'<br>' || '<b>Total Unbilled : </b>  $' ||{custevent_nx_case_amountunbilled}
    function renderFilters() {

    }

    function renderCasesSublist() {

    }

    function getUnbilledproperValue(id, value) {

        if(value == '.00' || value == null || value == 0) {
            var newVal =  0.00;
        } else {
            var newVal = value;
        }

        return newVal

    }

    function getCases() {
        var caseSearchObj = search.load({
            id: 'customsearch_nx_unbilled_cases_2'
        });
        var caseArr = [];
        var caseIds = [];
        caseSearchObj.run().each(function(result){
            var internalId = result.getValue({ name: "internalid" });

            if(caseIds.indexOf(internalId) === -1) {
                caseIds.push(internalId);
                caseArr.push({
                    "internal_id": { value: internalId, label: "Internal Id", type: serverWidget.FieldType.TEXT },
                    "startdate": { value: result.getValue({ name: "startdate" }), label: "Incident Date", type: serverWidget.FieldType.TEXT },
                    "company": { value: result.getText({ name: "company" }), label: "Project", type: serverWidget.FieldType.TEXT },
                    "casenumber": { value: result.getValue({ name: "casenumber" }), label: "Number", type: serverWidget.FieldType.TEXT },
                    "title": { value: result.getValue({ name: "title" }), label: "Subject", type: serverWidget.FieldType.TEXT },
                    // "unbilled_case_amount": { value: result.getValue({ name: "formulatext" }), label: "Unbilled Case Amounts", type: serverWidget.FieldType.TEXT },
                    // "custevent_nx_customer": { value: result.getText({ name: "custevent_nx_case_asset" }), label: "Asset", type: serverWidget.FieldType.TEXT },
                    "custevent_nx_case_timeunbilled": { value: result.getValue({ name: "custevent_nx_case_timeunbilled" }), label: "Unbilled Time", type: serverWidget.FieldType.FLOAT },
                    "custevent_nx_case_transaction_unbilled": { value: getUnbilledproperValue(internalId, result.getValue({ name: "custevent_nx_case_transaction_unbilled" })), label: "Unbilled SO", type: serverWidget.FieldType.FLOAT },
                    "custevent_nx_case_expensesunbilled": { value: getUnbilledproperValue(internalId, result.getValue({ name: "custevent_nx_case_expensesunbilled" })), label: "Unbilled Expenses", type: serverWidget.FieldType.FLOAT }
                });
            }
            log.debug('caseIds', caseIds);
            return true;
        });

        return caseArr;
    }

    function onRequest(context) {
        if(context.request.method == 'GET'){

            var form = serverWidget.createForm({
                title: 'Bulk Invoice'
            });

            var caseObj = getCases();

            var sublist = form.addSublist({
                id : 'custpage_sublist',
                type : serverWidget.SublistType.LIST,
                label : 'Cases for Invoicing'
            });

            sublist.addField({
                id: 'custpage_rec_process',
                label: 'Process',
                type: serverWidget.FieldType.CHECKBOX
            });

            var hidden_field = sublist.addField({
                id: 'custpage_case_id',
                label: 'Process',
                type: serverWidget.FieldType.TEXT
            });

            hidden_field.updateDisplayType({
                displayType : serverWidget.FieldDisplayType.HIDDEN
            });

            for (var key in caseObj[0]) {
                 var returnField = sublist.addField({
                    id: 'custpage_' + key,
                    type: caseObj[0][key].type,
                    label: caseObj[0][key].label
                });    
                if(key == 'internal_id') {
                    returnField.updateDisplayType({
                        displayType: serverWidget.FieldDisplayType.HIDDEN
                    });
                }
            }

            for(var i = 0; i < caseObj.length; i++){

                for (var key in caseObj[i]) {

                    log.debug(key, caseObj[i][key].value);
                    sublist.setSublistValue({
                        id: 'custpage_' + key,
                        line: i,
                        value: ((caseObj[i][key].value != '0') ? caseObj[i][key].value : '0.00')
                    });    


               }

            }

            sublist.addMarkAllButtons();

            form.addSubmitButton({
                label : 'Process'
            });

            context.response.writePage(form);
            
        } else {

            var sublistLines = context.request.getLineCount({ group: 'custpage_sublist' });
            var selectedCases = [];
            for (var i = 0; i < sublistLines; i++) {
                var processCase = context.request.getSublistValue({
                    group: 'custpage_sublist',
                    name: 'custpage_rec_process',
                    line: i
                });
                
                if(processCase == 'T'){
                    var caseId = context.request.getSublistValue({
                        group: 'custpage_sublist',
                        name: 'custpage_internal_id',
                        line: i
                    });
                    selectedCases.push(caseId);
                }
            }
            
            var form = serverWidget.createForm({
                title: 'Bulk Invoice'
            });

            try {
                
                task.create({
                    taskType: task.TaskType.MAP_REDUCE,
                    scriptId: 'customscript_acs_mr_invoice_cases',
                    deploymentId: 'customdeploy_acs_mr_invoice_cases',
                    params: {
                        custscript_case_ids: JSON.stringify(selectedCases)
                    }
                }).submit();

                context.response.writePage(form);
                // context.response.write({
                //     output: JSON.stringify(selectedCases)
                // });
            } catch (e) {

            }
            
        }
    }

    return {
        onRequest: onRequest
    }
});
