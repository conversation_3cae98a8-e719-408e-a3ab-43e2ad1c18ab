/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/ui/serverWidget', 'N/record'], function(serverWidget, record) {

    function beforeLoad(context) {
        var recObj = context.newRecord;
        if(recObj.getValue({ fieldId: 'custbody_acs_subsidiary_ein' }) == ''){

            var invoiceRecord = record.load({
                type: record.Type.INVOICE,
                id: recObj.id,
                isDynamic: true
            });

            var subsidiaryObj = record.load({
                type: record.Type.SUBSIDIARY,
                id: recObj.getValue({ fieldId: 'subsidiary' }),
                isDynamic: true
            }); 

            invoiceRecord.setValue({ fieldId: 'custbody_acs_subsidiary_ein', value: subsidiaryObj.getValue({ fieldId: 'federalidnumber' }) });
            invoiceRecord.save();
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
