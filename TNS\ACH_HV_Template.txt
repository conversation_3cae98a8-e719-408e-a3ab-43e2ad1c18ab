<#-- format specific processing -->
<#function getReferenceNote payment>
    <#assign paidTransactions=transHash[payment.internalid]>
    <#assign referenceNote="">
    <#assign paidTransactionsCount=paidTransactions?size>
    <#if (paidTransactionsCount>= 1)>
        <#list paidTransactions as transaction>
            <#if transaction.tranid?has_content>
                <#if referenceNote?has_content>
                    <#assign referenceNote=referenceNote + ", " + transaction.tranid>
                <#else> 
                    <#assign referenceNote=transaction.tranid>
                </#if>
            </#if>
        </#list>
    </#if>
    <#return referenceNote>
</#function>


<#function getTaxTotal payment>
    <#assign paidTransactions=transHash[payment.internalid]>
    <#assign taxTotal=0.00>
    <#assign exchangeRate=0.00>
    <#assign paidTransactionsCount=paidTransactions?size>
    <#if (paidTransactionsCount>= 1)>
        <#list paidTransactions as transaction>
            <#if taxtotal?has_content>
                <#assign taxTotal=taxTotal+transaction.taxtotal>
                <#assign exchangeRate=exchangeRate+transaction.exchangerate>
            <#else>
                <#assign exchangeRate=transaction.exchangerate>
                <#assign taxTotal=transaction.taxtotal>
            </#if>
        </#list>
    </#if>
    <#if taxTotal?has_content || taxTotal != 0.00>
        <#assign taxTotal=taxTotal?keep_after("$")>
        <#assign taxTotal=taxTotal?keep_before(")")>
        <#assign taxTotal=taxTotal?replace(",", "")>
        <#assign taxTotal=taxTotal?number>
        
        <#assign taxTotal = (taxTotal / exchangeRate)?string("###0.00")>
    </#if>
    
    <#return taxTotal>
</#function>

<#function getSvcLvl total>
    <#if total gt 999999>
        <#return 'URGP'>
    </#if>

    <#return 'NURG'>
</#function>

<#-- cached values -->
<#assign totalAmount=computeTotalAmount(payments)>

<#-- template building -->
#OUTPUT START#
<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
<CstmrCdtTrfInitn>
    <GrpHdr>
        <MsgId>${cbank.custrecord_2663_file_name_prefix}${pfa.name}</MsgId>
        <CreDtTm>${pfa.custrecord_2663_file_creation_timestamp?date?string("yyyy-MM-dd")}T${pfa.custrecord_2663_file_creation_timestamp?time?string("HH:mm:ss")}</CreDtTm>
        <NbOfTxs>${payments?size?c}</NbOfTxs>
        <CtrlSum>${formatAmount(totalAmount,"decLessThan1")}</CtrlSum>
        <InitgPty>
            <Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm>
            <Id>
                <OrgId>
                    <Othr>
                        <Id>${cbank.custpage_eft_custrecord_2663_bank_comp_id}</Id>
                    </Othr>
                </OrgId>
            </Id>
        </InitgPty>
    </GrpHdr>
    <PmtInf>
        <PmtInfId>${pfa.id}-1</PmtInfId>
        <PmtMtd>TRF</PmtMtd>
        <BtchBookg>false</BtchBookg>
        <NbOfTxs>${payments?size?c}</NbOfTxs>
        <CtrlSum>${formatAmount(totalAmount,"decLessThan1")}</CtrlSum>
        <PmtTpInf>
            <SvcLvl>
                <Cd>${getSvcLvl(totalAmount)}</Cd>
            </SvcLvl>
        </PmtTpInf>
        <ReqdExctnDt>${pfa.custrecord_2663_process_date?string("yyyy-MM-dd")}</ReqdExctnDt>
        <Dbtr>
            <Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm>
            <PstlAdr>
                <#if cbank.custrecord_dbtr_addr_1?has_content>
                <StrtNm>${cbank.custrecord_dbtr_addr_1}</StrtNm>
                </#if>
                <#if cbank.custrecord_dbtr_zip?has_content>
                <PstCd>${cbank.custrecord_dbtr_zip}</PstCd>
                </#if>
                <#if cbank.custrecord_dbtr_city?has_content>
                <TwnNm>${cbank.custrecord_dbtr_city}</TwnNm>
                </#if>
                <#if cbank.custrecord_dbtr_ctry?has_content>
                <Ctry>${getCountryCode(cbank.custrecord_dbtr_ctry)}</Ctry>
                </#if>
            </PstlAdr>
        </Dbtr>
        <DbtrAcct>
            <Id>
                <Othr>
                    <Id>${cbank.custpage_eft_custrecord_2663_acct_num}</Id>
                </Othr>
            </Id>
        </DbtrAcct>
        <DbtrAgt>
            <FinInstnId>
                <BIC>${cbank.custpage_eft_custrecord_2663_bic}</BIC>
                <Nm>${cbank.custpage_eft_custrecord_2663_bank_name}</Nm>
                <PstlAdr>
                    <Ctry>${cbank.custpage_eft_custrecord_2663_country_code}</Ctry>
                </PstlAdr>
            </FinInstnId>
            <BrnchId>
                <Id>${cbank.custpage_eft_custrecord_2663_branch_num}</Id>
            </BrnchId>
        </DbtrAgt>
        <ChrgBr>SLEV</ChrgBr>
        <#list payments as payment>
        <#assign ebank=ebanks[payment_index]>
        <#assign entity=entities[payment_index]>
        <CdtTrfTxInf>
            <PmtId>
                <InstrId>${pfa.id}-${payment.tranid}</InstrId>
                <EndToEndId>${pfa.id}-${payment.tranid}</EndToEndId>
            </PmtId>
            <Amt>
                <InstdAmt Ccy="${getCurrencySymbol(cbank.custrecord_2663_currency)}">${formatAmount(getAmount(payment),"decLessThan1")}</InstdAmt>
            </Amt>
            <CdtrAgt>
                <FinInstnId>
                    <#if ebank.custrecord_2663_entity_bic?has_content>
                    <BIC>${ebank.custrecord_2663_entity_bic}</BIC>
                    </#if>
                    <#if ebank.custrecord_2663_entity_bank_code?has_content>
                    <ClrSysMmbId>
                        <MmbId>${ebank.custrecord_2663_entity_bank_code}</MmbId>
                    </ClrSysMmbId>
                    </#if>
                    <PstlAdr>
                        <Ctry>${ebank.custrecord_2663_entity_country_code}</Ctry>
                    </PstlAdr>
                </FinInstnId>
            </CdtrAgt>
            <Cdtr>
                <Nm>${setMaxLength(convertToLatinCharSet(buildEntityName(entity)),70)}</Nm>
                <PstlAdr>
                    <#if entity.address1?has_content>
                        <#assign address = entity.address1>
                        <#if entity.address2?has_content>
                            <#assign address = address + ' ' + entity.address2>
                        </#if>
                    </#if>
                    <#if entity.billcountrycode?has_content>
                    <Ctry>${entity.billcountrycode}</Ctry>
                    <#elseif entity.address1?has_content>
                    <Ctry>${entity.countrycode}</Ctry>
                    </#if>
                    <AdrLine>${address}</AdrLine>
                </PstlAdr>
            </Cdtr>
            <CdtrAcct>
                <#if ebank.custrecord_2663_entity_acct_no?has_content || ebank.custrecord_2663_entity_iban?has_content>
                <Id>
                    <#if ebank.custrecord_2663_entity_iban?has_content && ebank.custrecord_2663_entity_acct_no?has_content == false>
                    <IBAN>${ebank.custrecord_2663_entity_iban}</IBAN>
                    </#if>
                    <#if ebank.custrecord_2663_entity_acct_no?has_content && ebank.custrecord_2663_entity_iban?has_content == false>
                    <Othr>
                        <Id>${ebank.custrecord_2663_entity_acct_no}</Id>
                    </Othr>
                    </#if>
                </Id>
                </#if>
            </CdtrAcct>
            <#assign taxAmount = getTaxTotal(payment)>
            <#if taxAmount?has_content>
            <Purp>
                <Prtry>SVPPL</Prtry>
            </Purp>
            <RmtInf>
                <Strd>
                    <RfrdDocInf>
                        <Nb>${getReferenceNote(payment)}</Nb>
                    </RfrdDocInf>
                    <RfrdDocAmt>
                        <TaxAmt Ccy="PLN">${taxAmount}</TaxAmt>
                    </RfrdDocAmt>
                    <Invcr>
                        <Nm>VAT Registration</Nm>
                        <Id>
                            <OrgId>
                                <Othr>
                                    <Id>${ebank.custrecord_tax_id}</Id>
                                </Othr>
                            </OrgId>
                        </Id>
                    </Invcr>
                </Strd>
            </RmtInf>
            </#if>
        </CdtTrfTxInf>
        </#list>
    </PmtInf>
</CstmrCdtTrfInitn>
</Document>
<#rt>
#OUTPUT END#