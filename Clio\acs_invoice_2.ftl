<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>        
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td>
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    style="float: left; margin: 7px" width="250px" height="85px"  /> </#if>
                        </td>
                        <td align="right"><span class="title">${record@title}</span></td>
                    </tr>
                </table>
                <#assign subsidiary_name = record.subsidiary + "<br />">
                <table class="header" style="width: 100%;">                
                    <tr>
                        <td width="42%" class="addressheader"><b>${record.billaddress@label}</b></td>
                        <td width="30%" class="nameandaddress" style="line-height: 120%">${record.tranid@label}: <b> ${record.tranid} </b></td>
                        <td width="28%" align="left"><span class="nameandaddress">${subsidiary_name?keep_before("<br />")}</span></td>
                    </tr>
                    <tr>
                        <td width="42%" class="address"><#if record.billaddress?has_content>${record.billaddress}<#else>${record.entity}</#if></td>
                        <td width="30%" class="nameandaddress">${record.trandate@label}: <b> ${record.trandate} </b></td>
                        <td width="28%" align="left">
                            <span class="nameandaddress">${subsidiary.mainaddress_text?keep_after(subsidiary_name)}</span><br />
                            <span class="nameandaddress">Tax ID ${record.custbody_subsidiary_tax_id}</span><br />
                            <span class="nameandaddress">P: ${record.custbody_subsidiary_telephone}</span><br />
                            <span class="nameandaddress">F: ${record.custbody_subsidiary_fax}</span><br />
                            <span class="nameandaddress">${record.custbody_subsidiary_ap_email}</span><br />
                        </td>
                    </tr>
                    <tr>
                    </tr>
                    <tr>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table style="width: 100%; font-size: 8pt;">
                    <tr>
                        <td align="right" style="padding: 0;">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="25%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">

        <#assign formatted_items = record.custpage_formatted_values?eval />

        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th>${record.terms@label}</th>
                <th>${record.duedate@label}</th>
                <th>${record.otherrefnum@label}</th>
                <th>Clio Manage ID</th>
                <th>Clio Grow ID</th>
            </tr>
            <tr>
                <td style="padding-top: 2px;">${record.terms}</td>
                <td style="padding-top: 2px;">${record.duedate}</td>
                <td style="padding-top: 2px;">${record.otherrefnum}</td>
                <td style="padding-top: 2px;">${record.entity.custentity1}</td>
                <td style="padding-top: 2px;">${record.entity.custentity2}</td>
            </tr>
        </table>
        <#if formatted_items?has_content>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list formatted_items as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th align="center" width="10%" style="padding: 10px 6px;">Quantity</th>
                                <th width="35%" style="padding: 10px 6px;">Item</th>
                                <th width="12%" style="padding: 10px 6px; white-space: nowrap;">Sub. Start Date</th>
                                <th width="12%" style="padding: 10px 6px; white-space: nowrap;">Sub. End Date</th>
                                <th align="right" style="padding: 10px 6px;">Rate</th>
                                <th align="right" style="padding: 10px 6px;">Discount</th>
                                <th align="right" style="padding: 10px 6px;">Amount</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td align="center" line-height="150%">${item.quantity}</td>
                        <td><span style="font-weight: bold; line-height: 150%; color: #333333;">${item.item}</span><br />${item.description}
                        </td>
                        <td align="left">${item.custcol_suitesync_rev_rec_start}</td>
                        <td align="left">${item.custcol_suitesync_rev_rec_end}</td>
                        <td align="right">${item.rate}</td>
                        <td align="right">${item.discount?string("0.##")}%</td>
                        <td align="right">${item.amount}</td>
                    </tr>
                </#list><!-- end items -->
            </table>

            <hr />
        </#if>
        <table width="100%">
            <tr>
                <td width="50%">
                    <table style="page-break-inside: avoid; width: 100%; margin-top: 10px;">
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>${record.custbody_clio_customer_message}</td>
                        </tr>
                    </table>
                </td>
                <td width="50%">
                    <table style="page-break-inside: avoid; width: 100%; margin-top: 10px;">
                        <tr>
                            <td colspan="1">&nbsp;</td>
                            <td align="right" style="font-weight: bold; color: #333333;">${record.subtotal@label}</td>
                            <td align="right">${record.subtotal}</td>
                        </tr>
                        <tr>
                            <td colspan="1">&nbsp;</td>
                            <td align="right" style="font-weight: bold; color: #333333;">${record.taxtotal@label}
                                (${record.taxrate}%)</td>
                            <td align="right">${record.taxtotal}</td>
                        </tr>
                        <tr style="background-color: #e3e3e3; line-height: 200%;">
                            <td background-color="#ffffff" colspan="1">&nbsp;</td>
                            <td align="right" style="font-weight: bold; color: #333333;"><b>${record.currency}</b> &nbsp; &nbsp; &nbsp; ${record.total@label}</td>
                            <td align="right">${record.total}</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
</pdf>