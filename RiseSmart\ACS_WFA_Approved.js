/**
 *@NApiVersion 2.x
 *@NScriptType WorkflowActionScript
 */
define(['N/record', 'N/runtime'], function(record, runtime) {

    function onAction(scriptContext) {
        var bankDetailObj = scriptContext.newRecord;
        var approvalStatus = runtime.getCurrentScript().getParameter('custscript_approval_status');
        var vendorId = bankDetailObj.getValue({ fieldId: 'custrecord_2663_parent_vendor' });
        var vendorObj = record.load({
            id: vendorId,
            type: record.Type.VENDOR
        });
        
        if(approvalStatus == 2) {
            vendorObj.setValue({
                fieldId: 'custentity_2663_payment_method',
                value: true
            });
            vendorObj.save();
        } else {
            vendorObj.setValue({
                fieldId: 'custentity_2663_payment_method',
                value: false
            });
            vendorObj.save();
        }
    }

    return {
        onAction: onAction
    }
});
