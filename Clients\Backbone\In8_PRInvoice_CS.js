function pageInit(type) {

    try {
        // Move some UI elements to make easier to edit
        setTimeout(function () {
            window.document.getElementById('custpage_sublisttxt').click();

            setTimeout(function () {
                window.document.getElementById('custpage_sublist_grouptxt').click();

                setTimeout(function () {
                    window.document.getElementById('custpage_sublist_inv_div').style = 'margin-top:-20px';
                    window.document.getElementById('custpage_sublist_layer').style = 'min-width:100%;float:left;margin-top:20px';
                    window.document.getElementById('custpage_sublist_group_layer').style = 'min-width:100%;float:right;margin-top:20px';

                    window.document.getElementsByClassName('bgsubtabbar')[1].style.display = 'none';
                    window.document.getElementById('custpage_sublist_inv_layer').style.display = 'block';

                    nlapiCancelLineItem('custpage_sublist');
                }, 500);
            }, 500);
        }, 500);

        for (var i = 1; i <= nlapiGetLineItemCount('custpage_sublist_group'); i++) {
            nlapiInsertLineItemOption('custpage_sublist', 'custpage_group', i, 'Group ' + i);
        }

        for (var i = 1; i <= nlapiGetLineItemCount('custpage_sublist'); i++) {
            nlapiSetLineItemValue('custpage_sublist', 'custpage_group', i, nlapiGetLineItemValue('custpage_sublist', 'custpage_group_h', i));
        }

        nlapiDisableLineItemField('custpage_sublist', 'custpage_billable', true);
        nlapiDisableLineItemField('custpage_sublist', 'custpage_expense', true);
        nlapiDisableLineItemField('custpage_sublist', 'custpage_category', true);
        nlapiDisableLineItemField('custpage_sublist', 'custpage_category_disp', true);
        nlapiDisableLineItemField('custpage_sublist', 'custpage_date', true);
        nlapiDisableLineItemField('custpage_sublist', 'custpage_cardholder', true);

        nlapiDisableField('custpage_subtotal', true);

        calculateTotals();
    } catch (e) {
        console.log('Error: ' + e);
    }
}

function fieldChanged(sublist, fieldName, lineNum) {

    //console.log(sublist + ' - ' + fieldName);

    if (fieldName == 'custpage_num_groups') {

        //debugger;
        // remove the groups
        for (var i = 50; i >= 1; i--) {
            try {
                nlapiRemoveLineItemOption('custpage_sublist', 'custpage_group', i);
            } catch (e) {}
        }

        for (var i = nlapiGetLineItemCount('custpage_sublist_group'); i >= 1; i--) {
            try {
                nlapiRemoveLineItem('custpage_sublist_group', i);
            } catch (e) {}
        }

        var n = nlapiGetFieldValue('custpage_num_groups');

        for (var i = 1; i <= n; i++) {
            nlapiSetCurrentLineItemValue('custpage_sublist_group', 'custpage_group', i);
            nlapiCommitLineItem('custpage_sublist_group');

            nlapiInsertLineItemOption('custpage_sublist', 'custpage_group', i, 'Group ' + i);
        }
    }
    if (sublist == 'custpage_sublist_inv' && fieldName == 'custpage_selected') {
        //alert(nlapiGetLineItemValue('custpage_sublist_inv', 'custpage_internalid', lineNum));

        if (nlapiGetLineItemValue('custpage_sublist_inv', 'custpage_internalid', lineNum) &&
            nlapiGetLineItemValue('custpage_sublist_inv', 'custpage_selected', lineNum) == 'T') {
            setWindowChanged(window, false);

            var url = nlapiResolveURL('SUITELET', 'customscript_in8_pr_invoice_sl', 'customdeploy1');
            url += '&invoice=' + nlapiGetLineItemValue('custpage_sublist_inv', 'custpage_internalid', lineNum);
            url += '&custpage_sort=' + nlapiGetFieldValue('custpage_sort');
            window.location.href = url + getParameters() + '&search=T';
        } else {
            setWindowChanged(window, false);
            var url = nlapiResolveURL('SUITELET', 'customscript_in8_pr_invoice_sl', 'customdeploy1');
            url += '&invoice=0';
            url += '&custpage_sort=' + nlapiGetFieldValue('custpage_sort');
            window.location.href = url + getParameters() + '&search=T';
        }
    }
    if (sublist == 'custpage_sublist' && (fieldName == 'custpage_quantity' || fieldName == 'custpage_rate')) {
        // Recalculate amount
        if (nlapiGetCurrentLineItemValue('custpage_sublist', 'custpage_quantity') &&
            nlapiGetCurrentLineItemValue('custpage_sublist', 'custpage_rate')) {
            var quantity = parseFloat(nlapiGetCurrentLineItemValue('custpage_sublist', 'custpage_quantity'));
            var rate = parseFloat(nlapiGetCurrentLineItemValue('custpage_sublist', 'custpage_rate'));
            var amount = quantity * rate;
        
            nlapiSetCurrentLineItemValue('custpage_sublist', 'custpage_amount', amount);
        }
    }
}

function search() {

    if (!nlapiGetFieldValue('custpage_customer')) {
        alert('Please select a customer.');
        return;
    }
    setWindowChanged(window, false);

    var url = nlapiResolveURL('SUITELET', 'customscript_in8_pr_invoice_sl', 'customdeploy1');

    window.location.href = url + getParameters() + '&search=T';
}

function back() {

    setWindowChanged(window, false);

    var url = nlapiResolveURL('SUITELET', 'customscript_in8_pr_invoice_sl', 'customdeploy1');

    window.location.href = url;
}

function getParameters() {

    var parameters = '&custpage_customer=' + nlapiGetFieldValue('custpage_customer') +
        '&custpage_inv_status=' + nlapiGetFieldValue('custpage_inv_status') +
        '&custpage_sort=' + nlapiGetFieldValue('custpage_sort');
    //'&custpage_num_groups=' + nlapiGetFieldValue('custpage_num_groups')    

    return parameters;
}

function saveRecord() {

    var hasSelected = false;
    for (var i = 1; i <= nlapiGetLineItemCount('custpage_sublist_inv'); i++) {
        if (nlapiGetLineItemValue('custpage_sublist_inv', 'custpage_selected', i) == 'T') {
            hasSelected = true;
            break;
        }
    }
    if (!hasSelected) {
        alert('Please select an invoice.');
        return false;
    }

    var hasItems = false;
    for (var i = 1; i <= nlapiGetLineItemCount('custpage_sublist'); i++) {
        if (nlapiGetLineItemValue('custpage_sublist', 'custpage_billable', i) == 'T' &&
            nlapiGetLineItemValue('custpage_sublist', 'custpage_apply', i) == 'T') {
            hasItems = true;
            break;
        }
        if (nlapiGetLineItemValue('custpage_sublist', 'custpage_expense', i) == 'T' &&
            nlapiGetLineItemValue('custpage_sublist', 'custpage_apply', i) == 'T') {
            hasItems = true;
            break;
        }
        if (nlapiGetLineItemValue('custpage_sublist', 'custpage_billable', i) != 'T' &&
            nlapiGetLineItemValue('custpage_sublist', 'custpage_expense', i) != 'T') {
            hasItems = true;
            break;
        }
    }
    if (!hasItems) {
        alert('Please select an item.');
        return;
    }

    if (confirm('Confirm?')) {
        return true;
    } else {
        return false;
    }
}

function markAll() {
    for (var i = 1; i <= nlapiGetLineItemCount('custpage_sublist'); i++) {
        nlapiSelectLineItem('custpage_sublist', i);
        nlapiSetCurrentLineItemValue('custpage_sublist', 'custpage_apply', 'T');
        nlapiCommitLineItem('custpage_sublist');
    }
    calculateTotals();
}

function unMarkAll() {
    for (var i = 1; i <= nlapiGetLineItemCount('custpage_sublist'); i++) {
        nlapiSelectLineItem('custpage_sublist', i);
        nlapiSetCurrentLineItemValue('custpage_sublist', 'custpage_apply', 'F');
        nlapiCommitLineItem('custpage_sublist');
    }
    calculateTotals();
}

function calculateTotals() {
    var total = 0;

    for (var i = 1; i <= nlapiGetLineItemCount('custpage_sublist'); i++) {
        if (nlapiGetLineItemValue('custpage_sublist', 'custpage_billable', i) == 'T' &&
            nlapiGetLineItemValue('custpage_sublist', 'custpage_apply', i) == 'T') {
            total += parseFloat(nlapiGetLineItemValue('custpage_sublist', 'custpage_amount', i));
        }
        if (nlapiGetLineItemValue('custpage_sublist', 'custpage_expense', i) == 'T' &&
            nlapiGetLineItemValue('custpage_sublist', 'custpage_apply', i) == 'T') {
            total += parseFloat(nlapiGetLineItemValue('custpage_sublist', 'custpage_amount', i));
        }
        if (nlapiGetLineItemValue('custpage_sublist', 'custpage_billable', i) != 'T' &&
            nlapiGetLineItemValue('custpage_sublist', 'custpage_expense', i) != 'T') {
            total += parseFloat(nlapiGetLineItemValue('custpage_sublist', 'custpage_amount', i));
        }
    }
    nlapiSetFieldValue('custpage_subtotal', nlapiFormatCurrency(total));
}