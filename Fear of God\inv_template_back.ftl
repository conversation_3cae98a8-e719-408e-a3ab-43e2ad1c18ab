<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
    <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
    <#if .locale == "zh_CN">
        <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
    <#elseif .locale == "zh_TW">
        <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
    <#elseif .locale == "ja_JP">
        <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
    <#elseif .locale == "ko_KR">
        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
    <#elseif .locale == "th_TH">
        <link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
    </#if>
    <macrolist>
        <macro id="nlheader">
            <table class="header" style="width: 100%;"><tr>
    <td rowspan="3"><img src="https://4782866.app.netsuite.com/core/media/media.nl?id=4598&amp;c=4782866&amp;h=469566f458e329f6e340" style="margin: 7px; width: 22%; height: 22%" align="center" /></td>
    </tr>
</table>
            <table style="width: 100%; border: 1px solid black">
                <tr>
                    <td style="font-size: 9pt; font-weight: bold" align="right">INVOICE # ${record.tranid}</td>
                </tr>
                <tr>
                    <td style="font-size: 9pt" align="right">INVOICE DATE: ${record.trandate}</td>
                </tr>
                <tr>
                    <td style="font-size: 9pt" align="right">DUE DATE: ${record.duedate}</td>
                </tr>
            </table>
            <table style="padding-bottom: 5px; width: 100%;">
                <tr>
                    <td style="font-size: 9pt; font-weight: bold" align="left">BILL TO</td>
                    <td style="font-size: 8pt" align="left" colspan="4">${record.billaddress}</td>
                    <td style="font-size: 9pt; font-weight: bold" align="left">SHIP TO</td>
                    <td style="font-size: 8pt" align="left" colspan="4">${record.shipaddress}</td>
                </tr>
            </table>
        </macro>
        <macro id="nlfooter">
            <table style="padding-bottom: 5px; width: 100%;">
                <tr>
                    <td style="font-size: 8pt; font-weight: bold" align="center">Buyer is bound by the Conditions of Fear of God. A Monthly penalty charge of 5% late payment fee will be applied to all invoices not paid by the due date.</td>
                </tr>
                <tr>
                    <td style="font-size: 8pt; font-weight: bold" align="center">Any goods deemed faulty will only be accepted if returned within 2 weeks of the shipment.</td>
                </tr>
            </table>
        </macro>
    </macrolist>
    <style type="text/css">* {
        <#if .locale == "zh_CN">
            font-family: NotoSans, NotoSansCJKsc, sans-serif;
        <#elseif .locale == "zh_TW">
            font-family: NotoSans, NotoSansCJKtc, sans-serif;
        <#elseif .locale == "ja_JP">
            font-family: NotoSans, NotoSansCJKjp, sans-serif;
        <#elseif .locale == "ko_KR">
            font-family: NotoSans, NotoSansCJKkr, sans-serif;
        <#elseif .locale == "th_TH">
            font-family: NotoSans, NotoSansThai, sans-serif;
        <#else>
            font-family: NotoSans, sans-serif;
        </#if>
        }
        table {
            font-size: 8pt;
            table-layout: fixed;
        }
        th {
            font-weight: bold;
            font-size: 8pt;
            vertical-align: middle;
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
            color: #333333;
        }
        td {
            padding: 4px 4px;
        }
        td p { align:left }
        b {
            font-weight: bold;
            color: #333333;
        }
        table.header td {
            padding: 0;
            font-size: 8pt;
        }
        table.footer td {
            padding: 0;
            font-size: 8pt;
        }
        table.itemtable th {
            padding-bottom: 10px;
            padding-top: 10px;
        }
        table.body td {
            padding-top: 2px;
        }
        table.total {
            page-break-inside: avoid;
        }
        tr.totalrow {
            background-color: #e3e3e3;
            line-height: 200%;
        }
        td.totalboxtop {
            font-size: 12pt;
            background-color: #e3e3e3;
        }
        td.addressheader {
            font-size: 8pt;
            padding-top: 6px;
            padding-bottom: 2px;
        }
        td.address {
            padding-top: 0;
        }
        td.totalboxmid {
            font-size: 28pt;
            padding-top: 20px;
            background-color: #e3e3e3;
        }
        td.totalboxbot {
            background-color: #e3e3e3;
            font-weight: bold;
        }
        span.title {
            font-size: 28pt;
        }
        span.number {
            font-size: 16pt;
        }
        span.itemname {
            font-weight: bold;
            line-height: 150%;
        }
        hr {
            width: 100%;
            color: #d3d3d3;
            background-color: #d3d3d3;
            height: 1px;
        }
</style>
</head>
<body header="nlheader" header-height="25%" footer="nlfooter" footer-height="2%" padding="0.5in 0.5in 0.5in 0.5in" size="Letter-Landscape">
    <#if JSON.missingsizes != ''>
    <table style="padding-bottom: 15px; width: 100%; margin-left: -15px; margin-right: -15px;">
        <tr>
            <td style="background-color: #ff0000; font-size: 10pt; font-weight: bold; height: 20px;" align="center" colspan="3">These sizes are missing from the product size rank list: ${JSON.missingsizes}</td>
        </tr>
    </table>
    </#if>
    <table style="margin-bottom: 5px; width: 100%; border: 1px solid black">
        <tr>
            <td style="font-size: 8pt; border-right: 1px solid black; font-weight: bold" align="center">Division</td>
            <td style="font-size: 8pt; border-right: 1px solid black; font-weight: bold" align="center">Season</td>
            <td style="font-size: 8pt; border-right: 1px solid black; font-weight: bold" align="center">Due Date</td>
            <td style="font-size: 8pt; border-right: 1px solid black; font-weight: bold" align="center">Customer #</td>
            <td style="font-size: 8pt; font-weight: bold" align="center">Terms</td>
        </tr>
        <tr>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.csegfog_division}</td>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.custbodyfog_season_header}</td>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.duedate}</td>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.entity}</td>
            <td style="font-size: 8pt" align="center">${record.terms}</td>
        </tr>
        <tr>
            <td style="font-size: 8pt; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">Factor</td>
            <td style="font-size: 8pt; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">Store</td>
            <td style="font-size: 8pt; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">Ship Via</td>
            <td style="font-size: 8pt; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">Sales Order</td>
            <td style="font-size: 8pt; border-top: 1px solid black; font-weight: bold" align="center">Department #</td>
        </tr>
        <tr>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.custbodyfog_factor}</td>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.custbodyfog_store}</td>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.shipmethod}</td>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.createdfrom}</td>
            <td style="font-size: 8pt" align="center">${record.custbodyfog_department_number}</td>
        </tr>
        <tr>
            <td style="font-size: 8pt; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">No. of Cartons</td>
            <td style="font-size: 8pt; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">Account Number</td>
            <td style="font-size: 8pt; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">Sub-Div</td>
            <td style="font-size: 8pt; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">Incoterms</td>
            <td style="font-size: 8pt; border-top: 1px solid black; font-weight: bold" align="center">Tracking #</td>
        </tr>
        <tr>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.custbodyfog_no_cartons}</td>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.custbodyfog_ven_account_no}</td>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.custbodyfog_sub_div}</td>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.incoterm}</td>
            <td style="font-size: 8pt" align="center">${record.custbodyfog_tracking_np}</td>
        </tr>
        <tr>
            <td style="font-size: 8pt; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">Weight</td>
            <td style="font-size: 8pt; border-top: 1px solid black; font-weight: bold" align="center"></td>
            <td style="font-size: 8pt; border-top: 1px solid black; font-weight: bold" align="center"></td>
            <td style="font-size: 8pt; border-top: 1px solid black; font-weight: bold" align="center"></td>
            <td style="font-size: 8pt; border-top: 1px solid black; font-weight: bold" align="center"></td>
        </tr>
        <tr>
            <td style="font-size: 8pt; border-right: 1px solid black" align="center">${record.custbodyfog_weight_invoice}</td>
            <td style="font-size: 8pt" align="center"></td>
            <td style="font-size: 8pt" align="center"></td>
            <td style="font-size: 8pt" align="center"></td>
            <td style="font-size: 8pt" align="center"></td>
        </tr>
    </table>
<#assign totalUnits = 0>
<#assign totalAmount = 0>

<#list JSON.division as division>
<#assign counter = 0>
<#assign counter1 = 0>
    <table style="width: 100%">
    <#list division.sizes as sizes>
        <#assign counter = counter + 1>
    </#list>
    <#assign counter1 = '"' + counter + '"'>
        <tr style="border: 1px solid black">
            <td style="font-size: 8pt; font-weight: bold; width: 85px" align="center" rowspan="2">Style</td>
            <td style="font-size: 8pt; font-weight: bold; width: 85px" align="center" rowspan="2">Style Des</td>
            <td style="font-size: 8pt; font-weight: bold; width: 85px" align="center" rowspan="2">Fabric</td>
            <td style="font-size: 8pt; font-weight: bold; width: 85px" align="center" rowspan="2">Color</td>
        <#if counter != 0>
            <td style="font-size: 8pt; font-weight: bold" align="center" rowspan="1" colspan=${counter1}>Sizes &amp; Qty's</td>
        <#else>
            <td style="font-size: 8pt; font-weight: bold" align="center" rowspan="1">Sizes &amp; Qty's</td>
        </#if>
            <td style="font-size: 8pt; font-weight: bold; width: 85px" align="center" rowspan="2">Qty</td>
            <td style="font-size: 8pt; font-weight: bold; width: 85px" align="center" rowspan="2">Price (USD)</td>
            <td style="font-size: 8pt; font-weight: bold; width: 85px" align="center" rowspan="2">Ext Price (USD)</td>
        </tr>
        <tr>
            <#if counter != 0>
                <#list division.sizes as sizes>
                    <td style="font-size: 8pt; font-weight: bold" align="center" rowspan="1">${sizes}</td>
                </#list>
            <#else>
                <td style="font-size: 8pt" align="center" rowspan="1"></td>
            </#if>
        </tr>
        <#assign total = 0>
        <#list division.item as item>
        <#assign totalQty = 0>
        <#if item.quantity != "0">
            <#assign totalQty = item.quantity?number>
        </#if>
        <tr>
            <td style="font-size: 8pt; width: 85px" align="center">${item.name}</td>
            <td style="font-size: 8pt; width: 85px" align="center">${item.displayName}</td>
            <td style="font-size: 8pt; width: 85px" align="center">${item.fabric}</td>
            <td style="font-size: 8pt; width: 85px" align="center">${item.color}: ${item.colordes}</td>
            <#if counter != 0>
                <#list item.allsizes as sizes>
                    <td style="font-size: 8pt" align="center">${sizes.quantity}</td>
                    <#assign totalQty = totalQty + sizes.quantity?number>
                </#list>
            <#else>
                <td style="font-size: 8pt" align="center"></td>
            </#if>
            <td style="font-size: 8pt; width: 85px" align="center">${totalQty}</td>
            <#assign totalAmt = item.rate?number * totalQty>
            <#assign totalUnits = totalUnits + totalQty?number>
            <#assign totalAmount = totalAmount + totalAmt?number>
            <td style="font-size: 8pt; width: 85px" align="center">$${(item.rate?number)?string(",##0.00")}</td>
            <td style="font-size: 8pt; width: 85px" align="center">$${totalAmt?string(",##0.00")}</td>
        </tr>
        <tr>
            <#assign counter2 = counter + 3>
            <#assign counter3 = '"' + counter2 + '"'>
            <td style="font-size: 8pt" align="left" colspan=${counter3}>
                <#if item.sellingPeriod != ''><b>Selling Period: </b>${item.sellingPeriod}<br /></#if>
                <#if item.hts != ''><b>HTS#: </b>${item.hts} </#if>
                <#if item.producedBy != ''><b>Produced by: </b>${item.producedBy} </#if>
                <#if item.countryOrigin != ''><b>Country of Origin: </b>${item.countryOrigin} </#if>
                <#if item.content != ''><b>Content: </b>${item.content} </#if>
                <#if item.content != ''><br /><b>Composition: </b>${item.content} </#if>
            </td>
        </tr>
        </#list>
    </table>
    </#list>
    <table style="width: 100%; font-weight: bold">
        <tr style="font-weight: bold">
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
            <td style="font-size: 8pt; border-top: 1px solid black" align="center"></td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="right">TOTAL UNITS</td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center">${totalUnits}</td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
        </tr>
    </table>
    <table style="width: 100%">
        <tr>
            <td style="padding: 0px">
                <table style="width: 100%; border: 1px solid black">
                    <tr>
                        <td style="font-size: 8pt; font-weight: bold" align="center">REMIT PAYMENT TO</td>
                    </tr>
                    <tr>
                        <td style="font-size: 8pt" align="left">${record.custbodyfog_remit_payment}</td>
                    </tr>
                </table>
            </td>
            <td style="padding: 0px">
                <table style="width: 100%; font-weight: bold">
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt" align="right">Merchandise Total: ${record.subtotal}</td>
                    </tr>
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt" align="right">Tax Total: ${record.taxtotal}</td>
                    </tr>
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt" align="right">Shipping Cost: ${record.altshippingcost}</td>
                    </tr>
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt" align="right">Discount: ${record.discounttotal}</td>
                    </tr>
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt" align="right">Amount Paid: ${record.amountpaid}</td>
                    </tr>
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt" align="right">Amount Due: ${record.amountremaining}</td>
                    </tr>
                </table>
            </td>
        </tr>

    </table>
    <table style="width: 100%">
        <tr style="margin-top: 10px">
            <td style="font-size: 8pt; font-weight: bold" align="left">SPECIAL INSTRUCTIONS</td>
        </tr>
        <tr style="margin-top: 10px">
            <td style="font-size: 8pt" align="left">${record.custbodyfog_special_instructions}</td>
        </tr>
    </table>
</body>
</pdf>
