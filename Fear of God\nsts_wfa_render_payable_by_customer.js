/**
 * @NScriptType workflowactionscript
 * @NAPIVersion 2.0
 *
 * 
 * CHANGELOG
 * 01/15/2021 - JAGONZALES
 * - changed line 210 (content: result1.getText('custcolfog_content'),) to (content: result1.getValue('custcolfog_content'),)
 * - added contentArr to be able to render item.content in PDF (for some unknown reason, item.content won't render even if it has the correct value)
 *
 * 02/01/2021 - JAGONZALES
 * - added a condition in line 138-140 to handle rows with different styles but same color
 */

define(["N/http", "N/file", "N/record", "N/render", "N/redirect", "N/runtime", "N/search", "N/error"],
    function(http, file, record, render, redirect, runtime, search, error) {
        var NSUtils = {
            inArray: function(stValue, arrValue) {
                for (var i = arrValue.length - 1; i >= 0; i--) {
                    if (stValue == arrValue[i]) {
                        break;
                    }
                }
                return (i > -1);
            }
        };
        function onAction(context) {
            var stMethodName = 'onAction';
            try {
                var myRec = context.newRecord;
                var recId = myRec.id;
                // log.debug('record id', recId);

                var xmlTemplateFile = file.load('SuiteScripts/nsts_inv_payable_by_customer.xml');
                var renderer = render.create();
                renderer.templateContent = xmlTemplateFile.getContents();

                renderer.addRecord('record', record.load({
                    type: record.Type.INVOICE,
                    id: recId
                }));

                var mySearch = search.load({
                    id: 'customsearch_nsts_transaction_search'
                });

                var myFilter = search.createFilter({
                    name: 'internalid',
                    operator: search.Operator.IS,
                    values: [recId]
                });

                var myFilter2 = search.createFilter({
                    name: 'recordtype',
                    operator: search.Operator.IS,
                    values: 'invoice'
                });

                mySearch.filters.push(myFilter);
                mySearch.filters.push(myFilter2);
                // mySearch.columns.push(myColumn);
                var mySearchResults = mySearch.run().getRange(0, 1000);
                // log.debug('my search results', mySearchResults);

                var sizeSearch = search.load({
                    id: 'customsearch_nsts_size_rank_list_search'
                });
                var sizeSearchResults = sizeSearch.run().getRange(0, 1000);
                // log.debug('sizeSearchResults', sizeSearchResults);

                var sizeList = [];

                sizeSearchResults.forEach(function(result) {
                    sizeList.push(result.getText('custrecordfog_size_list'))
                });
                // log.debug('sizeList', sizeList);

                var arrSizes = [];
                var arrItem = [];
                var tempStr = {};
                var colorDes = '';
                var itemName = '';
                var noSizeQty = 0;
                var diffColor = [];
                var divStr = {};
                var divArr = [];
                var sortedSizes = [];
                var missingSizes = '';
                var diffSizes = [];

                mySearchResults.forEach(function(result4) {
                    var temp = result4.getText({
                        name: 'custitem_psgss_product_size',
                        join: 'CUSTCOLFOG_STYLE'
                    });
                    temp = temp.split(",");
                    // log.debug('temp', temp);
                    for (var i = 0; i < temp.length; i++) {
                        if (!NSUtils.inArray(temp[i], diffSizes)) {
                            diffSizes.push(temp[i]);
                        }
                    }
                    // log.debug('diffSizes', diffSizes);
                });


                var lastDiv = '';
                // ADDED 01/15/2021 -- JAGONZALES
                var contentArr = [];

                mySearchResults.forEach(function(result3) { // RESULT 3
                    var currentDiv = result3.getValue('formulatext');
                    if (lastDiv != currentDiv) {

                        var lastStyle = 'z';
                        mySearchResults.forEach(function(result) { // RESULT 
                            var currentStyle = result.getText('custcolfog_style');
                            if (isEmpty(currentStyle)) {
                                currentStyle = result.getText('item');
                            }
                            // log.debug('currentStyle', currentStyle);
                            if ((currentDiv == result.getValue('formulatext')) && (lastStyle != currentStyle)) {
                                var lastColor = null;
                                diffColor = [];
                                mySearchResults.forEach(function(result1) { // RESULT 1
                                    var currentColor = result1.getText('custcol_product_color');
                                    // log.debug('currentColor', currentColor);
                                    // log.debug('currentStyle', currentStyle);
                                    // log.debug('lastColor', lastColor);
                                    var temp = result1.getText('custcolfog_style');
                                    if(isEmpty(temp)) {
                                        temp = result1.getText('item');
                                    }


                                    // ADDED 02/01/2021 -- JAGONZALES
                                    // TO HANDLE CONSECUTIVE STYLES WITH SAME COLOR
                                    if((currentDiv == result1.getValue('formulatext')) && (currentStyle == temp) && (lastColor == result1.getText('custcol_product_color'))){
                                        lastColor = null;
                                    }

                                    if ((currentDiv == result1.getValue('formulatext')) && (currentStyle == temp) && (lastColor != result1.getText('custcol_product_color'))) {                                        
                                        arrSizes = [];

                                        sortedSizes = [];

                                        for (var i = 0; i < sizeList.length; i++) {
                                            for (var j = 0; j < diffSizes.length; j++) {
                                                if (sizeList[i] == diffSizes[j]) {
                                                    sortedSizes.push(diffSizes[j]);
                                                }
                                            }
                                        }
                                        // log.debug('sortedSizes', sortedSizes);

                                        for (var i = 0; i < sortedSizes.length; i++) {
                                            arrSizes.push({size: sortedSizes[i], quantity: 0});
                                        }

                                        mySearchResults.forEach(function(result2) {
                                            var temp2 = result2.getText('custcolfog_style');
                                            if(isEmpty(temp2)) {
                                                temp2 = result2.getText('item');
                                            }
                                            if ((currentDiv == result2.getValue('formulatext')) && (currentStyle == temp2) && (currentColor == result2.getText('custcol_product_color'))) {
                                                if (sortedSizes.length == 0) {
                                                    noSizeQty = result2.getValue('quantity');
                                                }
                                                // log.debug('quantity', result2.getValue('quantity'));
                                                // log.debug('size', result2.getText('custcol_product_size'));
                                                //UPDATE SIZE
                                                for (var i = 0; i < sortedSizes.length; i++) {
                                                    if (sortedSizes[i] == result2.getText('custcol_product_size')) {
                                                        arrSizes[i].quantity = result2.getValue('quantity');
                                                    }
                                                    if (isEmpty(result2.getText('custcol_product_size'))) {
                                                        noSizeQty = result2.getValue('quantity');
                                                    }
                                                }
                                                if ((result2.getText('custcol_product_size') != "")
                                                    && !NSUtils.inArray((result2.getText('custcol_product_size')), sizeList)) {
                                                    if (missingSizes == '') {
                                                        missingSizes = result2.getText('custcol_product_size');
                                                    }
                                                    else {
                                                        missingSizes = missingSizes + ', ' + result2.getText('custcol_product_size');
                                                    }
                                                }
                                            }
                                        });
                                        // log.debug('arrSizes', arrSizes);
                                        // log.debug('missingSizes', missingSizes);

                                        colorDes = result1.getValue({
                                            name: 'custitem_psgss_product_color_desc',
                                            join: 'item'
                                        });
                                        // log.debug('colorDes', colorDes);

                                        if (colorDes == null) {
                                            colorDes = '';
                                        }

                                        itemName = result1.getText('custcolfog_style');

                                        if (isEmpty(itemName)) {
                                            itemName = result1.getText('item');
                                        }

                                        var disName = result1.getValue({
                                            name: 'displayname',
                                            join: 'item'
                                        });

                                        // log.debug('janel', disName);

                                        // log.debug('diffColor', diffColor);
                                        if (!NSUtils.inArray(result1.getText('custcol_product_color'), diffColor)) {
                                            tempStr = {
                                                name: itemName,
                                                displayName: disName,
                                                colordes: colorDes,
                                                content: result1.getValue('custcolfog_content'),
                                                color: result1.getText('custcol_product_color'),
                                                rate: result1.getValue('rate'),
                                                allsizes: arrSizes,
                                                quantity: noSizeQty,
                                                fabric: result1.getValue('custcolfog_fabric_line'),
                                                season: result1.getText('custcolfog_season_transaction_line'),
                                                sellingPeriod: result1.getText('custcolfog_selling_period_line'),
                                                origin: result1.getText('custcolfog_origin'),
                                                hts: result1.getValue('custcolfog_item_hts_code_line'),
                                                producedBy: result1.getValue('custcolfog_produced_by'),
                                                midCode: result1.getValue('custcolfog_mid_code'),
                                                upper: result1.getValue('custcolfog_upper'),
                                                upperSci: result1.getValue('custcolfog_upper_scientific_name'),
                                                countryOrigin: result1.getText({ name: 'custitemfog_country_of_origin', join: 'item' }),
                                                lining: result1.getValue('custcolfog_lining'),
                                                liningSci: result1.getValue('custcolfog_lining_scientific'),
                                                coo: result1.getText('custcolfog_coo'),
                                                sole: result1.getValue('custcolfog_sole'),
                                                source: result1.getValue('custcolfog_source')
                                            }
                                            arrItem.push(tempStr);
                                            diffColor.push(result1.getText('custcol_product_color'));
                                            // ADDED 01/15/2021 -- JAGONZALES
                                            contentArr.push(tempStr.content);

                                        }
                                        log.debug('arrItem', arrItem);

                                        noSizeQty = 0;

                                    }
                                    lastColor = result1.getText('custcol_product_color');
                                });
                            }
                            lastStyle = itemName;
                        });
                        divStr = {
                            sizes: sortedSizes,
                            item: arrItem
                        };
                        divArr.push(divStr);
                        // log.debug('divArr', divArr);
                        tempStr = {};
                        arrItem = [];
                        sortedSizes = [];
                    }
                    lastDiv = result3.getValue('formulatext');
                });

                // log.debug('contentArr', contentArr);
                renderer.addCustomDataSource({
                    format: render.DataSource.OBJECT,
                    alias: "JSON",
                    data: {
                        division: divArr,
                        missingsizes: missingSizes,
                        contentArr: contentArr
                    }
                });

                var soPdf = renderer.renderAsPdf();
                // log.debug('soPdf', soPdf);

                soPdf.folder = 1189;
                soPdf.name = recId + "_payable_by_customer";
                var fileId = soPdf.save();
                var fileIdReloaded = file.load({
                    id: fileId
                });

                // log.debug('soPdf url', fileIdReloaded.url);

                redirect.redirect({
                    url: fileIdReloaded.url
                });

                // log.debug('fileId', fileId);

            }
            catch(error){
                var stError = (error.message !== undefined) ? error.name + ' : ' + error.message : error.toString();
                log.error(stMethodName, 'Catch : ' + stError);
                throw stError;
            }
        }

        function isEmpty(value){
            return value === '' || value === null || value === undefined;
        }

        return {
            onAction : onAction
        };
    });
