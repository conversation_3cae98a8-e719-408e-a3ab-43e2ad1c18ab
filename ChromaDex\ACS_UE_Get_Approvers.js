/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/search', 'N/runtime', 'N/record'], function(search, runtime, record) {
    
    /**
     * 
     * @param {string} approverId - id of the approver
     * @param {float} total - total amount of the PO
     * @param {boolean} firstApprover - if first approver in process
     */
    function getApprover(approverId, total, firstApprover){
        if(approverId){
            var empLookUp = search.lookupFields({
                type: 'employee',
                id: approverId,
                columns: [
                    'purchaseorderapprover',
                    'purchaseorderapprovallimit'
                ]
            });

            log.debug('test', {
                total: total,
                approval_limit: empLookUp,
                first_approver: firstApprover,
                result: total > parseFloat(empLookUp.purchaseorderapprovallimit) || firstApprover
            });

            if(empLookUp.purchaseorderapprover.count != 0){
                if(total > parseFloat(empLookUp.purchaseorderapprovallimit) || firstApprover){
                    if(empLookUp.purchaseorderapprover.length > 0) {
                        return empLookUp.purchaseorderapprover[0].value;
                    } else {
                        return "";
                    }
                }
            }

            return "";
                
        }

        return "";
    }

    function afterSubmit(context) {
        if(context.type == context.UserEventType.CREATE){
            var loggedUser = runtime.getCurrentUser();
            var approvers = ["", "", "", "", "", ""];

            var recordObj = record.load({
                type: record.Type.PURCHASE_ORDER,
                id: context.newRecord.id,
                isDynamic: true
            });

            // get the total to check if total amount is beyond the approver's approver limits
            var total = parseFloat(recordObj.getValue({ fieldId: 'total' }));

            // get the first approver
            approvers[0] = getApprover(loggedUser.id, total, true);
            recordObj.setValue({ fieldId: "custbody_acs_approver_1", value: approvers[0] });

            // get the approver of those approvers until you hit someone who's above the purchase approver limit.
            for(var i = 1; i < approvers.length; i++){
                var approver = getApprover(approvers[i-1], total, false);
                if(approver == ""){
                    break;
                }
                approvers[i] = approver;
                recordObj.setValue({ fieldId: "custbody_acs_approver_" + (i+1), value: approvers[i] });
            }

            log.debug(approvers);

            recordObj.save();
        } else if (context.type == context.UserEventType.EDIT) {

            var approvers = ["", "", "", "", "", ""];

            // get old total
            var oldRec = context.oldRecord;
            var oldTotal = parseFloat(oldRec.getValue({ fieldId: 'total' }));

            // get new total
            var newRec = context.newRecord;
            var newTotal = parseFloat(newRec.getValue({ fieldId: 'total' }));

            // check if the total changed
            if(newTotal > oldTotal){
                var requestor = newRec.getValue({ fieldId: 'recordcreatedby' });
                var recordObj = record.load({
                    type: record.Type.PURCHASE_ORDER,
                    id: newRec.id,
                    isDynamic: true
                });

                // get the new list of approvers for the person editing
                approvers[0] = getApprover(requestor, newTotal, true);
                recordObj.setValue({ fieldId: "custbody_acs_approver_1", value: approvers[0] });
                for(var i = 1; i < approvers.length; i++){
                    var approver = getApprover(approvers[i-1], newTotal, false);
                    if(approver == ""){
                        break;
                    }
                    approvers[i] = approver;
                    recordObj.setValue({ fieldId: "custbody_acs_approver_" + (i+1), value: approvers[i] });
                }
    
                log.debug(approvers);
    
                recordObj.save();

            }

        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
