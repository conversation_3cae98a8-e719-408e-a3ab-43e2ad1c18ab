/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
 define(['N/record', 'N/search', 'N/format/i18n'], function(record, search, format) {

    function isEmpty(stValue) {
        return ((stValue === '' || stValue === null || stValue === undefined) || (stValue.constructor === Array && stValue.length === 0) || (stValue.constructor === Object && (function (v) {
            for (var k in v)
                return false;
            return true;
        })(stValue)));
    }

    function createSubsidiaryObj(recObj) {
        var subsidiary = {};

        var subsidiaryId = recObj.getValue({ fieldId: 'subsidiary' });
        var subObj = record.load({
            type: record.Type.SUBSIDIARY,
            id: subsidiaryId,
            isDynamic: true
        });

        var subsidiaryFields = subObj.getFields();
        var includedFields = ["currency"];
        for(var i = 0; i < subsidiaryFields.length; i++) {
            if(!subsidiary.hasOwnProperty(subsidiaryFields[i])){
                if(includedFields.indexOf(subsidiaryFields[i]) === -1) continue;
                var value = subObj.getValue({ fieldId: subsidiaryFields[i] });
                var text = subObj.getValue({ fieldId: subsidiaryFields[i] });
                subsidiary[subsidiaryFields[i]] = {
                    value: value,
                    text: text
                };
            }
        }

        return subsidiary;

    }

    function createInvoicesObj(recObj) {
        
        var invoices = {};

        var lineCount = recObj.getLineCount({ sublistId: "invoicegroup" });

        // TODO: GET INVOICE DATA TO GET DUE DATE FOR EACH INVOICE
        for(var i = 0; i < lineCount; i++) {
            // get invoice data here
        }

    }
    function onRequest(context) {

        try {
            var recId = context.request.parameters.recid;
            var recObj = record.load({
                type: 'invoicegroup',
                id: recId,
                isDynamic: true
            });


            var subsidiaryData = createSubsidiaryObj(recObj);
            var invoiceData = createInvoicesObj(recObj);

            var additionalData = {
                subsidiary: subsidiaryData,
                invoice_data: invoiceData
            }

            var returnStr = "<#assign additional_data = " + JSON.stringify(additionalData) + " />"; 

            context.response.write({
                output: returnStr
            });

        } catch (e) {

            log.error('error occured', e);

            var returnStr = "<#assign additional_data = '' />"; 

            context.response.write({
                output: returnStr
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
