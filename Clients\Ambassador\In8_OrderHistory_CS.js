function payInvoice(id, name, userId, customer, hash, amount, recordType) {

    var url = nlapiResolveURL('SUITELET', 'customscript_in8_creditcards_sl', 'customdeploy_in8_creditcards_sl', true);

    var title = '';

    url += '&userid=' + userId +
        '&custpage_customer=' + customer +
        '&hash=' + hash +
        '&amount=' + amount +
        '&amountremaining=' + amount +
        '&invoicename=' + escape(name);

    if (recordType == 'invoice') {
        url += '&invoice=' + id;
        title = 'Pay Invoice';
    } else {
        url += '&transaction=' + id;
        title = 'Pay';
    }

    // Open the popup
    nlExtOpenWindow(url, '', 500, 400, '', false, title);
}

function search() {
    setWindowChanged(window, false);

    //var url = nlapiResolveURL('SUITELET', 'customscript_in8_order_history_sl', 'customdeploy_in8_order_history_sl');

    //window.location.href = url + getParameters() + '&search=T';

    var url = window.location.href.replace('custpage_tranid', 'z');
    url = url.replace('custpage_ref_code', 'z');
    url = url + getParameters();
    window.location.href = url;
}

function getParameters() {

    var parameters = "&custpage_tranid=" + nlapiGetFieldValue('custpage_tranid');
    parameters += "&custpage_ref_code=" + nlapiGetFieldValue('custpage_ref_code');

    return parameters;
}