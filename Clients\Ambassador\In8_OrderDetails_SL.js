// https://tstdrv1330281.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1161&deploy=1&compid=TSTDRV1330281&h=c0ce18f191a69bc0ef00&custpage_customer=28293&custpage_type=SalesOrd&hash=1900ff503bbe8d759f019980fa66d785b93416f134a6ea4a1ef7daa1b21ece14&userid=0


/**
 * Order History Suitelet
 * 
 * Version    Date            Author           Remarks
 * 1.00       27 Jan 2020     Marcel <PERSON>na   Initial Version
 *
 */

//var secretKey = 'ac8420dd-5069-4877-8d19-c017c12706b2';
var secretKey = 'c42bbaaf-d96a-4d0d-a30b-5704dae8986e';

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response) {

    if (request.getMethod() == 'GET') {
        app.handleGet();
    } else {
        app.handlePost();
    }
}

var app = (function () {

    var FORM_NAME = 'Order Status', // Form Name
        LIST_NAME = 'Orders History', // List Name
        SUBMIT_BUTTON = 'Submit', // Submit button caption
        MAX_RECORDS = 1000, // Maximum number of records to display on the sublist
        MARKALL_ENABLED = false, // Mark all option enabled
        tranType;

    /**
     * Handles Suitelet GET Method
     * 
     * @returns {Void}
     */
    var handleGet = function () {

        var form,
            subList,
            searchResults;

        try {
            //var userId = request.getParameter('userid');
            //var hash = request.getParameter('hash');
            //var customer = request.getParameter('custpage_customer');
            tranType = request.getParameter('custpage_type') ? request.getParameter('custpage_type') : 'SalesOrd';

            //var hashCompare = String(CryptoJS.SHA256(secretKey + userId + customer));

            // if (!userId || hash != hashCompare) {
            //     displayMessage('Invalid access credentials.hash' + hash + ' - ' + hashCompare);
            //     return;
            // }
            // if (!customer) {
            //     displayMessage('Invalid parameters.');
            //     return;
            // }

            var html = renderHTML();

            response.write(html);

        } catch (e) {
            displayMessage('An error occurred. ' + (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
        }
    };

    function renderHTML() {
        var renderer = nlapiCreateTemplateRenderer(),
            searchResults,
            record,
            template,
            html,
            file;

        var filerec = nlapiLoadFile(1064312);
        var template = filerec.getValue();    

        template = template.replace('{custpage_zip}', request.getParameter('custpage_zip') || '');
        template = template.replace('{custpage_tranid}', request.getParameter('custpage_tranid') || '');

        if (request.getParameter('custpage_tranid') &&
            request.getParameter('custpage_zip')) {
            template = template.replace('{search}', 'true');
        } else {
            template = template.replace('{search}', 'false');
        }

        renderer.setTemplate(template);

        var searchResults = getSearchResults() || [];
        
        renderer.addSearchResults('records', searchResults);

        if (searchResults.length) {

            nlapiLogExecution('DEBUG', 'in8', 'Load record : ' + searchResults[0].getValue('internalid'));

            var record = nlapiLoadRecord('salesorder', searchResults[0].getValue('internalid'));

            renderer.addRecord('record', record);

            var search = nlapiSearchRecord('itemfulfillment', null, [
                new nlobjSearchFilter('createdfrom', null, 'anyof', searchResults[0].getValue('internalid')),
                new nlobjSearchFilter('mainline', null, 'is', 'T')
            ], [
                new nlobjSearchColumn('internalid'),
                new nlobjSearchColumn('custbody_amb_tracking_reference_number')
            ]) || [];

            if (search.length) {
                renderer.addSearchResults('fulfillments', search);
            }
        }
        
        nlapiLogExecution('DEBUG', 'in8', 'Search len: ' + searchResults.length);
        // Render to string
        html = renderer.renderToString();
        return html;
    }

    /**
     * Handles Suitelet POST method
     * 
     * @returns {Void} 
     */
    var handlePost = function () {

        for (var i = 1; i <= request.getLineItemCount('custpage_sublist'); i++) {
            if (request.getLineItemValue('custpage_sublist', 'custpage_selected', i) == 'T') {
                // TODO: Process the line
            }
        }

        // Reloads window
        handleGet();
    };        

    /**
     * Get the Searh Results
     * 
     * @returns {Void} 
     */
    var getSearchResults = function () {

        var filters = [],
            columns = [],
            i = 0;
        
        if (!request.getParameter('custpage_tranid') ||
        !request.getParameter('custpage_zip')) {
            return [];
        }
        filters = [
            ['mainline', 'is', 'T'], 'AND',
            ['type', 'anyof', tranType]
        ]

        if (request.getParameter('custpage_customer')) {
            //filters.push( 'AND', ['entity', 'anyof', request.getParameter('custpage_customer')]);
        } 
        //filters[i++] = new nlobjSearchFilter('entity', null, 'anyof', request.getParameter('custpage_customer'));

        if (request.getParameter('custpage_tranid')) {
            filters.push( 'AND', [['tranid', 'contains', request.getParameter('custpage_tranid')], 'OR', ['otherrefnum', 'equalto', request.getParameter('custpage_tranid')]]);
            //filters.push( 'AND', [['tranid', 'contains', request.getParameter('custpage_tranid')]]);
        }
        //filters[i++] = new nlobjSearchFilter('tranid', null, 'contains', request.getParameter('custpage_tranid'));

        if (request.getParameter('custpage_zip')) {
            filters.push( 'AND', [['shipzip', 'is', request.getParameter('custpage_zip')], 'OR', ['billzip', 'is', request.getParameter('custpage_zip')]]);
        }
        //filters[i++] = new nlobjSearchFilter('tranid', null, 'contains', request.getParameter('custpage_tranid'));

        //filters[i++] = new nlobjSearchFilter('mainline', null, 'is', 'T');
        //filters[i++] = new nlobjSearchFilter('type', null, 'anyof', tranType);

        nlapiLogExecution('DEBUG', 'filters', JSON.stringify(filters));
        i = 0;

        columns[i++] = new nlobjSearchColumn('internalid');
        columns[i++] = new nlobjSearchColumn('transactionname');
        columns[i++] = new nlobjSearchColumn('transactionnumber');
        columns[i++] = new nlobjSearchColumn('trandate');
        columns[i++] = new nlobjSearchColumn('status');
        columns[i++] = new nlobjSearchColumn('entity');
        columns[i++] = new nlobjSearchColumn('amount');

        // if (deliveryConfirmField) {
        //     columns[i++] = new nlobjSearchColumn(deliveryConfirmField);
        // }
        if (tranType == 'CustInvc') {
            columns[i++] = new nlobjSearchColumn('amountremaining');
        }
        return nlapiSearchRecord('transaction', null, filters, columns);
    };

    /**
     * Displays a message
     * 
     * @param {String} message Message
     * @returns {Void}
     */
    var displayMessage = function (message) {

        // Create a NetSuite form
        var form = nlapiCreateForm(FORM_NAME, false),
            html = message;

        // Add a new HTML field to display the HTML contents
        field = form.addField('file', 'inlinehtml', 'label');
        field.setLayoutType('outsidebelow');
        field.setDefaultValue('<font size="2pt">' + html + '</font>');

        form.addButton('custombutton_back', 'Back', 'window.history.back()');

        response.writePage(form);
    };

    return {
        handleGet: handleGet,
        handlePost: handlePost
    };
})();