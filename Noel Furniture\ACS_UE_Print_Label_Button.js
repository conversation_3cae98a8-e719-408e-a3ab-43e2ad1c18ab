/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define([], function() {

    function beforeLoad(context) {
        if(context.type == context.UserEventType.VIEW){
            var getForm = context.form;
            getForm.clientScriptModulePath = 'SuiteScripts/ACS_CS_Print_Labels_Redirect.js';
            getForm.addButton({
                id: 'custpage_print_labels',
                label: 'Print Labels',
                functionName: 'redirectToPDF()'
            });
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
