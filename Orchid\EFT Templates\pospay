<#-- format specific processing -->

<#function getVoidCheckIndicator payment>
    <#assign value = "">
    <#assign reversalDate = payment.reversaldate>
    <#if reversalDate?has_content>
        <#assign value = "V">
    </#if>
    <#return value>
</#function>

<#function getChequeNumber payment>
	<#assign value = "">
    <#if payment.recordtype == "cashrefund">
        <#assign value = payment.otherrefnum>
    <#else>
    	<#assign value = payment.tranid>
    </#if>
    <#return value>
</#function>

<#-- template building -->

#OUTPUT START#
<#list payments as payment>
    <#assign entity = entities[payment_index]>
D001${setPadding(cbank.custpage_pp_custrecord_2663_acct_num,"left","0",14)}${setPadding(getChequeNumber(payment),"left","0",10)}${setPadding(formatAmount(getAmount(payment)),"left","0",13)}${setLength(payment.trandate?string("yyyyMMdd"),8)}${setLength("",30)}${setLength(buildEntityName(entity,true),80)}${setLength(getVoidCheckIndicator(payment),1)}
</#list>
#OUTPUT END#