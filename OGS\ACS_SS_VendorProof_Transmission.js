/**
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
 define(['N/file', "N/sftp", 'N/runtime', 'N/search'], function (file, sftp, runtime, search) {

            
    var scriptObj = runtime.getCurrentScript();

    // get SFTP Credentials here using script parameters
    var sftpHostKey = scriptObj.getParameter({ name: 'custscript_sftp_host_key' });
    var sftpUsername = scriptObj.getParameter({ name: 'custscript_sftp_username' });
    var sftpPassword = scriptObj.getParameter({ name: 'custscript_sftp_password' });
    var sftpPort = scriptObj.getParameter({ name: 'custscript_sftp_port' });
    var sftpServerUrl = scriptObj.getParameter({ name: 'custscript_sftp_server_url' });
    var sftpDirectory = scriptObj.getParameter({ name: 'custscript_sftp_directory' });

    // saved search
    var queuedSearchID = scriptObj.getParameter({ name: 'custscript_sftp_queued_files_search_id' });
    var archiveFolderID = scriptObj.getParameter({ name: 'custscript_archive_folder_id' });

    function fileTransmissionProper(fileId) {

        var successfulTransmission = true;

        try {

            log.audit("Start SFTP Server connection", "-------- Initialize SFTP Server Connection --------");

            // connect to sftp server using script parameters
            var sftpConnection = sftp.createConnection({
                username: sftpUsername,
                passwordGuid: sftpPassword,
                url: sftpServerUrl,
                port: Number(sftpPort),
                hostKey: sftpHostKey,
                hostKeyType: 'rsa'
            });

            log.audit("Connected successfully to SFTP Server", "------- Connected Successfully ------");
            
            log.audit("Start SFTP Transmission", "-------- start SFTP transmission --------");

            var fileToBeSentObj = file.load({
                id: fileId
            });

            // send file to server
            sftpConnection.upload({
                file: fileToBeSentObj,
                directory: sftpDirectory,
                replaceExisting: true
            });
            log.audit("Successful File Transmission", "-------- Successfully transmitted file. File ID: " + fileId + " --------");
            
        } catch (e) {
            log.error("SFTP Server Connection Error", e);
            successfulTransmission = false;
        }

        if(successfulTransmission) {
            log.audit("Archiving file..", "-------- Archiving file. File ID: " + fileId + " ------");

            fileToBeSentObj.folder = archiveFolderID;
            var fileId = fileToBeSentObj.save();
            
            log.audit("Successfully moved file..", "-------- File Archived. File ID: " + fileId + " ------");
        }
    }

    function execute(context) {

        if(queuedSearchID) {

            var queueSearch = search.load({
                id: queuedSearchID
            });

            var i = 0;

            queueSearch.run().each(function(result){

                var fileId = result.getValue({ name: 'internalid' });
                fileTransmissionProper(fileId);
                i++;

            });

            if(i == 0) {
                log.audit("No Files", "No files has been found in Queue Folder.");
            }

        } else {

            log.error('Parameter missing!', 'No File ID or Search Queue ID found');

        }

    }

    return {
        execute: execute
    }
});
