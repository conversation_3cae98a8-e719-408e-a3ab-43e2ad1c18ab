function afterSubmit(type)
{
    var project = nlapiGetNewRecord();

    var oldProject = nlapiGetOldRecord();

    if(!oldProject || project.getFieldValue('custentity96') != oldProject.getFieldValue('custentity96'))
    {
        updateProjectManagerList();
    }

}
function updateProjectManagerList() {

    var project = nlapiLoadRecord('job', nlapiGetRecordId());

    for(var r = 1; r < project.getLineItemCount('jobresources'); r++)
    {
        project.selectLineItem('jobresources', r);

        project.setCurrentLineItemValue('jobresources', 'role', ["-3"]);

        var pms = project.getFieldValues('custentity96');

        if(pms && pms.indexOf(project.getLineItemValue('jobresources', 'jobresource', r)) > -1)
        {
            project.setCurrentLineItemValue('jobresources', 'role', ["-2"]);
        }

        project.commitLineItem('jobresources');

    }

    nlapiSubmitRecord(project);

}