/**
 *@NApiVersion 2.x
 *@NScriptType WorkflowActionScript
 */
define(['N/email', './crypto/crypto-js.js', 'N/runtime', 'N/file', 'N/url', 'N/render', 'N/search'], function(email, crypto, runtime, file, url, render, search) {

    var APPROVER_EMAIL;
    var TRANSACTION_NO;
    var RECID;
    var APPROVE_ID;
    var REJECT_ID;
    
    /**
     * 
     * @param {string} pid - ID of the pass phrase file
     * 
     * loads the content the pass phrase file.
     * 
     */
    function getPassphrase(pid) {
        var pFile = file.load({
            id: pid
        })
        if (pFile.size < 10485760){
           return pFile.getContents();
        } else {
            log.error("Error", "Email was not sent because pFile exceeds 10MB");
        }
    }

    /**
     * 
     * @param {string} type - "approve" or "reject"
     * 
     * generates the URL for the approval suitelet
     * 
     */
    function generateURL(type) {
        
        var pid = runtime.getCurrentScript().getParameter('custscript_acs_pid');
        var PP = getPassphrase(pid);

        var ecnrApprovalType = crypto.AES.encrypt(type, PP).toString();
        var encrEmail = crypto.AES.encrypt(APPROVER_EMAIL, PP).toString();
        var encrRec = crypto.AES.encrypt(RECID.toString(), PP).toString();
        if(type == 'approve') {
            var encrAction = crypto.AES.encrypt(APPROVE_ID, PP).toString();
        }  else {
            var encrAction = crypto.AES.encrypt(REJECT_ID, PP).toString();
        }

        var appProcessSuitelet = url.resolveScript({
            scriptId: "customscript_acs_sl_vendor_bill_approval",
            deploymentId: 'customdeploy_acs_sl_vendor_bill_approval',
            params: {
                em: encodeURIComponent(encrEmail),
                re: encodeURIComponent(encrRec),
                pi: encodeURIComponent(pid),
                apt: encodeURIComponent(ecnrApprovalType),
                ac: encodeURIComponent(encrAction),
            },
            returnExternalUrl: true
        });

        log.debug("URL", appProcessSuitelet);

        return appProcessSuitelet;

    }

    /**
     * 
     * Self explanatory. Generates the email body.
     * 
     */
    function generateEmailBody() {

        var body = 'Hi! <br />';
        body += 'Vendor bill ' + TRANSACTION_NO + ' has been submitted for your approval <br /><br />';
        body += '<a href="'+generateURL('approve')+'">Please click here to approve</a><br />';
        body += '<a href="'+generateURL('reject')+'">Please click here to reject</a><br />';

        return body;
    }
    
    /**
     * 
     * @param {object} recordObj - object record gotten from scriptContext
     * 
     * Generates the attachment for the email.
     * 
     */
    function createAttachment(recordObj) {

        // retrieve the xml template file from file cabinet
        var xmlTemplateFile = file.load('SuiteScripts/acs_vendor_bill_template.xml');
        var renderer = render.create();

        // insert the template file content to the renderer
        renderer.templateContent = xmlTemplateFile.getContents();
        
        // inject the vendor bill transaction object to the renderer
        renderer.addRecord('record', recordObj);
        
        // inject the creator to the renderer
        renderer.addCustomDataSource({
            format: render.DataSource.OBJECT,
            alias: "JSON",
            data: { requestor: runtime.getCurrentScript().getParameter('custscript_created_by') }
        });

        // render as pdf and change the name to the transaction no
        var vbPDF = renderer.renderAsPdf();
        vbPDF.name = TRANSACTION_NO.toString() + ".pdf";
        
        return vbPDF;

    }

    /**
     *  
     * @param {string} recId - id of the record
     * 
     * Retrieves the file attachments of the record
     * 
     */
    function retrieveAttachments(recId){

        var attachments = [];
        var fileSearch = search.create({
            type: "transaction",
            filters:
            [
                ["mainline","is","T"], 
                "AND", 
                ["internalid","anyof",recId]
            ],
            columns:
            [
                search.createColumn({
                    name: "internalid",
                    join: "file",
                    label: "Internal ID"
                })
            ]
        });
        var pagedData = fileSearch.runPaged({ pageSize: 5 });
        try{
            for( var i=0; i < pagedData.pageRanges.length; i++ ) {
        
                // fetch the current page data
                var currentPage = pagedData.fetch(i);
        
                // and forEach() thru all results
                currentPage.data.forEach( function(result) {
                    var attachment = file.load({
                        id: result.getValue({ name: 'internalid', join: 'file' })
                    });

                    attachments.push(attachment);
                });
        
            }
        } catch (e) {
            return attachments;
        }

        return attachments;

    }

    function onAction(scriptContext) {
        var recObj = scriptContext.newRecord;
        RECID = recObj.id;

        // get the necessary data from the parameters set in the action (can be found on workflow states)
        APPROVER_EMAIL = runtime.getCurrentScript().getParameter('custscript_approver_email');
        TRANSACTION_NO = runtime.getCurrentScript().getParameter('custscript_vend_bill_tran_no');
        APPROVE_ID = runtime.getCurrentScript().getParameter('custscript_approve_action_id');
        REJECT_ID = runtime.getCurrentScript().getParameter('custscript_reject_action_id');

        attachments = retrieveAttachments(recObj.id);
        // generate the file for the email
        attachments.push(createAttachment(recObj)); 

        var emailBody = generateEmailBody();
        email.send({
            author: 1663,
            recipients: APPROVER_EMAIL,
            subject: 'Vendor Bill: ' + TRANSACTION_NO + ' Requires Your Approval',
            body: emailBody,
            attachments: attachments,
            relatedRecords: {
                transactionId: recObj.id
            }
        });
    }

    return {
        onAction: onAction
    }
});
