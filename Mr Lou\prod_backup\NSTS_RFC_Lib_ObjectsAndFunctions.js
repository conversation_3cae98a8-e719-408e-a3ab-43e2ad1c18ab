/**
 * Copyright (c) 1998-2017 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * NetSuite, Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with NetSuite.
 * 
 * SuiteSolutions Lib File
 * 
 * Version    Date            Author           	Remarks
 * 1.00       18 Jul 2016     Roxanne Audette   Initial version.
 * 1.2		  03 Nov 2016     <PERSON>	Modified the variable name if it is referring to a native module
 * 												Updated the isEmpty function, support for empty Arrays and empty Objects
 * 												Added a script-wide object constant to send as the endpoint
 * 												added more details into the jsdoc
 * 1.2		  14 Apr 2017     rilagan			Used some functions on RMA from case
 * 
 */


 define(		
    ['N/record','N/search','N/runtime','N/file'],
            
    function(NSRecord,NSSearch,runtime,file)
    {
        var NSTSUtil = {};
        
        /**
        * Get file path from suiteapp and suitebundle*
        */
        function getFileId(stFilePath){
            var arBundleId = runtime.getCurrentScript().bundleIds;
            if(isEmpty(arBundleId)){
                stFilePath = '/c.'+	runtime.accountId+'/suiteapp/com.suitesolutions.nstsrfc/src/library/'+stFilePath;
                return stFilePath;
            }else{
                 stFilePath = 'SuiteBundles/Bundle ' + arBundleId[0] + '/' + 'com.suitesolutions.nstsrfc/src/library/'+stFilePath;
    
                 var fileObj = file.load(stFilePath);
                 return fileObj.url;
            }
                
        }
        NSTSUtil.getFileId = getFileId;
        
        /**
        * Get Amount with commas
        */
        function getAmountWithCommas(stAmount) {
            var stRet = '';
            try{
                stRet =  stAmount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }catch(error){
                stRet = stAmount
            }
            return stRet;
        }
        NSTSUtil.getAmountWithCommas = getAmountWithCommas;
        
        /**
        * Get Client Script Internal ID
        */
        function getScriptInternalId(stScriptId) {
            var intInternalId = '';
            var objScriptSearch = NSSearch.create({
                type: 'script',
                columns: ['scriptfile'],
                filters: ['scriptid', 'is', stScriptId]
                }).run();
            var searchRange = objScriptSearch.getRange(0, 1);
            if(!isEmpty(searchRange)){
                intInternalId = searchRange[0].getValue('scriptfile');
            }
            
            return intInternalId;
        }
        NSTSUtil.getScriptInternalId = getScriptInternalId;
        
        /**
        * Get Preference record and fields
        */
        function getPreferenceRecord(){
            //Search Preference Record
            try{
                var objPrefSearch = NSSearch.create({
                    type : HC_RFC_RECORDS.RFC_PREF.ID,
                    columns : [HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_ENABLE_CREATE,
                               HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_SO_FORM,
                               HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_INV_FORM,
                               HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_RMA_FORM,
                               HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_SS,
                               HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_SET_CASE_STAT,
                               HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_MEMO,
                               HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_SEARCH_TRANS_TYP,
                               HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DAYS_DIFF,
                               HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DATE_CREATE_TRANS,
                               HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_MAX_ROW],                          
                    filters: [['isinactive', 'is', 'F' ]]
                    
                }); 
                var objResult = null;
                objPrefSearch.run().each(function(result){
                    objResult = result;
                    return true;
                });
                return objResult;
            }catch(error){
                log.error('err',error.toString());
                return null;
            }
        } 
        NSTSUtil.getPreferenceRecord = getPreferenceRecord;
        
        /**
        * Get Related Records
        */
        function getRelatedRecords(stCase){
            //Search Preference Record
            var objFilters = [];
    
            try{
                objFilters.push(NSSearch.createFilter({
                     name: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_CASE,
                     operator: NSSearch.Operator.ANYOF,
                     values: [stCase]
                }));
                var objRec = NSSearch.create({
                     type : HC_RFC_RELATED_RECORDS.ID,
                     columns : ['internalid'],                          
                     filters: objFilters
                     
                 }); 
                var objResult = null;
                objRec.run().each(function(result){
                    objResult = result;
                    return true;
                });
                return objResult;
            }catch(error){
                log.error('err',error.toString());
                return null;
            }
    
        } 
        NSTSUtil.getRelatedRecords = getRelatedRecords;
        
        /**
        * Get All Related Records
        */
        function getAllRelatedRecords(stCase){
            //Search Preference Record
            var objFilters = [];
            var arrResults = [];
            var HC_RFC_RELATED_RECORDS = {
                    ID     :  'customrecord_nsts_rfc_case_related_rec',
                    FIELDS : {
                        ID											: 'internalid',
                        CUSTRECORD_NSTS_RFC_CASE					: 'custrecord_nsts_rfc_case',			
                        CUSTRECORD_NSTS_RFC_RELATED_RECORDS			: 'custrecord_nsts_rfc_related_records',					
                        CUSTRECORD_NSTS_RFC_CREATED_TRANS			: 'custrecord_nsts_rfc_created_trans'
                    }
            };
            var arrColumns = [];
            arrColumns.push( NSSearch.createColumn({
                 name: 'recordtype',
                 join: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_CREATED_TRANS
                 })
             );
            arrColumns.push( NSSearch.createColumn({
                 name: 'internalid'
                 })
             );
            arrColumns.push( NSSearch.createColumn({
                 name: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_RELATED_RECORDS
                 })
             );
            arrColumns.push( NSSearch.createColumn({
                 name: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_CREATED_TRANS
                 })
             );
            arrColumns.push( NSSearch.createColumn({
                 name: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_CASE
                 })
             );
            try{
                objFilters.push(NSSearch.createFilter({
                     name: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_CASE,
                     operator: NSSearch.Operator.ANYOF,
                     values: [stCase]
                }));
                objFilters.push(NSSearch.createFilter({
                     name: 'mainline',
                     join: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_CREATED_TRANS,
                     operator: NSSearch.Operator.IS,
                     values: 'T'
                 }));
                objFilters.push(NSSearch.createFilter({
                     name: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_CREATED_TRANS,
                     operator: NSSearch.Operator.NONEOF,
                     values: '@NONE@'
                }));
                /**objFilters.push(NSSearch.createFilter({
                     name: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_RELATED_RECORDS,
                     operator: NSSearch.Operator.NONEOF,
                     values: '@NONE@'
                }));*/
                var objRec = NSSearch.create({
                     type : HC_RFC_RELATED_RECORDS.ID,
                     columns : arrColumns,                        
                     filters: objFilters
                     
                 }); 
                var objResult = null;
                objRec.run().each(function(result){
                    arrResults.push(result);
                    return true;
                });
                return arrResults;
            }catch(error){
                log.error('err',error.toString());
                return null;
            }
    
        } 
        NSTSUtil.getAllRelatedRecords = getAllRelatedRecords;
        
        /**
         * This will validate is a Array/String/Object is empty or not
         * @param {String} stValue - the value to validate
         * @returns {Boolean} - true if value is empty, null, undefined, empty array or empty object
         * @memberOf NSTSUtil
         */
    
        function isEmpty(value){
            if (value == null || value == 'null' || value == undefined || value == 'undefined' || value == '' || value == "" || value.length <= 0) { return true; }
            return false;
        }
        NSTSUtil.isEmpty = isEmpty;
        
        /**
         * this function will return the defaultValue if the object is Empty
         * @param object object of any type
         * @defaultValue the replacing value if the object is not set or empty
         * @memberOf NSTSUtil
         */
        function isEmptyReplaceWith(object,defaultValue){
            if(isEmpty(object)){
                return defaultValue;
            }
            return object;
        }
        NSTSUtil.isEmptyReplaceWith = isEmptyReplaceWith;
        
        /**
         * this will get all result more than 1000
         * @param option: save search Option 
         * @param option.isLimitedResult
         * @return {result[]}
         * @memberOf NSTSUtil
         */
        function searchGetAllResult(option){
            var result = [];
            if(option.isLimitedResult == true){
                var rs = NSSearch.create(option).run();
                result = rs.getRange(0,1000);
                
                return result;
            }
            
            var rp = NSSearch.create(option).runPaged();
            rp.pageRanges.forEach(function(pageRange){
                var myPage = rp.fetch({index: pageRange.index});
                result = result.concat(myPage.data);
            });
            
            return result;
        }
        NSTSUtil.searchGetAllResult = searchGetAllResult;
        /**
        * Replace all occurences of a string
        */
        function replaceAll(find, replace, str) {
          return str.replace(new RegExp(find, 'g'), replace);
        }
    
        NSTSUtil.replaceAll = replaceAll;
        /**
         * this will get all result more than 1000
         * @param option: save search Option 
         * @param option.isLimitedResult
         * @return {result[]}
         * @memberOf NSTSUtil
         */
        function searchGetAllResultSrchObj(searchObject,option)
        {
            
            if(isEmpty(option)){
                option = {};
            }
            
            var result = [];
            if(option.isLimitedResult == true){
                var rs = searchObject.run();
                result = rs.getRange(0,1000)
                
                return result;
            }
            
            var rp = searchObject.runPaged();
            rp.pageRanges.forEach(function(pageRange){
                var myPage = rp.fetch({index: pageRange.index});
                result = result.concat(myPage.data);
            });
            
            return result;
        }
        NSTSUtil.searchGetAllResultSrchObj = searchGetAllResultSrchObj;
        
        
        /**
         * this function will sort the search result by ASC
         * @param result array(search result) 
         * @param field string(name of the field to sort)
         * @memberOf NSTSUtil
         */
        function searchSortResult(result,field)
        {
            if(isEmpty(result) || isEmpty(field)){
                return [];
            }
            
            var arrResult = result.sort(function(a,b){
                var inta = a.getValue(field);
                var intb = b.getValue(field);
                return inta - intb;
            });
            
            return arrResult;
        }
        NSTSUtil.searchSortResult = searchSortResult;
        
        
        /**
         * @description load search with the ability to append additional or remove filters and columns.
         * @param option a regular search module for search.load
         * @param option.addFilters (array) this will hold the new appending filters
         * @param option.addColumns (array) this will hold the new appending Columns
         * @param option.removeFilters (array) remove the filter by colname or colname + operator or  colname + operator + filter value (not yet implemented)
         * @param option.removeColumns (array) remove the columns by colname (not yet implemented)
         * @param option.resultLimit (number)
         * @param option.resultStart (number)
         * @result Array of result
         * @memberOf NSTSUtil
         */
        function searchLoad(option){
            if(isEmpty(option)){
                return null;
            }
            
            var objSearch = NSSearch.load({
                id: option.id,
            });
            
            if(!isEmpty(option.filters)){
                objSearch.filters = option.filters;
            }
            if(!isEmpty(option.columns)){
                objSearch.columns = option.columns;
                           
            }
            if(!isEmpty(objSearch.columns)){
                for(var i = 0; i < objSearch.columns.length; i++){
    
                    
                    if(!isEmpty(objSearch.columns[i].formula)){
                        var stName = objSearch.columns[i].name;
                        var stSummary = objSearch.columns[i].summary;
                        var stFormula = objSearch.columns[i].formula;
                        var stJoin = objSearch.columns[i].join;
                        var stFunction = objSearch.columns[i]['function'];
                        var stSort = objSearch.columns[i].sort;
                        var stSortdir = objSearch.columns[i].sortdir;
                        var stLabel = objSearch.columns[i].label;
                        var stLabelId = isEmptyReplaceWith(stLabel, '');
                            stLabelId = stLabelId.replace(/\s/g,'');
                            stLabelId = stLabelId.toLowerCase();
    
                        objSearch.columns[i] = NSSearch.createColumn({
                            name: stName + "_" + stLabelId + (i+1),
                            summary: stSummary,
                            join: stJoin,
                            label: stLabel,
                            'function': stFunction,
                            formula: stFormula,
                            sort: stSort,
                            sortdir: stSortdir
                        });
                            
                    }
                }
            }
    
            
            if(!isEmpty(option.addFilters)){
                var arrFil = objSearch.filters;
                arrFil = isEmpty(arrFil)? []: arrFil;
                
                objSearch.filters = arrFil.concat(option.addFilters);
            }
            
            if(!isEmpty(option.addColumns)){
                var arrCol = objSearch.columns;
                arrCol = isEmpty(arrCol)? []: arrCol;
                
                objSearch.columns = arrCol.concat(option.addColumns);
            }
            
            var arrResult = [];
            var intStart  = 0;
            if(!isEmpty(option.resultStart))
                intStart = option.resultStart;
          
            if(!isEmpty(option.resultLimit)){
    
                option.resultLimit = parseInt(option.resultLimit);
                option.resultLimit = (option.resultLimit <= 0 )? 1000: option.resultLimit;
                arrResult = objSearch.run().getRange(intStart,option.resultLimit);
            }else{
                arrResult = searchGetAllResultSrchObj(objSearch, option);
            }
            
            
            return {
                search: objSearch,
                result: arrResult,
            }
        }
        NSTSUtil.searchLoad = searchLoad;
        
        
        /**
         * 
         * @param obj
         * @param defaultStrValue default string value if the string Data can not be parse
         * @returns
         * @memberOf NSTSUtil
         */
        function parseJsonToString(obj,defaultStrValue){
            var retVal = "{}";
            try{
                retVal = JSON.stringify(obj);
                if(retVal === "null"){
                    retVal = (isEmpty(defaultStrValue))? "{}" : defaultStrValue;
                }
            }catch(e){
                if(!isEmpty(defaultStrValue)){
                    return defaultStrValue;
                }
            }
            return retVal;
        }
        NSTSUtil.parseJsonToString = parseJsonToString;
        
        /**
         * 
         * @param stringData
         * @param defaultObjValue default object value if the string Data can not be parse
         * @returns
         * @memberOf NSTSUtil
         */
        function parseStringToJson(stringData,defaultObjValue){
            var retVal = [];
            try{
                retVal = JSON.parse(stringData);
                if(isEmpty(retVal)){
                    if( defaultObjValue != null || defaultObjValue != undefined){
                        retVal = defaultObjValue;
                    }else{
                        retVal = {};
                    }
                }
                
            }catch(e){
               if( defaultObjValue != null || defaultObjValue != undefined){
                    return defaultObjValue;
                }
            }
            return retVal;
        }
        NSTSUtil.parseStringToJson = parseStringToJson;
        
        /**
         * @description Converts value into float
         * @memberOf NSTSUtil
         */
        function forceParseFloat(stValue){
            var flValue = parseFloat(stValue);
            if (isNaN(flValue) || (stValue == Infinity)){ return 0.00; }
            return flValue;
        }
        NSTSUtil.forceParseFloat = forceParseFloat;
        
    
        /**
        * Get Feature of Account
        * @param null
        * @return object 
        */
        function getFeatures(){
            var objFeatures = {
                   bOneWorld        : runtime.isFeatureInEffect({feature: 'subsidiaries'}),
                   bMultiLocation   : runtime.isFeatureInEffect({feature: 'multilocinvt'}),
                   bMulticurrency   : runtime.isFeatureInEffect({feature: 'multicurrency'}),
                   bMultiprice  	: runtime.isFeatureInEffect({feature: 'multiprice'}),
                   bQuantitypricing : runtime.isFeatureInEffect({feature: 'quantitypricing'})
            }
            return objFeatures;
        }
        
        NSTSUtil.getFeatures = getFeatures;
    
        /**
        * Replace All
        * @param null
        * @return object 
        */
        function replaceAll(find, replace, str) {
             return str.replace(new RegExp(find, 'g'), replace);
        }
        NSTSUtil.replaceAll = replaceAll;
        /**
         * Return the NSTSUtil as End Point
         */
        return NSTSUtil;    
    });