/**
 * @NApiVersion 2.x
 * @NScriptType UserEventScript
 * @NModuleScope SameAccount
 */
 define(['N/search', 'N/ui/serverWidget'],

 function (search, serverWidget) {

     function checkTransactionType(transId) {

         var transactionType = '';
         search.create({
             type: search.Type.TRANSACTION,
             filters: [{
                 name: 'internalid',
                 operator: search.Operator.ANYOF,
                 values: [transId]
             },
             {
                 name: 'mainline',
                 operator: search.Operator.IS,
                 values: ['T']
             }],
             columns: ['type'],
         }).run().each(function (result) {
             transactionType = result.getText({ name: 'type' });
             return true;
         });

         // only proceed if invoice
         return (transactionType == 'Invoice');

     }

     function beforeLoad(scriptContext) {
         try {
             var entityId = scriptContext.request.parameters.entity;
             var transId = scriptContext.request.parameters.transaction;
             var formObj = scriptContext.form;
             var countCounter = 0;

             if(checkTransactionType(transId)){
                 if (entityId) {
                     var recipientEmialField = formObj.getSublist({
                         id: 'otherrecipientslist'
                     });

                     var objSearch = search.create({
                         type: search.Type.CONTACT,
                         filters: [{
                             name: 'internalid',
                             join: 'customer',
                             operator: search.Operator.ANYOF,
                             values: [entityId]
                         }],
                         columns: ['internalid', 'entityid', 'email', 'custentity_primary_recipient', 'custentity_cc', 'custentity_bcc'],
                     }).run().each(function (result) {
                         
                         var to = result.getValue({ name: 'custentity_primary_recipient' });
                         var cc = result.getValue({ name: 'custentity_cc' });
                         var bcc = result.getValue({ name: 'custentity_bcc'});

                         log.debug('values', {
                             to: to,
                             cc: cc,
                             bcc: bcc
                         });

                         if(to || cc || bcc) { 
                             recipientEmialField.setSublistValue({
                                 id: 'otherrecipient',
                                 line: countCounter,
                                 value: result.getValue('internalid')
                             });
                             recipientEmialField.setSublistValue({
                                 id: 'email',
                                 line: countCounter,
                                 value: result.getValue('email')
                             });
                             
                             var fieldId = '';

                             if(to) {
                                 fieldId = 'toRecipients';
                             } else if (cc) {
                                 fieldId = 'cc';
                             } else if (bcc) {
                                 fieldId = 'bcc';
                             }
                             
                             recipientEmialField.setSublistValue({
                                 id: fieldId,
                                 line: countCounter++,
                                 value: 'T'
                             });
                         }

                         return true;
                     });
                 }
             }
         } catch (e) {
             log.debug('Error occurred', e);
         }
     }

     return {
         beforeLoad: beforeLoad
     };

 });