/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/log', 'N/search', 'N/record', 'N/https', 'N/runtime'], function (log, search, record, https, runtime) {

    function onRequest(context) {

        log.debug('Body', context.request.body);

        var obj = JSON.parse(context.request.body);

        if (obj.event_type == 'sale.line_items.added') {
            saleLineItemAdded(context, obj);
        }
    }

    function saleLineItemAdded(context, obj) {

        var items = {};
        var currentIndex = obj.line_items.length - 1;

        if (obj.line_items[currentIndex].custom_fields.length) {
            for (var i = 0; i < obj.line_items[currentIndex].custom_fields.length; i++) {
                setCustomField(obj.line_items[currentIndex].id, obj.line_items[currentIndex].custom_fields[i], "line_item");
            }
        } else {
            var itemId = obj.line_items[currentIndex].product_id;

            var item = findProductId(itemId);

            if (item.length) {

                var requiredCustomFields = [];

                var isSerialItem = item[0].getValue({
                    name: 'isserialitem',
                    join: 'custrecord_in8_vend_ids_item'
                });
                var item = item[0].getValue({
                    name: 'custrecord_in8_vend_ids_item'
                });

                var location = getLocation(obj);
                log.debug('In8', 'Location: ' + location);

                if (isSerialItem) {
                    // Get Serial Numbers
                    var serialNumbers = getSerialNumbers(location, item);

                    log.debug('Serial Numbers', serialNumbers);

                    if (serialNumbers.length) {
                        var values = [];

                        if (serialNumbers.length == 1) {
                            values.push({
                                "value": '-',
                                "title": 'No Serial #'
                            });
                        }

                        for (var i = 0; i < serialNumbers.length; i++) {
                            values.push({
                                "value": serialNumbers[i].getValue('internalid'),
                                "title": serialNumbers[i].getValue('inventorynumber')
                            });
                        }

                        requiredCustomFields.push({
                            "name": 'serial_number',
                            "values": values
                        });
                    } else {
                        var values = [];
                        values.push({
                            "value": '-',
                            "title": 'No Serial # in NetSuite'
                        });
                        values.push({
                            "value": '--',
                            "title": 'No Serial #. Please do not put on order. Contact admin.'
                        });
                        requiredCustomFields.push({
                            "name": 'serial_number',
                            "values": values
                        });
                    }
                }

                if (requiredCustomFields.length) {
                    var entityId = obj.line_items[currentIndex].id;

                    if (!items.actions) {
                        items.actions = [];
                    }
                    items.actions.push({
                        "type": "require_custom_fields",
                        "title": "Choose options",
                        "message": "Enter the item custom fields",
                        "entity": "line_item",
                        "entity_id": entityId,
                        "required_custom_fields": []
                    });

                    items.actions[items.actions.length - 1].required_custom_fields = requiredCustomFields;
                }
            }
        }
        context.response.setHeader({
            name: 'Content-Type',
            value: 'application/json'
        });

        log.debug('Results', JSON.stringify(items));

        context.response.write(JSON.stringify(items));
    }

    function setCustomField(entityId, customField, entity) {
        var settingRec = record.load({
            type: 'customrecord_in8_vend_settings',
            id: 1
        });

        var obj = {
            "entity": entity,
            "entity_id": entityId,
            "values": [{
                "name": customField.name,
                "string_value": customField.string_value,
            }]
        }
        log.debug('Custom Field Obj', JSON.stringify(obj));

        var resBody = JSON.parse(https.post({
            url: settingRec.getValue({
                fieldId: 'custrecord_in8_vend_sett_store_url'
            }) + '/api/2.0/workflows/custom_fields/values',
            headers: {
                'Authorization': 'Bearer ' + settingRec.getValue({
                    fieldId: 'custrecord_in8_vend_sett_access_token'
                }),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(obj)
        }).body);

        log.debug('Custom Field Resp', resBody);
    }

    function getSerialNumbers(location, item) {

        log.debug({
            title: 'Filters',
            details: [
                ["item", "anyof", item], "AND",
                ["location", "is", location], "AND",
                ["quantityavailable", "greaterthan", "0"]
            ]
        })
        var inventorynumberSearchObj = search.create({
            type: "inventorynumber",
            filters: [
                ["item", "anyof", item], "AND",
                ["location", "is", location], "AND",
                ["quantityavailable", "greaterthan", "0"]
            ],
            columns: [
                search.createColumn({
                    name: "inventorynumber",
                    sort: search.Sort.ASC,
                    label: "Number"
                }),
                search.createColumn({
                    name: "internalid"
                }),
            ]
        });
        var itemResults = inventorynumberSearchObj.run().getRange({
            start: 0,
            end: 100
        });
        return itemResults;
    }

    function getLocation(obj) {

        var outletId = obj.sale.outlet_id;
        var registerId = obj.sale.register_id;

        var s = search.create({
            type: "customrecord_in8_vend_locations",
            filters: [
                ["custrecord_in8_vend_outletid", "is", outletId], "AND",
                ["custrecord_in8_vend_location_register", "is", registerId]
            ],
            columns: [
                search.createColumn({
                    name: "custrecord_in8_vend_location"
                }),
            ]
        });
        var res = s.run().getRange({
            start: 0,
            end: 1
        });
        if (res.length) {
            return res[0].getValue('custrecord_in8_vend_location');
        }
        return null;
    }

    function findProductId(vendId) {

        var filters = [search.createFilter({
            name: 'custrecord_in8_vend_ids_id',
            operator: 'is',
            values: vendId
        })];
        var columns = ['internalid', 'custrecord_in8_vend_ids_item',
            'custrecord_in8_vend_ids_item.isserialitem'
        ];

        var itemSearch = search.create({
            type: 'customrecord_in8_vend_ids',
            filters: filters,
            columns: columns
        });
        var itemResults = itemSearch.run().getRange({
            start: 0,
            end: 1
        });

        return itemResults;
    }

    return {
        onRequest: onRequest
    }
});