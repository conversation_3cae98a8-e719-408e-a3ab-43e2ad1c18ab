/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/log', 'N/search', 'N/record', 'N/https', 'N/runtime'], function (log, search, record, https, runtime) {

    const ITEM_FULFILL_ANOTHER_LOCATION = '9cf6fd13-636f-4787-8f1b-35b081b351ac';

    function onRequest(context) {

        log.debug('Body', context.request.body);

        var obj = JSON.parse(context.request.body);

        if (obj.event_type == 'sale.line_items.added') {
            saleLineItemAdded(context, obj);
        } else if (obj.event_type == 'sale.ready_for_payment') {
            readyForPayment(context, obj);
        }
    }

    function readyForPayment(context, obj) {

        var items = {};
        items.actions = [];

        for (var i = 0; i < obj.sale.line_items.length; i++) {
            if (obj.sale.line_items[i].product_id == ITEM_FULFILL_ANOTHER_LOCATION) {
                if (obj.sale.line_items[i].custom_fields.length) {
                    if (obj.sale.line_items[i].custom_fields[0].name == 'location') {
                        log.debug('In8', 'Set location on previous item. Location: ' + obj.sale.line_items[i].custom_fields[0].string_value);
                        // Set custom field on previous item
                        setCustomField(obj.sale.line_items[i - 1].id, obj.sale.line_items[i].custom_fields[0], "line_item");
                    }
                }
                items.actions.push({
                    type: "remove_line_item",
                    line_item_id: obj.sale.line_items[i].id
                });
            }
        }
        context.response.setHeader({
            name: 'Content-Type',
            value: 'application/json'
        });

        log.debug('Results', JSON.stringify(items));

        context.response.write(JSON.stringify(items));
    }

    function saleLineItemAdded(context, obj) {

        var items = {};
        var currentIndex = obj.line_items.length - 1;
        var itemId = obj.line_items[currentIndex].product_id;
        var isValid = true;

        var lastStep = '';

        for (var i = 0; i < obj.sale.custom_fields.length; i++) {
            if (obj.sale.custom_fields[i].name == 'serial-step') {
                lastStep = obj.sale.custom_fields[i].string_value;
            }
        }

        log.debug({ title: 'last Step', details: lastStep });

        if (!obj.line_items[currentIndex].custom_fields.length) {
            if (itemId != ITEM_FULFILL_ANOTHER_LOCATION) {
                // 1 - Request serial number for the first time
                items = requireSerialNumbers({ obj: obj,
                    itemId: itemId, isAnotherLocation: false, currentIndex: currentIndex,
                    entityId: obj.line_items[currentIndex].id
                });
            } else {
                // After selecting the item Fulfill from Another location, require the Location custom field
                items = fulfillAnotherLocation(obj, currentIndex);
            }
        } else {
            var o;

            if (obj.line_items[currentIndex].product_id == ITEM_FULFILL_ANOTHER_LOCATION) {
                // After selecting a new location in the "Fulfill from Another location", it requests again the serial number for the previous item
                o = obj.sale.line_items[obj.sale.line_items.length - 2];

                if (lastStep == 'location') {
                    // Find the previous product id
                    var itemId = obj.sale.line_items[obj.sale.line_items.length - 2].product_id;

                    items = requireSerialNumbers({ obj: obj,
                        itemId: itemId, isAnotherLocation: true, currentIndex: currentIndex,
                        otherLocation: obj.line_items[currentIndex].custom_fields[0].string_value,
                        entityId: obj.sale.line_items[obj.sale.line_items.length - 2].id,
                        isConfirmation: false
                    });
                    // Reset custom fields
                    setCustomField(o.id, { name: 'serial_number_id', string_value: '' }, "line_item");
                    setCustomField(o.id, { name: 'serial_number2_id', string_value: '' }, "line_item");
                }
            } else {
                o = obj.line_items[currentIndex];
            }
            for (var i = 0; i < o.custom_fields.length; i++) {

                var customField = o.custom_fields[i];

                if (customField.name == 'serial_number' || customField.name == 'serial_number2') {
                    var item = findProductId(itemId == ITEM_FULFILL_ANOTHER_LOCATION ? obj.sale.line_items[obj.sale.line_items.length - 2].product_id : itemId);

                    var itemInternalId;

                    if (item.length) {
                        itemInternalId = item[0].getValue({
                            name: 'custrecord_in8_vend_ids_item'
                        });
                    }

                    var location = itemId == ITEM_FULFILL_ANOTHER_LOCATION ? getCustomFieldsValue(obj, 'location') : getLocation(obj);

                    var serialNumberRes = getSerialNumber(customField.string_value, location, itemInternalId);

                    if (serialNumberRes.length) {
                        // Store the Serial Number Internal Id in the field
                        setCustomField(o.id, { name: customField.name + '_id', string_value: serialNumberRes[0].getValue('internalid') }, 'line_item');
                    } else {
                        // If one of the serial number is Invalid, request serial numbers again
                        isValid = false;
                    }
                }
            }

            log.debug('Is Valid', isValid);

            // If one of the serial number is not valid ask again
            if (!isValid) {
                items = requireSerialNumbers({ obj: obj,
                    itemId: itemId, isAnotherLocation: false, currentIndex: currentIndex,
                    message: 'Could Not Find Serial Numbers in System! Please enter and Try again. If the serial Numbers are not Found Please Contact Admins.',
                    entityId: obj.line_items[currentIndex].product_id == ITEM_FULFILL_ANOTHER_LOCATION ? obj.sale.line_items[obj.sale.line_items.length - 2].id : obj.line_items[currentIndex].id,
                    isValid: false
                });
            } else {
                // Show the confirmation popup
                if (lastStep != 'confirmation' && !items.actions) {
                    items = requireSerialNumbers({ obj: obj,
                        itemId: itemId, isAnotherLocation: false, currentIndex: currentIndex,
                        isConfirmation: true,
                        entityId: obj.line_items[currentIndex].product_id == ITEM_FULFILL_ANOTHER_LOCATION ? obj.sale.line_items[obj.sale.line_items.length - 2].id : obj.line_items[currentIndex].id,
                        isValid: true
                    });
                }
            }
        }
        context.response.setHeader({
            name: 'Content-Type',
            value: 'application/json'
        });

        log.debug('Results', JSON.stringify(items));

        context.response.write(JSON.stringify(items));
    }

    function setCustomField(entityId, customField, entity) {

        var settingRec = record.load({
            type: 'customrecord_in8_vend_settings',
            id: 1
        });

        var obj = {
            "entity": entity,
            "entity_id": entityId,
            "values": [{
                "name": customField.name,
                "string_value": customField.string_value,
            }]
        }
        log.debug('Custom Field Obj', JSON.stringify(obj));

        var resBody = JSON.parse(https.post({
            url: settingRec.getValue({
                fieldId: 'custrecord_in8_vend_sett_store_url'
            }) + '/api/2.0/workflows/custom_fields/values',
            headers: {
                'Authorization': 'Bearer ' + settingRec.getValue({
                    fieldId: 'custrecord_in8_vend_sett_access_token'
                }),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(obj)
        }).body);

        log.debug('Custom Field Resp', resBody);
    }

    function getCustomFieldsValues(entityId) {

        var settingRec = record.load({
            type: 'customrecord_in8_vend_settings',
            id: 1
        });
        var resBody = JSON.parse(https.get({
            url: settingRec.getValue({
                fieldId: 'custrecord_in8_vend_sett_store_url'
            }) + '/api/2.0/workflows/custom_fields/values?entity=line_item&entity_id=' + entityId,
            headers: {
                'Authorization': 'Bearer ' + settingRec.getValue({
                    fieldId: 'custrecord_in8_vend_sett_access_token'
                }),
                'Content-Type': 'application/json'
            }
        }).body);

        log.debug('Custom Field Resp', resBody);

        return resBody;
    }

    function getCustomFieldsValue(obj, customFieldName) {

        if (obj.data) {
            for (var i = 0; i < obj.data.length; i++) {
                if (obj.data[i].name == customFieldName) {
                    return obj.data[i].string_value;
                }
            }
        }
        if (obj.line_items && obj.line_items.length) {
            for (var i = 0; i < obj.line_items[0].custom_fields.length; i++) {
                if (obj.line_items[0].custom_fields[i].name == customFieldName) {
                    return obj.line_items[0].custom_fields[i].string_value;
                }
            }
        }
        return null;
    }

    function getSerialNumbers(location, item) {

        log.debug({
            title: 'Filters',
            details: [
                ["item", "anyof", item], "AND",
                ["location", "is", location], "AND",
                ["quantityavailable", "greaterthan", "0"]
            ]
        })
        var inventorynumberSearchObj = search.create({
            type: "inventorynumber",
            filters: [
                ["item", "anyof", item], "AND",
                ["location", "is", location], "AND",
                ["quantityavailable", "greaterthan", "0"]
            ],
            columns: [
                search.createColumn({
                    name: "inventorynumber",
                    sort: search.Sort.ASC,
                    label: "Number"
                }),
                search.createColumn({
                    name: "internalid"
                }),
            ]
        });
        var itemResults = inventorynumberSearchObj.run().getRange({
            start: 0,
            end: 100
        });
        return itemResults;
    }

    function getSerialNumber(serialNumber, location, item) {

        log.debug({
            title: 'Get serial number',
            details: [
                ["inventorynumber", "is", serialNumber], "AND",
                ["location", "is", location], "AND",
                ["item", "is", item], "AND",
                ["quantityavailable", "greaterthan", "0"]
            ]
        });
        var inventorynumberSearchObj = search.create({
            type: "inventorynumber",
            filters: [
                ["inventorynumber", "is", serialNumber], "AND",
                ["location", "is", location], "AND",
                ["item", "is", item], "AND",
                ["quantityavailable", "greaterthan", "0"]
            ],
            columns: [
                search.createColumn({
                    name: "internalid"
                }),
            ]
        });
        var itemResults = inventorynumberSearchObj.run().getRange({
            start: 0,
            end: 1
        });
        return itemResults;
    }

    function getSerialNumberById(serialNumberId) {

        var inventorynumberSearchObj = search.create({
            type: "inventorynumber",
            filters: [
                ["internalid", "is", serialNumberId]
            ],
            columns: [
                search.createColumn({
                    name: "internalid"
                }),
                search.createColumn({
                    name: "inventorynumber",
                    sort: search.Sort.ASC,
                    label: "Number"
                })
            ]
        });
        var itemResults = inventorynumberSearchObj.run().getRange({
            start: 0,
            end: 1
        });
        return itemResults;
    }

    function getLocation(obj) {

        var outletId = obj.sale.outlet_id;
        var registerId = obj.sale.register_id;

        var s = search.create({
            type: "customrecord_in8_vend_locations",
            filters: [
                ["custrecord_in8_vend_outletid", "is", outletId], "AND",
                ["custrecord_in8_vend_location_register", "is", registerId]
            ],
            columns: [
                search.createColumn({
                    name: "custrecord_in8_vend_location"
                }),
            ]
        });
        var res = s.run().getRange({
            start: 0,
            end: 1
        });
        if (res.length) {
            return res[0].getValue('custrecord_in8_vend_location');
        }
        return null;
    }

    function fulfillAnotherLocation(obj, currentIndex) {

        var requiredCustomFields = [];
        var items = {};

        var locations = getLocationsFulfill(obj);

        requiredCustomFields.push({
            "name": 'location',
            "values": locations
        });
        if (requiredCustomFields.length) {
            var entityId = obj.line_items[currentIndex].id;

            if (!items.actions) {
                items.actions = [];
            }
            items.actions.push({
                "type": "require_custom_fields",
                "title": "Choose options",
                "message": "Enter the item custom fields",
                "entity": "line_item",
                "entity_id": entityId,
                "required_custom_fields": []
            });

            items.actions[items.actions.length - 1].required_custom_fields = requiredCustomFields;

            items.actions.push({
                "type": "set_custom_field",
                "entity": "sale",
                "custom_field_name": "serial-step",
                "custom_field_value": 'location'
            });
        }
        return items;
    }

    function getLocationsFulfill(obj) {

        var outletId = obj.sale.outlet_id;

        var s = search.create({
            type: "customrecord_in8_vend_locations",
            filters: [
                ["custrecord_in8_vend_outletid", "is", outletId]
            ],
            columns: [
                search.createColumn({
                    name: "custrecord_in8_vend_location"
                }),
                search.createColumn({
                    name: "custrecord_in8_vend_location_outlet"
                }),
            ]
        });
        var res = s.run().getRange({
            start: 0,
            end: 1
        });

        if (res.length) {
            s = search.create({
                type: "customrecord_in8_vend_locations",
                filters: [
                    ["custrecord_in8_vend_location_outlet", "contains", res[0].getValue('custrecord_in8_vend_location_outlet').split(' ')[0]]
                ],
                columns: [
                    search.createColumn({
                        name: "custrecord_in8_vend_location"
                    }),
                    search.createColumn({
                        name: "custrecord_in8_vend_location_outlet"
                    }),
                ]
            });
            res = s.run().getRange({
                start: 0,
                end: 100
            });
        }

        var values = [];
        var added = [];

        for (var i = 0; i < res.length; i++) {
            if (added.indexOf(res[i].getValue('custrecord_in8_vend_location_outlet')) == -1) {
                values.push({
                    "value": res[i].getValue('custrecord_in8_vend_location'),
                    "title": res[i].getValue('custrecord_in8_vend_location_outlet')
                });
                added.push(res[i].getValue('custrecord_in8_vend_location_outlet'));
            }
        }
        return values;
    }

    function findProductId(vendId) {

        var filters = [search.createFilter({
            name: 'custrecord_in8_vend_ids_id',
            operator: 'is',
            values: vendId
        })];
        var columns = ['internalid', 'custrecord_in8_vend_ids_item',
            'custrecord_in8_vend_ids_item.isserialitem',
            'custrecord_in8_vend_ids_item.saleunit'
        ];

        var itemSearch = search.create({
            type: 'customrecord_in8_vend_ids',
            filters: filters,
            columns: columns
        });
        var itemResults = itemSearch.run().getRange({
            start: 0,
            end: 1
        });

        return itemResults;
    }

    function requireSerialNumbers(options) {

        var obj = options.obj,
            itemId = options.itemId,
            isAnotherLocation = options.isAnotherLocation,
            currentIndex = options.currentIndex,
            otherLocation = options.otherLocation,
            message = options.message,
            isConfirmation = options.isConfirmation,
            entityId = options.entityId,
            isValid = options.isValid;

        var items = {};
        var requiredCustomFields = [];

        log.debug('Item Id', itemId);
        log.debug('Options', options);

        if (itemId == ITEM_FULFILL_ANOTHER_LOCATION) {
            options.isAnotherLocation = true;
            isConfirmation = true;
            // Item from the previous line
            itemId = obj.sale.line_items[obj.sale.line_items.length - 2].product_id;
            otherLocation = getCustomFieldsValue(obj, 'location');
        }

        var item = findProductId(itemId);

        log.debug('item.length', item.length);

        if (item.length) {
            var isSerialItem = item[0].getValue({
                name: 'isserialitem',
                join: 'custrecord_in8_vend_ids_item'
            });
            var saleUnit = item[0].getText({
                name: 'saleunit',
                join: 'custrecord_in8_vend_ids_item'
            });
            var item = item[0].getValue({
                name: 'custrecord_in8_vend_ids_item'
            });

            var location = otherLocation ? otherLocation : getLocation(obj);

            log.debug('In8', 'Location: ' + location);

            if (isConfirmation && isValid) {
                requiredCustomFields = confirmSerialNumbers(options);
                message = 'Confirm the serial numbers.';
            } else if (isSerialItem) {
                var qtySerial = 1;

                if (saleUnit == 'Box2') {
                    qtySerial = 2;
                }
                // Get Serial Numbers
                var serialNumbers = getSerialNumbers(location, item);

                log.debug('Serial Numbers', serialNumbers);

                if (serialNumbers.length) {
                    for (var j = 0; j < qtySerial; j++) {
                        requiredCustomFields.push({
                            "name": 'serial_number' + (j == 0 ? '' : j + 1)
                        });
                    }
                } else {
                    var values = [];
                    values.push({
                        "value": '-',
                        "title": 'No Serial # in NetSuite'
                    });
                    values.push({
                        "value": '--',
                        "title": 'No Serial #. Please do not put on order. Contact admin.'
                    });
                    requiredCustomFields.push({
                        "name": 'serial_number',
                        "values": values
                    });
                }
            }
        }

        log.debug('requiredCustomFields', requiredCustomFields);

        if (requiredCustomFields.length) {
            // If Refresh, ask the required fields from the previous item again
            //var entityId = isRefresh ? obj.sale.line_items[obj.sale.line_items.length - 2].id : obj.line_items[currentIndex].id;
            if (!items.actions) {
                items.actions = [];
            }
            items.actions.push({
                "type": "require_custom_fields",
                "title": "Choose options",
                "message": message || "Enter the item custom fields",
                "entity": "line_item",
                "entity_id": entityId,
                "required_custom_fields": []
            });

            items.actions[items.actions.length - 1].required_custom_fields = requiredCustomFields;

            if (isConfirmation && isValid) {
                items.actions.push({
                    "type": "set_custom_field",
                    "entity": "sale",
                    "custom_field_name": "serial-step",
                    "custom_field_value": 'confirmation'
                });
            } else {
                items.actions.push({
                    "type": "set_custom_field",
                    "entity": "sale",
                    "custom_field_name": "serial-step",
                    "custom_field_value": ''
                });
            }
        }
        return items;
    }

    function confirmSerialNumbers(options) {

        var obj = options.obj,
            currentIndex = options.currentIndex;

        var requiredCustomFields = [];
        var qty = 0;

        var entityId = options.isAnotherLocation ? obj.sale.line_items[obj.sale.line_items.length - 2].id : obj.line_items[currentIndex].id;
        var o = options.isAnotherLocation ? obj.sale.line_items[obj.sale.line_items.length - 2] : obj.line_items[currentIndex];

        var customFieldValues = getCustomFieldsValues(entityId);

        for (var i = 0; i < o.custom_fields.length; i++) {

            var customField = o.custom_fields[i];

            if (customField.name == 'serial_number' || customField.name == 'serial_number2') {
                for (var j = 0; j < customFieldValues.data.length; j++) {

                    if (customFieldValues.data[j].name == customField.name + '_id' && customFieldValues.data[j].string_value) {

                        var serialNumberRes = getSerialNumberById(customFieldValues.data[j].string_value);

                        if (serialNumberRes.length) {
                            var values = [];

                            values.push({
                                "value": '-',
                                "title": 'Please confirm serial number by clicking the radio button below.'
                            });
                            values.push({
                                "value": serialNumberRes[0].getValue('internalid'),
                                "title": serialNumberRes[0].getValue('inventorynumber')
                            });
                            requiredCustomFields.push({
                                'name': 'serial_number' + (qty == 0 ? '' : qty + 1) + '_id',
                                'values': values
                            });
                            qty++;
                        }
                    }
                }
            }
        }

        log.debug('Confirm', JSON.stringify(requiredCustomFields));

        return requiredCustomFields;
    }

    return {
        onRequest: onRequest
    }
});