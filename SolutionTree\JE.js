/**
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
 define(['N/record', 'N/log', 'N/search'], function(record, log, search) {

    function execute(context) {
        var newSearch = search.load({
            id: 958
        });

        var resultSet = newSearch.run();
        //loop the result

        resultSet.each(callMe);
        //each result will be passed on the results
        function callMe(result) {
            //prints the results
            var idJE = result.getValue({
                name: 'internalid',
                summary: search.Summary.GROUP
            });
            //var memoJeLine= result.getValue({name:'memo'});

            log.debug({
                title: "Amortization JE ID",
                details: idJE
            });

            var recJE = record.load({
                type: record.Type.JOURNAL_ENTRY,
                id: idJE
            });

            var lineItemCount = recJE.getLineCount({
                sublistId: 'line'
            });
            log.debug({
                title: "Line Item Count",
                details: lineItemCount
            });

            for (var cnt = 0; cnt < lineItemCount; cnt++) {

                var jeLineAmortSched = recJE.getSublistValue({
                    sublistId: 'line',
                    fieldId: 'schedulenum',
                    line: cnt
                });

                log.debug({
                    title: 'From Amortization:',
                    details: 'TRUE'
                }); 

                //Load Amortization Schedule of Journal Entry
                var amortSchedRec = search.lookupFields({
                    type: search.Type.AMORTIZATION_SCHEDULE,
                    id: jeLineAmortSched,
                    columns: ['srctran']
                });

                log.debug({
                    title: 'JE Line Amortization Schedule ' + cnt,
                    details: jeLineAmortSched
                });

                //Indentify source Transaction
                var sourceTranText = amortSchedRec.srctran[0].text;
                var sourceTranId = amortSchedRec.srctran[0].value;

                log.debug({
                    title: 'Source Transaction ID line ' + cnt,
                    details: sourceTranId
                });
                log.debug({
                    title: 'Source Transaction Text line ' + cnt,
                    details: sourceTranText
                });

                //access Source Transaction if Vendor Bill
                if (sourceTranText.match('Bill #') != null) {
                    var billSearch = search.load({
                        id: 968
                    });

                    var billfilters = new Array();
                    billfilters[0] = search.createFilter({
                        name: 'internalidnumber',
                        operator: search.Operator.EQUALTO,
                        values: sourceTranId
                    });

                    var billexistingFilters = billSearch.filters;
                    billexistingFilters.push(billfilters[0]);

                    var billResultSet = billSearch.run();
                    //loop the result

                    billResultSet.each(callMeJeBC);
                    //each result will be passed on the results



                } //end of if Vendor Bill

                //access Source Transaction if Journal Entry
                if (sourceTranText.match('Journal') != null) {

                    var jeSearch = search.load({
                        id: 962
                    });

                    var filters = new Array();
                    filters[0] = search.createFilter({
                        name: 'internalidnumber',
                        operator: search.Operator.EQUALTO,
                        values: sourceTranId
                    });

                    var existingFilters = jeSearch.filters;
                    existingFilters.push(filters[0]);

                    var jeResultSet = jeSearch.run();
                    //loop the result

                    jeResultSet.each(callMeJeBC);
                    //each result will be passed on the results

                } //end of if Journal Entry

                if (sourceTranText.match('Bill Credit') != null) {
                    var billCreditSearch = search.load({
                        id: 969
                    });

                    var billCreditfilters = new Array();
                    billCreditfilters[0] = search.createFilter({
                        name: 'internalidnumber',
                        operator: search.Operator.EQUALTO,
                        values: sourceTranId
                    });

                    var billCreditexistingFilters = billCreditSearch.filters;
                    billCreditexistingFilters.push(billCreditfilters[0]);

                    var billCreditResultSet = billCreditSearch.run();
                    //loop the result

                    billCreditResultSet.each(callMeJeBC);
                    //each result will be passed on the results

                } //end of if Bill Credit

            } //end of for loop

            recJE.save();
            return true;

            function callMeJeBC(result) {
                //prints the results
                var amortSchedSource = result.getValue({
                    name: 'internalid',
                    join: "amortizationSchedule"
                });
                var memo = result.getValue({
                    name: 'memo'
                });

                log.debug({
                    title: 'Bill Credit/Bill/JE Line AmmortSched ',
                    details: amortSchedSource
                });
                log.debug({
                    title: 'Bill Credit/Bill/JE Line Memo ',
                    details: memo
                });
                if (amortSchedSource == jeLineAmortSched) {

                    recJE.setSublistValue({
                        sublistId: 'line',
                        fieldId: 'memo',
                        line: cnt,
                        value: memo
                    });

                    recJE.setValue({
                        fieldId: 'memo',
                        value: 'Memo Updated via Scheduled Script'
                    });
                } //end of if
                return true;
            } //end of callMe
        } //end of function Call me
    }
    return {
        execute: execute
    }
});