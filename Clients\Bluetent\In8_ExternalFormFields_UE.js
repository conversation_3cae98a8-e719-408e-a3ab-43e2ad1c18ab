function beforeLoad(type, form)
{
    if(type == "edit" || type == "create")
    {
        var record = nlapiGetNewRecord();

        if(record.getFieldValue('custrecord_in8_ceff_externalform'))
        {
            var customform = nlapiLoadRecord('customrecord_in8_customexternalform', record.getFieldValue('custrecord_in8_ceff_externalform'));

            var fieldName = form.addField("custpage_fieldname", 'select', "Select field");

            var options = getRecordFields(customform.getFieldValue('custrecord_in8_cef_recordtype'));

            fieldName.addSelectOption('', '');

            for(var i = 0; i < options.length; i++)
            {
                fieldName.addSelectOption(options[i].id, options[i].label);
            }

            fieldName.setDefaultValue(record.getFieldValue('custrecord_in8_ceff_fieldid'));
        }
    }
}

function getRecordFields(recordType)
{
    var baserecord = nlapiCreateRecord(recordType, {recordmode: 'dynamic'});

    if(baserecord.getFieldValue('customform'))
    {
        var options = baserecord.getField('customform').getSelectOptions();

        for(var i = 0; i < options.length; i++)
        {
            if(parseInt(options[i].getId()) < 0)
            {
                baserecord.setFieldValue('customform', options[i].getId());
                break;
            }
        }
    }

    var basefields = baserecord.getAllFields();

    var fields = [];

    for(var i = 0; i < basefields.length; i++)
    {
        var fieldObj = baserecord.getField(basefields[i]);

        if(fieldObj && fieldObj.getLabel())
        {
            var field = {};
            field.id = basefields[i];
            field.label = fieldObj.getLabel();

            fields.push(field);
        }

    }
    return fields;
}