/**
 * PR Invoice Suitelet
 * 
 * Version    Date            Author           Remarks
 * 1.00       25 Nov 2019     Marcel P          Initial Version
 *
 */

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
 function suitelet(request, response) {

    if (request.getMethod() == 'GET') {
        app.handleGet();
    } else {
        app.handlePost();
    }
}

var app = (function () {

    var FORM_NAME = 'PR Invoicing', // Form Name
        LIST_NAME = 'Customers List', // List Name
        SUBMIT_BUTTON = 'Submit', // Submit button caption
        MAX_RECORDS = 10, // Maximum number of records to display on the sublist
        MARKALL_ENABLED = true; // Mark all option enabled

    /**
     * Handles Suitelet GET Method
     * 
     * @returns {Void}
     */
    var handleGet = function (openSearch) {

        var form,
            subList,
            searchResults;

        try {
            form = nlapiCreateForm(FORM_NAME, false);

            form.setScript('customscript_in8_pr_invoice_cs');

            addFilters(form);

            addButtons(form);

            //form.addFieldGroup('custpage_invoices', 'Invoices');

            if (request.getParameter('search') == 'T' || openSearch) {

                form.addFieldGroup('custpage_details', 'Details');

                var fld = form.addField('custpage_invoice', 'text', 'Invoice', null, 'custpage_details');
                fld.setDisplayType('hidden');
                if (request.getParameter('invoice')) fld.setDefaultValue(request.getParameter('invoice'));

                fld = form.addField('custpage_num_groups', 'select', 'Number of groups', null, 'custpage_details');
                fld.addSelectOption('', '');
                for (var i = 1; i <= 50; i++) {
                    fld.addSelectOption(i, i);
                }

                if (request.getParameter('custpage_num_groups')) fld.setDefaultValue(request.getParameter('custpage_num_groups'));

                fld = form.addField('custpage_markup', 'select', 'Markup', null, 'custpage_details');

                var markupItems = nlapiSearchRecord('markupitem', null, [new nlobjSearchFilter('isinactive', null, 'is', 'F')], [new nlobjSearchColumn('internalid'), new nlobjSearchColumn('name')]) || [];
                fld.addSelectOption('', '');
                for (var i = 0; i < markupItems.length; i++) {
                    fld.addSelectOption(markupItems[i].getValue('internalid'), markupItems[i].getValue('name'));
                }

                fld = form.addField('custpage_department', 'select', 'Department', 'department', 'custpage_details');
                fld = form.addField('custpage_otherrefnum', 'text', 'PO #', null, 'custpage_details');
                fld = form.addField('custpage_memo', 'longtext', 'Memo', null, 'custpage_details');
                fld = form.addField('custpage_subtotal', 'text', 'Subtotal', null, 'custpage_details');

                form.addFieldGroup('custpage_invoices', 'Invoices');

                field = form.addField('file', 'inlinehtml', 'label', '', 'custpage_invoices');
                field.setLayoutType('outsidebelow');
                field.setDefaultValue('<font size="2pt"></font>');

                //Creates and inserts the sublist on the form
                subList = getInvoiceList(form);

                // Gets the search results to be displayed
                searchResults = getInvoices();

                // Populates the sublist based on the search results
                populateInvoices(subList, searchResults);

                var lineItemsList = getLineItemsList(form);

                var groupsList = getGroupsList(form);

                populateLineItems(form, lineItemsList, groupsList);

            } else {
                getUnbilled(form);
            }

            // Displays the page
            response.writePage(form);
        } catch (e) {
            displayMessage('An error occurred. ' + (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
        }
    };

    /**
     * Handles Suitelet POST method
     * 
     * @returns {Void} 
     */
    var handlePost = function () {

        try {
            var internalId = request.getParameter('custpage_invoice');
            var recmac = 'recmachcustrecord_in8_tran_group_tran';
            var record;

            if (internalId && Number(internalId) != 0) {
                record = nlapiLoadRecord('invoice', internalId);

                // Custom form to use the new template
                record.setFieldValue('customform', nlapiGetContext().getCompany() == '789815_SB1' ? 121 : 108);

                for (var i = record.getLineItemCount('item'); i >= 1; i--) {
                    record.removeLineItem('item', i);
                }
                for (var i = record.getLineItemCount(recmac); i >= 1; i--) {
                    record.removeLineItem(recmac, i);
                }
            } else {
                record = nlapiCreateRecord('invoice', {
                    recordmode: 'dynamic',
                    entity: request.getParameter('custpage_customer'),
                    customform: nlapiGetContext().getCompany() == '789815_SB1' ? 121 : 108
                });
                record.setFieldValue('entity', request.getParameter('custpage_customer'));
            }

            record.setFieldValue('itemcostdiscount', request.getParameter('custpage_markup'));
            record.setFieldValue('expcostdiscount', request.getParameter('custpage_markup'));

            record.setFieldValue('department', request.getParameter('custpage_department'));
            record.setFieldValue('otherrefnum', request.getParameter('custpage_otherrefnum'));
            record.setFieldValue('memo', request.getParameter('custpage_memo'));

            for (var i = 1; i <= request.getLineItemCount('custpage_sublist'); i++) {
                if (request.getLineItemValue('custpage_sublist', 'custpage_billable', i) != 'T' &&
                    request.getLineItemValue('custpage_sublist', 'custpage_expense', i) != 'T') {
                    record.selectNewLineItem('item');
                    record.setCurrentLineItemValue('item', 'item', request.getLineItemValue('custpage_sublist', 'custpage_item', i));
                    record.setCurrentLineItemValue('item', 'quantity', request.getLineItemValue('custpage_sublist', 'custpage_quantity', i));
                    record.setCurrentLineItemValue('item', 'rate', request.getLineItemValue('custpage_sublist', 'custpage_rate', i));
                    record.setCurrentLineItemValue('item', 'amount', request.getLineItemValue('custpage_sublist', 'custpage_amount', i));
                    record.setCurrentLineItemValue('item', 'description', request.getLineItemValue('custpage_sublist', 'custpage_line_memo', i));
                    record.setCurrentLineItemValue('item', 'custcol_in8_group', request.getLineItemValue('custpage_sublist', 'custpage_group', i));
                    record.commitLineItem('item');
                } else {
                    // Apply
                    for (var j = 1; j <= record.getLineItemCount('itemcost'); j++) {
                        if (record.getLineItemValue('itemcost', 'doc', j) == request.getLineItemValue('custpage_sublist', 'custpage_doc', i) &&
                            request.getLineItemValue('custpage_sublist', 'custpage_billable', i) == 'T' &&
                            record.getLineItemValue('itemcost', 'line', j) == request.getLineItemValue('custpage_sublist', 'custpage_line', i)) {
                            nlapiLogExecution('DEBUG', 'in8', 'apply line Cost ' + j + ' - ' + request.getLineItemValue('custpage_sublist', 'custpage_apply', i));

                            record.selectLineItem('itemcost', j);
                            record.setCurrentLineItemValue('itemcost', 'apply', request.getLineItemValue('custpage_sublist', 'custpage_apply', i) == 'T' ? 'T' : 'F');

                            if (request.getLineItemValue('custpage_sublist', 'custpage_apply', i) == 'T') {
                                record.setCurrentLineItemValue('itemcost', 'quantity', request.getLineItemValue('custpage_sublist', 'custpage_quantity', i));
                                record.setCurrentLineItemValue('itemcost', 'cost', request.getLineItemValue('custpage_sublist', 'custpage_rate', i));
                                record.setCurrentLineItemValue('itemcost', 'amount', request.getLineItemValue('custpage_sublist', 'custpage_amount', i));
                                record.setCurrentLineItemValue('itemcost', 'memo', request.getLineItemValue('custpage_sublist', 'custpage_line_memo', i));                                
                                record.setCurrentLineItemValue('itemcost', 'custcol_in8_group', request.getLineItemValue('custpage_sublist', 'custpage_group', i));
                            }
                            record.commitLineItem('itemcost');
                            break;
                        }
                    }

                    for (var j = 1; j <= record.getLineItemCount('expcost'); j++) {
                        //nlapiLogExecution('DEBUG', 'exp ' + i, record.getLineItemValue('expcost', 'doc', j)  );

                        var a = record.getLineItemValue('expcost', 'doc', j) + ' - ' + request.getLineItemValue('custpage_sublist', 'custpage_doc', i);
                        var b = request.getLineItemValue('custpage_sublist', 'custpage_expense', i);
                        var c = record.getLineItemValue('expcost', 'category', j) + ' - ' + request.getLineItemValue('custpage_sublist', 'custpage_category', i);

                        if (record.getLineItemValue('expcost', 'doc', j) == request.getLineItemValue('custpage_sublist', 'custpage_doc', i) &&
                            request.getLineItemValue('custpage_sublist', 'custpage_expense', i) == 'T' &&
                            record.getLineItemValue('expcost', 'line', j) == request.getLineItemValue('custpage_sublist', 'custpage_line', i)
                            //record.getLineItemValue('expcost', 'category', j) == request.getLineItemValue('custpage_sublist', 'custpage_category', i) &&
                            //record.getLineItemValue('expcost', 'originalamount', j) == request.getLineItemValue('custpage_sublist', 'custpage_originalamount', i)
                        ) {
                            nlapiLogExecution('DEBUG', 'in8', 'apply line Exp ' + j + ' - ' + request.getLineItemValue('custpage_sublist', 'custpage_apply', i));

                            record.selectLineItem('expcost', j);
                            record.setCurrentLineItemValue('expcost', 'apply', request.getLineItemValue('custpage_sublist', 'custpage_apply', i) == 'T' ? 'T' : 'F');

                            if (request.getLineItemValue('custpage_sublist', 'custpage_apply', i) == 'T') {
                                record.setCurrentLineItemValue('expcost', 'quantity', request.getLineItemValue('custpage_sublist', 'custpage_quantity', i));
                                record.setCurrentLineItemValue('expcost', 'memo', request.getLineItemValue('custpage_sublist', 'custpage_line_memo', i));
                                record.setCurrentLineItemValue('expcost', 'amount', request.getLineItemValue('custpage_sublist', 'custpage_amount', i));
                                record.setCurrentLineItemValue('expcost', 'custcol_in8_group', request.getLineItemValue('custpage_sublist', 'custpage_group', i));
                            }
                            record.commitLineItem('expcost');
                            break;
                        }
                    }
                }
            }
            // Add selected groups
            for (var i = 1; i <= request.getLineItemCount('custpage_sublist_group'); i++) {
                record.selectNewLineItem(recmac);
                record.setCurrentLineItemValue(recmac, 'custrecord_in8_tran_group', request.getLineItemValue('custpage_sublist_group', 'custpage_group', i));
                record.setCurrentLineItemValue(recmac, 'custrecord_in8_tran_group_description', request.getLineItemValue('custpage_sublist_group', 'custpage_group_description', i));
                record.commitLineItem(recmac);
            }

            nlapiSubmitRecord(record, true, true);

            // Reloads window
            handleGet(true);
        } catch (e) {
            displayMessage('An error occurred. ' + (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
        }
    };

    function getItemsGroups(internalId) {

        var s = nlapiSearchRecord("transaction", null,
            [
                ["internalidnumber", "equalto", internalId],
                "AND",
                ["mainline", "is", "F"]
            ],
            [
                new nlobjSearchColumn("item"),
                new nlobjSearchColumn("custcol_in8_group"),
                new nlobjSearchColumn("appliedtotransaction"),
                new nlobjSearchColumn("lineuniquekey"),                
            ]
        ) || [];

        var itemsGroup = [];

        for (var i = 0; i < s.length; i++) {
            itemsGroup[s[i].getValue('lineuniquekey')] = s[i].getValue('custcol_in8_group');
        }
        return itemsGroup;
    }

    /**
     * Add Buttons
     * 
     * @param {nlobjForm} form Object containing the form

     * @returns {Void} 
     */
    var addButtons = function (form) {

        if (request.getParameter('search') == 'T') {
            form.addSubmitButton(SUBMIT_BUTTON);
        }

        form.addButton('custombutton', 'Search', 'search()');

        if (request.getParameter('invoice')) {
            form.addButton('custom_markall', 'Mark All', 'markAll()');
            form.addButton('custom_unmarkall', 'Unmark All', 'unMarkAll()');
        }
        if (request.getParameter('search') == 'T') {
            form.addButton('custom_back', 'Back', 'back()');
        }
    };

    /**
     * Add Filter fields to the Form
     * 
     * @param {nlobjForm} form Object containing the form

     * @returns {Void} 
     */
    var addFilters = function (form) {

        form.addFieldGroup('custpage_filter', 'Filter');

        var customer = form.addField('custpage_customer', 'select', 'Customer', 'customer', 'custpage_filter');

        if (request.getParameter('custpage_customer')) customer.setDefaultValue(request.getParameter('custpage_customer'));

        var fld = form.addField('custpage_inv_status', 'select', 'Invoice Status', null, 'custpage_filter');

        fld.addSelectOption('CustInvc:A', 'Open');
        fld.addSelectOption('CustInvc:B', 'Paid');        

        if (request.getParameter('custpage_inv_status')) fld.setDefaultValue(request.getParameter('custpage_inv_status'));

        // fld = form.addField('custpage_sort', 'select', 'Sort By', null, 'custpage_filter');

        // fld.addSelectOption('', '');
        // fld.addSelectOption('custpage_item', 'Item');
        // fld.addSelectOption('custpage_date', 'Date');
        // fld.addSelectOption('custpage_cardholder', 'Cardholder');
        // fld.addSelectOption('custpage_category', 'Category');
        // fld.addSelectOption('custpage_trans_memo', 'Trans Memo');
        // fld.addSelectOption('custpage_line_memo', 'Line Memo');
        // fld.addSelectOption('custpage_amount', 'Amount');
        // fld.addSelectOption('custpage_group', 'Group');

        // if (request.getParameter('custpage_sort')) fld.setDefaultValue(request.getParameter('custpage_sort'));
    };

    var getInvoiceList = function (form) {

        var subList = form.addSubList('custpage_sublist_inv', 'list', 'Invoices', 'custpage_invoices');

        subList.addField('custpage_selected', 'checkbox', 'Select');

        var fld = subList.addField('custpage_internalid', 'text', 'InternalId');
        fld.setDisplayType('hidden');
        subList.addField('custpage_name', 'text', 'Invoice');
        subList.addField('custpage_date', 'text', 'Date');
        subList.addField('custpage_amount', 'currency', 'Amount');
        subList.addField('custpage_open', 'text', 'Open');
        return subList;
    }

    /**
     * Add a sublist to the Form
     * 
     * @param {nlobjForm} form Object containing the form

     * @returns {Void} 
     */
    var getLineItemsList = function (form) {

        var subList = form.addSubList('custpage_sublist', 'inlineeditor', 'Line Items', 'custpage_invoices');
        var fld;

        subList.addField('custpage_apply', 'checkbox', 'Apply');
        fld = subList.addField('custpage_billable', 'checkbox', 'Billable Item');
        fld.setDisplayType('hidden');
        fld = subList.addField('custpage_expense', 'checkbox', 'Expense Item');
        fld.setDisplayType('hidden');
        subList.addField('custpage_item', 'select', 'Item', 'item');
        subList.addField('custpage_date', 'text', 'Date');
        subList.addField('custpage_cardholder', 'text', 'Cardholder');
        fld = subList.addField('custpage_category', 'text', 'Category Id');
        fld.setDisplayType('hidden');
        fld = subList.addField('custpage_line', 'text', 'Line');
        fld.setDisplayType('hidden');
        subList.addField('custpage_category_disp', 'text', 'Category');
        subList.addField('custpage_trans_memo', 'textarea', 'Trans Memo');
        subList.addField('custpage_line_memo', 'textarea', 'Line Memo');
        subList.addField('custpage_quantity', 'text', 'Quantity');
        subList.addField('custpage_rate', 'currency', 'Rate');
        fld = subList.addField('custpage_originalamount', 'currency', 'Original Amount');        
        fld.setDisplayType('hidden');
        subList.addField('custpage_amount', 'currency', 'Amount');
        subList.addField('custpage_group', 'select', 'Group');

        fld = subList.addField('custpage_group_h', 'text', 'Group');
        fld.setDisplayType('hidden');

        fld = subList.addField('custpage_doc', 'text', 'Doc');
        fld.setDisplayType('hidden');

        //subList.addMarkAllButtons();

        return subList;
    };

    var getGroupsList = function (form) {
        var subList = form.addSubList('custpage_sublist_group', 'inlineeditor', 'Groups', 'custpage_invoices');
        subList.addField('custpage_group', 'select', 'Group', 'customlist_in8_group_list');
        subList.addField('custpage_group_description', 'textarea', 'Description');
        return subList;
    }

    /**
     * Populate the SubList
     * 
     * @param {nlobjSublist} list Object sublist
     * @param {nlobjSearchResults} searchResults Object search results
     * 
     * @returns {Void} 
     */
    var populateInvoices = function (list, searchResults) {

        var searchLength = (searchResults ? searchResults.length : 0),
            i = 0;

        // Checks if needs to display only a number of records
        if (MAX_RECORDS) searchLength = searchLength > MAX_RECORDS ? MAX_RECORDS : searchLength;

        var curLine = 1;

        for (i = 0; i < searchLength; i++) {
            if (searchResults[i].getValue('tranid') != 'Memorized') {
                if (request.getParameter('invoice') && request.getParameter('invoice') == searchResults[i].getValue('internalid')) {
                    list.setLineItemValue('custpage_selected', curLine, 'T');
                }
                list.setLineItemValue('custpage_internalid', curLine, searchResults[i].getValue('internalid'));
                list.setLineItemValue('custpage_name', curLine, searchResults[i].getValue('tranid'));
                list.setLineItemValue('custpage_date', curLine, searchResults[i].getValue('trandate'));
                list.setLineItemValue('custpage_rate', curLine, searchResults[i].getValue('rate'));
                list.setLineItemValue('custpage_amount', curLine, searchResults[i].getValue('amount'));
                list.setLineItemValue('custpage_open', curLine, '<a href="/app/accounting/transactions/custinvc.nl?id=' + searchResults[i].getValue('internalid') + '" target="_blank">View</a>');
                curLine++;
            }
        }
        list.setLineItemValue('custpage_internalid', curLine, '');
        list.setLineItemValue('custpage_name', curLine, '-- NEW INVOICE --');

        if (request.getParameter('invoice') == '0') {
            list.setLineItemValue('custpage_selected', curLine, 'T');
        }
    };

    var populateLineItems = function (form, list, groupsList) {

        if (request.getParameter('invoice') != null) {
            var itemsGroups = [];
            var record;

            if (request.getParameter('invoice') == '0') {
                record = nlapiCreateRecord('invoice', {
                    recordmode: 'dynamic',
                    entity: request.getParameter('custpage_customer')
                });
            } else {
                record = nlapiLoadRecord('invoice', request.getParameter('invoice'));
                itemsGroups = getItemsGroups(request.getParameter('invoice'));
            }

            for (var i = 1; i <= record.getLineItemCount('recmachcustrecord_in8_tran_group_tran'); i++) {
                groupsList.setLineItemValue('custpage_group', i, record.getLineItemValue('recmachcustrecord_in8_tran_group_tran', 'custrecord_in8_tran_group', i));
                groupsList.setLineItemValue('custpage_group_description', i, record.getLineItemValue('recmachcustrecord_in8_tran_group_tran', 'custrecord_in8_tran_group_description', i));
            }
            var fld = form.getField('custpage_num_groups');
            fld.setDefaultValue(String(record.getLineItemCount('recmachcustrecord_in8_tran_group_tran')));

            var markup = record.getFieldValue('itemcostdiscount') || record.getFieldValue('expcostdiscount');

            if (markup) {
                fld = form.getField('custpage_markup');
                fld.setDefaultValue(markup);
            }

            fld = form.getField('custpage_department');
            fld.setDefaultValue(record.getFieldValue('department'));

            fld = form.getField('custpage_otherrefnum');
            fld.setDefaultValue(record.getFieldValue('otherrefnum'));

            fld = form.getField('custpage_memo');
            fld.setDefaultValue(record.getFieldValue('memo'));

            // Build an array with the items to be added
            var items = [];

            for (var i = 1; i <= record.getLineItemCount('item'); i++) {
                var item = {};
                item.custpage_item = record.getLineItemValue('item', 'item', i);
                item.custpage_item_h = record.getLineItemText('item', 'item', i) ? record.getLineItemText('item', 'item', i) : 'z';
                item.custpage_quantity = record.getLineItemValue('item', 'quantity', i);
                item.custpage_rate = parseFloat(record.getLineItemValue('item', 'rate', i));
                item.custpage_amount = parseFloat(record.getLineItemValue('item', 'amount', i));
                item.custpage_line_memo = record.getLineItemValue('item', 'description', i);
                item.custpage_group = record.getLineItemValue('item', 'custcol_in8_group', i);
                item.custpage_group_h = record.getLineItemValue('item', 'custcol_in8_group', i);
                item.custpage_billable = '';
                item.custpage_apply = '';
                item.custpage_date = '';
                item.custpage_cardholder = '';
                item.custpage_trans_memo = '';
                item.custpage_doc = '';
                item.custpage_line = '';
                item.custpage_expense = '';
                item.custpage_category = '';
                item.custpage_category_disp = '';
                item.custpage_originalamount = '';
                item.custpage_date_h = '';
                items.push(item);
            }

            for (var i = 1; i <= record.getLineItemCount('itemcost'); i++) {
                var item = {};
                var tranData = nlapiLookupField('transaction', record.getLineItemValue('itemcost', 'doc', i), ['memo', 'trandate', 'custbodycustbody_cardholder', 'recordtype']);

                var quantity = getTransactionQuantity(tranData.recordtype, record.getLineItemValue('itemcost', 'doc', i));

                item.custpage_billable = 'T';
                item.custpage_apply = record.getLineItemValue('itemcost', 'apply', i);
                item.custpage_item = record.getLineItemValue('itemcost', 'item', i);
                item.custpage_item_h = record.getLineItemText('itemcost', 'item', i) ? record.getLineItemText('itemcost', 'item', i) : 'z';
                item.custpage_date = tranData ? tranData.trandate : '';
                item.custpage_date_h = tranData && tranData.trandate ? nlapiStringToDate(tranData.trandate) : '';
                item.custpage_cardholder = tranData ? tranData.custbodycustbody_cardholder : '';
                item.custpage_trans_memo = tranData ? tranData.memo : '';
                item.custpage_rate = parseFloat(record.getLineItemValue('itemcost', 'cost', i));
                item.custpage_amount = parseFloat(record.getLineItemValue('itemcost', 'amount', i));
                item.custpage_group = itemsGroups[record.getLineItemValue('itemcost', 'lineuniquekey', i)];
                item.custpage_group_h = itemsGroups[record.getLineItemValue('itemcost', 'lineuniquekey', i)];
                item.custpage_doc = record.getLineItemValue('itemcost', 'doc', i);
                item.custpage_line = record.getLineItemValue('itemcost', 'line', i);
                item.custpage_line_memo = record.getLineItemValue('itemcost', 'memo', i);
                item.custpage_quantity = quantity;
                item.custpage_expense = '';
                item.custpage_category = '';
                item.custpage_category_disp = '';
                item.custpage_originalamount = '';
                items.push(item);
            }
            for (var i = 1; i <= record.getLineItemCount('expcost'); i++) {
                var item = {};
                var tranData = nlapiLookupField('transaction', record.getLineItemValue('expcost', 'doc', i), ['memo', 'trandate', 'custbodycustbody_cardholder', 'recordtype']);

                var quantity = getTransactionQuantity(tranData.recordtype, record.getLineItemValue('expcost', 'doc', i));

                item.custpage_expense = 'T';
                item.custpage_apply = record.getLineItemValue('expcost', 'apply', i);
                item.custpage_category = record.getLineItemValue('expcost', 'category', i);
                item.custpage_category_disp = record.getLineItemValue('expcost', 'categorydisp', i);
                item.custpage_date = tranData ? tranData.trandate : '';
                item.custpage_date_h = tranData && tranData.trandate ? nlapiStringToDate(tranData.trandate) : '';
                item.custpage_cardholder = tranData ? tranData.custbodycustbody_cardholder : '';
                item.custpage_trans_memo = tranData ? tranData.memo : '';
                item.custpage_line_memo = record.getLineItemValue('expcost', 'memo', i);
                item.custpage_rate = '';
                item.custpage_amount = parseFloat(record.getLineItemValue('expcost', 'amount', i) ? record.getLineItemValue('expcost', 'amount', i) : record.getLineItemValue('expcost', 'originalamount', i));
                item.custpage_originalamount = record.getLineItemValue('expcost', 'originalamount', i);
                item.custpage_group = itemsGroups[record.getLineItemValue('expcost', 'lineuniquekey', i)];
                item.custpage_group_h = itemsGroups[record.getLineItemValue('expcost', 'lineuniquekey', i)];
                item.custpage_doc = record.getLineItemValue('expcost', 'doc', i);
                item.custpage_line = record.getLineItemValue('expcost', 'line', i);
                item.custpage_quantity = quantity;
                item.custpage_item = '';
                item.custpage_item_h = 'z';
                item.custpage_billable = '';
                items.push(item);
            }

            // if (request.getParameter('custpage_sort')) {
            //     // Sort
            //     items.sort(dynamicSort(request.getParameter('custpage_sort')));
            // }
            items.sort(dynamicSort('custpage_item_h', 'custpage_date_h', 'custpage_trans_memo'));

            // Add items to the sublist
            for (var i = 0; i < items.length; i++) {
                var line = i + 1;
                list.setLineItemValue('custpage_billable', line, items[i].custpage_billable);
                list.setLineItemValue('custpage_item', line, items[i].custpage_item);
                list.setLineItemValue('custpage_quantity', line, items[i].custpage_quantity);
                list.setLineItemValue('custpage_expense', line, items[i].custpage_expense);
                list.setLineItemValue('custpage_apply', line, items[i].custpage_apply);
                list.setLineItemValue('custpage_category', line, items[i].custpage_category);
                list.setLineItemValue('custpage_category_disp', line, items[i].custpage_category_disp);
                list.setLineItemValue('custpage_date', line, items[i].custpage_date);
                list.setLineItemValue('custpage_cardholder', line, items[i].custpage_cardholder);
                list.setLineItemValue('custpage_trans_memo', line, items[i].custpage_trans_memo);
                list.setLineItemValue('custpage_line_memo', line, items[i].custpage_line_memo);
                list.setLineItemValue('custpage_rate', line, items[i].custpage_rate);
                list.setLineItemValue('custpage_amount', line, items[i].custpage_amount);
                list.setLineItemValue('custpage_originalamount', line, items[i].custpage_originalamount);
                list.setLineItemValue('custpage_group', line, items[i].custpage_group);
                list.setLineItemValue('custpage_group_h', line, items[i].custpage_group_h);
                list.setLineItemValue('custpage_doc', line, items[i].custpage_doc);
                list.setLineItemValue('custpage_line', line, items[i].custpage_line);
            }
        }
    }

    function getTransactionQuantity(recordType, tranId) {

        var quantity = '';

        try {
            // Try to get the quantity for the transaction. Not returned in the line item value
            var s = nlapiSearchRecord(recordType, null, [
                new nlobjSearchFilter('internalid', null, 'anyof', tranId),
                new nlobjSearchFilter('mainline', null, 'is', 'F')
            ], [
                new nlobjSearchColumn('internalid'),
                new nlobjSearchColumn('quantity')
            ]) || [];

            if (s.length) {
                quantity = s[0].getValue('quantity');
            }
        } catch(e) {
        }
        return quantity;
    }

    /**
     * Get the Searh Results
     * 
     * @returns {Void} 
     */
    var getInvoices = function () {

        var filters = [],
            columns = [],
            i = 0;

        if (request.getParameter('custpage_inv_status')) {
            filters[i++] = nlobjSearchFilter('status', null, 'anyof', request.getParameter('custpage_inv_status'));
        } else {
            filters[i++] = nlobjSearchFilter('status', null, 'anyof', 'CustInvc:A');
        }
        //filters[i++] = nlobjSearchFilter('tranid', null, 'isnot', 'Memorized');
        filters[i++] = nlobjSearchFilter('mainline', null, 'is', 'T');
        filters[i++] = nlobjSearchFilter('entity', null, 'anyof', request.getParameter('custpage_customer'));

        i = 0;

        columns[i++] = new nlobjSearchColumn('internalid');
        columns[i++] = new nlobjSearchColumn('tranid');
        columns[i++] = new nlobjSearchColumn('amount');
        var column = new nlobjSearchColumn('trandate');
        column.setSort(true);
        columns[i++] = column;

        return nlapiSearchRecord('invoice', null, filters, columns);
    };

    function getUnbilled(form) {

        var subList = form.addSubList('custpage_sub_unbilled', 'list', 'Unbilled Report', 'custpage_invoices');

        subList.addField('custpage_fld_customer', 'text', 'Customer');
        subList.addField('custpage_fld_type', 'text', 'Type');
        subList.addField('custpage_fld_vendor', 'text', 'Vendor Name');
        subList.addField('custpage_fld_merchant', 'text', 'Merchant');
        subList.addField('custpage_fld_ee', 'text', 'EE');
        subList.addField('custpage_fld_cardholder', 'text', 'Cardholder');
        subList.addField('custpage_fld_date', 'text', 'Date');
        subList.addField('custpage_fld_item', 'text', 'Item');
        subList.addField('custpage_fld_trans_memo', 'text', 'Tras Memo');
        subList.addField('custpage_fld_line_memo', 'text', 'Line Memo');
        subList.addField('custpage_fld_amount', 'currency', 'Amount');

        var searchResults = nlapiSearchRecord(null, 'customsearch_in8_bb_unbilled', [], []) || [];

        var line = 1;
        var lastCustomer = '';

        for (var i = 0; i < searchResults.length; i++) {

            if (lastCustomer != searchResults[i].getValue('entityid', 'customer')) {
                if (i > 0) {
                    subList.setLineItemValue('custpage_fld_customer', line, '');
                    line++;
                }
                subList.setLineItemValue('custpage_fld_customer', line, '<b>' + searchResults[i].getValue('entityid', 'customer') + '</b>');
                line++;
            }
            subList.setLineItemValue('custpage_fld_type', line, searchResults[i].getText('type'));
            subList.setLineItemValue('custpage_fld_vendor', line, searchResults[i].getValue('custcol_vendor_name'));

            if (searchResults[i].getValue('custcol_vendor_name')) {
                subList.setLineItemValue('custpage_fld_ee', line, searchResults[i].getValue('custcol_vendor_name'));
            } else {
                subList.setLineItemValue('custpage_fld_ee', line, searchResults[i].getValue('entityid', 'employee'));
            }

            subList.setLineItemValue('custpage_fld_merchant', line, searchResults[i].getValue('custbodycustbody_merchant'));
            subList.setLineItemValue('custpage_fld_cardholder', line, searchResults[i].getValue('custbodycustbody_cardholder'));
            subList.setLineItemValue('custpage_fld_date', line, searchResults[i].getValue('trandate'));
            subList.setLineItemValue('custpage_fld_item', line, searchResults[i].getText('item'));
            subList.setLineItemValue('custpage_fld_trans_memo', line, searchResults[i].getValue('memomain'));
            subList.setLineItemValue('custpage_fld_line_memo', line, searchResults[i].getValue('memo'));
            subList.setLineItemValue('custpage_fld_amount', line, searchResults[i].getValue('amount'));

            lastCustomer = searchResults[i].getValue('entityid', 'customer');
            line++;
        }
    }

    function dynamicSort(property) {
        var sortOrder = 1;
        if(property[0] === "-") {
            sortOrder = -1;
            property = property.substr(1);
        }
        return function (a,b) {
            /* next line works with strings and numbers, 
             * and you may want to customize it to your needs
             */
            var result = (a[property] < b[property]) ? -1 : (a[property] > b[property]) ? 1 : 0;
            return result * sortOrder;         
        }
    }    

    /**
     * Displays a message
     * 
     * @param {String} message Message
     * @returns {Void}
     */
    var displayMessage = function (message) {

        // Create a NetSuite form
        var form = nlapiCreateForm(FORM_NAME, false),
            html = message;

        // Add a new HTML field to display the HTML contents
        field = form.addField('file', 'inlinehtml', 'label');
        field.setLayoutType('outsidebelow');
        field.setDefaultValue('<font size="2pt">' + html + '</font>');

        form.addButton('custombutton_back', 'Back', 'window.history.back()');

        response.writePage(form);
    };

    return {
        handleGet: handleGet,
        handlePost: handlePost
    };
})();