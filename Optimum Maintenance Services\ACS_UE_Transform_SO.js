/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function afterSubmit(context) {
        var newRec = context.newRecord;
            
        var paymentMethod = newRec.getValue({ fieldId: 'paymentmethod' });
        if(paymentMethod){
            var cashSalesObj = record.transform({
                fromType: record.Type.SALES_ORDER,
                fromId: newRec.id,
                toType: record.Type.CASH_SALE,
                isDynamic: true 
            });
            cashSalesObj.save({ enableSourcing: true, ignoreMandatoryFields: true });
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
