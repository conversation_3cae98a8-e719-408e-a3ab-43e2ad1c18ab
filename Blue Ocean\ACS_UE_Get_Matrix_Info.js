/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search', 'N/format/i18n', 'N/ui/serverWidget'], function(record, search, format, serverWidget) {

    function beforeLoad(context) {
        if(context.type == context.UserEventType.PRINT) {

            try {
                var recordForm = context.form;
                var recObj = record.load({
                    type: ((context.newRecord.type == record.Type.INVOICE) ? record.Type.INVOICE : record.Type.PURCHASE_ORDER ),
                    id: context.newRecord.id
                });
                var curFormatter = format.getCurrencyFormatter({ currency: recObj.getText({ fieldId: 'currencysymbol' }) });


                var total = recObj.getValue({ fieldId: 'total' });
                var taxtotal = recObj.getValue({ fieldId: 'taxtotal' });

                var itemCount = recObj.getLineCount({
                    sublistId: 'item'
                });
                

                var items = [];
                var totalQty = 0;
                var markup = 0.00;
                var discount = 0.00;
                var subtotal = 0.00;
                for(var i = 0; i < itemCount; i++) {

                    var itemType = recObj.getSublistValue({ sublistId: 'item', fieldId: 'itemtype', line: i });

                    if(itemType == "Discount" || itemType == "Markup" || itemType == "OthCharge") {
                        switch (itemType) {
                            case "Discount":
                                discount += parseFloat(recObj.getSublistValue({ sublistId: 'item', fieldId: 'amount', line: i }))
                                break;

                            case "Markup":
                                markup += parseFloat(recObj.getSublistValue({ sublistId: 'item', fieldId: 'amount', line: i }))
                                break;
                            case "OthCharge":

                                var qty = recObj.getSublistValue({ sublistId: 'item', fieldId: 'quantity', line: i });
                                var rate = recObj.getSublistValue({ sublistId: 'item', fieldId: 'rate', line: i });
                                var amount = recObj.getSublistValue({ sublistId: 'item', fieldId: 'amount', line: i });
                                var itemName = recObj.getSublistText({ sublistId: 'item', fieldId: 'item', line: i })
                                var item = {
                                    itemid : itemName,
                                    vendorname : '',
                                    displayname : '',
                                    custitem_psgss_product_color : '',
                                    custitem_psgss_product_size : '',
                                    quantity : qty,
                                    rate : curFormatter.format({ number: rate }),
                                    amount : curFormatter.format({ number: amount })
                                }

                                subtotal += parseFloat(amount);
                                totalQty += parseInt(qty);
                
                                items.push(item);
                                
                                break;

                        }

                        continue;

                    }

                    var itemLookUp = search.lookupFields({
                        type: search.Type.INVENTORY_ITEM,
                        id: recObj.getSublistValue({ sublistId: 'item', fieldId: 'item', line: i }),
                        columns: [
                            'parent.itemid',
                            'vendorname',
                            'displayname',
                            'custitem_psgss_product_color',
                            'custitem_psgss_product_size',
                        ]
                    });

                    var qty = recObj.getSublistValue({ sublistId: 'item', fieldId: 'quantity', line: i });
                    var rate = recObj.getSublistValue({ sublistId: 'item', fieldId: 'rate', line: i });
                    var amount = recObj.getSublistValue({ sublistId: 'item', fieldId: 'amount', line: i });
                    
                    var item = {
                        itemid : itemLookUp["parent.itemid"],
                        vendorname : itemLookUp.vendorname,
                        displayname : itemLookUp.displayname,
                        custitem_psgss_product_color : itemLookUp.custitem_psgss_product_color[0].text,
                        custitem_psgss_product_size : itemLookUp.custitem_psgss_product_size[0].text,
                        quantity : qty,
                        rate : curFormatter.format({ number: rate }),
                        amount : curFormatter.format({ number: amount })
                    }

                    subtotal += parseFloat(amount);
                    totalQty += parseInt(qty);

                    items.push(item);
                }
                var itemData = {
                    items: items,
                    totalQty: totalQty,
                    subtotal: curFormatter.format({ number: subtotal }),
                    markup: curFormatter.format({ number: markup }),
                    discount: curFormatter.format({ number: discount }),
                    taxtotal: curFormatter.format({ number: ((taxtotal) ? taxtotal : 0.00 ) }),
                    total: curFormatter.format({ number: ((total) ? total : 0.00 ) })
                };

                var customField = recordForm.addField({
                    id : 'custpage_item_data',
                    type : serverWidget.FieldType.LONGTEXT,
                    label : 'item_data'
                });
                
                customField.defaultValue = JSON.stringify(itemData);


            } catch (e) {

                log.debug('Something went wrong', e);

                var customField = recordForm.addField({
                    id : 'custpage_item_data',
                    type : serverWidget.FieldType.LONGTEXT,
                    label : 'item_data'
                });
                
                customField.defaultValue = '';
            }
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
