<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
    <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
    <#if .locale == "zh_CN">
        <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
    <#elseif .locale == "zh_TW">
        <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
    <#elseif .locale == "ja_JP">
        <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
    <#elseif .locale == "ko_KR">
        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
    <#elseif .locale == "th_TH">
        <link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
    </#if>
    <macrolist>
        <macro id="nlheader">
            <table style="width: 100%; border: 1px solid black">
                <tr style="background-color: #176599; color: white">
                    <td style="font-size: 11pt" colspan= "2" align="left">FEAR OF GOD LLC</td>
                    <td style="font-size: 8pt" colspan= "1" align="left"></td>
                    <td style="font-size: 11pt" colspan= "2" align="right">PURCHASE ORDER #: ${record.tranid}</td>
                </tr>
                <tr style="background-color: #176599; color: white">
                    <td style="font-size: 8pt" colspan="2" align="left">3940 LAUREL CANYON BLDV. #427, STUDIO CITY 91604</td>
                    <td style="font-size: 8pt; font-weight: bold" colspan="1" align="center">${record.csegfog_division}</td>
                    <td style="font-size: 11pt" colspan="2" align="right">PO ISSUED ${record.createddate}</td>
                </tr>
            </table>
            <table style="padding-bottom: 5px; width: 100%;">
                <tr>
                    <td style="font-size: 8pt; border: 1px solid black; font-weight: bold; background-color: #e3e3e3" align="left">PURCHASED FROM</td>
                    <td style="font-size: 8pt; border: 1px solid black; font-weight: bold;  background-color: #e3e3e3" align="left">SHIP TO</td>
                    <td style="font-size: 8pt; border: 1px solid black; font-weight: bold;  background-color: #e3e3e3" align="left">PRODUCTION FACTORY</td>
                    <td style="font-size: 8pt; border: 1px solid black; font-weight: bold;  background-color: #e3e3e3" align="left">OTHER</td>
                </tr>
                <tr>
                    <td style="font-size: 8pt; border: 1px solid black" align="left">${record.billaddress}</td>
                    <td style="font-size: 8pt; border: 1px solid black" align="left">${record.shipaddress}</td>
                    <td style="font-size: 8pt; border: 1px solid black" align="left">${record.custbodyfog_production_factory}</td>
                    <td style="font-size: 8pt; border: 1px solid black" align="left">User PO: ${record.custbody_user_po}<br />Ship Via: ${record.shipmethod}<br />Delivery Terms: ${record.custbodyfog_delivery_terms_field}<br />Payment Terms: ${record.terms}</td>
                </tr>
            </table>
        </macro>
        <macro id="nlfooter">
        </macro>
    </macrolist>
    <style type="text/css">* {
        <#if .locale == "zh_CN">
            font-family: NotoSans, NotoSansCJKsc, sans-serif;
        <#elseif .locale == "zh_TW">
            font-family: NotoSans, NotoSansCJKtc, sans-serif;
        <#elseif .locale == "ja_JP">
            font-family: NotoSans, NotoSansCJKjp, sans-serif;
        <#elseif .locale == "ko_KR">
            font-family: NotoSans, NotoSansCJKkr, sans-serif;
        <#elseif .locale == "th_TH">
            font-family: NotoSans, NotoSansThai, sans-serif;
        <#else>
            font-family: NotoSans, sans-serif;
        </#if>
        }
        table {
            font-size: 8pt;
            table-layout: fixed;
        }
        th {
            font-weight: bold;
            font-size: 8pt;
            vertical-align: middle;
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
            color: #333333;
        }
        td {
            padding: 4px 4px;
        }
        td p { align:left }
        b {
            font-weight: bold;
            color: #333333;
        }
        table.header td {
            padding: 0;
            font-size: 8pt;
        }
        table.footer td {
            padding: 0;
            font-size: 8pt;
        }
        table.itemtable th {
            padding-bottom: 10px;
            padding-top: 10px;
        }
        table.body td {
            padding-top: 2px;
        }
        table.total {
            page-break-inside: avoid;
        }
        tr.totalrow {
            background-color: #e3e3e3;
            line-height: 200%;
        }
        td.totalboxtop {
            font-size: 12pt;
            background-color: #e3e3e3;
        }
        td.addressheader {
            font-size: 8pt;
            padding-top: 6px;
            padding-bottom: 2px;
        }
        td.address {
            padding-top: 0;
        }
        td.totalboxmid {
            font-size: 28pt;
            padding-top: 20px;
            background-color: #e3e3e3;
        }
        td.totalboxbot {
            background-color: #e3e3e3;
            font-weight: bold;
        }
        span.title {
            font-size: 28pt;
        }
        span.number {
            font-size: 16pt;
        }
        span.itemname {
            font-weight: bold;
            line-height: 150%;
        }
        hr {
            width: 100%;
            color: #d3d3d3;
            background-color: #d3d3d3;
            height: 1px;
        }
</style>
</head>
<body header="nlheader" header-height="17%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter-Landscape">
    <#if JSON.missingsizes != ''>
    <table style="padding-bottom: 15px; width: 100%; margin-left: -15px; margin-right: -15px;">
        <tr>
            <td style="background-color: #ff0000; font-size: 10pt; font-weight: bold; height: 20px;" align="center" colspan="3">These sizes are missing from the product size rank list: ${JSON.missingsizes}</td>
        </tr>
    </table>
    </#if>
    <#assign totalUnits = 0>
    <#assign totalAmount = 0>
    <#assign styleQty = 0>
    <#assign stylePrice = 0>
    <#list JSON.division as division>
        <#assign counter = 0>
        <#assign counter1 = 0>
        <#list division.sizes as sizes>
            <#assign counter = counter + 1>
        </#list>
        <#assign counter1 = '"' + counter + '"'>

        <#assign lastItem = 'x'>
        <#assign total = 0>
        <#list division.item as item>
            <#assign currentItem = item.name>
            <#if currentItem != lastItem>
            <#assign totalStyleAmt = 0>
            <#assign styleQty = 0>
                <table style="margin-bottom: 5px; width: 100%; border: 1px solid black; font-weight: bold">
                    <tr style="background-color: #176599; color: white">
                        <td style="font-size: 8pt" align="left">Season</td>
                        <td style="font-size: 8pt" align="left">Style</td>
                        <td style="font-size: 8pt" align="left">Description</td>
                        <td style="font-size: 8pt" align="right">HTS#</td>
                    </tr>
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt" align="left">${item.season}</td>
                        <td style="font-size: 8pt" align="left">${item.name}</td>
                        <td style="font-size: 8pt" align="left">${item.description}</td>
                        <td style="font-size: 8pt" align="right">${item.hts}</td>
                    </tr>
                </table>

                <table style="width: 100%">
                    <tr style="background-color: #e3e3e3">
                        <td style="font-size: 8pt; width: 85px; border: 1px solid black; font-weight: bold" align="center">Color</td>
                        <#if counter != 0>
                            <td style="font-size: 8pt; border: 1px solid black; font-weight: bold" align="center" colspan=${counter1}>Sizes &amp; Qyt's</td>
                        <#else>
                            <td style="font-size: 8pt; border: 1px solid black; font-weight: bold" align="center">Sizes &amp; Qyt's</td>
                        </#if>
                        <td style="font-size: 8pt; width: 85px; border: 1px solid black; font-weight: bold" align="center">Units</td>
                        <td style="font-size: 8pt; width: 85px; border: 1px solid black; font-weight: bold" align="center">Price (USD)</td>
                        <td style="font-size: 8pt; width: 85px; border: 1px solid black; font-weight: bold" align="center">Ext Price (USD)</td>
                        <td style="font-size: 8pt; width: 85px; border: 1px solid black; font-weight: bold" align="center">In Warehouse</td>
                        <td style="font-size: 8pt; width: 85px; border: 1px solid black; font-weight: bold" align="center">Cancel Date</td>
                    </tr>
                    <tr>
                        <td style="font-size: 8pt; width: 85px; border-right: 1px solid black" align="center"></td>
                        <#if counter != 0>
                            <#list division.sizes as sizes>
                                <td style="font-size: 8pt; font-weight:bold; border-right: 1px solid black; border-left: 1px solid black" align="center">${sizes}</td>
                            </#list>
                        <#else>
                            <td style="font-size: 8pt; font-weight:bold; border-right: 1px solid black; border-left: 1px solid black" align="center"></td>
                        </#if>
                        <td style="font-size: 8pt; width: 85px; border-left: 1px solid black" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                    </tr>
                    <#list division.item as item2>
                        <#if currentItem == item2.name>
                            <#assign totalQty = 0>
                            <#if item2.quantity != "0">
                                <#assign totalQty = item2.quantity?number>
                            </#if>
                            <tr>
                                <td style="font-size: 8pt; width: 85px; border-right: 1px solid black" align="center">${item2.color}: ${item2.colordes}</td>
                                <#if counter != 0>
                                    <#list item2.allsizes as sizes>
                                        <td style="font-size: 8pt; border-right: 1px solid black; border-left: 1px solid black" align="center">${sizes.quantity}</td>
                                        <#assign totalQty = totalQty + sizes.quantity?number>
                                    </#list>
                                <#else>
                                    <td style="font-size: 8pt; border-right: 1px solid black; border-left: 1px solid black" align="center"></td>
                                </#if>
                                <#assign styleQty = styleQty + totalQty>
                                <td style="font-size: 8pt; width: 85px; border-left: 1px solid black" align="center">${totalQty}</td>
                                <#assign totalAmt = item2.rate?number * totalQty>
                                <#assign totalUnits = totalUnits + totalQty?number>
                                <#assign totalStyleAmt = totalStyleAmt + totalAmt?number>
                                <#assign totalAmount = totalAmount + totalAmt?number>
                                <td style="font-size: 8pt; width: 85px" align="center">$${(item2.rate?number)?string(",##0.00")}</td>
                                <td style="font-size: 8pt; width: 85px" align="center">$${totalAmt?string(",##0.00")}</td>
                                <td style="font-size: 8pt; width: 85px" align="center">${item2.warehouse}</td>
                                <td style="font-size: 8pt; width: 85px" align="center"></td>
                            </tr>
                        </#if>
                    </#list>
                </table>
                <table style="width: 100%; font-weight: bold">
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt" align="right"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                    </tr>
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
                        <td style="font-size: 8pt; border-top: 1px solid black" align="right">Total for ${item.name}:</td>
                        <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center">${styleQty}</td>
                        <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
                        <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center">$${totalStyleAmt?string(",##0.00")}</td>
                        <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
                        <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
                    </tr>
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt" align="right"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                        <td style="font-size: 8pt; width: 85px" align="center"></td>
                    </tr>
                </table>
            </#if>
            <#assign lastItem = item.name>
        </#list>
    </#list>
    <table style="width: 100%; font-weight: bold">
        <tr style="font-weight: bold; background-color: #176599; color: white">
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
            <td style="font-size: 8pt; border-top: 1px solid black" align="right">Total Units</td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center">${totalUnits}</td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center">Amt (USD)</td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center">$${totalAmount?string(",##0.00")}</td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
            <td style="font-size: 8pt; width: 85px; border-top: 1px solid black" align="center"></td>
        </tr>
    </table>
    <table style="width: 100%">
        <tr>
            <td style="padding: 0px">
                <table style="width: 100%">
                    <tr style="font-weight: bold">
                        <td style="font-size: 8pt; background-color: #176599; color: white; border: 1px solid black" align="center">Comments</td>
                    </tr>
                    <tr>
                        <td style="font-size: 8pt; border: 1px solid black" align="left">${record.custbodyfog_po_comments_}</td>
                    </tr>
                </table>
            </td>
            <td style="padding: 0px">
                <table style="width: 100%">
                    <tr>
                        <td style="font-size: 8pt" align="left"><b>Packing Instructions: </b>${record.fog_packing_instructions}</td>
                    </tr>
                    <tr>
                        <td style="font-size: 8pt" align="left"><b>Special Instructions: </b>${record.custbodyfog_special_instructions}</td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <table style="width: 100%; font-weight: bold">
        <tr style="font-weight: bold; margin-top: 100px">
            <td style="font-size: 8pt; width: 55%" align="left"></td>
            <td style="font-size: 8pt; border-top: 1px solid black; width: 28%" align="left">Authorized Signature</td>
            <td style="font-size: 8pt; width: 2%" align="left"></td>
            <td style="font-size: 8pt; border-top: 1px solid black; width: 15%" align="left">Date</td>
        </tr>
    </table>
</body>
</pdf>