{"page_of_records": [{"record": {"ccavsstreetmatch": {"internalid": "ccavsstreetmatch.internalid", "name": "ccavsstreetmatch.name"}, "ccavszipmatch": {"internalid": "ccavszipmatch.internalid", "name": "ccavszipmatch.name"}, "custbodycustbody_approval_log": "custbodycustbody_approval_log", "authcode": "authcode", "balance": "balance", "isbasecurrency": false, "billaddress": "billaddress", "billaddresslist": {"internalid": "billaddresslist.internalid", "name": "billaddresslist.name"}, "billaddrtext": "billaddrtext", "billingaddress_text": "billingaddress_text", "billaddr1": "billaddr1", "billaddr2": "billaddr2", "billaddr3": "billaddr3", "billaddressformat": "billaddressformat", "billaddressee": "billaddressee", "billattention": "billattention", "billcity": "billcity", "billcountry": "billcountry", "billcustomform": {"internalid": "billcustomform.internalid", "name": "billcustomform.name"}, "bill_eml_nkey_": "bill_eml_nkey_", "billphone": "(917)494-4476", "billstate": "billstate", "billzip": "billzip", "ccsecuritycode": "ccsecuritycode", "ccsecuritycodematch": {"internalid": "ccsecuritycodematch.internalid", "name": "ccsecuritycodematch.name"}, "canhavestackable": false, "carddataprovided": false, "ccstreet": "ccstreet", "cczipcode": "cczipcode", "cardholderauthentication": {"internalid": "cardholderauthentication.internalid", "name": "cardholderauthentication.name"}, "custbody_celigo_export_count": 999, "class": {"internalid": "class.internalid", "name": "class.name"}, "couponcode": {"internalid": "couponcode.internalid", "name": "couponcode.name"}, "celigo_nlobjTransformId": {"name": "celigo_nlobjTransformId.name"}, "ccnumber": "ccnumber", "ccapproved": false, "creditcard": {"internalid": "creditcard.internalid", "name": "creditcard.name"}, "currencyname": "currencyname", "currency": {"internalid": "currency.internalid", "name": "currency.name"}, "currencysymbol": "currencysymbol", "customform": {"internalid": "customform.internalid", "name": "customform.name"}, "entity": {"internalid": "entity.internalid", "name": "entity.name"}, "customercode": "customercode", "message": "message", "custbody3": "custbody3", "trandate": "07/14/2021", "custbody_date_printed": "07/14/2021", "department": {"internalid": "department.internalid", "name": "department.name"}, "custbody_gmwrld_deposit_due": "custbody_gmwrld_deposit_due", "cchold": "cchold", "ccholdetails": "ccholdetails", "discounttotal": "discounttotal", "discountitem": {"internalid": "discountitem.internalid", "name": "discountitem.name"}, "custbody_celigo_disc_total_variance": "custbody_celigo_disc_total_variance", "_eml_nkey_": "_eml_nkey_", "enddate": "07/14/2021", "exchangerate": "exchangerate", "excludecommission": false, "ccexpiredate": "ccexpiredate", "externalid": "externalid", "fob": "fob", "generatetranidonsave": false, "getauth": false, "giftcertapplied": "giftcertapplied", "custbody_attached_graphics": {"internalid": "custbody_attached_graphics.internalid", "name": "custbody_attached_graphics.name"}, "cciavsmatch": {"internalid": "cciavsmatch.internalid", "name": "cciavsmatch.name"}, "ignoreavs": false, "ignorecsc": false, "celigo_ignore_mandatory_fields": false, "celigo_initializeValues": "celigo_initializeValues", "internalid": "internalid", "isdefaultshippingrequest": "isdefaultshippingrequest", "custbody_gmwrld_item_fulfillment": {"internalid": "custbody_gmwrld_item_fulfillment.internalid", "name": "custbody_gmwrld_item_fulfillment.name"}, "item": [{"celigo_inventorydetail": "celigo_inventorydetail", "amount": "amount", "isclosed": false, "commitinventory": {"internalid": "commitinventory.internalid", "name": "commitinventory.name"}, "commitmentfirm": false, "custcol_2663_companyname": "custcol_2663_companyname", "celigo_createline": false, "createpo": {"internalid": "createpo.internalid", "name": "createpo.name"}, "createwo": false, "description": "description", "excludefromraterequest": false, "custcol_2663_firstname": "custcol_2663_firstname", "freegiftpromotion": {"internalid": "freegiftpromotion.internalid", "name": "freegiftpromotion.name"}, "giftcertfrom": "giftcertfrom", "giftcertmessage": "giftcertmessage", "celigo_ignore_current_line_invdetail_subrecord": "celigo_ignore_current_line_invdetail_subrecord", "custcol_2663_isperson": "custcol_2663_isperson", "item": {"internalid": "item.internalid", "name": "item.name"}, "itemtype": "itemtype", "itemsubtype": "itemsubtype", "custcol_2663_lastname": "custcol_2663_lastname", "line": 999, "linenum": 999, "linenumber": 999, "matrixtype": "matrixtype", "minqty": "minqty", "options": "options", "orderpriority": "orderpriority", "custcol_pick_ticket_qty": 999, "price": {"internalid": "price.internalid", "name": "price.name"}, "printitems": "printitems", "quantity": "quantity", "rate": "rate", "giftcertrecipientemail": "<EMAIL>", "giftcertrecipientname": "giftcertrecipientname", "serialnumbers": "serialnumbers", "istaxable": false, "custcol_gmwrld_test_line_field": "custcol_gmwrld_test_line_field", "fulfillable": "fulfillable", "custcol_gmwrld_trans_line_field": "custcol_gmwrld_trans_line_field", "units": {"internalid": "units.internalid", "name": "units.name"}}], "leadsource": {"internalid": "leadsource.internalid", "name": "leadsource.name"}, "location": {"internalid": "location.internalid", "name": "location.name"}, "custbody_mes_ach_auth_type": {"internalid": "custbody_mes_ach_auth_type.internalid", "name": "custbody_mes_ach_auth_type.name"}, "memo": "memo", "custbody_mes_invl_config": {"internalid": "custbody_mes_invl_config.internalid", "name": "custbody_mes_invl_config.name"}, "custbody_mes_invlcf_disp_req_rsl_alert": false, "custbody_mes_invl_end_customer_link": "custbody_mes_invl_end_customer_link", "custbody_mes_invl_externalid": "custbody_mes_invl_externalid", "custbody_mes_invl_guid": "custbody_mes_invl_guid", "custbody_mes_invl_hpp_link": "custbody_mes_invl_hpp_link", "custbody_mes_invl_location_zip": "custbody_mes_invl_location_zip", "custbody_mes_invl_postback_link": "custbody_mes_invl_postback_link", "custbody_mes_inv_quote_logic_ovr": {"internalid": "custbody_mes_inv_quote_logic_ovr.internalid", "name": "custbody_mes_inv_quote_logic_ovr.name"}, "custbody_mes_invl_related_tran": {"internalid": "custbody_mes_invl_related_tran.internalid", "name": "custbody_mes_invl_related_tran.name"}, "custbody_mes_invl_sendto_invl": false, "custbody_mes_invl_sys_notes": "custbody_mes_invl_sys_notes", "custbody_mes_invl_track_num": "custbody_mes_invl_track_num", "custbody_mes_invl_uuid": "custbody_mes_invl_uuid", "ccname": "ccname", "opportunity": {"internalid": "opportunity.internalid", "name": "opportunity.name"}, "tranid": "tranid", "custbody_celigo_order_total_variance": "custbody_celigo_order_total_variance", "overridehold": false, "overrideholdchecked": false, "overrideshippingcost": "overrideshippingcost", "pnrefnum": "pnrefnum", "otherrefnum": "otherrefnum", "partner": {"internalid": "partner.internalid", "name": "partner.name"}, "paymentsessionamount": "paymentsessionamount", "paymentcustomdata": "paymentcustomdata", "paymentmethod": {"internalid": "paymentmethod.internalid", "name": "paymentmethod.name"}, "paymentoperation": {"internalid": "paymentoperation.internalid", "name": "paymentoperation.name"}, "creditcardprocessor": {"internalid": "creditcardprocessor.internalid", "name": "creditcardprocessor.name"}, "custbody_gmwrld_percent_deposit": "custbody_gmwrld_percent_deposit", "celigo_persist_new_line_insertion_order": false, "celigo_write_log": false, "promocodepluginimpl": "promocodepluginimpl", "promocode": {"internalid": "promocode.internalid", "name": "promocode.name"}, "ccispurchasecardbin": false, "discountrate": "discountrate", "paymenteventholdreason": {"internalid": "paymenteventholdreason.internalid", "name": "paymenteventholdreason.name"}, "custbody_record_changed": false, "isrecurringpayment": false, "returnurl": "<PERSON><PERSON><PERSON>", "redirecturl": "redirecturl", "returntrackingnumbers": "returntrackingnumbers", "saleseffectivedate": "07/14/2021", "salesrep": {"internalid": "salesrep.internalid", "name": "salesrep.name"}, "messagesel": {"internalid": "messagesel.internalid", "name": "messagesel.name"}, "ccprocessaspurchasecard": false, "shipcomplete": false, "shipdate": "07/14/2021", "shipaddress": "shipaddress", "shipaddresslist": {"internalid": "shipaddresslist.internalid", "name": "shipaddresslist.name"}, "custbody_celigo_ship_total_variance": "custbody_celigo_ship_total_variance", "shipaddrtext": "shipaddrtext", "shippingaddress_text": "shippingaddress_text", "shipaddr1": "shipaddr1", "shipaddr2": "shipaddr2", "shipaddr3": "shipaddr3", "shipcountry": "shipcountry", "shipstate": "shipstate", "shipaddressformat": "shipaddressformat", "shipzip": "shipzip", "shipaddressee": "shipaddressee", "shipattention": "shipattention", "shipcity": "shipcity", "altshippingcost": "altshippingcost", "shippingcost": "shippingcost", "shippingcostoverridden": "shippingcostoverridden", "shipcustomform": {"internalid": "shipcustomform.internalid", "name": "shipcustomform.name"}, "ship_eml_nkey_": "ship_eml_nkey_", "shipmethod": {"internalid": "shipmethod.internalid", "name": "shipmethod.name"}, "shipphone": "(917)494-4476", "billisresidential": "billisresidential", "shipisresidential": "shipisresidential", "startdate": "07/14/2021", "custbody4": "custbody4", "orderstatus": {"internalid": "orderstatus.internalid", "name": "orderstatus.name"}, "paymenteventresult": {"internalid": "paymenteventresult.internalid", "name": "paymenteventresult.name"}, "subsidiary": {"internalid": "subsidiary.internalid", "name": "subsidiary.name"}, "subtotal": "subtotal", "custbody_system_price": "custbody_system_price", "taxtotal": "taxtotal", "taxrate": "taxrate", "taxitem": {"internalid": "taxitem.internalid", "name": "taxitem.name"}, "custbody_celigo_tax_total_variance": "custbody_celigo_tax_total_variance", "istaxable": false, "terms": {"internalid": "terms.internalid", "name": "terms.name"}, "custbody1": "custbody1", "custbody2": {"internalid": "custbody2.internalid", "name": "custbody2.name"}, "tobeemailed": false, "tobefaxed": false, "tobeprinted": false, "total": "total", "linkedtrackingnumbers": "linkedtrackingnumbers", "transactionnumber": "transactionnumber", "unbilledorders": "unbilledorders", "updatecurrency": "updatecurrency", "celigo_use_address_subrecord": false, "celigo_recordmode_dynamic": false, "email": "email", "errornotificationsfield": "errornotificationsfield", "fax": "(917)494-4476", "noticenotificationsfield": "noticenotificationsfield", "custbody5": "custbody5", "warningnotificationsfield": "warningnotificationsfield", "recmachcustrecord_2663_transaction": [{"custrecord_2663_batch": {"internalid": "custrecord_2663_batch.internalid", "name": "custrecord_2663_batch.name"}, "celigo_createline": false, "id": 999, "linenum": 999, "rec29view": "rec29view"}], "celigo_groupLinesBy_recmachcustrecord_2663_transaction": "celigo_groupLinesBy_recmachcustrecord_2663_transaction", "celigo_replaceAllLines_recmachcustrecord_2663_transaction": false, "contacts": [{"entityid": "entityid", "celigo_createline": false, "email": "<EMAIL>", "title": "title", "line": 999, "linenum": 999, "phone": "(917)494-4476", "contactrole": {"internalid": "contactrole.internalid", "name": "contactrole.name"}}], "celigo_groupLinesBy_contacts": "celigo_groupLinesBy_contacts", "celigo_replaceAllLines_contacts": false, "events": [{"alldayevent": false, "celigo_createline": false, "startdate": "07/14/2021", "endtime": "endtime", "line": 999, "linenum": 999, "location": "location", "starttime": "starttime", "title": "title"}], "celigo_groupLinesBy_events": "celigo_groupLinesBy_events", "celigo_replaceAllLines_events": false, "mediaitem": [{"mediaitem": {"internalid": "mediaitem.internalid", "name": "mediaitem.name"}, "celigo_createline": false, "filetype": "filetype", "folder": "folder", "lastmodifieddate": "2021-07-14T14:51:15.855Z", "line": 999, "linenum": 999, "filesize": "filesize"}], "celigo_groupLinesBy_mediaitem": "celigo_groupLinesBy_mediaitem", "celigo_replaceAllLines_mediaitem": false, "giftcertredemption": [{"authcodeamtdesired": "authcodeamtdesired", "authcodeapplied": "authcodeapplied", "authcodeamtremaining": "authcodeamtremaining", "celigo_createline": false, "authcode": {"internalid": "authcode.internalid", "name": "authcode.name"}, "line": 999, "linenum": 999}], "celigo_groupLinesBy_giftcertredemption": "celigo_groupLinesBy_giftcertredemption", "celigo_replaceAllLines_giftcertredemption": false, "celigo_groupLinesBy_item": "celigo_groupLinesBy_item", "celigo_replaceAllLines_item": false, "paymentevent": [{"avsstreet": "avsstreet", "avszip": "a<PERSON><PERSON><PERSON>", "amount": "amount", "authcode": "authcode", "csc": "csc", "sstreet": "sstreet", "szipcode": "szipcode", "celigo_createline": false, "eventdate": "2021-07-14T14:51:15.855Z", "type": "type", "expiredate": "expiredate", "eventtype": "eventtype", "line": 999, "linenum": 999, "sname": "sname", "override": "override", "pnrefnum": "pnrefnum", "card": "card", "request": "request", "response": "response", "holdreason": "<PERSON><PERSON><PERSON>", "isrecurringpayment": "isrecurringpayment", "result": "result", "nseqnum": 999, "owningtransaction": "owningtransaction", "viewfld": "viewfld"}], "celigo_groupLinesBy_paymentevent": "celigo_groupLinesBy_paymentevent", "celigo_replaceAllLines_paymentevent": false, "calls": [{"celigo_createline": false, "startdate": "07/14/2021", "line": 999, "linenum": 999, "assigned": {"internalid": "assigned.internalid", "name": "assigned.name"}, "phone": "(917)494-4476", "title": "title"}], "celigo_groupLinesBy_calls": "celigo_groupLinesBy_calls", "celigo_replaceAllLines_calls": false, "promotions": [{"couponcode": {"internalid": "couponcode.internalid", "name": "couponcode.name"}, "celigo_createline": false, "discount": {"internalid": "discount.internalid", "name": "discount.name"}, "eligiblefreegifts": "eligiblefreegifts", "freegiftsadded": "freegiftsadded", "line": 999, "linenum": 999, "applicabilitymode": "applicabilitymode", "muccpromocodeinstance": "muccpromocodeinstance", "promocode": {"internalid": "promocode.internalid", "name": "promocode.name"}, "purchasediscount": "purchasediscount", "discountrate": "discountrate", "applicabilityreason": "applicabilityreason", "shippingdiscount": "shippingdiscount", "applicabilitystatus": {"internalid": "applicabilitystatus.internalid", "name": "applicabilitystatus.name"}}], "celigo_groupLinesBy_promotions": "celigo_groupLinesBy_promotions", "celigo_replaceAllLines_promotions": false, "tasks": [{"celigo_createline": false, "duedate": "07/14/2021", "line": 999, "linenum": 999, "priority": {"internalid": "priority.internalid", "name": "priority.name"}, "startdate": "07/14/2021", "title": "title"}], "celigo_groupLinesBy_tasks": "celigo_groupLinesBy_tasks", "celigo_replaceAllLines_tasks": false, "usernotes": [{"celigo_createline": false, "notedate": "07/14/2021", "direction": {"internalid": "direction.internalid", "name": "direction.name"}, "externalid": "externalid", "internalid": "internalid", "line": 999, "linenum": 999, "note": "note", "time": "time", "title": "title", "notetype": {"internalid": "notetype.internalid", "name": "notetype.name"}}], "celigo_groupLinesBy_usernotes": "celigo_groupLinesBy_usernotes", "celigo_replaceAllLines_usernotes": false, "billingaddress": {"addr1": "billingaddress.addr1", "addr2": "billingaddress.addr2", "addr3": "billingaddress.addr3", "addressee": "billingaddress.addressee", "addressformat": "billingaddress.addressformat", "addrphone": "(917)494-4476", "attention": "billingaddress.attention", "city": "billingaddress.city", "externalid": "billingaddress.externalid", "override": false, "state": "billingaddress.state", "zip": "billingaddress.zip", "country": {"internalid": "billingaddress.country.internalid", "name": "billingaddress.country.name"}}, "shippingaddress": {"addr1": "shippingaddress.addr1", "addr2": "shippingaddress.addr2", "addr3": "shippingaddress.addr3", "addressee": "shippingaddress.addressee", "addressformat": "shippingaddress.addressformat", "addrphone": "(917)494-4476", "attention": "shippingaddress.attention", "city": "shippingaddress.city", "externalid": "shippingaddress.externalid", "state": "shippingaddress.state", "zip": "shippingaddress.zip", "country": {"internalid": "shippingaddress.country.internalid", "name": "shippingaddress.country.name"}}}}]}