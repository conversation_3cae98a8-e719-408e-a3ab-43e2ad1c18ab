/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 *
 */
define(['N/currentRecord'], function(currentRecord) {

    function pageInit(context) {
        if(context.mode == 'edit'){
            
        }
    }

    function saveRecord(context) {
        
    }

    function validateField(context) {
        
    }

    function fieldChanged(context) {
        
    }

    function postSourcing(context) {
        
    }

    function lineInit(context) {
        
    }

    function validateDelete(context) {
        
    }

    function validateInsert(context) {
        
    }

    function validateLine(context) {
        
    }

    function sublistChanged(context) {
        
    }

    return {
        pageInit: pageInit,
        saveRecord: saveRecord,
        validateField: setInterval(validateField, 5000),
    }
});
