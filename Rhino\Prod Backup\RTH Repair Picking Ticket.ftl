<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
	<link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
	<#if .locale == "zh_CN">
		<link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
	<#elseif .locale == "zh_TW">
		<link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
	<#elseif .locale == "ja_JP">
		<link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
	<#elseif .locale == "ko_KR">
		<link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
	<#elseif .locale == "th_TH">
		<link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
	</#if>
    <macrolist>
        <macro id="nlheader">
            <table class="header" style="width: 100%;"><tr>
	<td rowspan="3">${record.custbodylocation_address}</td>
	<td align="right"><span class="title">${record@title}</span></td>
	</tr>
	<tr>
	<td align="right"><span class="number">#${record.tranid}</span></td>
	</tr>
	<tr>
	<td align="right">${record.trandate}</td>
	</tr></table>
        </macro>
        <macro id="nlfooter">
            <table class="footer" style="width: 100%;"><tr>
	<td><barcode codetype="code128" showtext="true" value="${record.tranid}"/></td>
	<td align="right"><pagenumber/> of <totalpages/></td>
	</tr></table>
        </macro>
    </macrolist>
    <style type="text/css">* {
		<#if .locale == "zh_CN">
			font-family: NotoSans, NotoSansCJKsc, sans-serif;
		<#elseif .locale == "zh_TW">
			font-family: NotoSans, NotoSansCJKtc, sans-serif;
		<#elseif .locale == "ja_JP">
			font-family: NotoSans, NotoSansCJKjp, sans-serif;
		<#elseif .locale == "ko_KR">
			font-family: NotoSans, NotoSansCJKkr, sans-serif;
		<#elseif .locale == "th_TH">
			font-family: NotoSans, NotoSansThai, sans-serif;
		<#else>
			font-family: NotoSans, sans-serif;
		</#if>
		}
		table {
			font-size: 9pt;
			table-layout: fixed;
		}
		th {
			font-weight: bold;
			font-size: 8pt;
			vertical-align: middle;
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
			color: #333333;
		}
		td {
            padding: 4px 6px;
        }
		td p { align:left }
		b {
			font-weight: bold;
			color: #333333;
		}
		table.header td {
			padding: 0;
			font-size: 10pt;
		}
		table.footer td {
			padding: 0;
			font-size: 8pt;
		}
		table.itemtable th {
			padding-bottom: 10px;
			padding-top: 10px;
		}
		table.body td {
			padding-top: 2px;
		}
		td.addressheader {
			font-size: 8pt;
			padding-top: 6px;
			padding-bottom: 2px;
		}
		td.address {
			padding-top: 0;
		}
		span.title {
			font-size: 28pt;
		}
		span.number {
			font-size: 16pt;
		}
		span.itemname {
			font-weight: bold;
			line-height: 150%;
		}
		hr {
			width: 100%;
			color: #d3d3d3;
			background-color: #d3d3d3;
			height: 1px;
		}
</style>
</head>
<body header="nlheader" header-height="10%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
    <table style="width: 793px; margin-top: 10px;"><tr>
	<td class="addressheader" style="width: 397px;"><b>${record.shipaddress@label}</b></td>
	<td class="addressheader" style="width: 358px;"><strong>Bill To</strong></td>
	</tr>
	<tr>
	<td class="address" style="width: 397px;">${record.shipaddress}<br /><br />Attn:&nbsp;${record.custbody51}</td>
	<td class="address" style="width: 358px;">${record.billaddress}</td>
	</tr></table>

<table class="body" style="width: 100%; margin-top: 10px;"><tr>
	<th style="width: 168px;">${record.shipmethod@label}</th>
	<th style="width: 201px;">Inside Sales Rep</th>
	<th style="width: 161px;">Sales Rep</th>
	<th style="width: 168px;">Purchase Order</th>
	</tr>
	<tr>
	<td style="width: 168px; text-align: center;">${record.shipmethod}</td>
	<td style="width: 201px; text-align: center;">${record.custbody21}</td>
	<td style="width: 161px; text-align: center;">${record.salesrep}</td>
	<td style="width: 168px; text-align: center;">${record.otherrefnum}</td>
	</tr></table>
<span style="font-size:11px;"><strong>Shipping Notes:</strong><br />${record.custbody43}<br /><br /><strong>Customer Header Notes:</strong><br />${record.custbody5}<br /><br /><strong>Customer Footer Notes:</strong><br />${record.custbody29}<br /><br /><strong>RT247 Case ID:&nbsp;</strong>${record.custbodyrt247caseid}<br />I<strong>tem:</strong>&nbsp;${record.custbodyrepitem}&nbsp; &nbsp; &nbsp; <strong>Description:</strong>&nbsp;${record.custbodyreppartdesc}&nbsp; &nbsp; &nbsp;<strong>Customer Item:</strong>&nbsp;${record.custbodyrepcustitem}&nbsp; &nbsp; &nbsp; <strong>Serial #:</strong>&nbsp;${record.custbodyreptoolserial}<br /><strong>Accessories:</strong>&nbsp;${record.custbodyrepaccessrecd}&nbsp; &nbsp; &nbsp;<strong><span style="font-size:11px;">Tool Bin:&nbsp;</span></strong>${record.custbodyreptoolbin}</span><span style="font-size:11px;"><strong>&nbsp; Customer Add&#39;l Ref: </strong>${record.custbodyrepcustaddlref}<br /><strong>Comments:</strong><br />${record.custbodyrepcusttoolnote}<br /><strong>Condition Received:</strong><br />${record.custbodyrepcondrecd}<br /><strong>Evaluation:</strong><br />${record.custbodyrepeval}&nbsp;</span><#if record.item?has_content>

<table class="itemtable" style="width: 100%; margin-top: 10px;"><!-- start items --><#list record.item as item><#if item_index==0>
<thead>
	<tr>
	<th colspan="4" style="width: 175px; text-align: left;">${item.item@label}</th>
	<th style="width: 157px; text-align: left;">Inventory Detail</th>
	<th style="width: 80px;">Location</th>
	<th style="width: 53px;">Qty</th>
	<th style="width: 90px;">Qty Avail</th>
	<th style="width: 50px;">${item.units@label}</th>
	<th style="width: 99px;">Committed</th>
	</tr>
</thead>
</#if><tr>
	<td colspan="4" style="width: 175px;"><strong>${item.custcol3}</strong><br />${item.description}<br />Brand:&nbsp;${item.custcol9}</td>
	<td style="width: 157px;">${item.inventorydetail}<br /><br />${item.custcol_supplyorder}</td>
	<td style="width: 80px; text-align: center;">${item.location}</td>
	<td style="width: 53px; text-align: center;">${item.quantity}</td>
	<td style="width: 90px; text-align: center;">${item.quantityavailable}</td>
	<td style="width: 50px; text-align: center;">${item.units}</td>
	<td style="width: 99px; text-align: center;">${item.quantitycommitted}</td>
	</tr>
	</#list><!-- end items --></table>
</#if>
</body>
</pdf>