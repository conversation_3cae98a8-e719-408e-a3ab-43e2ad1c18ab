/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {


    function afterSubmit(context) {

        try {
            var createdRec = record.load({
                id: context.newRecord.id,
                type: record.Type.ITEM_FULFILLMENT,
                isDynamic: false
            });

            packageCount = createdRec.getLineCount({ sublistId: 'package' });

            for(var j = 0; j < packageCount; j++) {
                var trackingNo = createdRec.getSublistValue({
                    sublistId: 'package',
                    fieldId: 'packagetrackingnumber',
                    line: j
                });

                if(trackingNo) {
                    createdRec.removeLine({
                        sublistId: 'package',
                        line: j
                    });
                }
            }
        } catch (e) {
            log.error("Error", e);
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
