function beforeLoad(type, form)
{
    if(type == "edit" || type == "create")
    {
        var record = nlapiGetNewRecord();

        if(record.getFieldValue('custrecord_in8_csfs_customform'))
        {
            var customform = nlapiLoadRecord('customrecord_in8_customexternalform', record.getFieldValue('custrecord_in8_csfs_customform'));

            var searchName = form.addField("custpage_searchname", 'select', "Select Saved Search");

            var options = getSavedSearches(customform.getFieldValue('custrecord_in8_csf_recordtype'));

            searchName.addSelectOption('', '');

            for(var i = 0; i < options.length; i++)
            {
                searchName.addSelectOption(options[i].id, options[i].label);
            }

            searchName.setDefaultValue(record.getFieldValue('custrecord_in8_csfs_savedsearch'));
        }
    }
}

function getSavedSearches(recordType)
{
    var columns = [];
    columns.push(new nlobjSearchColumn('id'));
    columns.push(new nlobjSearchColumn('title').setSort(false));

    var search = nlapiCreateSearch('savedsearch', null, columns);

    var resultSet = search.runSearch();

    var searches = [];

    var start = 0;
    var end = 1000;

    do
    {
        var results = resultSet.getResults(start, end);

        for (var i = 0; i < results.length; i++)
        {
            var savedsearch = {};
            savedsearch.id = results[i].getValue('id');
            savedsearch.label = results[i].getValue('title');

            searches.push(savedsearch);
        }

        start += 1000;
        end += 1000;
    }
    while(results.length > 0);

    return searches;
}