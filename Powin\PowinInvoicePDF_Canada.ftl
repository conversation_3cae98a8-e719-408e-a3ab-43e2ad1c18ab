<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="2" width="55%">
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    float="left" width="140px" height="53px" style="margin: 7px;" /> </#if>
                        </td>
                        <td width="45%" align="left"></td>
                    </tr>
                    <tr>
                        <td align="left"><span class="title">${record@title}</span></td>
                    </tr>
                    <tr>
                        <td><span class="nameandaddress">${companyInformation.companyName}</span><br /><span
                                class="nameandaddress">Powin Energy Ontario Storage, LLC<br />20550 SW 115th Ave<br />Tualatin OR 97062<br />United States</span></td>
                        <td>
                            <table width="100%">
                                <tr>
                                    <td><b>Date</b></td>
                                    <td>${record.trandate}</td>
                                </tr>
                                <tr>
                                    <td><b>Invoice #</b></td>
                                    <td>${record.tranid}</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td><b>${record.terms@label}</b></td>
                                    <td>${record.terms}</td>
                                </tr>
                                <tr>
                                    <td><b>${record.duedate@label}</b></td>
                                    <td>${record.duedate}</td>
                                </tr>
                                <tr>
                                    <td><b>${record.otherrefnum@label}</b></td>
                                    <td>${record.otherrefnum}</td>
                                </tr>
                                <tr>
                                    <td><b>${record.job@label}</b></td>
                                    <td>${record.job}</td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0px;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 9.5pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                font-size: 9pt;
                padding-top: 0px;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 21pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="20%" footer="nlfooter" footer-height="1pt" padding="0.4in 0.4in 0.4in 0.4in"
        size="Letter">
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
                <td class="addressheader" colspan="3"><b>${record.shipaddress@label}</b></td>
            </tr>
            <tr>
                <td class="address" colspan="3" rowspan="2">${record.billaddress}</td>
                <td class="address" colspan="3" rowspan="2">${record.shipaddress}</td>
            </tr>
        </table>
        <#if record.item?has_content>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th width="24%">${item.item@label}</th>
                                <th width="25%">${item.description@label}</th>
                                <th width="4%" align="center">QTY</th>
                                <th width="13%" align="right">${item.amountordered@label}</th>
                                <th width="8%" align="right">${item.currentpercent@label}</th>
                                <th width="13%" align="right">${item.amount@label}</th>
                            </tr>
                        </thead>
                    </#if>
                    <#assign amtorderered = item.amountordered />
                    <#if item.rate?starts_with("(")>
                        <#assign amtorderered = item.rate />
                    </#if> 
                    <tr>
                        <td><span class="itemname">${item.item}</span></td>
                        <td>${item.description}</td>
                        <td align="center" line-height="150%">${item.quantityordered}</td>
                        <td align="right">${amtorderered}</td>
                        <td align="right">${item.currentpercent}</td>
                        <td align="right">${item.amount}</td>
                    </tr>
                </#list><!-- end items -->
            </table>

            <hr />
        </#if>
        <table class="total" style="width: 100%; margin-top: 10px;">
            <tr>
                <td colspan="4">&nbsp;</td>
                <td align="right"><b>${record.subtotal@label}</b></td>
                <td align="right">${record.subtotal}</td>
            </tr>
            <tr>
                <td colspan="4" style="height: 25px;">&nbsp;</td>
                <td align="right" style="height: 25px;"><b>GST/HST Tax</b></td>
                <td align="right" style="height: 25px;">${record.taxtotal}</td>
            </tr>
            <tr>
                <td colspan="4" style="height: 25px;">&nbsp;</td>
                <td align="right" style="height: 25px;"><b>${record.total@label}</b></td>
                <td align="right" style="height: 25px;">${record.total}</td>
            </tr>
            <tr>
                <td colspan="4" style="height: 25px;">&nbsp;</td>
                <td align="right" style="height: 25px;"><b>${record.amountpaid@label}</b></td>
                <td align="right" style="height: 25px;">${record.amountpaid}</td>
            </tr>
            <tr class="totalrow">
                <td background-color="#ffffff" colspan="4">&nbsp;</td>
                <td align="right"><b>${record.amountremaining@label}</b></td>
                <td align="right">${record.amountremaining}</td>
            </tr>
        </table>
        <table width="100%">
            <tr>
                <td>${record.message}</td>
            </tr>
        </table>
        <table width="100%">
            <tr>
                <td width="60%" style="font-size: 12pt;"><b>GST/HST Tax#: ********* RT0001</b></td>
                <td width="40%"></td>
            </tr>
            <tr>
                <td width="60%" style="font-size: 10pt;"><b><span style="font-size: 11.5pt; padding-bottom:4px;">Bank Information</span></b><br />${record.custbody_pec_banking_information}</td>
                <td width="40%" style="font-size: 10pt;"><b><span style="font-size: 11.5pt; padding-bottom:4px;">Project Terms</span></b><br />${record.custbody_pec_project_terms}</td>
            </tr>
        </table>
    </body>
</pdf>