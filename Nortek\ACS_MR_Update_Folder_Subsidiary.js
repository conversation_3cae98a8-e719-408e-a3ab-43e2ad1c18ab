/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/record', 'N/search', 'N/email'], function(record, search, email) {

    function getInputData() {

        // var weekStartEnd = getWeek();
        var folderSearchObj = search.create({
            type: record.Type.FOLDER,
            filters:
            [
                ["subsidiary","noneof","@NONE@"], 
                "AND", 
                ["formulatext: {parent}","isnotempty",""]
            ],
            columns:
            [
                search.createColumn({name: "name", label: "Name"})
            ]
        });

        // var folderSearchObj = search.load({
        //     id: 'customsearch31729'
        // });

        var pagedData = folderSearchObj.runPaged({ pageSize: 1000 });
        var resultArray = [];
        // iterate the pages
        for( var i=0; i < pagedData.pageRanges.length; i++ ) {
        // for( var i=0; i < 1; i++ ) {
    
            // fetch the current page data
            var currentPage = pagedData.fetch(i);
    
            // and forEach() thru all results
            currentPage.data.forEach( function(result) {
    
                resultArray.push({
                    transId: result.id
                });
    
            });
    
        }

        return resultArray;

    }

    function map(context) {
        // accept data from previous stage
        var keyFromInput = context.key;
        var valueFromInput = context.value;

        var recordObj = JSON.parse(valueFromInput);
        try {
            var recObj = record.load({
                type: record.Type.FOLDER,
                id: recordObj.transId
            });
            recObj.setValue({ fieldId: 'subsidiary', value: ''});
            if(recObj.save()){
                log.debug({
                    title: "success",
                    details: "Successfully saved " + recordObj.transId
                })
            }
    
        } catch (errorObj) {
            log.debug({
                title: 'error 1',
                details: errorObj
            });
        }
        // pass data to the next stage
    }

    function reduce(context) {
    
    }

    function summarize(summary) {
        email.send({
            author: 2495012,
            recipients: '<EMAIL>',
            subject: 'ACS MR | Update Folder Subsidiary done running',
            body: 'ACS MR | Update Folder Subsidiary done running'
        });
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
