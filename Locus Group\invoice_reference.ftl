<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

        <#if .locale=="ru_RU">
            <link name="verdana" type="font" subtype="opentype" src="${nsfont.verdana}"
                src-bold="${nsfont.verdana_bold}" bytes="2" />
        </#if>
        <link name="hebrew" type="font" subtype="opentype" src="${nsfont.NotoSansHebrew_Regular}"
            src-bold="${nsfont.NotoSansHebrew_Bold}" bytes="2" />
        <!-- <link name="hebrew" type="font" subtype="opentype" src="https://5171742.app.netsuite.com/core/media/media.nl?id=3623&amp;c=5171742&amp;h=53068ea2ee68e1a99342&amp;_xt=.ttf ;"
                  src-bold="https://5171742.app.netsuite.com/core/media/media.nl?id=3650&amp;c=5171742&amp;h=d434d61aa26837c9c94a&amp;_xt=.ttf;" bytes="2" />  -->
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: stsong, sans-serif;
                <#elseif .locale=="zh_TW">font-family: msung, sans-serif;
                <#elseif .locale=="ja_JP">font-family: heiseimin, sans-serif;
                <#elseif .locale=="ko_KR">font-family: hygothic, sans-serif;
                <#elseif .locale=="ru_RU">font-family: verdana;
                <#else>font-family: hebrew, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
                border-collapse: collapse;
                border-spacing: 0;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                align: center;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
                /*color: #24b00e;   Color for the header*/
            }

            td {
                padding: 4px 6px;
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0px;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            hr {
                width: 100%;
                color: #c9c7c7;
                background-color: #c9c7c7;
                height: 1px;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            table.alt-lines tbody tr.odd,
            table.alt-lines tbody tr.odd td,
            table.alt-lines tbody tr td {
                background-color: #ededed;
            }

            table.alt-lines tbody tr.even,
            table.alt-lines tbody tr.even td,
            table.alt-lines tbody tr:nth-of-type(even) td {
                background-color: #f8f8f8;
            }

            #items_table td {
                border: 0.2px;
            }

            .red-font {
                color: #ff0000;
            }

            .comp-font {
                color: #183ead;
                font-size: 18px;
            }

            #invoice {
                padding: 10px;
                margin: 10px;
                font-size: 15px;
            }
        </style>

        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width:100%;">
                    <tr>
                        <td style="width:50%">
                            <table align="left" border="0" cellpadding="1" cellspacing="1" style="width:300px;">
                                <tr>
                                    <td colspan="5" style="text-align: left; display: block;">
                                        <div style="width:100%;text-align:left;">
                                            <#if subsidiary.logo@Url?length !=0>
                                                <img src="${subsidiary.logo@Url}" />
                                            </#if>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td style="width:30%">
                            <table align="center" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td class="comp-font" style="text-align: left;">${subsidiary.addressee}</td>
                                </tr>
                                <tr>
                                    <td height="5"></td>
                                </tr>
                                <tr>
                                    <td height="5">${subsidiary.addr1}</td>
                                </tr>
                                <tr>
                                    <td height="5">${subsidiary.addr2}</td>
                                </tr>
                                <tr>
                                    <td height="5">${subsidiary.city} ${subsidiary.state} ${subsidiary.zip}</td>
                                </tr>
                                <tr>
                                    <td height="5"> </td>
                                </tr>
                                <tr>
                                    <td>${subsidiary.country}</td>
                                </tr>
                                <tr>
                                    <td height="5"> </td>
                                </tr>
                                <tr>
                                    <td style="font-size:11px;">Tax ID: ${subsidiary.federalidnumber}</td>
                                </tr>
                                <tr></tr>
                            </table>
                        </td>

                    </tr>
                    <tr>
                        <td>
                            <hr />
                        </td>
                        <td>
                            <hr />
                        </td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <hr />
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td style="font-size:11px;align: left;width:30%;">E-Mail: ${subsidiary.email}</td>
                        <td style="font-size:11px;align: center;width:40%;">
                            <#if subsidiary.phone?has_content> Tel.:&nbsp;${subsidiary.phone}&nbsp;</#if>
                            <#if subsidiary.fax?has_content>FAX:&nbsp;${subsidiary.fax}</#if>
                        </td>
                        <td style="font-size:11px;align: right;width:30%;">${record.subsidiary.url}&nbsp;&nbsp;
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
    </head>

    <body header="nlheader" header-height="110px" footer="nlfooter" footer-height="20pt"
        padding="0.5in 0.5in 0.5in 0.5in" size="Letter">

        <br />
        <table style="width: 100%;">
            <tr>
                <td style="width: 73%;vertical-align: top;">
                    <table>
                        <tr>
                            <td>
                                <table style="width: 100%;" cellpadding="0" cellspacing="2">
                                    <tr>
                                        <td><b>${record.billaddress@label}:</b></td>
                                    </tr>
                                    <tr>
                                        <td style="line-height:12">${record.billaddress}</td>
                                    </tr>
                                    <#if record.vatregnum?has_content && (subsidiary.country!='Israel' ||
                                        record.billcountry=='IL' )>
                                        <tr>
                                            <td>${record.vatregnum}</td>
                                        </tr>
                                    </#if>
                                </table>
                            </td>
                            <#if record.shipaddress?has_content && record.billaddress!=record.shipaddress>
                                <td>

                                    <table style="width: 100%;" cellpadding="0" cellspacing="2">
                                        <tr>
                                            <td><b>${record.shipaddress@label}:</b></td>
                                        </tr>
                                        <tr>
                                            <td style="line-height:12">${record.shipaddress}</td>
                                        </tr>
                                    </table>
                                </td>
                            </#if>

                        </tr>
                    </table>
                </td>
                <td style="width: 27%;">
                    <table style="width:100%;">
                        <#if record.trandate?has_content>
                            <tr>
                                <td><b>${record.trandate@label}:</b>
                                    ${record.trandate?string("dd-MMM-yyyy")}</td>
                            </tr>
                        </#if>
                        <#if record.terms?has_content>
                            <tr>
                                <td><b>${record.terms@label}:</b> ${record.terms}</td>
                            </tr>
                        </#if>
                        <#if record.duedate?has_content>
                            <tr>
                                <td><b>${record.duedate@label}:</b> ${record.duedate?string("dd-MMM-yyyy")}
                                </td>
                            </tr>
                        </#if>
                        <tr>
                            <td><b>${record.account@label} #:</b> ${record.entity.entityid}</td>
                        </tr>
                        <#if record.invoicegroupnumber?has_content>
                            <tr>
                                <td><b>${record.invoicegroupnumber@label}:</b>&nbsp;${record.invoicegroupnumber}</td>
                            </tr>
                        </#if>
                        <#if record.memo?has_content>
                            <tr>
                                <td><b>Memo:</b>&nbsp;${record.memo}</td>
                            </tr>
                        </#if>
                    </table>
                </td>
            </tr>
        </table>

        <#assign Print=''>
            <#if record.subsidiary.custrecord_ac_prnt_orgnl>
                <#if record.custbodyoriginal_print_counter==0>
                    <#assign Print=' Draft'>
                </#if>
                <#if record.custbodyoriginal_print_counter==1>
                    <#assign Print=' Original'>
                </#if>
                <#if record.custbodyoriginal_print_counter \gte2>
                    <#assign Print=' Copy'>
                </#if>
            </#if>

            <#assign Tax='Tax'>
                <#if subsidiary.country=='Israel' && record.entity.vatregnumber==subsidiary.federalidnumber>
                    <#assign Tax='Self'>
                </#if>
                <#if subsidiary.country=='Japan'>
                    <#assign Tax=''>
                </#if>
                <#if subsidiary.country.internalid=='CN'>
                    <#assign Tax=''>
                </#if>

                <div style="width:100%;">
                    <p id="invoice" align="center">
                        <#if subsidiary.country=='Israel'>${Tax} ${record@title} ${record.tranid}${Print}<#else>
                                ${record@title} ${record.tranid}${Print}</#if>
                    </p>
                </div>

                <#list record.item as item>
                    <!-- start items -->
                    <table id="itemtable" style="width: 100%; margin-top: 10px;">
                        <!--<table id="items_table" class="table alt-lines" colspan="100" style="width:100%; nth-child(even);"> -->
                        <#if item_index==0>
                            <thead>
                                <tr>
                                    <th colspan="4">#</th>
                                    <!-- <th   colspan="12">${item.item@label}</th> -->
                                    <th colspan="44" align="left">${item.item@label}
                                        <!--${item.description@label}-->
                                    </th>
                                    <th colspan="10">${item.quantity@label}</th>
                                    <th colspan="14">${item.rate@label}</th>
                                    <!--	<th border="0.2" colspan="16">${item.amount@label} ${record.currency}</th>  -->
                                    <th colspan="16" align="right">${item.amount@label} ${record.currency}</th>
                                </tr>
                            </thead>
                        </#if>
                        <tbody>
                            <tr class="${['odd', 'even'][item_index%2]}">
                                <td colspan="4">${item_index+1}</td>
                                <!--<td colspan="12" align="left">${item.item}</td> -->
                                <td colspan="44" align="left"><b>${item.item}</b> <br />${item.Description}</td>
                                <#if item.itemtype!="Description">
                                    <td colspan="10" align="center">
                                        <#if item.itemtype!="Subtotal">
                                            <#if subsidiary.country.internalid=='US' &&
                                                item.custcol_lv_qnt_print?has_content>
                                                ${item.custcol_lv_qnt_print?string("#,##0.##")}<#else>${item.quantity}
                                            </#if>
                                        </#if>
                                    </td>
                                    <td colspan="14" align="right">
                                        <#if item.itemtype!="Subtotal">
                                            <#if subsidiary.country.internalid=='US' &&
                                                item.custcol_lv_qnt_print?has_content>
                                                ${item.custcol_list_rate?string("#,##0.00")}<#else>
                                                    ${item.rate?string("#,##0.00")}</#if>
                                        </#if>
                                    </td>
                                    <td colspan="16" align="right">${(item.amount)?string("#,##0.00")}</td>
                                    <#else>
                                        <td colspan="40">&nbsp;</td>
                                </#if>
                            </tr>
                        </tbody>
                    </table>
                </#list><!-- end items -->


                <#if record.custbodysecondary_book?has_content && subsidiary.country.internalid==record.billcountry>
                    <#if record.accountingbookdetail?has_content>
                        <#list record.accountingbookdetail as bookdetail>
                            <#if bookdetail.accountingbook==record.custbodysecondary_book && record.currency
                                !=bookdetail.currency>
                                <#assign ExRate=(bookdetail.exchangerate)>
                                    <#assign Cur=(bookdetail.currency)>
                            </#if>
                        </#list>
                        <#elseif record.currency !=record.subsidiary.currency>
                            <#assign ExRate=(record.exchangerate)>
                                <#assign Cur=(record.subsidiary.currency)>
                    </#if>
                </#if>

                <!-- <table width="100%" style="margin-top:15px" cellpadding="0" cellspacing="0"> -->
                <table class="total" style="width: 100%; margin-top: 10px;">
                    <tr>
                        <td width="62%">
                            <#if Cur?has_content>
                                <table width="88%">
                                    <tr>
                                        <td colspan="5">${Cur} ${record.exchangerate@label}
                                            ${(ExRate)?string("#,##0.0000")}</td>
                                        <td colspan="3" border="0.2">${Cur} ${record.subtotal@label}</td>
                                        <td colspan="3" border="0.2" align="right" style="text-align: right;">
                                            ${(ExRate*record.subtotal)?string("#,##0.00")}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="5">&nbsp;</td>
                                        <td colspan="3" border="0.2">${Cur} ${record.taxtotal@label}</td>
                                        <td colspan="3" border="0.2" align="right">
                                            ${(ExRate*record.taxtotal)?string("#,##0.00")}</td>
                                    </tr>
                                    <tr class="totalrow">
                                        <td colspan="5">&nbsp;</td>
                                        <td colspan="3" border="0.2">${Cur} ${record.total@label}</td>
                                        <td colspan="3" border="0.2" align="right">
                                            ${(ExRate*record.total)?string("#,##0.00")}</td>
                                    </tr>
                                </table>
                                <#else>&nbsp;
                            </#if>
                        </td>
                        <td align="right" width="38%">
                            <table width="92%">
                                <tr>
                                    <td width="48%" align="left"><b>${record.subtotal@label}</b></td>
                                    <td width="52%" align="right">${record.subtotal?string("#,##0.00")}</td>
                                </tr>
                                <tr>
                                    <td width="48%" align="left"><b>
                                            <#if subsidiary.country.internalid !='US'>${record.taxtotal@label}<#else>
                                                    Sales Tax</#if>
                                            (${(record.taxtotal/record.subtotal*100)?string("##.##")}%)
                                        </b></td>
                                    <td width="52%" align="right">${record.taxtotal?string("#,##0.00")}</td>
                                </tr>

                                <tr class="totalrow">
                                    <td width="48%" align="left"><b>${record.total@label}</b></td>
                                    <td width="52%" align="right">${record.currency} ${record.total?string("#,##0.00")}
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>

                <div width="75%" style="padding-top:15px">
                    <#if record.message?has_content>
                        <table align="left" border="0" cellpadding="1" cellspacing="1" style="padding-top:15pt">
                            <tr>
                                <td height="15"><b>${record.message@label}</b></td>
                            </tr>
                            <tr>
                                <td>${record.message}</td>
                            </tr>
                        </table>
                    </#if>
                </div>

                <div style="padding-top:12pt">
                    <#if record.custbody_ac_disclaimer?has_content>
                        <table width="100%" align="left" cellpadding="1" cellspacing="1">
                            <tr>
                                <td>${record.custbody_ac_disclaimer.custrecorddisclaimer}</td>
                            </tr>
                        </table>
                    </#if>
                </div>

    </body>
</pdf>