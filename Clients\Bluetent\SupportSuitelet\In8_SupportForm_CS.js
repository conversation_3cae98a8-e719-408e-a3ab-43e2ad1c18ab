function modifiedField(type, name)
{
    if(name != "custpage_touchedfields" &&
        !name.includes('custpage_filter') &&
        !name.includes('custpage_page') &&
        !nlapiGetFieldValue(name).includes('opensearch'))
    {
        var fields = JSON.parse(nlapiGetFieldValue('custpage_touchedfields'));

        if(fields)
        {
            var field = {};
            field.name = name;
            field.value = nlapiGetFieldValue(name);

            fields.push(field);

            if(name == "custpage_incomingmessage")
            {
                var field = {};
                field.name = "custpage_messagenew";
                field.value = nlapiGetFieldValue(name) ? "T" : "F";

                fields.push(field);
            }

            nlapiSetFieldValue('custpage_touchedfields', JSON.stringify(fields));
        }
    }

    if(name == "custpage_custevent_in8_customformsuitelet")
    {
        var currentUrl = window.location.href;

        var newCustomForm = nlapiGetFieldValue(name);

        var newUrl = currentUrl.replace(/(customform=).*?(&)/,'$1' + newCustomForm + '$2');

        window.location.href = newUrl;
    }

    if(name.includes('custpage_filter'))
    {
        var filters = JSON.parse(nlapiGetFieldValue("custpage_filters"));

        filters[name] = nlapiGetFieldValue(name);

        nlapiSetFieldValue("custpage_filters", JSON.stringify(filters));
    }

    if(name.includes('custpage_page'))
    {
        var sublistName = name.replace('_page_', '_');

        var pageNumber = "page="+nlapiGetFieldValue(name);

        startLoading();

        refreshmachine(sublistName, pageNumber);

        //setTimeout(endLoading(), 5000);
    }

    if(nlapiGetFieldValue(name).includes('opensearch'))
    {
        var formUrl = nlapiResolveURL('SUITELET',
            'customscript_in8_cef_lookupfield_sl',
            'customdeploy_in8_cef_lookupfield_sl',
            parseInt(nlapiGetContext().getUser()) < 0);

        var joinField = getJoinField(name);

        formUrl = formUrl+'&'+nlapiGetFieldValue(name)+'&name='+name

        if(joinField[0] && joinField[1])
            formUrl = formUrl+'&joinField='+joinField[0]+'&joinValue='+joinField[1];

        nlExtOpenWindow(formUrl+'&'+nlapiGetFieldValue(name)+'&name='+name, 'Select value', 300, 400);

        nlapiSetFieldValue(name, '');
    }

}

function editRecord()
{
    window.location = window.location.href+'&accesslevel=2';
}

function searchCustomer(sublist)
{
    startLoading();

    refreshmachine(sublist, 'filters='+JSON.stringify(nlapiGetFieldValue('custpage_filters')));
}

function init_page()
{
    for(var index in document.getElementsByClassName('formtabtext unrollformtabheaderexpand'))
    {
        var tab = document.getElementsByClassName('formtabtext unrollformtabheaderexpand')[parseInt(index)];

        if(tab != null)
        {
            tab.addEventListener('click', function (event) {
                var divId = this.id;
                divId = divId.replace('_pane_hd', '_div');

                showTab(divId);
            });

            tab.click();

            tab.style.border = '3px';
            tab.style.borderColor = 'white';
            tab.style.borderStyle = 'solid';
        }

    }

    for(var index in document.getElementsByClassName('nltabcontent'))
    {
        var tab = document.getElementsByClassName('nltabcontent')[parseInt(index)];

        if(tab != null)
        {
            tab.addEventListener('DOMSubtreeModified', function (event) {
                endLoading();
            });
        }

    }

    var companyFilterInput = document.getElementById("custpage_filtercustomersearchcompanyname");

    if(companyFilterInput)
    {
        companyFilterInput.addEventListener("keyup", function(event) {

            if (event.keyCode === 13) {
                event.preventDefault();
                searchCustomer();

            }
        });
    }

    if(nlapiGetFieldValue('custpage_defaultopened'))
    {
        var defaultOpened = JSON.parse(nlapiGetFieldValue('custpage_defaultopened'));

        for(var i = 0; i < defaultOpened.length; i++)
        {
            showTab(defaultOpened+'_div');
        }
    }

    var lists = document.getElementsByClassName('listtable');

    for(var l = 0; lists && l < lists.length; l++)
    {
        var sublistName = String(lists[l].id).replace('_splits', '');

        for(var i = 1; i <= nlapiGetLineItemCount(sublistName); i++)
        {
            if(nlapiGetLineItemValue(sublistName, 'custpage_linecolor'), i)
            {
                var color = nlapiGetLineItemValue(sublistName, 'custpage_linecolor', i);
                var row = document.getElementById(sublistName+'row'+(i-1));
                var cells = row.getElementsByTagName("td");
                for(var c = 0; c < cells.length; c++)
                {
                    cells[c].setAttribute('style','background-color: '+color+' !important');
                }
            }
        }
    }

    if(!nlapiGetFieldValue('custpage_customform'))
    {
        localStorage.setItem('nsHistory','');
    }

    if(parseInt(nlapiGetFieldValue('custpage_sourceid')) > 0)
    {
        if (!localStorage.getItem('nsHistory'))
        {
            localStorage.setItem('nsHistory',JSON.stringify([{
                customform: nlapiGetFieldValue('custpage_sourceform'),
                recordid: nlapiGetFieldValue('custpage_sourceid')
            }]));

        }
        else
        {
            var nsHistory = JSON.parse(localStorage.getItem('nsHistory'));

            if(nsHistory[nsHistory.length-1].recordid != nlapiGetFieldValue('custpage_sourceid'))
            {
                nsHistory.push({
                    customform: nlapiGetFieldValue('custpage_sourceform'),
                    recordid: nlapiGetFieldValue('custpage_sourceid')
                });

                localStorage.setItem('nsHistory', JSON.stringify(nsHistory));
            }
        }
    }

    if(document.getElementById('custpage_companynamefilter_fs_lbl'))
    {
        var element = document.getElementById('custpage_companynamefilter_fs_lbl').parentNode;

        element.style.width = "100px";
    }
}

function showTab(name) {

    var x = document.getElementById(name);

    if (x.style.display === "none") {
        x.style.display = "block";
    } else {
        x.style.display = "none";
    }
}

function createRecord(url)
{
    window.location = url;
}

function gotoMainRecord()
{
    var formUrl = nlapiResolveURL('SUITELET',
        'customscript_in8_supportform_sl',
        'customdeploy_in8_supportform_sl',
        parseInt(nlapiGetContext().getUser()) < 0);

    var nsHistory = JSON.parse(localStorage.getItem('nsHistory'));

    for(var i = nsHistory.length-1; i >= 0; i--)
    {
        if(nsHistory[i].recordid != nlapiGetFieldValue('custpage_recordid'))
        {
            var history = nsHistory[i];
            break;
        }
    }

    if(history)
    {
        formUrl += '&customform=' + history.customform
            + '&internalid=' + nlapiGetFieldValue('custpage_employee')
            + '&hash=' + nlapiGetFieldValue('custpage_hash')
            + '&contactid=' + nlapiGetFieldValue('custpage_contactid')
            + '&recordid=' + history.recordid;

        localStorage.setItem('nsHistory', JSON.stringify(nsHistory.slice(0, nsHistory.length)));

        window.location.href = formUrl;
    }
}

function gotoDashboard()
{
    var formUrl = nlapiResolveURL('SUITELET',
        'customscript_in8_supportform_sl',
        'customdeploy_in8_supportform_sl',
        parseInt(nlapiGetContext().getUser()) < 0);


    formUrl += '&customform=1&dash=T'
        + '&internalid=' + nlapiGetFieldValue('custpage_employee')
        + '&hash=' + nlapiGetFieldValue('custpage_hash')
        + '&contactid=' + nlapiGetFieldValue('custpage_contactid')
        + '&recordid=' + nlapiGetFieldValue('custpage_employee');

    window.location.href = formUrl;

}

function resolveCase()
{
    var formUrl = nlapiResolveURL('SUITELET',
        'customscript_in8_supportresolve_sl',
        'customdeploy_in8_supportresolve_sl',
        parseInt(nlapiGetContext().getUser()) < 0);


    formUrl += '&caseid=' + nlapiGetFieldValue('custpage_recordid');

    var response = nlapiRequestURL(formUrl);

    if(response.getCode() == "200")
    {
        var obj = JSON.parse(response.getBody());

        alert(obj.message);

        if(obj.status == "true")
            location.reload();
    }

}

function newMessageForm()
{
    var formUrl = nlapiResolveURL('SUITELET',
        'customscript_in8_csf_casemessage_sl',
        'customdeploy_in8_csf_casemessage_sl',
        parseInt(nlapiGetContext().getUser()) < 0);

    formUrl = formUrl+'&caseid='+nlapiGetFieldValue('custpage_recordid');

    nlExtOpenWindow(formUrl+'&'+nlapiGetFieldValue(name)+'&name='+name, 'Send message', 500, 700);

}


function filterName() {
    var name = window.document.getElementById('filterItemName');
    
    var url = window.location.href;

    url = replaceAll(url, 'filterItemName', 'filterItemOld');

    window.location = url +'&accesslevel=2' + '&filterItemName=' + name.value;
}

function replaceAll(string, token, newtoken) {
	while (string.indexOf(token) != -1) {
 		string = string.replace(token, newtoken);
	}
	return string;
}

function setLookupField(name, id, value)
{
    nlapiInsertSelectOption(name, id, value, true);

    nlapiSetFieldValue(name, id);

    jQuery('.x-tool-close').click();
}

function getJoinField(fieldId)
{
    var field = fieldId.replace('custpage_', '');
    var joinField = '';

    switch (field)
    {
        case 'custevent_project_name':
            joinField = 'company';
            break;

        case 'custevent_project_task':
            joinField = 'custevent_project_name';
            break;
    }

    return [joinField, nlapiGetFieldValue('custpage_'+joinField)];
}

function viewFile(fileId)
{

    var formUrl = nlapiResolveURL('SUITELET',
        'customscript_in8_cef_file_sl',
        'customdeploy_in8_cef_file_sl',
        parseInt(nlapiGetContext().getUser()) < 0);

    var joinField = getJoinField(name);

    formUrl = formUrl+'&fileid='+fileId;

    nlExtOpenWindow(formUrl+'&'+nlapiGetFieldValue(name)+'&name='+name, 'Attachment', 1000, 800);

}

function startLoading(){


    function showLoader(){

        var windowHeight = jQuery(document).height();
        var windowWidth = jQuery(window).width();

        jQuery('#mask').css({'width':windowWidth,'height':windowHeight, 'opacity':0.5});//windowHeight
        jQuery('#mask').fadeIn(500);
        jQuery('#mask').fadeTo("slow", 0.5);

        var width = window.innerWidth;
        var left = (jQuery(window).width() - width ) / 2 + jQuery(window).scrollLeft() ;
        var top = jQuery(window).height()/4 + jQuery(window).scrollTop()  /*- jQuery("#dialog").height() ) / 2 + jQuery(window).scrollTop()*/ ;

        jQuery(".window").css({'top':top,'left':left,'width':width});
        jQuery(".window").show();

    }

    var body = document.getElementsByTagName('body')[0];
    var head = document.getElementsByTagName('head')[0];

    var div = document.createElement('div');
    div.id = 'dialog';
    div.className = 'window';
    div.innerHTML = '<html><style>#loader {  position: absolute;  left: 50%;  top: 50%;  z-index: 1;  width: 150px;  height: 150px;  margin: -75px 0 0 -75px;  border: 16px solid #f3f3f3;  border-radius: 50%;  border-top: 16px solid #3498db;  width: 120px;  height: 120px;  -webkit-animation: spin 2s linear infinite;  animation: spin 2slinear infinite;  }@-webkit-keyframes spin {  0% { -webkit-transform: rotate(0deg); }  100% { -webkit-transform: rotate(360deg); }}@keyframes spin {  0%{transform: rotate(0deg); }  100% { transform: rotate(360deg); }}/* Add animation to "page content" */.animate-bottom {  position: relative; -webkit-animation-name: animatebottom;  -webkit-animation-duration: 1s;  animation-name: animatebottom;  animation-duration: 1s}@-webkit-keyframes animatebottom {  from { bottom:-100px; opacity:0 }   to { bottom:0px; opacity:1 }}@keyframes animatebottom {   from{ bottom:-100px; opacity:0 }   to{ bottom:0; opacity:1 }}#myDiv {  display: none;  text-align: center;}</style><div id="loader"></div></html>';
    body.appendChild(div);

    var style = document.createElement('style');

    //var winWidth = window.innerWidth*1;
    winHeight = 400;

    style.innerHTML = '.window{ font-size:small;font-family:Arial,Helvetica,sans-serif;display:none; width:' + 400 + 'px; height:' + winHeight + 'px; position:absolute; left:0; top:0; background:#FFF; z-index:9900; padding:10px; border-radius:10px; } '
        + '#mask{ position:absolute; left:0; top:0; z-index:9000; background-color:#EBEBEB; display:none; opacity:0.5;filter:alpha(opacity=50); } .fechar{display:block; text-align:right;} ';
    head.appendChild(style);

    div = document.createElement('div');
    div.id = 'mask';
    //div.onclick = endLoading;
    body.appendChild(div);

    showLoader();


}

function endLoading()
{
    var obj = window.document.getElementById("dialog");

    if(obj)
        obj.parentNode.removeChild(obj);

    jQuery("#mask").hide();
    jQuery(".window").hide();
}

function newFileForm()
{
    var formUrl = nlapiResolveURL('SUITELET',
        'customscript_in8_fileattachment_sl',
        'customdeploy_in8_fileattachment_sl',
        parseInt(nlapiGetContext().getUser()) < 0);

    formUrl = formUrl+'&caseid='+nlapiGetFieldValue('custpage_recordid');

    nlExtOpenWindow(formUrl+'&'+nlapiGetFieldValue(name)+'&name='+name, 'Send message', 500, 700);

}