/**
 * @NApiVersion 2.x
 * @NScriptType Suitelet
 * @NModuleScope SameAccount
 */
 define(['N/ui/serverWidget','N/runtime','N/sftp'],
 ​
 function(serverWidget,runtime,sftp) {
    
     /**
      * Definition of the Suitelet script trigger point.
      *
      * @param {Object} context
      * @param {ServerRequest} context.request - Encapsulation of the incoming request
      * @param {ServerResponse} context.response - Encapsulation of the Suitelet response
      * @Since 2015.2
      */
     function onRequest(context) {
         
         if(context.request.method == 'GET'){

            var password = runtime.getCurrentScript().getParameter('custscript_pwguid');
            var hostKey = runtime.getCurrentScript().getParameter('custscript_hostkey_2');

            log.debug('test', {test: password});
 ​
            var sftpConnection = sftp.createConnection({
                username: 'FNDTNCOM',
                passwordGuid: password, // references var myPwdGuid
                keyId: 'custkey_bbva_key',
                url: 'stage-gateway.bbvausa.com',
                port: Number(10022),
                hostKey: hostKey // references var myHostKey
            });

            log.debug('test', sftpConnection)
             
         }
         
     }
 ​
     return {
         onRequest: onRequest
     };
     
 });