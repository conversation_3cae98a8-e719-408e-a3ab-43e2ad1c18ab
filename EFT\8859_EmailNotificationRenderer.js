/**
 * Copyright (c) 2017, Oracle and/or its affiliates. All rights reserved.
 * otherwise make available this code.
 *
 * Version    Date            Author           Remarks
 * 1.00       05 Dec 2016     aalcabasa
 * 
 * @NModuleScope Public
 */
 define(['../lib/wrapper/9997_NsWrapperTemplateRenderer', '../lib/wrapper/9997_NsWrapperRecord', '../lib/wrapper/9997_NsWrapperFile'],

 function(renderer, record, file) {
     var subsidiaryRecord;
     var templateContent;
 
     function render(notification) {
         try {
             if(!templateContent){
                 var templateId = notification.template;
                 templateContent = file.load(templateId).getContents();
             }
         } catch (fileLoadEx) {
             log.error('EmailNotificationRenderer:loadTemplate', JSON.stringify(fileLoadEx));
             fileLoadEx['details']  = { templateId: notification.template };
             fileLoadEx['code'] = 'TEMPLATE_FILE_LOAD_ERROR'; // TODO: Replace this with EP ERROR CODE
             throw fileLoadEx;
         }
 
         try {
             renderer.setTemplateContents(templateContent);
 
             var paymentRecord = record.load({
                 id: notification.id,
                 type: notification.recordType
             });
 
             renderer.addRecord('record', paymentRecord.getRecord());
 
             if (notification.recordType === record.Type.CUSTOMER_PAYMENT) {
                 var entityRecord = record.load({
                     type: record.Type.CUSTOMER,
                     id: notification.entityId
                 });
                 renderer.addRecord('entity', entityRecord.getRecord());
             }
 
             if (notification.subsidiaryId) {
                  if(!subsidiaryRecord){
                      subsidiaryRecord = record.load({
                          type: record.Type.SUBSIDIARY,
                          id: notification.subsidiaryId
                      });
                  }
                  renderer.addRecord('subsidiary', subsidiaryRecord.getRecord());
             }
 
             notification.email.body = renderer.renderAsString();
         } catch (renderEx) {
             renderEx['details'] = { templateId: notification.template, paymentId: notification.email.relatedRecords.transactionId};
             renderEx['code']    = 'TEMPLATE_RENDER_ERROR';
             throw renderEx;
         }
 
         return notification.email;
     }
 
     /**
      * This is used only for testing
      */
     function unsetTemplateContent() {
         templateContent = undefined;
     }
 
 
     return {
         render: render,
         // This is used for unit testing only
         unsetTemplateContent : unsetTemplateContent
     };
 
 });