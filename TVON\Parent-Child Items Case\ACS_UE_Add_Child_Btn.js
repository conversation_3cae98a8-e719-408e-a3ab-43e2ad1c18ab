/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/ui/serverWidget'], function(record, serverWidget) {
    function isEmpty (stValue) {
        return ((stValue === '' || stValue == null || stValue == undefined) || (stValue.constructor === Array && stValue.length == 0) || (stValue.constructor === Object && (function (v) {
            for (var k in v)
                return false;
            return true;
        })(stValue)));
    };
    function beforeLoad(context) {
        var recObj = context.newRecord;
        var params = ((!isEmpty(context.request)) ? context.request.parameters : "" );
        
        if(params.parentRecord || context.type == context.UserEventType.EDIT) {
            var formObj = context.form;
            var parentItemField = formObj.getField({
                id: 'parent'
            });
            parentItemField.defaultValue = params.parentRecord;
            parentItemField.updateDisplayType({
                displayType: serverWidget.FieldDisplayType.INLINE
            });

        } else if(context.type == context.UserEventType.VIEW) {
        
            var getForm = context.form;
            getForm.clientScriptModulePath = 'SuiteScripts/ACS_CS_Add_Child.js';
            getForm.addButton({
                id: 'custpage_create_child',
                label: 'Create Subitem',
                functionName: 'createChild()'
            });
        }
    }

    function beforeSubmit(context) {
        
    }

    function afterSubmit(context) {
        // if(context.type == context.UserEventType.EDIT) {
        //     var newRecObj = context.newRecord;
        //     var parentId = newRecObj.getValue({ fieldId: 'parent' });
        //     if(parentId){
        //         record.submitFields({
        //             type: record.Type.INVENTORY_ITEM,
        //             id: parentId,
        //             values: {
        //                 custitem_is_parent: true
        //             },
        //             options: {
        //                 enableSourcing: false,
        //                 ignoreMandatoryFields : true
        //             }
        //         });
        //     }
        // }
    }

    return {
        beforeLoad: beforeLoad,
        beforeSubmit: beforeSubmit,
        afterSubmit: afterSubmit
    }
});
