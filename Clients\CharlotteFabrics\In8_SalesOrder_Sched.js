/**
 * Scheduled Script that syncs Orders to WooCommerce
 * 
 * Version    Date            Author           Remarks
 * 1.00       17 Nov 2017     Marcel P		   Initial Version
 *
 */

/**
 * @param {String} type Context Types: scheduled, ondemand, userinterface, aborted, skipped
 * @returns {Void}
 */
function scheduled(type) {

	try {		
		// Execute the Saved Search
		var filters = [],
		    columns = [],
		    searchResults,
		    i = 0;
	
		columns[i++] = new nlobjSearchColumn('internalid');
		   
		searchResults = nlapiSearchRecord(null, 'customsearch_in8_salesorder_sync_wc', filters, columns);       
	
		if (searchResults && searchResults.length) {
		    for (i = 0; i < searchResults.length; i++) {
		        var internalId = searchResults[i].getValue('internalid');
		        
		        // Add SO to queue
		        if (!nlapiLookupField('customer', searchResults[i].getValue('internalid', 'customer'), 'custentity_in8_wc_customer_id')) {
					In8Lib.addToQueue('customer', 'customer', searchResults[i].getValue('internalid', 'customer'), 'create');					
				}				
			
				// Update Sales Order Status
				In8Lib.addToQueue('salesorder', 'salesorder', internalId, 'create');	
				
				nlapiLogExecution('DEBUG', 'Sync', 'Order added to queue. Id: ' + internalId);
		    }
		}                
    } catch(e) {
        nlapiLogExecution('ERROR', 'Error', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
    }
}
