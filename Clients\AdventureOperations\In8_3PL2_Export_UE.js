/**
 * @copyright In8Sync, LLC.
 * @NApiVersion 2.x
 * @NScriptType UserEventScript
 * @NModuleScope Public
 */
var scriptName = "In8 3PL2 Export UE";

define(["N/https", "N/record", "N/runtime", "N/search", "N/format", "../operations/helper", "../operations/exportFieldMappings"],
/**
 * @param {https} https
 * @param {record} record
 * @param {runtime} runtime
 * @param {search} search
 */
function(https, record, runtime, search, format, helper, exportFieldMappings) {
    /**
     * Function definition to be triggered before record is loaded.
     *
     * @param {Object} scriptContext
     * @param {Record} scriptContext.newRecord - New record
     * @param {Record} scriptContext.oldRecord - Old record
     * @param {string} scriptContext.type - Trigger type
     * @Since 2015.2
     */
    function afterSubmit(scriptContext) {
    	var logTitle = "(main) ";
    	var accountId = runtime.accountId;
    	
    	log.audit(logTitle + "Account ID", accountId);
    	log.audit(logTitle + "Context Type", scriptContext.type);

    	if (scriptContext.type != scriptContext.UserEventType.CREATE && 
    			scriptContext.type != scriptContext.UserEventType.EDIT && 
    			scriptContext.type != scriptContext.UserEventType.XEDIT &&
    			scriptContext.type != scriptContext.UserEventType.APPROVE && 
    			scriptContext.type != scriptContext.UserEventType.SHIP) {
    		return;
    	}
    	
    	var newRec = scriptContext.newRecord;
//		var oldRec = context.oldRecord;
    	
    	var recordType = newRec.type;
    	var recordId = newRec.id
    	
    	log.audit(logTitle + "Record Type and ID", recordType + " : " + newRec.id);
    	log.audit(logTitle + ">> EXPORT FLOW <<", "Starting...");
    	
    	var recordObj = record.load({
			type: recordType,
			id: recordId
		});
		
		// Exit the script based on the 2 checkbox fields ("Do Not Export To 3PL", and "Exported To 3PL")
		var dontExportTo3PL = recordObj.getValue({
			fieldId: "custbody_in8_3pl2_dont_export"
		});
		
		var exportedTo3PL = recordObj.getValue({
			fieldId: "custbody_in8_3pl2_exported"
		});
		
		if (dontExportTo3PL) {
			log.audit(logTitle + "In8Sync 3PL > Do Not Export to 3PL field is checked", "Exiting the script...");
			
			return;
		} else {
			if (exportedTo3PL) {
				log.audit(logTitle + "NetSuite record (ID: " + recordId + " | Type: " + recordType + ") has already been exported successfully to 3PL", "Exiting the script...");
				
				return;
			}
		}
    	
    	// Get "3PL Settings"
		var currentScript = runtime.getCurrentScript();
		var settingsId = currentScript.getParameter("custscript_in8_3pl2_settings");
//		log.debug(logTitle + "settingsId", settingsId);
		
		if (settingsId) {
			log.audit(logTitle + "A valid settings id has been found.", "Getting the actual settings object...");
			
			var settingsObj = helper.getSettings(settingsId);
			
			if (settingsObj && Object.keys(settingsObj).length != 0) {
	    		log.audit(logTitle + "A valid settings object has been found.", "Continuing the script...");
	    	} else {
	    		var settingErrorDetails = "No '3PL Settings' has been assigned to '" + scriptName + "' script";
				
				log.audit(logTitle + settingErrorDetails, "Exiting the script...");
				
				return;
	    	}
		} else {
    		var settingsIdErrorDetails = "No '3PL Settings' has been assigned to '" + scriptName + "' script";
			
			log.audit(logTitle + settingsIdErrorDetails, "Exiting the script...");
			
			return;
		}
		
		// Check if Sandbox Testing setting is enabled
		var sandboxTesting = settingsObj.sandboxTesting;

		if (sandboxTesting) {
			log.audit(logTitle + "'3PL Settings > Sandbox Testing' is enabled", "Checking the Account ID...");

			//Do not run the script in non-Sandbox environments
			if (accountId.indexOf("SB") == -1) {
				log.audit(logTitle + "This is not a Sandbox environment", "Exiting the script...");

				return;
			}
		} else {
			log.audit(logTitle + "'3PL Settings > Sandbox Testing' is disabled", "Checking the Account ID...");

			//Do not run the script in Sandbox environments
			if (accountId.indexOf("SB") !== -1) {
				log.audit(logTitle + "This is a Sandbox environment", "Exiting the script...");

				return;
			}
		}
		
		// Location variables for Transfer Order
		var fromLocationId, toLocationId;
		
		// Export conditions specific ONLY to some client accounts
		if (accountId == "6737909") {
			// 6737909 == Vade Nutrition
			// Only export SO, PO, and TO with "IDS" location
			if (recordType == "transferorder") {
				fromLocationId = recordObj.getValue({
					fieldId: "location"
				});
				
				toLocationId = recordObj.getValue({
					fieldId: "transferlocation"
				});
				
				log.debug(logTitle + "From Location ID", fromLocationId);
				log.debug(logTitle + "To Location ID", toLocationId);
				
				// 6 == IDS
				if (fromLocationId != "6") { 
					if (toLocationId != "6") { 
						log.audit(logTitle + "From/To Location is not 'IDS'", "Exiting the script...");
						
						return;
					}
				}
			} else {
				var locationId = recordObj.getValue({
					fieldId: "location"
				});
				
				if (locationId != "6") { // 6 == IDS
					log.audit(logTitle + "Location is not 'IDS'", "Exiting the script...");
					
					return;
				}
			}
		} else if (accountId == "6739033" || accountId == "6739033_SB1" || accountId == "6739033_SB2") {
			// 6739033 == Adventure Operations
			// 6739033_SB1 == Adventure Operations (SB1)
			// 6739033_SB2 == Adventure Operations (SB2)
			
			var adventOpsSalesOrderRecId = recordObj.getValue({
				fieldId: "createdfrom"
			});
			
			var adventOpsSalesOrderRecObj = record.load({
			    type: record.Type.SALES_ORDER,
			    id: adventOpsSalesOrderRecId,
			    isDynamic: true,
			});
				
			var machShipLocationId = adventOpsSalesOrderRecObj.getValue({
				fieldId: "location"
			});
			
			var machShipLocationText = adventOpsSalesOrderRecObj.getText({
				fieldId: "location"
			});
			
			if (machShipLocationId != "1" && machShipLocationId != "2" && machShipLocationId != "3" && machShipLocationId != "22" && 
					machShipLocationId != "19") {
				// 1 == Eagle Farm DC
				// 2 == Tullamarine DC
				// 3 == Wangara DC1
				// 22 == Canning Vale DC
				// 19 == Tullamarine OD
				
				var errorDetails = "Sales Order's location is '" + machShipLocationText + "' which is currently unsupported.";
				
				helper.updateExportErrorDetails(errorDetails, recordType, recordId);
				
				return;
			} else {
				log.audit(logTitle + "Sales Order's location is '" + machShipLocationText + "'.", "Checking the Carrier field...");
				
				var carrierId = recordObj.getValue({
					fieldId: "custbody_shipcarrier"
				});
				
				if (carrierId != "1") {
					// 1 == Cora
					
					var errorDetails = "Carrier field is not 'Cora'.";
					
					helper.updateExportErrorDetails(errorDetails, recordType, recordId);
					
					return;
				} else {
					log.audit(logTitle + "Carrier field is 'Cora'.", "Continuing with the script...");
				}
			}
		}
		
		// Exit the script based on record status
		var recordStatusExit = helper.recordStatusCheck(recordType, recordObj, fromLocationId, toLocationId);
//		log.debug(logTitle + "recordStatusExit", recordStatusExit);
		if (recordStatusExit) {
			return;
		}
		
		// Get "3PL Export Mappings"
		var exportMappingsId = helper.getExportMappings(settingsId, recordType);
		
		var customParametersObj = {};
		
		if (accountId == "6739033" || accountId == "6739033_SB1" || accountId == "6739033_SB2") {
			// 6739033 == Adventure Operations
			// 6739033_SB1 == Adventure Operations (SB1)
			// 6739033_SB2 == Adventure Operations (SB2)
			
			// ONLY FOR TESTING
			// Employee (Name: In8Sync, ID: 667)
//			var employeeApiKey = "gnBMFpnhTkCrG4O1F1HBuw";
			
			// For Go-live
			var employeeApiKey = helper.getEmployeeApiKey(recordId, recordType);
			log.debug(logTitle + "employeeApiKey", employeeApiKey);
			
			if (!employeeApiKey) {
				return;
			}
			
			// Check the line items for any DG
			var hasDangerousGoods = helper.checkForDangerousGoods(recordObj);
			var cheapestCarrierObj;
			
			if (hasDangerousGoods) {
				log.audit(logTitle + "There is at least 1 Dangerous Good for this Item Fulfillment...", "Continuing with the Dangerous Goods flow...");
				
				// Get the cheapest carrier for DG items
				cheapestCarrierObj = helper.getCheapestCarrierForDg(settingsObj, "/routes/returnrouteswithcomplexitems", employeeApiKey, machShipLocationId, recordObj, recordType);
			} else {
				log.audit(logTitle + "There are no Dangerous Goods for this Item Fulfillment...", "Continuing with the NON-Dangerous Goods flow...");
				
				// Get the cheapest carrier for Non-DG items
				cheapestCarrierObj = helper.getCheapestCarrierForNonDg(settingsObj, "/routes/returnroutes", employeeApiKey, machShipLocationId, recordObj, recordType);
			}
			
			if (cheapestCarrierObj == "empty" || !cheapestCarrierObj) {
				var errorDetails = "MachShip returned an empty list of carriers based on the sent items, From Location, and To Location data.";
				
				helper.updateExportErrorDetails(errorDetails, recordType, recordId);
				
				return;
			} else if (!cheapestCarrierObj) {
				log.error(logTitle + "Error", "Exiting the script...");
				
				return;
			}
			
			customParametersObj.employeeApiKey = employeeApiKey;
			customParametersObj.cheapestCarrierObj = cheapestCarrierObj;
			customParametersObj.salesOrderNumber = adventOpsSalesOrderRecObj.getValue({fieldId: 'tranid'});
			customParametersObj.machShipLocationId = machShipLocationId;
			customParametersObj.hasDangerousGoods = hasDangerousGoods;
		}
		
		// Convert JSON payload based on record type
		log.audit(logTitle + "Convert JSON payload based on record type (" + recordType + ").", "Starting...");
		var convertedPayload = exportFieldMappings.convertPayload(settingsObj, recordType, recordObj, fromLocationId, toLocationId, accountId, customParametersObj);
		
		var apiExported = false;
		var responseStatus;
		var isSuccessful = false;
		var emailBody;
		
		
		// Call Export API based on record type
		var exportNetSuiteRecordResponse;
		
		customParametersObj.fromLocationId = fromLocationId;
		customParametersObj.toLocationId = toLocationId;
		
		if (convertedPayload) {
			var exportNetSuiteRecordResultObj = exportNetSuiteRecord(accountId, recordType, settingsObj, convertedPayload, exportNetSuiteRecordResponse, responseStatus, isSuccessful, emailBody, customParametersObj);
		} else {
			log.audit(logTitle + "No valid 'convertedPayload' has been found.", "Exiting the script...")
			
			return;
		}
		
		isSuccessful = exportNetSuiteRecordResultObj.isSuccessful;
		emailBody = exportNetSuiteRecordResultObj.emailBody;
		exportNetSuiteRecordResponse = JSON.parse(exportNetSuiteRecordResultObj.exportNetSuiteRecordResponse.body);
		
//		log.debug(logTitle + "emailBody", exportNetSuiteRecordResultObj.emailBody);
//		log.debug(logTitle + "isSuccessful", exportNetSuiteRecordResultObj.isSuccessful);
		
        if (isSuccessful) {
			// Successful response
        	log.audit(logTitle + "NetSuite record (ID: " + recordId + " | Type: " + recordType + ") has been successfully exported to 3PL", "Updating the NetSuite record...");
        	
        	apiExported = true;
        	
        	record.submitFields({
				type: recordType,
				id: recordId,
				values: {
					custbody_in8_3pl2_exported: true,
				}
			});
        	
        	if (accountId == "6739033" || accountId == "6739033_SB1" || accountId == "6739033_SB2") {
	  			// 6739033 == Adventure Operations
	  			// 6739033_SB1 == Adventure Operations (SB1)
	  			// 6739033_SB2 == Adventure Operations (SB2)
	  			
	  			// Update the MachShip fields in Item Fulfillment
        		
        		var consignmentObj = exportNetSuiteRecordResponse.object;
	  			
        		var despatchDateUtc = consignmentObj.despatchDateUtc + ".000Z";
        		despatchDateUtc = new Date(despatchDateUtc).toDateString() + " - " + consignmentObj.despatchDateUtc.split("T")[1];
        		
        		var deliveryDateUtc = consignmentObj.etaUtc + ".000Z";
        		deliveryDateUtc = new Date(deliveryDateUtc).toDateString() + " - " + consignmentObj.etaUtc.split("T")[1];
        		
	  			record.submitFields({
					type: recordType,
					id: recordId,
					values: {
						custbody_in8_3pl2_error_details: "",
//						custbody_in8_3pl2_consignment_number: consignmentObj.consignmentNumber,
						custbody_in8_3pl2_consignment_number: consignmentObj.carrierConsignmentId,
						custbody_in8_3pl2_machship_carrier: consignmentObj.carrier.displayName,
						custbody_in8_3pl2_machship_carrierserv: consignmentObj.carrierService.displayName,
						custbody_in8_3pl2_machship_total_cost: consignmentObj.consignmentTotal.totalSellPrice,
						custbody_in8_3pl2_machship_despatch: despatchDateUtc,
						custbody_in8_3pl2_machship_delivery: deliveryDateUtc
					}
				});
	  		}
		} else {
			log.audit(logTitle + "NetSuite record (ID: " + recordId + " | Type: " + recordType + ") has failed to be exported to 3PL", "Updating the '3PL Export Results Queue' record...");
			
			//log.debug(logTitle + "emailBody", emailBody);
			
			var emailSubject = "3PL Export Error (Internal ID: " + recordId + " | Record Type: " + recordType + ")";
			
	  		helper.errorEmailNotif(emailBody, emailSubject);
	  		
	  		if (accountId == "6739033" || accountId == "6739033_SB1" || accountId == "6739033_SB2") {
	  			// 6739033 == Adventure Operations
	  			// 6739033_SB1 == Adventure Operations (SB1)
	  			// 6739033_SB2 == Adventure Operations (SB2)
	  			
	  			helper.updateExportErrorDetails(emailBody, recordType, recordId);
	  		}
		}
		
		// Check for "3PL Export Results Queue" duplicates
        var filters = [];
		if (recordType == "salesorder" || recordType == "purchaseorder" || recordType == "transferorder" || recordType == "itemfulfillment") {
			filters.push(["custrecord_in8_3pl2_transaction","anyof", recordId])
		}
        
        var exportResultsDuplicateCheckerObj = helper.recordDuplicateChecker("customrecord_in8_3pl2_trans_exp_results", filters);
		
        var duplicateCount = exportResultsDuplicateCheckerObj.duplicateCount;
        var exportResultsQueueId = exportResultsDuplicateCheckerObj.duplicateId;
        
		if (duplicateCount > 0) {
			log.audit(logTitle + "Found an existing '3PL Export Results Queue' record (ID: " + exportResultsQueueId +" | Record Type: " + recordType + ")", "Updating the record...");
			
			// Update '3PL Export Results Queue' record
			var exportResultsQueueRecId = helper.createOrUpdateExportResults("update", exportResultsQueueId, recordId, exportMappingsId, settingsId, apiExported, convertedPayload, exportNetSuiteRecordResponse);
			
			log.audit(logTitle + "'3PL Export Results Queue' record has been updated", "ID: " + exportResultsQueueRecId);
		} else {
			// Create '3PL Export Results Queue' record
			log.audit(logTitle + "No '3PL Export Results Queue' record duplicates found for record (ID: " + recordId + " | Type: " + recordType + ")", "Creating the '3PL Export Results Queue' record...")
			
			var exportResultsQueueRecId = helper.createOrUpdateExportResults("create", exportResultsQueueId, recordId, exportMappingsId, settingsId, apiExported, convertedPayload, exportNetSuiteRecordResponse);
            
            log.audit(logTitle + "'3PL Export Results Queue' record has been created", "ID: " + exportResultsQueueRecId);
		}
		
		log.audit(logTitle + "Export flow is finished", "Exiting the script...");
    }
    
    function exportNetSuiteRecord(accountId, recordType, settingsObj, convertedPayload, exportNetSuiteRecordResponse, responseStatus, isSuccessful, emailBody, customParametersObj) {    	
    	var exportNetSuiteRecordResponseCode, exportNetSuiteRecordResponseBody;
    	
    	if (recordType == "salesorder") {
			exportNetSuiteRecordResponse = helper.exportNetSuiteRecord(settingsObj, "POST", "/request/ship", null, convertedPayload);
			
			exportNetSuiteRecordResponseCode = exportNetSuiteRecordResponse.code;
			exportNetSuiteRecordResponseBody = JSON.parse(exportNetSuiteRecordResponse.body);
			
			responseStatus = exportNetSuiteRecordResponseBody.Orders[0].Received;
			
			if (responseStatus) {
				isSuccessful = true;
			} else {
				emailBody = exportNetSuiteRecordResponseBody.Orders[0].Message;
			}
		} else if (recordType == "purchaseorder") {
			exportNetSuiteRecordResponse = helper.exportNetSuiteRecord(settingsObj, "POST", "/request/inventoryreceipt", null, convertedPayload);
			
			exportNetSuiteRecordResponseCode = exportNetSuiteRecordResponse.code;
			exportNetSuiteRecordResponseBody = JSON.parse(exportNetSuiteRecordResponse.body);
			
//			responseStatus = exportNetSuiteRecordResponse;
			
			// Successful response for this API does not have a valid response body. Hence, response code is being returned
			if (exportNetSuiteRecordResponseCode == 200 || exportNetSuiteRecordResponseCode == 201) {
				isSuccessful = true;
			} else {
				emailBody = exportNetSuiteRecordResponse;
			}
		} else if (recordType == "transferorder") {
			if (customParametersObj.fromLocationId == "6") {
				exportNetSuiteRecordResponse = helper.exportNetSuiteRecord(settingsObj, "POST", "/request/ship", null, convertedPayload);
				
				exportNetSuiteRecordResponseCode = exportNetSuiteRecordResponse.code;
				exportNetSuiteRecordResponseBody = JSON.parse(exportNetSuiteRecordResponse.body);
				
				responseStatus = exportNetSuiteRecordResponseBody.Orders[0].Received;
				
				if (responseStatus) {
					isSuccessful = true;
				} else {
					emailBody = exportNetSuiteRecordResponse.Orders[0].Message;
				}
			} else if (customParametersObj.toLocationId == "6") {
				exportNetSuiteRecordResponse = helper.exportNetSuiteRecord(settingsObj, "POST", "/request/inventoryreceipt", null, convertedPayload);
				
				exportNetSuiteRecordResponseCode = exportNetSuiteRecordResponse.code;
				exportNetSuiteRecordResponseBody = JSON.parse(exportNetSuiteRecordResponse.body);
				
//				responseStatus = exportNetSuiteRecordResponse;
				
				// Successful response for this API does not have a valid response body. Hence, response code is being returned
				if (exportNetSuiteRecordResponseCode == 200 || exportNetSuiteRecordResponseCode == 201) {
					isSuccessful = true;
				} else {
					emailBody = exportNetSuiteRecordResponse;
				}
			}
		} else if (recordType == "itemfulfillment") {
			if (accountId == "6739033" || accountId == "6739033_SB1" || accountId == "6739033_SB2") {
				// 6739033 == Adventure Operations
				// 6739033_SB1 == Adventure Operations (SB1)
				// 6739033_SB2 == Adventure Operations (SB2)
				
				//Create Consignment
				if (customParametersObj.hasDangerousGoods) {
					exportNetSuiteRecordResponse = helper.exportNetSuiteRecord(settingsObj, "POST", "/consignments/createConsignmentwithComplexItems", null, convertedPayload, 
							accountId, customParametersObj);
				} else {
					exportNetSuiteRecordResponse = helper.exportNetSuiteRecord(settingsObj, "POST", "/consignments/createConsignment", null, convertedPayload, 
							accountId, customParametersObj);
				}
				
				
				exportNetSuiteRecordResponseCode = exportNetSuiteRecordResponse.code;
				exportNetSuiteRecordResponseBody = JSON.parse(exportNetSuiteRecordResponse.body);
				
				responseStatus = exportNetSuiteRecordResponseBody.object;
				
				// Successful response for this API does not have a valid response body. Hence, response code is being returned
				if (responseStatus) {
					isSuccessful = true;
				} else {
					// TODO: Notify the user/employee

					emailBody = exportNetSuiteRecordResponseBody.errors;
				}
			}
		}
    	
    	var exportNetSuiteRecordResultObj = {
    			"isSuccessful": isSuccessful,
    			"emailBody": emailBody,
    			"exportNetSuiteRecordResponse": exportNetSuiteRecordResponse
    	};
    	
    	
    	return exportNetSuiteRecordResultObj;
	}

    return {
        afterSubmit: afterSubmit
    };
});