require(["N/record", "N/query", "N/search"],
	function(record, query, search) {

		var sqlQueryData = "SELECT salesorder.id, soline.item, soline.itemtype, soline.netamount, items.fullname, items.itemid FROM transaction salesorder INNER JOIN transactionline soline ON salesorder.id = soline.transaction INNER JOIN item items ON items.id = soline.item WHERE salesorder.id IN ('6189') ";
		
		var resultSet = query.runSuiteQL({
			query: sqlQueryData
		});
	 
		var results = resultSet.results;
		for(var i = 0; i < results.length; i++ ){
			console.log(results[i].values);
			
		}
	 }
);
