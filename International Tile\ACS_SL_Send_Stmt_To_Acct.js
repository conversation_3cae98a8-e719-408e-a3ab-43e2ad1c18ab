/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
 define(['N/search', 'N/email', 'N/render', 'N/record', 'N/format'], function(search, email, render, record, format) {

    function onRequest(context) {
        
        var customerId = context.request.parameters.cust_id;
        var statementDate = context.request.parameters.statementdate;
        var startDate = context.request.parameters.startdate;

        var startDate = format.format({
            value: startDate,
            type: format.Type.DATE
        });

        var statementDate = format.format({
            value: statementDate,
            type: format.Type.DATE
        });

        // var recObj = record.load({
        //     id: recId,
        //     type: record.Type.WORK_ORDER,
        //     isDynamic: true
        // });

        // var startdate = recObj.getValue({ fieldId: 'startdate' });


        // var formattedDate = format.format({
        //     value: startdate,
        //     type: format.Type.DATE
        // });
        try {
            log.debug('test', 'test1');
            var emailBody = render.statement({
                entityId: parseInt(customerId),
                formId: 81,
                printMode: render.PrintMode.HTML,
                startDate: startDate,
                statementDate: statementDate
            }).getContents();
            log.debug('test', 'test2');
            log.debug('emailBody', emailBody);
        } catch (e) {
            log.debug('error', e);
        }
        log.debug('test', 'test3');

        // log.debug('test', {customerId: customerId,
        //     statementDate: statementDate,
        //     startDate: startDate,});
        
        // context.response.write({
        //     output: JSON.stringify(context.request.parameters)
        // });
        
    }

    return {
        onRequest: onRequest
    }
});


// /**
//  *@NApiVersion 2.x
//  *@NScriptType UserEventScript
//  */
//  define(['N/search', 'N/email', 'N/render', 'N/record'], function(search, email, render, record) {

//     function afterSubmit(context) {
//         var currRecord = context.newRecord;
//         if(context.type == context.UserEventType.CREATE){
//             var companyId = currRecord.getValue({ fieldId: 'entity'});
//             var transactionId = search.lookupFields({
//                 type: search.Type.INVOICE,
//                 id: currRecord.id,
//                 columns: [
//                     'tranid'
//                 ]
//             }).tranid;

//             var companyObj = record.load({
//                 id: companyId,
//                 type: record.Type.CUSTOMER
//             });

//             // get conditions for each recipients
//             var emailToSalesRep = currRecord.getValue({ fieldId: 'custbody_acs_email_inv_to_salesrep' });
//             var emailToCust = companyObj.getValue({ fieldId: 'custentity_acs_email_inv_to_customer' });
//             var emailToContact = companyObj.getValue({ fieldId: 'custentity_acs_email_inv_to_contact' });

// //             // get template renderer email and body
//             var emailBody = render.transaction({
//                 entityId: currRecord.id,
//                 printMode: render.PrintMode.HTML,
//                 inCustLocale: true
//             }).getContents();
            // var emailSubject = "QALO Holdings, LLC: #" + transactionId;

//             // send email to sales rep
//             if(emailToSalesRep){
//                 // get salesrep email
//                 var salesRepEmail = search.lookupFields({
//                     type: search.Type.EMPLOYEE,
//                     id: currRecord.getValue({ fieldId: 'salesrep' }),
//                     columns: [
//                         'email'
//                     ]
//                 }).email;

//                 // if no email has been found, skip.
//                 if(salesRepEmail){
//                     email.send({
//                         author: 3881,
//                         recipients: salesRepEmail,
//                         subject: emailSubject,
//                         body: emailBody,
//                         relatedRecords: {
//                             transactionId: currRecord.id
//                         }
//                     });
//                 }
//             }

//             // send email to customer
//             if(emailToCust){
//                 log.debug(companyId);
//                 // get customer email
//                 var customerEmail = search.lookupFields({
//                     type: search.Type.CUSTOMER,
//                     id: companyId,
//                     columns: [
//                         'email'
//                     ]
//                 }).email;
//                 // if no email has been found, skip.
//                 if(emailToCust){
//                     email.send({
//                         author: 3881,
//                         recipients: customerEmail,
//                         subject: emailSubject,
//                         body: emailBody,
//                         relatedRecords: {
//                             entityId: companyId,
//                             transactionId: currRecord.id
//                         }
//                     });
//                 }
//             }            

//             // send email to customer contacts
//             if(emailToContact){
//                 // email result of search
//                 var contactSearchObj = search.create({
//                     type: "contact",
//                     filters: [["company","anyof",companyId]],
//                     columns: [search.createColumn({name: "email", label: "Email"})]
//                 });
//                 // loop through each results and send to each result
//                 contactSearchObj.run().each(function(result){
//                     var contactEmail = result.getValue({ name: 'email' });
//                     email.send({
//                         author: 3881,
//                         recipients: contactEmail,
//                         subject: emailSubject,
//                         body: emailBody,
//                         relatedRecords: {
//                             entityId: companyId,
//                             transactionId: currRecord.id
//                         }
//                     });

//                     return true;
//                 });
//             }
//         }
//     }

//     return {
//         afterSubmit: afterSubmit
//     }
// });
