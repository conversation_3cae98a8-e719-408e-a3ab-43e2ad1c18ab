function suitelet(request, response)
{
    try
    {
        if(request.getMethod() == "POST")
        {

            var requestObj = JSON.parse(request.getBody());

            if(!requestObj.type)
                throw nlapiCreateError("Type", 'Type not defined');

            if(!requestObj.id)
                throw nlapiCreateError("ID", "ID couldn't be empty");

            var recordId = null;
            var customform = null;

            switch (requestObj.type)
            {
                case "1":
                    recordId = searchCase(requestObj.id);
                    customform = searchCustomForm(requestObj.internalid, 'supportcase');
                    break;

                case "2":
                    recordId = searchProjectTask(requestObj.id);
                    customform = searchCustomForm(requestObj.internalid, 'projectTask');
                    break;

                case "3":
                    recordId = searchCustomer(requestObj.id);
                    customform = searchCustomForm(requestObj.internalid, 'customer');
                    break;

                case "4":
                    recordId = searchProject(requestObj.id);
                    customform = searchCustomForm(requestObj.internalid, 'job');
                    break;
            }

            if(!recordId)
                throw nlapiCreateError("Record ID", "Record not found");

            var ret = {
                sucess: true,
                recordId: recordId,
                customform: customform
            };

            response.write(JSON.stringify(ret));

        }
        else
        {
            throw nlapiCreateError("Method",'Method not allowed');
        }
    }
    catch(e)
    {
        response.write(JSON.stringify(createError(e.getDetails())));
    }
}

function createError(message)
{
    return {
        sucess: false,
        message: message
    };
}

function searchCase(caseNumber)
{
    var searchCase = nlapiSearchRecord('supportcase', null, new nlobjSearchFilter('number', null, 'equalto', caseNumber) , null);

    if(searchCase)
    {
        return searchCase[0].getId();
    }
    else
        return null;
}

function searchCustomer(customerId)
{
    var searchCustomer = nlapiSearchRecord('customer', null, new nlobjSearchFilter('entityid', null, 'is', customerId), null);

    if(searchCustomer)
    {
        return searchCustomer[0].getId();
    }
    else
        return null;
}

function searchProject(projectId)
{
    var searchProject = nlapiSearchRecord('job', null, new nlobjSearchFilter('entityid', null, 'is', projectId), null);

    if(searchProject)
    {
        return searchProject[0].getId();
    }
    else
        return null;
}

function searchProjectTask(projectTaskId)
{
    var searchTask = nlapiSearchRecord('projecttask', null, new nlobjSearchFilter('internalid', null, 'anyof', projectTaskId) , null);

    if(searchTask)
    {
        return searchTask[0].getId();
    }
    else
        return null;
}

function searchCustomForm(internalid, recordType)
{
    var filters = [];
    filters.push(new nlobjSearchFilter('custrecord_in8_cfa_employee', null, 'anyof', internalid));
    filters.push(new nlobjSearchFilter('custrecord_in8_cef_recordtype', 'custrecord_in8_cfa_form', 'is', recordType));

    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_cfa_level').setSort(false));
    columns.push(new nlobjSearchColumn('custrecord_in8_cfa_form'));

    var search = nlapiSearchRecord('customrecord_in8_externalformaccess', null, filters, columns);

    if(search)
        return search[0].getValue('custrecord_in8_cfa_form');
    else
        return null;
}