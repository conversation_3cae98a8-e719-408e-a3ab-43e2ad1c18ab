/**
* @NApiVersion 2.x
* @NScriptType ClientScript
*/
define(["require", "exports", "N/currentRecord", "N/https", "N/runtime", "N/ui/dialog", "../ds_common_cs", "../types/ns_types", "../types/ds_t_telemetry"], function (require, exports, currentRec, https, runtime, uiDialog, dcc, n, dsttelemetry) {
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.pageInit = function (context) {
        // this is a necessary placeholder. This will compile without this 
        // function but NetSuite will not allow the file to be uploaded to 
        // the file cabinet without at least one formal script entry point.
    };
    /**
     * @desc  Helper Function: Create and return the header array for making api call to DocuSign services for NetSuite
     * @return {object} - the header array for making api call to DocuSign services for NetSuite
     */
    exports.getRequestHeaders = function () {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent-x': 'SuiteScript Call'
        };
    };
    /**
     * @desc  Helper Function: Trim the response message
     * @param {string} response - the response of the api call
     * @return {string} - the value of the parameter
     */
    exports.trimResponseMessage = function (response) {
        var result = response;
        if ((response.substring(0, 1) === "\"") && (response.substring(response.length - 1, response.length) === "\"")) {
            result = response.substring(1, response.length - 1);
        }
        return result;
    };
    exports.ds_cs_openDSAdminConsole = function () {
        exports.logAction(dsttelemetry.TelemetryUserEvents.DocuSignAdminConsole, null)
            .then(function () {
            var _url = dcc.getNetSuiteUrl(n.urlEndpoint.restlet);
            var parameter = "&action=opendsadminconsole" + "&time=" + new Date().getTime();
            var headers = exports.getRequestHeaders();
            var response = https.request({
                method: https.Method.GET,
                url: _url + parameter,
                headers: headers,
            });
            var responseBody = exports.trimResponseMessage(response.body);
            if (responseBody.substring(0, 5) === "https") {
                window.open(responseBody, 'DocuSign');
            }
            else {
                alert(responseBody);
            }
        });
    };
    exports.ds_cs_openDSAcctSettingsPage = function () {
        exports.logAction(dsttelemetry.TelemetryUserEvents.DocuSignAccountSettings, null)
            .then(function () {
            var _url = dcc.getNetSuiteUrl(n.urlEndpoint.configuration_suitelet);
            window.location.href = _url;
        });
    };
    exports.ds_openDSAcctConfigPage = function () {
        exports.logAction(dsttelemetry.TelemetryUserEvents.DocuSignConfigureAccount, null)
            .then(function () {
            var _url = dcc.getNetSuiteUrl(n.urlEndpoint.configuration_suitelet);
            window.onbeforeunload = null;
            window.open(_url + '&configacct=true', '_self', 'false');
        });
    };
    /**
     * @desc  Event triggered by: "Update" button
     *      Update the DocuSign Envelope Status record.  Save the Postback documents to NetSuite if the envelope is completed.
     * @param {string} loadingGif - the source of the loading gif image
     */
    exports.docusign_update = function (loadingGif) {
        exports.displayLoader(loadingGif);
        setTimeout(function () {
            var recordId = encodeURIComponent(currentRec.get().id || '');
            var recordType = encodeURIComponent(currentRec.get().type || '');
            var url = dcc.getNetSuiteUrl(n.urlEndpoint.restlet);
            url += "&action=update&time=" + new Date().getTime();
            url += "&recordId=" + recordId + "&recordType=" + recordType;
            var headers = exports.getRequestHeaders();
            exports.logAction(dsttelemetry.TelemetryUserEvents.DocumentUpdate, null)
                .then(function () {
                try {
                    var response = https.get({
                        url: url,
                        headers: headers
                    });
                    var responseBody = exports.trimResponseMessage(response.body);
                    if (responseBody !== "") {
                        alert(responseBody.replace(/\\"/g, "\""));
                    }
                    exports.refreshPage(true);
                }
                catch (error) {
                    exports.displayErrorMessage(error);
                }
                jQuery.unblockUI({ fadeOut: 500 });
            });
        }, 1000);
    };
    /**
     * @desc  This function will validate the Custom Button for things such as Merge Fields and provide
     *  the user with a confirmation dialog asking whether to continue or cancel in failed validations.
     *  On success will be sent to docusign_Process with the same parameters.
     * @param  {string} action - the DS action; gets passed to the restlet
     * @param  {string} loadingGif - the source of the loading gif image
     * @param  {string} showTYPage - the flag indicating if the "Show thank you page ..." checkbox is checked
     * @param  {string} dsCustomBtnId - ID of the DS custom script; gets passed to the restlet [optional]
     * @param  {string} dsUserEmail - NetSuite user's email address; gets passed to the restlet [optional]
     * @param  {string} dsUserName - NetSuite user's name; gets passed to the restlet [optional]
     * @param  {boolean} emailVerified - flag indicating if the user's email address is verified [optional]
     * @param  {string} dsUserId - the selected user id
     */
    exports.docusign_processMergeFieldButton = function (action, loadingGif, showTYPage, dsCustomBtnId, dsUserEmail, dsUserName, emailVerified, dsUserId) {
        var recordId = encodeURIComponent(currentRec.get().id || '');
        var recordType = encodeURIComponent(currentRec.get().type || '');
        // call field matching suitelet
        var matchingUrl = dcc.getNetSuiteUrl(n.urlEndpoint.data_merge_suitelet, {
            recordType: recordType,
            recordId: recordId,
            dsCustomBtnId: dsCustomBtnId,
            ifrmcntnr: 'T' // so ns knows this is an iframe
        });
        var parameter = generateParameter(action, recordId, recordType, dsCustomBtnId, dsUserEmail, dsUserName, dsUserId);
        matchingUrl += parameter;
        exports.displayLoader(loadingGif);
        var iframeOpened = false;
        https.get.promise({
            url: matchingUrl
        }).then(function (response) {
            jQuery.unblockUI({ fadeOut: 0 });
            var contentWidth = (action === 'sign') ? 1028 : 1140;
            exports.displayDocuSignView(matchingUrl, contentWidth);
            jQuery('#docusign_close').click(function () {
                jQuery.unblockUI();
                exports.refreshPage();
                return false;
            });
            var envelopeId = "";
            try {
                // Try to get the envelope id from the response if it was a postback.
                var json = JSON.parse(response.body);
                envelopeId = json.envelopeId;
            }
            catch (er) {
                // The response was likely html instead of json this is expected in get requests.
            }
            setIFrameCloseFunctionality(iframeOpened, action, showTYPage, envelopeId);
        }).catch(function (e) {
            jQuery.unblockUI({ fadeOut: 0 });
            console.log(e);
        });
    };
    /**
     * @desc  Execute either a built-in DS function or a custom DS script
     *
     *    This function will do the followings:
     *      - Make an API call to DocuSign Services for NetSuite(DocuSign RESTLet)
     *        - if the API call returns a view url, create and display the sender view iFrame
     *          - if "Close" button or "Discard Changes" link is clicked, close the sender view iFrame
     *          - if "Send" button is clicked, show the thank you page(depending if "Show thank you .." checkbox is checked or not).
     *          - if "Save Draft" link is clicked, display the "Save Envelope" dialog box
     *        - if the API call returns "OpenVerifyAccountDialogBox", display the "Provisioning" dialog box
     *        - if the API call doesn't return a url, display the error message
     *
     * @param  {string} action - the DS action; gets passed to the restlet
     * @param  {string} loadingGif - the source of the loading gif image
     * @param  {string} showTYPage - the flag indicating if the "Show thank you page ..." checkbox is checked
     * @param  {string} dsCustomBtnId - ID of the DS custom script; gets passed to the restlet [optional]
     * @param  {string} dsUserEmail - NetSuite user's email address; gets passed to the restlet [optional]
     * @param  {string} dsUserName - NetSuite user's name; gets passed to the restlet [optional]
     * @param  {boolean} emailVerified - flag indicating if the user's email address is verified [optional]
     * @param  {string} dsUserId - the selected user id
     */
    exports.docusign_process = function (action, loadingGif, showTYPage, dsCustomBtnId, dsUserEmail, dsUserName, emailVerified, dsUserId) {
        var iframeOpened = false;
        exports.displayLoader(loadingGif);
        var _url = dcc.getNetSuiteUrl(n.urlEndpoint.restlet);
        var recordId = encodeURIComponent(currentRec.get().id || '');
        var recordType = encodeURIComponent(currentRec.get().type || '');
        var parameter = generateParameter(action, recordId, recordType, dsCustomBtnId, dsUserEmail, dsUserName, dsUserId);
        var headers = exports.getRequestHeaders();
        https.get
            .promise({
            url: _url + parameter,
            headers: headers
        })
            .then(function (response) {
            var responseBody = exports.trimResponseMessage(response.body);
            var dsApiCall = null;
            try {
                dsApiCall = JSON.parse(responseBody);
            }
            catch (e) {
                uiDialog
                    .alert({
                    title: 'There was an error creating the envelope',
                    message: responseBody
                })
                    .finally(function () {
                    exports.refreshPage();
                });
            }
            jQuery.unblockUI({ fadeOut: 0 });
            if (dsApiCall && dsApiCall.viewUrl && dsApiCall.viewUrl.substring(0, 4) === "http") {
                var contentWidth = (action === 'sign') ? 1028 : 1140;
                exports.displayDocuSignView(dsApiCall.viewUrl, contentWidth);
                jQuery('#docusign_close').click(function () {
                    jQuery.unblockUI();
                    exports.cancelEnvelopeSend(dsApiCall.envelopeId);
                    exports.refreshPage();
                    return false;
                });
                setIFrameCloseFunctionality(iframeOpened, action, showTYPage, dsApiCall.envelopeId);
            }
        })
            .catch(function (error) {
            var errorObj = null;
            try {
                errorObj = JSON.parse(error.message);
            }
            catch (err) {
                errorObj = error;
            }
            uiDialog
                .alert({
                title: 'There was an error creating the envelope',
                message: !!errorObj.message ? errorObj.message : 'Please try again'
            })
                .then(function (response) {
                jQuery.unblockUI();
            });
            return;
        });
    };
    /**
     * @desc  Display the loading image
     * @param  {string} loadingGif - the source of the loading image
     */
    exports.displayLoader = function (loadingGif) {
        var windowWidth = exports.getWindowWidth();
        var windowHeight = exports.getWindowHeight();
        var imageHeight = 20;
        var imageWidth = 120;
        jQuery.blockUI({
            message: '<img src="' + loadingGif + '" height="' + imageHeight + 'px" width="' + imageWidth + 'px"  />',
            css: {
                top: ((windowHeight - imageHeight) / 2) + 'px',
                left: ((windowWidth - imageWidth) / 2) + 'px',
                height: imageHeight + 'px',
                width: imageWidth + 'px',
                border: 'none',
                backgroundColor: 'transparent',
                color: 'gray',
                opacity: .9
            }
        });
    };
    /**
     * @desc  Create and open the dialog box for sender and recipient view
     * @param  {string} content - the content of the dialog box
     * @param  {string} contentWidth - the width of the dialog box
     * @param  {string} contentHeight - the height of the dialog box
     */
    exports.displayDocuSignView = function (content, contentWidth, contentHeight) {
        var windowWidth = exports.getWindowWidth();
        var windowHeight = exports.getWindowHeight();
        var ds_iframe_width = windowWidth - 100;
        var ds_iframe_height = windowHeight - 25;
        if (contentWidth) {
            ds_iframe_width = contentWidth;
        }
        if (contentHeight) {
            ds_iframe_height = windowHeight;
        }
        jQuery.blockUI({
            message: '<div><button style="float:right" type="button" id="docusign_close" >Close</button><label>DocuSign - The Global Standard in eSignature</label>'
                + '<div style="clear:both;"></div></div>'
                + '<div><iframe id="docuSigniFrame" src=' + content + ' height="' + (ds_iframe_height - 34) + 'px" width="' + (ds_iframe_width - 10) + 'px" scrolling="auto"></iframe></div>',
            css: {
                top: '10px',
                left: (windowWidth - ds_iframe_width) / 2 + 'px',
                width: ds_iframe_width + 'px',
                height: ds_iframe_height + 'px',
                position: 'absolute'
            }
        });
    };
    /**
     * @desc  Event triggered by: "Remove all DocuSign Envelope Status" button
     *      Delete all the "DocuSign Envelope Status" Record
     */
    exports.deleteDSEnvelopeStatusRecord = function (envelopeId) {
        var _url = dcc.getNetSuiteUrl(n.urlEndpoint.restlet);
        var parameter = "&dsAction=deletedsenvelopestatusrecord&envelopeId=" + encodeURIComponent(envelopeId)
            + "&time=" + new Date().getTime();
        var headers = exports.getRequestHeaders();
        return https.delete.promise({
            url: _url + parameter,
            headers: headers,
        });
    };
    /**
     * @desc  Helper Function: Remove the dstab parameter and refresh the page
     * @param {boolean} openDSTab: flag indicating if the DocuSign tab should be open
     */
    exports.refreshPage = function (openDSTab) {
        if (openDSTab) {
            var newWindowLocation = window.location.href.replace("\#", "");
            if (!exports.getURLParameter('dstab', newWindowLocation)) {
                newWindowLocation += "&dstab=true";
            }
            window.location.href = newWindowLocation;
        }
        else {
            window.location.href = window.location.href.replace("\#", "").replace("&dstab=true", "");
        }
    };
    /**
     * @desc  Helper Function: Return the value of a specific parameter
     * @param {string} parameterName - the name of the image
     * @param {string} url - the name of the image
     * @return {string} - the value of the parameter
     */
    exports.getURLParameter = function (parameterName, url) {
        return decodeURIComponent((new RegExp('[?|&]' + parameterName + '=' + '([^&;]+?)(&|#|;|$)').exec(url) || [, ""])[1].replace(/\+/g, '%20')) || null;
    };
    /**
     * @desc  Create and open the "Thank You" dialog box
     * @param  {string} envelopeId - the envelope id
     */
    exports.openThankYouDialogBox = function (envelopeId) {
        var windowWidth = exports.getWindowWidth();
        var windowHeight = exports.getWindowHeight();
        var pageHeight = 655;
        var pageWidth = 800;
        if (!envelopeId) {
            envelopeId = "";
        }
        var content = dcc.getNetSuiteUrl(n.urlEndpoint.landingpage);
        content += "&envelopeId=" + encodeURIComponent(envelopeId) + "&dsUserAction=thankyou" + "&time=" + new Date().getTime();
        jQuery.blockUI({
            message: '<iframe id="dsThankYouPageiFrame" marginheight="0" marginwidth="0" height="100%" width="100%" scrolling="no" frameborder="0" src=' + content + '></iframe>',
            css: {
                top: ((windowHeight - pageHeight) / 2) + 'px',
                left: ((windowWidth - pageWidth) / 2) + 'px',
                height: pageHeight + 'px',
                width: pageWidth + 'px',
                border: 'none',
                backgroundColor: 'transparent',
                color: 'gray'
            }
        });
        jQuery('#dsThankYouPageiFrame').load(function () {
            setTimeout(function () {
                var dsThankYouPageiFrame = jQuery("#dsThankYouPageiFrame").contents();
                dsThankYouPageiFrame.find("#ds_thankyoupage_close").click(function () {
                    jQuery.unblockUI({ fadeOut: 500 });
                    exports.refreshPage(true);
                    return false;
                });
            }, 500);
        });
    };
    /**
     * @desc  Create and open the "Save Envelope" dialog box
     */
    exports.openDSSaveEnvelopeDialogBox = function () {
        var windowHeight = exports.getWindowHeight();
        var windowWidth = exports.getWindowWidth();
        var dialogHeight = 230;
        var dialogWidth = 700;
        var content = dcc.getNetSuiteUrl(n.urlEndpoint.landingpage);
        content += "&dsUserAction=saveenvelope" + "&time=" + new Date().getTime();
        jQuery.blockUI({
            message: '<iframe id="dsSaveEnvelopeiFrame" marginheight="0" marginwidth="0" height="100%" width="100%" scrolling="no" frameborder="0" src=' + content + '></iframe>',
            css: {
                top: ((windowHeight - dialogHeight) / 2) + 'px',
                left: ((windowWidth - dialogWidth) / 2) + 'px',
                width: dialogWidth + 'px',
                height: dialogHeight + 'px',
                position: 'absolute',
                border: 'none',
                backgroundColor: 'white',
                color: 'black'
            }
        });
        jQuery('#dsSaveEnvelopeiFrame').load(function () {
            setTimeout(function () {
                var dsSaveEnvelopeiFrame = jQuery("#dsSaveEnvelopeiFrame").contents();
                dsSaveEnvelopeiFrame.find("#ds_SaveEnvelope_DialogBox_close").click(function () {
                    jQuery.unblockUI({ fadeOut: 500 });
                    exports.refreshPage(true);
                    return false;
                });
                dsSaveEnvelopeiFrame.find("#ds_save_envelope_close").click(function () {
                    jQuery.unblockUI({ fadeOut: 500 });
                    exports.refreshPage(true);
                    return false;
                });
            }, 500);
        });
    };
    /**
     * @desc  Create and open the "Provisioning" dialog box
     * @param  {string} loadingGif - the source of the loading image
     * @param  {string} userEmail - DocuSign account email
     * @param  {string} userName - DocuSign account user name
     * @param  {string} showTYPage - the flag indicating if the thank you page should be diaplyed
     * @param  {string} dsAction - DocuSign Action - either "DsSend" or "DsSign"
     */
    exports.openDSProvisioningDialogBox = function (loadingGif, userEmail, userName, showTYPage, dsAction) {
        var windowHeight = exports.getWindowHeight();
        var windowWidth = exports.getWindowWidth();
        var dialogHeight = 300;
        var dialogWidth = 700;
        var content = dcc.getNetSuiteUrl(n.urlEndpoint.landingpage);
        content += "&dsUserAction=provision&userName=" + encodeURIComponent(userName) + "&time=" + new Date().getTime();
        jQuery.blockUI({
            message: '<iframe id="dsProvisioniFrame" marginheight="0" marginwidth="0" height="100%" width="100%" scrolling="no" frameborder="0" src=' + content + '></iframe>',
            css: {
                top: ((windowHeight - dialogHeight) / 2) + 'px',
                left: ((windowWidth - dialogWidth) / 2) + 'px',
                width: dialogWidth + 'px',
                height: dialogHeight + 'px',
                position: 'absolute',
                border: 'none',
                backgroundColor: 'white',
                color: 'black'
            }
        });
        jQuery('#dsProvisioniFrame').load(function () {
            setTimeout(function () {
                var dsProvisioniFrame = jQuery("#dsProvisioniFrame").contents();
                dsProvisioniFrame.find("#ds_user_email").val(userEmail);
                dsProvisioniFrame.find("#ds_user_email").prop("disabled", true);
                dsProvisioniFrame.find("#ds_Provision_DialogBox_close").click(function () {
                    jQuery.unblockUI({ fadeOut: 500 });
                    return false;
                });
                dsProvisioniFrame.find('#ds_provision_new_user_submit').click(function () {
                    var dsNewUserName = dsProvisioniFrame.find("#ds_user_name").val();
                    var dsNewUserEmail = dsProvisioniFrame.find("#ds_user_email").val();
                    if (!dsNewUserName || dsNewUserName === "Enter a new username") {
                        alert("User name cannot be empty.");
                    }
                    else if (!dsNewUserEmail || dsNewUserEmail === "Enter your email address") {
                        alert("Email Address cannot be empty.");
                    }
                    else if (!exports.isValidEmail(dsNewUserEmail)) {
                        alert("Please enter a valid email address.");
                    }
                    else {
                        jQuery.unblockUI({ fadeOut: 0 });
                        exports.docusign_process(dsAction, loadingGif, showTYPage, null, dsNewUserEmail, dsNewUserName, true);
                    }
                });
            }, 500);
        });
    };
    /**
     * @desc  Create and open the "Verify DS User" dialog box
     * @param  {string} loadingGif - the source of the loading image
     * @param  {string} dsAction - DocuSign Action - either "DsSend" or "DsSign"
     * @param  {string} showTYPage - the flag indicating if the thank you page should be diaplyed
     */
    exports.openDSVerifyAcctDialogBox = function (loadingGif, dsAction, showTYPage) {
        var windowHeight = exports.getWindowHeight();
        var windowWidth = exports.getWindowWidth();
        var dialogHeight = 270;
        var dialogWidth = 620;
        var userEmail = runtime.getCurrentUser().email;
        var content = dcc.getNetSuiteUrl(n.urlEndpoint.landingpage);
        content += "&dsUserAction=verifyaccount&email=" + encodeURIComponent(userEmail) + "&time=" + new Date().getTime();
        jQuery.blockUI({
            message: '<iframe id="dsVerifyAcctiFrame" marginheight="0" marginwidth="0" height="100%" width="100%" scrolling="no" frameborder="0" src=' + content + '></iframe>',
            constrainTabKey: false,
            bindEvents: false,
            css: {
                top: ((windowHeight - dialogHeight) / 2) + 'px',
                left: ((windowWidth - dialogWidth) / 2) + 'px',
                width: dialogWidth + 'px',
                height: dialogHeight + 'px',
                position: 'absolute',
                border: 'none',
                backgroundColor: 'white',
                color: 'black'
            }
        });
        jQuery('#dsVerifyAcctiFrame').load(function () {
            setTimeout(function () {
                var dsVerifyAcctiFrame = jQuery("#dsVerifyAcctiFrame").contents();
                dsVerifyAcctiFrame.find("#ds_VerifyAcct_DialogBox_close").click(function () {
                    jQuery.unblockUI({ fadeOut: 500 });
                    return false;
                });
                dsVerifyAcctiFrame.find('#ds_verify_account_submit').click(function () {
                    var password = dsVerifyAcctiFrame.find("#ds_dsAcct_password").val();
                    if (!password || password === "" || password === "Enter your DocuSign password") {
                        alert("Password cannot be empty.");
                    }
                    else {
                        var _url = dcc.getNetSuiteUrl(n.urlEndpoint.restlet);
                        var parameter = "&action=verifyaccount&username=" + encodeURIComponent(userEmail)
                            + "&password=" + encodeURIComponent(password)
                            + "&time=" + new Date().getTime();
                        var headers = exports.getRequestHeaders();
                        var response = https.request({
                            method: https.Method.GET,
                            url: _url + parameter,
                            headers: headers
                        });
                        var responseBody = exports.trimResponseMessage(response.body);
                        jQuery.unblockUI({ fadeOut: 0 });
                        if (responseBody.indexOf("ACCOUNTVERIFIED") !== -1) {
                            var userName = responseBody.replace("ACCOUNTVERIFIED", "");
                            exports.openDSProvisioningDialogBox(loadingGif, userEmail, userName, showTYPage, dsAction);
                        }
                        else {
                            alert(responseBody);
                            exports.openDSVerifyAcctDialogBox(loadingGif, dsAction, showTYPage);
                        }
                    }
                    return false;
                });
            }, 500);
        });
    };
    /**
     * @desc  Create and open the "DS User Account Selection" dialog box
     * @param {string} loadingGif - the source of the loading image
     * @param {string} action -
     * @param {string} showTYPage -
     * @param {string} userEmail - the email of the DocuSign user
     */
    exports.openDSUserAcctSelectionDialogBox = function (loadingGif, action, showTYPage, userEmail) {
        var windowHeight = exports.getWindowHeight();
        var windowWidth = exports.getWindowWidth();
        var dialogHeight = 200;
        var dialogWidth = 620;
        var content = dcc.getNetSuiteUrl(n.urlEndpoint.landingpage);
        content += "&dsUserAction=selectdsuseraccount&userEmail=" + encodeURIComponent(userEmail) + "&time=" + new Date().getTime();
        jQuery.blockUI({
            message: '<iframe id="dsSelectUserAccountiFrame" marginheight="0" marginwidth="0" height="100%" width="100%" scrolling="no" frameborder="0" src=' + content + '></iframe>',
            css: {
                top: ((windowHeight - dialogHeight) / 2) + 'px',
                left: ((windowWidth - dialogWidth) / 2) + 'px',
                width: dialogWidth + 'px',
                height: dialogHeight + 'px',
                position: 'absolute',
                border: 'none',
                backgroundColor: 'white',
                color: 'black'
            }
        });
        jQuery('#dsSelectUserAccountiFrame').load(function () {
            setTimeout(function () {
                var dsSelectUserAccountiFrame = jQuery("#dsSelectUserAccountiFrame").contents();
                dsSelectUserAccountiFrame.find("#ds_SelectUserAcct_DialogBox_close").click(function () {
                    jQuery.unblockUI({ fadeOut: 500 });
                    exports.refreshPage();
                    return false;
                });
                dsSelectUserAccountiFrame.find("#ds_select_user_ds_account").click(function () {
                    var dsUserId = dsSelectUserAccountiFrame.find('#ds_user_accounts_dropdown').val();
                    jQuery.unblockUI({ fadeOut: 500 });
                    exports.docusign_process(action, loadingGif, showTYPage, null, null, null, null, dsUserId);
                    return false;
                });
            }, 500);
        });
    };
    /**
     * @desc  Helper Function: Return the width of browser window
     * @return {windowWidth} - the height of browser window
     */
    exports.getWindowWidth = function () {
        var windowWidth = 0;
        if (typeof (window.innerWidth) === 'number') {
            windowWidth = window.innerWidth; //Non-IE
        }
        else if (document.documentElement && (document.documentElement.clientWidth || document.documentElement.clientHeight)) {
            windowWidth = document.documentElement.clientWidth; //IE 6+ in 'standards compliant mode'
        }
        else if (document.body && (document.body.clientWidth || document.body.clientHeight)) {
            windowWidth = document.body.clientWidth; //IE 4 compatible
        }
        return windowWidth;
    };
    /**
     * @desc  Helper Function: Return the height of browser window
     * @return {windowHeight} - the height of browser window
     */
    exports.getWindowHeight = function () {
        var windowHeight = 0;
        if (typeof (window.innerWidth) === 'number') {
            windowHeight = window.innerHeight; //Non-IE
        }
        else if (document.documentElement && (document.documentElement.clientWidth || document.documentElement.clientHeight)) {
            windowHeight = document.documentElement.clientHeight; //IE 6+ in 'standards compliant mode'
        }
        else if (document.body && (document.body.clientWidth || document.body.clientHeight)) {
            windowHeight = document.body.clientHeight; //IE 4 compatible
        }
        return windowHeight;
    };
    /**
     * @desc  Helper Function: Format and display the error message
     * @param {string} error: error message to be formatted and displayed
     */
    exports.displayErrorMessage = function (error) {
        var errorMessage = '';
        if (error.message && error.message.indexOf("An Error Occurred during anchor tag processing.") !== -1) {
            errorMessage = "You have attempted to create an envelope, and map Anchor Tags to that document without first specifying a document.  To remedy this, add a file to the object you are Sending from (Communication>Files) before clicking the Send to DocuSign button.";
        }
        else {
            errorMessage = "[" + error.name + "] " + error.message;
        }
        alert(errorMessage);
    };
    /**
     * @desc Check if an email address is properly formatted.
     * @param {string} email - The email address to validate.
     * @returns {boolean} True if the email is formatted properly, false otherwise.
     */
    exports.isValidEmail = function (email) {
        var emailRegex = /^(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)])$/i;
        return emailRegex.test(email);
    };
    /**
     * @description Validate whether or not merge fields will run into issues.
     * - Make sure there's at least 1 NetSuite contact
     * - Make sure that the Contact Roles match the Template Recipient Roles.
     */
    exports.validateMergeFieldRecipients = function (mergeFieldRecipients) {
        var confirmationInstructions = "<br /><br />Press 'OK' to continue or 'Cancel' to correct the Contact Roles on the NetSuite contacts.";
        var response = {
            success: true,
            message: ""
        };
        //If we don't have any template recipients, assume it's a valid request 
        if (!mergeFieldRecipients || !mergeFieldRecipients.template || mergeFieldRecipients.template.length === 0)
            return response;
        //If we do not have any NetSuite contacts, warn the user that no contacts exist.
        if (!mergeFieldRecipients.netsuite || mergeFieldRecipients.netsuite.length === 0) {
            response.success = false;
            response.message = "There are no NetSuite contacts to map to the Template's recipient roles. " + confirmationInstructions;
            return response;
        }
        else {
            // Make sure that the NetSuite contact roles match up with the DocuSign template's recipient roles.
            // If we have as many or more netsuite contacts, make sure we have at least [# temp. recip] matches.
            // If we have less netsuite contacts, make sure those all at least match.
            var matchingRoles = 0;
            var _loop_1 = function (i) {
                var recipientRole = mergeFieldRecipients.template[i].role;
                var netsuiteMatch = mergeFieldRecipients.netsuite.filter(function (i) { return i.role === recipientRole; });
                if (netsuiteMatch.length > 0) {
                    matchingRoles++;
                }
            };
            for (var i = 0; i < mergeFieldRecipients.template.length; i++) {
                _loop_1(i);
            }
            var minimum = Math.min(mergeFieldRecipients.template.length, mergeFieldRecipients.netsuite.length);
            if (matchingRoles < minimum) {
                response.success = false;
                response.message = "One or more of your contacts do not match up with the DocuSign Template's Recipient Roles. " + confirmationInstructions;
            }
        }
        return response;
    };
    var generateParameter = function (action, recordId, recordType, dsCustomBtnId, dsUserEmail, dsUserName, dsUserId) {
        var parameter = '&action=' + encodeURIComponent(action)
            + '&recordId=' + recordId
            + '&recordType=' + recordType
            + '&domain=' + window.location.protocol
            + '//' + document.domain;
        if (typeof dsCustomBtnId !== 'undefined' && dsCustomBtnId !== null) {
            parameter += "&dsCustomBtnId=" + encodeURIComponent(dsCustomBtnId);
        }
        if (dsUserEmail && dsUserEmail !== "" && dsUserName && dsUserName !== "") {
            parameter += "&dsUserEmail=" + encodeURIComponent(dsUserEmail)
                + "&dsUserName=" + encodeURIComponent(dsUserName);
        }
        if (dsUserId && dsUserId !== "") {
            parameter += "&dsUserId=" + encodeURIComponent(dsUserId);
        }
        return parameter;
    };
    var setIFrameCloseFunctionality = function (iframeOpened, action, showTYPage, envelopeId) {
        jQuery('#docuSigniFrame').on('load', function () {
            setTimeout(function () {
                if (iframeOpened === false) {
                    iframeOpened = true;
                }
                else {
                    if (action === 'send' || action === 'automate') {
                        var dsUserAction = "";
                        var userAgent = navigator.userAgent.toLowerCase();
                        var iframe = document.getElementById("docuSigniFrame");
                        if (userAgent.indexOf('msie') !== -1) {
                            dsUserAction = exports.getURLParameter("event", frames["docuSigniFrame"].location);
                        }
                        else if (iframe.contentWindow) {
                            dsUserAction = exports.getURLParameter("event", String(iframe.contentWindow.location.href));
                        }
                        if (dsUserAction) {
                            jQuery.unblockUI({ fadeOut: 0 });
                            if (dsUserAction === "Send") {
                                exports.processEnvelopeSent(showTYPage, envelopeId);
                            }
                            else if (dsUserAction === "Save") {
                                exports.openDSSaveEnvelopeDialogBox();
                            }
                            else if (dsUserAction === "Cancel") {
                                exports.cancelEnvelopeSend(envelopeId);
                                exports.refreshPage();
                            }
                        }
                    }
                    else {
                        jQuery.unblockUI({ fadeOut: 0 });
                        exports.processEnvelopeSent(showTYPage, envelopeId);
                    }
                }
                return false;
            }, 500);
        });
    };
    exports.logAction = function (action, envelopeId) {
        var _url = dcc.getNetSuiteUrl(n.urlEndpoint.restlet);
        return https.post.promise({
            url: _url,
            headers: exports.getRequestHeaders(),
            body: {
                "action": "telemetry",
                "envelopeId": envelopeId,
                "telemetryAction": action
            }
        });
    };
    exports.cancelEnvelopeSend = function (envelopeId) {
        Promise.all([
            exports.logAction(dsttelemetry.TelemetryUserEvents.EnvelopeCancel, envelopeId).catch(function () { }),
            exports.deleteDSEnvelopeStatusRecord(envelopeId).catch(function () { return console.log('Unable to delete envelope.'); })
        ]).then(function (responses) {
            if (responses[1] && responses[1].code === 200 && envelopeId === "all") {
                alert("All DocuSign Envelope Status records have been removed.");
            }
        })
            .finally(function () {
            exports.refreshPage();
        });
    };
    exports.processEnvelopeSent = function (showTYPage, envelopeId) {
        exports.logAction(dsttelemetry.TelemetryUserEvents.EnvelopeSend, envelopeId)
            .finally(function () {
            if (showTYPage && showTYPage === 'T') {
                exports.openThankYouDialogBox(envelopeId);
            }
            else {
                exports.refreshPage(true);
            }
        });
    };
});
