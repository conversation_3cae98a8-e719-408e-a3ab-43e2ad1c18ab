<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="3" style="width: 565px;">
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    style="float: left; height: 6.5%; width: 6.5%; margin: 3px" /> </#if> <span
                                style="font-size:16px;"><strong><span
                                        class="nameandaddress">${companyInformation.companyName}</span></strong></span><br /><span
                                style="font-size:10px;"><span
                                    class="nameandaddress">${companyInformation.addressText}</span></span>
                        </td>
                        <td align="right" style="width: 240px;"><span class="title">${record@title}</span></td>
                    </tr>
                    <tr>
                        <td align="right" style="width: 240px;"><span class="number">#${record.tranid}</span></td>
                    </tr>
                    <tr>
                        <td align="right" style="width: 240px;">${record.trandate}</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                &nbsp;
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td>
                            <barcode codetype="code128" showtext="true" value="${record.tranid}" />
                        </td>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0px;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0px;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="10%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader" colspan="3" style="width: 443px;"><b>${record.billaddress@label}</b></td>
                <td class="totalboxtop" colspan="5" style="width: 337px;"><b>${record.total@label?upper_case}</b></td>
            </tr>
            <tr>
                <td class="address" colspan="3" rowspan="2" style="width: 443px;">${record.billaddress}</td>
                <td align="right" class="totalboxmid" colspan="5" style="width: 337px;">${record.total}</td>
            </tr>
            <tr>
                <td align="right" class="totalboxbot" colspan="5" style="width: 337px;"><b>${record.duedate@label}:</b>
                    ${record.duedate}</td>
            </tr>
        </table>

        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th style="width: 97px;">${record.terms@label}</th>
                <th style="width: 104px;">${record.duedate@label}</th>
                <th style="width: 127px;">Client</th>
                <th style="width: 110px;">Firm</th>
                <th style="width: 113px;">Neutral</th>
                <th style="width: 165px;">Matter Name</th>
            </tr>
            <tr>
                <td style="width: 97px;">${record.terms}</td>
                <td style="width: 104px;">${record.duedate}</td>
                <td style="width: 127px;">${record.custbody_cust_clientid_hidden}</td>
                <td style="width: 110px;">${record.custbody2}</td>
                <td style="width: 113px;">${record.custbody_cust_neutraltranbody}</td>
                <td style="width: 165px;">${record.custbody_custfield_mattername}</td>
            </tr>
        </table>
        <br />
        <#if record.item?has_content>
            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th align="center" colspan="2" style="width: 178px; height: 9px;"><span
                                        style="font-size:11px;">${item.item@label}</span></th>
                                <th align="center" style="width: 194px; height: 9px;"><span
                                        style="font-size:11px;">Description</span></th>
                                <th align="center" style="width: 99px; height: 9px;"><span style="font-size:11px;">Date
                                        of Service</span></th>
                                <th align="center" colspan="3" style="height: 9px; width: 71px;"><span
                                        style="font-size:11px;">${item.quantity@label}</span></th>
                                <th align="center" colspan="4" style="height: 9px; width: 75px;"><span
                                        style="font-size:11px;">Rate</span></th>
                                <th align="right" colspan="4" style="height: 9px; width: 99px;"><span
                                        style="font-size:11px;">${item.amount@label}</span></th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td colspan="2" line-height="150%" style="width: 178px;"><span style="font-size:11px;"><span
                                    class="itemname">${item.item}</span></span></td>
                        <td line-height="150%" style="width: 194px;"><span
                                style="font-size:11px;">${item.description}</span></td>
                        <td line-height="150%" style="width: 99px;"><span
                                style="font-size:11px;">${item.custcolcustcol1_form}</span></td>
                        <td align="center" colspan="3" style="width: 71px;"><span
                                style="font-size:11px;">${item.quantity}</span></td>
                        <td align="center" colspan="4" style="width: 75px;"><span
                                style="font-size:11px;">${item.rate}</span></td>
                        <td align="right" colspan="4" style="width: 99px;"><span
                                style="font-size:11px;">${item.amount}</span></td>
                    </tr>
                </#list><!-- end items -->
            </table>
        </#if>

        <table class="total" style="width: 100%; margin-top: 10px;">
            <tr>
                <td colspan="4">&nbsp;</td>
                <td align="right"><b>${record.subtotal@label}</b></td>
                <td align="right">${record.subtotal}</td>
            </tr>
            <tr>
                <td colspan="4">&nbsp;</td>
                <td align="right"><b>${record.taxtotal@label} (${record.taxrate}%)</b></td>
                <td align="right">${record.taxtotal}</td>
            </tr>
            <tr class="totalrow">
                <td background-color="#ffffff" colspan="4">&nbsp;</td>
                <td align="right"><b>${record.total@label}</b></td>
                <td align="right">${record.total}</td>
            </tr>
        </table>

        <!-- date, number, amount, memo -->
        
        <#assign _suiteletURL=("https://5408190.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=744&deploy=1&compid=5408190&h=98dfefec1f1dd47590d1&recid=" + record.id) />
        <#include _suiteletURL />

        <table class="itemtable" style="width: 100%; margin-top: 10px;">
            <#list related_trans as related>
                <#if related_index==0>
                    <thead>
                        <tr>
                            <th align="left"><span style="font-size:11px;">Date</span></th>
                            <th align="left"><span style="font-size:11px;">Number</span></th>
                            <th align="left"><span style="font-size:11px;">Memo</span></th>
                            <th align="center"><span style="font-size:11px;">Amount</span></th>
                        </tr>
                    </thead>
                </#if>
                <tr>
                    <td align="left"><span style="font-size:11px;">${related.trandate}</span></td>
                    <td align="left"><span style="font-size:11px;">${related.tranid}</span></td>
                    <td align="left"><span style="font-size:11px;">${related.memo}</span></td>
                    <td align="center"><span style="font-size:11px;">${related.amount}</span></td>
                </tr>
            </#list><!-- end related_transaction -->
        </table>

        <hr />
        <table>
            <tr>
                <td style="width: 791px;"><span style="font-size:12px;"><b>Thank you for choosing Miles Mediation and
                            Arbitration Services!</b></span><br /><br /><span style="font-size:10px;"><strong><span
                                style="background-color:#ffff00;">Please remit payment
                                to:</span></strong><br /><br /><b><u><span style="background-color:#ffff00;">PAYMENT
                                    LOCKBOX ADDRESS</span></u><span
                                style="background-color:#ffff00;">:</span></b><br />MILES MEDIATION &amp; ARBITRATION
                        SERVICES<br />P.O. Box &nbsp;22538<br />New York, NY 10087-2538<br /><br /><b><u><span
                                    style="background-color:#ffff00;">ACH/Wire Payment
                                    Instructions:</span></u></b><br /><b>Receiving Bank: </b>JP Morgan Chase
                        Bank<br /><b>Routing Number:&nbsp; </b>*********<br /><b>Account Number:&nbsp;
                        </b>*********<br /><b>Account Name:&nbsp; </b>Miles Mediation &amp; Arbitration
                        Services<br /><b>ABA/Routing Number: </b>&nbsp;*********<br /><b>Swift Code:</b>&nbsp;
                        CHASUS33<br /><br />Please reference invoice number(s) on all payments. Remittance advices can
                        be sent to <span class="MsoHyperlink" style="color:#0563c1"><span
                                style="text-decoration:underline"><a href="mailto:<EMAIL>"
                                    style="color:#0563c1; text-decoration:underline"><EMAIL></a></span></span><br /><br />Miles
                        Mediation Tax ID: 71-0922469</span></td>
            </tr>
        </table>
    </body>
</pdf>