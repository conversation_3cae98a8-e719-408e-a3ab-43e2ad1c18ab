/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/render', 'N/record'], function(https, render, record) {

    function onRequest(context) {
        if(context.request.method == https.Method.GET){
            requestParams = context.request.parameters;
            purchaseOrderId = parseInt(requestParams.custpage_POId);

            // CREATE TEMPLATE RENDERER OBJECT
            var renderer = render.create();

            // SELECT TEMPLATE TO BE USED VIA SCRIPT ID
            renderer.setTemplateByScriptId({
                scriptId: "CUSTTMPL_115_4939393_891"
            });

            // ADD THE RECORD TO BE USED BASED ON PARAMETERS GIVEN
            renderer.addRecord({
                templateName: 'record',
                record: record.load({
                    type: record.Type.PURCHASE_ORDER,
                    id: purchaseOrderId,
                    isDynamic: true
                })
            });

            // RENDER AS STRING
            var pickupSlipXML = renderer.renderAsString();

            // SEND RESPONSE XML TO RENDER AS PDF
            context.response.renderPdf({
                xmlString: pickupSlipXML
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
