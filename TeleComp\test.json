{"openapi": "3.0.1", "info": {"title": "NetSuite REST Record API", "description": "NetSuite REST Record API generated 2021-09-20 at 20:30:55 UTC for account 3499370, user <EMAIL> with role Administrator.", "termsOfService": "http://www.netsuite.com/portal/resource/terms-of-service.shtml", "contact": {"name": "Oracle NetSuite", "url": "http://www.netsuite.com", "email": "<EMAIL>"}, "version": "v1"}, "servers": [{"url": "https://3499370.suitetalk.api.netsuite.com/services/rest/record/v1"}], "security": [{"OAuth_1.0_authorization": []}], "paths": {"/customerPayment": {"get": {"tags": ["CustomerPayment (Beta)"], "summary": "Get list of records.", "parameters": [{"name": "Prefer", "in": "header", "description": "The server behavior requested by the client. Use 'respond-async' to execute the request asynchronously. If the request is executed asynchronously, 'Preference-applied: respond-async' is returned in the response.", "required": false, "schema": {"type": "string", "enum": ["respond-async"]}}, {"name": "q", "in": "query", "description": "The search query that is used to filter results.", "required": false, "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "The limit used to specify the number of results on a single page.", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1000}}, {"name": "offset", "in": "query", "description": "The offset used for selecting a specific page of results.", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}], "responses": {"200": {"description": "List of records.", "content": {"application/vnd.oracle.resource+json; type=collection": {"schema": {"$ref": "#/components/schemas/customerPaymentCollection"}}}}, "default": {"description": "Error response.", "content": {"application/vnd.oracle.resource+json; type=error": {"schema": {"$ref": "#/components/schemas/nsError"}}}}}}, "post": {"tags": ["CustomerPayment (Beta)"], "summary": "Insert record.", "parameters": [{"name": "Prefer", "in": "header", "description": "The server behavior requested by the client. Use 'respond-async' to execute the request asynchronously. If the request is executed asynchronously, 'Preference-applied: respond-async' is returned in the response.", "required": false, "schema": {"type": "string", "enum": ["respond-async"]}}, {"name": "replace", "in": "query", "description": "The names of sublists on this record. All sublist lines will be replaced with lines specified in the request. The sublists not specified here will have lines added to the record. The names are delimited by comma.", "required": false, "schema": {"type": "string"}, "examples": {"multiple sublists": {"value": "sublist1,subrecord.sublist2"}, "sublist on subrecord": {"value": "subrecord.sublist"}, "single sublist": {"value": "sublist"}}}, {"name": "X-NetSuite-PropertyNameValidation", "in": "header", "description": "Sets the strictness of property name validation.", "required": false, "schema": {"type": "string", "enum": ["Error", "Warning", "Ignore"], "default": "Warning"}}, {"name": "X-NetSuite-PropertyValueValidation", "in": "header", "description": "Sets the strictness of property value validation.", "required": false, "schema": {"type": "string", "enum": ["Error", "Warning", "Ignore"], "default": "Error"}}], "requestBody": {"description": "Request body.", "content": {"application/vnd.oracle.resource+json; type=singular": {"schema": {"$ref": "#/components/schemas/customerPayment"}}}, "required": true}, "responses": {"204": {"description": "Inserted record.", "headers": {"X-NetSuite-PropertyValidation": {"description": "The description of a property validation failure.", "required": false, "schema": {"type": "string"}}, "X-NetSuite-Warning": {"description": "Warning messages that occurred during processing.", "required": false, "schema": {"type": "string"}}}}, "default": {"description": "Error response.", "content": {"application/vnd.oracle.resource+json; type=error": {"schema": {"$ref": "#/components/schemas/nsError"}}}}}}}, "/customerPayment/{id}": {"get": {"tags": ["CustomerPayment (Beta)"], "summary": "Get record.", "parameters": [{"name": "Prefer", "in": "header", "description": "The server behavior requested by the client. Use 'respond-async' to execute the request asynchronously. If the request is executed asynchronously, 'Preference-applied: respond-async' is returned in the response.", "required": false, "schema": {"type": "string", "enum": ["respond-async"]}}, {"name": "id", "in": "path", "description": "Internal identifier", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "expandSubResources", "in": "query", "description": "Set to 'true' to automatically expand all sublists, sublist lines, and subrecords on this record.", "required": false, "schema": {"type": "boolean", "default": false}}, {"name": "simpleEnumFormat", "in": "query", "description": "Set to true to return enumeration values in a format that only shows the internal ID value.", "required": false, "schema": {"type": "boolean", "default": false}}, {"name": "fields", "in": "query", "description": "The names of the fields and sublists on the record. Only the selected fields and sublists will be returned in the response.", "required": false, "schema": {"type": "string"}, "examples": {"multiple fields": {"value": "field1,field2"}, "single field": {"value": "field"}}}], "responses": {"200": {"description": "Retrived record.", "headers": {"X-NetSuite-Warning": {"description": "Warning messages that occurred during processing.", "required": false, "schema": {"type": "string"}}}, "content": {"application/vnd.oracle.resource+json; type=singular": {"schema": {"$ref": "#/components/schemas/customerPayment"}}}}, "default": {"description": "Error response.", "content": {"application/vnd.oracle.resource+json; type=error": {"schema": {"$ref": "#/components/schemas/nsError"}}}}}}, "put": {"tags": ["CustomerPayment (Beta)"], "summary": "Insert or update record.", "parameters": [{"name": "Prefer", "in": "header", "description": "The server behavior requested by the client. Use 'respond-async' to execute the request asynchronously. If the request is executed asynchronously, 'Preference-applied: respond-async' is returned in the response.", "required": false, "schema": {"type": "string", "enum": ["respond-async"]}}, {"name": "id", "in": "path", "description": "External identifier.", "required": true, "schema": {"pattern": "eid:(.+)", "type": "string"}}, {"name": "X-NetSuite-PropertyNameValidation", "in": "header", "description": "Sets the strictness of property name validation.", "required": false, "schema": {"type": "string", "enum": ["Error", "Warning", "Ignore"], "default": "Warning"}}, {"name": "X-NetSuite-PropertyValueValidation", "in": "header", "description": "Sets the strictness of property value validation.", "required": false, "schema": {"type": "string", "enum": ["Error", "Warning", "Ignore"], "default": "Error"}}, {"name": "replace", "in": "query", "description": "The names of sublists on this record. All sublist lines will be replaced with lines specified in the request. The sublists not specified here will have lines added to the record. The names are delimited by comma.", "required": false, "schema": {"type": "string"}, "examples": {"multiple sublists": {"value": "sublist1,subrecord.sublist2"}, "sublist on subrecord": {"value": "subrecord.sublist"}, "single sublist": {"value": "sublist"}}}, {"name": "replaceSelectedFields", "in": "query", "description": "If set to 'true', all fields that should be deleted in the update request, including body fields, must be included in the 'replace' query parameter.", "required": false, "schema": {"type": "boolean", "default": false}}], "requestBody": {"description": "Request body.", "content": {"application/vnd.oracle.resource+json; type=singular": {"schema": {"$ref": "#/components/schemas/customerPayment"}}}, "required": true}, "responses": {"204": {"description": "Upserted record.", "headers": {"X-NetSuite-PropertyValidation": {"description": "The description of a property validation failure.", "required": false, "schema": {"type": "string"}}, "X-NetSuite-Warning": {"description": "Warning messages that occurred during processing.", "required": false, "schema": {"type": "string"}}}}, "default": {"description": "Error response.", "content": {"application/vnd.oracle.resource+json; type=error": {"schema": {"$ref": "#/components/schemas/nsError"}}}}}}, "delete": {"tags": ["CustomerPayment (Beta)"], "summary": "Remove record.", "parameters": [{"name": "Prefer", "in": "header", "description": "The server behavior requested by the client. Use 'respond-async' to execute the request asynchronously. If the request is executed asynchronously, 'Preference-applied: respond-async' is returned in the response.", "required": false, "schema": {"type": "string", "enum": ["respond-async"]}}, {"name": "id", "in": "path", "description": "Internal identifier", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"204": {"description": "Removed record."}, "default": {"description": "Error response.", "content": {"application/vnd.oracle.resource+json; type=error": {"schema": {"$ref": "#/components/schemas/nsError"}}}}}}, "patch": {"tags": ["CustomerPayment (Beta)"], "summary": "Update record.", "parameters": [{"name": "Prefer", "in": "header", "description": "The server behavior requested by the client. Use 'respond-async' to execute the request asynchronously. If the request is executed asynchronously, 'Preference-applied: respond-async' is returned in the response.", "required": false, "schema": {"type": "string", "enum": ["respond-async"]}}, {"name": "id", "in": "path", "description": "Internal identifier", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "X-NetSuite-PropertyNameValidation", "in": "header", "description": "Sets the strictness of property name validation.", "required": false, "schema": {"type": "string", "enum": ["Error", "Warning", "Ignore"], "default": "Warning"}}, {"name": "X-NetSuite-PropertyValueValidation", "in": "header", "description": "Sets the strictness of property value validation.", "required": false, "schema": {"type": "string", "enum": ["Error", "Warning", "Ignore"], "default": "Error"}}, {"name": "replace", "in": "query", "description": "The names of sublists on this record. All sublist lines will be replaced with lines specified in the request. The sublists not specified here will have lines added to the record. The names are delimited by comma.", "required": false, "schema": {"type": "string"}, "examples": {"multiple sublists": {"value": "sublist1,subrecord.sublist2"}, "sublist on subrecord": {"value": "subrecord.sublist"}, "single sublist": {"value": "sublist"}}}, {"name": "replaceSelectedFields", "in": "query", "description": "If set to 'true', all fields that should be deleted in the update request, including body fields, must be included in the 'replace' query parameter.", "required": false, "schema": {"type": "boolean", "default": false}}], "requestBody": {"description": "Request body.", "content": {"application/vnd.oracle.resource+json; type=singular": {"schema": {"$ref": "#/components/schemas/customerPayment"}}}, "required": true}, "responses": {"204": {"description": "Updated record.", "headers": {"X-NetSuite-PropertyValidation": {"description": "The description of a property validation failure.", "required": false, "schema": {"type": "string"}}, "X-NetSuite-Warning": {"description": "Warning messages that occurred during processing.", "required": false, "schema": {"type": "string"}}}}, "default": {"description": "Error response.", "content": {"application/vnd.oracle.resource+json; type=error": {"schema": {"$ref": "#/components/schemas/nsError"}}}}}}}, "/customerPayment/{id}/!transform/customerRefund": {"post": {"tags": ["CustomerPayment (Beta)"], "summary": "Transform to customerRefund.", "parameters": [{"name": "Prefer", "in": "header", "description": "The server behavior requested by the client. Use 'respond-async' to execute the request asynchronously. If the request is executed asynchronously, 'Preference-applied: respond-async' is returned in the response.", "required": false, "schema": {"type": "string", "enum": ["respond-async"]}}, {"name": "X-NetSuite-PropertyNameValidation", "in": "header", "description": "Sets the strictness of property name validation.", "required": false, "schema": {"type": "string", "enum": ["Error", "Warning", "Ignore"], "default": "Warning"}}, {"name": "X-NetSuite-PropertyValueValidation", "in": "header", "description": "Sets the strictness of property value validation.", "required": false, "schema": {"type": "string", "enum": ["Error", "Warning", "Ignore"], "default": "Error"}}, {"name": "replace", "in": "query", "description": "The names of sublists on this record. All sublist lines will be replaced with lines specified in the request. The sublists not specified here will have lines added to the record. The names are delimited by comma.", "required": false, "schema": {"type": "string"}, "examples": {"multiple sublists": {"value": "sublist1,subrecord.sublist2"}, "sublist on subrecord": {"value": "subrecord.sublist"}, "single sublist": {"value": "sublist"}}}, {"name": "id", "in": "path", "description": "Internal identifier", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "Request body.", "content": {"application/vnd.oracle.resource+json; type=singular": {"schema": {"$ref": "#/components/schemas/nsResource"}}}, "required": true}, "responses": {"204": {"description": "Transformed record.", "headers": {"X-NetSuite-PropertyValidation": {"description": "The description of a property validation failure.", "required": false, "schema": {"type": "string"}}, "X-NetSuite-Warning": {"description": "Warning messages that occurred during processing.", "required": false, "schema": {"type": "string"}}}, "content": {"application/vnd.oracle.resource+json; type=singular": {"schema": {"$ref": "#/components/schemas/nsResource"}}}}, "default": {"description": "Error response.", "content": {"application/vnd.oracle.resource+json; type=error": {"schema": {"$ref": "#/components/schemas/nsError"}}}}}}}}, "components": {"schemas": {"nsResourceCollection": {"type": "object", "properties": {"links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "totalResults": {"title": "Total Results", "type": "integer", "format": "int64", "readOnly": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/nsResource"}}, "count": {"title": "Count", "type": "integer", "format": "int64", "readOnly": true}, "hasMore": {"title": "Has More Results", "type": "boolean", "readOnly": true}, "offset": {"title": "Query Offset", "type": "integer", "format": "int64", "readOnly": true}}}, "nsResource": {"type": "object", "properties": {"id": {"title": "Internal identifier", "type": "string"}, "refName": {"title": "Reference Name", "type": "string"}, "externalId": {"title": "External identifier", "type": "string"}, "links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}}}, "customerPayment-applyCollection": {"type": "object", "properties": {"links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "totalResults": {"title": "Total Results", "type": "integer", "format": "int64", "readOnly": true}, "count": {"title": "Count", "type": "integer", "format": "int64", "readOnly": true}, "hasMore": {"title": "Has More Results", "type": "boolean", "readOnly": true}, "offset": {"title": "Query Offset", "type": "integer", "format": "int64", "readOnly": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/customerPayment-applyElement"}}}, "description": "This record is available as a beta record.", "x-ns-support-level": "beta"}, "customerPayment-accountingBookDetailElement": {"type": "object", "properties": {"links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "exchangeRate": {"title": "Exchange Rate", "type": "number", "format": "double", "nullable": true}, "refName": {"title": "Reference Name", "type": "string"}, "accountingBook": {"$ref": "#/components/schemas/nsResource"}, "subsidiary": {"$ref": "#/components/schemas/nsResource"}}, "description": "This record is available as a beta record.", "x-ns-filterable": [], "x-ns-support-level": "beta"}, "nsError": {"type": "object", "properties": {"type": {"title": "Type", "type": "string", "description": "A URI reference to the documentation about the problem type.", "format": "URI", "readOnly": true}, "title": {"title": "Title", "type": "string", "description": "A human-readable description of the problem type.", "readOnly": true}, "status": {"title": "Status", "type": "integer", "description": "The HTTP status code generated by the server the request originates from.", "format": "int32", "readOnly": true}, "o:errorDetails": {"type": "array", "description": "An array containing one or more problem types.", "readOnly": true, "items": {"type": "object", "properties": {"detail": {"title": "Detail", "type": "string", "description": "A detailed, human-readable description of the problem occurrence.", "readOnly": true}, "o:errorPath": {"title": "Error Path", "type": "string", "description": "The JSON path that indicates where the problem occurs within the request body.", "format": "JSONPath", "readOnly": true}, "o:errorUrl": {"title": "Error URL", "type": "string", "description": "The URI of the first element in the request URL where the problem occurs.", "format": "URI", "readOnly": true}, "o:errorHeader": {"title": "<PERSON><PERSON><PERSON>", "type": "string", "description": "The name of the HTTP header where the problem occurs.", "readOnly": true}, "o:errorQueryParam": {"title": "Error Query <PERSON>meter", "type": "string", "description": "The name of the query parameter where the problem occurs.", "readOnly": true}, "o:errorCode": {"title": "Error Code", "type": "string", "description": "The application-specific error code. Similar problem types are grouped together.", "readOnly": true}}}}}}, "nsLink": {"type": "object", "properties": {"rel": {"title": "Relationship", "type": "string", "readOnly": true}, "href": {"title": "Hypertext Reference", "type": "string", "readOnly": true}}}, "customerPayment-accountingBookDetailCollection": {"type": "object", "properties": {"links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "totalResults": {"title": "Total Results", "type": "integer", "format": "int64", "readOnly": true}, "count": {"title": "Count", "type": "integer", "format": "int64", "readOnly": true}, "hasMore": {"title": "Has More Results", "type": "boolean", "readOnly": true}, "offset": {"title": "Query Offset", "type": "integer", "format": "int64", "readOnly": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/customerPayment-accountingBookDetailElement"}}}, "description": "This record is available as a beta record.", "x-ns-support-level": "beta"}, "customerPayment-creditCollection": {"type": "object", "properties": {"links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "totalResults": {"title": "Total Results", "type": "integer", "format": "int64", "readOnly": true}, "count": {"title": "Count", "type": "integer", "format": "int64", "readOnly": true}, "hasMore": {"title": "Has More Results", "type": "boolean", "readOnly": true}, "offset": {"title": "Query Offset", "type": "integer", "format": "int64", "readOnly": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/customerPayment-creditElement"}}}, "description": "This record is available as a beta record.", "x-ns-support-level": "beta"}, "customerPaymentCollection": {"type": "object", "properties": {"totalResults": {"title": "Total Results", "type": "integer", "format": "int64", "readOnly": true}, "items": {"title": "Items", "type": "array", "description": "An array field that represents a collection of elements, for example, sublist lines, multiselect items, or search results.", "items": {"$ref": "#/components/schemas/customerPayment"}}, "links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "count": {"title": "Count", "type": "integer", "format": "int64", "readOnly": true}, "hasMore": {"title": "Has More Results", "type": "boolean", "readOnly": true}, "offset": {"title": "Query Offset", "type": "integer", "format": "int64", "readOnly": true}}}, "customerPayment-depositCollection": {"type": "object", "properties": {"links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "totalResults": {"title": "Total Results", "type": "integer", "format": "int64", "readOnly": true}, "count": {"title": "Count", "type": "integer", "format": "int64", "readOnly": true}, "hasMore": {"title": "Has More Results", "type": "boolean", "readOnly": true}, "offset": {"title": "Query Offset", "type": "integer", "format": "int64", "readOnly": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/customerPayment-depositElement"}}}, "description": "This record is available as a beta record.", "x-ns-support-level": "beta"}, "customerPayment": {"type": "object", "properties": {"autoApply": {"title": "Auto Apply", "type": "boolean", "description": "Check this box to automatically apply payments to the oldest open receivable. If you clear this box, the payment amount defaults to unapplied and you must later apply the payment amount to an open receivable. Note: When you enter a customer payment and check the Auto Apply box, only the payment you enter on the transaction is applied. Other existing credits and deposits are not automatically applied against open receivables. Once you check or uncheck the Auto Apply box on a payment and save it, the box retains the setting the next time you enter a payment."}, "custbody_suretax_totaltax": {"title": "Total Tax", "type": "string", "nullable": true, "x-ns-custom-field": true}, "outputAuthCode": {"title": "Auth. Code", "type": "string", "nullable": true}, "custbody_mes_settlement_discrepancy": {"title": "Credit Card Settlement Discrepancy", "type": "number", "format": "double", "nullable": true, "x-ns-custom-field": true}, "custbody_suretax_body_discrate_ap": {"title": "SureTax - Discount Rate (A/P)", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_mes_reconciliation_complete": {"title": "Credit Card Settlement Data Complete", "type": "boolean", "x-ns-custom-field": true}, "clearedDate": {"title": "Date Cleared", "type": "string", "format": "date", "nullable": true}, "handlingMode": {"type": "object", "properties": {"id": {"title": "Internal identifier", "type": "string", "enum": ["MIMIC", "PROCESS", "SAVE_ONLY"]}, "refName": {"title": "Reference Name", "type": "string"}}}, "custbody_mes_invl_postback_link": {"title": "MerchantE InvoiceLink PostBack Link", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_suretax_body_bitm_discrate_ap": {"title": "SureTax - Billable Item Discount Rate (A/P)", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_sii_land_register": {"title": "Land register/Cadaster reference", "type": "string", "nullable": true, "x-ns-custom-field": true}, "debitPinBlock": {"title": "Debit Pin Block", "type": "string", "nullable": true}, "custbody_stc_tax_after_discount": {"title": "Tax after discount", "type": "number", "format": "double", "nullable": true, "x-ns-custom-field": true}, "custbody_link_lsa": {"title": "LSA Link", "type": "string", "nullable": true, "x-ns-custom-field": true}, "createdDate": {"title": "Date Created", "type": "string", "format": "date-time", "nullable": true}, "integrationId": {"type": "string", "nullable": true}, "custbody_nx_customer_signature": {"title": "NextService Customer Signature", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_adjustment_journal": {"title": "Adjustment Journal", "type": "boolean", "x-ns-custom-field": true}, "custbody_mes_invl_end_customer_link": {"title": "MerchantE InvoiceLink End Customer Link", "type": "string", "nullable": true, "x-ns-custom-field": true}, "paymentInstrumentLimit": {"title": "Payment Instrument Limit", "type": "number", "format": "double", "nullable": true}, "custbody_link_name_lsa": {"title": "LSA Link Name", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_establishment_code": {"title": "Establishment Code", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_country_of_origin": {"title": "Country of Origin", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_sii_not_reported_in_time": {"title": "Billing Record Cannot Be Submitted On Time", "type": "boolean", "x-ns-custom-field": true}, "custbody_mes_invl_sys_notes": {"title": "MerchantE InvoiceLink System Notes", "type": "string", "nullable": true, "x-ns-custom-field": true}, "paymentCardCsc": {"title": "CSC", "type": "string", "nullable": true}, "prevDate": {"title": "Previous Effective Date", "type": "string", "format": "date", "nullable": true}, "custbody_itr_nexus": {"title": "ITR Nexus", "type": "string", "nullable": true, "x-ns-custom-field": true}, "customForm": {"type": "object", "properties": {"id": {"title": "Internal identifier", "type": "string", "enum": ["176"]}, "refName": {"title": "Reference Name", "type": "string"}}}, "custbody_report_timestamp": {"title": "Report Timestamp", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_suretax_latest_transid_ap": {"title": "Latest SureTax Transaction Id (A/P)", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_sii_is_third_party": {"title": "Issued By Third Party", "type": "boolean", "x-ns-custom-field": true}, "custbody_sii_registration_msg": {"title": "Registration Message", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_mes_invlcf_disp_req_rsl_alert": {"title": "MerchantE InvoiceLink Display RX Alert?", "type": "boolean", "x-ns-custom-field": true}, "externalId": {"title": "External ID", "type": "string", "nullable": true}, "custbody_ctc_cw_inv_number": {"title": "CW Invoice Number", "type": "string", "nullable": true, "x-ns-custom-field": true}, "toBeEmailed": {"title": "To Be Emailed", "type": "boolean"}, "custbody_mes_settlement_batch": {"title": "Credit Card Batch", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_sii_article_72_73": {"title": "Simplified Invoice Article 7.2 7.3", "type": "boolean", "x-ns-custom-field": true}, "cardSwipe": {"title": "Card Swipe", "type": "string", "nullable": true}, "cleared": {"title": "Cleared", "type": "boolean"}, "custbody_stc_payment_transaction_id": {"title": "Payment Transaction ID", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_suretax_latest_transid": {"title": "Latest SureTax Transaction Id", "type": "string", "nullable": true, "x-ns-custom-field": true}, "applied": {"title": "Applied", "type": "number", "description": "NetSuite keeps track of the total applied payments here. You can apply payments by placing or clearing check marks in the Apply column next to the appropriate invoices at the bottom of the page. If you want, you can change dollar amounts in the Payment column next to these invoices. When you do this, the Applied field automatically updates.", "format": "double", "nullable": true}, "exchangeRate": {"title": "Exchange Rate", "type": "number", "description": "The currency&apos;s exchange rate is shown in this field. You can edit the exchange rate for this transaction only, or you can update the currency record with the exchange rate you enter here.", "format": "double", "nullable": true}, "custbody_suretax_created_from": {"title": "Created From String", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_itr_doc_number": {"title": "Document Number", "type": "string", "nullable": true, "x-ns-custom-field": true}, "pending": {"title": "Pending", "type": "number", "description": "If your customer has unapproved credit card payments, NetSuite automatically fills in the sum amount of these payments here. If you validate credit card payments outside of NetSuite, these payments will be pending until they are approved. Once payments are approved, check the Card Approved box.", "format": "double", "nullable": true}, "originator": {"title": "Originator", "type": "string", "nullable": true}, "custbody_suretax_totaltax_ap": {"title": "Total Tax (A/P)", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_esc_created_date": {"title": "Created Date", "type": "string", "format": "date", "nullable": true, "x-ns-custom-field": true}, "custbody_mes_invl_hpp_link": {"title": "MerchantE InvoiceLink HPP Link", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_sii_operation_date": {"title": "Operation Date", "type": "string", "format": "date", "nullable": true, "x-ns-custom-field": true}, "custbody10": {"title": "# of Seats", "type": "integer", "format": "int64", "nullable": true, "x-ns-custom-field": true}, "custbody12": {"title": "Total Contract Value", "type": "number", "format": "double", "nullable": true, "x-ns-custom-field": true}, "custbody_mes_settlement_amount": {"title": "Credit Card Settled Amount", "type": "number", "format": "double", "nullable": true, "x-ns-custom-field": true}, "custbody_suretax_group_like_taxes": {"title": "Group Like Taxes", "type": "boolean", "x-ns-custom-field": true}, "unapplied": {"title": "Unapplied", "type": "number", "description": "NetSuite shows the difference between amounts available to apply to payments and payments you have actually applied. To make this equal to 0.00, you can (1) change the Payment Amount above, (2) check the Apply Existing Credits check box above, or (3) change Payment amounts below.", "format": "double", "nullable": true}, "custbody1": {"title": "Opportunity Name", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody2": {"title": "Projected Gross Profit", "type": "number", "format": "double", "nullable": true, "x-ns-custom-field": true}, "custbody3": {"title": "Message", "type": "string", "nullable": true, "x-ns-custom-field": true}, "tranDate": {"title": "Date", "type": "string", "description": "NetSuite inserts today&apos;s date for the posting date of this payment. You can enter or select another date. What you enter here determines the date range in which this transaction appears on the Accounts Receivable Register.", "format": "date", "nullable": true}, "memo": {"title": "Memo", "type": "string", "description": "If you wish, enter a memo to describe this payment. It will appear on reports such as the 2-line Accounts Receivable register that NetSuite merchants and their guest-customers can see (if given permission to log in and view their transaction history).", "nullable": true}, "custbody_mes_anticipated_depositdate": {"title": "Credit Card Deposit Anticipated", "type": "string", "nullable": true, "x-ns-custom-field": true}, "balance": {"title": "Current Balance", "type": "number", "description": "NetSuite shows you the balance in this customer account.", "format": "double", "nullable": true}, "custbody_date_of_taxable_supply": {"title": "Date of Taxable Supply", "type": "string", "format": "date", "nullable": true, "x-ns-custom-field": true}, "payment": {"title": "Payment Amount", "type": "number", "description": "Enter the currency amount of the payment here.", "format": "double", "nullable": true}, "id": {"title": "Internal ID", "type": "string", "nullable": true}, "custbody_suretax_sh_enablesuretax": {"title": "S&H Enable CCH® SureTax®", "type": "boolean", "x-ns-custom-field": true}, "custbody_cash_register": {"title": "Cash Register", "type": "boolean", "x-ns-custom-field": true}, "custbody5": {"title": "Account", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody6": {"title": "WIN/LOSS REASON", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_suretax_body_btim_discrate_ap": {"title": "SureTax - Billable Time Discount (A/P)", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody7": {"title": "Quote Type", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody8": {"title": "Serial Number", "type": "string", "nullable": true, "x-ns-custom-field": true}, "status": {"type": "object", "properties": {"id": {"title": "Internal identifier", "type": "string", "enum": ["A", "R", "B", "C"]}, "refName": {"title": "Reference Name", "type": "string"}}}, "custbody_stc_total_after_discount": {"title": "Total after discount", "type": "number", "format": "double", "nullable": true, "x-ns-custom-field": true}, "custbody_suretax_sh_handtaxamt": {"title": "CCH® SureTax® Handling Tax Amount", "type": "number", "format": "double", "nullable": true, "x-ns-custom-field": true}, "custbody_ctc_cw_invnumber": {"title": "Connectwise Invoice Number", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_my_import_declaration_num": {"title": "Import Declaration No.", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_mes_invl_sendto_invl": {"title": "MerchantE InvoiceLink Send To InvoiceLink", "type": "boolean", "x-ns-custom-field": true}, "paymentOperation": {"type": "object", "properties": {"id": {"title": "Internal identifier", "type": "string", "enum": ["SALE", "CAPTURE"]}, "refName": {"title": "Reference Name", "type": "string"}}}, "custbody_mes_invl_guid": {"title": "MerchantE InvoiceLink GUID", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_document_date": {"title": "Document Date", "type": "string", "format": "date", "nullable": true, "x-ns-custom-field": true}, "paymentDeviceId": {"title": "Payment Device ID", "type": "string", "nullable": true}, "custbody_ctc_cw_po_number": {"title": "CW PO Number", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_stc_discountpercent": {"title": "Discount", "type": "number", "format": "float", "nullable": true, "x-ns-custom-field": true}, "custbody_esc_last_modified_date": {"title": "Last Modified Date", "type": "string", "format": "date", "nullable": true, "x-ns-custom-field": true}, "custbody_sii_invoice_date": {"title": "Invoice Date", "type": "string", "format": "date", "nullable": true, "x-ns-custom-field": true}, "custbody_stc_amount_after_discount": {"title": "Amount after discount", "type": "number", "format": "double", "nullable": true, "x-ns-custom-field": true}, "custbody_sii_accounting_date": {"title": "Accounting Date", "type": "string", "format": "date", "nullable": true, "x-ns-custom-field": true}, "custbody_createdfrom_expensify": {"title": "Created From", "type": "string", "nullable": true, "x-ns-custom-field": true}, "undepFunds": {"type": "object", "properties": {"id": {"title": "Internal identifier", "type": "string"}, "refName": {"title": "Reference Name", "type": "string"}}}, "custbody_suretax_disc_amount": {"title": "Discount Amount", "type": "number", "format": "float", "nullable": true, "x-ns-custom-field": true}, "custbody_mes_invl_location_zip": {"title": "MerchantE InvoiceLink Location Zip", "type": "string", "nullable": true, "x-ns-custom-field": true}, "debitKsn": {"title": "Debit KSN", "type": "string", "nullable": true}, "custbody_mes_settlement_date": {"title": "Credit Card Settlement Date", "type": "string", "format": "date", "nullable": true, "x-ns-custom-field": true}, "custbody_mes_actual_depositdate": {"title": "Credit Card Deposit Date", "type": "string", "format": "date", "nullable": true, "x-ns-custom-field": true}, "custbody_4110_customregnum": {"title": "Customs Registration Number", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_mes_invl_uuid": {"title": "MerchantE InvoiceLink UUID", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_sii_ref_no": {"title": "Reference No.", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_4599_sg_import_permit_num": {"title": "Import Permit No.", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_stc_daysuntilexpiry": {"title": "Days Until Expiry", "type": "integer", "format": "int64", "nullable": true, "x-ns-custom-field": true}, "custbody_suretax_body_discrate": {"title": "SureTax - Discount Rate", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_me_invlink_statement_ref": {"title": "MerchantE InvLink Statement Ref", "type": "string", "nullable": true, "x-ns-custom-field": true}, "checkNumber": {"title": "Check #", "type": "string", "nullable": true}, "custbody_sii_external_reference": {"title": "External Reference", "type": "string", "nullable": true, "x-ns-custom-field": true}, "outputReferenceCode": {"title": "P/N Ref.", "type": "string", "nullable": true}, "excludeFromGLNumbering": {"title": "Exclude from GL Audit Numbering", "type": "boolean"}, "custbody_mes_invl_externalid": {"title": "MerchantE InvoiceLink External ID", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_suretax_body_bexp_discrate_ap": {"title": "SureTax - Billable Expense Discount Rate (A/P)", "type": "string", "nullable": true, "x-ns-custom-field": true}, "total": {"title": "To Apply", "type": "number", "description": "NetSuite adds the payment amount from above plus any credits you have chosen to apply and displays the total here.", "format": "double", "nullable": true}, "custbody_suretax_sh_shiptaxamt": {"title": "CCH® SureTax® Shipping Tax Amount", "type": "number", "format": "double", "nullable": true, "x-ns-custom-field": true}, "custbody_sii_article_61d": {"title": "Invoice With No Counterpart Article 6.1.d", "type": "boolean", "x-ns-custom-field": true}, "custbody_prod_view": {"title": "InsideView Container", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_nondeductible_processed": {"title": "Non-Deductible Tax Adjusted", "type": "boolean", "x-ns-custom-field": true}, "custbody_mes_invl_track_num": {"title": "MerchantE InvoiceLink Tracking Number", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_date_lsa": {"title": "Last Sales Activity", "type": "string", "format": "date", "nullable": true, "x-ns-custom-field": true}, "tranId": {"title": "Payment #", "type": "string", "description": "This is the document number automatically generated for the transaction by NetSuite.", "nullable": true}, "custbody_counterparty_vat": {"title": "Counterparty VAT Number", "type": "string", "nullable": true, "x-ns-custom-field": true}, "custbody_refno_originvoice": {"title": "Reference No. of Original Invoice", "type": "string", "nullable": true, "x-ns-custom-field": true}, "lastModifiedDate": {"title": "Date Last Modified", "type": "string", "format": "date-time", "nullable": true}, "custbody_mes_depost_reference": {"title": "Credit Card Dep Ref", "type": "string", "nullable": true, "x-ns-custom-field": true}, "inputReferenceCode": {"title": "Input P/N Ref.", "type": "string", "nullable": true}, "custbody_mes_deposit_created": {"title": "Credit Card Deposit Created", "type": "boolean", "x-ns-custom-field": true}, "custbody_suretax_disc_amount_ap": {"title": "Discount Amount (A/P)", "type": "number", "format": "float", "nullable": true, "x-ns-custom-field": true}, "refName": {"title": "Reference Name", "type": "string"}, "links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "custbody_ctc_cw_inv_link": {"$ref": "#/components/schemas/nsResource"}, "custbody_op_lob": {"$ref": "#/components/schemas/nsResource"}, "custbody_suretax_sh_transtypecode": {"$ref": "#/components/schemas/nsResource"}, "paymentOption": {"$ref": "#/components/schemas/nsResource"}, "credit": {"$ref": "#/components/schemas/customerPayment-creditCollection"}, "custbody_nexus_notc": {"$ref": "#/components/schemas/nsResource"}, "custbody11": {"$ref": "#/components/schemas/nsResourceCollection"}, "custbody_ctc_cw_inv_link_multi": {"$ref": "#/components/schemas/nsResourceCollection"}, "custbody_nx_case": {"$ref": "#/components/schemas/nsResource"}, "custbody13": {"$ref": "#/components/schemas/nsResourceCollection"}, "custbody_ctc_cw_po_link": {"$ref": "#/components/schemas/nsResource"}, "custbody_suretax_trans_ltcalllog": {"$ref": "#/components/schemas/nsResource"}, "custbody_nondeductible_ref_genjrnl": {"$ref": "#/components/schemas/nsResource"}, "custbody_suretax_trans_ltcalllog_ap": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_property_location": {"$ref": "#/components/schemas/nsResource"}, "cardholderAuthentication": {"$ref": "#/components/schemas/nsResource"}, "custbody_nx_projecttask": {"$ref": "#/components/schemas/nsResource"}, "customer": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_intra_txn_type": {"$ref": "#/components/schemas/nsResource"}, "custbody_suretax_sh_taxincludedcode": {"$ref": "#/components/schemas/nsResource"}, "custbody_vendorlink": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_orig_bill": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_spcl_scheme_code_sales": {"$ref": "#/components/schemas/nsResource"}, "department": {"$ref": "#/components/schemas/nsResource"}, "currency": {"$ref": "#/components/schemas/nsResource"}, "custbody_ctc_cw_agreement_link": {"$ref": "#/components/schemas/nsResource"}, "custbody_regime_code": {"$ref": "#/components/schemas/nsResource"}, "custbody_suretax_sh_salestypecode": {"$ref": "#/components/schemas/nsResource"}, "custbody_mes_invl_config": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_registration_status": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_issued_inv_type": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_correction_type": {"$ref": "#/components/schemas/nsResource"}, "custbody_mes_invl_related_tran": {"$ref": "#/components/schemas/nsResource"}, "custbody_mes_inv_quote_logic_ovr": {"$ref": "#/components/schemas/nsResource"}, "custbody_esc_campaign_category": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_registration_code": {"$ref": "#/components/schemas/nsResource"}, "custbody_4599_mx_operation_type": {"$ref": "#/components/schemas/nsResource"}, "apply": {"$ref": "#/components/schemas/customerPayment-applyCollection"}, "custbody_nx_asset": {"$ref": "#/components/schemas/nsResource"}, "location": {"$ref": "#/components/schemas/nsResource"}, "class": {"$ref": "#/components/schemas/nsResource"}, "paymentProcessingProfile": {"$ref": "#/components/schemas/nsResource"}, "custbody_suretax_sh_exemptcode": {"$ref": "#/components/schemas/nsResource"}, "account": {"$ref": "#/components/schemas/nsResource"}, "custbody_suretax_sh_taxsitusrule": {"$ref": "#/components/schemas/nsResource"}, "custbody_notc": {"$ref": "#/components/schemas/nsResource"}, "custbody_mode_of_transport": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_received_inv_type": {"$ref": "#/components/schemas/nsResource"}, "custbody_nx_customer": {"$ref": "#/components/schemas/nsResource"}, "custbody_mes_ach_auth_type": {"$ref": "#/components/schemas/nsResource"}, "custbody_suretax_sh_regcode": {"$ref": "#/components/schemas/nsResource"}, "custbody_regime_code_of_supply": {"$ref": "#/components/schemas/nsResource"}, "custbody_nx_task": {"$ref": "#/components/schemas/nsResource"}, "custbody_delivery_terms": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_exempt_details": {"$ref": "#/components/schemas/nsResource"}, "deposit": {"$ref": "#/components/schemas/customerPayment-depositCollection"}, "custbody_nondeductible_ref_tran": {"$ref": "#/components/schemas/nsResourceCollection"}, "custbody9": {"$ref": "#/components/schemas/nsResourceCollection"}, "aracct": {"$ref": "#/components/schemas/nsResource"}, "custbody4": {"$ref": "#/components/schemas/nsResource"}, "postingPeriod": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_orig_invoice": {"$ref": "#/components/schemas/nsResource"}, "custbody_suretax_sh_unittype": {"$ref": "#/components/schemas/nsResource"}, "custbody_suretax_handling_trantypecode": {"$ref": "#/components/schemas/nsResource"}, "accountingBookDetail": {"$ref": "#/components/schemas/customerPayment-accountingBookDetailCollection"}, "custbody_transaction_region": {"$ref": "#/components/schemas/nsResource"}, "subsidiary": {"$ref": "#/components/schemas/nsResource"}, "custbody_sii_spcl_scheme_code_purchase": {"$ref": "#/components/schemas/nsResource"}}, "description": "This record is available as a beta record.", "x-ns-filterable": ["custbody_suretax_handling_trantypecode", "custbody_suretax_totaltax", "custbody_sii_property_location", "custbody_nondeductible_ref_tran", "custbody_suretax_body_discrate_ap", "custbody_esc_campaign_category", "custbody_mes_reconciliation_complete", "custbody_mes_invl_config", "custbody_mes_invl_postback_link", "custbody_suretax_body_bitm_discrate_ap", "custbody_nx_case", "custbody_sii_land_register", "custbody_sii_exempt_details", "postingPeriod", "custbody_sii_orig_invoice", "custbody_stc_tax_after_discount", "custbody_link_lsa", "createdDate", "custbody_nx_customer_signature", "custbody_ctc_cw_po_link", "custbody_adjustment_journal", "currency", "custbody_mes_invl_end_customer_link", "custbody_link_name_lsa", "custbody_establishment_code", "custbody_country_of_origin", "custbody_sii_not_reported_in_time", "custbody_mes_invl_sys_notes", "custbody_itr_nexus", "custbody_report_timestamp", "custbody_suretax_latest_transid_ap", "custbody_regime_code_of_supply", "custbody_sii_is_third_party", "custbody_sii_registration_msg", "externalId", "custbody_suretax_trans_ltcalllog", "custbody_ctc_cw_inv_number", "custbody_suretax_sh_regcode", "custbody_regime_code", "custbody_ctc_cw_inv_link", "custbody_suretax_sh_exemptcode", "custbody_mes_settlement_batch", "custbody_4599_mx_operation_type", "custbody_sii_article_72_73", "custbody_op_lob", "custbody_stc_payment_transaction_id", "custbody_suretax_latest_transid", "exchangeRate", "custbody_suretax_created_from", "custbody_itr_doc_number", "custbody_transaction_region", "custbody_suretax_totaltax_ap", "custbody_nondeductible_ref_genjrnl", "custbody_sii_orig_bill", "custbody_vendorlink", "custbody_mes_invl_hpp_link", "custbody_suretax_trans_ltcalllog_ap", "custbody_sii_issued_inv_type", "custbody_sii_registration_status", "custbody_sii_correction_type", "custbody_sii_operation_date", "custbody_notc", "custbody11", "custbody10", "custbody13", "custbody12", "custbody_mes_settlement_amount", "custbody_suretax_group_like_taxes", "custbody1", "custbody2", "custbody3", "custbody4", "tranDate", "memo", "custbody_sii_spcl_scheme_code_purchase", "custbody_nx_projecttask", "custbody_mes_anticipated_depositdate", "custbody_date_of_taxable_supply", "custbody_suretax_sh_transtypecode", "id", "custbody_nexus_notc", "paymentOption", "custbody_suretax_sh_enablesuretax", "custbody_cash_register", "custbody5", "custbody6", "custbody_suretax_body_btim_discrate_ap", "custbody7", "custbody_ctc_cw_inv_link_multi", "custbody8", "custbody9", "custbody_sii_spcl_scheme_code_sales", "custbody_nx_customer", "custbody_mes_ach_auth_type", "custbody_sii_intra_txn_type", "custbody_stc_total_after_discount", "custbody_suretax_sh_handtaxamt", "custbody_mes_inv_quote_logic_ovr", "custbody_ctc_cw_invnumber", "custbody_my_import_declaration_num", "custbody_sii_registration_code", "custbody_mes_invl_sendto_invl", "custbody_mes_invl_guid", "custbody_document_date", "custbody_mes_invl_related_tran", "custbody_ctc_cw_po_number", "custbody_sii_invoice_date", "custbody_stc_amount_after_discount", "custbody_nx_task", "custbody_sii_accounting_date", "custbody_createdfrom_expensify", "custbody_suretax_sh_salestypecode", "custbody_suretax_disc_amount", "custbody_suretax_sh_taxsitusrule", "custbody_mes_settlement_date", "custbody_mes_actual_depositdate", "custbody_4110_customregnum", "custbody_mes_invl_uuid", "custbody_sii_ref_no", "custbody_nx_asset", "custbody_4599_sg_import_permit_num", "custbody_suretax_sh_unittype", "custbody_suretax_body_discrate", "custbody_delivery_terms", "custbody_me_invlink_statement_ref", "custbody_sii_external_reference", "custbody_mes_invl_externalid", "custbody_suretax_body_bexp_discrate_ap", "custbody_mode_of_transport", "custbody_suretax_sh_shiptaxamt", "custbody_sii_article_61d", "custbody_nondeductible_processed", "custbody_sii_received_inv_type", "custbody_mes_invl_track_num", "custbody_date_lsa", "tranId", "custbody_counterparty_vat", "custbody_refno_originvoice", "lastModifiedDate", "custbody_mes_depost_reference", "custbody_mes_deposit_created", "custbody_suretax_disc_amount_ap", "custbody_suretax_sh_taxincludedcode"], "x-ns-support-level": "beta"}, "customerPayment-depositElement": {"type": "object", "properties": {"links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "depositDate": {"title": "Date", "type": "string", "format": "date", "nullable": true}, "total": {"title": "Orig. Amt.", "type": "number", "format": "double", "nullable": true}, "refNum": {"title": "Ref No.", "type": "string", "nullable": true}, "apply": {"title": "Apply", "type": "boolean"}, "remaining": {"title": "Amount Re<PERSON>ining", "type": "number", "format": "double", "nullable": true}, "refName": {"title": "Reference Name", "type": "string"}}, "description": "This record is available as a beta record.", "x-ns-filterable": [], "x-ns-support-level": "beta"}, "customerPayment-creditElement": {"type": "object", "properties": {"links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "amount": {"title": "Credit", "type": "number", "format": "double", "nullable": true}, "total": {"title": "Orig. Amt.", "type": "number", "format": "double", "nullable": true}, "creditDate": {"title": "Date", "type": "string", "format": "date", "nullable": true}, "refNum": {"title": "Ref No.", "type": "string", "nullable": true}, "apply": {"title": "Apply", "type": "boolean"}, "due": {"title": "Amount Re<PERSON>ining", "type": "number", "format": "double", "nullable": true}, "createdFrom": {"type": "string", "nullable": true}, "line": {"type": "integer", "format": "int64", "nullable": true}, "appliedTo": {"title": "Applied To", "type": "string", "nullable": true}, "type": {"title": "Type", "type": "string", "nullable": true}, "refName": {"title": "Reference Name", "type": "string"}, "doc": {"$ref": "#/components/schemas/nsResource"}}, "description": "This record is available as a beta record.", "x-ns-filterable": [], "x-ns-support-level": "beta"}, "customerPayment-applyElement": {"type": "object", "properties": {"links": {"title": "Links", "type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/nsLink"}}, "discDate": {"title": "Disc. Date", "type": "string", "format": "date", "nullable": true}, "amount": {"title": "Payment", "type": "number", "format": "double", "nullable": true}, "apply": {"title": "Apply", "type": "boolean"}, "line": {"type": "integer", "format": "int64", "nullable": true}, "createdFrom": {"type": "string", "nullable": true}, "type": {"title": "Type", "type": "string", "nullable": true}, "discAmt": {"title": "Disc. Avail.", "type": "number", "format": "double", "nullable": true}, "total": {"title": "Orig. Amt.", "type": "number", "format": "double", "nullable": true}, "applyDate": {"title": "Date", "type": "string", "format": "date", "nullable": true}, "refNum": {"title": "Ref No.", "type": "string", "nullable": true}, "due": {"title": "Amt. Due", "type": "number", "format": "double", "nullable": true}, "disc": {"title": "Disc. Taken", "type": "number", "format": "double", "nullable": true}, "refName": {"title": "Reference Name", "type": "string"}, "doc": {"$ref": "#/components/schemas/nsResource"}}, "description": "This record is available as a beta record.", "x-ns-filterable": [], "x-ns-support-level": "beta"}}, "securitySchemes": {"OAuth_1.0_authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "OAuth 1.0 - https://tools.ietf.org/html/rfc5849", "name": "authorization", "in": "header"}}}}