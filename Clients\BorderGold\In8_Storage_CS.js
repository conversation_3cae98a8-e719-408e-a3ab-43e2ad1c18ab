function search() {

    if (!nlapiGetFieldValue('custpage_start_date') ||
        !nlapiGetFieldValue('custpage_end_date') ||
        !nlapiGetFieldValue('custpage_au') ||
        !nlapiGetFieldValue('custpage_ag') ||
        !nlapiGetFieldValue('custpage_pt') ||
        !nlapiGetFieldValue('custpage_pd') ||
        !nlapiGetFieldValue('custpage_cad')) {

        alert('Enter the required fields.');
        return;
    }

    //alert('search');
    setWindowChanged(window, false);

    var url = nlapiResolveURL('SUITELET', 'customscript_in8_storage_sl', 'customdeploy1');

    url += '&custpage_start_date=' + nlapiGetFieldValue('custpage_start_date') +
        '&custpage_end_date=' + nlapiGetFieldValue('custpage_end_date') +
        '&custpage_au=' + nlapiGetFieldValue('custpage_au') +
        '&custpage_ag=' + nlapiGetFieldValue('custpage_ag') +
        '&custpage_pt=' + nlapiGetFieldValue('custpage_pt') +
        '&custpage_pd=' + nlapiGetFieldValue('custpage_pd') +
        '&custpage_cad=' + nlapiGetFieldValue('custpage_cad') +
        '&search=T';

    window.location.href = url;
}

function saveRecord() {

    var hasSelected = false;

    for (var i = 1; i <= nlapiGetLineItemCount('custpage_sublist'); i++) {
        if (nlapiGetLineItemValue('custpage_sublist', 'custpage_selected', i) == 'T') {
            hasSelected = true;
            break;
        }
    }
    if (!hasSelected) {
        alert('Select at least one customer to process.');
        return false;
    }
    if (confirm('Confirm?')) {
        return true;
    }
    return false;
}

function pageInit() {
    for (var i = 1; i <= nlapiGetLineItemCount('custpage_sublist'); i++) {
        if (!nlapiGetLineItemValue('custpage_sublist', 'custpage_entityid', i)) {
            nlapiDisableLineItemField('custpage_sublist', 'custpage_selected' + i, true);
            nlapiDisableLineItemField('custpage_sublist', 'custpage_usd' + i, true);
            nlapiDisableLineItemField('custpage_sublist', 'custpage_cad' + i, true);
        }
    }
}