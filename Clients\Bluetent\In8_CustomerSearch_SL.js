var secretKey = 'ac8420dd-5069-4877-8d19-c017c12706b2';
var id = '';
function suitelet(request, response)
{

    if(request.getMethod() == "GET")
    {
        id = request.getParameter('internalid');
        var hash = request.getParameter('hash');

        var hashCompare = String(CryptoJS.SHA256(secretKey + id));

        if (!id || hash != hashCompare) {

            var form = nlapiCreateForm("Error");

            var field = form.addField('custpage_message', 'inlinehtml', 'Message');
            field.setDefaultValue("<h2>Invalid parameters: " + hash + "</h2>");

            response.writePage(form);
            return;
        }

        var accessLevel = checkAccess(id);

        if(accessLevel == 3)
        {
            var form = nlapiCreateForm("Error");

            var field = form.addField('custpage_message', 'inlinehtml', 'Message');
            field.setDefaultValue("<h2>Invalid permission. User Id: " + id + "</h2>");

            response.writePage(form);
            return;
        }

        var form = nlapiCreateForm("");
        form.setScript('customscript_in8_externalform_cs');

        form.addFieldGroup('custpage_searchgroup', "Filters");

        form.addField('custpage_companyname', 'text', 'Company Name');
        var accessLevelField = form.addField('custpage_accesslevel', 'text', 'Access Level');
        accessLevelField.setDefaultValue(accessLevel);
        accessLevelField.setDisplayType('hidden');

        var employeeField = form.addField('custpage_employee', 'text', 'Employee');
        employeeField.setDefaultValue(id);
        employeeField.setDisplayType('hidden');

        var hashField = form.addField('custpage_hash', 'text', 'Hash');
        hashField.setDefaultValue(hash);
        hashField.setDisplayType('hidden');

        form.addField('custpage_manual', 'inlinehtml', '').setDefaultValue('<a href="https://tstdrv1330281.app.netsuite.com/core/media/media.nl?id=48082&c=TSTDRV1330281&h=4c7df63a11df199b1742&_xt=.pdf">Check the documentation for help!</a>');

        form.addSubmitButton('Search');

        form = createSavedSearchSublist(form);

        response.writePage(form);
    }
    else
    {
        var filters = [];

        if(request.getParameter('custpage_companyname'))
            filters.push(new nlobjSearchFilter('companyname', null, 'contains', request.getParameter('custpage_companyname')));

        var form = nlapiCreateForm("Customer results");

        var sublist = form.addSubList('custpage_customersresult', 'staticlist', 'Customers');
        sublist.addField('custpage_link', 'text', 'Open');

        var customForm = findRecordTypeForm(request.getParameter('custpage_employee'));
        var acessLevelName = request.getParameter('custpage_accesslevel') == '1' ? "View" : "Edit";

        var formUrl = nlapiResolveURL('SUITELET',
                            'customscript_in8_externalcustomer_sl',
                                'customdeploy_in8_externalcustomer_sl',
                                    parseInt(nlapiGetContext().getUser()) < 0);

        formUrl += '&customform='+customForm+'&internalid='+request.getParameter('custpage_employee')+'&hash='+request.getParameter('custpage_hash')+'&recordid=';

        var search = nlapiSearchRecord('customer', 'customsearch_customers', filters, null);

        var searchDef = nlapiLoadSearch('customer', 'customsearch_customers');

        if(search)
        {
            var columns = searchDef.getColumns();

            for (var i = 0; i < columns.length; i++)
            {
                sublist.addField("custpage_" + columns[i].getName(), columns[i].type == "select" ? "text" : columns[i].type, columns[i].getLabel());
            }

            var line = 1;

            for (var i = 0; i < search.length; i++)
            {
                var url = formUrl + search[i].getId();
                url = "<a href='"+url+"'>"+acessLevelName+"</a>";
                sublist.setLineItemValue("custpage_link", line, url);

                for (var c = 0; c < columns.length; c++)
                {
                    sublist.setLineItemValue("custpage_" + columns[c].getName(), line, search[i].getText(columns[c].getName()) ? search[i].getText(columns[c].getName()) : search[i].getValue(columns[c].getName()));
                }

                line++;
            }
        }

        response.writePage(form);

    }
}

function findRecordTypeForm(employeeId)
{
    var search = nlapiSearchRecord('employee', null, new nlobjSearchFilter('internalid', null, 'anyof', employeeId), new nlobjSearchColumn('custentity_in8_employeecustform'));

    if(search)
    {
        return parseInt(search[0].getValue('custentity_in8_employeecustform'));
    }

    return null;
}

function checkAccess(id) {

    var search = nlapiSearchRecord('employee', null, new nlobjSearchFilter('internalid', null, 'anyof', id), new nlobjSearchColumn('custentity_in8_employeeslaccess'));

    if(search)
    {
        return parseInt(search[0].getValue('custentity_in8_employeeslaccess'));
    }

    return 3;
}

function createSavedSearchSublist(form)
{
    var searchSS = nlapiSearchRecord('savedsearch', null, new nlobjSearchFilter('internalid', null, 'anyof', '1269'), null);

    if(searchSS)
    {
        var newTab = form.addTab('custpage_pacereport', 'Pace Report');

        var sublistSearch = nlapiLoadSearch(null, 'customsearch1269');
        sublistSearch.addFilter(new nlobjSearchFilter('jobresource', null, 'anyof', id));

        var sublist = form.addSubList('custpage_pacereportlist', 'list', 'Pace Report List');

        var columns = sublistSearch.getColumns();
        sublist = createSublistFields(sublist, columns);

        var line = 1;

        sublistSearch.runSearch().forEachResult(function (result){

            for(var c = 0; c < columns.length; c++)
            {
                if (result.getText(columns[c].getName()))
                    sublist.setLineItemValue("custpage_" + columns[c].getName(), line, result.getText(columns[c].getName()));
                else
                    sublist.setLineItemValue("custpage_" + columns[c].getName(), line, result.getValue(columns[c].getName()));
            }

            var color = setPaceReportColor(parseFloat(result.getValue('custentity145')));

            if(color)
                sublist.setLineItemValue("custpage_linecolor", line, color);


            line++;
            return true;
        });
    }

    return form;
}

function createSublistFields(sublist, columns)
{
    for(var i = 0; i < columns.length; i++)
    {
        sublist.addField("custpage_"+columns[i].getName(), "text", columns[i].getLabel());
    }

    sublist.addField('custpage_linecolor', 'text', '').setDisplayType('hidden');

    return sublist;
}

function setPaceReportColor(value)
{
    if(value < -75)
    {
        return "#ffffff";
    } else if(value >= -75 && value < -50)
    {
        return "#ddedf5";
    } else if(value >= -50 && value < -25)
    {
        return "#9acee8";
    } else if(value >= -25 && value < 0)
    {
        return "#35ace7";
    } else if(value >= 0 && value < 10)
    {
        return "#b0d674";
    } else if(value >= 10 && value < 20)
    {
        return "#fde201";
    } else if(value >= 20)
    {
        return "#fd6001";
    }

    return null;
}
