/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/runtime', 'N/search', 'N/record'], function(runtime, search, record) {

    function afterSubmit(context) {

        // if record is new, autoamtically populate the modified field
        // on the event that the user deletes a record, do nothing.
        if(context.type == context.UserEventType.CREATE) {
            var newRecObj = context.newRecord;

            record.submitFields({
                type: record.Type.CUSTOMER,
                id: newRecObj.id,
                values: {
                    custentity_acs_last_modified: new Date()
                },
                options: {
                    enableSourcing: false,
                    ignoreMandatoryFields : true
                }
            });

            return true;
        } else if (context.type == context.UserEventType.DELETE) {
            return true;
        }

        // get all the fields to exclude
        const EXCLUDED_FIELDS = runtime.getCurrentScript().getParameter('custscript_acs_excluded_fields').split(",");
        const MODIFIED_DATE_FIELD_ID = runtime.getCurrentScript().getParameter('custscript_acs_modified_date_field');
        const STATIC_EXCLUDED_FIELDS = ["submitnext_y","lastmodifieddate","otherrelationships","nsapiCT","nsbrowserenv","sys_id","entryformquerystring","_eml_nkey_","selectedtab","whence"];

        var newRecObj = context.newRecord;
        var oldRecObj = context.oldRecord;

        var fieldsArr = newRecObj.getFields();

        // loop through all the fields and check if changed
        for(var i = 0; i < fieldsArr.length; i++) {

            // don't do work if any of the fields are excluded or the field is the custom modified date field
            if(EXCLUDED_FIELDS.indexOf(fieldsArr[i]) !== -1) continue;
            if(STATIC_EXCLUDED_FIELDS.indexOf(fieldsArr[i]) !== -1) continue;
            if(fieldsArr[i] == MODIFIED_DATE_FIELD_ID) continue;

            var oldValue = oldRecObj.getValue({ fieldId: fieldsArr[i] });

            // if the old value is null, don't do any further work
            if(oldValue == null) continue;


            var newValue = newRecObj.getValue({ fieldId: fieldsArr[i] });

            // if old value is not the same as the new value, insert date to custom modified date field
            if(oldValue != newValue){
                log.debug('test', {
                    fieldId: fieldsArr[i],
                    oldValue: oldValue,
                    newValue: newValue
                });
                record.submitFields({
                    type: record.Type.CUSTOMER,
                    id: newRecObj.id,
                    values: {
                        custentity_acs_last_modified: new Date()
                    },
                    options: {
                        enableSourcing: false,
                        ignoreMandatoryFields : true
                    }
                });
            }
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
