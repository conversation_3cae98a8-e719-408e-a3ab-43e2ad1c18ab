<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>	
    <macrolist>
        <macro id="nlheader">
    <table class="header" style="width: 100%;">
    <tr>
	  <td style="width:70%">
        <#if subsidiary.name == 'rygr, LLC'>
            <img src="${subsidiary.logo@url}" style="float: left; margin-top: -40px;margin-left:-40px" width="200px" height="100px" />
        <#else>
            <img src="${subsidiary.logo@url}" style="float: left; margin: 7px" width="200px" height="60px" />  
        </#if>      
        <span class="nameandaddress"></span><br /><span class="nameandaddress"></span>
      </td>
	  <td align="right">
        <br/><br/>
          <table style="width: 100%;padding: 0px;">
            <tr>
              <td><b><span style="font-size: 14pt">Invoice</span></b></td>
              <td></td>
            </tr>
            <tr>
              <td><br/>
              </td>
            <td></td></tr>
            <tr>
              <td class="addressheader" style="font-size:9pt;padding: 0px"><b>Date</b></td>
              <td class="addressheader" style="font-size:9pt;padding: 0px">${record.trandate}</td>
            </tr>
            <tr>
              <td class="addressheader" style="font-size:9pt;padding: 0px"><b>Invoice #</b></td>
              <td class="addressheader" style="font-size:9pt;padding: 0px">${record.tranid}</td>
            </tr>
            <tr>
              <td><br/>
              </td>
            <td></td></tr>
            <tr>
              <td class="addressheader" style="font-size:9pt;padding: 0px"><b>Terms</b></td>
              <td class="addressheader" style="font-size:9pt;padding: 0px">${record.terms}</td>
            </tr>
            <tr>
              <td class="addressheader" style="font-size:9pt;padding: 0px"><b>PO #</b></td>
              <td class="addressheader" style="font-size:9pt;padding: 0px">${record.otherrefnum}</td>
            </tr>
        </table>
      </td>
	</tr>
    </table>
        </macro>
        <macro id="nlfooter">
            <table class="footer" style="width: 100%;"><tr>
	<td></td>
	<td align="left"><pagenumber/> of <totalpages/></td>
	</tr></table>
        </macro>
    </macrolist>
    <style type="text/css">* {
		<#if .locale == "zh_CN">
			font-family: NotoSans, NotoSansCJKsc, sans-serif;
		<#elseif .locale == "zh_TW">
			font-family: NotoSans, NotoSansCJKtc, sans-serif;
		<#elseif .locale == "ja_JP">
			font-family: NotoSans, NotoSansCJKjp, sans-serif;
		<#elseif .locale == "ko_KR">
			font-family: NotoSans, NotoSansCJKkr, sans-serif;
		<#elseif .locale == "th_TH">
			font-family: NotoSans, NotoSansThai, sans-serif;
		<#else>
			font-family: NotoSans, sans-serif;
		</#if>
		}
		table {
			font-size: 9pt;
			table-layout: fixed;
		}
        th {
            font-weight: bold;
            font-size: 8pt;
            vertical-align: middle;
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
            color: #333333;
        }
        td {
            padding: 4px 6px;
        }
		td p { align:left }
        b {
            font-weight: bold;
            color: #333333;
        }
        table.header td {
            padding: 0px;            
        }
        table.footer td {
            padding: 0px;
            font-size: 8pt;
        }
        table.itemtable th {
            padding-bottom: 5px;
            padding-top: 5px;
        }
        table.body td {
            padding-top: 2px;
        }
        table.total {
            page-break-inside: avoid;
        }
        tr.totalrow {
            background-color: #e3e3e3;
            line-height: 200%;
        }
        td.totalboxtop {
            font-size: 12pt;
            background-color: #e3e3e3;
        }
        td.addressheader {
            font-size: 8pt;
            padding-top: 6px;
            padding-bottom: 2px;
        }
        td.address {
            padding-top: 0px;
        }
        td.totalboxmid {
            font-size: 28pt;
            padding-top: 20px;
            background-color: #e3e3e3;
        }
        td.totalboxbot {
            background-color: #e3e3e3;
            font-weight: bold;
        }
        span.title {
            font-size: 28pt;
        }
        span.number {
            font-size: 16pt;
        }
        span.itemname {
            font-weight: bold;
            line-height: 150%;
        }
        hr {
            width: 100%;
            color: #d3d3d3;
            background-color: #d3d3d3;
            height: 1px;
        }
</style>
</head>
<body header="nlheader" header-height="10%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
    <table style="width: 100%; margin-top: 10px;"><tr>
	<td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
    <td class="addressheader" colspan="3"></td>
	<td class="addressheader" colspan="2"><b></b></td>
	</tr>
	<tr>
	<td class="address" colspan="3" rowspan="2">${record.billaddress}</td>
    <td class="address" colspan="3" rowspan="2"></td>
	<td align="right" colspan="2"></td>
	</tr>
	<tr>
	<td align="right" colspan="3">
      </td>
	</tr></table>
  
<#if record.item?has_content>
<#assign groups = []>
  
<#list record.item as item>  
  <#assign hasItem = false>
  <#list groups as group>
    <#if group.custcol_start_date?string == item.custcol_start_date?string && group.custcol_vendor_ref == item.custcol_vendor_ref && group.description == item.description>
      <#assign hasItem = true>
      <#break>
    </#if>    
  </#list>
  <#if !hasItem>
    <#assign groups = groups + [ { "custcol_start_date": item.custcol_start_date, "custcol_vendor_ref": item.custcol_vendor_ref, "description": item.description, "amount": item.amount } ]>
  </#if>
</#list>
  
  <table class="itemtable" style="width: 100%; margin-top: 20px;border: 1px solid #d3d3d3;"><!-- start items -->
  <thead>
      <tr>
      <th colspan="3">Start Date</th>
      <th colspan="4">Ad Company</th>
      <th colspan="12">Description</th>
      <th align="right" colspan="4">Amount</th>
      </tr>
  </thead>
  <#list groups as group>
    <#assign amount = 0>
    <#list record.item as item>  
        <#if group.custcol_start_date?string == item.custcol_start_date?string && group.custcol_vendor_ref == item.custcol_vendor_ref && group.description == item.description>
          <#assign amount = amount + item.amount>
        </#if>
    </#list>
      <tr>
        <td colspan="3">${group.custcol_start_date}</td>
        <td colspan="4">${group.custcol_vendor_ref}</td>
        <td colspan="12">${group.description}</td>
        <td align="right" colspan="4">${amount?string(",##0.00")}</td>
      </tr>
  </#list><!-- end items --></table>
</#if>
<table style="width: 100%; margin-top: 20px; padding: 0px;margin-right:7px" cellpadding="0">
  <tr style="padding:0px">
	<td colspan="4">&nbsp;</td>
	<td align="right" style="padding: 0px"><b>Total</b></td>
	<td align="right" style="padding: 0px">US ${record.total}</td>
	</tr>
	  <tr style="padding:0px">
	<td colspan="4">&nbsp;</td>
	<td align="right" style="padding: 0px"><b>Amount Due</b></td>
	<td align="right" style="padding: 0px">US ${record.amountremaining}</td>
	</tr>	
  </table>
<br/>
${subsidiary.custrecord_bb_subsidiary_inv_disclaimer}
</body>
</pdf>