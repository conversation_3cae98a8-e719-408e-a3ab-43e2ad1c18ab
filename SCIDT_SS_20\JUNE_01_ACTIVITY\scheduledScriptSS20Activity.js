/**
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
define(['N/search', 'N/record'], function(search, record) {

    function execute(context) {
        var filterArr = [
            search.createFilter({ name: 'mainline',  operator: search.Operator.IS, values: true }),
            search.createFilter({ name: 'type', operator: search.Operator.ANYOF, values: 'PurchOrd' }),
            search.createFilter({ name: 'datecreated', operator: search.Operator.ON, values: ['today'] })
        ];

        var searchObj = search.create({
            type: search.Type.PURCHASE_ORDER,
            filters: filterArr
        });

        var searchResultObj = searchObj.run();

        searchResultObj.each(function(result){
            record.submitFields({
                type: record.Type.PURCHASE_ORDER,
                id: result.id,
                values: {
                    approvalstatus: '1'
                }
            });

            return true;
        });
    }

    return {
        execute: execute
    }
});
