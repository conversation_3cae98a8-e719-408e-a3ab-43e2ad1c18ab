/**
 * Order History Suitelet
 * 
 * Version    Date            Author           Remarks
 * 1.00       05 Sep 2019     <PERSON>   Initial Version
 *
 */

//var secretKey = 'ac8420dd-5069-4877-8d19-c017c12706b2';
var secretKey = 'c42bbaaf-d96a-4d0d-a30b-5704dae8986e';

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response) {

    if (request.getMethod() == 'GET') {
        app.handleGet();
    } else {
        app.handlePost();
    }
}

var app = (function () {

    var FORM_NAME = 'Orders History', // Form Name
        LIST_NAME = 'Orders History', // List Name
        SUBMIT_BUTTON = 'Submit', // Submit button caption
        MAX_RECORDS = 10, // Maximum number of records to display on the sublist
        MARKALL_ENABLED = false, // Mark all option enabled
        tranType,
        userId;

    /**
     * Handles Suitelet GET Method
     * 
     * @returns {Void}
     */
    var handleGet = function () {

        var form,
            subList,
            searchResults;

        try {
            userId = request.getParameter('userid');
            var hash = request.getParameter('hash');
            var customer = request.getParameter('custpage_customer');
            tranType = request.getParameter('custpage_type') ? request.getParameter('custpage_type') : ['SalesOrd', 'Estimate', 'CustInvc'];


            //var hashCompare = String(CryptoJS.SHA256(secretKey + userId + customer));

            // if (customer && hash != hashCompare) {
            //     displayMessage('Invalid access credentials.' + hashCompare);
            //     return;
            //     userId = null;
            // }
            // if (!customer) {
            //     displayMessage('Invalid parameters.');
            //     return;
            // }

            // form = nlapiCreateForm('', false);

            // form.setScript('customscript_in8_orderhistory_cs');

            // addFilters(form);

            // addButtons(form);

            // if (request.getParameter('custpage_tranid')) {
            //     // Creates and inserts the sublist on the form
            //     subList = getSubList(form);

            //     // Gets the search results to be displayed
            //     searchResults = getSearchResults();

            //     // Populates the sublist based on the search results
            //     populateSubList(subList, searchResults);
            // }
            // // Displays the page
            // response.writePage(form);

            var html = renderHTML();

            response.write(html);

        } catch (e) {
            displayMessage('An error occurred. ' + (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
        }
    };

    function renderHTML() {
        var renderer = nlapiCreateTemplateRenderer(),
            searchResults,
            record,
            template,
            html,
            file;

        var filerec = nlapiLoadFile(1072863);
        var template = filerec.getValue();

        template = template.replace('{custpage_ref_code}', request.getParameter('custpage_ref_code') || '');
        template = template.replace('{custpage_tranid}', request.getParameter('custpage_tranid') || '');

        if (request.getParameter('custpage_tranid') &&
            request.getParameter('custpage_ref_code')) {
            template = template.replace('{search}', 'true');
        } else {
            template = template.replace('{search}', 'false');
        }

        renderer.setTemplate(template);

        var searchResults = getSearchResults() || [];

        var deposits = getDeposits() || [];

        renderer.addSearchResults('records', searchResults);
        renderer.addSearchResults('deposits', deposits);

        nlapiLogExecution('DEBUG', 'in8', 'Search len: ' + searchResults.length);

        // Render to string
        html = renderer.renderToString();

        var htmlPay = '';

        if (searchResults.length) {
            var i = 0;
            var hash = String(CryptoJS.SHA256('ac8420dd-5069-4877-8d19-c017c12706b2' + '0' + searchResults[i].getValue('entity')));

            var amountPay = searchResults[i].getValue('amountremaining') || searchResults[i].getValue('custbody257');

            amountPay = Number(amountPay);

            if (searchResults[i].getValue('recordtype') != 'invoice' || (searchResults[i].getValue('recordtype') == 'invoice' && searchResults[i].getText('status') == 'Open')) {
                htmlPay = '&nbsp;&nbsp;<input class="buttonpay" type="button" onclick="javascript:payInvoice(' + searchResults[i].getValue('internalid') + ',\'' + searchResults[i].getValue('transactionname') + '\',' + '0' + ',' +
                    searchResults[i].getValue('entity') + ',\'' + hash + '\',' + amountPay + ',\'' + searchResults[i].getValue('recordtype') + '\');' + '' + '" value="Pay">';
            }
        }

        html = html.replace('{pay}', htmlPay);
        return html;
    }

    /**
     * Handles Suitelet POST method
     * 
     * @returns {Void} 
     */
    var handlePost = function () {

        for (var i = 1; i <= request.getLineItemCount('custpage_sublist'); i++) {
            if (request.getLineItemValue('custpage_sublist', 'custpage_selected', i) == 'T') {
                // TODO: Process the line
            }
        }

        // Reloads window
        handleGet();
    };

    /**
     * Add Buttons
     * 
     * @param {nlobjForm} form Object containing the form
     * @returns {Void} 
     */
    var addButtons = function (form) {

        form.addButton('custombutton_search', 'Search', 'search()');

        //form.addSubmitButton(SUBMIT_BUTTON);

    };

    /**
     * Add Filter fields to the Form
     * 
     * @param {nlobjForm} form Object containing the form
     * @returns {Void} 
     */
    var addFilters = function (form) {

        // var field = form.addField('file1', 'inlinehtml', 'label');
        // field.setDefaultValue("<span style='font-size:12pt'>Credentials saved in this form are not saved on the website, they are directly saved in In8Syncs' NetSuite account only,  for security reasons.</span><br/>");
        // field.setLayoutType('outsidebelow','startrow');

        var fld = form.addField('custpage_tranid', 'text', 'Sales order # / Estimate # / Invoice #', null);
        if (request.getParameter('custpage_tranid')) fld.setDefaultValue(request.getParameter('custpage_tranid'));

        fld = form.addField('custpage_ref_code', 'text', 'Payment Submission Reference Code', null);
        if (request.getParameter('custpage_ref_code')) fld.setDefaultValue(request.getParameter('custpage_ref_code'));
    };

    /**
     * Add a sublist to the Form
     * 
     * @param {nlobjForm} form Object containing the form
     * @returns {Void} 
     */
    var getSubList = function (form) {

        var deliveryStatusField = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_delivery_status_field');

        //form.addFieldGroup('custpage_grp', 'Contacts');

        var subList = form.addSubList('custpage_sublist', 'list', LIST_NAME);

        subList.addField('custpage_trandate', 'text', 'Date');
        subList.addField('custpage_name', 'text', 'Transaction Name');
        //subList.addField('custpage_type', 'text', 'Type');
        if (!deliveryStatusField || tranType != 'SalesOrd') subList.addField('custpage_status', 'text', 'Status');
        subList.addField('custpage_amount', 'currency', 'Amount');

        // if (tranType == 'CustInvc') {
        //     subList.addField('custpage_amount_remaining', 'currency', 'Amount Remaining');
        // }
        subList.addField('custpage_amount_remaining', 'currency', 'Payment Amount Required');

        //if (request.getParameter('custpage_customer')) {
        //subList.addField('custpage_view', 'textarea', 'View/Download');
        //}
        if (tranType == 'SalesOrd' && deliveryStatusField) {
            subList.addField('custpage_status', 'text', 'Delivery Status');
        }

        //if (request.getParameter('custpage_customer')) {
        subList.addField('custpage_pay', 'textarea', '');
        //}

        if (tranType == 'SalesOrd') {
            subList.addField('custpage_packing', 'textarea', '');
        }

        return subList;
    };

    /**
     * Populate the SubList
     * 
     * @param {nlobjSublist} list Object sublist
     * @param {nlobjSearchResults} searchResults Object search results
     * 
     * @returns {Void} 
     */
    var populateSubList = function (list, searchResults) {

        var deliveryConfirmField = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_delivery_confirm_field');
        var deliveryStatusField = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_delivery_status_field');
        var enablePrintPacking = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_enable_print_packing');
        var enableInvoicePayments = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_enable_invoice_payments');

        var searchLength = (searchResults ? searchResults.length : 0),
            i = 0;

        // Checks if needs to display only a number of records
        if (MAX_RECORDS) searchLength = searchLength > MAX_RECORDS ? MAX_RECORDS : searchLength;

        var url = nlapiResolveURL('SUITELET', 'customscript_in8_orderprint_sl', 'customdeploy_in8_orderprint_sl_dep', 'external');

        for (i = 0; i < searchLength; i++) {
            list.setLineItemValue('custpage_trandate', i + 1, searchResults[i].getValue('trandate'));
            list.setLineItemValue('custpage_name', i + 1, searchResults[i].getValue('transactionname'));

            if (tranType == 'SalesOrd' && deliveryStatusField) {
                list.setLineItemValue('custpage_status', i + 1, getTrackingStatus(deliveryStatusField, searchResults[i].getValue('internalid')));
            } else {
                list.setLineItemValue('custpage_status', i + 1, searchResults[i].getText('status'));
            }
            list.setLineItemValue('custpage_amount', i + 1, searchResults[i].getValue('amount'));

            //list.setLineItemValue('custpage_type', i + 1, searchResults[i].getValue('recordtype'));

            // if (tranType == 'CustInvc') {

            if (searchResults[i].getValue('recordtype') != 'invoice') {
                list.setLineItemValue('custpage_amount_remaining', i + 1, searchResults[i].getValue('custbody257'));
            } else {
                list.setLineItemValue('custpage_amount_remaining', i + 1, searchResults[i].getValue('amountremaining'));
            }

            // }

            //if (userId) {
            var hash = String(CryptoJS.SHA256(secretKey + searchResults[i].getValue('internalid')));

            var html = '<a href="' + url + '&custpage_internalid=' + searchResults[i].getValue('internalid') + '&userid=' + request.getParameter('userid') + '&hash=' + hash + '" target="_blank">View</a>' +
                '&nbsp;&nbsp;<a href="' + url + '&custpage_internalid=' + searchResults[i].getValue('internalid') + '&userid=' + request.getParameter('userid') + '&hash=' + hash + '&download=T" target="_blank">Download</a>';

            //list.setLineItemValue('custpage_view', i + 1, html);

            html = '';

            //hash = String(CryptoJS.SHA256('ac8420dd-5069-4877-8d19-c017c12706b2' + request.getParameter('userid') + request.getParameter('custpage_customer')));

            hash = String(CryptoJS.SHA256('ac8420dd-5069-4877-8d19-c017c12706b2' + '0' + searchResults[i].getValue('entity')));

            var amountPay = searchResults[i].getValue('amountremaining') || searchResults[i].getValue('custbody257');

            amountPay = Number(amountPay);

            if (searchResults[i].getValue('recordtype') != 'invoice' || (searchResults[i].getValue('recordtype') == 'invoice' && searchResults[i].getText('status') == 'Open')) {
                html += '&nbsp;&nbsp;<a href="javascript:payInvoice(' + searchResults[i].getValue('internalid') + ',\'' + searchResults[i].getValue('transactionname') + '\',' + '0' + ',' +
                    searchResults[i].getValue('entity') + ',\'' + hash + '\',' + amountPay + ',\'' + searchResults[i].getValue('recordtype') + '\');' + '' + '">Pay</a>';
            }

            //if (request.getParameter('custpage_customer')) {
            // if (searchResults[i].getValue('recordtype') != 'invoice' || (searchResults[i].getValue('recordtype') == 'invoice' && searchResults[i].getText('status') == 'Open')) {
            //     html += '&nbsp;&nbsp;<a href="javascript:payInvoice(' + searchResults[i].getValue('internalid') + ',\'' + searchResults[i].getValue('transactionname') + '\',' + request.getParameter('userid') + ',' +
            //         request.getParameter('custpage_customer') + ',\'' + hash + '\',' + amountPay + ',\'' + searchResults[i].getValue('recordtype') + '\');' + '' + '">Pay</a>';
            // }
            //}
            if (deliveryConfirmField && searchResults[i].getValue(deliveryConfirmField)) {
                html += '&nbsp;&nbsp;<a href="' + searchResults[i].getValue(deliveryConfirmField) + '" target="_blank">Delivery Confirmation</a>';
            }

            //if (request.getParameter('custpage_customer')) {
            hash = String(CryptoJS.SHA256(secretKey + searchResults[i].getValue('internalid')));
            list.setLineItemValue('custpage_pay', i + 1, html);
            //}
            //}

            html = '';
            if (tranType == 'SalesOrd' && enablePrintPacking == 'T' && (searchResults[i].getText('status') == 'Billed' || searchResults[i].getText('status') == 'Pending Billing')) {
                html += '&nbsp;&nbsp;<a href="' + url + '&custpage_internalid=' + searchResults[i].getValue('internalid') + '&userid=' + request.getParameter('userid') + '&hash=' + hash + '&packingSlip=T" target="_blank">Packing Slip</a>';
                list.setLineItemValue('custpage_packing', i + 1, html);
            }
        }
    };

    function getTrackingStatus(fieldName, internalId) {

        var statuses = [];

        var search = nlapiSearchRecord('itemfulfillment', null, [
            new nlobjSearchFilter('createdfrom', null, 'anyof', internalId)
        ], [
            new nlobjSearchColumn(fieldName)
        ]) || [];

        for (var i = 0; i < search.length; i++) {
            if (search[i].getValue(fieldName) && statuses.indexOf(search[i].getValue(fieldName)) == -1) {
                statuses.push(search[i].getValue(fieldName));
            }
        }
        return statuses.join('<br/>').substr(0, 300);
    }

    /**
     * Get the Searh Results
     * 
     * @returns {Void} 
     */
    var getSearchResults = function () {

        var filters = [],
            columns = [],
            i = 0;

        if (!request.getParameter('custpage_tranid') && !request.getParameter('custpage_ref_code')) {
            return [];
        }
        var deliveryConfirmField = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_delivery_confirm_field');

        if (request.getParameter('custpage_customer')) filters[i++] = new nlobjSearchFilter('entity', null, 'anyof', request.getParameter('custpage_customer'));

        if (request.getParameter('custpage_tranid')) filters[i++] = new nlobjSearchFilter('tranid', null, 'is', request.getParameter('custpage_tranid'));
        if (request.getParameter('custpage_ref_code')) filters[i++] = new nlobjSearchFilter('custbody256', null, 'is', request.getParameter('custpage_ref_code'));

        filters[i++] = new nlobjSearchFilter('mainline', null, 'is', 'T');
        filters[i++] = new nlobjSearchFilter('type', null, 'anyof', tranType);

        i = 0;

        columns[i++] = new nlobjSearchColumn('internalid');
        columns[i++] = new nlobjSearchColumn('transactionname');
        columns[i++] = new nlobjSearchColumn('transactionnumber');
        columns[i++] = new nlobjSearchColumn('trandate');
        columns[i++] = new nlobjSearchColumn('status');
        columns[i++] = new nlobjSearchColumn('entity');
        columns[i++] = new nlobjSearchColumn('amount');
        columns[i++] = new nlobjSearchColumn('custbody257');
        columns[i++] = new nlobjSearchColumn('recordtype');
        columns[i++] = new nlobjSearchColumn('amountpaid');

        if (deliveryConfirmField) {
            columns[i++] = new nlobjSearchColumn(deliveryConfirmField);
        }
        //if (tranType == 'CustInvc') {
        columns[i++] = new nlobjSearchColumn('amountremaining');
        //}
        return nlapiSearchRecord('transaction', null, filters, columns);
    };

    function getDeposits() {

        var filters = [];
        var i = 0;

        if (request.getParameter('custpage_tranid')) filters[i++] = new nlobjSearchFilter('tranid', null, 'is', request.getParameter('custpage_tranid'));
        if (request.getParameter('custpage_ref_code')) filters[i++] = new nlobjSearchFilter('custbody256', null, 'is', request.getParameter('custpage_ref_code'));        

        filters[i++] = new nlobjSearchFilter('mainline', null, 'is', 'T');
        filters[i++] = new nlobjSearchFilter('type', null, 'anyof', tranType);

        filters[i++] = new nlobjSearchFilter('type', 'applyingtransaction', 'anyof', 'CustDep');

        var transactionSearch = nlapiSearchRecord("transaction",null,
                filters, 
                [
                new nlobjSearchColumn("amount","applyingTransaction",null),                 
                ]
                );
        return transactionSearch;
    }

    /**
     * Displays a message
     * 
     * @param {String} message Message
     * @returns {Void}
     */
    var displayMessage = function (message) {

        // Create a NetSuite form
        var form = nlapiCreateForm(FORM_NAME, false),
            html = message;

        // Add a new HTML field to display the HTML contents
        field = form.addField('file', 'inlinehtml', 'label');
        field.setLayoutType('outsidebelow');
        field.setDefaultValue('<font size="2pt">' + html + '</font>');

        form.addButton('custombutton_back', 'Back', 'window.history.back()');

        response.writePage(form);
    };

    return {
        handleGet: handleGet,
        handlePost: handlePost
    };
})();