/**
 *@NApiVersion 2.1
 *@NScriptType UserEventScript
 */
define(['N/render','N/record'], function(render,record) {

    const FOLDER_ID = 1034725
    function isEmpty(stValue) {
        return ((stValue === '' || stValue == null || stValue == undefined) || (stValue.constructor === Array && stValue.length == 0) || (stValue.constructor === Object && (function(
            v) {
            for (var k in v)
                return false;
            return true;
        })(stValue)));
    }
    function afterSubmit(context) {
        try {
            log.audit('test', context.type);
            if(context.type != context.UserEventType.DELETE){
                var currentRecord = context.newRecord;
                // var fileField = currentRecord.getValue({fieldId: "custbody_nsacs_file_"});
    
                // if(isEmpty(fileField)){
                    var fileObj = render.transaction({
                        entityId: context.newRecord.id,
                        printMode: render.PrintMode.PDF,
                        inCustLocale: true
                    });
        
                    fileObj.name = `${context.newRecord.id}.pdf`;
                    fileObj.folder = FOLDER_ID;
                    var fileID = fileObj.save();
        
                    var ID = record.submitFields({
                        type: context.newRecord.type,
                        id:context.newRecord.id,
                        values: {
                            "custbody_nsacs_file_": fileID
                        },
                    });
                  
                   log.audit("file saved",`File ID: ${fileID}; Record ID: ${ID}`);

                    // added by jagonzales 09/14/2021
                    log.debug('record type', currentRecord.type);
                    if(currentRecord.type == record.Type.CREDIT_MEMO) {
                        var invoiceId = currentRecord.getValue({ fieldId: 'createdfrom' });

                        if(invoiceId){
                            var fileObj = render.transaction({
                                entityId: parseInt(invoiceId),
                                printMode: render.PrintMode.PDF,
                                inCustLocale: true
                            });
                
                            fileObj.name = `${invoiceId}.pdf`;
                            fileObj.folder = FOLDER_ID;
                            var fileID = fileObj.save();
                
                            var ID = record.submitFields({
                                type: context.newRecord.type,
                                id:invoiceId,
                                values: {
                                    "custbody_nsacs_file_": fileID
                                },
                            });
                          
                        }
                    } else {
                        var lineCount = currentRecord.getLineCount({ sublistId: 'apply' });

                        for(var i = 0; i < lineCount; i++){
                            if(currentRecord.getSublistValue({ sublistId: 'apply', fieldId: 'apply', line: i })){
                                var invoiceId = currentRecord.getSublistValue({ sublistId: 'apply', fieldId: 'internalid', line: i });

                                var fileObj = render.transaction({
                                    entityId: parseInt(invoiceId),
                                    printMode: render.PrintMode.PDF,
                                    inCustLocale: true
                                });
                    
                                fileObj.name = `${invoiceId}.pdf`;
                                fileObj.folder = FOLDER_ID;
                                var fileID = fileObj.save();
                    
                                var ID = record.submitFields({
                                    type: context.newRecord.type,
                                    id:invoiceId,
                                    values: {
                                        "custbody_nsacs_file_": fileID
                                    },
                                });

                            }
                        }
                    }


                // }
            }
        } catch (error) {
            log.error("After-Submit Error",error)
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});

