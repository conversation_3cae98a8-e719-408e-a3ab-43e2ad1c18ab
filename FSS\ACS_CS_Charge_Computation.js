/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/format'], function(format) {

    function getSublistVal(sublist, field, obj) {
        var temp = obj.getCurrentSublistValue({ sublistId: sublist, fieldId: field });
        if(temp == "" || isNaN(temp)){
            return 0.00
        }
        return parseFloat(temp);
    }

    function pageInit(context) {
        
    }

    function fieldChanged(context) {
        var fields = ['custcol_call_charge', 'custcol_service_charge', 'rate', 'quantity'];
        if(fields.indexOf(context.fieldId) !== -1) {
            var currRecObj = context.currentRecord;
            var sublistId = context.sublistId;
            var fieldId = context.fieldId;

            var callCharge = getSublistVal(sublistId, 'custcol_call_charge', currRecObj);
            var serviceCharge = getSublistVal(sublistId, 'custcol_service_charge', currRecObj);
            var rate = getSublistVal(sublistId, 'rate', currRecObj);
            var quantity = getSublistVal(sublistId, 'quantity', currRecObj);

            switch (fieldId) {
                case fields[0]: // custcol_call_charge
                case fields[1]: // custcol_service_charge
                    log.debug('confCharge + callCharge + serviceCharge', confCharge + " + " + callCharge + " + " + serviceCharge)
                    var amount = (rate * quantity)  + callCharge + serviceCharge;
                    break;

                case fields[2]: // rate                    
                case fields[3]: // quantity
                    var confCharge = rate * quantity;

                    currRecObj.setCurrentSublistValue({
                        sublistId: context.sublistId,
                        fieldId: 'custcol_conference_charge',
                        value: confCharge,
                        ignoreFieldChange: true
                    });

                    amount = confCharge + callCharge + serviceCharge;
                    break;
            
                default:
                    break;
            }

            amount = format.format({
                type: format.Type.CURRENCY2,
                value: amount
            });

            currRecObj.setCurrentSublistValue({
                sublistId: context.sublistId,
                fieldId: 'amount',
                value: amount,
                ignoreFieldChange: true
            });
        }
    }

    return {
        pageInit: pageInit,
        fieldChanged: fieldChanged
    }
});
