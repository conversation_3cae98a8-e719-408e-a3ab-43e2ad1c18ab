/**
 *@NApiVersion 2.1
 *@NScriptType Suitelet
 */
define(['N/record', 'N/ui/serverWidget', 'N/log', 'N/url','N/search'], function (record, serverWidget, log, url, search) {

    function onRequest(context) {
        // var billsavedsearch = 508;
        log.debug('context',context.request.parameters)
        var vendBillID = context.request.parameters.bill_id;
        var approvalStatus = context.request.parameters.approval_status;
        log.debug('vendBillID', 'vendBillID: ' + vendBillID);

        var vendBill = record.load({
            type: record.Type.VENDOR_BILL,
            id: vendBillID
        });
        var current_status = vendBill.getValue({
            fieldId: 'custbody_apapproval'
        });
        var form = serverWidget.createForm({
            title: " "
        });
        if(current_status == 2 ||  current_status == 3){
            var field = form.addField({
                id: 'custpage_vendor_bill_url',
                type: serverWidget.FieldType.INLINEHTML,
                label: ' '
            });
            var bill_search = search.load({
                id: billsavedsearch
            });
            var bilfilters = bill_search.filters;
            var bilfilter1 = search.createFilter({ //create new filter
                name: 'internalid',
                operator: search.Operator.ANYOF,
                values: [vendBillID]
            });
            bilfilters.push(bilfilter1);
            var billResult = bill_search.run();
            var bill_result = billResult.getRange({start: 0, end: 1000});
            var icolumns = billResult.columns;
            var action_date = "";
            if(bill_result.length > 0){
                action_date = bill_result[0].getValue(icolumns[1]);
            }
            var html = "<style>@import url('//maxcdn.bootstrapcdn.com/font-awesome/4.2.0/css/font-awesome.min.css'); .info-msg, .success-msg, .warning-msg, .error-msg {margin: 10px 0; padding: 10px; border-radius: 3px 3px 3px 3px; } .info-msg {color: #059; background-color: #BEF; } .success-msg {color: #270; background-color: #DFF2BF; } .warning-msg {color: #9F6000; background-color: #FEEFB3; } .error-msg {color: #D8000C; background-color: #FFBABA; }</style>"
            if(current_status == 2){
                html_message = "Vendor Bill was already approved last "+action_date+".";
            }
            else if(current_status == 3){
                html_message = "Vendor Bill was already rejected last "+action_date+".";
            }
            html += '<div class="warning-msg" style="font-size:14px;"> <i class="fa fa-warning"></i> '+html_message+' </div>';
            
            field.defaultValue = html;
        }
        else{
            vendBill.setValue({
                fieldId: 'custbody_apapproval',
                value: approvalStatus
            });
            var successid = vendBill.save();
            log.debug('successid', 'successid: ' + successid);
            var approvalMsg = (approvalStatus == 2) ? 'approved' : 'rejected';
            var endMessage = 'Vendor Bill has been ' + approvalMsg;
            form.title = endMessage;
            
            var field = form.addField({
                id: 'custpage_vendor_bill_url',
                type: serverWidget.FieldType.URL,
                label: 'Vendor Bill URL'
            });
            var vendorBillURL = resolveRecordUrl(successid);
            field.updateDisplayType({
                displayType : serverWidget.FieldDisplayType.INLINE
            });
            field.defaultValue = vendorBillURL;
        }
        
        context.response.writePage("TEST");
    }

    function resolveRecordUrl(vendBillID) {
        var scheme = 'https://';
        var host = url.resolveDomain({
            hostType: url.HostType.APPLICATION
        });
        var relativePath = url.resolveRecord({
            recordType: record.Type.VENDOR_BILL,
            recordId: vendBillID
        });
        var output = scheme + host + relativePath;
        return output;
    }

    return {
        onRequest: onRequest
    }
});