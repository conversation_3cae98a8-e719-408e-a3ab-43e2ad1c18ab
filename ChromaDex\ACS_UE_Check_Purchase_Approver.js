/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/search', 'N/runtime', 'N/record'], function(search, runtime, record) {

    function checkIfHaveApprover(empId) {
        var empObj = record.load({
            type: record.Type.EMPLOYEE,
            id: empId,
            isDynamic: false
        });

        return ((empObj.getValue({ fieldId: 'purchaseorderapprover' })) ? false : true );
    }

    function beforeSubmit(context) {
        var loggedUser = runtime.getCurrentUser();
        var employeeSearchObj = search.create({
            type: "employee",
            filters:
            [
               ["purchaseorderapprover","anyof",loggedUser.id]
            ],
            columns:
            [
               search.createColumn({name: "internalid", label: "Internal ID"})
            ]
         });
        var employeePagedData = employeeSearchObj.runPaged();

        // check if user is an approver for other users and has purchase approver
        // if user does not have a purchase approver but is approver for other users, they can proceed
        console.log(employeePagedData.count < 1 && checkIfHaveApprover(loggedUser.id));
        if(employeePagedData.count < 1 && checkIfHaveApprover(loggedUser.id)) {
            dialog.alert({
                title: 'Warning!',
                message: 'You cannot proceed to saving this file because you do not have a Purchase Approver. Please contact your administrator!'
            }).then(function proceed(result) {
                return false;
            });
        } else {
            return true;
        }
    }

    return {
        beforeSubmit: beforeSubmit
    }
});
