/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define([], function() {

    function beforeLoad(context) {
        log.debug({
            title: "Before Load Function",
            details: "Before Load Triggered"
        })
    }

    function beforeSubmit(context) {
        
    }

    function afterSubmit(context) {
        
    }

    return {
        beforeLoad: setInterval(beforeLoad, 5000),
        beforeSubmit: beforeSubmit,
        afterSubmit: afterSubmit
    }
});
