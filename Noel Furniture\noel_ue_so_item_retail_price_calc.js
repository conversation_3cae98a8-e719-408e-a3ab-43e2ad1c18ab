/**
 * @NApiVersion 2.x
 * @NScriptType UserEventScript
 * @NModuleScope SameAccount
 */
define(['N/search','N/record'],

function(search,record) {
   
    /**
     * Function definition to be triggered before record is loaded.
     *
     * @param {Object} scriptContext
     * @param {Record} scriptContext.newRecord - New record
     * @param {string} scriptContext.type - Trigger type
     * @param {Form} scriptContext.form - Current form
     * @Since 2015.2
     */
    function beforeLoad(scriptContext) {

    }

    /**
     * Function definition to be triggered before record is loaded.
     *
     * @param {Object} scriptContext
     * @param {Record} scriptContext.newRecord - New record
     * @param {Record} scriptContext.oldRecord - Old record
     * @param {string} scriptContext.type - Trigger type
     * @Since 2015.2
     */
    function beforeSubmit(context) {
      var eventType = context.type;
      log.debug('Event Type', eventType);
      
      if(eventType){// == context.UserEventType.CREATE){
        var newSO = context.newRecord;
        var itemLineCount = newSO.getLineCount({
          sublistId: 'item'
        });
        log.debug('item Line Count', itemLineCount);
        
        for(var i=0; i< itemLineCount; i++){
          var itemId= newSO.getSublistValue({
            sublistId: 'item',
            fieldId: 'item',
            line: i
          });
          //NEED FIELD TO CHECK TYPE OF ITEM!!!!!!!!!!!
          var inventoryItem = record.load({
            type: record.Type.INVENTORY_ITEM,
            id: itemId
          });
          log.debug('Item Loaded', inventoryItem);
          /*var discountItem = record.load({
            type: record.Type.DISCOUNT_ITEM,
            id: itemId
          });
          var nonInventItem = record.load({
            type: record.Type.NON_INVENTORY_ITEM,
            id: itemId
          });
          var otherCharge = record.load({
            type: record.Type.OTHER_CHARGE_ITEM,
            id: itemId
          });*/
          var itemRetailPrice= inventoryItem.getSublistValue({
            sublistId: 'price',
            fieldId: 'price_1_',
            line: 0
          });
          log.debug('Item Retail Price', itemRetailPrice);
          
          
          var itemQTY= newSO.getSublistValue({
            sublistId: 'item',
            fieldId: 'quantity',
            line: i
          });
          var retailPLevel= newSO.getSublistValue({
            sublistId: 'item',
            fieldId: 'custcol_noel_retail_price',
            line: i
          });
          var retailPRate= newSO.getSublistValue({
            sublistId: 'item',
            fieldId: 'custcol_noel_retail_price_rate',
            line: i
          });
          var retailTotal= newSO.getSublistValue({
            sublistId: 'item',
            fieldId: 'custcol_noel_total_retail_amount',
            line: i
          });
          //log.debug('Retail Price Line', retailPLevel);
          //log.debug('Retail Price Rate', retailPRate);
          //log.debug('Retail Price Total', retailTotal);
          log.debug('Item Name', itemId);
          log.debug('Item Quantity', itemQTY);
        }
      }
    }

    /**
     * Function definition to be triggered before record is loaded.
     *
     * @param {Object} scriptContext
     * @param {Record} scriptContext.newRecord - New record
     * @param {Record} scriptContext.oldRecord - Old record
     * @param {string} scriptContext.type - Trigger type
     * @Since 2015.2
     */
    function afterSubmit(scriptContext) {

    }

    return {
        beforeLoad: beforeLoad,
        beforeSubmit: beforeSubmit,
        afterSubmit: afterSubmit
    };
    
});
