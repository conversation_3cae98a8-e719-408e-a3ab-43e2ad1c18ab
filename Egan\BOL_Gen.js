/**
 * Copyright (c) 1998-2019 NetSuite, Inc. 2955 Campus Drive, Suite 100, San
 * Mateo, CA, USA 94403-2511 All Rights Reserved.
 *
 * This software is the confidential and proprietary information of NetSuite,
 * Inc. ("Confidential Information"). You shall not disclose such Confidential
 * Information and shall use it only in accordance with the terms of the license
 * agreement you entered into with NetSuite.
 */

/**
 * Description:
 * Functions to create single and master bill of lading *
 *
 */

 define(['N/record', 'N/search','N/xml','./NSUtilvSS2.js'],

 function(record, search,xml,NSUtil) {

     function createMasterBol(arrSelectedIf, arrMasters, strMasterNotes, intCounter, intWeightUnit, strWeightUnit, bMasterBol,
         intBolNumber, intSLDepIdParam) {
         
         var strToPrint = "";
         require(['N/render'], function(render) {
             //search for common columns among selected IF to create Master BoL
             var itemfulfillmentSearchObj = search.create({
                 type: "itemfulfillment",
                 filters: [
                     // ["type", "anyof", "ItemShip"],
                     // "AND",
                     ["internalid", "anyof", arrSelectedIf],
                     "AND", ["formulanumeric: MOD({linesequencenumber},3)", "equalto", "0"],
                     "AND", ["mainline", "is", "T"],
                 ],
                 columns: [search.createColumn({
                     name: "internalid",
                     label: "Internal ID"
                 }), search.createColumn({
                     name: "shipaddress",
                     join: "createdFrom",
                     label: "Shipping Address"
                 }), search.createColumn({
                     name: "custbody_so_bol_third_party_add",
                     join: "createdFrom",
                     label: "Third Party Billing Address"
                 }), search.createColumn({
                     name: "custbody_so_bol_third_party_account",
                     join: "createdFrom",
                     label: "Third party Account #"
                 }), search.createColumn({
                     name: "shipmethod",
                     join: "createdFrom",
                     label: "Carrier"
                 }), search.createColumn({
                     name: "custbody_so_bol_freight_terms",
                     join: "createdFrom",
                     label: "BoL Freight Terms"
                 }), search.createColumn({
                     name: "createdfrom",
                     label: "Created From"
                 })]
             });
             var arrReturnSearchResults = [];
             var objPagedSearch = itemfulfillmentSearchObj.runPaged({
                 pageSize: 1000
             });
             for (var i = 0, j = objPagedSearch.pageRanges.length; i < j; i++) {
                 var objSearchData = objPagedSearch.fetch({
                     index: objPagedSearch.pageRanges[i].index
                 });
                 arrReturnSearchResults = arrReturnSearchResults.concat(objSearchData.data);
             }
             log.debug("arrReturnSearchResults", arrReturnSearchResults);
             log.debug("objPagedSearch.count", objPagedSearch.count);
             for (var i = 0; i < objPagedSearch.count; i++) {

                 var intIfId = arrReturnSearchResults[i].getValue({
                     name: "internalid"
                 });
                 var strCarrier = arrReturnSearchResults[i].getValue({
                     name: "shipmethod",
                     join: "createdFrom",
                 });
                 var strShipAddress = arrReturnSearchResults[i].getValue({
                     name: "shipaddress",
                     join: "createdfrom"
                 });
                 var strthirdPartyAddress = arrReturnSearchResults[i].getValue({
                     name: "custbody_so_bol_third_party_add",
                     join: "createdfrom"
                 });
                 var strthirdPartyAccount = arrReturnSearchResults[i].getValue({
                     name: "custbody_so_bol_third_party_account",
                     join: "createdfrom"
                 });
                 var strFreightTerms = arrReturnSearchResults[i].getValue({
                     name: "custbody_so_bol_freight_terms",
                     join: "createdfrom"
                 });
                 var strCarrier = arrReturnSearchResults[i].getValue({
                     name: "shipmethod",
                     join: "createdfrom"
                 });

                 // values that IF for One Master has in common
                 var objValues = {
                     ShipAddress: strShipAddress,
                     ThirdPartyAddress: strthirdPartyAddress,
                     ThirdPartyAccount: strthirdPartyAccount,
                     FreightTerms: strFreightTerms,
                     Carrier: strCarrier,
                 }

                 // this creates array of objects. If the object values are the same it will group common values.
                 //Ids of those If that are the same will be stored in internalIDs
                 if (i == 0) {
                     // log.debug("i = 0",i);
                     var objMaster = {
                         internalIDs: [intIfId],
                         values: objValues,
                     };
                     arrMasters.push(objMaster);

                 } else {
                     var bfound = false;
                     for (var j = 0; j < arrMasters.length; j++) {
                         if (JSON.stringify(arrMasters[j].values) == JSON.stringify(objValues)) {
                             log.debug("JSON.stringify(arrMasters[j].values)", JSON.stringify(arrMasters[j].values));
                             log.debug("JSON.stringify(objValues))", JSON.stringify(objValues));
                             arrMasters[j].internalIDs.push(intIfId);
                             bfound = true;
                             break;
                         }
                     }
                     if (!bfound) {
                         var objMaster = {
                             internalIDs: [intIfId],
                             values: objValues,
                         };
                         arrMasters.push(objMaster);
                     }
                 }
             }

             // creating MASTER Bols
             for (var z = 0; z < arrMasters.length; z++) {
                 // ////////////////////////////////////////
                 log.debug("Create Master Bol");

                 // // search to get data for Master///
                 var arrMasterReturnSearchResults = [];
                 var itemMasterfulfillmentSearchObj = search.create({
                     type: "itemfulfillment",
                     filters: [
                         ["type", "anyof", "ItemShip"],
                         "AND", ["internalid", "anyof", arrMasters[z].internalIDs],
                         "AND", ["formulanumeric: MOD({linesequencenumber},3)", "equalto", "0"],
                     ],
                     columns: [search.createColumn({
                             name: "tranid",
                             summary: search.Summary.GROUP
                         }), search.createColumn({
                             name: "quantity",
                             summary: search.Summary.SUM
                         }), search.createColumn({
                             name: "tranid",
                             join: "createdFrom",
                             summary: search.Summary.GROUP
                         }),
                         // search
                         //      .createColumn({
                         //          name : "custcol_if_bol_col_weight",
                         //          summary : search.Summary.SUM
                         //      }),
                         search.createColumn({
                             name: "custbody_if_bol_total_weight_grams",
                             summary: search.Summary.GROUP
                         }),

                         search.createColumn({
                             name: "custcol_ns_bol_qty_handling_unit",
                             summary: search.Summary.SUM
                         }),
                           search.createColumn({
                             name: "custitem_egan_cubicmeasurement",
                              join: "item",
                             summary: search.Summary.SUM
                         }),
                     ]
                 });

                 var objPagedSearch = itemMasterfulfillmentSearchObj.runPaged({
                     pageSize: 1
                 });
                 for (var i = 0, j = objPagedSearch.pageRanges.length; i < j; i++) {
                     var objSearchData = objPagedSearch.fetch({
                         index: objPagedSearch.pageRanges[i].index
                     });
                     arrMasterReturnSearchResults = arrMasterReturnSearchResults.concat(objSearchData.data);
                 }
                 log.debug("arrMasterReturnSearchResults", arrMasterReturnSearchResults);
                 log.debug("objPagedSearch.count2", objPagedSearch.count);
                 var strToReplace = "";
                 var intPKGStotal = 0;
                 var intWeightTotal = 0;

                 for (var i = 0; i < objPagedSearch.count; i++) {
                   
                    var strcubicmeasurement = arrMasterReturnSearchResults[i].getValue({
                         name: "custitem_egan_cubicmeasurement",
                         join : "item",
                         summary: search.Summary.SUM
                     });
                    var strTotalWeight = NSUtil.forceFloat(strcubicmeasurement) + NSUtil.forceFloat(strTotalWeight) ;
                     var strIFnum = arrMasterReturnSearchResults[i].getValue({
                         name: "tranid",
                         summary: search.Summary.GROUP
                     });
                     var intPKGs = arrMasterReturnSearchResults[i].getValue({
                         name: "custcol_ns_bol_qty_handling_unit",
                         summary: search.Summary.SUM
                     }) || 0;
                     intPKGStotal += parseFloat(intPKGs);
                     // var intWeight = arrMasterReturnSearchResults[i]
                     //      .getValue({
                     //          name : "custcol_if_bol_col_weight",
                     //          summary : search.Summary.SUM
                     //      });
                     var intWeight = arrMasterReturnSearchResults[i].getValue({
                         name: "custbody_if_bol_total_weight_grams",
                         summary: search.Summary.GROUP
                     });
                     log.debug("intWeight1", intWeight);
                     intWeight = convertFromGtoWeightUnit(intWeightUnit, intWeight);
                     log.debug("intWeight2", intWeight);
                     intWeightTotal += parseFloat(intWeight);
                     var strPO = arrMasterReturnSearchResults[i].getValue({
                         name: "tranid",
                         join: "createdFrom",
                         summary: search.Summary.GROUP
                     });
                   
                      var strPO = xml.escape({
                                 xmlText : strPO
                             });

                     strToReplace += "<tr height=\"25\"><td style=\"align:center; valign:middle;\">" + strIFnum +
                         "</td><td style=\"align:center; valign:middle;\">" + intPKGs + "</td><td style=\"align:center; valign:middle;\">" +
                         N(intWeight, 3) + " " + strWeightUnit + "</td><td style=\"align:center; valign:middle;\">"
                       + strcubicmeasurement + "</td><td style=\"align:center; valign:middle;\">"
                       + strPO + "</td></tr>";
                 }

                 var renderer = render.create();
                 renderer.setTemplateByScriptId("CUSTTMPL_MASTER_BOL_2"); // CUSTTMPL_TSTDRV1759279_1

                 // loading first IF to fill all header fields on Master Bol = Note that these fields are the same for all IFs for each Master BoL
                 var objRecIF = record.load({
                     type: record.Type.ITEM_FULFILLMENT,
                     id: arrMasters[z].internalIDs[0],
                 });
                 var myContent = renderer.addRecord({
                     templateName: 'record',
                     record: objRecIF
                 });
                 var objBoLPdf = renderer.renderAsString();

                 // log.debug("strToReplace", strToReplace);
                 objBoLPdf = objBoLPdf.replace("CUSTOMERORDERTABLE_PLACEHOLDER", strToReplace);
                 objBoLPdf = objBoLPdf.replace("TOTAL_PKGS_PLACEHOLDER", intPKGStotal);
                 var strTotalW = N(intWeightTotal, 3) + " " + strWeightUnit;
                 objBoLPdf = objBoLPdf.replace("TOTAL_WEIGHT_PLACEHOLDER", strTotalW);
                   objBoLPdf = objBoLPdf.replace("TOTAL_CUBICFEET_PLACEHOLDER", strTotalWeight);
                 // /// end search ////
                 /////

                 log.debug("arrMasters[z].internalIDs[0]", arrMasters[z].internalIDs[0]);
                 ///
                 var date = new Date();

                 intBolNumber += z;
                 
                 strMasterNotes = xml.escape({
                                 xmlText : strMasterNotes
                             });
               
                 var objToreplace = {
                     "BOL_NUM_PLACEHOLDER": "MASTERBOL00" + intBolNumber, // 748,
                     "NOTES_PLACEHOLDER": strMasterNotes,
                     "TODAY_DATE_PLACEHOLDER": (date.getMonth() + 1) + "/" + date.getDate() + "/" + date.getFullYear()
                 }

                 for (var property in objToreplace) {
                     objBoLPdf = objBoLPdf.replace(property, objToreplace[property]);
                 }
                 // search to enter carrier information table
                 var arrMasterMasterItems = [];
                 var itemfulfillmentItemObj = search.create({
                     type: "itemfulfillment",
                     filters: [
                         ["type", "anyof", "ItemShip"],
                         "AND", ["internalid", "anyof", arrMasters[z].internalIDs],
                         "AND", ["formulanumeric: MOD({linesequencenumber},3)", "equalto", "0"],
                         "AND", ["shipping", "is", "F"],
                     ],
                     columns: [search.createColumn({
                             name: "item",
                             summary: "GROUP",
                             label: "Item"
                         }), search.createColumn({
                             name: "quantity",
                             summary: "SUM",
                             label: "Quantity"
                         }), search.createColumn({
                             name: "custcol_ns_bol_weight_units_show",
                             summary: "GROUP",
                             label: "WU"
                         }),

                         search.createColumn({
                             name: "custcol_if_bol_col_weight",
                             summary: "SUM",
                             label: "Total Item Weight"
                         }), search.createColumn({
                             name: "custcol_ns_bol_qty_handling_unit",
                             summary: "SUM",
                             label: "Handling Unit Qty"
                             }), search.createColumn({
                             name: "custcol_if_bol_col_type",
                             summary: "GROUP",
                             label: "Handling Unit Type"
                         }), search.createColumn({
                             name: "custcol_if_bol_col_nmfc_no",
                             summary: "GROUP",
                             label: "NMFC No."
                         }), search.createColumn({
                             name: "custcol_if_bol_col_fak_class",
                             summary: "GROUP",
                             label: "FAK Class"
                         }), search.createColumn({
                                 name: "custcol_item_description",
                                 summary: "GROUP",
                                 label: "Description"
                             })
                     ]
                 });

                 var objPagedSearch = itemfulfillmentItemObj.runPaged({
                     pageSize: 1000
                 });
                 for (var i = 0, j = objPagedSearch.pageRanges.length; i < j; i++) {
                     var objSearchData = objPagedSearch.fetch({
                         index: objPagedSearch.pageRanges[i].index
                     });
                     arrMasterMasterItems = arrMasterMasterItems.concat(objSearchData.data);
                 }
               
                  log.debug('arrMasterMasterItems : ', JSON.stringify(arrMasterMasterItems));

                 var strToReplace = "";
                 for (var i = 0; i < objPagedSearch.count; i++) {
                     var intHandUnitQty = arrMasterMasterItems[i].getValue({
                         name: "custcol_ns_bol_qty_handling_unit",
                         summary: search.Summary.SUM
                     });
                     var strType = arrMasterMasterItems[i].getText({
                         name: "custcol_if_bol_col_type",
                         summary: search.Summary.GROUP
                     });

                     strType = (strType != "- None -" ? strType : "");

                     var intQty = arrMasterMasterItems[i].getValue({
                         name: "quantity",
                         summary: search.Summary.SUM
                     });
                     var intWeight = arrMasterMasterItems[i].getValue({
                         name: "custcol_if_bol_col_weight",
                         summary: search.Summary.SUM
                     }) || 0;
                     var strWeightunits = arrMasterMasterItems[i].getValue({
                         name: "custcol_ns_bol_weight_units_show",
                         summary: search.Summary.GROUP
                     });

                     strWeightunits = (strWeightunits != "- None -" ? strWeightunits : "");

                     var strItem = arrMasterMasterItems[i].getText({
                         name: "item",
                         summary: search.Summary.GROUP
                     });
                     var strNMFC = arrMasterMasterItems[i].getText({
                         name: "custcol_if_bol_col_nmfc_no",
                         summary: search.Summary.GROUP
                     });

                     strNMFC = (strNMFC != "- None -" ? strNMFC : "");
                     var strClass = arrMasterMasterItems[i].getText({
                         name: "custcol_if_bol_col_fak_class",
                         summary: search.Summary.GROUP
                     });

                     var strItemDescription = arrMasterMasterItems[i].getValue({
                             name: "custcol_item_description",
                             summary: search.Summary.GROUP
                         }) || "";
                   
                     strItemDescription = strItemDescription.replace('- None -','');
                   
                     strItemDescription = xml.escape({
                                 xmlText : strItemDescription
                             });
                   
                    strItem = xml.escape({
                                 xmlText : strItem
                             });
                     
                     strClass = (strClass != "- None -" ? strClass : "");
                     // strToReplace += "<tr height=\"25\">" +
                     // strIFnum + "</td>
                     // <td style=\"align:center; valign:middle;\">"
                     
                   strToReplace += "<tr><td>" + intHandUnitQty + "</td><td>" + strType + "</td><td>" + intQty + "</td><td>" + N(intWeight, 3) + " " +
                         strWeightunits + "</td>";
                     strToReplace += "<td ><span class=\"itemname\">" + strItem + " " + strItemDescription + "</span></td><td>" + strNMFC + "</td><td>" + strClass + "</td></tr>";
                   
                  
                 }
                 // log.debug("strToReplaceItems", strToReplace);
                objBoLPdf = objBoLPdf.replace("CARRIERINFORMATIONTABLE_PLACEHOLDER", strToReplace);
                 
                 // ////
                 var intSlicefrom = objBoLPdf.indexOf("<pdf>");
                 if (intCounter == 0) {
                     var strBegin = objBoLPdf.substring(0, intSlicefrom);
                     strToPrint += strBegin;
                     strToPrint += "<pdfset>";
                 }

                 strToPrint += objBoLPdf.substring(intSlicefrom);
                 // Master Bol and associated Bols
                 if (bMasterBol == "masterandsingle") {
                     strToPrint = strToPrint.replace("DELIVERINS_PLACEHOLDER", "See underlying Bills of Lading");
                     strToPrint = strToPrint.replace("ACC_PALCEHOLDER", "See underlying Bills of Lading");
                     strToPrint = strToPrint.replace("MASTER_CHECK_PLACEHOLDER", "Master Bill of Lading with associated BoLs");

                     for (var x = 0; x < arrMasters[z].internalIDs.length; x++) {
                         strToPrint += createSingleBol(arrMasters[z].internalIDs[x], 1)
                     }
                     // Master Bol Only
                 } else {
                     strToPrint = strToPrint.replace("DELIVERINS_PLACEHOLDER", "");
                     strToPrint = strToPrint.replace("ACC_PALCEHOLDER", "");
                     strToPrint = strToPrint.replace("MASTER_CHECK_PLACEHOLDER", "Master Bill of Lading only");
                 }
                 intCounter++;
                 // }
             } // for

             record.submitFields({
                 type: record.Type.SCRIPT_DEPLOYMENT,
                 id: intSLDepIdParam,
                 values: {
                     custscript_ns_master_bol_numbering: intBolNumber + 1
                 },
                 options: {
                     enableSourcing: false,
                     ignoreMandatoryFields: true
                 }
             });
         
         });
        // log.debug("strToPrint", strToPrint);
         return strToPrint;
     }

     /* This funtion creates single Bol from item fulfilments id */
     function createSingleBol(intIfId, intCounter) {
         var strToPrint = "";
         require(['N/render'], function(render) {
             log.debug("intIfId", intIfId);                

             var renderer = render.create();
             renderer.setTemplateByScriptId("CUSTTMPL_NS_BOL_TEMPLATE");
             var objRecIF = record.load({
                 type: record.Type.ITEM_FULFILLMENT,
                 id: intIfId,
             });
             var myContent = renderer.addRecord({
                 templateName: 'record',
                 record: objRecIF
             });
             var objBoLPdf = renderer.renderAsString();
             var intSlicefrom = objBoLPdf.indexOf("<pdf>");
             if (intCounter == 0) {
                 var strBegin = objBoLPdf.substring(0, intSlicefrom);
                 strToPrint += strBegin;
                 strToPrint += "<pdfset>";
             }
             var intSlicefrom = objBoLPdf.indexOf("<pdf>");
             strToPrint += objBoLPdf.substring(intSlicefrom);
         });

         return strToPrint;
     }

     /*
     Convert Weight units from Grams to Selected Unit (weightunit)
     */
     function convertFromGtoWeightUnit(weightunit, num) {

         var flConvertedWeight = 0;
         // log.debug("funweightunit",weightunit);
         switch (weightunit) {
             case "1": // KG
                 flConvertedWeight = num / 1000;
                 break;
             case "2": //g
                 flConvertedWeight = num;
                 break;
             case "3": //Lb
                 flConvertedWeight = num / 453.59237;
                 break;
             case "4": //Oz
                 flConvertedWeight = num / 28.34952313;
                 break;
         }
         // log.debug("flConvertedWeight",flConvertedWeight);
         return N(flConvertedWeight, 3);
     }

     /*
     Round given number(num) to given decimal places(places)
     */
     function N(num, places) {
         return +(Math.round(num + "e+" + places) + "e-" + places);
     }
     
     
     function convertWeightToBaseUnit(stBaseUnit, strItemWeightUnit, flLineWeight) {
         var flItemWeightBaseUnit = flLineWeight;
         switch (stBaseUnit) {
             case 'lbs':
                 switch (strItemWeightUnit) {
                     case 'oz':
                     case 'ozs':
                         flItemWeightBaseUnit = N(flLineWeight * 0.0625, 3);
                         break;
                     case 'kgs':
                         flItemWeightBaseUnit = N(flLineWeight * 2.20462262185, 3);
                         break;
                     case 'g':
                     case 'gs':
                         flItemWeightBaseUnit = N(flLineWeight * 0.00220462262185, 3);
                         break;                                        
                 }
                 break;
             case 'oz':
             case 'ozs':
                 switch (strItemWeightUnit) {                                       
                     case 'lbs':
                         flItemWeightBaseUnit = N(flLineWeight * 16, 3);
                         break;
                     case 'kgs':
                         flItemWeightBaseUnit = N(flLineWeight * 35.27396195, 3);
                         break;
                     case 'g':
                     case 'gs':
                         flItemWeightBaseUnit = N(flLineWeight * 0.03527396195, 3);
                         break;                                        
                 }
                 break;
             case 'kgs':
                 switch (strItemWeightUnit) {                                     
                     case 'lbs':
                         flItemWeightBaseUnit = N(flLineWeight * 0.45359237, 3);
                         break;
                     case 'oz':
                     case 'ozs':
                         flItemWeightBaseUnit = N(flLineWeight * 0.02834952, 3);
                         break;
                     case 'g':
                     case 'gs':
                         flItemWeightBaseUnit = N(flLineWeight * 0.001, 3);
                         break;                                        
                 }
                 break;
             case 'g':
             case 'gs':
                 switch (strItemWeightUnit) {                                     
                     case 'lbs':
                         flItemWeightBaseUnit = N(flLineWeight * 453.59237, 3);
                         break;
                     case 'oz':
                     case 'ozs':
                         flItemWeightBaseUnit = N(flLineWeight * 28.34952, 3);
                         break;
                     case 'kgs':
                         flItemWeightBaseUnit = N(flLineWeight * 1000, 3);
                         break;                                        
                 }
                 break;                                
         }
         
         return flItemWeightBaseUnit; 
     }

     return {
         createMasterBol: createMasterBol,
         createSingleBol: createSingleBol,
         convertWeightToBaseUnit : convertWeightToBaseUnit            
     };

 });
