function beforeLoad(type, form)
{
    if(type == "edit" || type == "create")
    {
        var record = nlapiGetNewRecord();

        if(record.getFieldValue('custrecord_in8_cefsf_sublist'))
        {
            var savedSearchId = nlapiLookupField('customrecord_in8_cef_sublists', record.getFieldValue('custrecord_in8_cefsf_sublist'), 'custrecord_in8_cefs_savedsearch');
            var search = nlapiLoadSearch(null, savedSearchId);

            var fieldName = form.addField("custpage_fieldname", 'select', "Select field");

            var options = getRecordFields(search.getSearchType());

            fieldName.addSelectOption('', '');

            for(var i = 0; i < options.length; i++)
            {
                fieldName.addSelectOption(options[i].id, options[i].label);
            }

            fieldName.setDefaultValue(record.getFieldValue('custrecord_in8_ceff_fieldid'));
        }
    }
}

function getRecordFields(recordType)
{
    try {


        var baserecord = nlapiCreateRecord(recordType, {recordmode: 'dynamic'});

        if (baserecord.getFieldValue('customform')) {
            var options = baserecord.getField('customform').getSelectOptions();

            for (var i = 0; i < options.length; i++) {
                if (parseInt(options[i].getId()) < 0) {
                    baserecord.setFieldValue('customform', options[i].getId());
                    break;
                }
            }
        }

        var basefields = baserecord.getAllFields();

        var fields = [];

        for (var i = 0; i < basefields.length; i++) {
            var fieldObj = baserecord.getField(basefields[i]);

            if (fieldObj &&
                fieldObj.getLabel() &&
                (fieldObj.getType() == "text" ||
                    fieldObj.getType() == "select" ||
                    fieldObj.getType() == "date")) {
                var field = {};

                if(baserecord.getRecordType() == "timebill" && basefields[i] == "trandate")
                    field.id = "date|"+fieldObj.getType();
                else
                    field.id = basefields[i]+"|"+fieldObj.getType();

                field.label = fieldObj.getLabel();

                fields.push(field);
            }

        }
        return fields;
    }
    catch(e)
    {
        throw nlapiCreateError('UNSUPPORTED_TYPE', 'This feature currently doesn\'t support this record type.');
    }
}