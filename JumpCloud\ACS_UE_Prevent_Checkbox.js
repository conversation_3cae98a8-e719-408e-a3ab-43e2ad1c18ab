/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function afterSubmit(context) {
        log.debug(context.type);
        if(context.type == context.UserEventType.CREATE || context.type == context.UserEventType.EDIT){
            var recId = context.newRecord.id;
            var recObj = record.load({
                id: recId,
                type: record.Type.CUSTOMER,
                isDynamic: false
            });

            recObj.setValue({ fieldId: 'custentity_3805_dunning_letters_toemail', value: false });
            recObj.save({
                enableSourcing: false,
                ignoreMandatoryFields: true
            });
        }

    }

    return {
        afterSubmit: afterSubmit
    }
});
