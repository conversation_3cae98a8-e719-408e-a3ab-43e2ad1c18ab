/**
 * Sales Order custom
 * 
 * Version    Date            Author           Remarks
 * 1.00       23 Nov 2018     Marcel P
 *
 */

function beforeSubmit(type) {

    try {
        if (nlapiGetFieldValue('custbody_in8_shop_id')) {
            if (type == 'create' && nlapiGetFieldValue('custbody_in8_shop_source') != 'pos') {
                setLocations();
            }
        }
    } catch (e) {
        nlapiLogExecution('ERROR', 'Error ', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
    }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit,
 *                      
 * @returns {Void}
 */
function afterSubmit(type) {

    try {
        if (nlapiGetFieldValue('custbody_in8_shop_id')) {
            if (type == 'create') {

                for (var i = 1; i < nlapiGetLineItemCount('item'); i++) {
                    // If the order comes in using a FBA item, need to create an Invoice with the Amazon warehouse location
                    if (nlapiLookupField('item', nlapiGetLineItemValue('item', 'item', i), 'custitem_fba_product') == 'T') {
                        nlapiLogExecution('DEBUG', 'In8', 'Amazon product.');
                        createFulfillment();
                        createInvoice();
                        return;
                    }
                }

                // If an order comes in from the ChiChi POS, it needs to create a cash sale (could have different payment methods) with the ChiChi warehouse on it. 
                // Auto create cash sale
                if (nlapiGetFieldValue('custbodyordersource') == 13) {
                    createFulfillment();
                    createCashSale();
                }
            } else if (type == 'edit') {

                // var cashSale = nlapiSearchRecord('cashsale', null, [
                //     new nlobjSearchFilter('createdfrom', null, 'is', nlapiGetRecordId())
                // ], [
                //     new nlobjSearchColumn('internalid')                
                // ]) || [];

                // if (cashSale.length) {
                //     nlapiSubmitField('cashsale', cashSale[0].getValue('internalid'), ['salesrep', 'location', 'paymentmethod', 'taxitem'], [nlapiGetFieldValue('salesrep'), nlapiGetFieldValue('location'), nlapiGetFieldValue('paymentmethod'), nlapiGetFieldValue('taxitem') ]);
                // }
            }
        }
    } catch (e) {
        nlapiLogExecution('ERROR', 'Error ', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
    }
}

function createFulfillment() {

    var itemFulfillmentRecord = nlapiTransformRecord('salesorder', nlapiGetRecordId(), 'itemfulfillment');
    itemFulfillmentRecord.setFieldValue('trandate', nlapiGetFieldValue('trandate'));
    itemFulfillmentRecord.setFieldValue('sendorderfulfillmentemail', 'F');
    itemFulfillmentRecord.setFieldValue('shipstatus', 'C');

    // Marks the checkbox on each line
    for (var i = 1; i <= itemFulfillmentRecord.getLineItemCount('item'); i++) {
        itemFulfillmentRecord.setLineItemValue('item', 'itemreceive', i, 'T');
        if (!itemFulfillmentRecord.getLineItemValue('item', 'quantity', i))
            itemFulfillmentRecord.setLineItemValue('item', 'quantity', i, itemFulfillmentRecord.getLineItemValue('item', 'itemquantity', i));
    }

    var id = nlapiSubmitRecord(itemFulfillmentRecord, true, true);

    nlapiLogExecution('DEBUG', 'In8', 'Fulfillement Id: ' + id);
}

function createInvoice() {

    // Creates the Invoice
    var tranRecord = nlapiTransformRecord('salesorder', nlapiGetRecordId(), 'invoice');
    tranRecord.setFieldValue('trandate', nlapiGetFieldValue('trandate'));
    tranRecord.setFieldValue('location', 11); // Amazon Warehouse
    var id = nlapiSubmitRecord(tranRecord, true, true);

    nlapiLogExecution('DEBUG', 'In8', 'Invoice Id: ' + id);
}

function createCashSale() {

    var tranRecord = nlapiTransformRecord('salesorder', nlapiGetRecordId(), 'cashsale');
    tranRecord.setFieldValue('trandate', nlapiGetFieldValue('trandate'));
    tranRecord.setFieldValue('ccapproved', 'T');
    tranRecord.setFieldValue('location', 25);
    //tranRecord.setFieldValue('account', settings.billAccount);
    var id = nlapiSubmitRecord(tranRecord, true, true);

    nlapiLogExecution('DEBUG', 'In8', 'Cash Sale Id: ' + id);
}

function setLocations() {

    var itemsIds = [];
    var PALM_SPRING = 21;
    var CIRCUS = 36;
    var CHICHI = 25;

    nlapiLogExecution('DEBUG', 'In8', 'Shopify order. Set line items locations. Shopify Id: ' + nlapiGetFieldValue('custbody_in8_shop_id'));

    // Get list of items from the order
    for (var i = 1; i <= nlapiGetLineItemCount('item'); i++) {
        itemsIds.push(nlapiGetLineItemValue('item', 'item', i));
    }

    var itemsQuantity = getInventoryLocation([PALM_SPRING, CIRCUS, CHICHI], itemsIds);

    for (var i = 1; i <= nlapiGetLineItemCount('item'); i++) {
        var internalId = nlapiGetLineItemValue('item', 'item', i);
        var quantity = nlapiGetLineItemValue('item', 'quantity', i);

        if (itemsQuantity[internalId]) {
            if (parseFloat(itemsQuantity[internalId][PALM_SPRING]) >= parseFloat(quantity)) {
                nlapiLogExecution('DEBUG', 'In8', 'Line ' + i + ' - Location: Palm Spring. Quantity available: ' + itemsQuantity[internalId][PALM_SPRING]);
                nlapiSetLineItemValue('item', 'location', i, PALM_SPRING);
            } else if (parseFloat(itemsQuantity[internalId][CIRCUS]) >= parseFloat(quantity)) {
                nlapiLogExecution('DEBUG', 'In8', 'Line ' + i + ' - Location: Circus. Quantity available: ' + itemsQuantity[internalId][CIRCUS]);
                nlapiSetLineItemValue('item', 'location', i, CIRCUS);
            } else if (parseFloat(itemsQuantity[internalId][CHICHI]) >= parseFloat(quantity)) {
                nlapiLogExecution('DEBUG', 'In8', 'Line ' + i + ' - Location: Chichi. Quantity available: ' + itemsQuantity[internalId][CHICHI]);
                nlapiSetLineItemValue('item', 'location', i, CHICHI);
            } else {
                nlapiLogExecution('DEBUG', 'In8', 'Line ' + i + ' - Do not set location.');
            }
        } else {
            nlapiLogExecution('DEBUG', 'In8', 'Line ' + i + ' - Do not set location!');
        }
    }
}

function getInventoryLocation(locations, recordIds) {

    var filters = [],
        columns = [],
        searchResults,
        sourceField,
        total = 0,
        i = 0;

    filters[i++] = new nlobjSearchFilter('inventorylocation', null, 'anyof', locations);
    filters[i++] = new nlobjSearchFilter('internalid', null, 'anyof', recordIds);

    i = 0;

    columns[i++] = new nlobjSearchColumn('internalid');
    columns[i++] = new nlobjSearchColumn('inventorylocation');
    columns[i++] = new nlobjSearchColumn('locationquantityavailable');

    searchResults = nlapiSearchRecord('item', null, filters, columns) || [];

    var items = [];

    for (i = 0; i < searchResults.length; i++) {
        if (searchResults[i].getValue('locationquantityavailable')) {
            var internalId = searchResults[i].getValue('internalid');
            var location = searchResults[i].getValue('inventorylocation');

            if (!items[internalId]) {
                items[internalId] = [];
            }
            items[internalId][location] = parseFloat(searchResults[i].getValue('locationquantityavailable'));
        }
    }
    return items;
}