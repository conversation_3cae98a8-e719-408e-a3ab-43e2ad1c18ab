<#-- format specific processing -->

<#function getVoidCheckIndicator payment>
<#assign value = "">
<#assign reversalDate = payment.reversaldate>
<#if reversalDate?has_content>
<#assign value = "V">
</#if>
<#return value>
</#function>

<#function getReferenceNote payment>
<#assign value = "">
<#if payment.recordtype == 'cashrefund'>
<#return payment.otherrefnum>
</#if>
<#return payment.tranid>
</#function>

<#-- template building -->
#OUTPUT START#
<#list payments as payment>
<#assign amount = getAmount(payment)>
<#assign entity = entities[payment_index]>
${cbank.custpage_pp_custrecord_2663_acct_num},${payment.trandate?string("MM/dd/yy")},${getReferenceNote(payment)},${formatAmount(getAmount(payment),"dec")},${buildEntityName(entity,true)?replace(",", " ")}
</#list>
#OUTPUT END#