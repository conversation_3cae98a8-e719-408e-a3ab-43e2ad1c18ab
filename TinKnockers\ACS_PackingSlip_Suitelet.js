/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/render', 'N/record'], function(https, render, record) {

    function onRequest(context) {
        if(context.request.method == https.Method.GET){
            requestParams = context.request.parameters;
            workOrderId = parseInt(requestParams.custpage_WOId);

            // CREATE TEMPLATE RENDERER OBJECT
            var renderer = render.create();

            // SELECT TEMPLATE TO BE USED VIA SCRIPT ID
            renderer.setTemplateByScriptId({
                scriptId: "CUSTTMPL_ACS_CUSTOM_PACKING_SLIP"
            });

            // ADD THE RECORD TO BE USED BASED ON PARAMETERS GIVEN
            renderer.addRecord({
                templateName: 'record',
                record: record.load({
                    type: record.Type.WORK_ORDER,
                    id: workOrderId,
                    isDynamic: true
                })
            });

            // RENDER AS STRING
            var packingSlipXML = renderer.renderAsString();

            // SEND RESPONSE XML TO RENDER AS PDF
            context.response.renderPdf({
                xmlString: packingSlipXML
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
