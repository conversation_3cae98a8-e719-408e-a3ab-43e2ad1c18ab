function uploader(request, response) {
    if (request.getMethod() == 'GET') {
        var form = nlapiCreateForm('Attach Multiple Files');
        var entityID = nlapiGetContext().getUser();
        var entityName = nlapiLoadRecord('customer', entityID);
        //Show Customer ID
        var entityField = form.addField('custpage_entity', 'text', 'Customer').setDefaultValue(entityName.getFieldValue('entityid'));

        //Add File field
        var fileField1 = form.addField('custpage_file1', 'file', 'Select 1st File');
        var fileField2 = form.addField('custpage_file2', 'file', 'Select 2nd File');
        var fileField3 = form.addField('custpage_file3', 'file', 'Select 3rd File');

        //Set File fields as mandatory
        fileField1.setMandatory(true);
        fileField2.setMandatory(true);
        fileField3.setMandatory(true);

        form.addSubmitButton();
        form.addResetButton();
        response.writePage(form);

    } else {

        //var entity = request.getParameter("custpage_entity");
        var entityID = nlapiGetContext().getUser();
        var file1 = request.getFile("custpage_file1");
        var file2 = request.getFile("custpage_file2");
        var file3 = request.getFile("custpage_file3");

        // set the folder where this file will be added. In this case, 10 is the internal ID
        // of the SuiteScripts folder in the NetSuite file cabinet	      
        file1.setFolder(10);
        file2.setFolder(10);
        file3.setFolder(10);

        // Create file and upload it to the file cabinet.
        var id1 = nlapiSubmitFile(file1);
        var id2 = nlapiSubmitFile(file2);
        var id3 = nlapiSubmitFile(file3);

        // Attach file to customer record
        nlapiAttachRecord("file", id1, "customer", entityID);
        nlapiAttachRecord("file", id2, "customer", entityID);
        nlapiAttachRecord("file", id3, "customer", entityID);

        // Show successfully added files
        var form = nlapiCreateForm('Files Attached');
        form.addField("custpage_uploded_file1", "text", 'File 1').setDisplayType('inline').setDefaultValue(file1.getName());
        form.addField("custpage_uploded_file2", "text", 'File 2').setDisplayType('inline').setDefaultValue(file2.getName());
        form.addField("custpage_uploded_file3", "text", 'File 3').setDisplayType('inline').setDefaultValue(file3.getName());

        response.writePage(form);
    }

}