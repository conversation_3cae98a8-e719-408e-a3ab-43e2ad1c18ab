/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/runtime', 'N/record', 'N/search'], function(runtime, record, search) {

    var itemNames = {};

    function afterSubmit(context) {
        log.debug('context.type', context.type);
        if(context.type == context.UserEventType.EDIT) {
            var oldRec = context.oldRecord;
            var newRec = context.newRecord;

            var sublistId = 'itempricing';

            var oldLineCount = oldRec.getLineCount({ sublistId: sublistId });
            var newLineCount = newRec.getLineCount({ sublistId: sublistId });

            // log.debug('counts', {
            //     oldLineCount: oldLineCount,
            //     newLineCount: newLineCount
            // })

            var oldRecordPriceList = {};
            for(var i = 0; i < oldLineCount; i++) {
                var oldPrice = oldRec.getSublistValue({ sublistId: sublistId, fieldId: 'price', line: i });
                var itemId = oldRec.getSublistValue({ sublistId: sublistId, fieldId: 'item', line: i });
                var itemText = oldRec.getSublistText({ sublistId: sublistId, fieldId: 'item', line: i });
                var level = oldRec.getSublistValue({ sublistId: sublistId, fieldId: 'level', line: i });
                var levelText = oldRec.getSublistText({ sublistId: sublistId, fieldId: 'level', line: i });

                itemNames[itemId] = itemText;

                if(!oldRecordPriceList.hasOwnProperty(itemId)) {
                    oldRecordPriceList[itemId] = [{
                        line: i,
                        itemText: itemText,
                        level: level,
                        levelText: ((level == -1) ? "Custom" : levelText),
                        price: oldPrice
                    }]
                } else {
                    oldRecordPriceList[itemId].push({
                        line: i,
                        itemText: itemText,
                        level: level,
                        levelText: ((level == -1) ? "Custom" : levelText),
                        price: oldPrice
                    })
                }

            }

            var newRecordPriceList = {};
            for(var i = 0; i < newLineCount; i++) {
                var newPrice = newRec.getSublistValue({ sublistId: sublistId, fieldId: 'price', line: i });
                var itemId = newRec.getSublistValue({ sublistId: sublistId, fieldId: 'item', line: i });
                var itemText = newRec.getSublistText({ sublistId: sublistId, fieldId: 'item', line: i });
                var level = newRec.getSublistValue({ sublistId: sublistId, fieldId: 'level', line: i });
                var levelText = newRec.getSublistText({ sublistId: sublistId, fieldId: 'level', line: i });

                itemNames[itemId] = itemText;

                if(!newRecordPriceList.hasOwnProperty(itemId)) {
                    newRecordPriceList[itemId] = [{
                        line: i,
                        itemText: itemText,
                        level: level,
                        levelText: ((level == -1) ? "Custom" : levelText),
                        price: newPrice
                    }]
                } else {
                    newRecordPriceList[itemId].push({
                        line: i,
                        itemText: itemText,
                        level: level,
                        levelText: ((level == -1) ? "Custom" : levelText),
                        price: newPrice
                    })
                }
            }

            var changes = [];

            for(var key in oldRecordPriceList) {
                if(!newRecordPriceList.hasOwnProperty(key)){
                    changes.push({
                        action: 'Removed',
                        itemName: itemNames[key],
                        item: key,
                        key: 'item',
                        from: itemNames[key],
                        to: ''
                    });

                    for(var i = 0; i < oldRecordPriceList[key].length; i++) {
                        
                        var fields = ((oldRecordPriceList[key][i]['levelText'] == 'Custom') ? ['price', 'level'] : ['level'] );

                        fields.forEach(function(field, index) {
                            var oldVal = oldRecordPriceList[key][i][field];
                            changes.push({
                                action: 'Removed',
                                itemName: oldRecordPriceList[key][i]['itemText'],
                                item: key,
                                key: field,
                                from: ((index == 1) ? oldRecordPriceList[key][i]['levelText'] : oldVal),
                                to: ''
                            });
                        });
                    }

                } else {
                    for(var i = 0; i < oldRecordPriceList[key].length; i++) {
                        ['price', 'level'].forEach(function(item, index) {
                            var newVal = newRecordPriceList[key][i][item];
                            var oldVal = oldRecordPriceList[key][i][item];
                            if(newVal != oldVal){
                                changes.push({
                                    action: 'Changed',
                                    itemName: oldRecordPriceList[key][i]['itemText'],
                                    item: key,
                                    key: item,
                                    from: ((index == 1) ? oldRecordPriceList[key][i]['levelText'] : oldVal),
                                    to: ((index == 1) ? newRecordPriceList[key][i]['levelText'] : newVal)
                                });
                            }
                        });
                    }
                }
            }

            for(var key in newRecordPriceList) {
                if(!oldRecordPriceList.hasOwnProperty(key)){
                    changes.push({
                        action: 'Added',
                        itemName: itemNames[key],
                        item: key,
                        key: 'item',
                        from: '',
                        to: itemNames[key]
                    });

                    for(var i = 0; i < newRecordPriceList[key].length; i++) {
                        
                        var fields = ((newRecordPriceList[key][i]['levelText'] == 'Custom') ? ['price', 'level'] : ['level'] );

                        fields.forEach(function(field, index) {
                            var newVal = newRecordPriceList[key][i][field];
                            changes.push({
                                action: 'Added',
                                itemName: newRecordPriceList[key][i]['itemText'],
                                item: key,
                                key: field,
                                from: '',
                                to: ((index == 1) ? newRecordPriceList[key][i]['levelText'] : newVal)
                            });
                        });
                    }
                }
            }

            log.debug('changes', changes);
            log.debug('recordObjs', { oldRecordPriceList: oldRecordPriceList,  newRecordPriceList: newRecordPriceList });

            if(changes.length) {
                for(var i = 0; i < changes.length; i++) {

                    var logRecObj = record.create({
                        type: 'customrecord_acs_item_pricing_changes',
                        isDynamic: false
                    });

                    logRecObj.setValue({ fieldId: 'custrecord_field', value: changes[i].key });
                    logRecObj.setValue({ fieldId: 'custrecord_from', value: changes[i].from });
                    logRecObj.setValue({ fieldId: 'custrecord_to', value: changes[i].to });


                    logRecObj.setValue({ fieldId: 'name', value: newRec.id + "_" + new Date() });
                    logRecObj.setValue({ fieldId: 'custrecord_audit_date', value: new Date() });
                    logRecObj.setValue({ fieldId: 'custrecord_set_by', value: runtime.getCurrentUser().id });
                    logRecObj.setValue({ fieldId: 'custrecord_type', value: changes[i].action  });
                    logRecObj.setValue({ fieldId: 'custrecord_customer', value: newRec.id  });
                    logRecObj.setValue({ fieldId: 'custrecord_item', value: changes[i].item  });
                    
                    logRecObj.save();

                }
            }

        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
