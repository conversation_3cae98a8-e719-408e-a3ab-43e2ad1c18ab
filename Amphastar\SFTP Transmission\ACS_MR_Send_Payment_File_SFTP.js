/**
 * @NApiVersion 2.x
 * @NScriptType MapReduceScript
 * @NModuleScope SameAccount
 */
 define(['N/file', "N/sftp", 'N/runtime', 'N/search', 'N/record', 'N/email', 'N/url', 'N/https'],
    function (file, sftp, runtime, search, record, email, url, https) {

        
        function decryptFile(contents) {
            
            var scriptID = 'customscript_15152_encryption_suitelet';
            var deployementID = 'customdeploy_15152_encryption_suitelet';

            var decryptParameters = {
                action : 'decrypt',
                actionInput : contents,
                featureKey : 'NACHA'
            };

            var suiteletURL = url.resolveScript({
                scriptId: scriptID,
                deploymentId: deployementID,
                returnExternalUrl : true
            });

            var response = https.post({
                url: suiteletURL,
                body: decryptParameters
            });
            var body = JSON.parse(response.body);

            if(body.isSuccess){
                return body.actionOutput;
            }
            
            return false;
            

        }

        var filesSent = [];

        /**
         * Marks the beginning of the Map/Reduce process and generates input data.
         *
         * @typedef {Object} ObjectRef
         * @property {number} id - Internal ID of the record instance
         * @property {string} type - Record type id
         *
         * @return {Array|Object|Search|RecordRef} inputSummary
         * @since 2015.1
         */
        function getInputData() {

            try {

                // retrieve files created today
                var searchObj = search.load({
                    id: 'customsearch_acs_payment_file_created_to'
                });
        
                return searchObj;
            } catch (e) {
                log.error('getInputData error', e);
            }

        }

        /**
         * Executes when the map entry point is triggered and applies to each key/value pair.
         *
         * @param {MapSummary} context - Data collection containing the key/value pairs to process through the map stage
         * @since 2015.1
         */
        function map(context) {

            try {

                var scriptObj = runtime.getCurrentScript();

                // get PFA and File ID
                var pfaID = context.key;
                log.debug('pfaID', pfaID);
                var contextValues = JSON.parse(context.value);
                var fileId = contextValues.values.custrecord_2663_file_ref.value;

                // get SFTP Credentials here using script parameters
                var sftpHostKey = scriptObj.getParameter({ name: 'custscript_sftp_host_key' });
                var sftpUsername = scriptObj.getParameter({ name: 'custscript_sftp_username' });
                var sftpPort = scriptObj.getParameter({ name: 'custscript_sftp_port' });
                var sftpKeyId = scriptObj.getParameter({ name: 'custscript_sftp_key_id' });
                var sftpServerUrl = scriptObj.getParameter({ name: 'custscript_sftp_server_url' });
                var archiveId = scriptObj.getParameter({ name: 'custscript_sftp_archive_id' });

                // load file object
                var origFileObj = file.load({
                    id: fileId
                });
            
                var fileContent = origFileObj.getContents();
                if(fileContent) {

                    // set the name based on bank's requirements and move to 'sent folder'
                    log.audit("Archive ID", { archiveId: archiveId});

                    var fileObj = file.create({
                        name: origFileObj.name,
                        fileType: file.Type.PLAINTEXT,
                        contents: fileContent,
                        folder: archiveId
                    });

                    var newFileID = fileObj.save();

                    // reload file
                    var fileToBeSentObj = file.load({
                        id: newFileID
                    });

                    log.audit("File Saved", "ID :" + newFileID);
                    
                    log.audit("Start SFTP Transmission", "-------- start SFTP transmission --------");

                    // connect to sftp server using script parameters
                    var sftpConnection = sftp.createConnection({
                        username: sftpUsername,
                        keyId: sftpKeyId,
                        url: sftpServerUrl,
                        port: Number(sftpPort),
                        hostKey: sftpHostKey
                    });

                    log.debug('SFTP Connection', sftpConnection);

                    // send file to server
                    sftpConnection.upload({
                        file: fileToBeSentObj,
                        replaceExisting: true
                    });
                    
                    log.debug('File has been transmitted successfully!', { fileId: fileToBeSentObj.id, fileName: fileToBeSentObj.name });
                            
                    record.submitFields({
                        type: 'customrecord_2663_file_admin',
                        id: pfaID,
                        values: {
                            custrecord_file_sent: true
                        }
                    });

                }
            } catch(e) {
                log.error('Map - Error during sftp upload', e);
            }
        }

        /**
         * Executes when the reduce entry point is triggered and applies to each group.
         *
         * @param {ReduceSummary} context - Data collection containing the groups to process through the reduce stage
         * @since 2015.1
         */
        function reduce(context) {

        }


        /**
         * Executes when the summarize entry point is triggered and applies to the result set.
         *
         * @param {Summary} summary - Holds statistics regarding the execution of a map/reduce script
         * @since 2015.1
         */
        function summarize(summary) {
                
            // var scriptObj = runtime.getCurrentScript();
            // var emailSuccess = scriptObj.getParameter({ name: 'custscript_email_success_transmit' });
            // var emailAuthor = scriptObj.getParameter({ name: 'custscript_email_author' });

            // var emailBody = "Good Day!<br><br>Payment has been successfully transmitted. Transmitted files can be seen in the File Cabinet under Sent Payment Files.<br>"
            // emailBody += '<ul>';
            // for(var i = 0; i < filesSent.length; i++) {
            //     emailBody += '<li>' + filesSent[i] + '</li>';
            // }
            // emailBody += '</ul>';
            
            // try {
            //     email.send({
            //         author: emailAuthor,
            //         recipients: emailSuccess,
            //         subject: 'Payment Transmission Successful',
            //         body: emailBody
            //     });
            // } catch (e) {
            //     log.error('Summarize - Error while trying to send email', e);
            // }

        }

        return {
            getInputData: getInputData,
            map: map,
            reduce: reduce,
            summarize: summarize
        };

    });