/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       15 Mar 2016     venkatsankaran
 *
 */

/**
 * @param {Object} dataIn Parameter object
 * @returns {Object} Output object
 */

function getInvData(dataIn) {
	var retData = new Object();
	try 
	{
		nlapiLogExecution('Debug', 'Checking', 'In restlet : ' );
		var srchId = 'customsearch_invdata4webstore';
		var filters = new Array();
		var outDetails = new Array();
		var list = nlapiSearchRecord('item', srchId, filters);
		if (list == null || list.length <= 0)
		{
			retData.status = 'Success';
			retData.message = '';
			return retData;	
		}
		if (list.length >= 1000)
		{
			list = getAllRowsFromList('item', srchId, filters);
		}
		for (var intPos = 0; intPos < list.length; intPos++)
		{
			var srchRow = list[intPos];
			var cols = srchRow.getAllColumns();
			var str = '{';
			var line = [];

			cols.forEach(function(c) {
				if(c.getLabel() )
				{
					var data = srchRow.getText(c) || srchRow.getValue(c) ;
					if (data == '- None -')
						data = '';
					var lbl = c.getLabel();
					line.push( '"' + lbl + '":"' + escapeSpecialChars(data)  + '"');
				}
			});
			str += line.join(",") ;
			str += '}';	
			outDetails.push(JSON.parse(str));
		}		
		retData.status = 'Success';
		retData.message = '';
		retData.result = outDetails;
		return retData;
	}
	catch (ex)
	{
		retData.status = 'Error';
		retData.message = 'Exception in processing request : ' + ex.toString();
        var msg = '';
        if (typeof ex.getStackTrace === 'function') {
            msg = ex.toString() + '<br />' + ex.getStackTrace().join('<br />');
        }
        else if (ex.stack) {
            msg = ex.toString() + ex.stack.replace(/(^|\s)at(\s|$)/g, '<br />at ');
        }
        else {
            msg = ex.toString();
        }		
		nlapiLogExecution('Debug','Checking','Error : ' + msg );
		return retData;			
	}
	
}

function getShipData(dataIn) {
	var retData = new Object();
	try 
	{
		nlapiLogExecution('Debug', 'Checking', 'json : ' + JSON.stringify(dataIn));
		var frmDate = '';//convNull(dataIn.fromDate);
		var toDate = '';//convNull(dataIn.toDate);
		var orderId = '';
		if (dataIn == null )
		{
			frmDate = nlapiDateToString(nlapiAddDays(new Date(), -6)); //Five days before Yesterday if no date was provided
			toDate = nlapiDateToString(nlapiAddDays(new Date(), -1)); //Yesterday if no date was provided
		}
		else
		{
			frmDate = convNull(dataIn.fromDate);
			toDate = convNull(dataIn.toDate);
			if(frmDate == '' || toDate == '')
			{
				frmDate = nlapiDateToString(nlapiAddDays(new Date(), -6)); //Five days before Yesterday if no date was provided
				toDate = nlapiDateToString(nlapiAddDays(new Date(), -1)); //Yesterday if no date was provided
			}
		}
		nlapiLogExecution('Debug','Checking', 'dataIn : ' + JSON.stringify(dataIn) );
		orderId = convNull(dataIn.sourceOrderId);
		
		var srchId = 'customsearch_shipment4webstore';
		var filters = new Array();
		if (orderId != '')
			filters.push(new nlobjSearchFilter('externalidstring', 'createdfrom', 'is', 'WS-' + orderId));				
		else if(frmDate != '' && toDate != '')
			filters.push(new nlobjSearchFilter('trandate', null, 'within', frmDate, toDate));
				
		var outDetails = new Array();
		var list = nlapiSearchRecord('itemfulfillment', srchId, filters);
		if (list == null || list.length <= 0)
		{
			retData.status = 'Success';
			retData.message = '';
			retData.result = outDetails;
			return retData;	
		}
		if (list.length >= 1000)
		{
			list = getAllRowsFromList('itemfulfillment', srchId, filters);
		}
		for (var intPos = 0; intPos < list.length; intPos++)
		{
			var srchRow = list[intPos];
			var cols = srchRow.getAllColumns();
			var str = '{';
			var line = [];

			cols.forEach(function(c) {
				if(c.getLabel() )
				{
					var data = srchRow.getText(c) || srchRow.getValue(c) ;
					if (data == '- None -')
						data = '';
					var lbl = c.getLabel();
					line.push( '"' + lbl + '":"' + escapeSpecialChars(data)  + '"');
				}
			});
			str += line.join(",") ;
			str += '}';	
			outDetails.push(JSON.parse(str));
		}		
		retData.status = 'Success';
		retData.message = '';
		retData.result = outDetails;
		return retData;
	}
	catch (ex)
	{
		retData.status = 'Error';
		retData.message = 'Exception in processing request : ' + ex.toString();
        var msg = '';
        if (typeof ex.getStackTrace === 'function') {
            msg = ex.toString() + '<br />' + ex.getStackTrace().join('<br />');
        }
        else if (ex.stack) {
            msg = ex.toString() + ex.stack.replace(/(^|\s)at(\s|$)/g, '<br />at ');
        }
        else {
            msg = ex.toString();
        }		
		nlapiLogExecution('Debug','Checking','Error : ' + msg );
		return retData;			
	}
	
}

function postOrder(dataIn) {
	
	var retData = new Object();
	try 
	{
		nlapiLogExecution('Debug', 'Checking', 'In restlet : ' + new Date());
		if (dataIn == null )
		{
			retData.status = 'Error';
			retData.message = 'No input data provided';
			nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
			return retData;
		}
		nlapiLogExecution('Debug', 'Checking', 'json : ' + JSON.stringify(dataIn));
		var nsOrderNo = '';
		//Getting input parameters
		var orderId = convNull(dataIn.sourceOrderId);
		var customerId = convNull(dataIn.customerId);
		if (customerId == '')
			customerId = '11111111111';
		else if (customerId != '11111111111')
			customerId = 'WS-' + customerId ;
		var firstName = convNull(dataIn.firstName);
		var lastName = convNull(dataIn.lastName);
		var nameOnCard  = convNull(dataIn.nameOnCard );
		var email = convNull(dataIn.email);
		var expiryMonth  = convNull(dataIn.expiryMonth );
		var ccNumber  = convNull(dataIn.ccnumToken );
		var currency  = convNull(dataIn.currency) ;
		if (currency == '') 
			currency = 'USD'; 
		var billToStreet  = convNull(dataIn.billToStreet );
		var billToStreet2  = convNull(dataIn.billToStreet2 );
		var billToCity = convNull(dataIn.billToCity);
		var billToStateCode   = convNull(dataIn.billToStateCode  );
		var billToZipCode   = convNull(dataIn.billToZipCode  );
		var billToCountryCode   = convNull(dataIn.billToCountryCode  );
		var shipToStreet  = convNull(dataIn.shipToStreet );
		var shipToStreet2  = convNull(dataIn.shipToStreet2 );
		var shipToStateCode   = convNull(dataIn.shipToStateCode  );
		var shipToCity = convNull(dataIn.shipToCity);
		var shipToZipCode   = convNull(dataIn.shipToZipCode  );
		var shipToCountryCode   = convNull(dataIn.shipToCountryCode  );
		var shipMethod = convNull(dataIn.shipMethod);
		var shippingFee = convNull(dataIn.shippingFee);
		var tax = convNull(dataIn.tax);
		var phone = convNull(dataIn.phone);
		var items = dataIn.items;
		var discountitem = dataIn.discountitem;
		
		var billToName = dataIn.billToName;		//NS CSS Case 3013929
		var shipToName = dataIn.shipToName;		//NS CSS Case 3013929
		
		if (items == undefined)
			items = dataIn.Items;
		var couponCode = convNull(dataIn.couponCode);
		var discountAmount = (dataIn.discountAmount == null || dataIn.discountAmount == undefined || dataIn.discountAmount == '' ) ? 0 : dataIn.discountAmount;
		if (discountAmount == 0)
			couponCode = '';
		var paymentType  = convNull(dataIn.paymentType) ;
		var authCode  = convNull(dataIn.authCode) ; // DSG Case 60453
		var pnRef  = convNull(dataIn.pnRef) ; // DSG Case 60453
		if (orderId == '' || customerId == '' || firstName == '' || lastName == '' || email == ''
				|| items == null || items.length <= 0 
			)
		{
			retData.status = 'Error';
			retData.message = 'Required fields are missing.';
			nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
			return retData;			
		}
        var parentCustomerId = getParentCustomerId(); // '1264885' Modified per DSG Case 60386
		var classId = '3';
		var billingName = firstName + ' ' + lastName;
		var currencyId = getCurrencyId(currency);
		if (currencyId == '')
		{
			retData.status = 'Error';
			retData.message = 'Invalid currency : ' + currency;
			nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
			return retData;			
		}
		if (paymentType == '')
		{
			retData.status = 'Error';
			retData.message = 'Payment Type is required for payment. ';
			nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
			return retData;					
		}
		var custId = getCustId(customerId);
		if (custId == null || custId == '')
		{
			var custRec = nlapiCreateRecord('customer', {recordmode:'dynamic'});
			custRec.setFieldValue('firstname', firstName);
			custRec.setFieldValue('lastname', lastName);
          	custRec.setFieldValue('companyname', firstName + ' ' + lastName);
			custRec.setFieldValue('externalid', customerId);
			custRec.setFieldValue('parent', parentCustomerId);
			custRec.setFieldValue('custentity_dsg_class', classId);
			custRec.setFieldText('category', 'Retail - eCommerce');
			custRec.setFieldValue('entitystatus', 13);
			custRec.setFieldValue('email', email);
			custRec.setFieldValue('phone', phone);
			custRec.setFieldValue('isperson', 'T');			
			custRec.setFieldValue('istaxable', 'T');
			custRec.selectNewLineItem('addressbook');
			addrSubRec = custRec.createCurrentLineItemSubrecord('addressbook', 'addressbookaddress');
			addrSubRec.setFieldValue('country', billToCountryCode );
			addrSubRec.setFieldValue('addressee', firstName + ' ' + lastName);
			addrSubRec.setFieldValue('addr1', billToStreet );
			addrSubRec.setFieldValue('addr2', billToStreet2);
			addrSubRec.setFieldValue('addr3', '');
			addrSubRec.setFieldValue('city', billToCity);
			if(billToCountryCode == 'US' || billToCountryCode == 'AU' || billToCountryCode == 'CA')
				addrSubRec.setFieldValue('dropdownstate', billToStateCode );
			else
			{
				addrSubRec.setFieldValue('dropdownstate', '');
				addrSubRec.setFieldValue('state', billToStateCode );
			}
			addrSubRec.setFieldValue('zip', billToZipCode );
			addrSubRec.setFieldValue('addrphone', phone); 
			addrSubRec.commit();
			custRec.commitLineItem('addressbook');
			// End changes per DSG Case 46778
			custRec.setFieldValue('currency', currencyId); 
			try
            {
				custId = nlapiSubmitRecord(custRec, true,true);        
            }
          	catch (ex)
            {
				retData.status = 'Error';
                if (ex instanceof nlobjError)
                    retData.message = ex.getDetails();
                else
                    retData.message = ex.toString();
		        var msg = '';
        		if (typeof ex.getStackTrace === 'function') {
            		msg = ex.toString() + '<br />' + ex.getStackTrace().join('<br />');
        		}
		        else if (ex.stack) {
        		    msg = ex.toString() + ex.stack.replace(/(^|\s)at(\s|$)/g, '<br />at ');
        		}
		        else {
        		    msg = ex.toString();
        		}
				nlapiLogExecution('Debug','Checking','Error creating customer : ' + msg );
				if (retData.message == null)
					retData.message = 'Unexpected Error in creating customer, Contact Netsuite Administrator.';
				return retData;
            }
			nlapiLogExecution('Debug','Checking','After customer save : ' + new Date());		
		}
		// Start changes per DSG Case 48277
		else
		{
			var custRec = nlapiLoadRecord('customer', custId);
			if (nlapiGetFieldValue('currency') != currencyId)
			{
				var currFound = false;
				for (var currPos = 1; currPos <= custRec.getLineItemCount('currency'); currPos++ )
				{
					if (custRec.getLineItemValue('currency', 'currency', currPos) == currencyId )
					{
						currFound = true;
						break;
					}
				}
				if (!currFound)
				{
					custRec.selectNewLineItem('currency');
					custRec.setCurrentLineItemValue('currency', 'currency', currencyId);
					custRec.commitLineItem('currency');
					nlapiSubmitRecord(custRec, true,true);
				}
			}
		}
		var orderRec = nlapiCreateRecord('salesorder', {recordmode:'dynamic'});
		orderRec.setFieldValue('entity', custId);
		orderRec.setFieldValue('class', classId);
		//orderRec.setFieldValue('location', locId);
		orderRec.setFieldValue('externalid', 'WS-' + orderId);
		orderRec.setFieldValue('currency', currencyId); 
		orderRec.setFieldValue('email', email);
		//Now setup Shipping address
		orderRec.setFieldText('shipaddresslist', '');
		orderRec.setFieldText('billaddresslist', '');
		//orderRec.setFieldValue('shipaddresslist', '-2');
		orderRec.setFieldValue('shipcountry', shipToCountryCode );
		orderRec.setFieldValue('shipaddr1', '');
		orderRec.setFieldValue('shipaddr2', ''); 
		orderRec.setFieldValue('shipaddr3', ''); 
		orderRec.setFieldValue('shipcity', ''); 
		orderRec.setFieldValue('shipstate', ''); 
		orderRec.setFieldValue('shipzip', '');
		orderRec.setFieldValue('shipphone', ''); 
		var shipAddr;
		if (orderRec.viewSubrecord('shippingaddress') != null)
			shipAddr = orderRec.editSubrecord('shippingaddress');
		else
			shipAddr = orderRec.createSubrecord('shipingaddress');
		shipAddr.setFieldValue('country', shipToCountryCode );
//		shipAddr.setFieldValue('addressee', firstName + ' ' + lastName);	//NS CSS Case 3013929
		if (shipToName != null && shipToName != '')							//NS CSS Case 3013929
			shipAddr.setFieldValue('addressee', shipToName);
		else
			shipAddr.setFieldValue('addressee', firstName + ' ' + lastName);
		shipAddr.setFieldValue('addr1', shipToStreet );
		shipAddr.setFieldValue('addr2', shipToStreet2);
		shipAddr.setFieldValue('addr3', '');
		shipAddr.setFieldValue('city', shipToCity);
		if(shipToCountryCode == 'US' || shipToCountryCode == 'AU' || shipToCountryCode == 'CA')
			shipAddr.setFieldValue('dropdownstate', shipToStateCode);
		else
		{
			shipAddr.setFieldValue('dropdownstate', '');
			shipAddr.setFieldValue('state', shipToStateCode);
		}
		shipAddr.setFieldValue('zip', shipToZipCode );
		shipAddr.setFieldValue('addrphone', phone); 
		shipAddr.commit();
		var billAddr;
		if (orderRec.viewSubrecord('billingaddress') != null)
			billAddr = orderRec.editSubrecord('billingaddress');
		else
			billAddr = orderRec.createSubrecord('billingaddress');
//		billAddr.setFieldValue('addressee', billingName);			//NS CSS Case 3013929
		if (billToName != null && billToName != '')
			billAddr.setFieldValue('addressee', billToName);		//NS CSS Case 3013929
		else
			billAddr.setFieldValue('addressee', firstName + ' ' + lastName);
		billAddr.commit();
		
		orderRec.setFieldText('paymentmethod', paymentType);
		if (orderRec.getFieldValue('paymentmethod') == '')
		{
			retData.status = 'Error';
			retData.message = 'Invalid Credit Card Number or payment method. ';
			nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
			return retData;			
		}
		if (ccNumber != '')
		{
			if (expiryMonth == '' || expiryMonth.length < 4) // DSG Case 62293
			{
				retData.status = 'Error';
				retData.message = 'Invalid Credit Card expiry month.';
				nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
				return retData;							
			}
			orderRec.setFieldValue('ccnumber', ccNumber);
			orderRec.setFieldValue('ccexpiredate', expiryMonth.substring(0,2) + '/' + expiryMonth.substring(2) );
			orderRec.setFieldValue('ccname', nameOnCard );
			if(billToZipCode != '')
				orderRec.setFieldValue('cczipcode', billToZipCode);
			//if(cvc != '')
			//	orderRec.setFieldValue('ccsecuritycode', cvc);
			
			orderRec.setFieldValue('ccapproved', 'T');
			if (authCode != '') // DSG Case 60453
			orderRec.setFieldValue('authcode', authCode); 
			if (pnRef != '') // DSG Case 60453
				orderRec.setFieldValue('pnrefnum', pnRef); 
		}

		nlapiLogExecution('Debug','Checking','discountitem : ' + JSON.stringify(discountitem));
		
		if (discountitem != null && discountitem != '')
		{
			var discountItemName = discountitem[0].itemId;
			var discountItemRate = discountitem[0].rate;

			nlapiLogExecution('Debug','Checking','discountItemName : ' + discountItemName + ' discountItemRate : ' + discountItemRate);

			orderRec.setFieldText('discountitem', discountItemName); 
			orderRec.setFieldValue('discountrate', discountItemRate); 
			
		}
		for (var intPos = 0; intPos < items.length; intPos++)
		{
			var item = items[intPos];
			var sku = convNull(item.itemId);
			if (sku == null || sku == '')
			{
				retData.status = 'Error';
				retData.message = 'Item Id is required.' ;
				nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
				return retData;			
			}	
			var itemId = '';
			if (itemId == null || itemId == '')
			{
				itemId = getItemId(sku);
				if (itemId == null || itemId == '')
				{
					retData.status = 'Error';
					retData.message = 'Invalid SKU : ' + sku;
					nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
					return retData;			
				}				
			}
			var quantity = convNull(item.quantity);
			var rate = convNull(item.rate);
			var amount = convNull(item.amount);
			orderRec.selectNewLineItem('item');
			orderRec.setCurrentLineItemValue('item', 'item', itemId);
			orderRec.setCurrentLineItemValue('item', 'quantity', quantity);
			orderRec.setCurrentLineItemValue('item', 'price', '-1');
			orderRec.setCurrentLineItemValue('item', 'rate', rate);
			orderRec.setCurrentLineItemValue('item', 'amount', amount);
			orderRec.commitLineItem('item');
		}
		if (shipMethod != '')
		{
			orderRec.setFieldValue('shipcarrier', 'nonups');
			orderRec.setFieldText('shipmethod', shipMethod);
			if (orderRec.getFieldValue('shipmethod') == '' )
			{
				orderRec.setFieldValue('shipcarrier', 'ups');
				orderRec.setFieldText('shipmethod', shipMethod);			
			}
			if (orderRec.getFieldValue('shipmethod') == '')
			{
				retData.status = 'Error';
				retData.message = 'Invalid shipment code : ' + shipMethod;
				nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
				return retData;				
			}			
		}
		if (shippingFee != '')
		{
			if (shipMethod == null || shipMethod == '')
			{
				orderRec.setFieldText('shipcarrier', 'More');
				orderRec.setFieldText('shipmethod', 'FedEx');
			}
			orderRec.setFieldValue('shippingcost', shippingFee);
		}
		if (couponCode != null && couponCode != '')
		{
			var couponCodeId = getCouponCodeId(couponCode);
			if (couponCodeId != null && couponCodeId != '')
			{
				orderRec.setFieldValue('couponcode', couponCodeId);
			}
		}		
		if (tax != null && tax != '' && parseFloat(tax) > 0)
		{
			if (country == 'US')
			{
				orderRec.setFieldValue('istaxable', 'T');
			}
		}
		orderRec.setFieldText('custbodyintegrationstatus', 'Ready');
		nlapiLogExecution('Debug','Checking','Before save : ' + new Date() + ',before save : ' + JSON.stringify(orderRec) );
		var soId = nlapiSubmitRecord(orderRec, true, true);
		//var orderData = nlapiLookupField('salesorder', soId, ['authcode', 'tranid']);
		orderRec = nlapiLoadRecord('salesorder', soId);
		
		if (ccNumber != '' && orderRec.getFieldValue('ccapproved') != 'T')
		//if (orderData.authCode != null && orderData.authCode != '' )
		{
			nlapiLogExecution('Debug', 'Checking', 'CC is not approved. deleting order ' + new Date());
			retData.status = 'Error';
			retData.message = 'Credit Card authorization failed.';
			if (orderRec.getLineItemCount('paymentevent') > 0 )
			{
				var response = convNull(orderRec.getLineItemValue('paymentevent', 'response',1));
				if (response.indexOf('CVV2 Mismatch') > 0 || response.indexOf('No CSC Match') > 0 || orderRec.getLineItemValue('paymentevent', 'csc',1) == 'N' )
					retData.message = 'Invalid credit card.';
			}
		
			nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
			return retData;
		}


		nsOrderNo = orderRec.getFieldValue('tranid');
		retData.status = 'Success';
		retData.message = '';
		retData.nsOrderId  = nsOrderNo;
		nlapiLogExecution('Debug','Checking','Return : ' + JSON.stringify(retData) + new Date());
		return retData;
	}
	catch (ex)
	{
		retData.status = 'Error';
		if (ex instanceof nlobjError)
			retData.message = ex.getDetails();
		else
			retData.message = ex.toString();
		if (ex.toString().indexOf('DUP_RCRD') > 0)
			retData.message = 'This order already exist';
        var msg = '';
        if (typeof ex.getStackTrace === 'function') {
            msg = ex.toString() + '<br />' + ex.getStackTrace().join('<br />');
        }
        else if (ex.stack) {
            msg = ex.toString() + ex.stack.replace(/(^|\s)at(\s|$)/g, '<br />at ');
        }
        else {
            msg = ex.toString();
        }
		nlapiLogExecution('Debug','Checking','Error : ' + msg );
		if (retData.message == null)
			retData.message = 'Unexpected Error, Contact Netsuite Administrator.';
		return retData;			
	}
	return retData;
}

// Added per DSG Case 60386
function getParentCustomerId()
{
    var currId = nlapiLookupField('customrecord_parent_cust', 1, 'name');
    var filters = new Array();
    filters.push(new nlobjSearchFilter('parent', '', 'anyOf', currId));
    var search = nlapiCreateSearch('customer');
    search.addFilters(filters);
    var resultSet = search.runSearch();
    var retList = null;
    var startPos = 0;
    var endPos = 1000;
    while (startPos <= 10000)
    {
        var currList = resultSet.getResults(startPos, endPos);
        if (currList == null || currList.length <= 0)
            break;
        if (retList == null)
        {
            retList = currList;
        }
        else
        {
            retList = retList.concat(currList);
        }
        if (currList.length < 1000)
        {
            break;
        }
        startPos += 1000;
        endPos += 1000;
    }
//	if (retList != null && retList.length > 5) // 9900
	if (retList != null && retList.length > 9900) // 9900
    {
        var newCustRec = nlapiCopyRecord('customer', currId);
        currId = nlapiSubmitRecord(newCustRec, true, true);
        nlapiSubmitField('customrecord_parent_cust', 1, 'name', currId);
    }
    return currId;
}

function getCustId(customerId)
{
	var filters = new Array();
	filters.push(new nlobjSearchFilter('externalidstring', '', 'is', customerId));
	filters.push(new nlobjSearchFilter('isinactive', '', 'is', 'F'));
	var list = nlapiSearchRecord('customer', '', filters);
	if (list != null && list.length > 0)
		return list[0].getId();
	
	return '';
}

function getCurrencyId(sfCurrency)
{
	var filters = new Array();
	filters.push(new nlobjSearchFilter('symbol', '', 'is', sfCurrency));
	var list = nlapiSearchRecord('currency', '', filters);
	if (list == null || list.length <= 0)
		return '';
	else {
		return list[0].getId();
	}
	
}

function getItemId(sku)
{
	var filters = new Array();
	filters.push(new nlobjSearchFilter('upccode', '', 'is', sku));
	filters.push(new nlobjSearchFilter('isinactive', '', 'is', 'F'));
	var list = nlapiSearchRecord('item', '', filters);
	if (list != null && list.length > 0)
		return list[0].getId();
	filters = new Array();
	filters.push(new nlobjSearchFilter('itemid', '', 'is', sku));
	filters.push(new nlobjSearchFilter('isinactive', '', 'is', 'F'));
	var list = nlapiSearchRecord('item', '', filters);
	if (list != null && list.length > 0)
		return list[0].getId();	
	return '';
}

function getAllRowsFromList(recType, searchId,filters,columns )
{
	var retList = null;
	var search;
	if (searchId == null || searchId == '') {
		search = nlapiCreateSearch(recType);
	}
	else
		search = nlapiLoadSearch(recType, searchId);
		
	if (filters != null && filters.length > 0)
		search.addFilters(filters);
	if  (columns != null && columns.length > 0)
		search.addColumns(columns);
	
	var resultSet = search.runSearch();
	var startPos = 0;
	var endPos = 1000;
	while (startPos <= 10000)
	{
		var currList = resultSet.getResults(startPos, endPos);
		if (currList == null || currList.length <= 0)
			break;
		if (retList == null)
		{
			retList = currList;
		}
		else
		{
			retList = retList.concat(currList);
		}
		if (currList.length < 1000)
		{
			break;
		}
		startPos += 1000;
		endPos += 1000;
	}
	return retList;
}

function escapeSpecialChars(str) {
	if (str == undefined || str == null )
		return '';
    return str.replace(/[\"]/g, '\\"')
      .replace(/[\n]/g, '\\n')
      .replace(/[\r]/g, '\\r')
      .replace(/[\t]/g, '\\t')

    ;

}

function isNotANumber(value)
{
	if(value == null || value == '' || isNaN(value) || parseFloat(value) == 'NaN')
	{
		return true;
	}
	return false;
}

function convNull(value)
{
	if(value == null || value == undefined)
		value = '';
	return value;
}
