function suitelet(request, response)
{
    var secretKey = 'ac8420dd-5069-4877-8d19-c017c12706b2';
    var hash = String(CryptoJS.SHA256(secretKey + (request.getParameter('custpage_userid') || "1")+(request.getParameter('custpage_customer') || "1")));

    var form = nlapiCreateForm('How to call Customer Support Suitelet?');

    form.addFieldGroup('custpage_instructions', 'Instructions');

    var message = '<html><body><br/><span style="font-size: 15px">Base URL: {externalurl}<br/>Parameters:<br/>-\t<b>internalid</b> : Customer Internal Id<br/>-\t<b>hash</b>: String <span style="color:red">"secretkey"</span>+<span style="color:blue">"internalid"</span> ' +
    '(Example: <span style="color:red">abcde<PERSON>ghijklmnop</span><span style="color:blue">123456</span>) encrypted on SHA256 method <br/><br/>Secret Key: <span style="color:green; font-weight: bold">“ac8420dd-5069-4877-8d19-c017c12706b2”</span><br/><br/>' + 
    'Dashboard URL: <a href="{externalurl1}&internalid={customerid}&contactid={contactid}&recordid={customerid}&hash={hash}&customform=1">{externalurl1}&internalid={customerid}&contactid={contactid}&recordid={customerid}&hash={hash}&customform=1</a><br/>' +
    '</span></body></html>';

    var baseurl1 = nlapiResolveURL('SUITELET', 'customscript_in8_supportform_sl', 'customdeploy_in8_supportform_sl', true);

    message = message.replace(/{externalurl1}/g, baseurl1);
    message = message.replace(/{customerid}/g, request.getParameter('custpage_customer'));
    message = message.replace(/{customerid}/g, request.getParameter('custpage_customer'));
    message = message.replace(/{contactid}/g, request.getParameter('custpage_userid'));
    message = message.replace(/{hash}/g, hash);
    message = message.replace(/{hash}/g, hash);

    form.addField('custpage_info', 'inlinehtml', '', null, 'custpage_instructions').setDefaultValue(message);


    if(request.getMethod() == "GET")
    {
        form.addFieldGroup('custpage_generate', 'Enter the fields below to generate a valid Sample URL');

        var custField = form.addField('custpage_customer', 'select', 'Select a Customer to Create a Sample URL:', 'customer', 'custpage_generate');
        custField.setMandatory(true);

        form.addField('custpage_userid', 'select', 'Contact', 'contact', 'custpage_generate').setMandatory(true);

        form.addSubmitButton('Hash');

    }

    response.writePage(form);

}