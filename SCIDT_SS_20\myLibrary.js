define([], function() {

    function sayHello() {
        
    }

    function tagaAdd(x, y) {
        return x+y;
    }

    return {
        hello: say<PERSON><PERSON>,
        add: tagaAdd
    };
});


require(['N/record'], function(record){
    var salesOrder = record.load({
        type: record.Type.SALES_ORDER, 
        id: 290801
    });
});

///////////////////////////////////////////

require(['N/record'], function(record) {

        var taskRec = record.create({
            type: record.Type.TASK
        });

        taskRec.setValue({
            fieldId: 'title',
            value: 'From 2.0 Script'
        });

        var x = taskRec.save();

});

///////////////////////////////////////////

require(['N/record'], function(record) {

    var taskRec = record.delete({
        type: record.Type.TASK,
        id: 1110
    });

});

///////////////////////////////////////////

require(['N/record'], function(record) {

    record.submitFields({
        type: record.Type.EMPLOYEE,
        id: 2,
        values: {
            comments: 'From Submit Field 2.0',

        }
    });

});


///////////////////////////////////////////

require(['N/record', 'N/runtime'], function(record, runtime) {

    var supervisorId = runtime.getCurrentUser().id;

    var empRecord = record.create({
        type: record.Type.EMPLOYEE
    });

    empRecord.setValue({
        fieldId: 'firstname',
        value: 'Act'
    });

    
    empRecord.setValue({
        fieldId: 'lastname',
        value: 'Guy'
    });

    empRecord.setValue({
        fieldId: 'supervisor',
        value: supervisorId
    });

    empRecord.save();

    record.submitFields({
        type: record.Type.EMPLOYEE,
        id: supervisorId,
        values: {
            comments: '12345',

        }
    });

});

///////////////////////////////////////////

require(['N/record'], function(record) {

    var empRec = record.load({
        type: record.Type.EMPLOYEE,
        id: 2,
        isDynamic: true
    });

    empRec.selectNewLine({ sublistId: 'addressbook' });
    var addressSubRec = empRec.getCurrentSublistSubrecord({
        sublistId: 'addressbook',
        fieldId: 'addressbookaddress'
    });

    addressSubRec.setValue({
        fieldId: 'country',
        value: 'PH'
    });

    addressSubRec.setValue({
        fieldId: 'city',
        value: 'Manila'
    });

    addressSubRec.setValue({
        fieldId: 'zipcode',
        value: '1410'
    });

    empRec.commitLine({ sublistId: 'addressbook' });
    empRec.save();

});

///////////////////////////////////////////
// create search

require(['N/search'], function(search) {

    var myFilters = [
        search.createFilter({ name: 'mainline',  operator: search.Operator.IS, values: true }),
        search.createFilter({ name: 'type', operator: search.Operator.ANYOF, values: 'SalesOrd' })
    ];

    var myColumns = [
        search.createColumn({ name: 'entity' }),
        search.createColumn({ name: 'amount' })
    ];

    var mySearch = search.create({
        type: search.Type.TRANSACTION,
        filters: myFilters,
        columns: myColumns    
    });

    mySearch.title = 'Search from SS2.0';
    mySearch.id = 'customsearch_search_demo_ss20'

    mySearch.save();

});

///////////////////////////////////////////
// create search with results

require(['N/search'], function(search) {

    var myFilters = [
        search.createFilter({ name: 'mainline',  operator: search.Operator.IS, values: true }),
        search.createFilter({ name: 'type', operator: search.Operator.ANYOF, values: 'SalesOrd' })
    ];

    var myColumns = [
        search.createColumn({ name: 'entity' }),
        search.createColumn({ name: 'amount' })
    ];

    var mySearch = search.create({
        type: search.Type.TRANSACTION,
        filters: myFilters,
        columns: myColumns    
    });

    mySearch.title = 'Search from SS2.0 v2';
    mySearch.id = 'customsearch_search_demo_ss20_2'

    mySearch.save();

    var myResultSet = mySearch.run();
    
    myResultSet.each(function(result){
        var id = result.id;
        var entity = result.getValue({ name: 'entity' });
        var amount = result.getValue({ name: 'amount' });

        console.log("ID: " + id + ", Entity: " + entity + ", Amount: " + amount);

        return true
    });

});

///////////////////////////////////////////
// load search with results

require(['N/search'], function(search) {

    var mySearch = search.load({
        id: 'customsearch_search_demo_ss20'
    });

    var resultSet = mySearch.run();

    resultSet.each(function(result){
        var id = result.id;
        var entity = result.getValue({ name: 'entity' });
        var amount = result.getValue({ name: 'amount' });

        console.log("ID: " + id + ", Entity: " + entity + ", Amount: " + amount);

        return true
    });

});

///////////////////////////////////////////
// load search with add filter and results

require(['N/search'], function(search) {

    var mySearch = search.load({
        id: 'customsearch_search_demo_ss20'
    });

    var myAddFilter = search.createFilter({
        name: 'datecreated',
        operator: search.Operator.WITHIN,
        values: 'thisyear'
    });

    mySearch.filters.push(myAddFilter);

    
    var myAddFilter = search.createColumn({
        name: 'datecreated'
    });

    mySearch.columns.push(myAddFilter);

    var resultSet = mySearch.run();

    resultSet.each(function(result){
        var id = result.id;
        var entity = result.getValue({ name: 'entity' });
        var amount = result.getValue({ name: 'amount' });
        var datecreated = result.getValue({ name: 'datecreated'});

        console.log("ID: " + id + ", Entity: " + entity + ", Amount: " + amount + ', Date Created: ' + datecreated);

        return true
    });

});

///////////////////////////////////////////
// 

require(['N/search'], function(search) {

    var myFilters = [
        search.createFilter({ name: 'mainline',  operator: search.Operator.IS, values: true }),
        search.createFilter({ name: 'type', operator: search.Operator.ANYOF, values: 'SalesOrd' })
    ];

    var myColumns = [
        search.createColumn({ name: 'entity' }),
        search.createColumn({ name: 'amount' })
    ];

    var mySearch = search.create({
        type: search.Type.TRANSACTION,
        filters: myFilters,
        columns: myColumns    
    });
    
    var pagedResult = mySearch.runPaged({ pageSize: 5 });
    
    console.log('Count of records: ' + pagedResult.count);
    console.log('Page Ranges: ' + pagedResult.pageRanges);
    console.log('Count per page: ' + pagedResult.pageSize);

    var myPage = pagedResult.fetch({ index: 0 });

    var result = myPage[0];
    console.log(result);
    // var entity = result.getValue({ name: 'entity' });
    // var amount = result.getValue({ name: 'amount' });

    // console.log("Entity: " + entity + ", Amount: " + amount + ', Date Created: ' + datecreated);

});


///////////////////////////////////////////
// 

require(['N/search'], function(search) {

    var myFilters = [
        search.createFilter({ name: 'mainline',  operator: search.Operator.IS, values: true }),
        search.createFilter({ name: 'type', operator: search.Operator.ANYOF, values: 'SalesOrd' })
    ];

    var myColumns = [
        search.createColumn({ name: 'entity' }),
        search.createColumn({ name: 'amount' })
    ];

    var mySearch = search.create({
        type: search.Type.TRANSACTION,
        filters: myFilters,
        columns: myColumns    
    });
    
    var pagedResult = mySearch.runPaged({ pageSize: 5 });

    for(var i = 0; i < pagedResult.pageRanges.length; i++){

        var pageData = pagedResult.fetch({ index: i });

        for(var x = 0; x < pageData.data.length; x++){

            var entity = pageData.data[x].getText({ name: 'entity' });
            var amount = pageData.data[x].getValue({ name: 'amount' });
            
            console.log("Entity: " + entity + ", Amount: " + amount);
        }
    }
    
    console.log('Count of records: ' + pagedResult.count);

});


///////////////////////////////////////////
// ACTIVITY 1

require(['N/search'], function(search) {

    var myColumns = [
        search.createColumn({ name: 'title' }),
        search.createColumn({ name: 'owner' }),
        search.createColumn({ name: 'lastrunby' })
    ];

    var mySearch = search.create({
        type: search.Type.SAVED_SEARCH,
        columns: myColumns    
    });

    var myResultSet = mySearch.run();
    
    myResultSet.each(function(result){
        var title = result.getValue({ name: 'title' });
        var owner = result.getValue({ name: 'owner' });
        var lastrunby = result.getText({ name: 'lastrunby' });

        console.log("Title: " + title + ", Owner: " + owner + ", Last Run By: " + lastrunby);

        return true
    });

});

///////////////////////////////////////////
// ACTIVITY 2

require(['N/search'], function(search) {

    var myFilters = [
        search.createFilter({ name: 'mainline',  operator: search.Operator.IS, values: true }),
        search.createFilter({ name: 'type', operator: search.Operator.ANYOF, values: 'SalesOrd' })
    ];

    var myColumns = [
        search.createColumn({ name: 'tranid', summary: search.Summary.COUNT }),
        search.createColumn({ name: 'entity', summary: search.Summary.GROUP })
    ];

    var mySearch = search.create({
        type: search.Type.TRANSACTION,
        filters: myFilters,
        columns: myColumns
    });

    var myResultSet = mySearch.run();
    
    console.log('====================================');
    myResultSet.each(function(result){
        var count = result.getValue({ name: 'tranid', summary: search.Summary.COUNT });
        var entity = result.getText({ name: 'entity', summary: search.Summary.GROUP });

        console.log("Customer: " + entity);
        console.log("No of SO: " + count);
        console.log('====================================');

        return true
    });

});


require(['N/search'], function(search) {

    var myColumns = [
        search.createColumn({ name: 'title' }),
        search.createColumn({ name: 'owner' }),
        search.createColumn({ name: 'lastrunby' })
    ];

    var mySearch = search.create({
        type: search.Type.SAVED_SEARCH,
        columns: myColumns    
    });

    var myResultSet = mySearch.run();
    
    myResultSet.each(function(result){
        var test = [result.getValue({ name: 'title' }), result.getValue({ name: 'owner' }), result.getValue({ name: 'lastrunby' })];

        console.table(test, ['Title', 'Owner', 'Last Run By']);

        return true
    });

});

require(['N/record'], function(record) {

    record.create({
        type: record.Type.PURCHASE_ORDER
    });

    record.setValue({
        fieldId: 'entity',
        value: '210'
    });

    record.setValue({
        fieldId: 'supervisor',
        value: supervisorId
    });

    empRecord.save();
});


///////////////////////////////////////////
// RESTlet caller - GET

require(['N/https', 'N/url'], function(https, url) {

    var myURL = url.resolveScript({
        scriptId: 'customscript_my_first_ss2_restlet',
        deploymentId: 'customdeploy_myfirst_restlet_2'
    });

    var myHeaders = {
        'Content-Type': 'application/json',
    };

    var result = https.get({
        url: myURL,
        headers: myHeaders
    });

    console.table(result);

    // var json = JSON.parse(result);
    // console.log(result.body.toJSON());    

    // for(var key in result.body) {
    //     if(result.body.hasOwnProperty(key)){
    //         console.log(result.body[key])
    //     }
    // }

});

///////////////////////////////////////////
// RESTlet caller - POST

require(['N/https', 'N/url'], function(https, url) {

    var myURL = url.resolveScript({
        scriptId: 'customscript_my_first_ss2_restlet',
        deploymentId: 'customdeploy_myfirst_restlet_2'
    });

    var myBody = {
        empid: 2,
        country: 'US',
        city: 'New York',
        state: 'NY',
        zipcode: '10001'
    };

    var myHeaders = {
        'Content-Type': 'application/json'
    };

    https.post({
        url: myURL,
        body: myBody,
        headers: myHeaders
    });

});


require(['N/record'], function(record) {

    // transform sales order
    var itemFfObj = record.transform({
        fromType: record.Type.SALES_ORDER,
        fromId: 1829,
        toType: record.Type.ITEM_FULFILLMENT
    });

    itemFfObj.setValue({
        fieldId: 'shipstatus',
        value: 'C'
    });

    console.log(itemFfObj.save());

});


require(['N/ui/message'], function(message) {

    message.create({
        type: message.Type.CONFIRMATION,
        title: 'CONFIRMED',
        message: 'UYYY CONFIRMED!',
        duration: 5000
    }).show();

});

require(['N/record'], function(record) {

    var recordLoadPromise = record.load.promise({
        type: record.Type.EMPLOYEE,
        id: 2
    });

    recordLoadPromise.then(function(empRec){
        var email = empRec.getValue({
            fieldId: 'email'
        });
        
        console.log('Email of Employee Loaded: ' + email);
    }).catch(function(e){
        console.log('Error! ' + e);
    }).finally(function(){
        console.log('well');
    });

});


require(['N/ui/serverWidget'], function(serverWidget) {

    

});



require(['N/record'], function(record){
    var invObj = record.load({
        type: record.Type.INVOICE, 
        id: 312676
    });

    // console.log(invObj.getValue({
    //     fieldId: 'taxrate'
    // }));
    var sublist = invObj.getSublistValue({
        sublistId: 'item',
        fieldId: 'taxcode',
        line: 0
    });

    console.log(sublist);
});

require(['N/record'], function(record){
    var x = record.load({
        type: record.Type.SALES_ORDER,
        id: 323451,
        isDynamic: true
    });
    x.setSublistValue({ sublistId: 'item', fieldId: 'isclosed', line: 0, value: false });
    var sublistObj = x.getSublist({ sublistId: 'item' });
    var lineCount = x.getLineCount({ sublistId: 'item' });
    for(var i = 0; i < lineCount; i++){
        var sublistItem = sublistObj({})
    }
    x.save();
});


require(['N/currentRecord'], function(currentRecord) {

    var record = currentRecord.get();
    var lineCount = record.getLineCount({ sublistId : 'item' });

    for(var i = 0; i < lineCount; i++){
        var isClosed = record.getSublistValue({
            sublistId: 'item',
            fieldId: 'isclosed',
            line: i
        });
        
        if(isClosed){
            record.getSublistField({
                sublistId: 'item',
                fieldId: 'item',
                line: i
            }).isDisabled = true;
        }
    }

});


require(['N/search', 'N/format'], function(search, format) {
    function getDateYear(){
        var currdate = new Date();
        var year = [];
        year[1] = '2015';
        year[2] = '2016';
        year[3] = '2017';
        year[4] = '2018';
        year[5] = '2019';
        year[6] = '2020';

        return (currdate.getMonth()+1) + '/' + currdate.getDate() + '/' + year[currdate.getDay()];
    }

    function getWeek(){

        var curr = new Date(getDateYear()); // get current date

        // get first date of the week
        var first = curr.getDate() - curr.getDay();
        if(first < 1){
            var lastMonth = new Date(curr.getFullYear(), curr.getMonth());
            lastMonth.setDate(first + lastMonth.getDate());
            first = lastMonth;
        } else {
            first = new Date(curr.getFullYear(), curr.getMonth(), first);
        }

        // get last day of the week
        var last = curr.getDate() + (7 - (curr.getDay() + 1));
        var lastDateOfMonth = new Date(curr.getFullYear(), curr.getMonth, 0).getDate(); // get last day of month
        if(last > lastDateOfMonth){
            last = new Date(curr.getFullYear(), curr.getMonth()+1, (last - lastDateOfMonth), 23, 59, 59);
        } else {
            last = new Date(curr.getFullYear(), curr.getMonth(), last, 23, 59, 59);
        }
        
        return [format.format({ value: first, type: format.Type.DATE }), format.format({ value: last, type: format.Type.DATE })];
        
    }

    var weekStartEnd = getWeek();
    var folderSearchObj = search.create({
    type: "folder",
    filters:
    [
        ["subsidiary","noneof","@NONE@"], 
        "AND", 
        ["formulatext: {parent}","isnotempty",""], 
        "AND", 
        ["lastmodifieddate","within",weekStartEnd[0],weekStartEnd[1]],
    ],
    columns:
    [
        search.createColumn({name: "name", label: "Name"}),
        search.createColumn({name: "foldersize", label: "Size (KB)"}),
        search.createColumn({
            name: "lastmodifieddate",
            sort: search.Sort.ASC,
            label: "Last Modified"
        }),
        search.createColumn({name: "parent", label: "Sub of"}),
        search.createColumn({name: "numfiles", label: "# of Files"}),
        search.createColumn({name: "internalid", label: "Internal ID"})
    ]
    });
    var searchResultCount = folderSearchObj.runPaged({ pageSize: 1000 }).count;
    console.log(searchResultCount);
//     folderSearchObj.run().each(function(result){
//         console.log(result.getValue({ name: 'name' }));
//         return true;
//     });
});