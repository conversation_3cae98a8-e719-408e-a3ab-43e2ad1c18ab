/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/email'], function(search, record, email) {

    function getInputData() {
        var mySearch = search.load({
            id: 29
        });

        return mySearch;
    }

    function map(context) {
        // accept data from previous stage
        var keyFromInput = context.key;
        var valueFromInput = context.value;
        
        var recordObj = JSON.parse(valueFromInput);
        var vendorId = recordObj.values.entity.value;
        context.write({
            key: vendorId,
            value: recordObj
        })

        // pass data to the next stage
    }

    function reduce(context) {
        // accept the data here

        var reduceKey = context.key;
        var reduceValues = context.values;


        // process the data here
        var vendPaymentRec = record.create({
            type: record.Type.VENDOR_PAYMENT,
            isDynamic: true
        });

        vendPaymentRec.setValue({
            fieldId: 'entity',
            value: reduceKey
        });

        var lineCount = vendPaymentRec.getLineCount({
            sublistId: 'apply'
        });

        for(var x = 0; x < lineCount; x++){
            var currentLineId = vendPaymentRec.getSublistValue({
                sublistId: 'apply',
                fieldId: 'internalid',
                line: x
            });

            for(var y = 0; y < reduceValues.length; y++){
                if(currentLineId == JSON.parse(reduceValues[y]).id){

                    vendPaymentRec.selectLine({
                        sublistId: 'apply',
                        line: x
                    });

                    vendPaymentRec.setCurrentSublistValue({
                        sublistId: 'apply',
                        fieldId: 'apply',
                        value: true
                    });

                    vendPaymentRec.commitLine({
                        sublistId: 'apply'
                    });

                    break;
                }
            }
        }

        var vendPaymentID = vendPaymentRec.save();
        var vendPaymentObj = record.load({
            type: record.Type.VENDOR_PAYMENT,
            id: vendPaymentID
        });

        var recipient = vendPaymentObj.getValue({ fieldId: 'entity' });
        var subject = 'Payment has been made!'
        var body = generateBody(vendPaymentObj);

        email.send({
            author: 2,
            recipients: 'recipient',
            subject: subject,
            body: body,
            relatedRecords: {
                entityId: recipient,
                transactionId: vendPaymentID
            }
        });
        
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});


function generateBody(vendPaymentObj){

    var totalPaid = vendPaymentObj.getValue({ fieldId: 'total' });
    var lineCount = vendPaymentObj.getLineCount({
        sublistId: 'apply'
    });

    var table = '<table border="1"><tr><td>#</td><td>Name</td><td>Amount</td></tr>';

    for(var x = 0; x < lineCount; x++){
        var bill = vendPaymentObj.getSublistValue({ sublistId: 'apply', fieldId: 'type', line: x });
        var amount = vendPaymentObj.getSublistValue({ sublistId: 'apply', fieldId: 'total', line: x });
        table += '<tr><td>' + (x+1).toString() + '</td><td>' + bill + '</td><td align="right">' + amount.toFixed(2) + '</td></tr>';
    }

    table += '<tr><td colspan="2"><b>Total Amount</b></td><td align="right">' + totalPaid.toFixed(2) + '</td></tr>';
    table += '</table>';

    var body = 'Good day!<br><br>';
    body += 'The following bills has been paid in full!';
    body += table;
    
    return body;

}

