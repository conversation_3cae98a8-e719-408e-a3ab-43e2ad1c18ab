/**
 * Module Description
 *    - Marks lines on insertion orders that have been invoiced from the "Invoice Insertion Orders" page
 * Version    Date            Author           Remarks
 * 1.00       30 Jan 2019     Brandon <PERSON>    TVG
 *
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
var modules = ['N/record', 'N/runtime', 'N/search', 'N/render', 'N/url', 'N/email', '../TVG 2.0 Utils/utils'];
define(modules, function(record, runtime, search, render, url, email, u) {
    var arInvoiceField = 'custcol_ar_invoice_tvg';
    function execute(context) {
        var script = runtime.getCurrentScript();
        var data = script.getParameter('custscript_data');
            data = JSON.parse(data); // Parse data JSON string to get object containing invoice & insertion order info
        // User Id that created the invoice submission, to be emailed upon successful creation.
        var userId = data.userId,
            linesToInclude = data.linesToInclude;
        // Building search filter for IO lines to be invoiced.
        var lineFilt = [];
        for (var line in linesToInclude) {
            lineFilt.push(['lineuniquekey', 'equalto', line], 'OR');
        }
        lineFilt.pop();
        lineFilt = [lineFilt];
        lineFilt.push("AND", [arInvoiceField, "anyof", "@NONE@"]);
        var ioSearch = search.create({
            type : 'transaction',
            filters : lineFilt,
            columns : [
                search.createColumn({name : 'internalid', join : 'vendor'}), search.createColumn('item'), search.createColumn({name : 'tranid', sort : search.Sort.ASC}),
                search.createColumn('amount'), search.createColumn('custbody_campaign_name_field_tvg'), search.createColumn('custbody_client'), search.createColumn('lineuniquekey'),
                search.createColumn({name : 'custcol_start_date', sort : search.Sort.ASC}), search.createColumn('custcol_end_date'), search.createColumn('linesequencenumber'),
                search.createColumn({name : 'internalid', join : 'customer'}), search.createColumn('type'), search.createColumn('custcol_on_sale_date'), search.createColumn('custcol_placement'),
                search.createColumn('custcol_note')
            ]
        });
        var invoicesObj = {};
        var ioSearchResults = u.getAllSearchResults(ioSearch);
        if (!u.isNullOrEmpty(ioSearchResults)) {
            log.audit('number', ioSearchResults.length);
            ioSearchResults.forEach(function(result) {
                var client = result.getValue('custbody_client');
                    client = !u.isNullOrEmpty(client) ? client : result.getValue({name : 'internalid', join : 'customer'});
                var io = result.id;
                var ioLine = result.getValue('linesequencenumber');
                var uniqueKey = result.getValue('lineuniquekey');
                var type;
                switch(result.getValue('type')) {
                    case 'PurchOrd' : type = 'purchaseorder'; break;
                    case 'VendBill' : type = 'vendorbill'; break;
                    case 'VendCred' : type = 'vendorcredit'; break;
                    case 'CardRfnd' : type = 'creditcardrefund'; break;
                    case 'CardChrg' : type = 'creditcardcharge'; break;
                    case 'Check' : type = 'check';
                }
                // Building object to associate invoices and IO lines under the same client
                if (!(client in invoicesObj)) {
                    invoicesObj[client] = {};
                    invoicesObj[client].invoice = record.create({
                        type : 'invoice',
                        isDynamic : true,
                        defaultValues : {
                            entity : result.getValue('custbody_client'),
                            customform : 100
                        }
                    });
                    invoicesObj[client].invoice.setValue({fieldId : 'department', value : 2}); // 2 = Media Planning Department
                    invoicesObj[client][io] = {};
                    invoicesObj[client][io].lines = [ioLine];
                    invoicesObj[client][io].type = type;
                    setNewInvoiceLine(invoicesObj[client].invoice, result, linesToInclude[uniqueKey]);
                } else if (!(io in invoicesObj[client])) {
                    invoicesObj[client][io] = {};
                    invoicesObj[client][io].lines = [ioLine];
                    invoicesObj[client][io].type = type;
                    setNewInvoiceLine(invoicesObj[client].invoice, result, linesToInclude[uniqueKey]);
                } else {
                    invoicesObj[client][io].lines.push(ioLine);
                    setNewInvoiceLine(invoicesObj[client].invoice, result, linesToInclude[uniqueKey]);
                }
            });
        }

        var invoiceIds = [];
        var clientIds = [];
        var errors = ''; // Storing any invoicing or IO update errors to be emailed to user.
        for (var client in invoicesObj) {
            try {
                var invoiceId = invoicesObj[client].invoice.save();
                invoiceIds.push(invoiceId);
                clientIds.push(client);
                for (var io in invoicesObj[client]) {
                    // Skipping the first index within the client object because it is the actual invoice record (invoicesObj[client].invoice), not an IO to be marked.
                    if (Object.keys(invoicesObj[client]).indexOf(io) == 0) {
                        continue;
                    }
                    var ioRec = record.load({
                        type : invoicesObj[client][io].type,
                        id : io,
                        isDynamic : true
                    });
                    // Marking each IO line with the invoice that was just created for this client.
                    invoicesObj[client][io].lines.forEach(function(line) {
                        ioRec.selectLine('item', parseInt(line-1));
                        ioRec.setCurrentSublistValue('item', arInvoiceField, invoiceId);
                        ioRec.commitLine('item');
                    });
                    ioRec.save(true, true);
                    u.pauseScriptIfNecessary();
                }
            } catch(e) {
                log.error('Error saving IO or Invoice: ', e.toString());
                errors += '\nError creating invoice for ' + client + ': ' + e.toString();
            }
        }
        // Searching invoices that were just saved to retrieve Invoice # and client name text for emailing user notification.
        if (!u.isNullOrEmpty(invoiceIds)) {
            var emailSearch = search.create({
                type : 'invoice',
                filters : [
                    ['internalid', 'anyof', invoiceIds], 'AND', ['mainline', 'is', 'T']
                ],
                columns : [
                    search.createColumn('transactionname'), search.createColumn('entity')
                ]
            });
            var emailResults = u.getAllSearchResults(emailSearch);
        } else {
            var emailSearch = null;
            var emailResults = [];
        }
        var emailSubject = [];
        var emailBody = '';
        var attachments = [];
        if (!!emailResults && emailResults.length > 0) {
            emailResults.forEach(function (result) {
                // Creating clickable link to Invoice record embedded in email body.
                var invoiceURL = url.resolveRecord({
                    recordType : 'invoice',
                    recordId : result.id,
                    isEditMode : false
                });
                emailSubject.push(result.getText('entity'));
                emailBody += '<a href=' + invoiceURL + '>' + result.getValue('transactionname') + '</a> created successfully!\n';
                // Rendering PDF attachment for user email.
                attachments.push(render.transaction({entityId : parseInt(result.id)}));
            });
            emailSubject = 'Invoices successfully created for ' + emailSubject.toString();
        } else {
            emailSubject = 'Failed to create invoices requested on ' + new Date();
            emailBody = 'ERRORS: ';
        }
        email.send({
            author : userId,
            recipients : userId,
            bcc : ['<EMAIL>'],
            subject : emailSubject,
            body : emailBody + errors,
            attachments : attachments
        });
    }
    // Sets the next line on an invoice record based on the search result.
    function setNewInvoiceLine(invoice, result, description) {
        var type = result.getValue('type');
        var tranId = result.id;
        var tranLine = result.getValue('linesequencenumber');
        if (type != 'PurchOrd') {
            var billLineCount = invoice.getLineCount('itemcost');
            log.debug('billLineCount', billLineCount);
            for (var i = 0; i < billLineCount; i++) {
                invoice.selectLine('itemcost', i);
                var billTran = invoice.getCurrentSublistValue('itemcost', 'doc');
                var billLine = invoice.getCurrentSublistValue('itemcost', 'line');
                if (billTran == tranId && billLine == tranLine) {
                    invoice.setCurrentSublistValue('itemcost', 'apply', true);
                    log.debug('set true', i);
                    invoice.commitLine('itemcost');
                }
            }
        } else {
            var endDate = !!result.getValue('custcol_end_date') ? new Date(result.getValue('custcol_end_date')) : '';
            var startDate = !!result.getValue('custcol_start_date') ? new Date(result.getValue('custcol_start_date')) : '';
            var onSaleDate = !!result.getValue('custcol_on_sale_date') ? new Date(result.getValue('custcol_on_sale_date')) : '';
            var amount = result.getValue('amount') * -1;
                //amount = (result.getValue('type') == 'PurchOrd') ? (amount * -1) : amount;
            log.audit('amount', amount);
            invoice.selectNewLine('item');
            invoice.setCurrentSublistValue('item', 'item', result.getValue('item'));
            invoice.setCurrentSublistValue('item', 'description', description);
            invoice.setCurrentSublistValue('item', 'custcol_placement', result.getValue('custcol_placement'));
            invoice.setCurrentSublistValue('item', 'custcol_note', result.getValue('custcol_note'));
            invoice.setCurrentSublistValue('item', 'quantity', 1);
            invoice.setCurrentSublistValue('item', 'amount', amount);
            invoice.setCurrentSublistValue('item', 'rate', amount);
            invoice.setCurrentSublistValue('item', 'custcol_campaign_name', result.getValue('custbody_campaign_name_field_tvg'));
            if (!!startDate) invoice.setCurrentSublistValue('item', 'custcol_start_date', startDate);
            if (!!endDate) invoice.setCurrentSublistValue('item', 'custcol_end_date', endDate);
            if (!!onSaleDate) invoice.setCurrentSublistValue('item', 'custcol_start_date', onSaleDate);
            invoice.setCurrentSublistValue('item', 'custcol_insertion_order_line', tranLine);
            invoice.setCurrentSublistValue('item', 'custcol_insertion_order_ref', tranId);
            invoice.setCurrentSublistValue('item', 'custcol_vendor_ref', result.getValue({name : 'internalid', join : 'vendor'}));
            invoice.commitLine('item');
        }
    }

    return {
        execute : execute
    };
});
