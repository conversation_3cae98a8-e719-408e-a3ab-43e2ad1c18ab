/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 *
 * This script is to handle the logic of the discount items
 * Line 1 = Non-discount item
 * Line 2 = Discount item (display the Discount Rate = discount line amount / line amount above * -100)
 * Line 3 = Non-discount item
 * Line 4 = Discount item (display the Discount Rate = discount line amount / line amount above * -100)
 * 
 * Can't create this type of logic in the PDF template itself. Freemarker variable types (i.e. containers) are usually immutable and a hassle to put logic in
 * reference: https://freemarker.apache.org/docs/pgui_datamodel_parent.html
 * 
 */
 define(['N/ui/serverWidget', 'N/format/i18n', 'N/format', 'N/record'], function(serverWidget, format, formatDate, record) {

    var CURRENCY;

    function formattoCurrency(value) {
        var curFormatter = format.getCurrencyFormatter({currency: CURRENCY});
        return curFormatter.format({ number: value });
    }

    function beforeLoad(context) {
        if(context.type == context.UserEventType.PRINT){
            try {
                var recordForm = context.form;
                var recordObj = context.newRecord;
                var rec = record.load({
                    type: recordObj.type,
                    id: recordObj.id,
                    isDynamic: true
                });
                CURRENCY = rec.getText({ fieldId: 'currency' });

                var customField = recordForm.addField({
                    id : 'custpage_formatted_values',
                    type : serverWidget.FieldType.LONGTEXT,
                    label : 'formatted item values'
                });
                
                // variable to be sent over to printout
                var unformattedItems = [];

                for(var i = 0; i < recordObj.getLineCount({ sublistId: 'item' }); i++){
                    var itemAmount = parseFloat(recordObj.getSublistValue({ sublistId: 'item', fieldId: 'amount', line: i}));
                    var itemDetails = {};
                    if(itemAmount < 0) {

                        var discountAmount = recordObj.getSublistValue({ sublistId: 'item', fieldId: 'amount', line: i});
                        var prevItemAmount = recordObj.getSublistValue({ sublistId: 'item', fieldId: 'amount', line: (i-1)});
                        
                        unformattedItems[i-1].discount = ((parseFloat(discountAmount) / parseFloat(prevItemAmount)) * -100);
                        unformattedItems[i-1].amount = formattoCurrency((parseFloat(prevItemAmount) + parseFloat(discountAmount)));

                        itemDetails = "";
                        
                    } else {

                        var revRecStart = recordObj.getSublistValue({ sublistId: 'item', fieldId: 'custcol_suitesync_rev_rec_start', line: i});
                        if(revRecStart != ""){
                            revRecStart = formatDate.format({
                                value: new Date(revRecStart),
                                type: formatDate.Type.DATE
                            });
                        }
                        var revRecEnd = recordObj.getSublistValue({ sublistId: 'item', fieldId: 'custcol_suitesync_rev_rec_end', line: i});
                        if(revRecEnd != ""){
                            revRecEnd = formatDate.format({
                                value: new Date(revRecEnd),
                                type: formatDate.Type.DATE
                            });
                        }
                        
                        itemDetails = {
                            quantity: recordObj.getSublistValue({ sublistId: 'item', fieldId: 'quantity', line: i}),
                            item: recordObj.getSublistText({ sublistId: 'item', fieldId: 'item', line: i}),
                            description: recordObj.getSublistText({ sublistId: 'item', fieldId: 'description', line: i}),
                            discount: 0,
                            rate: 0,
                            amount: formattoCurrency(recordObj.getSublistValue({ sublistId: 'item', fieldId: 'amount', line: i})),
                            custcol_suitesync_rev_rec_start: revRecStart,
                            custcol_suitesync_rev_rec_end: revRecEnd
                        }
                        
                    }

                    unformattedItems.push(itemDetails);
                }

                var formattedItems = [];
                for(var x = 0; x < unformattedItems.length; x++){
                    if(unformattedItems[x] !== "") {
                        formattedItems.push(unformattedItems[x]);
                    }
                }

                customField.defaultValue = JSON.stringify(formattedItems)
                customField.updateDisplayType({
                displayType : serverWidget.FieldDisplayType.HIDDEN
                });
            } catch (e) {
                log.debug("Error", e);
            }
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});