<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="3">
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    style="float: left; margin: 7px" /> </#if> <span
                                class="nameandaddress">${companyInformation.companyName}</span><br /><span
                                class="nameandaddress">${companyInformation.addressText}</span>
                        </td>
                        <td align="right"><span class="title">${record@title}</span></td>
                    </tr>
                    <tr>
                        <td align="right"><span class="number">#${record.tranid}</span></td>
                    </tr>
                    <tr>
                        <td align="right">${record.trandate}</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td>
                            <barcode codetype="code128" showtext="true" value="${record.tranid}" />
                        </td>
                        <td align="right">
                            <pagenumber /> of<br />
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="10%" footer="nlfooter" footer-height="10pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th>${record.entity@label}</th>
                <th>${record.assemblyitem@label}</th>
                <th>${record.quantity@label}</th>
                <th>${record.revision@label}</th>
                <th>${record.units@label}</th>
            </tr>
            <tr>
                <td>${record.entity}</td>
                <td>${record.assemblyitem}</td>
                <td>${record.quantity}</td>
                <td>${record.revision}</td>
                <td>${record.units}</td>
            </tr>
        </table>
        <#if record.item?has_content>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th align="center" colspan="3">${item.quantity@label}</th>
                                <th colspan="8">${item.item@label}</th>
                                <th align="right" colspan="4">${item.options@label}</th>
                                <th align="right" colspan="8">${item.inventorydetail@label}</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td align="center" colspan="3" line-height="150%">${item.quantity}</td>
                        <td colspan="8"><span class="itemname">${item.item}</span><br />${item.description}</td>
                        <td align="right" colspan="4">${item.options}</td>
                        <td align="right" colspan="8">${item.inventorydetail}</td>
                    </tr>
                </#list><!-- end items -->
            </table>

            <hr />
        </#if>
        <#if record.assemblydetail?has_content>
            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.assemblydetail as assembly>
                    <#if assembly_index==0>
                        <thead>
                            <tr>
                                <th align="center" colspan="2">${assembly.quantity@label}</th>
                                <th colspan="12">${assembly.item@label}</th>
                                <th align="right" colspan="3">${assembly.issubassembly@label}</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td align="center" colspan="2">${assembly.quantity}</td>
                        <td colspan="12"><span class="itemname">${assembly.item}</span><br />${assembly.description}
                        </td>
                        <td align="right" colspan="3">${assembly.issubassembly}</td>
                    </tr>
                </#list><!-- end items -->
            </table>
            &nbsp;

            <table border="1" cellpadding="1" cellspacing="1" style="width:500px;">
                <tr>
                    <td>Cost: ${record.item.cost}</td>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
            </table>
        </#if>
    </body>
</pdf>