/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
 define(['N/https', 'N/ui/serverWidget', 'N/record', 'N/redirect', 'N/workflow'], function(https, serverWidget, record, redirect, workflow) {

    function onRequest(context) {
        log.debug({
            title: 'Suitelet Start',
            details: 'Suitelet Executed'
        });

        if(context.request.method == https.Method.GET){
            // logic for GET
            
            var recId = context.request.parameters.recid;

            log.debug('test', recId);
        
            var getForm = serverWidget.createForm({
                title: 'Reject Reason'
            });

            // Reject reason
            var rejectReasonField = getForm.addField({
                id: 'custpage_reject_reason',
                label: 'Reject Reason',
                type: serverWidget.FieldType.LONGTEXT
            });
            rejectReasonField.isMandatory = true;

            // originating record
            var originatingField = getForm.addField({
                id: 'custpage_originating',
                label: 'Originating ID',
                type: serverWidget.FieldType.TEXT
            });
            originatingField.defaultValue = recId;
            originatingField.updateDisplayType({
                displayType : serverWidget.FieldDisplayType.HIDDEN
            });

            // submit button
            getForm.addSubmitButton({
                label: 'Submit'
            });

            context.response.writePage({ pageObject: getForm });

        } else {
        // logic for POST
            var requestParams = context.request.parameters;
            var recId = requestParams.custpage_originating;
            var reject_reason = requestParams.custpage_reject_reason;

            try {

                var recObj = record.load({
                    type: record.Type.VENDOR_BILL,
                    id: recId
                });

                recObj.setValue({ fieldId: 'custbody_acs_reject_reason', value: reject_reason });
            
                var recId = recObj.save();

                workflow.trigger({
                    recordType: record.Type.VENDOR_BILL,
                    recordId: recId,
                    workflowId: 'customworkflow_acs_vnd_bill_approval',
                    actionId: 'workflowaction283'
                });

                redirect.toRecord({
                    type: record.Type.VENDOR_BILL,
                    id: recId,
                })

            } catch (e) {
                log.debug("Error", e);
                context.response.write({
                    output: 'An error has occured. Please contact your administrator for more information!'
                });
            }
        }
    }

    return {
        onRequest: onRequest
    }
});
