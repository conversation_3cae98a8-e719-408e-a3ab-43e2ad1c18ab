/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/ui/serverWidget'], function(serverWidget) {

    function beforeLoad(context) {
        var form = context.form;
        var fieldObj = form.addField({
            id : 'custpage_test_field',
            type : serverWidget.FieldType.LONGTEXT,
            label : 'item receipt id'
        });
        var item_receipt_id = context.request.parameters.item_receipt_id;
        
                    
        fieldObj.defaultValue = item_receipt_id;
    }

    return {
        beforeLoad: beforeLoad
    }
});
