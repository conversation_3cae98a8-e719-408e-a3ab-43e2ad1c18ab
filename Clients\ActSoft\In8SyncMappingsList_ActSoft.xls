<?xml version="1.0" encoding="utf-8"?>
 <Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
<Author>NetSuite</Author>
<LastAuthor>NetSuite</LastAuthor>
<Company>NetSuite</Company>
</DocumentProperties>
<Styles>
<Style ss:ID="company">
<Alignment ss:Horizontal="Center"/>
<Font ss:Bold="1"/>
</Style> <Style ss:ID="error">
<Alignment ss:Horizontal="Center"/>
<Interior ss:Color="#d04040" ss:Pattern="Solid"/>
<Font ss:Bold="1"/>
</Style> <Style ss:ID="header">
<Alignment ss:Horizontal="Center"/>
<Font ss:Size="7" ss:Bold="1"/>
<Interior ss:Color="#d0d0d0" ss:Pattern="Solid"/>
</Style> <Style ss:ID="Default" ss:Name="Normal">
<Alignment ss:Vertical="Bottom"/>
<Borders/>
<Font ss:FontName="Arial" ss:Size="8"/>
<Interior/>
<NumberFormat/>
<Protection/>
</Style>
<Style ss:ID="s__TIMEOFDAY"><NumberFormat ss:Format="Medium Time"/></Style>
<Style ss:ID="s__DATETIME"><NumberFormat ss:Format="General Date"/></Style>
<Style ss:ID="s__DATETIMETZ"><NumberFormat ss:Format="General Date"/></Style>
<Style ss:ID="s__DATE"><NumberFormat ss:Format="Short Date"/>
</Style><Style ss:ID="s__text"></Style><Style ss:ID="s__currency"><NumberFormat ss:Format="Currency"/></Style>
<Style ss:ID="s__percent"><NumberFormat ss:Format="Percent"/></Style>
<Style ss:ID="s1_b_text"><Alignment ss:Indent="1"/><Font ss:FontName="Verdana" x:Family="Swiss" ss:Size="8" ss:Color="#000000" ss:Bold="1"/></Style>
<Style ss:ID="s_b_text"><Font ss:FontName="Verdana" x:Family="Swiss" ss:Size="8" ss:Color="#000000" ss:Bold="1"/></Style>
<Style ss:ID="s2__text"><Alignment ss:Indent="2"/></Style>
<Style ss:ID="s_b_currency"><Font ss:FontName="Verdana" x:Family="Swiss" ss:Size="8" ss:Color="#000000" ss:Bold="1"/><NumberFormat ss:Format="Currency"/></Style>
<Style ss:ID="s_currency_nosymbol"><Font ss:FontName="Verdana" x:Family="Swiss" ss:Size="8" ss:Color="#000000" /><NumberFormat ss:Format="#,##0.00_);[Red]\(#,##0.00\)"/></Style>
<Style ss:ID="s1__text"><Alignment ss:Indent="1"/></Style>
<Style ss:ID="s_b_currency_X"><Font ss:FontName="Verdana" x:Family="Swiss" ss:Size="8" ss:Color="#000000" ss:Bold="1"/><Interior ss:Color="#f0e0e0" ss:Pattern="Solid"/><NumberFormat ss:Format="Currency"/></Style>
<Style ss:ID="s__currency_en_CA"><Alignment ss:Vertical="Center" ss:Horizontal="Right"/><NumberFormat ss:Format="&quot;Can$&quot;#,##0.00_);(&quot;Can$&quot;#,##0.00)"/></Style>
<Style ss:ID="s__currency_en_US"><Alignment ss:Vertical="Center" ss:Horizontal="Right"/><NumberFormat ss:Format="&quot;$&quot;#,##0.00_);(&quot;$&quot;#,##0.00)"/></Style>
<Style ss:ID="s__currency_fr_FR_EURO"><Alignment ss:Vertical="Center" ss:Horizontal="Right"/><NumberFormat ss:Format="&quot;€&quot;#,##0.00_);(&quot;€&quot;#,##0.00)"/></Style>
<Style ss:ID="s__currency_en_GB"><Alignment ss:Vertical="Center" ss:Horizontal="Right"/><NumberFormat ss:Format="&quot;£&quot;#,##0.00_);(&quot;£&quot;#,##0.00)"/></Style>
</Styles>
<Worksheet ss:Name="InSyncMappingsList">
<Table><Row>
<Cell ss:StyleID="header"><Data ss:Type="String">Internal ID</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">In8 Sync</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">NetSuite Field</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Source Filter (Internal Id)</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Source List (List Name)</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Fixed Value</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Value To Compare</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Value If True</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Value If False</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">WC Field Name</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Format</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Is Custom Field</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Is Image</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Is Product Bundle Field</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Is Source Field Parent</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Is Variation Field</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Get Field Value</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Is Custom Taxonomy</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Custom Taxonomy Record</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Custom Taxonomy Term Field</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Send Translation</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Translation Field</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Exclude Field</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Order Item Meta</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">66</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">recmachcustrecord_rr_c_customerlnk</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contracts</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">67</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contractlines</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contract_billing</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">creditcards</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">creditcards</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">internalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">customerinternalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">69</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">partner</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">sales_rep</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">55</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custbody_rr_contractpaymentmethod</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contractpaymentmethod</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">56</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custbody_rr_contract_billing_day</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contract_billing_day</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">57</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custbody_rr_contractweeklybillingday</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contractweeklybillingday</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">62</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">tranid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">salesordernumber</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">54</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custbody_rr_contractendoftermaction</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contractendoftermaction</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">53</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custbody_rr_contracttermmonths</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contracttermmonths</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">63</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">internalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">internalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">51</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">invoices</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">invoices</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">73</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">trandate</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">orderdate</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">yyyymmdd</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">52</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custbody_rr_contractbillingfreq</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contractbillingfreq</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">itemid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">sku</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">4</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">storedetaileddescription</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">description</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">5</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">storedisplayname</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">title</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">6</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">inventorylocation</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">stock_quantity</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">7</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">regular_price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">8</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">internalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">internalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">9</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">storedescription</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">short_description</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">storedisplayimage</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">images</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">11</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">searchkeywords</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">tags</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">18</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">9</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_ATT</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">17</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_in8_pricelevels</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">available_pricelevels</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">19</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_Sprint</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">20</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">11</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_T-Mobile</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">21</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_in8_discounts</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">old_discount_labels</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">22</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_in8_discounts</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">old_discount_ids</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">23</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_in8_discounts</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">old_discount_rate</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">64</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">istaxable</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">T</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Standard</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">none</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">tax_class</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">65</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">istaxable</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">T</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">taxable</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">none</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">tax_status</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">58</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_rr_contractitemtype</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contractitemtype</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">59</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_rr_contractendoftermaction</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contractendoftermaction</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">60</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_rr_contractbillingfreq</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contractbillingfreq</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">61</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_rr_claadjustmentitem</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">claadjustmentitem</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">16</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">0</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">test</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">42</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">in8discounts</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">discounts</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">44</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">4</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_Alternate-Price-3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">43</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">2</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_Alternate-Price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">45</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_Bundled-Price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">46</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">14</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_MSRP</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">47</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">7</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_Reseller</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">48</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">13</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_US-Cellular</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">49</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">8</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_VAR</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">50</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">12</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_Verizon</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">12</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">7</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">internalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">contactinternalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">13</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">8</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">linkedtrackingnumbers</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">tracking_number</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">14</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">8</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">trandate</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">date_shipped</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">timestamp</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">15</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">8</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">ups</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">tracking_provider</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">28</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">regular_price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">40</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_in8_discounts</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">discount_rate</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">39</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_in8_discounts</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">discount_ids</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">38</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_in8_discounts</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">discount_labels</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">24</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">itemid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">sku</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">25</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">storedetaileddescription</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">description</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">26</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">storedisplayname</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">title</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">27</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">inventorylocation</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">stock_quantity</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">41</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">type</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">type</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">29</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">internalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">internalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">30</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">storedescription</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">short_description</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">31</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">storedisplayimage</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">images</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">32</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">searchkeywords</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">tags</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">33</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">0</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">test</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">34</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">custitem_in8_pricelevels</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">available_pricelevels</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">35</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">9</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_ATT</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">36</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_Sprint</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">37</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">10</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">11</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">price_T-Mobile</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">68</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">11</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">internalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">partnerinternalid</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">72</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">11</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">admin</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">access_control</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">71</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">11</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">T</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">giveaccess</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">70</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">11</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">rep</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">role_control</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
</Row>
</Table>
</Worksheet>
</Workbook>