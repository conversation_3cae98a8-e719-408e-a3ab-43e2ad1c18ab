/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord', 'N/url', 'N/https', 'N/record'], function(currentRecord, url, https) {

    var recTypes = {
        "Check": "check",
        "CustRfnd": "customerrefund",
        "VendPymt": "vendorpayment",
    }

    function pageInit(context) {

    }

    function fieldChanged(context) {
        var fieldId = context.fieldId;
        var sublistId = context.sublistId;

        if(sublistId == 'custpage_payment_sublist' && fieldId == 'print') {
            var recObj = context.currentRecord;
            var checkNum = Number(recObj.getValue({ fieldId: 'custpage_first_check_no' }));
            var toAdd = 0;
            var batchCount = 0;
            var batchTotal = 0.00;
            var lineCount = recObj.getLineCount({ sublistId: sublistId });
            for(var i = 0; i < lineCount; i++) {
                var toPrint = recObj.getSublistValue({ sublistId: sublistId, fieldId: fieldId, line: i });
                if(toPrint == 'T' || toPrint){
                    toAdd += 1;
                    batchCount++;
                    batchTotal += recObj.getSublistValue({ sublistId: sublistId, fieldId: 'amount', line: i });
                }
            }

            if(batchCount > 0) {
                checkNum = checkNum + (toAdd - 1);    
                recObj.setValue({ fieldId: 'custpage_check_no', value: checkNum });
                recObj.setValue({ fieldId: 'custpage_batch_count', value: batchCount });
                recObj.setValue({ fieldId: 'custpage_batch_amount', value: batchTotal });
            } else {
                checkNum = '';
                recObj.setValue({ fieldId: 'custpage_check_no', value: '' });
                recObj.setValue({ fieldId: 'custpage_batch_count', value: '' });
                recObj.setValue({ fieldId: 'custpage_batch_amount', value: '' });
            }


        } else if (fieldId == 'custpage_first_check_no') {
            
            var recObj = context.currentRecord;
            var fcheckNum = recObj.getValue({ fieldId: 'custpage_first_check_no' });

            // remove non numeric characters
            var numberPattern = /\d+/g;
            var firstCheckNum = fcheckNum.match( numberPattern ).join('');

            recObj.setValue({ fieldId: 'custpage_first_check_no', value: firstCheckNum, ignoreFieldChange: true });

            var checkNum = recObj.getValue({ fieldId: 'custpage_check_no' });

            if(checkNum) {
                if(firstCheckNum != checkNum) {

                    var toAdd = 0;
                    var lineCount = recObj.getLineCount({ sublistId: 'custpage_payment_sublist' });
                    for(var i = 0; i < lineCount; i++) {
                        var toPrint = recObj.getSublistValue({ sublistId: 'custpage_payment_sublist', fieldId: 'print', line: i });
                        if(toPrint == 'T' || toPrint){
                            toAdd += 1;
                        }
                    }

                    checkNum = (Number(firstCheckNum) + (toAdd-1));

                    recObj.setValue({ fieldId: 'custpage_check_no', value: checkNum });

                }
            }
        }
    }
    
    function markAll(markunmark) {
        var recObj = currentRecord.get();
        var lineCount = recObj.getLineCount({ sublistId: 'custpage_payment_sublist' });
        for(var i = 0; i < lineCount; i++) {
            recObj.selectLine({
                sublistId: 'custpage_payment_sublist',
                line: i
            });
            if(markunmark) {
                recObj.setCurrentSublistValue({
                    sublistId: 'custpage_payment_sublist',
                    fieldId: 'print',
                    value: true
                });
            } else {
                recObj.setCurrentSublistValue({
                    sublistId: 'custpage_payment_sublist',
                    fieldId: 'print',
                    value: false
                });
            }
        }
    }

    function print() {
        var recObj = currentRecord.get();
        
        var lineCount = recObj.getLineCount({ sublistId: 'custpage_payment_sublist' });
        var paymentIds = '';
        var trantypes = '';
        for(var i = 0; i < lineCount; i++) {
            var toPrint = recObj.getSublistValue({ sublistId: 'custpage_payment_sublist', fieldId: 'print', line: i });
            if(toPrint == 'T' || toPrint){
                var id = recObj.getSublistValue({ sublistId: 'custpage_payment_sublist', fieldId: 'payment_id', line: i });
                var trantype = recObj.getSublistValue({ sublistId: 'custpage_payment_sublist', fieldId: 'tran_type', line: i });
                paymentIds += id + ',';
                trantypes += recTypes[trantype] + ',';
            }
        }

        console.log(trantypes);

        paymentIds = paymentIds.replace(/,\s*$/, "");
        trantypes = trantypes.replace(/,\s*$/, "");

        var suiteletURL = url.resolveScript({
            scriptId: 'customscript_acs_sl_uncheck_to_print',
            deploymentId: 'customdeploy_acs_sl_uncheck_to_print',
            params: {
                payment_ids: paymentIds,
                tran_types: trantypes,
                account_id: recObj.getValue({ fieldId: 'custpage_accounts' }),
                check_no: recObj.getValue({ fieldId: 'custpage_first_check_no' })
            },
            returnExternalUrl: true
        });

        https.get.promise({
            url: suiteletURL
        });

        var outputUrl = url.resolveScript({
            scriptId: 'customscript_acs_sl_render_check_pdf',
            deploymentId: 'customdeploy_acs_sl_render_check_pdf',
            params: {
                payments: paymentIds
            }
        });
        window.open(outputUrl);
    }

    return {
        pageInit: pageInit,
        fieldChanged: fieldChanged,
        markAll: markAll,
        print: print
    }
});
