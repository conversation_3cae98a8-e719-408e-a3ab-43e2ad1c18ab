/**
 * In8 Employee Unsubscribe SL
 * 
 * Version    Date            Author           Remarks
 * 1.00       25 Aug 2016     <PERSON> (In8)   Initial Version
 *
 */

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response) {

	var employeeId = request.getParameter('internalid');
	
	if (employeeId) {		
		
		try {
			nlapiSubmitField('employee', employeeId, 'custentity_in8_unsubscribe_notifications', 'T');
			
			displayMessage('Thank you. You unsubscribed successfully.');
		} catch(e) {
			displayMessage('Error: ' + e);
		}

	} else {
		displayMessage('Invalid parameters.');
	}
}

/**
 * Displays a message
 * 
 * @param {String} message Message
 * @returns {Void}
 */
function displayMessage(message) {
	
	// Create a NetSuite form
    var form = nlapiCreateForm('Notifications', false),
    	html = message;
    
    // Add a new HTML field to display the HTML contents
    field = form.addField('file', 'inlinehtml', 'label');
    field.setLayoutType('outsidebelow');
    field.setDefaultValue('<font size="2pt">' + html + '</font>');
    
    //form.addButton('custombutton_back', 'Back', 'window.history.back()');
    
    response.writePage(form);
};