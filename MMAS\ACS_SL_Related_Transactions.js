/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/search', 'N/format/i18n'], function(search, format) {

    function onRequest(context) {

        try {
            
            var recId = context.request.parameters.recid;

            var curFormatter = format.getCurrencyFormatter({ currency: 'USD' });
            
            var invoiceSearchObj = search.load({
                id: 'customsearch833'
            });

            var invoiceIdFilter = search.createFilter({
                name: 'internalidnumber',
                operator: search.Operator.EQUALTO,
                values: recId
            });

            invoiceSearchObj.filters.push(invoiceIdFilter);

            var resultArray = [];
            
            var pagedData = invoiceSearchObj.runPaged({ pageSize: 5 });
            // iterate the pages
            for( var i=0; i < pagedData.pageRanges.length; i++ ) {

                // fetch the current page data
                var currentPage = pagedData.fetch(i);

                // and forEach() thru all results
                currentPage.data.forEach( function(result) {
                    var amount = Math.abs(result.getValue({ name: 'amount', join: 'applyingTransaction' }));
                    amount = curFormatter.format({ number: amount });

                    resultArray.push({
                        tranid: result.getValue({ name: 'tranid', join: 'applyingTransaction' }),
                        memo: result.getValue({ name: 'memo', join: 'applyingTransaction' }),
                        amount: amount,
                        trandate: result.getValue({ name: 'trandate', join: 'applyingTransaction' })
                    });
                });
        
            }

            log.debug('test', resultArray);
            
            var returnStr = "<#assign related_trans = " + JSON.stringify(resultArray) + " />"; 

            context.response.write({
                output: returnStr
            });

        } catch (e) {

            log.debug('Error', e);

        }
    }

    return {
        onRequest: onRequest
    }
});
