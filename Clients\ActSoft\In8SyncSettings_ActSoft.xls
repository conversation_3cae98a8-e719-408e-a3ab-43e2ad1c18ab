<?xml version="1.0" encoding="utf-8"?>
 <Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
<Author>NetSuite</Author>
<LastAuthor>NetSuite</LastAuthor>
<Company>NetSuite</Company>
</DocumentProperties>
<Styles>
<Style ss:ID="company">
<Alignment ss:Horizontal="Center"/>
<Font ss:Bold="1"/>
</Style> <Style ss:ID="error">
<Alignment ss:Horizontal="Center"/>
<Interior ss:Color="#d04040" ss:Pattern="Solid"/>
<Font ss:Bold="1"/>
</Style> <Style ss:ID="header">
<Alignment ss:Horizontal="Center"/>
<Font ss:Size="7" ss:Bold="1"/>
<Interior ss:Color="#d0d0d0" ss:Pattern="Solid"/>
</Style> <Style ss:ID="Default" ss:Name="Normal">
<Alignment ss:Vertical="Bottom"/>
<Borders/>
<Font ss:FontName="Arial" ss:Size="8"/>
<Interior/>
<NumberFormat/>
<Protection/>
</Style>
<Style ss:ID="s__TIMEOFDAY"><NumberFormat ss:Format="Medium Time"/></Style>
<Style ss:ID="s__DATETIME"><NumberFormat ss:Format="General Date"/></Style>
<Style ss:ID="s__DATETIMETZ"><NumberFormat ss:Format="General Date"/></Style>
<Style ss:ID="s__DATE"><NumberFormat ss:Format="Short Date"/>
</Style><Style ss:ID="s__text"></Style><Style ss:ID="s__currency"><NumberFormat ss:Format="Currency"/></Style>
<Style ss:ID="s__percent"><NumberFormat ss:Format="Percent"/></Style>
<Style ss:ID="s1_b_text"><Alignment ss:Indent="1"/><Font ss:FontName="Verdana" x:Family="Swiss" ss:Size="8" ss:Color="#000000" ss:Bold="1"/></Style>
<Style ss:ID="s_b_text"><Font ss:FontName="Verdana" x:Family="Swiss" ss:Size="8" ss:Color="#000000" ss:Bold="1"/></Style>
<Style ss:ID="s2__text"><Alignment ss:Indent="2"/></Style>
<Style ss:ID="s_b_currency"><Font ss:FontName="Verdana" x:Family="Swiss" ss:Size="8" ss:Color="#000000" ss:Bold="1"/><NumberFormat ss:Format="Currency"/></Style>
<Style ss:ID="s_currency_nosymbol"><Font ss:FontName="Verdana" x:Family="Swiss" ss:Size="8" ss:Color="#000000" /><NumberFormat ss:Format="#,##0.00_);[Red]\(#,##0.00\)"/></Style>
<Style ss:ID="s1__text"><Alignment ss:Indent="1"/></Style>
<Style ss:ID="s_b_currency_X"><Font ss:FontName="Verdana" x:Family="Swiss" ss:Size="8" ss:Color="#000000" ss:Bold="1"/><Interior ss:Color="#f0e0e0" ss:Pattern="Solid"/><NumberFormat ss:Format="Currency"/></Style>
<Style ss:ID="s__currency_en_CA"><Alignment ss:Vertical="Center" ss:Horizontal="Right"/><NumberFormat ss:Format="&quot;Can$&quot;#,##0.00_);(&quot;Can$&quot;#,##0.00)"/></Style>
<Style ss:ID="s__currency_en_US"><Alignment ss:Vertical="Center" ss:Horizontal="Right"/><NumberFormat ss:Format="&quot;$&quot;#,##0.00_);(&quot;$&quot;#,##0.00)"/></Style>
<Style ss:ID="s__currency_fr_FR_EURO"><Alignment ss:Vertical="Center" ss:Horizontal="Right"/><NumberFormat ss:Format="&quot;€&quot;#,##0.00_);(&quot;€&quot;#,##0.00)"/></Style>
<Style ss:ID="s__currency_en_GB"><Alignment ss:Vertical="Center" ss:Horizontal="Right"/><NumberFormat ss:Format="&quot;£&quot;#,##0.00_);(&quot;£&quot;#,##0.00)"/></Style>
</Styles>
<Worksheet ss:Name="InSyncSettingsSearchResults">
<Table><Row>
<Cell ss:StyleID="header"><Data ss:Type="String">Internal ID</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Assign Categories</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Backorders</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Bill Transaction Account</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Category</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Comments</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Create Cash Sale</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Create Customer If Deleted</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Create Customer if Inactive</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Create Invoice</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Create Item Fulfillment Transaction</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Customer Default Password</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Customer Default Role</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Enforce Display in Website (Deprecated by Item Parameter)</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Ignore Members on Kit Items</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">In8Sync API URL</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">In8Sync Consumer Key</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">In8Sync Consumer Secret</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Manage Stock</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Matrix SubItems Sync Only "Display Web Site"=Yes?</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Name</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Product Default Status</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Send Translations</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Site</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Stock Sync Enabled</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Subsidiary</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Sync Images on Update</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">Use Addressee As Company Name</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">WooCommerce REST URL</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">WordPress API URL</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">WordPress Access Token</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">WordPress Access Token Secret</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">WordPress Admin URL</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">WordPress Consumer Key</Data></Cell>
<Cell ss:StyleID="header"><Data ss:Type="String">WordPress Consumer Secret</Data></Cell>
</Row>
<Row><Cell ss:StyleID="s__text"><Data ss:Type="Number">1</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">Yes</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">password123</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">https://carriersiis.actsoft.com/wc-api/v3</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">ck_ab76f5abdeea3089cb61503b8108e6fd9c298879</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">cs_d4beca5ad03ea1010268e50d285ef93314ffde2f</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">WooCommerce</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String">No</Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
<Cell ss:StyleID="s__text"><Data ss:Type="String"></Data></Cell>
</Row>
</Table>
</Worksheet>
</Workbook>