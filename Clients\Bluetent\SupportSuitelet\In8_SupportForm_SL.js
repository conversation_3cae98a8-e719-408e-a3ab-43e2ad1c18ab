var secretKey = 'ac8420dd-5069-4877-8d19-c017c12706b2';
var recordId = null;
var contactid = null;
var accesslevel = 1;
var customform = null;
var id = null;
var hash = '';
var subtabs = [];
var sourceid = null;
var sourceform = null;
var touchedFields = [];
var defaultOpen = [];

function suitelet(request, response)
{
    try {
        if(request.getMethod() == "GET")
        {
            customform = request.getParameter('customform');

            if(customform == "null")
                throw nlapiCreateError('Custom Form', "You have insufficient privileges to access to this form");

            recordId = request.getParameter('recordid') || request.getParameter('custpage_customer');

            if(request.getParameter('accesslevel'))
                accesslevel = request.getParameter('accesslevel');

            id = request.getParameter('internalid') || request.getParameter('custpage_customer');
            contactid = request.getParameter('contactid') || request.getParameter('userid');
            hash = request.getParameter('hash');

            sourceid = request.getParameter('sourceid');
            sourceform = request.getParameter('sourceform');

            nlapiLogExecution('DEBUG', 'Hash', secretKey + contactid + id);

            var hashCompare = String(CryptoJS.SHA256(secretKey + contactid + id));

            if (!id || hash != hashCompare) {

                var form = nlapiCreateForm("Error");

                var field = form.addField('custpage_message', 'inlinehtml', 'Message');
                field.setDefaultValue("<h2>Invalid parameters: " + hash + "</h2>");

                response.writePage(form);
                return;
            }

            var form = nlapiCreateForm('');

            form.setScript('customscript_in8_supportform_cs');

            createBodyFields(form, customform);

            if(recordId != "new")
                createSublists(form, customform);

            //form.addButton('custpage_homebutton', 'Home', 'gotoDashboard()');

            var recordType = nlapiLookupField('customrecord_in8_customsupportform', customform, 'custrecord_in8_csf_recordtype');

            if(accesslevel == 2)
            {
                form.addSubmitButton('Save');
            }
            else
            {
                // if(!request.getParameter('dash'))
                //     form.addButton('custpage_editbutton', 'Edit', 'editRecord()');

                if(recordType == "supportcase")
                {
                    var caseStatus = nlapiLookupField('supportcase', recordId, 'status');

                    if(caseStatus != nlapiGetContext().getSetting('SCRIPT', 'SUPPORT_DEFAULT_CLOSEDSTATUS'))
                        form.addButton('custpage_resolvebutton', 'Resolve Case', 'resolveCase()');
                }
            }


            if (!request.getParameter('dash') && recordType != "customer") {
                form.addButton('custpage_mainrecordbutton', 'Back to Main Record', 'gotoMainRecord()');
            }

            response.writePage(form);
        }
        else
        {
            customform = request.getParameter('custpage_customform');
            recordId = request.getParameter('custpage_recordid');
            contactid = request.getParameter('custpage_contactid');

            id = request.getParameter('custpage_customer');
            hash = request.getParameter('custpage_hash');

            sourceid = request.getParameter('custpage_sourceid');
            sourceform = request.getParameter('custpage_sourceform');

            recordId = request.getParameter('custpage_recordid');

            var record = getSampleRecord();

            try
            {
                var fields = JSON.parse(request.getParameter('custpage_touchedfields'));

                for (var i = 0; i < fields.length; i++) {
                    record.setFieldValue(fields[i].name.replace('custpage_', ''), fields[i].value);
                }

                if(record.getRecordType() == "supportcase")
                {
                    var viewForm = nlapiLookupField('customrecord_in8_customsupportform', request.getParameter('custpage_custevent_in8_customformsuitelet'), 'custrecord_in8_csf_viewcaseform');

                    record.setFieldValue('custevent_in8_customformsuitelet', viewForm);// || request.getParameter('custpage_custevent_in8_customformsuitelet'));

                    if(viewForm)
                        customform = viewForm;
                }

                recordId = nlapiSubmitRecord(record);

                var formUrl = nlapiResolveURL('SUITELET',
                    'customscript_in8_supportform_sl',
                    'customdeploy_in8_supportform_sl',
                    parseInt(nlapiGetContext().getUser()) < 0);

                formUrl += '&customform='+customform
                    +'&internalid='+request.getParameter('custpage_employee')
                    +'&hash='+request.getParameter('custpage_hash')
                    +'&recordid='+recordId
                    +'&sourceid='+sourceid
                    +'&contactid='+contactid
                    +'&sourceform='+sourceform;


                response.write('<script>window.location = "'+formUrl+'"</script>');

            }
            catch (e1) {
                throw nlapiCreateError('Submit record', 'Error saving record: ' + e1.message);
            }
        }
    } catch(e) {
        nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e) + ' ' + e.stack);

        var form = nlapiCreateForm("Error");
        form.setScript('customscript_in8_supportform_cs');
        var code = "UNEXPECTED_ERROR";
        var message = "";

        if(e instanceof nlobjError)
        {
            code = e.getCode();
            message = e.getDetails();
        }
        else
        {
            message = e.message;
        }

        var field = form.addField('custpage_message', 'inlinehtml', 'Message');
        field.setDefaultValue("<h2>Code: " + code +" <br/>Message: " + message + "</h2>");

        form.addButton('custpage_mainrecordbutton', 'Back to Main Record', 'gotoMainRecord()');

        response.writePage(form);

	}
}

function createFormField(form, fieldObj, value, tab, mandatory, readonly, text)
{
    if(fieldObj)
    {
        var label = fieldObj.label;

        try
        {
            if (readonly)
            {
                var newField = form.addField('custpage_' + fieldObj.name, 'longtext', label,null, tab);
                newField.setDisplayType('inline');
            }
            else {

                if (mandatory)
                    label += " (*)";

                if (fieldObj.type == 'poscurrency')
                    var newField = form.addField('custpage_' + fieldObj.name, 'currency', label, null, tab);
                if (fieldObj.type == 'datetime')
                    var newField = form.addField('custpage_' + fieldObj.name, 'datetimetz', label, null, tab);
                if (fieldObj.type == 'identifieranycase')
                    var newField = form.addField('custpage_' + fieldObj.name, 'text', label, null, tab);
                else {
                    try {
                        var newField = form.addField('custpage_' + fieldObj.name, fieldObj.getType(), label, null, tab);
                    } catch (e) {
                        var newField = form.addField('custpage_' + fieldObj.name, 'text', label, null, tab);
                    }
                }

                if (mandatory)
                    newField.setMandatory(true);

                if (fieldObj.type == "select" || fieldObj.type == "multiselect") {
                    var options = fieldObj.getSelectOptions();

                    if (options)
                    {
                        newField.addSelectOption('', '');

                        if((options.length > 100 || options.length == 0) &&
                            fieldObj.getName() != 'custevent3' &&
                            fieldObj.getName() != 'custevent5' &&
                            fieldObj.getName() != 'assigned' &&
                            fieldObj.getName() != 'custevent35' &&
                            fieldObj.getName() != 'custentity96')
                        {
                            newField.addSelectOption('opensearch='+fieldObj.getName(), '******* Search *******');
                            newField.addSelectOption('showAll=T&opensearch='+fieldObj.getName(), '******* Show All *******');

                            nlapiLogExecution('DEBUG', 'Value+Text', value+"|"+text);

                            if(value && text)
                            {
                                newField.addSelectOption(value,text);
                            }
                        }
                        else
                        {
                            for (var i = 0; i < options.length; i++)
                            {
                                newField.addSelectOption(options[i].getId(), options[i].getText());
                            }
                        }
                    }
                }
            }

            //nlapiLogExecution('DEBUG', 'Value', value);

            if (value)
                newField.setDefaultValue(value)

        }
        catch(e)
        {
            nlapiLogExecution('ERROR', 'Creating field', e.message);
        }
    }

    return form;
}

function getSampleRecord()
{
    var customForm = nlapiLoadRecord('customrecord_in8_customsupportform', customform);

    if(recordId == "new")
    {
        var record = nlapiCreateRecord(customForm.getFieldValue('custrecord_in8_csf_recordtype'));

        if(request.getParameter('sourcetype') && request.getParameter('sourceid'))
        {
            var linkField = nlapiLookupField('customrecord_in8_csf_sublists', request.getParameter('sourcetype'), 'custrecord_in8_csfs_parentfield');

            if(linkField)
            {
                record.setFieldValue(linkField, request.getParameter('sourceid'));

                var field = {};
                field.name = linkField;
                field.value = request.getParameter('sourceid');

                touchedFields.push(field);

                if(customForm.getFieldValue('custrecord_in8_csf_recordtype') == "supportcase")
                {
                    record.setFieldValue('contact', contactid);

                    var field = {};
                    field.name = 'custpage_contact';
                    field.value = contactid;

                    touchedFields.push(field);
                }
            }
        }

        if(sourceid && customForm.getFieldValue('custrecord_in8_csf_recordtype') == "projectTask")
        {
            //nlapiLogExecution('DEBUG', 'Create task', "Task created");
            var record = nlapiCreateRecord('projectTask', {company : sourceid});
            record.setFieldValue('custevent5', id);
        }

        return record;
    }
    else
        return nlapiLoadRecord(customForm.getFieldValue('custrecord_in8_csf_recordtype'), recordId);
}

function createBodyFields(form, customform)
{
    var record = getSampleRecord(customform);

    var userForms = getUserForms();

    form = createParameterField(form, 'custpage_touchedfields', JSON.stringify(touchedFields));
    form = createParameterField(form, 'custpage_recordid', recordId);
    form = createParameterField(form, 'custpage_contactid', contactid);
    form = createParameterField(form, 'custpage_customform', customform);
    form = createParameterField(form, 'custpage_employee', id);
    form = createParameterField(form, 'custpage_hash', hash);
    form = createParameterField(form, 'custpage_sourceid', sourceid);
    form = createParameterField(form, 'custpage_sourceform', sourceform);
    form = createParameterField(form, 'custpage_defaultopened', '');
    form = createParameterField(form, 'custpage_filters', request.getParameter('filters') ? JSON.parse(request.getParameter('filters')) : {});
    form = createParameterField(form, 'custpage_history', nlapiGetContext().getSessionObject('random'));

    if(record.getRecordType() == "supportcase")
    {
        form = createCustomFormField(form);
    }

    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_csff_fieldid'));
    columns.push(new nlobjSearchColumn('custrecord_in8_csff_readonly'));
    columns.push(new nlobjSearchColumn('custrecord_in8_csff_mandatory'));
    columns.push(new nlobjSearchColumn('custrecord_in8_csff_relatedrecord'));
    columns.push(new nlobjSearchColumn('custrecord_in8_csff_group').setSort(false));
    columns.push(new nlobjSearchColumn('custrecord_in8_csff_order').setSort(false));

    var search = nlapiSearchRecord('customrecord_in8_csf_fields', null, new nlobjSearchFilter('custrecord_in8_csff_externalform', null, 'anyof', customform), columns);

    var currentTab = '';
    var currentGroup = '';

    var tab = null;

    for(var i = 0; search != null && i < search.length; i++)
    {
        // if(buildObjectName(currentTab) != buildObjectName(search[i].getValue('custrecord_in8_csff_tab')) && search[i].getValue('custrecord_in8_csff_tab'))
        // {
        //     tab = form.addTab(buildObjectName(search[i].getValue('custrecord_in8_csff_tab')), search[i].getValue('custrecord_in8_csff_tab'));
        //     currentTab = search[i].getValue('custrecord_in8_csff_tab');
        //
        //     subtabs.push({ id: buildObjectName(search[i].getValue('custrecord_in8_csff_tab')) , label: search[i].getValue('custrecord_in8_csff_tab')});
        // }

        if(search[i].getValue('custrecord_in8_csff_group'))
            form.addFieldGroup(buildObjectName(search[i].getValue('custrecord_in8_csff_group')),search[i].getValue('custrecord_in8_csff_group'));

        var field = record.getField(search[i].getValue('custrecord_in8_csff_fieldid'));

        var readOnly = true;
        var value = null;

        var touchField = touchedFields.filter(function(value){
            return value.name == search[i].getValue('custrecord_in8_csff_fieldid');
        });

        //nlapiLogExecution('DEBUG', 'Touch', JSON.stringify(touchField));

        if(accesslevel == 2 &&
            search[i].getValue('custrecord_in8_csff_readonly') == "F" &&
            touchField.length == 0)
        {
            readOnly = false;

            if(search[i].getValue('custrecord_in8_csff_fieldid') != "incomingmessage")
                value = record.getFieldValue(search[i].getValue('custrecord_in8_csff_fieldid'));
        }
        else
        {
            value = record.getFieldText(search[i].getValue('custrecord_in8_csff_fieldid')) ? record.getFieldText(search[i].getValue('custrecord_in8_csff_fieldid')) : record.getFieldValue(search[i].getValue('custrecord_in8_csff_fieldid'));

            if(search[i].getValue('custrecord_in8_csff_relatedrecord'))
            {
                var userForm = userForms.filter(function (result){
                    if(result.getValue('custrecord_in8_csf_recordtype') == search[i].getValue('custrecord_in8_csff_relatedrecord'))
                        return result;
                })

                nlapiLogExecution('DEBUG', 'UserForm', JSON.stringify(userForm));

                if(userForm.length > 0)
                {
                    var formUrl = nlapiResolveURL('SUITELET',
                        'customscript_in8_supportform_sl',
                        'customdeploy_in8_supportform_sl',
                        parseInt(nlapiGetContext().getUser()) < 0) +
                        '&hash='+hash+
                        '&internalid='+id+
                        '&contactid='+contactid+
                        '&customform='+userForm[0].getId()+
                        '&recordid='+record.getFieldValue(search[i].getValue('custrecord_in8_csff_fieldid'));

                    value = '<a href=\''+formUrl+'\'>'+value+'</a>';
                }
            }
        }

        form = createFormField(form,
            field,
            value,
            search[i].getValue('custrecord_in8_csff_group') ? buildObjectName(search[i].getValue('custrecord_in8_csff_group')) : null,
            search[i].getValue('custrecord_in8_csff_mandatory') == "T",
            readOnly,
            record.getFieldText(search[i].getValue('custrecord_in8_csff_fieldid')));
    }

    if(search == null)
        form.addField('custpage_nofields', 'inlinehtml', '').setDefaultValue('There are no fields setup for this form.');

    return form;
}

function buildObjectName(label)
{
    var objectName = "custpage_" + label.replace(/[^a-zA-Z0-9_]+/g, '');

    objectName = objectName.toLowerCase();

    return objectName;
}

function createSublists(form, customform)
{
    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_csfs_savedsearch'));
    columns.push(new nlobjSearchColumn('custrecord_in8_csfs_parentfield'));
    columns.push(new nlobjSearchColumn('name'));
    columns.push(new nlobjSearchColumn('custrecord_in8_csfs_edit'));
    columns.push(new nlobjSearchColumn('custrecord_in8_csfs_create'));
    columns.push(new nlobjSearchColumn('custrecord_in8_csfs_opened'));

    var search = nlapiSearchRecord('customrecord_in8_csf_sublists', null, new nlobjSearchFilter('custrecord_in8_csfs_customform', null, 'anyof', customform), columns);

    var formUrl = nlapiResolveURL('SUITELET',
        'customscript_in8_supportform_sl',
        'customdeploy_in8_supportform_sl',
        parseInt(nlapiGetContext().getUser()) < 0);

    for(var i = 0; search && i < search.length; i++)
    {
        //nlapiLogExecution('DEBUG', 'Create Sublists', 'name: ' + search[i].getValue('name') );

        var newTab = form.addTab(buildObjectName('tab_'+search[i].getValue('name')),search[i].getValue('name'));

        subtabs.push({ id: buildObjectName('tab_'+search[i].getValue('name')) , label: search[i].getValue('name')});

        var sublistSearch = nlapiLoadSearch(null, search[i].getValue('custrecord_in8_csfs_savedsearch'));

        var sublistForm = getCustomForm(sublistSearch.getSearchType());

        if(sublistSearch.getSearchType() == "supportcase" && search[i].getValue('custrecord_in8_csfs_parentfield') == "company")
        {
            sublistSearch.addFilter(new nlobjSearchFilter('internalid', 'customer', 'anyof', recordId));

            if(contactid)
                sublistSearch.addFilter(new nlobjSearchFilter('internalid','contact', 'anyof', contactid));

            sublistSearch.addColumn(new nlobjSearchColumn('custevent_in8_customformsuitelet'));
        }
        else if(sublistSearch.getSearchType() == "message")
        {
            if(nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_csf_internalmessage') == "T")
                sublistSearch.addFilter(new nlobjSearchFilter('internalonly', null, 'is', "F"));

            sublistSearch.addFilter(new nlobjSearchFilter('internalid', search[i].getValue('custrecord_in8_csfs_parentfield'), 'anyof', recordId));
        }
        else if(sublistSearch.getSearchType() == "timebill" && search[i].getValue('custrecord_in8_csfs_parentfield') == "custevent_project_task")
        {
            var projectTaskId = nlapiLookupField('supportcase', recordId, 'custevent_project_task');

            if(projectTaskId)
                sublistSearch.addFilter(new nlobjSearchFilter('casetaskevent', null, 'anyof', projectTaskId));

            sublistSearch.addFilter(new nlobjSearchFilter('custcoltime_case_id', null, 'anyof', recordId));
        }
        else {
            if (search[i].getValue('custrecord_in8_csfs_parentfield')) {
                sublistSearch.addFilter(new nlobjSearchFilter(search[i].getValue('custrecord_in8_csfs_parentfield'), null, 'anyof', recordId));
            }
        }

        var sublist = form.addSubList(buildObjectName(search[i].getValue('name')), 'list', search[i].getValue('name'), buildObjectName('tab_'+search[i].getValue('name')));

        [form, sublist, sublistSearch] = addSublistFilters(form, search[i].getId(), sublist, sublistSearch, search[i].getValue('name'));

        if(search[i].getValue('custrecord_in8_csfs_parentfield') == "case" && sublistSearch.getSearchType() == "message")
            sublist.addButton('custpage_newmessage', 'New Message', 'newMessageForm()');

        if(search[i].getValue('custrecord_in8_csfs_parentfield') == "internalid" && sublistSearch.getSearchType() == "supportcase")
            sublist.addButton('custpage_newfile', 'New file', 'newFileForm()');
        //var field = form.addField('custpage_filter', 'inlinehtml', '', null, buildObjectName('tab_'+search[i].getValue('name')));
        //field.setDefaultValue('<div id="divFilter">Filter  Name: <input type="text" id="filterItemName" onblur="filterName()" value="' + (request.getParameter('filterItemName') ? request.getParameter('filterItemName') : '') + '"></div>');

        var pagesField = form.addField(buildObjectName('page_'+search[i].getValue('name')), 'select', '', null, buildObjectName('tab_'+search[i].getValue('name')));

        var pagesValues = getSearchPages(sublistSearch);

        for(var opt = 0; opt < pagesValues.length; opt++)
        {
            pagesField.addSelectOption(pagesValues[opt].id, pagesValues[opt].label);
        }

        var sublistURL = formUrl + '&hash='+hash+'&internalid='+id+'&sourceid='+recordId+'&sourceform='+customform+'&contactid='+contactid+'&recordid=';

        var columns = sublistSearch.getColumns();

        sublist = createSublistFields(sublist, columns);

        if(search[i].getValue('custrecord_in8_csfs_create') == "T")
        {
            sublist.addButton('custpage_addrecord', sublistSearch.getSearchType() != "supportcase" ? 'Create' : "Open Support Case", "createRecord('"+sublistURL+'new&customform='+sublistForm+"&accesslevel=2&contactid="+contactid+"&sourceid="+recordId+"&sourcetype="+search[i].getId()+"');");
        }

        var line = 1;

        if(request.getParameter('machine') == buildObjectName(search[i].getValue('name')) && request.getParameter('page'))
            var page = request.getParameter('page');
        else
            var page = 1;

        pagesField.setDefaultValue(page);

        var summedValues = [];
        var hasSumFields = false;

        if((sublistSearch.getSearchType() != "customer" || sublistSearch.getFilters().length > 2) && recordId != "new")
        {
            var start = 50*(page-1);
            var end = 50*page;

            sublistSearch.runSearch().getResults(start, end).map(function (result) {

                if(sublistSearch.getSearchType() == "supportcase")
                    sublistForm = result.getValue('custevent_in8_customformsuitelet') || nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_csf_caseviewform');

                var url = sublistURL + result.getId()+'&customform='+sublistForm;
                var link = "<a href='#' onclick=\"window.location.href = '" + url + "'\">View</a>";

                // if (search[i].getValue('custrecord_in8_csfs_edit') == "T") {
                //     var url = sublistURL + result.getId() + '&accesslevel=2&customform='+sublistForm;
                //     link += "| <a href='#' onclick=\"window.location.href = '" + url + "'\">Edit</a>";
                // }

                if (sublistForm) {
                    sublist.setLineItemValue("custpage_link", line, link);
                }

                for (var c = 0; c < columns.length; c++) {
                    var value = "";

                    if(columns[c].getName() == "name" && columns[c].getJoin() == "file")
                    {
                        var link = "<a href='#' onclick=\"viewFile('" + result.getValue('internalid', 'file') + "')\">View</a>";
                        sublist.setLineItemValue("custpage_link", line, link);
                    }

                    if(result.getRecordType() == "projecttask" && columns[c].getName() == "title")
                    {
                        var titleArray = String(result.getValue(columns[c])).split(":");

                        if(titleArray.length > 1)
                            value = "&nbsp;&nbsp;&nbsp;&nbsp;" + titleArray[titleArray.length-1];
                        else
                            value = '<b><h1>' + titleArray[titleArray.length-1] + '</h1></b>';

                        nlapiLogExecution('DEBUG', 'Title', 'Tiel');
                    }
                    else
                    {
                        if (result.getText(columns[c]))
                            value = String(result.getText(columns[c])).length > 300 ? String(stripHtml(result.getText(columns[c]))).substr(0, 300) : stripHtml(result.getText(columns[c]));
                        else
                            value = String(result.getValue(columns[c])).length > 300 ? String(stripHtml(result.getValue(columns[c]))).substr(0, 300) : stripHtml(result.getValue(columns[c]));
                    }
                    sublist.setLineItemValue("custpage_" + columns[c].getName(), line, value);

                    if(columns[c].type == "currency" || columns[c].type == "float" || columns[c].type == "integer" || columns[c].name == "hours")
                    {
                        if(columns[c].name == "hours")
                            value = hoursToInt(value);

                        if(!isNaN(Number(value)))
                        {
                            if (summedValues["custpage_" + columns[c].getName()])
                                summedValues["custpage_" + columns[c].getName()] = parseFloat(summedValues["custpage_" + columns[c].getName()] || 0) + parseFloat(value || 0);
                            else
                                summedValues["custpage_" + columns[c].getName()] = parseFloat(value || 0);

                            hasSumFields = true;
                        }
                    }
                }

                if (sublistSearch.getId() == "1269") {
                    //nlapiLogExecution('DEBUG', 'Value', parseFloat(result.getValue('custentity145')));
                    var color = setPaceReportColor(parseFloat(result.getValue('custentity145')));

                    if (color)
                        sublist.setLineItemValue("custpage_linecolor", line, color);
                }

                line++;

                // if (line > maxResults) {
                //     return false;
                // }
                return true;
            });

            if(hasSumFields)
            {
                sublist.setLineItemValue('custpage_link', line, '<b>Totals</b>');
                for (var keys in summedValues)
                {
                    if(keys == 'custpage_hours')
                        sublist.setLineItemValue(keys, line, intToHours(summedValues[keys]));
                    else
                        sublist.setLineItemValue(keys, line, summedValues[keys]);
                }
            }
        }

        if(search[i].getValue('custrecord_in8_csfs_opened') == "T" ||
            (request.getParameter('companyname') && sublistSearch.getSearchType() == "customer"))
        {
            defaultOpen.push(buildObjectName('tab_' + search[i].getValue('name')));
        }

    }

    form.getField('custpage_defaultopened').setDefaultValue(JSON.stringify(defaultOpen));

    return form;
}

function createSublistFields(sublist, columns)
{
    sublist.addField('custpage_link', 'textarea', '');

    var hasAmountField = false;

    for(var i = 0; i < columns.length; i++)
    {
        try {

            if(columns[i].type == "date" || columns[i].type == "float" || columns[i].type == "currency")
                var field = sublist.addField("custpage_"+columns[i].getName(), columns[i].type, columns[i].getLabel());
            //else if(columns[i].type == "datetime")
            //    var field = sublist.addField("custpage_"+columns[i].getName(), 'datetimetz', columns[i].getLabel());
            else
                var field = sublist.addField("custpage_"+columns[i].getName(), "text", columns[i].getLabel());

            if(columns[i].getName() == "title")
                field.setLabel("Name ______________________________");

        } catch(e) {
            nlapiLogExecution('ERROR', 'Creating field ' + columns[i].getName(), e);
        }
    }

    sublist.addField('custpage_linecolor', 'text', '').setDisplayType('hidden');

    return sublist;
}


function checkAccess(id) {

    var search = nlapiSearchRecord('employee', null, new nlobjSearchFilter('internalid', null, 'anyof', id), new nlobjSearchColumn('custentity_in8_employeeslaccess'));

    if(search)
    {
        return parseInt(search[0].getValue('custentity_in8_employeeslaccess'));
    }

    return 3;
}

function getCustomForm(recordType) //TODO
{
    var filters = [];
    filters.push(new nlobjSearchFilter('custrecord_in8_csf_recordtype', null, 'is', recordType));

    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_csf_recordtype'));

    var search = nlapiSearchRecord('customrecord_in8_customsupportform', null, filters, columns);

    if(search)
        return search[0].getId();
    else
        return [];
}

function getUserForms()
{
    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_csf_recordtype'));

    var search = nlapiSearchRecord('customrecord_in8_customsupportform', null, null, columns);

    if(search)
        return search;
    else
        return [];
}

function createParameterField(form, id, value)
{
    var hiddenField = form.addField(id, 'longtext', '');
    hiddenField.setDefaultValue(value);
    hiddenField.setDisplayType('hidden');

    return form;
}

function addSublistFilters(form, sublistId, sublist, search, sublistName)
{
    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_csfsf_type'));
    columns.push(new nlobjSearchColumn('custrecord_in8_csfsf_fieldid'));
    columns.push(new nlobjSearchColumn('name'));

    var filterData = {};

    if(request.getParameter('filters'))
    {
        var filterData = JSON.parse(JSON.parse(request.getParameter('filters')));
    }

    var searchFilters = nlapiSearchRecord('customrecord_in8_csfs_filters', null, new nlobjSearchFilter('custrecord_in8_csfsf_sublist', null, 'anyof', sublistId), columns);

    if(searchFilters)
    {
        var baseRecord = nlapiCreateRecord(search.getSearchType());

        sublist.addButton(sublistName+"btn", 'Search', 'searchCustomer(\''+buildObjectName(sublistName)+'\')');

        for (var f = 0; searchFilters && f < searchFilters.length; f++)
        {
            var filterFieldId = buildObjectName('filter_'+sublistName+'_'+searchFilters[f].getValue('custrecord_in8_csfsf_fieldid'));

            if (searchFilters[f].getValue('custrecord_in8_csfsf_type') == "select")
            {
                var fieldObj = baseRecord.getField(searchFilters[f].getValue('custrecord_in8_csfsf_fieldid'));

                if (fieldObj && fieldObj.getType() == "select")
                {
                    var sublistFilter = form.addField(filterFieldId, "select", searchFilters[f].getValue('name'), null, buildObjectName('tab_'+sublistName));

                    var options = fieldObj.getSelectOptions();

                    if (options)
                    {
                        sublistFilter.addSelectOption('', '');

                        for (var i = 0; i < options.length; i++)
                        {
                            sublistFilter.addSelectOption(options[i].getId(), options[i].getText());
                        }
                    }

                    var value = filterData[filterFieldId];

                    if(value)
                    {
                        if(search.getSearchType() == "supportcase" && searchFilters[f].getValue('custrecord_in8_csfsf_fieldid') == "company")
                            search.addFilter(new nlobjSearchFilter('internalid', 'customer', 'anyof', value));
                        else
                            search.addFilter(new nlobjSearchFilter(searchFilters[f].getValue('custrecord_in8_csfsf_fieldid'), null, 'anyof', value));

                        sublistFilter.setDefaultValue(value);
                    }
                }
            }
            else if(searchFilters[f].getValue('custrecord_in8_csfsf_type') == "date")
            {
                var sublistFilter = form.addField(filterFieldId+"_start", "date", searchFilters[f].getValue('name') + " From", null, buildObjectName('tab_'+sublistName));
                var startDate = filterData[filterFieldId+"_start"];

                if(startDate)
                    sublistFilter.setDefaultValue(startDate);

                var sublistFilter = form.addField(filterFieldId+"_end", "date", searchFilters[f].getValue('name') + " To", null, buildObjectName('tab_'+sublistName));
                var endDate = filterData[filterFieldId+"_end"];

                if(endDate)
                    sublistFilter.setDefaultValue(endDate);

                if(startDate && endDate)
                {
                    search.addFilter(new nlobjSearchFilter(searchFilters[f].getValue('custrecord_in8_csfsf_fieldid'), null, 'within', startDate, endDate));
                }
                else if(startDate)
                {
                    search.addFilter(new nlobjSearchFilter(searchFilters[f].getValue('custrecord_in8_csfsf_fieldid'), null, 'onorafter', startDate));
                }
                else if(endDate)
                {
                    search.addFilter(new nlobjSearchFilter(searchFilters[f].getValue('custrecord_in8_csfsf_fieldid'), null, 'onorbefore', endDate));
                }
            }
            else
            {
                var sublistFilter = form.addField(filterFieldId, "text", searchFilters[f].getValue('name'), null, buildObjectName('tab_'+sublistName));

                var value = filterData[filterFieldId];

                if(value)
                {
                    search.addFilter(new nlobjSearchFilter(searchFilters[f].getValue('custrecord_in8_csfsf_fieldid'), null, 'contains', value));
                    sublistFilter.setDefaultValue(value);
                }
            }
        }
    }

    return [form, sublist, search];
}

function stripHtml (html)
{
    var ret = html.replace(/&gt;/g, '>');
    ret = ret.replace(/&lt;/g, '<');
    ret = ret.replace(/&quot;/g, '"');
    ret = ret.replace(/&apos;/g, "'");
    ret = ret.replace(/&amp;/g, '&');

    return ret.replace(/<(?:.|\n)*?>/gm, '');
}

function getSearchPages(search)
{

    var ret = [];
    var resultsQty = 0;
    var resultSet = search.runSearch();

    var start = 0;
    var end = 1000;

    do
    {
        var results = resultSet.getResults(start,end);

        resultsQty += parseInt(results.length);

        start += 1000;
        end += 1000;
    }
    while(results.length > 0 && end < 4000)

    var pages = parseInt(Math.floor(resultsQty / 50)) + 1;

    for(var i = 1; i <= pages; i++)
    {
        ret.push({
           id: i,
           label: "Page "+((i*50)-49)+" to " + (i*50)
        });
    }

    return ret
}

function hoursToInt(hours)
{
    var time = hours.split(':');

    var ret = parseFloat(time[0] * 60) + parseFloat(time[1]);

    return ret;
}

function intToHours(minutes)
{
    var hours = parseInt(minutes / 60);

    minutes = Number(minutes % 60);

    return hours+":"+(minutes < 10 ? "0"+minutes : minutes);
}

function createCustomFormField(form)
{

    if(recordId == "new")
    {
        form.addFieldGroup('custpage_casetypegroup', 'Case Type')

        var hiddenField = form.addField('custpage_custevent_in8_customformsuitelet', 'select', 'What problem are you looking for help?', null, 'custpage_casetypegroup');

        var filters = [];
        filters.push(new nlobjSearchFilter('custrecord_in8_csf_recordtype', null, 'is', 'supportcase'));
        filters.push(new nlobjSearchFilter('custrecord_in8_csf_casecreation', null, 'is', 'T'));

        var columns = [];
        columns.push(new nlobjSearchColumn('name'));

        var search = nlapiSearchRecord('customrecord_in8_customsupportform', null, filters, columns);

        for (var i = 0; search && i < search.length; i++) {
            hiddenField.addSelectOption(search[i].getId(), search[i].getValue('name'));
        }

        hiddenField.setDefaultValue(customform);
    }

    return form;

}