<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
<#if .locale == "ru_RU">
    <link name="verdana" type="font" subtype="opentype" src="${nsfont.verdana}" src-bold="${nsfont.verdana_bold}" bytes="2" />
</#if>
    <macrolist>
        <macro id="nlheader">
         
<table class="header" style="width: 100%;"><tr>
    <td width="500px" rowspan="7">
        <img src="https://system.na3.netsuite.com/core/media/media.nl?id=154&amp;c=4768130&amp;h=1f7ec539a1e3f8baa175" style="width: 128px; height: 64px;" />

        <table style="width: 100%; margin-top: 10px;">
            <tr>
            <td class="address" colspan="3" rowspan="2"><span class="nameandaddress">${companyInformation.companyName}</span><br/><span class="nameandaddress">${companyInformation.addressText} <br/>
            Tel: (*************</span></td>
            <td class="address" colspan="3" rowspan="2">
            <b>${record.billaddress@label}</b><br/>
            ${record.billaddress}</td>
            </tr>
        </table>
    </td>
	<td colspan="2" align="right"><span class="title"><br/></span></td>
	</tr>
	<tr>
	<td colspan="2" align="right"><span class="number"><br/></span></td>
	</tr>
    <tr>
	<td align="right">Account</td>
    <td align="right">${record.entity.custentityaccount_number?string.computer}</td>
	</tr>
	<tr>
	<td align="right">Date</td>
    <td align="right">${record.trandate}</td>
	</tr>
    <tr>
    <td align="right">Terms</td>
    <td align="right">${record.terms}</td>
    </tr>
    <tr>
    <td align="right">Due Date</td>
	<td align="right">${record.duedate}</td>
    </tr>
    </table>
          
        </macro>
        <macro id="nlfooter">
            <table class="footer" style="width: 100%;"><tr>
	<td></td>
	<td align="right"></td>
	</tr></table>
        </macro>
    </macrolist>
    <style type="text/css">table {
        <#if .locale == "zh_CN">
            font-family: stsong, sans-serif;
        <#elseif .locale == "zh_TW">
            font-family: msung, sans-serif;
        <#elseif .locale == "ja_JP">
            font-family: heiseimin, sans-serif;
        <#elseif .locale == "ko_KR">
            font-family: hygothic, sans-serif;
        <#elseif .locale == "ru_RU">
            font-family: verdana;
        <#else>
            font-family: sans-serif;
        </#if>
            font-size: 9pt;
            table-layout: fixed;
        }
        th {
            font-weight: bold;
            font-size: 8pt;
            
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
            color: #333333;
        }
        td {
            padding: 4px 6px;
        }
        b {
            font-weight: bold;
            color: #333333;
        }
        table.header td {
            padding: 0px;
            font-size: 10pt;
        }
        table.footer td {
            padding: 0px;
            font-size: 8pt;
        }
        table.itemtable th {
            padding-bottom: 10px;
            padding-top: 10px;
        }
        table.body td {
            padding-top: 2px;
        }
        table.total {
            page-break-inside: avoid;
        }
        tr.totalrow {
            background-color: #e3e3e3;
            line-height: 200%;
        }
        td.totalboxtop {
            font-size: 12pt;
            background-color: #e3e3e3;
        }
        td.addressheader {
            font-size: 8pt;
            padding-top: 6px;
            padding-bottom: 2px;
        }
        td.address {
            padding-top: 0px;
        }
        td.totalboxmid {
            font-size: 28pt;
            padding-top: 20px;
            background-color: #e3e3e3;
        }
        td.totalboxbot {
            background-color: #e3e3e3;
            font-weight: bold;
        }
        span.title {
            font-size: 28pt;
        }
        span.number {
            font-size: 16pt;
        }
        span.itemname {
            font-weight: bold;
            line-height: 150%;
        }
        hr {
            width: 100%;
            color: #d3d3d3;
            background-color: #d3d3d3;
            height: 1px;
        }
</style>
  <style> 
    #page2 {header:nlheader} 
</style>
</head>

<body  header-height="20%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">

<br/><br/><br/><br/><br/><br/>   
  <table style="width: 100%;"><tr>
	<td width="500px" rowspan="7">
    	<img src="https://system.na3.netsuite.com/core/media/media.nl?id=154&amp;c=4768130&amp;h=1f7ec539a1e3f8baa175" style="width: 128px; height: 64px;" />

        <table style="width: 100%; margin-top: 10px;">
          <tr>
          <td class="address" colspan="3" rowspan="2"><span class="nameandaddress">${companyInformation.companyName}</span><br/><span class="nameandaddress">${companyInformation.addressText} <br/>
           Tel: (*************</span></td>
          <td class="address" colspan="3" rowspan="2">
            <b>${record.billaddress@label}</b><br/>
            ${record.billaddress}</td>
          </tr>
        </table>

    </td>
	<td colspan="2" align="right"><span class="title">${record@title}</span></td>
	</tr>
	<tr>
	<td colspan="2" align="right"><span class="number">#${record.tranid}</span></td>
	</tr>
    <tr>
	<td align="right">Account</td>
    <td align="right">${record.entity.custentityaccount_number?string.computer}</td>
	</tr>
	<tr>
	<td align="right">Date</td>
    <td align="right">${record.trandate}</td>
	</tr>
    <tr>
    <td align="right">Terms</td>
    <td align="right">${record.terms}</td>
    </tr>
    <tr>
    <td align="right">Due Date</td>
	<td align="right">${record.duedate}</td>
    </tr>
    </table>
  
   <table style="width: 100%; margin-top: 10px;"><tr>
	<td class="addressheader" colspan="3"></td>
	<td class="addressheader" colspan="3"></td>
	<td class="totalboxtop" colspan="5"><b>Amount Due</b></td>
	</tr>
	<tr>
	<td class="address" colspan="3" rowspan="2"></td>
	<td class="address" colspan="3" rowspan="2"></td>
	<td align="right" class="totalboxmid" colspan="5">${record.amountremaining}</td>
	</tr>
	</table>

  	<#assign taxrate='0%'>
    <#list record.item as item>
      <#if item.taxrate1?has_content>
        <#assign taxrate=item.taxrate1>
      </#if>
    </#list>

      <table style="width: 100%;">
        <tr>
          <td style="width:60%"></td>
          <td>Subtotal:</td>
          <td>${record.subtotal}</td>
        </tr>
        <tr>
          <td></td>
          <td>Tax (%):</td>
          <td>${taxrate}</td>
        </tr>
        <tr>
          <td></td>
          <td>Total:</td>
          <td>${record.total}</td>
        </tr>
        <tr>
          <td></td>
          <td>Credits Applied:</td>
          <td>${record.custbody_cm_applied?string.currency}</td>
        </tr>
        <tr>
          <td></td>
          <td>Amount Paid</td>
          <td>${record.amountpaid}</td>
        </tr>
      </table>

	<#assign total = record.total>
	<#assign remaining = record.amountremaining>
  
 <pbr/>
  
<#if record.item?has_content>

<!-- Groups Information -->
<#assign groups = []>
<#list record.item as item>
  <#assign hasItem = false>
  <#list groups as group>
    <#if group.custcol_customer_site == item.custcol_customer_site>
      <#assign hasItem = true>
      <#break>
    </#if>
  </#list>
  <#if !hasItem>
    <#assign groups = groups + [ { "custcol_customer_site": item.custcol_customer_site } ]>
  </#if>
</#list>
</#if>
    
<!-- Group Items -->
<#assign groupsItems = []>
<#list record.item as item>
  <#assign hasItem = false>
  <#list groupsItems as group>
    <#if group.custcol_customer_site == item.custcol_customer_site && group.item == item.item && group.rate == item.rate>
      <#assign hasItem = true>
      <#break>
    </#if>
  </#list>
  <#if !hasItem>
    <#assign groupsItems = groupsItems + [ { "custcol_customer_site": item.custcol_customer_site, "item": item.item, "rate": item.rate } ]>
  </#if>
</#list>

<table class="itemtable" style="width: 100%; margin-top: 10px;"><!-- start items -->
<thead>
	<tr>
	  <th align="center" colspan="5">Branch</th>
      <th align="center" colspan="3">Qty</th>
	  <th colspan="12">Description</th>
      <th colspan="7">Billing Period</th>
	  <th align="right" colspan="4">Rate</th>
	  <th align="right" colspan="4">Amount</th>
	</tr>
</thead>
    <!-- First get the groups -->
   <#list groups?sort_by("custcol_customer_site") as group>
    <#assign groupIndex = 0>
    <#assign amountGroup = 0>
    
    <!-- Go over grouped items-->
    <#list groupsItems as groupItem>
      
      <!-- Calculate quantity, amount -->
      <#assign quantity = 0>
      <#assign amount = 0>
      <#assign vehicles = ''>
      <#assign billingperiod = ''>
      <#assign rate = ''>
    
      <!-- Show grouped items -->
      <#if group.custcol_customer_site == groupItem.custcol_customer_site>

        <!-- For each grouped item, calculate values -->
        <#list record.item as item>
            <#if groupItem.custcol_customer_site == item.custcol_customer_site && groupItem.item == item.item && groupItem.rate == item.rate>
                <#assign quantity = quantity + item.quantity>
                <#assign amount = amount + item.amount>
                <#assign rate = item.rate>
                <#assign vehicles = vehicles + "<br/>" + item.custcol_customer_vehicle?replace('&lt;br /&gt;', '<br/>')>
                <#assign billingperiod = item.custcol_rr_billingperiodstart + " - " + item.custcol_rr_billingperiodend>
            </#if>
        </#list>

            <#assign amountGroup = amountGroup + amount>
            <tr>
              <#if groupIndex == 0>
                <td colspan="5" style="vertical-align: top"><p style="text-align: left;text-justify: none; vertical-align: top;font-weight:bold">${groupItem.custcol_customer_site}</p></td>
                <#assign groupIndex = groupIndex + 1>
              <#else>
                 <td colspan="5"></td>
              </#if>
              <td align="center" colspan="3" line-height="150%">${quantity}</td>
              <td colspan="12"><p style="text-align: left;text-justify: none; vertical-align: top;">${groupItem.item} ${vehicles}</p></td>
              <td colspan="7" >${billingperiod}</td>
              <td align="right" colspan="4">${rate}</td>
              <td align="right" colspan="4">${amount?string.currency}</td>
            </tr>
        </#if>
    </#list>

    <!-- End of group -->
	<tr style="margin-bottom:10px">
      <td align="center" colspan="5"></td>
      <td align="center" colspan="3"></td>
      <td colspan="3"></td>
      <td align="right" colspan="20"><span class="itemname">${group.custcol_customer_site} Total</span></td>
      <td align="right" colspan="4">${amountGroup?string.currency}</td>
   </tr>
  </#list>
</table>

  </body>
</pdf>