/**
 *@NApiVersion 2.1
 *@NScriptType Restlet
 */
 define(['N/query', 'N/search', 'N/record', 'N/https', 'N/url'], function(query, search, record, https, url) {

    var tayloredLocation = 17;

    function getSalesOrderList(type, filter, trandate, startrange, endrange, override) {

        var returnRegardless = ((override == 1) ? true : false);

        var returnArr = [];

        var fields = "transaction.id, transaction.tranid, transaction.shipdate, transactionshippingaddress.addrtext, transactionshippingaddress.addr1, transactionshippingaddress.addr2, transactionshippingaddress.addressee, transactionshippingaddress.city, transactionshippingaddress.country, transactionshippingaddress.state, transactionshippingaddress.zip, transaction.otherrefnum, transaction.createddate, transactionline.linesequencenumber, item.id, item.itemid, transactionline.memo, ABS(transactionline.quantitycommitted), transactionline.location, transactionbillingaddress.addrtext, transactionbillingaddress.addr1, transactionbillingaddress.addr2, transactionbillingaddress.addressee, transactionbillingaddress.city, transactionbillingaddress.country, transactionbillingaddress.state, transactionbillingaddress.zip, customer.thirdpartyacct, customer.thirdpartycarrier";


        // var fields = "transaction.id, transaction.tranid, transaction.shipdate, transactionshippingaddress.addrtext, transactionshippingaddress.addr1, transactionshippingaddress.addr2, transactionshippingaddress.addressee, transactionshippingaddress.city, transactionshippingaddress.country, transactionshippingaddress.state, transactionshippingaddress.zip, transaction.otherrefnum, transaction.createddate, transactionline.linesequencenumber, item.id, item.itemid, transactionline.memo, ABS(transactionline.quantitycommitted), transactionline.location, transaction.billingaddress";

        if(type == 'list') {

            if(trandate) {
                var sqlQueryData = "SELECT " + fields + " FROM transaction INNER JOIN transactionline ON transactionline.transaction = transaction.id INNER JOIN transactionshippingaddress ON transactionshippingaddress.nkey = transaction.shippingaddress LEFT JOIN transactionbillingaddress ON transactionbillingaddress.nkey = transaction.billingaddress INNER JOIN item ON item.id = transactionline.item LEFT JOIN customer ON customer.id = transaction.entity WHERE transactionline.location = 17 AND transaction.type = 'SalesOrd' AND transaction.trandate = ? AND (ABS(transactionline.quantitycommitted) > 0) GROUP BY " + fields;
                var resultSet  = query.runSuiteQL({
                    query: sqlQueryData,
                    params: [trandate]
                });       
            } else {
                var sqlQueryData = "SELECT " + fields + " FROM transaction INNER JOIN transactionline ON transactionline.transaction = transaction.id INNER JOIN transactionshippingaddress ON transactionshippingaddress.nkey = transaction.shippingaddress LEFT JOIN transactionbillingaddress ON transactionbillingaddress.nkey = transaction.billingaddress INNER JOIN item ON item.id = transactionline.item LEFT JOIN customer ON customer.id = transaction.entity WHERE transactionline.location = 17 AND transaction.type = 'SalesOrd' AND (ABS(transactionline.quantitycommitted) > 0) GROUP BY " + fields;
                var resultSet  = query.runSuiteQL({
                    query: sqlQueryData
                });

            }
        } else {

            var sqlQueryData = "SELECT " + fields + " FROM transaction INNER JOIN transactionline ON transactionline.transaction = transaction.id INNER JOIN transactionshippingaddress ON transactionshippingaddress.nkey = transaction.shippingaddress LEFT JOIN transactionbillingaddress ON transactionbillingaddress.nkey = transaction.billingaddress INNER JOIN item ON item.id = transactionline.item LEFT JOIN customer ON customer.id = transaction.entity WHERE transactionline.location = 17 AND transaction.type = 'SalesOrd' AND transaction.id = ? AND (ABS(transactionline.quantitycommitted) > 0) GROUP BY " + fields;
            var resultSet  = query.runSuiteQL({
                query: sqlQueryData,
                params: [filter]
            });       
            
        }

        var results = resultSet.results;
        
        if(results.length < endrange) endrange = results.length;
        var orders = [];
        var skip = [];
        for(var i = startrange; i < endrange; i++ ){
            var internalid = results[i].values[0];
            if(skip.indexOf(internalid) !== -1) continue;
            if(orders.indexOf(internalid) !== -1) {
                
                salesOrderObj.items.push({
                    lineno: results[i].values[13],
                    sku_id: results[i].values[14],
                    sku: results[i].values[15],
                    description: results[i].values[16],
                    committed_quantity: results[i].values[17],
                });
                
            } else {

                var objRecord = search.lookupFields({
                    type: search.Type.SALES_ORDER,
                    id: internalid,
                    columns: ['customer.firstname', 'customer.lastname', 'customer.altname', 'shipphone', 'shipcarrier', 'shipmethod', 'terms', 'custbody_third_party_terms', 'custbody_received_by_taylored', 'custbody_ff_count']
                });

                // filter out reserves
                if(results[i].values[11] != 'DO NOT SHIP') {
                    if(objRecord.custbody_third_party_terms) {
                        
                        var shipping_carrier = results[i].values[28];
                        var shipping_method = ((objRecord.shipmethod.length > 0) ? objRecord.shipmethod[0].text : null);
                        var third_party_acct_no = results[i].values[27];
                        var terms = 'Third Party';

                    } else {
                        
                        var shipping_carrier = ((objRecord.shipcarrier.length > 0) ? objRecord.shipcarrier[0].text : null);
                        var shipping_method = ((objRecord.shipmethod.length > 0) ? objRecord.shipmethod[0].text : null);
                        var third_party_acct_no = null;
                        var terms = 'Prepaid';

                    }

                    var transaction_addtl = objRecord.custbody_ff_count ? '-' + String(objRecord.custbody_ff_count) : '';
                    transaction_addtl = transaction_addtl.replace('.0', '');
                    var transactionname = results[i].values[1]+transaction_addtl;
                    log.debug('transaction_addtl', transaction_addtl);
                    var salesOrderObj = { 
                        internalid: internalid,
                        tagged: objRecord.custbody_received_by_taylored,
                        transactionname: transactionname,
                        shipdate: results[i].values[2],
                        shipping_address: {
                            full_name: ((results[i].values[6]) ? results[i].values[6] : objRecord['customer.altname']),
                            first_name: objRecord['customer.firstname'],
                            last_name: objRecord['customer.lastname'],
                            shipping_address: results[i].values[3],
                            shipping_address_1: results[i].values[4],
                            shipping_address_2: results[i].values[5],
                            shipping_city: results[i].values[7],
                            shipping_country: results[i].values[8],
                            shipping_state: results[i].values[9],
                            shipping_zip: results[i].values[10],
                            shipping_phone: objRecord.shipphone
                        },
                        shipping_carrier: shipping_carrier,
                        shipping_method: shipping_method,
                        third_party_acct_no: third_party_acct_no,
                        terms: terms,
                        billing_address : {
                            full_name: objRecord['customer.altname'],
                            first_name: objRecord['customer.firstname'],
                            last_name: objRecord['customer.lastname'],
                            billing_address: results[i].values[19],
                            billing_address_1: results[i].values[20],
                            billing_address_2: results[i].values[21],
                            billing_city: results[i].values[23], 
                            billing_country: results[i].values[24],
                            billing_state: results[i].values[25],
                            billing_zip: results[i].values[26]
                        },
                        po_no: results[i].values[11],
                        datecreated: results[i].values[12],
                        items: []
                    };

                    salesOrderObj.items.push({
                        lineno: results[i].values[13],
                        sku_id: results[i].values[14],
                        sku: results[i].values[15],
                        description: results[i].values[16],
                        committed_quantity: results[i].values[17],
                    });
                    
                    returnArr.push(salesOrderObj);
                    orders.push(internalid);
                } else {
                    skip.push(internalid);
                }
            }
        }

        log.debug('orders', orders);

        var suiteletURL = url.resolveScript({
            scriptId: 'customscript_acs_sl_tag_orders',
            deploymentId: 'customdeploy_acs_sl_tag_orders_taylored',
            returnExternalUrl: true
        });

        https.post.promise({
            url: suiteletURL,
            body: {
                sales_order_ids: orders.join()
            }
        }).then(function(response){
            log.audit({
                title: 'Order Tagging Response',
                details: response.body
            });
        }).catch(function onRejected(reason) {
            log.debug({
                title: 'Invalid Request: ',
                details: reason
            });
        });

        return returnArr;

    }

    function _get(context) {

        var tranDate = ((context.trandate) ? context.trandate : '');
        var salesOrderId =  ((context.salesorderid) ? context.salesorderid : '');
        var startrange =  ((context.startrange) ? context.startrange : '');
        var endrange =  ((context.endrange) ? context.endrange : '');
        var override = ((context.override) ? context.override : '');
        var soList = [];
        
        try {

            if(salesOrderId) {

                soList = getSalesOrderList('specific', salesOrderId, tranDate, startrange, endrange, override);

            } else {

                soList = getSalesOrderList('list', 0, tranDate, startrange, endrange, override);

            }


            return {
                success: 1,
                count: soList.length,
                result: soList
            };

        } catch (e) {
            log.error('Error', e);
            return {
                success: 0,
                message: e.message
            }
        }
    }

    function _post(context) {
        try {

            if(context.hasOwnProperty('items_to_fulfill')){
                var itemsToFulfill = context.items_to_fulfill;
                var savedIds = [];

                var salesOrder = itemsToFulfill;

                // transform sales order
                var itemFfObj = record.transform({
                    fromType: record.Type.SALES_ORDER,
                    fromId: salesOrder.internalid,
                    toType: record.Type.ITEM_FULFILLMENT
                });

                var itemCount = itemFfObj.getLineCount({ sublistId: 'item' });

                for(var j = 0; j < itemCount; j++) {

                    itemFfObj.setSublistValue({
                        sublistId: 'item',
                        fieldId: 'itemreceive',
                        value: false,
                        line: j
                    });

                }
                var itemsObj = {};
                var items = salesOrder.items;

                for(var j = 0; j < items.length; j++) {
                    
                    if(!itemsObj.hasOwnProperty(items[j].sku_id)){
                        itemsObj[items[j].sku_id] = {
                            line: (parseInt(items[j].line) - 1),
                            sku_id: items[j].sku_id,
                            quantity: parseInt(items[j].quantity)
                        }
                    } else {
                        itemsObj[items[j].sku_id].quantity += parseInt(items[j].quantity);
                    }

                }

                for(var sku in itemsObj) {

                    for(var line = 0; line < itemCount; line++) {
                        var location = itemFfObj.getSublistValue({ sublistId: 'item', fieldId: 'location', line: line });
                        var item = itemFfObj.getSublistValue({ sublistId: 'item', fieldId: 'item', line: line });;
                        
                        if(item == sku && location == tayloredLocation) {

                            itemFfObj.setSublistValue({
                                sublistId: 'item',
                                fieldId: 'itemreceive',
                                value: true,
                                line: line
                            });
                            
                            itemFfObj.setSublistValue({
                                sublistId: 'item',
                                fieldId: 'quantity',
                                value: itemsObj[sku].quantity,
                                line: line
                            });
                        
                        }

                    }

                }

                
                var itemFfId = itemFfObj.save();

                var itemFfObj = record.load({
                    type: record.Type.ITEM_FULFILLMENT,
                    id: itemFfId
                });

                itemFfObj.setValue({
                    fieldId: 'shipmethod',
                    value: ''
                });

                itemFfObj.setValue({
                    fieldId: 'shipstatus',
                    value: 'C'
                });

                packageCount = itemFfObj.getLineCount({ sublistId: 'package' });

                for(var j = 0; j < packageCount; j++) {
                    itemFfObj.removeLine({
                        sublistId: 'package',
                        line: j
                    });
                }
                
                if(salesOrder.hasOwnProperty('tracking_numbers')){
                    var trackingNumbers = salesOrder.tracking_numbers;
                    var packageWeight = salesOrder.package_weight;

                    var shipMethod = salesOrder.shipmethod;

                    for(var x = 0; x < trackingNumbers.length; x++) {
                        itemFfObj.insertLine({
                            sublistId: 'package',
                            line: x,
                        });
                        
                        itemFfObj.setSublistValue({
                            sublistId: 'package',
                            fieldId: 'packagedescr',
                            value: shipMethod,
                            line: x
                        });

                        itemFfObj.setSublistValue({
                            sublistId: 'package',
                            fieldId: 'packageweight',
                            value: parseInt(packageWeight[x]),
                            line: x
                        });

                        itemFfObj.setSublistValue({
                            sublistId: 'package',
                            fieldId: 'packagetrackingnumber',
                            value: trackingNumbers[x],
                            line: x
                        });

                    }
                }

                var itemFfId = itemFfObj.save();
                savedIds.push(itemFfId);

                var itemFfCount = search.lookupFields({
                    type: search.Type.SALES_ORDER,
                    id: salesOrder.internalid,
                    columns: ['custbody_item_ff_count']
                });

                var itemFfCount = ((itemFfCount) ? parseInt(itemFfCount)++ : 1);

                record.submitFields({
                    type: record.Type.SALES_ORDER,  
                    id: salesOrder.internalid,
                    values: {
                        custbody_item_ff_count: itemFfCount
                    },
                    options: {
                        enableSourcing: false,
                        ignoreMandatoryFields : true
                    }
                });

                return {
                    success: 1,
                    count: savedIds.length,
                    result: savedIds
                }
            }
                
        } catch (e){
            log.error("Error", e);
            return {
                success: 0,
                message: e.message
            }
        }
    }

    function _put(context) {
        
    }

    function _delete(context) {
        
    }

    return {
        get: _get,
        post: _post,
        put: _put,
        delete: _delete
    }
});
