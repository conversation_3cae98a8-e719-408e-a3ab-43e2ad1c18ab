/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/record', 'N/search'], function(record, search) {

    function getBin(itemId, location) {
        var itemSearchObj = search.create({
            type: "item",
            filters:
            [
               ["binonhand.quantityavailable","greaterthan","0"], 
               "AND", 
               ["internalidnumber","equalto",itemId], 
               "AND", 
               ["binonhand.location","anyof",location]
            ],
            columns:
            [
               search.createColumn({
                  name: "formulatext",
                  formula: "{binonhand.binnumber} || '(' || {binonhand.quantityavailable} || ')'",
                  label: "Formula (Text)"
               })
            ]
         });
         var bin = '';
         itemSearchObj.run().each(function(result){
            bin = result.getValue({ name: "formulatext" });
            return false;
         });

         return bin;
    }

    function onRequest(context) {
        
        var soid = context.request.parameters.soid;
        
        var recObj = record.load({ 
            type: record.Type.SALES_ORDER,
            id: soid
        });

        var transactionSearchObj = search.create({
            type: "transaction",
            filters:
            [
               ["internalidnumber","equalto",soid], 
               "AND", 
               ["mainline","is","F"], 
               "AND", 
               ["custcollinenum","isnotempty",""]
            ],
            columns:
            [
               search.createColumn({
                  name: "custcollinenum",
                  sort: search.Sort.ASC,
                  label: "Line #"
               }),
               search.createColumn({name: "item", label: "Item"}),
               search.createColumn({name: "quantitypicked", label: "Quantity Picked"}),
               search.createColumn({name: "quantitypacked", label: "Quantity Shipped not Invoiced"}),
               search.createColumn({name: "location", label: "Location"})
            ]
         });

         var items = [];

         transactionSearchObj.run().each(function(result){
            
            var line = recObj.findSublistLineWithValue({
                sublistId: 'item',
                fieldId: 'custcollinenum',
                value: result.getValue({ name: 'custcollinenum' })
            });

            var quantitycommitted = recObj.getSublistValue({ sublistId: 'item', fieldId: 'quantitycommitted', line: line });
            var custcol3 = recObj.getSublistValue({ sublistId: 'item', fieldId: 'custcol3', line: line });
            var description = recObj.getSublistValue({ sublistId: 'item', fieldId: 'description', line: line });
            var custbodyrepbrand = recObj.getSublistValue({ sublistId: 'item', fieldId: 'custcol9', line: line });
            var inventorydetail = ((quantitycommitted) ? getBin(result.getValue({ name: 'item' }), result.getValue({ name: 'location' })) : "");
            var custcol_supplyorder = recObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_supplyorder', line: line });
            var quantitypicked = result.getValue({ name: 'quantitypicked' });
            var quantitypacked = result.getValue({ name: 'quantitypacked' });
            var quantityavailable = recObj.getSublistValue({ sublistId: 'item', fieldId: 'quantityavailable', line: line });
            var units = recObj.getSublistText({ sublistId: 'item', fieldId: 'units', line: line });
            var quantitytobefulfilled = recObj.getSublistValue({ sublistId: 'item', fieldId: 'quantitytobefulfilled', line: line });
            var quantity = recObj.getSublistValue({ sublistId: 'item', fieldId: 'quantity', line: line });

            var item = {
                custcol3: ((custcol3) ? custcol3 : "") ,
                description: ((description) ? description : "") ,
                custbodyrepbrand: ((custbodyrepbrand) ? custbodyrepbrand : "") ,
                inventorydetail: ((inventorydetail) ? inventorydetail : "") ,
                custcol_supplyorder: ((custcol_supplyorder) ? custcol_supplyorder : "") ,
                quantitypacked: ((quantitypacked) ? quantitypacked : "") ,
                quantitypicked: ((quantitypicked) ? quantitypicked : "") ,
                quantityavailable: ((quantityavailable) ? quantityavailable : "") ,
                units: ((units) ? units : "") ,
                quantitycommitted: ((quantitycommitted) ? quantitycommitted : "") ,
                quantitytobefulfilled: ((quantitytobefulfilled) ? quantitytobefulfilled : "") ,
                quantity: ((quantity) ? quantity : "") ,
            }

            items.push(item);

            return true;
         });

         log.debug("items", items);

         var items = [
            {a: 'a', b: 'b'},
            {c: 'c', d: 'd'},
         ];

        var returnStr = "<#assign custitems =" + JSON.stringify(items) + " />"; 

        context.response.write({
            output: returnStr
        });

    }

    return {
        onRequest: onRequest
    }
});
