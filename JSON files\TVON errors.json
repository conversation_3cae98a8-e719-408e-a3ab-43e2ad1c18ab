{"type": "error.SuiteScriptError", "name": "INVALID_SEARCH", "message": "That search or mass update does not exist.", "stack": ["load(N/searchObject)", "afterSubmit(/SuiteScripts/ACS_UE_Copy_Parent_Changes.js:197)"], "cause": {"type": "internal error", "code": "INVALID_SEARCH", "details": "That search or mass update does not exist.", "userEvent": "aftersubmit", "stackTrace": ["load(N/searchObject)", "afterSubmit(/SuiteScripts/ACS_UE_Copy_Parent_Changes.js:197)"], "notifyOff": false}, "id": "", "notifyOff": false, "userFacing": false}