/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord'], function(currentRecord) {

    function fieldChanged(context) {
        if(context.fieldId == 'custbody_customer'){
            var recObj = context.currentRecord;
            var fieldValue = recObj.getValue({ fieldId : 'custbody_customer' });
            var sublists = ['item', 'expense'];
            for(var i = 0; i < sublists.length; i++) {
                var sublist = sublists[i];
                var lineCount = recObj.getLineCount({ sublistId : sublist });
                for(var x = 0; x < lineCount; x++){
                    recObj.selectLine({
                        sublistId: sublist,
                        line: x
                    });
                    recObj.setCurrentSublistValue({
                        sublistId: sublist,
                        fieldId: 'custcol_customer_project',
                        value: fieldValue,
                        ignoreFieldChange: true
                    });
                    recObj.commitLine({
                        sublistId: sublist
                    });
                }
            }
        } else if(context.sublistId){
            var recObj = context.currentRecord;
            var fieldValue = recObj.getValue({ fieldId : 'custbody_customer' });
            var sublistCustCol = recObj.getCurrentSublistValue({
                sublistId: context.sublistId,
                fieldId: 'custcol_customer_project'
            });
            // if fieldValue has value and sublistCustCol is empty, proceed to populate custcol_customer_project
            if((fieldValue && !sublistCustCol) || (fieldValue != sublistCustCol)){
                recObj.setCurrentSublistValue({
                    sublistId: context.sublistId,
                    fieldId: 'custcol_customer_project',
                    value: fieldValue,
                    ignoreFieldChange: true
                });
            }
        }
    }

    function lineInit(context) {
        var recObj = context.currentRecord;
        var fieldValue = recObj.getValue({ fieldId : 'custbody_customer' });
        if(fieldValue){
            recObj.setCurrentSublistValue({
                sublistId: context.sublistId,
                fieldId: 'custcol_customer_project',
                value: fieldValue,
                ignoreFieldChange: true
            });
        }
    }

    return {
        fieldChanged: fieldChanged,
        lineInit: lineInit
    }
});
