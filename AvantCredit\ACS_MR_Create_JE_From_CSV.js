/**
 *@NApiVersion 2.1
 *@NScriptType MapReduceScript
 */
define(['N/record'], function(record) {

    function getInputData() {
        
    }

    function map(context) {
        
        var fileId = context.key;
        
        try {
            context.write({
                key: fileId,
                value: {}
            });
    
        } catch (errorObj) {
            log.debug({
                title: 'MAP: Error',
                details: errorObj
            });
        }

    }

    function reduce(context) {
        
        var fileId = context.key;

        var accountSearchObj = search.load({
            id: 'customsearch_acs_accounts'
        });

        var accounts = {};
        accountSearchObj.run().each((result) => {
            var accountNumber = result.getValue({ name: 'number' });
            var accountId = result.getValue({ name: 'internalid' });

            accounts[accountNumber] = accountId;

            return true;
        });
    
        // LOAD FILE OBJECT
        var origFileObj = file.load({
            id: fileId
        });

        var iterator = origFileObj.lines.iterator();
        
        // THE FIRST ROW IS HEADER, SO, SKIP.
        iterator.each(() => {
            return false;
        });

        // LOOP THROUGH EACH LINE OF THE FILE
        // 0-Entry No, 1-Date, 2-Account, 3-Debit, 4-Credit, 5-Memo, 6-Department, 7-Product, Types, 8-Subsidiary

        iterator.each((line) => {

            var lineValues = line.value.split(',');
            var date = lineValues[0];
            var account = lineValues[2].split(' ')[0];
            var debit = lineValues[3];
            var credit = lineValues[4];
            var memo = lineValues[4];

        });
        
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
