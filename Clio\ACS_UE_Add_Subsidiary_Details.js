/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search'], function(record, search) {

    function beforeLoad(context) {
        var formObj = context.form;        
        //addrtext
        //custrecord_clio_subsidiary_tax_id
        //custrecordclio_ap_email
        //custrecord_clio_subsidiary_telephone
        //custrecord_clio_subsidiary_fax

        var recordLookUp = search.lookupFields({
            type: search.Type.SUBSIDIARY,
            id: recordObj.id,
            columns: [
                'custbody_gridoe_productgrpdata'
            ]
        });
                
        var poField = formObj.addField({
            id : 'custpage_po_grid_template_data',
            type : serverWidget.FieldType.LONGTEXT,
            label : 'po grid template data'
        });
        
        poField.defaultValue = "4562044 (Legacy: 0T806) VAT:GB303097824";
        poField.updateDisplayType({
            displayType : serverWidget.FieldDisplayType.HIDDEN
        });
    }

    return {
        beforeLoad: beforeLoad,
    }
});
