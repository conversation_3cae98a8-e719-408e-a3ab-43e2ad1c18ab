/**
 * @NScriptType workflowactionscript
 * @NAPIVersion 2.0
 *
 *
 */

define(["N/http", "N/file", "N/record", "N/render", "N/redirect", "N/runtime", "N/search", "N/error"],
    function(http, file, record, render, redirect, runtime, search, error) {
        var NSUtils = {
            inArray: function(stValue, arrValue) {
                for (var i = arrValue.length - 1; i >= 0; i--) {
                    if (stValue == arrValue[i]) {
                        break;
                    }
                }
                return (i > -1);
            }
        };
        function onAction(context) {
            var stMethodName = 'onAction';
            try {
                var myRec = context.newRecord;
                var recId = myRec.id;
                log.debug('record id', recId);

                var xmlTemplateFile = file.load('SuiteScripts/nsts_so_order_confirmation.xml');
                var renderer = render.create();
                renderer.templateContent = xmlTemplateFile.getContents();

                var recObj = record.load({
                    type: record.Type.SALES_ORDER,
                    id: recId
                })
                
                renderer.addRecord('record', recObj);

                log.debug('companyInformation', record.load({
                    type: record.Type.SUBSIDIARY,
                    id: recObj.getValue({ fieldId: 'subsidiary' })
                }));

                renderer.addRecord('companyInformation', record.load({
                    type: record.Type.SUBSIDIARY,
                    id: recObj.getValue({ fieldId: 'subsidiary' })
                }));


                var mySearch = search.load({
                    id: 'customsearch_nsts_transaction_search'
                });

                var myFilter = search.createFilter({
                    name: 'internalid',
                    operator: search.Operator.IS,
                    values: [recId]
                });

                var myFilter2 = search.createFilter({
                    name: 'recordtype',
                    operator: search.Operator.IS,
                    values: 'salesorder'
                });

                mySearch.filters.push(myFilter);
                mySearch.filters.push(myFilter2);
                // mySearch.columns.push(myColumn);
                var mySearchResults = mySearch.run().getRange(0, 1000);
                log.debug('my search results', mySearchResults);

                var sizeSearch = search.load({
                    id: 'customsearch_nsts_size_rank_list_search'
                });
                var sizeSearchResults = sizeSearch.run().getRange(0, 1000);
                log.debug('sizeSearchResults', sizeSearchResults);

                var sizeList = [];

                sizeSearchResults.forEach(function(result) {
                    sizeList.push(result.getText('custrecordfog_size_list'))
                });
                log.debug('sizeList', sizeList);

                var arrSizes = [];
                var arrItem = [];
                var tempStr = {};
                var colorDes = '';
                var itemName = '';
                var noSizeQty = 0;
                var diffColor = [];
                var divStr = {};
                var divArr = [];
                var sortedSizes = [];
                var missingSizes = '';
                var diffSizes = [];
                
                mySearchResults.forEach(function(result4) {
                    var temp = result4.getText({
                        name: 'custitem_psgss_product_size',
                        join: 'CUSTCOLFOG_STYLE'
                    });
                    temp = temp.split(",");
                    // log.debug('temp', temp);
                    for (var i = 0; i < temp.length; i++) {
                        if (!NSUtils.inArray(temp[i], diffSizes)) {
                            diffSizes.push(temp[i]);
                        }
                    }
                    log.debug('diffSizes', diffSizes);
                });

                var lastDiv = '';
                mySearchResults.forEach(function(result3) {
                    var currentDiv = result3.getValue('formulatext');
                    if (lastDiv != currentDiv) {
                        var lastStyle = 'z';
                        mySearchResults.forEach(function(result) {
                            var currentStyle = result.getText('custcolfog_style');
                            if (isEmpty(currentStyle)) {
                                currentStyle = result.getText('item');
                            }
                            // log.debug('currentStyle', currentStyle);
                            if ((currentDiv == result.getValue('formulatext')) && (lastStyle != currentStyle)) {
                                var lastColor = null;
                                diffColor = [];
                                mySearchResults.forEach(function(result1) {
                                    var currentColor = result1.getText('custcol_product_color');
                                    // log.debug('currentColor', currentColor);
                                    // log.debug('currentStyle', currentStyle);
                                    // log.debug('lastColor', lastColor);
                                    var temp = result1.getText('custcolfog_style');
                                    if(isEmpty(temp)) {
                                        temp = result1.getText('item');
                                    }
                                    if ((currentDiv == result1.getValue('formulatext')) && (currentStyle == temp) && (lastColor != result1.getText('custcol_product_color'))) {
                                        arrSizes = [];

                                        sortedSizes = [];

                                        for (var i = 0; i < sizeList.length; i++) {
                                            for (var j = 0; j < diffSizes.length; j++) {
                                                if (sizeList[i] == diffSizes[j]) {
                                                    sortedSizes.push(diffSizes[j]);
                                                }
                                            }
                                        }
                                        log.debug('sortedSizes', sortedSizes);

                                        for (var i = 0; i < sortedSizes.length; i++) {
                                            arrSizes.push({size: sortedSizes[i], quantity: 0});
                                        }

                                        mySearchResults.forEach(function(result2) {
                                            var temp2 = result2.getText('custcolfog_style');
                                            if(isEmpty(temp2)) {
                                                temp2 = result2.getText('item');
                                            }
                                            if ((currentDiv == result2.getValue('formulatext')) && (currentStyle == temp2) && (currentColor == result2.getText('custcol_product_color'))) {
                                                if (sortedSizes.length == 0) {
                                                    noSizeQty = result2.getValue('quantity');
                                                }
                                                //UPDATE SIZE
                                                for (var i = 0; i < sortedSizes.length; i++) {
                                                    if (sortedSizes[i] == result2.getText('custcol_product_size')) {
                                                        arrSizes[i].quantity = result2.getValue('quantity');
                                                    }
                                                    if (isEmpty(result2.getText('custcol_product_size'))) {
                                                        noSizeQty = result2.getValue('quantity');
                                                    }
                                                }
                                                if ((result2.getText('custcol_product_size') != "") 
                                                    && !NSUtils.inArray((result2.getText('custcol_product_size')), sizeList)) {
                                                    if (missingSizes == '') {
                                                        missingSizes = result2.getText('custcol_product_size');
                                                    }
                                                    else {
                                                        missingSizes = missingSizes + ', ' + result2.getText('custcol_product_size');
                                                    }
                                                }
                                            }
                                        });
                                        // log.debug('arrSizes', arrSizes);
                                        // log.debug('missingSizes', missingSizes);

                                        colorDes = result1.getValue({
                                            name: 'custitem_psgss_product_color_desc',
                                            join: 'item'
                                        });
                                        // log.debug('colorDes', colorDes);

                                        if (colorDes == null) {
                                            colorDes = '';
                                        }

                                        itemName = result1.getText('custcolfog_style');

                                        if (isEmpty(itemName)) {
                                            itemName = result1.getText('item');
                                        }

                                        // log.debug('diffColor', diffColor);
                                        if (!NSUtils.inArray(result1.getText('custcol_product_color'), diffColor)) {
                                            tempStr = {
                                                name: itemName,
                                                colordes: colorDes,
                                                color: result1.getText('custcol_product_color'),
                                                rate: result1.getValue('rate'),
                                                allsizes: arrSizes,
                                                quantity: noSizeQty,
                                                sellPrd: result1.getText('custcolfog_selling_period_line'),
                                                fabric: result1.getValue('custcolfog_fabric_line'),
                                                composition: result1.getValue('custcolfog_content'),
                                                group: result1.getText('custcolfog_group'),
                                                msrp: result1.getValue('custcolfog_msrp')
                                            }
                                            arrItem.push(tempStr);
                                            diffColor.push(result1.getText('custcol_product_color'));

                                        }
                                        log.debug('arrItem', arrItem);

                                        noSizeQty = 0;

                                    }
                                    lastColor = result1.getText('custcol_product_color');
                                });
                            }
                            lastStyle = itemName;
                        });
                        divStr = {
                            sizes: sortedSizes,
                            item: arrItem
                        };
                        divArr.push(divStr);
                        log.debug('divArr', divArr);
                        tempStr = {};
                        arrItem = [];
                        sortedSizes = [];
                    }
                    lastDiv = result3.getValue('formulatext');
                });


                renderer.addCustomDataSource({
                    format: render.DataSource.OBJECT,
                    alias: "JSON",
                    data: {
                        division: divArr,
                        missingsizes: missingSizes
                    }
                });

                var locObj = record.load({
                    type: record.Type.LOCATION,
                    id: myRec.getValue({ fieldId: 'location' })
                });

                var locSubObj = locObj.getSubrecord({ fieldId: 'mainaddress' });
                var country = locSubObj.getText({ fieldId: 'country' });

                renderer.addCustomDataSource({
                    format: render.DataSource.OBJECT,
                    alias: "location",
                    data: {
                        country: country
                    }
                });

                var invoicePdf = renderer.renderAsPdf();
                log.debug('invoicePdf', invoicePdf);

                invoicePdf.folder = 1189;
                invoicePdf.name = recId + "_order_confirmation";
                var fileId = invoicePdf.save();
                var fileIdReloaded = file.load({
                    id: fileId
                });

                log.debug('invoicePdf url', fileIdReloaded.url);

                redirect.redirect({
                    url: fileIdReloaded.url
                });

                log.debug('fileId', fileId);

            } 
            catch(error){
                var stError = (error.message !== undefined) ? error.name + ' : ' + error.message : error.toString();
                log.error(stMethodName, 'Catch : ' + stError);
                throw stError;
            }
        }

        function isEmpty(value){
            return value === '' || value === null || value === undefined;
        }

        return {
            onAction : onAction
        };
    });
