/**
 * Copyright (c) 1998-2017 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of NetSuite, Inc. ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with NetSuite.
 */

/**
 * Module Description : Script to copy budget data to custom Budget Copy record using web services.
 * 
 * Version    Date				Author				Remarks
 * 1.00       Jan 27, 2018		vsankaran			Initial version.
 */

/**
 * @NApiVersion 2.x
 * @NScriptType MapReduceScript
 * @NModuleScope SameAccount
 */
define(function(require)
{
	var ScriptFunc = (typeof ScriptFunc === 'undefined') ? {} : ScriptFunc;
	var email = require('N/email');
	var error = require('N/error');
	var format = require('N/format');
	var search = require('N/search');
	var record = require('N/record');
	var runtime = require('N/runtime');
	var render = require('N/render');
    var https = require('N/https');
    var xml = require('N/xml');

    // Start Crypto JS 
    /*
    CryptoJS v3.1.2
    code.google.com/p/crypto-js
    (c) 2009-2013 by Jeff Mott. All rights reserved.
    code.google.com/p/crypto-js/wiki/License
    */
    var CryptoJS = CryptoJS || function(h, s) {
        var f = {},
            g = f.lib = {},
            q = function() {},
            m = g.Base = {
                extend: function(a) {
                    q.prototype = this;
                    var c = new q;
                    a && c.mixIn(a);
                    c.hasOwnProperty("init") || (c.init = function() {
                        c.$super.init.apply(this, arguments)
                    });
                    c.init.prototype = c;
                    c.$super = this;
                    return c
                },
                create: function() {
                    var a = this.extend();
                    a.init.apply(a, arguments);
                    return a
                },
                init: function() {},
                mixIn: function(a) {
                    for (var c in a) a.hasOwnProperty(c) && (this[c] = a[c]);
                    a.hasOwnProperty("toString") && (this.toString = a.toString)
                },
                clone: function() {
                    return this.init.prototype.extend(this)
                }
            },
            r = g.WordArray = m.extend({
                init: function(a, c) {
                    a = this.words = a || [];
                    this.sigBytes = c != s ? c : 4 * a.length
                },
                toString: function(a) {
                    return (a || k).stringify(this)
                },
                concat: function(a) {
                    var c = this.words,
                        d = a.words,
                        b = this.sigBytes;
                    a = a.sigBytes;
                    this.clamp();
                    if (b % 4)
                        for (var e = 0; e < a; e++) c[b + e >>> 2] |= (d[e >>> 2] >>> 24 - 8 * (e % 4) & 255) << 24 - 8 * ((b + e) % 4);
                    else if (65535 < d.length)
                        for (e = 0; e < a; e += 4) c[b + e >>> 2] = d[e >>> 2];
                    else c.push.apply(c, d);
                    this.sigBytes += a;
                    return this
                },
                clamp: function() {
                    var a = this.words,
                        c = this.sigBytes;
                    a[c >>> 2] &= 4294967295 <<
                        32 - 8 * (c % 4);
                    a.length = h.ceil(c / 4)
                },
                clone: function() {
                    var a = m.clone.call(this);
                    a.words = this.words.slice(0);
                    return a
                },
                random: function(a) {
                    for (var c = [], d = 0; d < a; d += 4) c.push(4294967296 * h.random() | 0);
                    return new r.init(c, a)
                }
            }),
            l = f.enc = {},
            k = l.Hex = {
                stringify: function(a) {
                    var c = a.words;
                    a = a.sigBytes;
                    for (var d = [], b = 0; b < a; b++) {
                        var e = c[b >>> 2] >>> 24 - 8 * (b % 4) & 255;
                        d.push((e >>> 4).toString(16));
                        d.push((e & 15).toString(16))
                    }
                    return d.join("")
                },
                parse: function(a) {
                    for (var c = a.length, d = [], b = 0; b < c; b += 2) d[b >>> 3] |= parseInt(a.substr(b,
                        2), 16) << 24 - 4 * (b % 8);
                    return new r.init(d, c / 2)
                }
            },
            n = l.Latin1 = {
                stringify: function(a) {
                    var c = a.words;
                    a = a.sigBytes;
                    for (var d = [], b = 0; b < a; b++) d.push(String.fromCharCode(c[b >>> 2] >>> 24 - 8 * (b % 4) & 255));
                    return d.join("")
                },
                parse: function(a) {
                    for (var c = a.length, d = [], b = 0; b < c; b++) d[b >>> 2] |= (a.charCodeAt(b) & 255) << 24 - 8 * (b % 4);
                    return new r.init(d, c)
                }
            },
            j = l.Utf8 = {
                stringify: function(a) {
                    try {
                        return decodeURIComponent(escape(n.stringify(a)))
                    } catch (c) {
                        throw Error("Malformed UTF-8 data");
                    }
                },
                parse: function(a) {
                    return n.parse(unescape(encodeURIComponent(a)))
                }
            },
            u = g.BufferedBlockAlgorithm = m.extend({
                reset: function() {
                    this._data = new r.init;
                    this._nDataBytes = 0
                },
                _append: function(a) {
                    "string" == typeof a && (a = j.parse(a));
                    this._data.concat(a);
                    this._nDataBytes += a.sigBytes
                },
                _process: function(a) {
                    var c = this._data,
                        d = c.words,
                        b = c.sigBytes,
                        e = this.blockSize,
                        f = b / (4 * e),
                        f = a ? h.ceil(f) : h.max((f | 0) - this._minBufferSize, 0);
                    a = f * e;
                    b = h.min(4 * a, b);
                    if (a) {
                        for (var g = 0; g < a; g += e) this._doProcessBlock(d, g);
                        g = d.splice(0, a);
                        c.sigBytes -= b
                    }
                    return new r.init(g, b)
                },
                clone: function() {
                    var a = m.clone.call(this);
                    a._data = this._data.clone();
                    return a
                },
                _minBufferSize: 0
            });
        g.Hasher = u.extend({
            cfg: m.extend(),
            init: function(a) {
                this.cfg = this.cfg.extend(a);
                this.reset()
            },
            reset: function() {
                u.reset.call(this);
                this._doReset()
            },
            update: function(a) {
                this._append(a);
                this._process();
                return this
            },
            finalize: function(a) {
                a && this._append(a);
                return this._doFinalize()
            },
            blockSize: 16,
            _createHelper: function(a) {
                return function(c, d) {
                    return (new a.init(d)).finalize(c)
                }
            },
            _createHmacHelper: function(a) {
                return function(c, d) {
                    return (new t.HMAC.init(a,
                        d)).finalize(c)
                }
            }
        });
        var t = f.algo = {};
        return f
    }(Math);
    (function(h) {
        for (var s = CryptoJS, f = s.lib, g = f.WordArray, q = f.Hasher, f = s.algo, m = [], r = [], l = function(a) {
                return 4294967296 * (a - (a | 0)) | 0
            }, k = 2, n = 0; 64 > n;) {
            var j;
            a: {
                j = k;
                for (var u = h.sqrt(j), t = 2; t <= u; t++)
                    if (!(j % t)) {
                        j = !1;
                        break a
                    }
                j = !0
            }
            j && (8 > n && (m[n] = l(h.pow(k, 0.5))), r[n] = l(h.pow(k, 1 / 3)), n++);
            k++
        }
        var a = [],
            f = f.SHA256 = q.extend({
                _doReset: function() {
                    this._hash = new g.init(m.slice(0))
                },
                _doProcessBlock: function(c, d) {
                    for (var b = this._hash.words, e = b[0], f = b[1], g = b[2], j = b[3], h = b[4], m = b[5], n = b[6], q = b[7], p = 0; 64 > p; p++) {
                        if (16 > p) a[p] =
                            c[d + p] | 0;
                        else {
                            var k = a[p - 15],
                                l = a[p - 2];
                            a[p] = ((k << 25 | k >>> 7) ^ (k << 14 | k >>> 18) ^ k >>> 3) + a[p - 7] + ((l << 15 | l >>> 17) ^ (l << 13 | l >>> 19) ^ l >>> 10) + a[p - 16]
                        }
                        k = q + ((h << 26 | h >>> 6) ^ (h << 21 | h >>> 11) ^ (h << 7 | h >>> 25)) + (h & m ^ ~h & n) + r[p] + a[p];
                        l = ((e << 30 | e >>> 2) ^ (e << 19 | e >>> 13) ^ (e << 10 | e >>> 22)) + (e & f ^ e & g ^ f & g);
                        q = n;
                        n = m;
                        m = h;
                        h = j + k | 0;
                        j = g;
                        g = f;
                        f = e;
                        e = k + l | 0
                    }
                    b[0] = b[0] + e | 0;
                    b[1] = b[1] + f | 0;
                    b[2] = b[2] + g | 0;
                    b[3] = b[3] + j | 0;
                    b[4] = b[4] + h | 0;
                    b[5] = b[5] + m | 0;
                    b[6] = b[6] + n | 0;
                    b[7] = b[7] + q | 0
                },
                _doFinalize: function() {
                    var a = this._data,
                        d = a.words,
                        b = 8 * this._nDataBytes,
                        e = 8 * a.sigBytes;
                    d[e >>> 5] |= 128 << 24 - e % 32;
                    d[(e + 64 >>> 9 << 4) + 14] = h.floor(b / 4294967296);
                    d[(e + 64 >>> 9 << 4) + 15] = b;
                    a.sigBytes = 4 * d.length;
                    this._process();
                    return this._hash
                },
                clone: function() {
                    var a = q.clone.call(this);
                    a._hash = this._hash.clone();
                    return a
                }
            });
        s.SHA256 = q._createHelper(f);
        s.HmacSHA256 = q._createHmacHelper(f)
    })(Math);
    (function() {
        var h = CryptoJS,
            s = h.enc.Utf8;
        h.algo.HMAC = h.lib.Base.extend({
            init: function(f, g) {
                f = this._hasher = new f.init;
                "string" == typeof g && (g = s.parse(g));
                var h = f.blockSize,
                    m = 4 * h;
                g.sigBytes > m && (g = f.finalize(g));
                g.clamp();
                for (var r = this._oKey = g.clone(), l = this._iKey = g.clone(), k = r.words, n = l.words, j = 0; j < h; j++) k[j] ^= 1549556828, n[j] ^= 909522486;
                r.sigBytes = l.sigBytes = m;
                this.reset()
            },
            reset: function() {
                var f = this._hasher;
                f.reset();
                f.update(this._iKey)
            },
            update: function(f) {
                this._hasher.update(f);
                return this
            },
            finalize: function(f) {
                var g =
                    this._hasher;
                f = g.finalize(f);
                g.reset();
                return g.finalize(this._oKey.clone().concat(f))
            }
        })
    })();

    /*
    CryptoJS v3.1.2
    code.google.com/p/crypto-js
    (c) 2009-2013 by Jeff Mott. All rights reserved.
    code.google.com/p/crypto-js/wiki/License
    */
    var CryptoJS = CryptoJS || function(h, r) {
        var k = {},
            l = k.lib = {},
            n = function() {},
            f = l.Base = {
                extend: function(a) {
                    n.prototype = this;
                    var b = new n;
                    a && b.mixIn(a);
                    b.hasOwnProperty("init") || (b.init = function() {
                        b.$super.init.apply(this, arguments)
                    });
                    b.init.prototype = b;
                    b.$super = this;
                    return b
                },
                create: function() {
                    var a = this.extend();
                    a.init.apply(a, arguments);
                    return a
                },
                init: function() {},
                mixIn: function(a) {
                    for (var b in a) a.hasOwnProperty(b) && (this[b] = a[b]);
                    a.hasOwnProperty("toString") && (this.toString = a.toString)
                },
                clone: function() {
                    return this.init.prototype.extend(this)
                }
            },
            j = l.WordArray = f.extend({
                init: function(a, b) {
                    a = this.words = a || [];
                    this.sigBytes = b != r ? b : 4 * a.length
                },
                toString: function(a) {
                    return (a || s).stringify(this)
                },
                concat: function(a) {
                    var b = this.words,
                        d = a.words,
                        c = this.sigBytes;
                    a = a.sigBytes;
                    this.clamp();
                    if (c % 4)
                        for (var e = 0; e < a; e++) b[c + e >>> 2] |= (d[e >>> 2] >>> 24 - 8 * (e % 4) & 255) << 24 - 8 * ((c + e) % 4);
                    else if (65535 < d.length)
                        for (e = 0; e < a; e += 4) b[c + e >>> 2] = d[e >>> 2];
                    else b.push.apply(b, d);
                    this.sigBytes += a;
                    return this
                },
                clamp: function() {
                    var a = this.words,
                        b = this.sigBytes;
                    a[b >>> 2] &= 4294967295 <<
                        32 - 8 * (b % 4);
                    a.length = h.ceil(b / 4)
                },
                clone: function() {
                    var a = f.clone.call(this);
                    a.words = this.words.slice(0);
                    return a
                },
                random: function(a) {
                    for (var b = [], d = 0; d < a; d += 4) b.push(4294967296 * h.random() | 0);
                    return new j.init(b, a)
                }
            }),
            m = k.enc = {},
            s = m.Hex = {
                stringify: function(a) {
                    var b = a.words;
                    a = a.sigBytes;
                    for (var d = [], c = 0; c < a; c++) {
                        var e = b[c >>> 2] >>> 24 - 8 * (c % 4) & 255;
                        d.push((e >>> 4).toString(16));
                        d.push((e & 15).toString(16))
                    }
                    return d.join("")
                },
                parse: function(a) {
                    for (var b = a.length, d = [], c = 0; c < b; c += 2) d[c >>> 3] |= parseInt(a.substr(c,
                        2), 16) << 24 - 4 * (c % 8);
                    return new j.init(d, b / 2)
                }
            },
            p = m.Latin1 = {
                stringify: function(a) {
                    var b = a.words;
                    a = a.sigBytes;
                    for (var d = [], c = 0; c < a; c++) d.push(String.fromCharCode(b[c >>> 2] >>> 24 - 8 * (c % 4) & 255));
                    return d.join("")
                },
                parse: function(a) {
                    for (var b = a.length, d = [], c = 0; c < b; c++) d[c >>> 2] |= (a.charCodeAt(c) & 255) << 24 - 8 * (c % 4);
                    return new j.init(d, b)
                }
            },
            t = m.Utf8 = {
                stringify: function(a) {
                    try {
                        return decodeURIComponent(escape(p.stringify(a)))
                    } catch (b) {
                        throw Error("Malformed UTF-8 data");
                    }
                },
                parse: function(a) {
                    return p.parse(unescape(encodeURIComponent(a)))
                }
            },
            q = l.BufferedBlockAlgorithm = f.extend({
                reset: function() {
                    this._data = new j.init;
                    this._nDataBytes = 0
                },
                _append: function(a) {
                    "string" == typeof a && (a = t.parse(a));
                    this._data.concat(a);
                    this._nDataBytes += a.sigBytes
                },
                _process: function(a) {
                    var b = this._data,
                        d = b.words,
                        c = b.sigBytes,
                        e = this.blockSize,
                        f = c / (4 * e),
                        f = a ? h.ceil(f) : h.max((f | 0) - this._minBufferSize, 0);
                    a = f * e;
                    c = h.min(4 * a, c);
                    if (a) {
                        for (var g = 0; g < a; g += e) this._doProcessBlock(d, g);
                        g = d.splice(0, a);
                        b.sigBytes -= c
                    }
                    return new j.init(g, c)
                },
                clone: function() {
                    var a = f.clone.call(this);
                    a._data = this._data.clone();
                    return a
                },
                _minBufferSize: 0
            });
        l.Hasher = q.extend({
            cfg: f.extend(),
            init: function(a) {
                this.cfg = this.cfg.extend(a);
                this.reset()
            },
            reset: function() {
                q.reset.call(this);
                this._doReset()
            },
            update: function(a) {
                this._append(a);
                this._process();
                return this
            },
            finalize: function(a) {
                a && this._append(a);
                return this._doFinalize()
            },
            blockSize: 16,
            _createHelper: function(a) {
                return function(b, d) {
                    return (new a.init(d)).finalize(b)
                }
            },
            _createHmacHelper: function(a) {
                return function(b, d) {
                    return (new u.HMAC.init(a,
                        d)).finalize(b)
                }
            }
        });
        var u = k.algo = {};
        return k
    }(Math);

    /*
    CryptoJS v3.1.2
    code.google.com/p/crypto-js
    (c) 2009-2013 by Jeff Mott. All rights reserved.
    code.google.com/p/crypto-js/wiki/License
    */
    (function() {
        var h = CryptoJS,
            j = h.lib.WordArray;
        h.enc.Base64 = {
            stringify: function(b) {
                var e = b.words,
                    f = b.sigBytes,
                    c = this._map;
                b.clamp();
                b = [];
                for (var a = 0; a < f; a += 3)
                    for (var d = (e[a >>> 2] >>> 24 - 8 * (a % 4) & 255) << 16 | (e[a + 1 >>> 2] >>> 24 - 8 * ((a + 1) % 4) & 255) << 8 | e[a + 2 >>> 2] >>> 24 - 8 * ((a + 2) % 4) & 255, g = 0; 4 > g && a + 0.75 * g < f; g++) b.push(c.charAt(d >>> 6 * (3 - g) & 63));
                if (e = c.charAt(64))
                    for (; b.length % 4;) b.push(e);
                return b.join("")
            },
            parse: function(b) {
                var e = b.length,
                    f = this._map,
                    c = f.charAt(64);
                c && (c = b.indexOf(c), -1 != c && (e = c));
                for (var c = [], a = 0, d = 0; d <
                    e; d++)
                    if (d % 4) {
                        var g = f.indexOf(b.charAt(d - 1)) << 2 * (d % 4),
                            h = f.indexOf(b.charAt(d)) >>> 6 - 2 * (d % 4);
                        c[a >>> 2] |= (g | h) << 24 - 8 * (a % 4);
                        a++
                    }
                return j.create(c, a)
            },
            _map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
        }
    })();
    // End of CryptoJS


    // Start functions for XML to JSON Object
    var DOMNodeTypes = {
        ELEMENT_NODE 	   : 'ELEMENT_NODE',
        TEXT_NODE    	   : 'TEXT_NODE' ,
        CDATA_SECTION_NODE : 'CDATA_SECTION_NODE',
        COMMENT_NODE	   : 'COMMENT_NODE',
        DOCUMENT_NODE 	   : 'DOCUMENT_NODE'
    };

    var config = {};

    function initConfigDefaults(config) {
        if(config.escapeMode === undefined) {
            config.escapeMode = true;
        }
        
        config.attributePrefix = config.attributePrefix || "_";
        config.arrayAccessForm = config.arrayAccessForm || "none";
        config.emptyNodeForm = config.emptyNodeForm || "text";		
        
        if(config.enableToStringFunc === undefined) {
            config.enableToStringFunc = true; 
        }
        config.arrayAccessFormPaths = config.arrayAccessFormPaths || []; 
        if(config.skipEmptyTextNodesForObj === undefined) {
            config.skipEmptyTextNodesForObj = true;
        }
        if(config.stripWhitespaces === undefined) {
            config.stripWhitespaces = true;
        }
        config.datetimeAccessFormPaths = config.datetimeAccessFormPaths || [];

        if(config.useDoubleQuotes === undefined) {
            config.useDoubleQuotes = false;
        }
        
        config.xmlElementsFilter = config.xmlElementsFilter || [];
        config.jsonPropertiesFilter = config.jsonPropertiesFilter || [];
        
        if(config.keepCData === undefined) {
            config.keepCData = false;
        }

        return config;
    }

    function checkFromXmlDateTimePaths(value, childName, fullPath) {
        if(config.datetimeAccessFormPaths.length > 0) {
            var path = fullPath.split("\.#")[0];
            if(checkInStdFiltersArrayForm(config.datetimeAccessFormPaths, value, childName, path)) {
                return fromXmlDateTime(value);
            }
            else
                return value;			
        }
        else
            return value;
    }

    function toArrayAccessForm(obj, childName, path) {
        switch(config.arrayAccessForm) {
            case "property":
                if(!(obj[childName] instanceof Array))
                    obj[childName+"_asArray"] = [obj[childName]];
                else
                    obj[childName+"_asArray"] = obj[childName];
                break;
            /*case "none":
                break;*/
        }
        
        if(!(obj[childName] instanceof Array) && config.arrayAccessFormPaths.length > 0) {
            if(checkInStdFiltersArrayForm(config.arrayAccessFormPaths, obj, childName, path)) {
                obj[childName] = [obj[childName]];
            }			
        }
    }
    function checkXmlElementsFilter(obj, childType, childName, childPath) {
        if( childType == DOMNodeTypes.ELEMENT_NODE && config.xmlElementsFilter.length > 0) {
            return checkInStdFiltersArrayForm(config.xmlElementsFilter, obj, childName, childPath);	
        }
        else
            return true;
    }

    function getNodeLocalName( node ) {
        var nodeLocalName = node.localName;			
        if(nodeLocalName == null) // Yeah, this is IE!! 
            nodeLocalName = node.baseName;
        if(nodeLocalName == null || nodeLocalName=="") // =="" is IE too
            nodeLocalName = node.nodeName;
        return nodeLocalName;
    }
    
    function getNodePrefix(node) {
        return node.prefix;
    }

    function parseDOMChildren( node, path ) {
        if(node.nodeType == DOMNodeTypes.DOCUMENT_NODE) {
            var result = new Object;
            var nodeChildren = node.childNodes;
            // Alternative for firstElementChild which is not supported in some environments
            for(var cidx=0; cidx <nodeChildren.length; cidx++) {
                var child = nodeChildren[cidx];
                if(child.nodeType == DOMNodeTypes.ELEMENT_NODE) {
                    var childName = getNodeLocalName(child);
                    result[childName] = parseDOMChildren(child, childName);
                }
            }
            return result;
        }
        else
        if(node.nodeType == DOMNodeTypes.ELEMENT_NODE) {
            var result = new Object;
            result.__cnt=0;
            var attrVal = node.getAttribute('internalId');
            if (attrVal != null && attrVal != '')
                result.__internalId = attrVal;
            var attrVal = node.getAttribute('typeId');
            if (attrVal != null && attrVal != '')
                result.__typeId = attrVal;
            var attrVal = node.getAttribute('scriptId');
            if (attrVal != null && attrVal != '')
                result.__scriptId = attrVal;
            var attrVal = node.getAttribute('isSuccess');
            if (attrVal != null && attrVal != '')
                result.__isSuccess = attrVal;


            var nodeChildren = node.childNodes;
            
            // Children nodes
            for(var cidx=0; cidx <nodeChildren.length; cidx++) {
                var child = nodeChildren[cidx]; // nodeChildren[cidx];
                var childName = getNodeLocalName(child);
                
                if(child.nodeType!= DOMNodeTypes.COMMENT_NODE) {
                    var childPath = path+"."+childName;
                    if (checkXmlElementsFilter(result,child.nodeType,childName,childPath)) {
                        result.__cnt++;
                        if(result[childName] == null) {
                            result[childName] = parseDOMChildren(child, childPath);
                            toArrayAccessForm(result, childName, childPath);					
                        }
                        else {
                            if(result[childName] != null) {
                                if( !(result[childName] instanceof Array)) {
                                    result[childName] = [result[childName]];
                                    toArrayAccessForm(result, childName, childPath);
                                }
                            }
                            (result[childName])[result[childName].length] = parseDOMChildren(child, childPath);
                        }
                    }
                }								
            }
            
            // Attributes
            for(var aidx=0; aidx <node.attributes.length; aidx++) {
                var attr = node.attributes.item(aidx); // [aidx];
                result.__cnt++;
                result[config.attributePrefix+attr.name]=attr.value;
            }
            
            // Node namespace prefix
            var nodePrefix = getNodePrefix(node);
            if(nodePrefix!=null && nodePrefix!="") {
                result.__cnt++;
                result.__prefix=nodePrefix;
            }
            
            if(result["#text"]!=null) {				
                result.__text = result["#text"];
                if(result.__text instanceof Array) {
                    result.__text = result.__text.join("\n");
                }
                //if(config.escapeMode)
                //	result.__text = unescapeXmlChars(result.__text);
                if(config.stripWhitespaces)
                    result.__text = result.__text.trim();
                delete result["#text"];
                if(config.arrayAccessForm=="property")
                    delete result["#text_asArray"];
                result.__text = checkFromXmlDateTimePaths(result.__text, childName, path+"."+childName);
            }
            if(result["#cdata-section"]!=null) {
                result.__cdata = result["#cdata-section"];
                delete result["#cdata-section"];
                if(config.arrayAccessForm=="property")
                    delete result["#cdata-section_asArray"];
            }
            
            if( result.__cnt == 0 && config.emptyNodeForm=="text" ) {
                result = '';
            }
            else
            if( result.__cnt == 1 && result.__text!=null  ) {
                result = result.__text;
            }
            else
            if( result.__cnt == 1 && result.__cdata!=null && !config.keepCData  ) {
                result = result.__cdata;
            }			
            else			
            if ( result.__cnt > 1 && result.__text!=null && config.skipEmptyTextNodesForObj) {
                if( (config.stripWhitespaces && result.__text=="") || (result.__text.trim()=="")) {
                    delete result.__text;
                }
            }
            delete result.__cnt;			
            
            if( config.enableToStringFunc && (result.__text!=null || result.__cdata!=null )) {
                result.toString = function() {
                    return (this.__text!=null? this.__text:'')+( this.__cdata!=null ? this.__cdata:'');
                };
            }
            
            return result;
        }
        else
        if(node.nodeType == DOMNodeTypes.TEXT_NODE || node.nodeType == DOMNodeTypes.CDATA_SECTION_NODE) {
            return node.nodeValue;
        }	
    }
    // End functions for XML to JSON Object

    var stEmailFrom = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_email_f');
    var stEmailTo = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_email_t');
    var stEmailTemplate = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_tmpl');

    var soapRequestXML = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_soap_req');
    var soapRequestAction = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_soap_action');
    var soapRequestURL = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_soap_req_url');

    var consumerKey = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_ws_cons_key');
    var consumerSecret = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_ws_cons_secret');
    var tokenId = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_ws_token_id');
    var tokenSecret = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_ws_token_secret');

    var budgetSrchId = runtime.getCurrentScript().getParameter('custscript_mr_budg_copy_srch_id');
    var budgetCopyMapping = null, wsDomain = null;
    config = initConfigDefaults(config);

	/**
	 * Marks the beginning of the Map/Reduce process and generates input data.
	 *
	 * @typedef {Object} ObjectRef
	 * @property {number} id - Internal ID of the record instance
	 * @property {string} type - Record type id
	 *
	 * @return {Array|Object|Search|RecordRef} inputSummary
	 * @since 2015.1
	 * 
	 * @memberOf CodeScript
	 */
	ScriptFunc.getInputData = function()
	{
		var stLogTitle = 'getInputData';
        // log.debug(stLogTitle, '**** START: Entry Point Invocation ****');
        var arrScriptErrors = [],arrReturnData = [];
		try
		{
            var arrFilters = [], arrColumns = [];

            var arrResult = NSUtil.search(null, budgetSrchId, arrFilters, arrColumns);
            for (var intIndex = 0; arrResult != null && intIndex < arrResult.length; intIndex++)
			{
                var procData = {};
                procData.id = arrResult[intIndex].id;
                arrReturnData.push(procData);
            }
		}
		catch (e)
		{
			var stErrorTitle = stLogTitle + ': ProccessError ';
			var stError = (e.name != null && e.name != '') ? e.name + ': ' + e.message : 'UnexpectedError: ' + e.message;
			log.error(stErrorTitle, stError )

			arrScriptErrors.push(stError);
			throw (e.name != null && e.name != '') ? e : error.create(
				{
				    name : '9999',
				    message : stError,
				    notifyOff : false
				});
		}
		finally
		{
			if (arrScriptErrors.length > 0)
			{
				//Send an email to the User
				var stEmailMessage = '';

				stEmailMessage += '<table width="100%" border="1" style="border-collapse:collapse; text-align:center; line-height: 12px;" cellpadding="3">';
				stEmailMessage += '<tr>';
				stEmailMessage += '<td>Error Messages</td>';
				stEmailMessage += '</tr>';
				for (var i = 0; i < arrScriptErrors.length; i++)
				{
					stEmailMessage += '<tr>';
					stEmailMessage += '<td>' + arrScriptErrors[i] + '</td>';
					stEmailMessage += '</tr>';
				}

				stEmailMessage += '</table>';
				sendEmail(stEmailTemplate, stEmailFrom, stEmailTo, stEmailMessage);
			}
		}

		// log.debug(stLogTitle, '**** END: Entry Point Invocation **** ' + arrReturnData.length);

		return arrReturnData;
    }   
    
	/**
	 * Executes when the map entry point is triggered and applies to each key/value pair.
	 *
	 * @param {MapContext} context - Data collection containing the key/value pairs to process through the map stage
	 * @since 2015.1
	 * 
	 * @memberOf CodeScript
	 */
	ScriptFunc.map = function(context)
	{
		var stLogTitle = 'map(context)';
		// log.debug(stLogTitle, '**** START: Entry Point Invocation ****');

		var arrScriptErrors = [];
		try
		{
            var objProcDetails = JSON.parse(context.value);
            //log.audit(stLogTitle, 'Processing : ' + objProcDetails.id);
            context.write(objProcDetails.id, objProcDetails);
        }
		catch (e)
		{
			var stErrorTitle = stLogTitle + ' : ProccessError : ' ;
			var stError = (e.name != null && e.name != '') ? e.name + ': ' + e.message : 'UnexpectedError: ' + e.message;

			log.error(stErrorTitle, stError)
			arrScriptErrors.push(stError);
			throw (e.name != null && e.name != '') ? e : error.create(
				{
				    name : '9999',
				    message : stError,
				    notifyOff : false
				});
		}
		finally
		{
			if (arrScriptErrors.length > 0)
			{
				//Send an email to the User
				var stEmailMessage = '';

				stEmailMessage += '<table width="100%" border="1" style="border-collapse:collapse; text-align:center; line-height: 12px;" cellpadding="3">';
				stEmailMessage += '<tr>';
				stEmailMessage += '<td>Error Messages</td>';
				stEmailMessage += '</tr>';

				for (var i = 0; i < arrScriptErrors.length; i++)
				{
					stEmailMessage += '<tr>';
					stEmailMessage += '<td>' + arrScriptErrors[i] + '</td>';
					stEmailMessage += '</tr>';
				}

				stEmailMessage += '</table>';

				//sendEmail(stEmailTemplate, stEmailFrom, stEmailTo, stEmailMessage);
			}
		}

		// log.debug(stLogTitle, '**** END: Entry Point Invocation ****')
    }
    
	/**
	 * Executes when the reduce entry point is triggered and applies to each group.
	 *
	 * @param {ReduceSummary} context - Data collection containing the groups to process through the reduce stage
	 * @since 2015.1
	 * 
	 * @memberOf CodeScript
	 */
	ScriptFunc.reduce = function(context)
	{
        var stLogTitle = 'reduce(context)';
		// log.debug(stLogTitle, '**** START: Entry Point Invocation ****');

		var arrScriptErrors = [];
        var stBudgetId = context.key;

        // log.debug(stLogTitle, 'Budget Id : ' + stBudgetId );
        try
		{
            if (budgetCopyMapping == null)
                budgetCopyMapping = getBudgetCopyMapping();
            if (wsDomain == null)
            {
                var dcUrl = 'https://rest.netsuite.com/rest/datacenterurls';
                if (JSON.stringify(runtime.envType) == 'SANDBOX')
                    dcUrl = 'https://rest.sandbox.netsuite.com/rest/datacenterurls';
                wsDomain = getWSDomain(runtime.accountId, dcUrl);
                soapRequestURL = wsDomain + soapRequestURL;
            }

            // log.debug('soapRequestURL', soapRequestURL);

            /*var consumerKey = '****************************************************************';
            var consumerSecret = '****************************************************************';
            var tokenId = 'd312e10534b9e26c3a9db6fb5facc6f4bf40cee2019f16bec3ed9dd5f858b1c6';
            var tokenSecret = '007964ef3cfb84189817f08a0a30b08e61c85051d9a084e2b5130a9441713b88';*/

            var accountId = runtime.accountId;
            var timeStamp = (new Date()).getTime() / 1000;
            timeStamp = Math.round(timeStamp);
            var nonce = randomString(20);
            var algorithm = 'HMAC_SHA256';
            var baseString = accountId + "&" + consumerKey + "&" + tokenId + "&" + nonce + "&" + timeStamp;
            var key = consumerSecret + '&' + tokenSecret; 

            var hash = CryptoJS.HmacSHA256(baseString, key);
            var wsToken = CryptoJS.enc.Base64.stringify(hash);
            //log.debug('Credentials' ,'key: ' + key + ' ,baseString : ' + baseString + ',signature : ' + wsToken);

            var stRequestXML= soapRequestXML;
            stRequestXML = stRequestXML.replace(/{ws_consumer_key}/gi, consumerKey);
            stRequestXML = stRequestXML.replace(/{ws_token}/gi, tokenId);
            stRequestXML = stRequestXML.replace(/{ws_nonce}/gi, nonce);
            stRequestXML = stRequestXML.replace(/{ws_timestamp}/gi, timeStamp);
            stRequestXML = stRequestXML.replace(/{ws_sign_algorithm}/gi, algorithm);
            stRequestXML = stRequestXML.replace(/{ws_signature}/gi, wsToken);

            stRequestXML = stRequestXML.replace(/{ws_acct_id}/gi, accountId);
            stRequestXML = stRequestXML.replace(/{ws_budget_id}/gi, stBudgetId);
            stRequestXML = stRequestXML.replace(/<br>/gi, '');
            var response = invokeWS(soapRequestURL, soapRequestAction, stRequestXML);
            if (response.indexOf('Error') == 0)
            {
                log.debug(stLogTitle, 'Error Response : ' + response);
            }
            else
            {
                var xmlResponse = xml.Parser.fromString({
                    text : response
                });
                var jsonResp = parseDOMChildren(xmlResponse);
                var readResp = jsonResp.Envelope.Body.getResponse.readResponse;
                if (readResp.status.__isSuccess == 'true')
                {
                    var recordResp = readResp.record;
                    var recordObj = {};
                    for(var fld in recordResp)
                    {
                        if (fld == 'customFieldList')
                        {
                            var customFieldList = recordResp.customFieldList;
                            if (customFieldList != null && customFieldList.customField != null && customFieldList.customField.length > 0)
                            {
                                for (var custPos = 0; custPos < customFieldList.customField.length; custPos++)
                                {
                                    var custFieldObj = customFieldList.customField[custPos];
                                    recordObj[custFieldObj.__scriptId.toLowerCase()] = getWSFieldValue(custFieldObj.value, custFieldObj.__scriptId );
                                }
                            }        
                        }
                        else if (fld == '__internalId')
                            recordObj.internalid = recordResp.__internalId;
                        else if (fld != '__prefix')
                            recordObj[fld.toLowerCase()] = getWSFieldValue(recordResp,fld) ;
                    }

                    // Process standard budget record into custom budget copy record
                    var budgetCopyRec = null;
                    var arrFilters = [];
                    arrFilters.push(search.createFilter(
                        {
                            name : 'custrecord_nfp_budg_internalid',
                            operator : search.Operator.ANYOF,
                            values :
                                [
                                    recordObj.internalid
                                ]
                        }));
                    var arrResult = NSUtil.search('customrecord_budget_copy', null, arrFilters, null);
                    if (arrResult.length > 0)
                        budgetCopyRec = record.load({type:'customrecord_budget_copy', id:arrResult[0].id, recordmode: 'dynamic' });
                    else
                    {
                        budgetCopyRec = record.create({type: 'customrecord_budget_copy', recordmode: 'dynamic'});
                        budgetCopyRec.setValue('custrecord_nfp_budg_internalid', recordObj.internalid);
                    }
                    var customRecordFldPrefix = 'custrecord_nfp_budg_';
                    for(var custFld in recordObj)
                    {
                        if(custFld != 'internalid')
                        {
                            var value = recordObj[custFld].text;
                            if (recordObj[custFld].id != null && recordObj[custFld].id != undefined && recordObj[custFld].id != '')
                                value = recordObj[custFld].id ;

                            if (custFld == 'year' )
                                value = recordObj[custFld].text;
                            else if (custFld.toLowerCase() == 'budgettype')
                            {
                                value = value.substring(1);
                                value = value.charAt(0).toUpperCase() + value.substring(1);
                            }
                            budgetCopyRec.setValue( budgetCopyMapping[custFld.toLowerCase()], value);
                        }
                    }
                    var budgetCopyId = budgetCopyRec.save();
                    //log.debug('Budget Record Copy ' ,' Saved : ' + budgetCopyId);
                }
            }
		}
		catch (e)
		{
            var stErrorTitle = stLogTitle + ' : ' + 'Process Error';
            var stError = (e.name != null && e.name != '') ? e.name + ': ' + e.message : 'UnexpectedError: ' + e.message;
            var msg = '';
            /*if (typeof e.getStackTrace === 'function') {
                msg = e.toString() + '<br />' + e.getStackTrace().join('<br />');
            }
            else if (e.stack) {
                msg = e.toString() + e.stack.replace(/(^|\s)at(\s|$)/g, '<br />at ');
            }
            else {
                msg = e.toString();
            }*/
            log.error(stErrorTitle, stError + ' : ' + msg );
			arrScriptErrors.push(stError);
		}
		finally
		{
			if (arrScriptErrors.length > 0)
			{
				//Send an email to the User
				var stEmailMessage = '';

				stEmailMessage += '<table width="100%" border="1" style="border-collapse:collapse; text-align:center; line-height: 12px;" cellpadding="3">';
				stEmailMessage += '<tr>';
				stEmailMessage += '<td>Error Messages</td>';
				stEmailMessage += '</tr>';

				for (var i = 0; i < arrScriptErrors.length; i++)
				{
					stEmailMessage += '<tr>';
					stEmailMessage += '<td>' + arrScriptErrors[i] + '</td>';
					stEmailMessage += '</tr>';
				}

				stEmailMessage += '</table>';

				//sendEmail(stEmailTemplate, stEmailFrom, stEmailTo, stEmailMessage);
			}
		}

		// log.debug(stLogTitle, '**** END: Entry Point Invocation ****');
	}
            
	/**
	 * Executes when the summarize entry point is triggered and applies to the result set.
	 *
	 * @param {Summary} summary - Holds statistics regarding the execution of a map/reduce script
	 * @since 2015.1
	 * 
	 * @memberOf CodeScript
	 */
	ScriptFunc.summarize = function(summary)
	{
		var stLogTitle = 'summarize(summary)';
		// log.debug(stLogTitle, '**** START: Entry Point Invocation ****');

		try
		{
			var type = summary.toString();
			log.audit(stLogTitle, 'Usage Consumed = ' + summary.usage + ' | Concurrency Number = ' + summary.concurrency + ' | Number of Yields = ' + summary.yields);

        }
		catch (e)
		{
			var stErrorTitle = stLogTitle;
			var stError = (e.name != null && e.name != '') ? e.name + ': ' + e.message : 'UnexpectedError: ' + e.message;
			var stErrorTitle = (stErrorTitle) ? stErrorTitle : 'ProccessError';

			log.error(stErrorTitle, stError)
			throw (e.name != null && e.name != '') ? e : error.create(
				{
				    name : '9999',
				    message : stError,
				    notifyOff : false
				});
		}

		// log.debug(stLogTitle, '**** END: Entry Point Invocation ****')
    }


    function getWSFieldValue(obj, fieldName)
    {
        var retVal = {id: '', text: ''};
        if (obj[fieldName] != null)
        {
            if (obj[fieldName].__internalId != undefined && obj[fieldName].__internalId != null)
            {
                retVal.id = obj[fieldName].__internalId ;
                retVal.text = obj[fieldName].name.__text;
            }
            else
                retVal.text = obj[fieldName].__text;
        }
        else 
        {
            if (obj.__internalId != undefined && obj.__internalId != null)
            {
                retVal.id = obj.__internalId ;
                retVal.text = obj.name.__text;
            }
            else if (obj.__text != undefined && obj.__text != null)
                retVal.text = obj.__text;

        }
        return retVal;
    }

    function getWSDomain(accountId, dcUrl)
    {
        var stLogTitle = 'getWSDomain';
        var headers = new Array();
        headers['Content-Type'] = 'application/json';

        var reqUrl = dcUrl + '?account=' + accountId;
        var jsonResp = https.request({
            method: 'GET',
            url: reqUrl,
            headers: headers
        });
        if (jsonResp && jsonResp.code == 200)
        {
            var jsonBody = JSON.parse(jsonResp.body);
            return jsonBody.webservicesDomain;
        }
        log.debug(stLogTitle, 'Response Body : ' + jsonResp.body  );
        return null;
    }

    function invokeWS(soapUrl, soapAction, soapRequest)
    {
        var stLogTitle = 'invokeWS';
        //Set up Headers
        var headers = new Array();
        headers['User-Agent-x'] = 'SuiteScript-Call';
        headers['Content-Type'] = 'text/xml';
        headers['SOAPAction'] = soapAction;

        var soapResp = https.request({
            method: 'POST',
            url: soapUrl,
            headers: headers,
            body: soapRequest
        });

        if (soapResp && soapResp.code == 200)
        {
            //log.debug(stLogTitle, 'Response Body : ' + soapResp.body );
            return soapResp.body;
        }
        log.debug(stLogTitle,'url : ' + soapUrl + 'SOAPAction : ' + soapAction);
        log.debug(stLogTitle, 'Request Body : ' + soapRequest  );
        log.debug(stLogTitle, 'Response Body : ' + soapResp.body  );
        return 'Error : Response code returned : ' + soapResp.code;
    }

    function getBudgetCopyMapping()
    {
        var retValData = {};
        var arrFilters = [], arrColumns = [];
        arrFilters.push(search.createFilter({name : 'isinactive',operator : search.Operator.IS,values : ['F'] }));
        arrColumns.push(search.createColumn({name : 'name' }));
        arrColumns.push(search.createColumn({name : 'custrecord_nfp_budm_bud_copy_internalid' }));

        var arrResult = NSUtil.search('customrecord_budget_budget_copy_mapping', null, arrFilters, arrColumns);
        for (var intIndex = 0; arrResult != null && intIndex < arrResult.length; intIndex++)
        {
            retValData[arrResult[intIndex].getValue('name') ] = arrResult[intIndex].getValue('custrecord_nfp_budm_bud_copy_internalid');
        }

        return retValData;
    }

    function randomString(m) {
        var m = m || 20; s = '', r = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (var i=0; i < m; i++) { s += r.charAt(Math.floor(Math.random()*r.length)); }
        return s;
    };

    function convToDate(input)
    {
        if (input == null || input == undefined || input == '')
            return null;
        try
        {
            var arrInput = input.split('-');
            return new Date(arrInput[0], parseInt(arrInput[1]) - 1, arrInput[2] );
        }
        catch (ex)
        {
            return null;
        }
    }
    
	function sendEmail(stEmailTemplate, stEmailFrom, stEmailTo, stEmailMessage)
	{
		var stLoggerTitle = 'sendEmail';

		if (NSUtil.isEmpty(stEmailFrom) || NSUtil.isEmpty(stEmailTo))
		{
			log.debug(stLoggerTitle, 'Cannot send email. Missing Email From/To');
			return;
		}

		var objEmailMerger = render.mergeEmail(
			{
				templateId : stEmailTemplate,
			});

		var stEmailSubject = objEmailMerger.subject;
		var stEmailBody = objEmailMerger.body;

		stEmailBody = stEmailBody.replace('{NSSCRIPTDATA}', stEmailMessage);

		//send email    
		email.send(
			{
			    author : stEmailFrom,
			    recipients : stEmailTo,
			    subject : stEmailSubject,
			    body : stEmailBody
			});
		log.debug(stLoggerTitle, 'Sent Email to =' + stEmailTo);
	}
 

    /*
    * Calculate the SHA-1 of an array of big-endian words, and a bit length
    */
    function core_sha1(x, len)
    {
    /* append padding */
    x[len >> 5] |= 0x80 << (24 - len % 32);
    x[((len + 64 >> 9) << 4) + 15] = len;

    var w = Array(80);
    var a =  1732584193;
    var b = -271733879;
    var c = -1732584194;
    var d =  271733878;
    var e = -1009589776;

    for(var i = 0; i < x.length; i += 16)
    {
        var olda = a;
        var oldb = b;
        var oldc = c;
        var oldd = d;
        var olde = e;

        for(var j = 0; j < 80; j++)
        {
        if(j < 16) w[j] = x[i + j];
        else w[j] = rol(w[j-3] ^ w[j-8] ^ w[j-14] ^ w[j-16], 1);
        var t = safe_add(safe_add(rol(a, 5), sha1_ft(j, b, c, d)),
                        safe_add(safe_add(e, w[j]), sha1_kt(j)));
        e = d;
        d = c;
        c = rol(b, 30);
        b = a;
        a = t;
        }

        a = safe_add(a, olda);
        b = safe_add(b, oldb);
        c = safe_add(c, oldc);
        d = safe_add(d, oldd);
        e = safe_add(e, olde);
    }
    return Array(a, b, c, d, e);

    }

    /*
    * Perform the appropriate triplet combination function for the current
    * iteration
    */
    function sha1_ft(t, b, c, d)
    {
    if(t < 20) return (b & c) | ((~b) & d);
    if(t < 40) return b ^ c ^ d;
    if(t < 60) return (b & c) | (b & d) | (c & d);
    return b ^ c ^ d;
    }

    /*
    * Determine the appropriate additive constant for the current iteration
    */
    function sha1_kt(t)
    {
    return (t < 20) ?  1518500249 : (t < 40) ?  1859775393 :
            (t < 60) ? -1894007588 : -899497514;
    }

    /*
    * Calculate the HMAC-SHA1 of a key and some data
    */
    function core_hmac_sha1(key, data)
    {
    var chrsz   = 8; 
    var bkey = str2binb(key);
    if(bkey.length > 16) bkey = core_sha1(bkey, key.length * chrsz);

    var ipad = Array(16), opad = Array(16);
    for(var i = 0; i < 16; i++)
    {
        ipad[i] = bkey[i] ^ 0x36363636;
        opad[i] = bkey[i] ^ 0x5C5C5C5C;
    }

    var hash = core_sha1(ipad.concat(str2binb(data)), 512 + data.length * chrsz);
    return core_sha1(opad.concat(hash), 512 + 160);
    }

    /*
    * Add integers, wrapping at 2^32. This uses 16-bit operations internally
    * to work around bugs in some JS interpreters.
    */
    function safe_add(x, y)
    {
    var lsw = (x & 0xFFFF) + (y & 0xFFFF);
    var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
    return (msw << 16) | (lsw & 0xFFFF);
    }

    /*
    * Bitwise rotate a 32-bit number to the left.
    */
    function rol(num, cnt)
    {
    return (num << cnt) | (num >>> (32 - cnt));
    }

    /*
    * Convert an 8-bit or 16-bit string to an array of big-endian words
    * In 8-bit function, characters >255 have their hi-byte silently ignored.
    */
    function str2binb(str)
    {
    var chrsz   = 8; 
    var bin = Array();
    var mask = (1 << chrsz) - 1;
    for(var i = 0; i < str.length * chrsz; i += chrsz)
        bin[i>>5] |= (str.charCodeAt(i / chrsz) & mask) << (32 - chrsz - i%32);
    return bin;
    }

    /*
    * Convert an array of big-endian words to a string
    */
    function binb2str(bin)
    {
    var chrsz   = 8; 
    var str = "";
    var mask = (1 << chrsz) - 1;
    for(var i = 0; i < bin.length * 32; i += chrsz)
        str += String.fromCharCode((bin[i>>5] >>> (32 - chrsz - i%32)) & mask);
    return str;
    }

    /*
    * Convert an array of big-endian words to a hex string.
    */
    function binb2hex(binarray)
    {
    var hexcase = 0;  /* hex output format. 0 - lowercase; 1 - uppercase        */
    var hex_tab = hexcase ? "0123456789ABCDEF" : "0123456789abcdef";
    var str = "";
    for(var i = 0; i < binarray.length * 4; i++)
    {
        str += hex_tab.charAt((binarray[i>>2] >> ((3 - i%4)*8+4)) & 0xF) +
            hex_tab.charAt((binarray[i>>2] >> ((3 - i%4)*8  )) & 0xF);
    }
    return str;
    }

    /*
    * Convert an array of big-endian words to a base-64 string
    */
    function binb2b64(binarray)
    {
    var b64pad  = ""; /* base-64 pad character. "=" for strict RFC compliance   */
    var tab = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    var str = "";
    for(var i = 0; i < binarray.length * 4; i += 3)
    {
        var triplet = (((binarray[i   >> 2] >> 8 * (3 -  i   %4)) & 0xFF) << 16)
                    | (((binarray[i+1 >> 2] >> 8 * (3 - (i+1)%4)) & 0xFF) << 8 )
                    |  ((binarray[i+2 >> 2] >> 8 * (3 - (i+2)%4)) & 0xFF);
        for(var j = 0; j < 4; j++)
        {
        if(i * 8 + j * 6 > binarray.length * 32) str += b64pad;
        else str += tab.charAt((triplet >> 6*(3-j)) & 0x3F);
        }
    }
    return str;
    }

	var NSUtil = (typeof NSUtil === 'undefined') ? {} : NSUtil;

	/**
	 * Evaluate if the given string or object value is empty, null or undefined.
	 * @param {String} stValue - string or object to evaluate
	 * @returns {Boolean} - true if empty/null/undefined, false if not
	 * <AUTHOR>
	 * @memberOf NSUtil
	 */
	NSUtil.isEmpty = function(stValue)
	{
		return ((stValue === '' || stValue == null || stValue == undefined) || (stValue.constructor === Array && stValue.length == 0) || (stValue.constructor === Object && (function(v)
		{
			for ( var k in v)
				return false;
			return true;
		})(stValue)));
	};

	/**
	 * Evaluate if the given string is an element of the array, using reverse looping
	 * @param {String} stValue - String value to find in the array
	 * @param {String[]} arrValue - Array to be check for String value
	 * @returns {Boolean} - true if string is an element of the array, false if not
	 * @memberOf NSUtil
	 */
	NSUtil.inArray = function(stValue, arrValue)
	{
		for (var i = arrValue.length - 1; i >= 0; i--)
		{
			if (stValue == arrValue[i])
			{
				break;
			}
		}
		return (i > -1);
	};

	/**
	 * Round decimal number
	 * @param {Number} flDecimalNumber - decimal number value
	 * @param {Number} intDecimalPlace - decimal places
	 *
	 * @returns {Number} - a floating point number value
	 * <AUTHOR> and lochengco
	 */
	NSUtil.roundDecimalAmount = function(flDecimalNumber, intDecimalPlace)
	{
		//this is to make sure the rounding off is correct even if the decimal is equal to -0.995
		var bNegate = false;
		if (flDecimalNumber < 0)
		{
			flDecimalNumber = Math.abs(flDecimalNumber);
			bNegate = true;
		}

		var flReturn = 0.00;
		intDecimalPlace = (intDecimalPlace == null || intDecimalPlace == '') ? 0 : intDecimalPlace;

		var intMultiplierDivisor = Math.pow(10, intDecimalPlace);
		flReturn = Math.round((parseFloat(flDecimalNumber) * intMultiplierDivisor)) / intMultiplierDivisor;
		flReturn = (bNegate) ? (flReturn * -1) : flReturn;

		return flReturn.toFixed(intDecimalPlace);
	};

	/**
	 * Converts string to float. If value is infinity or can't be converted to a number, 0.00 will be returned.
	 * @param {String} stValue - any string
	 * @returns {Number} - a floating point number
	 * <AUTHOR>
	 */
	NSUtil.forceFloat = function(stValue)
	{
		var flValue = parseFloat(stValue);

		if (isNaN(flValue) || (stValue == Infinity))
		{
			return 0.00;
		}

		return flValue;
	};

	/**
	 * Get all of the results from the search even if the results are more than 1000.
	 * @param {String} stRecordType - the record type where the search will be executed.
	 * @param {String} stSearchId - the search id of the saved search that will be used.
	 * @param {nlobjSearchFilter[]} arrSearchFilter - array of nlobjSearchFilter objects. The search filters to be used or will be added to the saved search if search id was passed.
	 * @param {nlobjSearchColumn[]} arrSearchColumn - array of nlobjSearchColumn objects. The columns to be returned or will be added to the saved search if search id was passed.
	 * @returns {nlobjSearchResult[]} - an array of nlobjSearchResult objects
	 * <AUTHOR> - initial version
	 * <AUTHOR> - used concat when combining the search result
	 */
	NSUtil.search = function(stRecordType, stSearchId, arrSearchFilter, arrSearchColumn)
	{
		if (stRecordType == null && stSearchId == null)
		{
			error.create(
				{
				    name : 'SSS_MISSING_REQD_ARGUMENT',
				    message : 'search: Missing a required argument. Either stRecordType or stSearchId should be provided.',
				    notifyOff : false
				});
		}

		var arrReturnSearchResults = new Array();
		var objSavedSearch;

		var maxResults = 1000;

		if (stSearchId != null)
		{
			objSavedSearch = search.load(
				{
					id : stSearchId
				});

			// add search filter if one is passed
			if (arrSearchFilter != null)
			{
				if (arrSearchFilter[0] instanceof Array || (typeof arrSearchFilter[0] == 'string'))
				{
					objSavedSearch.filterExpression = objSavedSearch.filterExpression.concat(arrSearchFilter);
				}
				else
				{
					objSavedSearch.filters = objSavedSearch.filters.concat(arrSearchFilter);
				}
			}

			// add search column if one is passed
			if (arrSearchColumn != null)
			{
				objSavedSearch.columns = objSavedSearch.columns.concat(arrSearchColumn);
			}
		}
		else
		{
			objSavedSearch = search.create(
				{
					type : stRecordType
				});

			// add search filter if one is passed
			if (arrSearchFilter != null)
			{
				if (arrSearchFilter[0] instanceof Array || (typeof arrSearchFilter[0] == 'string'))
				{
					objSavedSearch.filterExpression = arrSearchFilter;
				}
				else
				{
					objSavedSearch.filters = arrSearchFilter;
				}
			}

			// add search column if one is passed
			if (arrSearchColumn != null)
			{
				objSavedSearch.columns = arrSearchColumn;
			}
		}

		var objResultset = objSavedSearch.run();
		var intSearchIndex = 0;
		var arrResultSlice = null;
		do
		{
			arrResultSlice = objResultset.getRange(intSearchIndex, intSearchIndex + maxResults);
			if (arrResultSlice == null)
			{
				break;
			}

			arrReturnSearchResults = arrReturnSearchResults.concat(arrResultSlice);
			intSearchIndex = arrReturnSearchResults.length;
		}
		while (arrResultSlice.length >= maxResults);

		return arrReturnSearchResults;
	};

	return ScriptFunc;

});