/**
 * @NApiVersion 2.x
 * @NScriptType MapReduceScript
 */

define([
    'N/search',
    'N/error',
    './In8_StorageReportHelper'
], function (search, error, stgReportHelper) {

    function getInputData() {
        var filters = [
            search.createFilter({
                name: 'custentity_default_storage_location',
                operator: search.Operator.NONEOF,
                values: '@NONE@'
            }),
            search.createFilter({
                name: 'custentity_monthlystoragereport',
                operator: search.Operator.IS,
                values: true
            }),
            search.createFilter({
                name: 'email',
                operator: search.Operator.ISNOTEMPTY,
                values: null
            })
            // ,
            // search.createFilter({
            //     name: 'internalid',
            //     operator: search.Operator.IS,
            //     values: 320487
            // }),
        ];

        var columns = [
            'internalid'
        ];
        return search.create({
            type: search.Type.CUSTOMER,
            filters: filters,
            columns: columns
        });
    }

    function map(context) {
        // For each payout
        var id = context.key;
        var valuesObj = JSON.parse(context.value);
        var values = valuesObj.values;

        log.debug('Map ' + id, JSON.stringify(values));

        stgReportHelper.sendReportEmail(id);
    }

    function summarize(context) {

        handleErrorIfAny(context);

        log.audit({
            title: 'Usage units consumed',
            details: context.usage
        });
        log.audit({
            title: 'Concurrency',
            details: context.concurrency
        });
        log.audit({
            title: 'Number of yields',
            details: context.yields
        });
    }

    function handleErrorIfAny(summary) {
        var inputSummary = summary.inputSummary;
        var mapSummary = summary.mapSummary;
        var reduceSummary = summary.reduceSummary;

        if (inputSummary.error) {
            var e = error.create({
                name: 'INPUT_STAGE_FAILED',
                message: inputSummary.error
            });
            log.error('Stage: getInputData failed', e);
        }

        handleErrorInStage('map', mapSummary);
        handleErrorInStage('reduce', reduceSummary);
    }

    function handleErrorInStage(stage, summary) {
        var errorMsg = [];
        summary.errors.iterator().each(function (key, value) {
            var msg = 'Error id: ' + key + '. Error was: ' + JSON.parse(value).message + '\n';
            errorMsg.push(msg);

            log.error({
                title: 'Error in Storage Report SCH',
                details: msg
            });
            return true;
        });
    }

    return {
        getInputData: getInputData,
        map: map,
        summarize: summarize
    };
});