/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       25 May 2020     jdgonzal
 *
 */

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @param {String} type Operation types: create, edit, view, copy, print, email
 * @param {nlobjForm} form Current form
 * @param {nlobjRequest} request Request object
 * @returns {Void}
 */
function userEventBeforeLoad(type, form, request){
    if(type == 'view'){
        var recId = nlapiGetRecordId();
        form.setScript('customscript22');
        form.addButton('custpage_go_to_suitelet', 'Go to Suitelet', 'redirectToSuitelet('+ recId +')');
    }
}
