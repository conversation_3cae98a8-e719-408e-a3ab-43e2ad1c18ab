/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/sftp'], function(sftp) {

    function onRequest(context) {
        var connection = sftp.createConnection({
            username: 'tomousaach1002',
            passwordGuid: 'b5f7f16cf1fc4b9081abdd9b222865c2',
            url: 'mftprd.svb.com',
            port: 8022,
            hostKey: "AAAAB3NzaC1yc2EAAAADAQABAAAAgQCV69uwFuAUUIu1Wqy6tB8tB0oVwi79U2m2faRbzRarqZWXH4eZxD3IMaoWiO+dpPaGto32pv34gvi9357T5NVIjdEHtzuWCzCL0YD94jj/RJkjts+QgJ9mfXR9C05FWTpn1P9jB+cQx9Jq9UeZjAqhxXX7SBDcRiKh+Sok56VzSw==",
            hostKeyType: 'rsa'
        });

        var dir = connection.list({
            path: '/'
        });

        log.debug('test', { directories: dir });
    }

    return {
        onRequest: onRequest
    }
});
