/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/file', 'N/runtime'], function (search, record, file, runtime) {

    function getInputData() {
        var fileSearch = search.load({
            id: 'customsearch_acs_files_to_be_uploaded'
        });

        return fileSearch;
    }

    function map(context) {

        var fileId = context.key;

        try {

            // ACCOUNT TO BE USED -- ID: 217
            // ACCOUNT TO BE USED FOR SERVICE CHARGE -- ID: 610
            // FOR CYBERSOURCE, POS9 IS THE REFERENCE NUMBER. STILL NEED TO GET THE SALES ORDER NUMBER FROM CUSTOMER DEPOSIT ITSELF BEFORE YOU CAN PROCEED
            // FOR SHOPIFY, POS2 IS THE REFERENCE NUMBER. NO NEED TO LOAD THE CUSTOMER DEPOSIT.
            // ENDING ROWS FOR EACH FILE COULD HAVE SC -- WHICH MEANS IT'S CASHBACK.

            // CREATE THE DEPOSIT RECORD OBJECT
            var depositObj = record.create({
                type: record.Type.DEPOSIT
            });

            // SET THE ACCOUNT
            depositObj.setValue({ fieldId: 'account', value: 217 });

            // GET THE POSSIBLE CUSTOMER DEPOSITS
            var salesOrderNumbers = [];
            var referenceNumbers = [];
            var refundNumbers = [];
            var docNumbers = [];

            var paymentsLength = depositObj.getLineCount({ sublistId: 'payment' });
            for(var i = 0; i < paymentsLength; i++) {
                var paymentType = depositObj.getSublistValue({ fieldId: 'type', sublistId: 'payment', line: i });
                if(paymentType == 'CustDep') {
                    var custDepositID = depositObj.getSublistValue({ fieldId: 'id', sublistId: 'payment', line: i });

                    var referenceNumber = depositObj.getSublistValue({ fieldId: 'refnum', sublistId: 'payment', line: i });

                    var custDepositSearchLookup = search.lookupFields({
                        type: search.Type.CUSTOMER_DEPOSIT,
                        id: parseInt(custDepositID),
                        columns: ['salesorder']
                    });

                    salesOrderNumbers.push((custDepositSearchLookup.salesorder.length > 0) ? custDepositSearchLookup.salesorder[0].text.split(' #')[1] : ''); 
                    referenceNumbers.push((referenceNumber) ? referenceNumber : '');
                    refundNumbers.push('');
                    docNumbers.push('');

                } else if(paymentType == 'CustRfnd') {
                    
                    var refundNumber = depositObj.getSublistValue({ fieldId: 'memo', sublistId: 'payment', line: i });

                    if(refundNumber.indexOf('web') !== -1) {
                        refundNumber = refundNumber.split(' ')[1];
                        refundNumber = refundNumber.split('web')[0];
                        log.debug('refundNumber', refundNumber);
                    } else if (refundNumber.indexOf('cs') !== -1){
                        refundNumber = refundNumber.split(' ')[1];
                        refundNumber = refundNumber.split('cs')[0];
                    } else {
                        refundNumber = '';
                    }

                    salesOrderNumbers.push('');
                    referenceNumbers.push('');
                    refundNumbers.push((refundNumber) ? refundNumber : '');
                    docNumbers.push('');


                } else if (paymentType == 'CustPymt') {

                    var docNumber = depositObj.getSublistValue({ fieldId: 'refnum', sublistId: 'payment', line: i });

                    salesOrderNumbers.push('');
                    referenceNumbers.push('');
                    refundNumbers.push('');
                    docNumbers.push(docNumber);
                    
                } else {
                    salesOrderNumbers.push('');
                    referenceNumbers.push('');
                    refundNumbers.push('');
                    docNumbers.push('');
                }
            }

            log.debug('refundNumbers', {refundNumbers: refundNumbers});
            log.debug('salesOrderNumbers', {salesOrderNumbers: salesOrderNumbers});
            log.debug('referenceNumbers', {referenceNumbers: referenceNumbers});
            log.debug('docNumbers', {docNumbers: docNumbers});

            // load file object
            var origFileObj = file.load({
                id: fileId
            });

            var iterator = origFileObj.lines.iterator();

            var fileType = '';
            // THE FIRST ROW WILL INDICATE WHAT FILE THIS IS FROM AND WE WILL USE THAT TO IDENTIFY WHICH POSITION EACH ROW REFERENCE NUMBER WILL BE (9 FOR CYBERSOURCE, 2 FOR SHOPIFY)
            iterator.each(function (line) {

                var lineValues = line.value.split(',');
                fileType = lineValues[0];

                return false;
            });

            // THE SECOND ROW IS HEADER, SO, SKIP.
            iterator.each(function () {
                return false;
            });

            // LOOP THROUGH EACH ROW AND GET REFERENCE NUMBER.
            var cashbackCtr = 0;
            iterator.each(function (line) {

                var lineValues = line.value.split(',');
                
                    // check deposit record
                    // document # "3118/3116"
                    // cybersource = SO is the determining factor, if not SO, this is payment
                    // shopify = # is the determining factor, if no #, this is payment
                    
                if(fileType == 'CYBERSOURCE') {

                    var soNumber = lineValues[9];
                    var amount = parseFloat(lineValues[3]);
                    if(soNumber != 'SC' && amount > 0.00) {
                        if (soNumber.indexOf('SO') == -1) {
                            var paymentLine = docNumbers.indexOf(soNumber);
                            depositObj.setSublistValue({ fieldId: 'deposit', sublistId: 'payment', value: true, line: paymentLine });                            
                        } else { 
                            var custDepositLine = salesOrderNumbers.indexOf(soNumber);
                            depositObj.setSublistValue({ fieldId: 'deposit', sublistId: 'payment', value: true, line: custDepositLine });
                        }
                    } else if (amount < 0.00) {
                        // var custRefundLine = refundNumbers.indexOf(soNumber.split('SO')[1]);
                        var custRefundLine = refundNumbers.indexOf(soNumber);
                        depositObj.setSublistValue({ fieldId: 'deposit', sublistId: 'payment', value: true, line: custRefundLine });
                    } else {
                        var amount = lineValues[3];
                        depositObj.setSublistValue({ fieldId: 'account', sublistId: 'cashback', value: 610, line: cashbackCtr });
                        depositObj.setSublistValue({ fieldId: 'amount', sublistId: 'cashback', value: parseFloat(amount), line: cashbackCtr });
                        cashbackCtr++;
                    }

                } else if (fileType == 'SHOPIFY') {

                    var refNumber = lineValues[2];
                    var amount = parseFloat(lineValues[8]);
                    if(refNumber != 'SC' && amount > 0.00) {
                        if(soNumber.indexOf('#') == -1) {
                            var paymentLine = docNumbers.indexOf(soNumber);
                            depositObj.setSublistValue({ fieldId: 'deposit', sublistId: 'payment', value: true, line: paymentLine });       
                        } else {
                            var custDepositLine = referenceNumbers.indexOf(refNumber.split('#')[1]);
                            depositObj.setSublistValue({ fieldId: 'deposit', sublistId: 'payment', value: true, line: custDepositLine });
                        }
                    } else if (amount < 0.00) {
                        log.debug('test 2', {
                            test: refundNumbers.indexOf(refNumber.split('#')[1])
                        });
                        var custRefundLine = refundNumbers.indexOf(refNumber.split('#')[1]);
                        depositObj.setSublistValue({ fieldId: 'deposit', sublistId: 'payment', value: true, line: custRefundLine });
                    } else {
                        var amount = lineValues[8];
                        depositObj.setSublistValue({ fieldId: 'account', sublistId: 'cashback', value: 610, line: cashbackCtr });
                        depositObj.setSublistValue({ fieldId: 'amount', sublistId: 'cashback', value: parseFloat(amount), line: cashbackCtr });
                        cashbackCtr++;
                    }

                }


                return true;
            });

            origFileObj.folder = 12200;
            origFileObj.save();

            var depositId = depositObj.save();
            log.debug('saved', 'id: ' + depositId);

        } catch (e) {
            log.error("Error", e);
        }
    }

    function summarize(summary) {
        // if (summary.isRestarted) {
        //     log.audit('SUMMARY isRestarted', 'YES');
        // } else {
        //     log.audit('SUMMARY isRestarted', 'NO');
        // }

        // log.audit('summarize', summary);
    }

    return {
        getInputData: getInputData,
        map: map,
        summarize: summarize
    }
});
