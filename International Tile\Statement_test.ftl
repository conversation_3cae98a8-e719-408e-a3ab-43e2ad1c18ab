<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdfset>
    <#if statements?has_content>
        <#list statements as statement>
            <pdf>

                <head>
                    <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
                        src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
                        src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
                    <#if .locale=="zh_CN">
                        <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                            src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
                        <#elseif .locale=="zh_TW">
                            <link name="NotoSansCJKtc" type="font" subtype="opentype"
                                src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}"
                                bytes="2" />
                            <#elseif .locale=="ja_JP">
                                <link name="NotoSansCJKjp" type="font" subtype="opentype"
                                    src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}"
                                    bytes="2" />
                                <#elseif .locale=="ko_KR">
                                    <link name="NotoSansCJKkr" type="font" subtype="opentype"
                                        src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}"
                                        bytes="2" />
                                    <#elseif .locale=="th_TH">
                                        <link name="NotoSansThai" type="font" subtype="opentype"
                                            src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}"
                                            bytes="2" />
                    </#if>

                    <macrolist>
                        <macro id="nlheader">
                            <table class="header" style="width: 100%;">
                                <tr>
                                    <td rowspan="2">
                                        <#if companyInformation.logoUrl?length !=0><img
                                                src="${companyInformation.logoUrl}" style="float: left; margin: 7px" />
                                        </#if><span
                                            class="nameandaddress">${companyInformation.companyName}</span><br /><span
                                            class="nameandaddress">${companyInformation.addressText}</span>
                                    </td>
                                    <td align="right"><span class="title">${record@title}</span></td>
                                </tr>
                                <tr>
                                    <td align="right">${statement.trandate}</td>
                                </tr>
                            </table>
                        </macro>
                        <macro id="nlfooter">
                            <table class="footer" style="width: 100%;">
                                <tr>
                                    <td align="right">
                                        <pagenumber /> of
                                        <totalpages />
                                    </td>
                                </tr>
                            </table>
                        </macro>
                    </macrolist>
                    <style type="text/css">
                        * {
                            <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                            <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                            <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                            <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                            <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                            <#else>font-family: NotoSans, sans-serif;
                            </#if>
                        }

                        table {
                            font-size: 9pt;
                            table-layout: fixed;
                        }

                        th {
                            font-weight: bold;
                            font-size: 8pt;
                            vertical-align: middle;
                            padding: 5px 6px 3px;
                            background-color: #e3e3e3;
                            color: #333333;
                        }

                        td {
                            padding: 4px 6px;
                        }

                        td p {
                            align: left
                        }

                        b {
                            font-weight: bold;
                            color: #333333;
                        }

                        table.header td {
                            padding: 0;
                            font-size: 10pt;
                        }

                        table.footer td {
                            padding: 0;
                            font-size: 8pt;
                        }

                        table.itemtable th {
                            padding-bottom: 10px;
                            padding-top: 10px;
                        }

                        table.body td {
                            padding-top: 2px;
                        }

                        td.addressheader {
                            font-weight: bold;
                            font-size: 8pt;
                            padding-top: 6px;
                            padding-bottom: 2px;
                        }

                        td.address {
                            padding-top: 0;
                        }

                        span.title {
                            font-size: 28pt;
                        }

                        span.number {
                            font-size: 16pt;
                        }

                        div.remittanceSlip {
                            width: 100%;
                            /* To ensure minimal height of remittance slip */
                            height: 200pt;
                            page-break-inside: avoid;
                            page-break-after: avoid;
                        }

                        hr {
                            border-top: 1px dashed #d3d3d3;
                            width: 100%;
                            color: #ffffff;
                            background-color: #ffffff;
                            height: 1px;
                        }
                    </style>
                </head>

                <body header="nlheader" header-height="10%" footer="nlfooter" footer-height="20pt"
                    padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
                    <table style="width: 100%; margin-top: 10px;">
                        <tr>
                            <td class="addressheader" colspan="3">${statement.billaddress@label}</td>
                        </tr>
                        <tr>
                            <td class="address" colspan="3">${statement.billaddress}</td>
                        </tr>
                    </table>

                    <table class="body" style="width: 100%;">
                        <tr>
                            <th align="right">${statement.amountDue@label}</th>
                        </tr>
                        <tr>
                            <td align="right">${statement.amountDue}</td>
                        </tr>
                    </table>
                    <#if statement.lines?has_content>

                        <table class="itemtable" style="width: 100%; margin-top: 10px;">
                            <!-- start items -->
                            <#list statement.lines as line>
                                <#if line_index==0>
                                    <#assign startdate = line.datecol>
                                    <thead>
                                        <tr>
                                            <th colspan="3">${line.datecol@label}</th>
                                            <th colspan="12">${line.description@label}</th>
                                            <th align="right" colspan="3">${line.charge@label}</th>
                                            <th align="right" colspan="4">${line.payment@label}</th>
                                            <th align="right" colspan="4">${line.balance@label}</th>
                                        </tr>
                                    </thead>
                                </#if>
                                <tr>
                                    <td colspan="3">${line.datecol}</td>
                                    <td colspan="12">${line.description}</td>
                                    <td align="right" colspan="3">${line.charge}</td>
                                    <td align="right" colspan="4">${line.payment}</td>
                                    <td align="right" colspan="4">${line.balance}</td>
                                </tr>
                            </#list><!-- end items -->
                        </table>

                        <table class="aging" style="width: 100%; margin-top: 10px;">
                            <tr>
                                <th>${statement.aging1@label}</th>
                                <th>${statement.aging2@label}</th>
                                <th>${statement.aging3@label}</th>
                                <th>${statement.aging4@label}</th>
                                <th>${statement.aging5@label}</th>
                                <th>${statement.agingbal@label}</th>
                            </tr>
                            <tr>
                                <td>${statement.aging1}</td>
                                <td>${statement.aging2}</td>
                                <td>${statement.aging3}</td>
                                <td>${statement.aging4}</td>
                                <td>${statement.aging5}</td>
                                <td>${statement.agingbal}</td>
                            </tr>
                        </table>
                    </#if>

                    
                    <#assign _suiteletURL=("https://tstdrv2243445.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=249&deploy=1&compid=TSTDRV2243445&h=1dcb8b38f1538009cc6d&cust_id="+customer.id+"&startdate="+startdate+"&statementdate="+statement.trandate) />
                    <#include _suiteletURL />

                    <#if preferences.RETURNFORM && remittanceSlip??>

                        <hr />
                        <div class="remittanceSlip">
                            <table style="width: 100%; margin-top: 10px;">
                                <tr>
                                    <td><span class="nameandaddress">${companyInformation.companyName}</span></td>
                                    <td align="right"><span class="number">${remittanceSlip@title}</span></td>
                                </tr>
                            </table>

                            <table style="width: 100%; margin-top: 10px;">
                                <tr>
                                    <th>${remittanceSlip.customername@label}</th>
                                    <th>${statement.trandate@label}</th>
                                    <th>${statement.amountDue@label}</th>
                                    <th>${remittanceSlip.amountPaid@label}</th>
                                </tr>
                                <tr>
                                    <td>${companyInformation.addressText}</td>
                                    <td>${statement.trandate}</td>
                                    <td align="right">${statement.amountDue}</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </table>

                            <table style="width: 100%;">
                                <tr>
                                    <th>${remittanceSlip.ccinfo@label}</th>
                                    <th>${remittanceSlip.companyaddress@label}</th>
                                </tr>
                                <tr>
                                    <td>${remittanceSlip.ccinfo}</td>
                                    <td>${companyInformation.addressText}</td>
                                </tr>
                            </table>
                        </div>
                    </#if>
                </body>
            </pdf>
        </#list>
        <#else>
            <pdf>

                <head></head>

                <body>

                    <p>Please use Single Currency Advanced Printing Template</p>
                </body>
            </pdf>
    </#if>
</pdfset>