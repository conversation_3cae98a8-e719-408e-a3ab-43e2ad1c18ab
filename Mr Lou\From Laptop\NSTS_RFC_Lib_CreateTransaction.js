/**
 * Copyright (c) 1998-2017 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * NetSuite, Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with NetSuite.
 * 
 * Create RMA  and SO from selected lines
 * 
 * Version    Date            		Author           	Remarks
 * 1.00       13 April 2017      	rilagan		   	Initial version.
 * 1.00       13 April 2017      	vbien		   	Create RMA and SO
 * 1.20		  13 April 2021			jdgonzal		Added Shipping Method
 * 
 */

/**
* @NApiVersion 2.0
*/
define(['N/record', 'N/search','N/error','N/format', 'N/ui/serverWidget', 'N/task','N/redirect','../library/NSTS_RFC_Lib_ObjectsAndFunctions.js','../library/NSTS_RFC_Lib_Constants.js'],
    function (record, search, error,format,serverWidget,task,redirect,lib) {

		var stSubmittedCase	= '';	
		var stCustomer = '';
		var stTransFrom = '';
		var stTransTo = '';
		var stSearchType = '';
		var stDateCreate = '';
		var stCreateTrans ='';
		var stLines ='';
		var stSelTrans ='';
		var stSelRecType ='';
		var stFieldMemo = '';
		var stError = '';
		
		var stError = '';
		function createNewTrans(context,form,objPrefRec,accountFeatures) {
			stSubmittedCase = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.CASE.ID];
			stLines = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID];
			stSelTrans = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TRANS.ID];
			stCustomer = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.CUSTOMER.ID];
			stTransFrom = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_FROM.ID];
			stTransTo = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_TO.ID];
			stSearchType = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SEARCH_TYPE.ID];
			stDateCreate = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID];
			stCreateTrans = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID];
			stSelRecType = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TYPE.ID];
			stFieldMemo = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID];
			stFieldMemo = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID];
			stShipMethod = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID];
			stShipAddress = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_ADDRESS.ID];
			
			if(lib.isEmpty(accountFeatures))
				var accountFeatures	= lib.getFeatures();

			var transactionLinesArr = stLines.split(",");
			
			if(transactionLinesArr.length > TRANS_LINES_THRESHOLD){
				var createdRecForm = '';
				/**
				 * Determines the type of record to be created based on the Create Transaction field
				 */
				if(stCreateTrans == HC_RFC_LIST_CREATE_TRANS.CREATE_RMA){
					createdRecForm = objPrefRec.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_RMA_FORM);
				}else if(stCreateTrans == HC_RFC_LIST_CREATE_TRANS.CREATE_SO){
					createdRecForm = objPrefRec.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_SO_FORM);
				}
				
				var success = forceSchedule(HC_RFC_SCRIPT_DEPLOYMENT.CREATE_TRANS_SC, stSelRecType, stSelTrans, stSubmittedCase, stLines, stDateCreate, stFieldMemo, stCreateTrans, createdRecForm);
				
				if (!success){
					var activeDeployments = getActiveDeployments(HC_RFC_SCRIPT.CREATE_TRANS_SC);
					
					log.error('Active Deployments', JSON.stringify(activeDeployments));
					
					for(var i in activeDeployments){
						log.error('Active Deployment ID', activeDeployments[i].name);
						
						if(!success && activeDeployments[i].name != HC_RFC_SCRIPT_DEPLOYMENT.CREATE_TRANS_SC){
							success = forceSchedule(activeDeployments[i].name, stSelRecType, stSelTrans, stSubmittedCase, stLines, stDateCreate, stFieldMemo, stCreateTrans, createdRecForm);
						}
					}
					
					if (!success){
						var recDeployment = record.copy({
							type: record.Type.SCRIPT_DEPLOYMENT,
							id: activeDeployments[0].id
						});
						
						var maxlen = 28;
						
						var deployName = HC_RFC_SCRIPT.CREATE_TRANS_SC;
						deployName = deployName.toUpperCase().split('CUSTOMSCRIPT')[1];
						deployName = [deployName.substring(0, maxlen), generateRandomNumber(5)].join('_');
						deployName = deployName.replace(/ /g, "").toLowerCase();
						
						log.error('Scheduled Script Deployment', 'New Script Deployment Name : ' + '"' + deployName + '"');
						
						recDeployment.setValue('isdeployed', true);
						recDeployment.setValue('status', 'NOTSCHEDULED');
						recDeployment.setValue('scriptid', deployName);
						//recDeployment.setValue('queueid', recDeployment.getValue('queueid'));
						
						var recDeploymentID = recDeployment.save();
						
						log.error('Scheduled Script Deployment', 'New Script Deployment ID : ' + recDeploymentID);
						
						var success = forceSchedule('customdeploy' + deployName, stSelRecType, stSelTrans, stSubmittedCase, stLines, stDateCreate, stFieldMemo, stCreateTrans, createdRecForm);
					}
				}

				/**
				 * Redirects the page to the Created Transaction
				 */
				redirect.toRecord({
                    type: HC_NS_RECORDS.CASE.ID,
                    id: stSubmittedCase
                });
			}else{
				try{
					var createdRec = null;
					var createdRecType = '';
					var createdRecDate = '';
					var createdRecForm = '';
					
					var caseCreatedDate = '';
					var caseCompany = '';
					var caseSubsidiary = '';
					
					/**
					 * Load the Selected Record
					 * Script Governance Usage: 10 units
					 */
					var selectedRecord = record.load({type: stSelRecType, id: stSelTrans, isDynamic: true});
					
					/**
					 * Determines the type of record to be created based on the Create Transaction field
					 */
					if(stCreateTrans == HC_RFC_LIST_CREATE_TRANS.CREATE_RMA){
						createdRecType = HC_NS_RECORDS.RETURN_AUTHORIZATION.ID;
						createdRecForm = objPrefRec.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_RMA_FORM);
					}else if(stCreateTrans == HC_RFC_LIST_CREATE_TRANS.CREATE_SO){
						createdRecType = HC_NS_RECORDS.SALES_ORDER.ID;
						createdRecForm = objPrefRec.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_SO_FORM);
					}
					
					var columnsArr = [HC_NS_RECORDS.CASE.FIELDS.CREATED_DATE, HC_NS_RECORDS.CASE.FIELDS.COMPANY];
					
					if(accountFeatures.bOneWorld){
						columnsArr.push(HC_NS_RECORDS.CASE.FIELDS.SUBSIDIARY);
					}
					
					/**
					 * Looks up the field value on the Case record
	            	 * Script Governance Usage: 1 unit
	            	 */
	            	var caseRecordLookupField = search.lookupFields({
	            		type: HC_NS_RECORDS.CASE.ID, 
	            		id: stSubmittedCase, 
	            		columns: columnsArr
	            	});
	            	/**
	            	 * Derived the looked up fields in the Case Record
	            	 */
	            	if(!lib.isEmpty(caseRecordLookupField)){
	            		if(!lib.isEmpty(caseRecordLookupField[HC_NS_RECORDS.CASE.FIELDS.CREATED_DATE])){
	            			caseCreatedDate = caseRecordLookupField[HC_NS_RECORDS.CASE.FIELDS.CREATED_DATE];
	            			
	            			if(lib.isEmpty(caseCreatedDate)){
	            				caseCreatedDate = caseRecordLookupField[HC_NS_RECORDS.CASE.FIELDS.CREATED_DATE][0].value;
	            			}
	            			
	        				var caseCreatedDateArr = caseCreatedDate.split(" ");
	        				caseCreatedDate = new Date(caseCreatedDateArr[0]);
	            		}
	            		
	            		if(!lib.isEmpty(caseRecordLookupField[HC_NS_RECORDS.CASE.FIELDS.COMPANY])){
	            			caseCompany = caseRecordLookupField[HC_NS_RECORDS.CASE.FIELDS.COMPANY][0].value;
	            		}
	            		
	            		if(accountFeatures.bOneWorld){
	            			if(!lib.isEmpty(caseRecordLookupField[HC_NS_RECORDS.CASE.FIELDS.SUBSIDIARY])){
		            			caseSubsidiary = caseRecordLookupField[HC_NS_RECORDS.CASE.FIELDS.SUBSIDIARY][0].value;
		            		}
	            		}
	            	}
	            	
	            	/**
	            	 * Checks and gets the appropriate date based on the Date of Created Transaction
	            	 */
	            	if(stDateCreate == HC_RFC_LIST_DATE_TYPE.TRANSACTION){
	            		createdRecDate = selectedRecord.getValue(HC_NS_RECORDS.COMMON.FIELDS.TRAN_DATE);
	            	}else if(stDateCreate == HC_RFC_LIST_DATE_TYPE.SYSTEM){
	            		createdRecDate = new Date();
	            	}else if(stDateCreate == HC_RFC_LIST_DATE_TYPE.CASE){
	            		createdRecDate = caseCreatedDate;
	            	}

	            	log.error('createdRecDate', createdRecDate);
	            	
	            	selectedRecDepartment = selectedRecord.getValue(HC_NS_RECORDS.COMMON.FIELDS.DEPARTMENT);
	            	selectedRecClass = selectedRecord.getValue(HC_NS_RECORDS.COMMON.FIELDS.CLASS);
	            	selectedRecLocation = selectedRecord.getValue(HC_NS_RECORDS.COMMON.FIELDS.LOCATION);
	            	selectedRecLeadSource = selectedRecord.getValue(HC_NS_RECORDS.COMMON.FIELDS.LEAD_SOURCE);
	            	selectedRecSalesRep = selectedRecord.getValue(HC_NS_RECORDS.COMMON.FIELDS.SALES_REP);
	            	selectedRecPartner = selectedRecord.getValue(HC_NS_RECORDS.COMMON.FIELDS.PARTNER);
					selectedRecCurrency = selectedRecord.getValue(HC_NS_RECORDS.COMMON.FIELDS.CURRENCY);
					
					// JLG - ADDED 01/27/2021
	            	selectedRecChannelAdvisor = selectedRecord.getValue(HC_NS_RECORDS.COMMON.FIELDS.CHNNELADVSR);

					// JLG - ADDED 04/13/2021

					selectedShippingMethod = stShipMethod;

					if(stShipMethod == '' || stShipMethod === null){
						selectedShippingMethod = selectedRecord.getValue(HC_NS_RECORDS.COMMON.FIELDS.SHIP_METHOD);
					}

					selectedShippingAddress = stShipAddress;

	            	
					/**
					 * Creates a record based on the Create Transaction field
					 * Script Governance Usage: 10 units
					 */
					createdRec = record.create({type: createdRecType, isDynamic: true});
					
					if(!lib.isEmpty(createdRecForm)){
						createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.FORM, parseInt(createdRecForm));
					}
					
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.ENTITY, caseCompany);
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.MEMO, stFieldMemo);
					
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.DEPARTMENT, selectedRecDepartment);
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.CLASS, selectedRecClass);
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.LOCATION, selectedRecLocation);
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.LEAD_SOURCE, selectedRecLeadSource);
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.SALES_REP, selectedRecSalesRep);
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.PARTNER, selectedRecPartner);
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.CURRENCY, selectedRecCurrency);
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.MARKETPLACE, 'RA-' + selectedRecChannelAdvisor);
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.CHNNELADVSR, 'RA-' + selectedRecChannelAdvisor);

					log.audit('janel - test', { test: selectedShippingMethod});

					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.SHIP_METHOD, selectedShippingMethod);
					createdRec.setValue('shipaddresslist', selectedShippingAddress);
					
					createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.STATUS, 'A');
					
					if(!lib.isEmpty(createdRecDate)){
						if(stDateCreate == HC_RFC_LIST_DATE_TYPE.SYSTEM){

							var stDate = new Date(createdRecDate);
				            var formattedDateDiff = format.format({
				                value: stDate,
				                type: format.Type.DATE
				            });
				            createdRecDate = new Date(formattedDateDiff);
						}
						createdRec.setValue(HC_NS_RECORDS.COMMON.FIELDS.TRAN_DATE, createdRecDate);
					}
	            	
					log.error('param: ' + stSubmittedCase, stSelTrans + ' | ' + stLines + ' | ' + stSelRecType + ' | ' + createdRecType);
					
					for(var i in transactionLinesArr){
						/**
						 * Finds the corresponding Line Number of the associated Line Unique Key
						 */
						var recLineNumber = selectedRecord.findSublistLineWithValue({
							sublistId: HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, 
							fieldId: HC_NS_RECORDS.COMMON.SUBLIST_FIELD.LINE_UNIQUE_KEY, 
							value: transactionLinesArr[i]
						});

						if(recLineNumber > -1){
							/**
							 * Gets the values and sets it to the created transaction accordingly
							 */
							var transactionItem = selectedRecord.getSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.ITEM, recLineNumber);
							var transactionQuantity = selectedRecord.getSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.QUANTITY, recLineNumber);
							var transactionRate = selectedRecord.getSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.RATE, recLineNumber);
							var transactionAmount = selectedRecord.getSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.AMOUNT, recLineNumber);
							var transactionTaxCode = selectedRecord.getSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.TAX_CODE, recLineNumber);
							var transactionDescription = selectedRecord.getSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.DESCRIPTION, recLineNumber);
							var transactionUnits = selectedRecord.getSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.UNITS, recLineNumber);
							
							//log.error('Item: ' + transactionLinesArr[i], transactionItem + ' | ' + transactionRate + ' | ' + transactionQuantity + ' | ' + transactionAmount + ' | ' + transactionTaxCode);
							
							createdRec.selectNewLine(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM);
							createdRec.setCurrentSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.ITEM, parseInt(transactionItem));
							createdRec.setCurrentSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.QUANTITY, transactionQuantity);
							createdRec.setCurrentSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.UNITS, transactionUnits);
							createdRec.setCurrentSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.RATE, transactionRate);
							createdRec.setCurrentSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.AMOUNT, transactionAmount);
							createdRec.setCurrentSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.TAX_CODE, transactionTaxCode);
							createdRec.setCurrentSublistValue(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM, HC_NS_RECORDS.COMMON.SUBLIST_FIELD.DESCRIPTION, transactionDescription);
							createdRec.commitLine(HC_NS_RECORDS.COMMON.SUBLIST_ID.ITEM);
						}
					}

					var createdRecId = createdRec.save({ignoreMandatoryFields: true});
					
					try{
						/**
						 * Creates a Case Related Records record for the Case and the Created Transaction
						 * Script Governance Usage: 10 units
						 */

						recRelatedRecords = record.create({type: HC_RFC_RELATED_RECORDS.ID, isDynamic: true});
						recRelatedRecords.setValue(HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_CASE, stSubmittedCase);
						recRelatedRecords.setValue(HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_RELATED_RECORDS, stSelTrans);
						recRelatedRecords.setValue(HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_CREATED_TRANS, createdRecId);
						recRelatedRecords.save();
						
						/**
						 * Redirects the page to the Created Transaction
						 */
						redirect.toRecord({
		                    type: createdRecType,
		                    id: createdRecId
		                });
					}catch(e){
						stError = _getErrorDetails(e);
			    		log.error('error', e.toString());
			    	}
				}catch(e){
					stError = _getErrorDetails(e);
		    		log.error('error', e.toString());
		    	}
			}
			
			if(stError){

				var ERROR_CSS 	= '' +
				'<style type="text/css">' +
					'.isa_error { margin: 10px 0px; padding:12px; background-color: #FFBABA; color: #D8000C;}' +						
				'</style>';
				
				stError =  ERROR_CSS + '<div class="isa_error">'+
					'<i class="fa fa-times-circle"></i>'+
						'The following errors are found: '+'<br/>'+stError+'</div>';
				return stError;
				/**var fldError = form.addField({
	                id: 'custpage_nsts_rfc_flderror',
	                type: serverWidget.FieldType.INLINEHTML,
	                label: 'Error',
	                container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
	            });
				fldError.defaultValue = ERROR_CSS + '<div class="isa_error">'+
				   						'<i class="fa fa-times-circle"></i>'+
				   						'The following errors are found: '+'<br/>'+stError+'</div>';*/

			}	
			
			//return form;
        }
		

        
        /**
		* Get Error Details
		*/
		function _getErrorDetails(objError){
			var stError = '';
			try{

			    if (typeof objError == 'object'){
			    	var stType = objError.type;
			    	if(stType == error.SuiteScriptError || stType == error.UserEventError)
			    		stError = objError.name + ' : '+objError.message;
			    	else
			    		stError = objError.toString();
			    	return stError;
			    }
			    else
			    	return objError.toString();
			}catch(err){
			}
		}
		
        return {
        	newTrans: createNewTrans
        };
        
        function forceSchedule(deploymentId, stSelRecType, stSelTrans, stSubmittedCase, stLines, stDateCreate, stFieldMemo, stCreateTrans, createdRecForm){
    		var mrTaskId = '';
    		var scTaskId = null;
    		
    		try{
    			/**
    	    	 * Script Governance Usage: 20 unit
    	    	 */
    	    	var scTask = task.create({
    	    		taskType: task.TaskType.SCHEDULED_SCRIPT,
    	    		scriptId: 'customscript_nsts_rfc_create_trans_sc',
    	    		params: {
    	    			custscript_nsts_rfc_record_type: stSelRecType, 
    	    			custscript_nsts_rfc_selected_trans: stSelTrans, 
    	    			custscript_nsts_rfc_submitted_case: stSubmittedCase, 
    	    			custscript_nsts_rfc_selected_lines: stLines, 
    	    			custscript_nsts_rfc_trans_date_ref: stDateCreate, 
    	    			custscript_nsts_rfc_memo: stFieldMemo,
    	    			custscript_nsts_rfc_creation_action: stCreateTrans,
    	    			custscript_nsts_rfc_default_trans_form: createdRecForm
    	    		}
    	    	});
    	    	
    	    	scTaskId = scTask.submit();
    		}catch(e){
        		log.error('error', e.toString());
    		}
    		
        	if(lib.isEmpty(scTaskId)){
        		return false;
        	}else{
        		var taskStatus = task.checkStatus(scTaskId);
            	
            	log.debug('Force Schedule | Map/Reduce Task Status', 'Deployment ID | Success : ' + deploymentId + " | " + (taskStatus != task.TaskStatus.FAILED));
            	
        		return (taskStatus != task.TaskStatus.FAILED);
        	}
    	}
        
        function getActiveDeployments(scriptID) {
    		var arrDeployments = [];
    		var filters = [];
    		
    		filters.push(search.createFilter({
           		name: 'scriptid',
           		join: 'script',
           		operator: search.Operator.IS,
           		values: scriptID
           	}));
    		
    		filters.push(search.createFilter({
           		name: 'isdeployed',
           		operator: search.Operator.IS,
           		values: true
           	}));
    		
    		filters.push(search.createFilter({
           		name: 'status',
           		operator: search.Operator.ANYOF,
           		values: ['NOTSCHEDULED']
           	}));
    		
    		var arrSearch = lib.searchGetAllResult({
    			type: 'scriptdeployment',
    			columns: ['scriptid'],
    			filters: filters
    		});
    		
    		arrSearch.forEach(function(row) {
    			arrDeployments.push({
    				id: row.id,
    				name: row.getValue({name : 'scriptid'})
    			});
    		});
    		
    		return arrDeployments;
    	}
        function generateRandomNumber()
        {
            var text = "";
            var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

            for( var i=0; i < 5; i++ )
                text += possible.charAt(Math.floor(Math.random() * possible.length));

            return text;
        }
		
    });