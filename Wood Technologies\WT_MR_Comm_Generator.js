/**
 *@NApiVersion 2.1
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/format', 'N/runtime'], function (search, record, format, runtime) {

    function getInputData() {

        try {

            const str_ParamterDate = runtime.getCurrentScript().getParameter({
                name: 'custscript_date_to_run'
            });

            // get date today (this script should only run on the 1st of every month)
            let obj_Date = ((str_ParamterDate) ? new Date(str_ParamterDate) : new Date());
            // get date yesterday
            obj_Date.setDate(obj_Date.getDate() - 1);

            let str_Date = format.format({
                value: obj_Date,
                type: format.Type.DATE
            });

            log.debug('Date Yesterday based on date input', str_Date);
            let accountingperiodSearchObj = search.create({
                type: "accountingperiod",
                filters: [
                    ["enddate", "on", str_Date],
                    "AND",
                    ["isquarter", "is", "T"]
                ],
                columns: [
                    search.createColumn({
                        name: "periodname",
                        label: "Name"
                    })
                ]
            });
            let searchResultCount = accountingperiodSearchObj.runPaged().count;

            if(searchResultCount) {

                let searchObj = search.load({
                    id: 'customsearch765'
                });

                return searchObj;
            } else {
                log.audit("Not End Of Quarter", `Yesterday (${str_Date}) is not end of quarter.`)
            }

        } catch (e) {

            log.error('getInputData error', e);

        }
    }

    function map(mapContext) {
        try {
            let obj_ContextValues = JSON.parse(mapContext.value);

            log.debug("obj_ContextValues", obj_ContextValues);
            let str_TranDate = obj_ContextValues.values.trandate;
            let str_Item = obj_ContextValues.values.item.text;
            let str_ItemDescription = obj_ContextValues.values.memo;
            let str_Type = obj_ContextValues.values.type.text;
            let str_TranID = obj_ContextValues.values.tranid;
            let str_Name = obj_ContextValues.values.entity.text;
            let float_CommissionAmount = Number(obj_ContextValues.values.formulacurrency);
            let int_InvoiceID = Number(obj_ContextValues.values.internalid.value);

            return mapContext.write({
                key: "1",
                value: { item_desciption: `${str_TranDate} - ${str_Item} - ${str_ItemDescription} - ${str_Type} - ${str_TranID} - ${str_Name}`, item_amount: float_CommissionAmount, invoice_internal_id: int_InvoiceID }
            });

        } catch (e) {
            log.error('Map - Error', e);
        }
    }

    function reduce(reduceContext) {

        try {
            const arr_MapValues = reduceContext.values;

            let obj_CreditMemo = record.create({
                type: record.Type.CREDIT_MEMO,
                isDynamic: true
            });

            obj_CreditMemo.setValue({
                fieldId: "entity",
                value: 6474 // 123 Five Iron Golf
            });

            obj_CreditMemo.setValue({
                fieldId: "location",
                value: 1 // Wood Technologies, LLC
            });

            let arr_InvoiceIDs = [];

            for(let i = 0; i < arr_MapValues.length; i++) {

                let obj_MapValue = JSON.parse(arr_MapValues[i]);

                log.debug("obj_MapValue", obj_MapValue);
                
                obj_CreditMemo.selectNewLine({
                    sublistId: 'item'
                });

                obj_CreditMemo.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'item',
                    value: 30212 // Five Iron Golf CM
                });

                obj_CreditMemo.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'description',
                    value: obj_MapValue.item_desciption
                });

                obj_CreditMemo.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'description',
                    value: obj_MapValue.item_desciption
                });

                obj_CreditMemo.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'amount',
                    value: obj_MapValue.item_amount
                });

                obj_CreditMemo.commitLine({
                    sublistId: 'item'
                });

                if(arr_InvoiceIDs.indexOf(obj_MapValue.invoice_internal_id) == -1){
                    arr_InvoiceIDs.push(obj_MapValue.invoice_internal_id)
                }

            }

            let int_CreditMemoId = obj_CreditMemo.save();

            record.submit

            for(let j = 0; j < arr_InvoiceIDs.length; j++) {
                record.submitFields({
                    type: record.Type.INVOICE,
                    id: Number(arr_InvoiceIDs[j]),
                    values: {
                        'custbody_linked_commission_cm': Number(int_CreditMemoId)
                    }
                });
            }


        } catch (e) {
            log.error('Reduce - Error', e);
        }
    }

    function summarize(summaryContext) {

    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
