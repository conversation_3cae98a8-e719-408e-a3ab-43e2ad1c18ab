
/* NL Base */

.iArrowLeft,
.iArrowRight		{ display: inline-block; height: 15px; width: 16px; margin: 0 2px; background: url(/images/chiles/dashboard_icons.png) no-repeat; text-decoration: none; zoom: 1; }

.iArrowLeft		{ margin: 0 20px; background-position: 0 -140px; }
.iArrowLeft:focus,
.iArrowLeft:active	{ background-position: -20px -140px; }

.iArrowRight		{ margin: 0 20px; background-position: 0 -160px; }
.iArrowRight:focus,
.iArrowRight:active	{ background-position: -20px -160px; }

.required_icon,
.label_span .required_icon	{ display: none; }

.uir-required-icon
{
	padding-left:3px;
	background: none;
	font-size:14px;
	font-weight:600;
	color:#c77f02;
	padding: 0 2px;
}


.iBullet		{ background-image: url(/images/nav/nub10t1.gif); }
.titlebar		{ background-color: #FFFFFF; }
.hideElement		{ display: none; }
.tabtextshadow		{ position: absolute; top: 1px; left: 2px; color: #FFFFFF; opacity: 0.4; filter: alpha(opacity=40); }
.tabtextshadowed	{ position: relative; top: -1px; left: 1px; }
.portletlabel		{ font-size: 8pt; font-weight: bold; }
.portletlabelDragDrop	{ font-size: 11px; font-weight: bold; cursor: move; }
.tasklinkSearchBar	{ color: #DDDDDD; background-color: #EEEEEE; } 
.crumb			{ font-size: 8pt; }
.headbarsubnolink	{ font-size: 8pt; font-weight: normal; text-decoration: none; }
.headbarsub		{ font-size: 8pt; font-weight: normal; }

.crosslinktext,
.crosslinktextul,
.crosslinktitle,
a.newbar		{ font-size: 7.5pt; font-weight: normal; padding: 0 8px; }

.crosslinktitle		{ padding: 0px; }
.crosslinktext		{ text-decoration: none; }
.ontab			{ font-size: 8pt; text-decoration: none; font-weight: bold; }
.offtab			{ font-size: 8pt; text-decoration: none; font-weight: bold; }
.ontabbottom		{ font-size: 8pt; text-decoration: none; }
.offtabbottom		{ font-size: 8pt; text-decoration: none; }
.bgontabbottom		{ font-size: 8pt; }
.bgofftabbottom		{ font-size: 8pt; }
.ontabhover		{ font-size: 8pt; text-decoration: underline; font-weight: bold; }
.offtabhover		{ font-size: 8pt; text-decoration: underline; font-weight: bold; }
.btntext		{ font-size: 8pt; text-decoration: none; }
.btntexthover		{ font-size: 8pt; text-decoration: underline; }
.helperopener		{ text-decoration: none; border: 0 0 0 0; }
.btnmenuentry		{ text-decoration: none; padding: 2px 3px 2px 3px; }
.btnmenuentryover	{ background-color: #FFFFFF; text-decoration: none; padding: 2px 3px 2px 3px; }

/* Used by the Web store for item and category descriptions and titles */

.medtext         		{ font-size: 8pt; }
.medtextbold     		{ font-size: 8pt; font-weight: bold; }
.medtextboldnolink		{ font-size: 8pt; font-weight: bold ; color: #000000 ; text-decoration: none; }

.text			{ font-size: 8pt; }
.textctr		{ font-size: 8pt; text-align: center; }
.textrt			{ font-size: 8pt; text-align: right; }
.inputrt		{ font-size: 8pt; color: #000000; text-align: right; padding-right: 2px; }
.inputrtreq		{ font-size: 8pt; color: #000000; text-align: right; padding-right: 2px; }
.textnolink		{ font-size: 8pt; text-decoration: none; }
.textul			{ font-size: 8pt; text-decoration: underline; }
.textbold		{ font-size: 8pt; font-weight: bold; }
.textboldctr		{ font-size: 8pt; font-weight: bold; text-align: center; }
.textboldrt		{ font-size: 8pt; font-weight: bold; text-align: right; }
.textboldul		{ font-size: 8pt; font-weight: bold ; text-decoration: underline; }
.textboldnolink		{ font-size: 8pt; font-weight: bold ; color: #000000 ; text-decoration: none; }
.textdark		{ font-size: 8pt; color: #FFFFFF; background-color: #666666; }
.textdarkbold		{ font-size: 8pt; color: #FFFFFF; background-color: #666666; font-weight: bold; }
.textdarkboldnolink	{ font-size: 8pt; color: #FFFFFF; background-color: #666666; font-weight: bold; text-decoration: none; }
.textdarkboldul		{ font-size: 8pt; color: #FFFFFF; background-color: #666666; font-weight: bold; text-decoration: underline; }
.textdarkctr		{ font-size: 8pt; color: #FFFFFF; background-color: #666666; text-align: center; }
.textdarkrt		{ font-size: 8pt; color: #FFFFFF; background-color: #666666; text-align: right; }
.textboldul		{ font-size: 8pt; color: #000000; font-weight: bold; text-decoration: underline; }
.textmediumbold		{ font-size: 8pt; background-color: #CCCCCC; }

.input,
.inputreadonly,
.inputtotalling,
.totallingcurrency	{ font-size: 8pt; color: #000000; }

.inputgray		{ font-size: 8pt; color: #999999; }
.inputgrayitalic	{ font-size: 8pt; color: #999999; font-style: italic; }
.inputgraybold		{ font-size: 8pt; color: #999999; font-weight: bold; }
.inputreq		{ font-size: 8pt; color: #000000; }

.textareainput,
.textareainputreq	{ border: 1px solid #D5DEE7; color: black; font-size: 8pt; }

.nlbutton			{ font-size: 8pt; color: #000000; font-weight: bold; cursor: pointer; }
.nlbuttonDisabled		{ font-size: 8pt; color: #777777; font-weight: bold; }
.nlbuttongray			{ font-size: 8pt; color: #AAAAAA; cursor: pointer; }
.nlbuttontiny			{ font-size: 7pt; color: #000000; cursor: pointer; background-color: #DDDDDD; }
.nlinlineeditbutton		{ font-size: 7pt; color: #000000; padding: 0 6px; display: block; height: 14; vertical-align: middle; background-color: transparent; border: 0; }
.nlinlineeditbuttonDisabled	{ font-size: 7pt; color: #777777; padding: 0 6px; display: block; height: 14; vertical-align: middle; background-color: transparent; border: 0; }
.editorbutton			{ font-size: 8pt; color: #000000; width: 22px; height: 22px; border: 1px solid #ECEFF6; margin: 0; padding: 0; }
.editorbuttonhover		{ font-size: 8pt; color: #000000; width: 22px; height: 22px; border-style: outset; border-width: 1px; border-color: #999999; }
.editorbuttondown		{ font-size: 8pt; color: #000000; width: 22px; height: 22px; border-style: inset; border-width: 1px; border-color: #999999; background-color: buttonhighlight; }
.bgbutton			{ font-size: 8pt; color: #000000; font-weight: bold; cursor: pointer; }
.bgbuttonDisabled		{ font-size: 8pt; color: #777777; font-weight: bold; }
.textwhite			{ font-size: 8pt; color: #FFFFFF; }
.textwhitenolink		{ font-size: 8pt; color: #FFFFFF; text-decoration: none; }
.textwhitert			{ font-size: 8pt; color: #FFFFFF ; text-align: right; }
.textmedium			{ font-size: 8pt; color: #000000; background-color: #AAAAAA; }
.textmediumctr			{ font-size: 8pt; color: #000000; background-color: #AAAAAA ; text-align: center; }
.textmediumrt			{ font-size: 8pt; color: #000000; background-color: #AAAAAA ; text-align: right; }
.mheadquicklooklauncher		{ color: #EF9C01; }
.mheadquicklooklauncher:hover	{ color: #F0C384; }
.mheadwhite			{ color: #FFFFFF; }
.mheadwhite:hover		{ color: #D5DEE6; }
.textredfld			{ color: #FF0000; font-size: 8pt ; text-decoration: none; }
.smalltext			{ font-size: 8pt; }
.smalltextpad			{ font-size: 8pt ; padding: 1px; }
.smalltextbpad			{ font-size: 8pt ; padding: 1px; font-weight: bold; }
.tinytext			{ font-size: 7pt; }
.tinytextnolink			{ font-size: 7pt ; text-decoration: none; }
.tinytextbnolink		{ font-size: 7pt ; font-weight: bold; text-decoration: none; }
.smalltextul			{ font-size: 8pt ; text-decoration: underline; }
.smalltextbul			{ font-size: 8pt ; text-decoration: underline; font-weight: bold; }
.smalltextrt			{ font-size: 8pt ; text-align: right; }
.smalltextctr			{ font-size: 8pt ; text-align: center; }
.smalltextb			{ font-size: 8pt ; font-weight: bold; }
.smalltextbrt			{ font-size: 8pt ; font-weight: bold; text-align: right; }
.smalltextbnolink		{ font-size: 8pt ; font-weight: bold; text-decoration: none; }
.smalltextinolink		{ font-size: 8pt ; font-style: italic; text-decoration: none; }
.smalltextnolink		{ font-size: 8pt ; text-decoration: none; }
.smalltextnolinkgray		{ font-size: 8pt ; text-decoration: none ; color: #999999; }

/*red and green versions of smalltext used in the "Key Performance Indicators" portlet */

.smalltextgrn			{ font-size: 8pt ; color: #025527; padding: 1px; }
.smalltextred			{ font-size: 8pt ; color: #C41D0B; padding: 1px; }
.smallgraytext			{ font-size: 8pt ; color: #666666; }
.smallergraytext		{ font-size: 8pt ; color: #666666; }
.smallergraytextbold		{ font-size: 8pt ; color: #666666; font-weight: bold; text-decoration: none; }
.smallergraytextnolink		{ font-size: 8pt ; color: #666666; text-decoration: none; }
.tinygraytext			{ font-size: 7pt ; color: #666666; }
.tinylightgraytext		{ font-size: 7pt ; color: #999999; }
.tinygraytextul			{ font-size: 7pt ; color: #666666; text-decoration: underline; }
.tinygraytextnolink		{ font-size: 7pt ; color: #666666; text-decoration: none; }
.tinygraytextnolink:hover	{ text-decoration: underline; }
.smallgraytextul		{ font-size: 8pt ; color: #666666; text-decoration: underline; }
.smallgraytextrt		{ font-size: 8pt ; text-align: right ; color: #666666; }
.smallgraytextnolink		{ font-size: 8pt ; color: #666666; text-decoration: none; }
.smallgraytextbold		{ font-size: 8pt ; color: #666666; font-weight: bold; text-decoration: none; }

/* currently used for indented links in linklist portlets. */

.smalltextsub		{ font-size: 8pt; padding-left: 20px; }

/* use with caution - text-indent adds padding for the first line only */

.smalltextsub1		{ font-size: 8pt ; text-indent: 20pt; }
.helpertext		{ font-size: 8pt ; font-family: arial,sans-serif; color: #666666; padding-left: 3px; padding-top: 0px; }
.tinywhitetextnolink	{ font-size: 7pt ; ; color: #FFFFFF; text-decoration: none; }
.dashboardtext		{ font-size: 8pt ; text-decoration: none; }
.dashboardtext:hover	{ text-decoration: underline; }

/* .pic must also have the characteristics of smalltext. This is so that if the image tag is replaced by &nbsp; the resulting row won't be very tall. */

.pic			{ text-align: center; vertical-align: top; font-size: 8pt ; font-weight: normal; }
.picctr			{ text-align: center; vertical-align: middle; font-size: 8pt ; font-weight: normal; }
.ctr			{ text-align: center; }
.rt			{ text-align: right; }
.graylt			{ background-color: #CCCCCC; }
.graymd			{ background-color: #AAAAAA; }
.graydk			{ background-color: #666666; }
.errortext		{ font-size: 8pt; color: #EE0000; background-color: #FFF4F4; }
.errortextheading	{ font-size: 8pt; color: #EE0000; background-color: #FFF4F4; font-weight: bold; }
.greytitle		{ font-size: 14pt; }
.tasktitle		{ font-size: 14pt; }
.tasktitlemed		{ font-size: 13pt; font-weight: bold; }
.taskstatus		{ font-size: 13pt; }
.bigboldtext		{ font-size: 16pt; font-weight: bold; }
.buttontext		{ font-size: 8pt ; text-decoration: none ; color: #000000; }
.bigbuttontextb		{ font-size: 10pt ; text-decoration: none ; color: #FFFFFF; font-weight: bold; }
.adtext			{ font-size: 8pt; color: #000000; text-decoration: none; }
.mediumtext		{ font-size: 9pt; }

a.testDriveHeaderQuickLooks		{ text-decoration: none; color: #FBCF67; }
a.testDriveHeaderQuickLooks:hover	{ color: #EF9C01; }
a.testDriveHeader			{ text-decoration: none; color: #FFFFFF; }
a.testDriveHeader:hover			{ color: #EF9C01; }

.helpcenterlinks	{ text-decoration: none; color: #828181; }
.helpcenterlinkactive	{ text-decoration: none; color: #000000; }
.helpcenterlinks:hover	{ text-decoration: none; color: #000000; }


/* list header */

.listtable		{ background-color: white; }
.listborder		{ border-style: solid; border-width: 1px; border-color: #B4B4B4; }
.listheadertd		{ border-width: 0 0 1px 1px; }
.listheadertdleft	{ border-width: 0 0 1px 0; }
.listheadertdmid	{ border-width: 0 0 1px 0; }
.listheader		{ height: 100%; padding: 2px; vertical-align: top; background: url(/images/core/list/header_bg_t.png) repeat-x left top; }
.listheaderleft		{ height: 100%; padding: 2px; vertical-align: top; background: url(/images/core/list/header_bg_t.png) repeat-x left top; }
.listheadermid		{ height: 100%; padding: 2px; vertical-align: top; background: url(/images/core/list/header_bg_t.png) repeat-x left top; }
.listheaderportlet	{ height: 100%; padding: 0 2px 2px 2px; vertical-align: top; border-style: solid; border-width: 0 1px 1px 1px; border-color: #C2C2C2 #8F8F8F #888888 white; margin-left: 1px; background-image: url(/images/nav/listheaderbg2.gif); }

.listheadertdwht	{ height: 100%; padding: 1px; border-style: solid; border-width: 0 0 1px 0; background-color: #FFFFFF; border-color: #CCCCCC white white #CCCCCC; margin-left: 1px; }
.listheadertextb	{ color: #666666; font-size: 11px; font-weight: bold; }
.listheadertextbrt	{ color: #666666; font-size: 11px; font-weight: bold; text-align: right; }
.listheadertextbctr	{ color: #666666; font-size: 11px; font-weight: bold; text-align: center; }
.listheaderreq		{ width: 11px; height: 11px; font-size: 0px; background: url(/images/core/list/required.png); display: inline-block; }
.listheadersort		{ width: 0px; height: 12px; font-size: 0px; padding-right: 10px; }
.listheadersortup	{ width: 6px; height: 6px; font-size: 0px; background: url(/images/core/list/sort_up.png); display: inline-block; }
.listheadersortdown	{ width: 6px; height: 6px; font-size: 0px; background: url(/images/core/list/sort_down.png); display: inline-block; }

/* form */

.formtabtext	{ font-size: 11px; font-weight: bold; outline-style: none; }
.formsubtabtext	{ font-size: 8pt; outline-style: none; }

/* 2nd level tab bar */

.tabur	{ position: relative; float: right; font-size: 0px; width: 4px; height: 3px; background: url(/images/forms/border/tab_corner1.png); }
.tabll	{ font-size: 0px; width: 12px; height: 12px; background: url(/images/forms/border/tab_corner2.png) 0px 12px; }
.tablr	{ font-size: 0px; width: 12px; height: 12px; background: url(/images/forms/border/tab_corner2.png) 12px 12px; }

/* 3rd level tab bar */

.subtabbarul		{ font-size: 0px; width: 4px; height: 4px; background: url(/images/forms/border/subtab_corner4.png) 6px 6px; }
.subtabbarur		{ font-size: 0px; width: 6px; height: 6px; background: url(/images/forms/border/subtab_corner4.png) 0px 6px; }
.subtabbarinnerul	{ font-size: 0px; width: 2px; height: 2px; background: url(/images/core/common/corner_r3_t_gray_w.png) #CBCBCB 1px 0px; }
.subtabbarinnerur	{ font-size: 0px; width: 2px; height: 2px; background: url(/images/core/common/corner_r3_t_gray_w.png) #CBCBCB 1px 0px; }
.subtabbarinnerll	{ font-size: 0px; width: 2px; height: 2px; background: url(/images/core/common/corner_r3_t_gray_w.png) #CBCBCB 1px 2px; }
.subtabbarinnerlr	{ font-size: 0px; width: 2px; height: 2px; background: url(/images/core/common/corner_r3_t_gray_w.png) #CBCBCB 1px 2px; }

.subtabul	{ font-size: 0px; width: 4px; height: 4px; background: url(/images/forms/border/subtab_corner1.png) 0px 0px; }
.subtabur	{ font-size: 0px; width: 7px; height: 7px; background: url(/images/forms/border/subtab_corner3.png) 7px 0px; }
.subtabll	{ font-size: 0px; width: 7px; height: 7px; background: url(/images/forms/border/subtab_corner3.png) 0px 7px; }
.subtablr	{ font-size: 0px; width: 7px; height: 7px; background: url(/images/forms/border/subtab_corner3.png) 7px 7px; }

/* unrolled form tab */

.unrollformtabbordercorner	{ width: 8px; height: 8px; font-size: 0px; }

/* unrolled form subtab */

.unrollformsubtabbordercorner	{ width: 7px; height: 7px; font-size: 0px; }

/* unrolled form subtab collapsed */

.unrollformsubtablcollapse	{ width: 3px; font-size: 0px; background: #efefef; }
.unrollformsubtabrcollapse	{ width: 3px; font-size: 0px; background: #efefef; }
.unrollformsubtabtcollapse	{ height: 3px; font-size: 0px; background: #efefef; }
.unrollformsubtabbcollapse	{ height: 2px; font-size: 0px; background: #efefef; }

/* 3d styles for our new and imporoved list header blocks (obsolete) */

.header3dtd,
.header3dtdleft,
.header3dtdmid,
.header3dtdright,
.header3dtdportlet,
.header3dtdstartrow	{ vertical-align: top; height: 100%; border-style: solid; border-color: #B7B7B7; background: url(/images/nav/3dlistbot.gif); background-repeat: repeat-x; background-position: bottom left; padding: 0px 0px 3px 0px; }

.header3dtdstartrow	{ border-width: 1px 1px 1px 1px; }
.header3dtd		{ border-width: 1px 1px 1px 0px; }
.header3dtdleft		{ border-width: 1px 0px 1px 0px; }
.header3dtdmid		{ border-width: 1px 0px 1px 0px; }
.header3dtdright	{ border-width: 1px 1px 1px 0px; }
.header3dtdportlet	{ border-width: 0px 1px 1px 0px; }

.header3ddiv,
.header3ddivctr,
.header3ddivrt,
.header3ddivcontinue	{ background-image: url(/images/nav/3dlisttop.gif); background-repeat: repeat-x; background-position: top left; padding: 3px 3px 0px 3px; }

.header3ddiv,
.header3ddivctr,
.header3ddivrt		{ border-left: 1px solid white; }

.borderbox		{ box-sizing: border-box; -moz-box-sizing: border-box; -webkit-box-sizing: border-box; }

.i_dropdownarrow,
.i_createnew,
.i_dropdownlink,
.i_options,
.i_options_focus,
.i_calendar,
.i_calendar_focus,
.i_timer,
.i_timer_focus,
.i_timecalc,
.i_timecalc_focus,
.i_colorpicker,
.i_colorpicker_focus,
.i_list2,
.i_popupsearch				{ background-image: url(/images/chiles/sprite_field_widgets_15.png); height: 22px; width: 22px; display: inline-block; vertical-align: middle; zoom: 1; }

.ddarrowSpan				{ position: relative; top: -1px; left: -12px; vertical-align: middle; }
.i_dropdownarrow			{ background-position: -100px -50px; }
.i_createnew				{ background-position: -100px -600px; }
.i_dropdownlink				{ background-position: -100px -150px; }
.effectDisabled .i_dropdownarrow	{ background-position: -200px -50px; }
.effectDisabled .i_createnew		{ background-position: -200px -600px; }
.effectDisabled .i_dropdownlink		{ background-position: -200px -150px; }
.i_options				{ background-position: -100px -150px; }
.i_calendar				{ background-position: -100px -250px; }
.i_timer				{ background-position: -100px -850px; }
.i_timecalc				{ background-position: -100px -800px; }
.i_colorpicker				{ background-position: -100px -900px; }
.i_list2				{ background-position: -100px -950px; }
.i_popupsearch				{ background-position: -100px -1050px; }


/*
	@authors Prague UI - fhladik, jpesek, pvomacka
	since 19.1 we are changing icons into base64 encoded instead of sprite - to be able to change size in various places
	and these icon will be unified with those on portlets (originally in portlets.css)
*/

.ns-chart-control-panel-wrapper {
	display: flex;
	margin-bottom: 16px;
	flex-wrap: wrap;
}

.ns-chart-control-area {
	display: flex;
}

.ns-chart-control-area--icons {
	border: 1px solid #ccc;
	border-radius: 4px;
}

.ns-chart-control-area--icons .ns-chart-icon:first-of-type {
	border-radius: 3px 0 0 3px;
}
.ns-chart-control-area--icons .ns-chart-icon:last-of-type {
	border-radius: 0 3px 3px 0;
}

.ns-chart-control-area--dropdown {
	order: 0;
	margin-right: auto;
	flex-grow: 1;
}
.ns-chart-control-area--icons  {
	order: 1;
	margin-left: 10px;
}

.ns-chart-control-area--icons-type {

}

.ns-chart-control-area--icons-mode {

}

.ns-chart-icon {
	height: 30px;
	width: 40px;
	background: 50% 50% no-repeat;
	background-size: contain;
	cursor: pointer;
}

.ns-chart-icon:nth-of-type(n+2) {
	border-left: 1px solid #ccc;
}

.ns-chart-icon-selected {
	background-color: #006aff;
	cursor: auto !important;
}
.ns-chart-icon-area								{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAABANJREFUeNrsl01oHHUYxv+zM7uZfDSbzybsJiGbj645SEs9tCAKpagoRBDEk4ilKFjagpfeKrTnHkUhh1LwoIIXLyp40F482IIohDQEEsgSg0m2aZrdzWY32fH3DLMhpsluUhOSQ154MjPvvDPz/J/347+xPM8zR9lC5ojbMcFjgscEq5gzMjKy12feBNfBALgPboPpo6LgJ+AH27YvNDQ0xDm/bFnWrxw7DkzBPcS+Dr5obGzM9/X1uZA0mUzGjI+PJ/DfBFcPU8FmcDcSiWyQk6GiaW1t1ekV8MJhErwB4olEYoNc2WKxmCHNwjeKOQyCMTWFUivFthqqmt7eXos9/TQkPw/cr4ZCoV+4XhQ4/xnf2YMiqMaoi8fj7k4BLS0tprlZVWAuQOizoLvP1dXVpQTIv8L1Q/DufjfJSfCp1ONDbqXAcDjslyVkbhE7Sjn86LpuVs5sNhudnJx8r1AofMvlefBgvxT8ANR3dHS41V60trZmIGfX19ePDQ0NfVcmJ8O3NDAw8DWpzqHwV3uZHpUIRsBHNTU1eRSsSm5pack4jmO6u7t/3y6mtrY2w0J/YhFJLq/tB8Fz4FR7e3tV9WZnZ836+rppamoS2bYduy0WG0XZSU7vgDf+L8FLpGNNDVDJpNzc3JzfJHzcW11dfbFSPLX5PaMqox0JvF+NoD08PLydX/PknhRpa2t7pl7y+by/iywsLJhUKmUoA8MAN9SYxb1oqVTK4fuHBXrbNNMqTTRB48RR/UOeeYe4k8EM7QWa/DmwUqmLL2q0bFZPKZyfnzfpdNonuLHFoFxXV5ffxapBOtXL5XJvraysvAyR+9TvH1tfji9NI92bmZk5u7i4eJ6yuLUlpADxR9TrlzsR/Jg0FKLRaCQYE4YxoY+rIw0z0d/mNKSFsmlHgbAVKHyC595GyWmQfiZ1tr3e09PzgKZ6qDFULBZrUd6hRBq57mGBCXzbEuwCrym9rMIQaCYmJvxzRoWBdMWaEUk6VoqGpDjPJyH4W4V4j8U+4fTJJveo/qDwaSfY5E+BYlAD2vidzs5OS2mdmpryn0gmk36t7XoHIN3Ag+BF3tPMAjOkrAaVTnAMQ6ykb+LPgqfEPqbJ/uaYKb+DTP0pgn+pdjcVcR7pLdWU0ioF+/v790Ru0xZoqctJ20sSC3iQKUlliGqwC6HgnlleXtb9x5BM8b1HZGLagbVqIVxeNcRcAs3Y2JherMHrz7fnMf3yEcmAiNIp+8/PIflFVsOe71nUeRNoBWfgUXQIcDUqtCoFU5h+MMR3VXO7seDn2I73tBAhyJKt0hIPpoHtyFleYbAlaQz440ONcRhWJoxIIWdwcPBo/1cnOctSP2+agvo6EIL/CjAAz+CV4biZNOQAAAAASUVORK5CYII='); }
.ns-chart-icon-area.ns-chart-icon-selected		{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAt5JREFUeNrsl0tIVGEUxx0bNUlLQ7NSLEMsekG4iAgSomIKKoiCNkUthHQRLaJFy2hTiyBaFFGrSGgRJNELooeRQW0iosRHVKaZZWAPNc25/U/9Bi6Dc+dOzuAs5sCPucw997v/75zznTMTcBwnK50tOyvNLSMwIzAjMAUCN4tbokNcEJUpVWh9MAEanH/WL1q57hBlCa7jm0ScNyGoRdSJWlHPd2dSJTDgc5IUixciLPaIn657x0RILBNtU1WDR0SFOB4lzuycGBNNojzZAv1EcL5oF8/E4Rg+IcRfFTvFArFBVHG/S9wWHxMVGPTh0yBmiPMePvby9aKOz21kp5v7tWK5uCheJVPgHHFIPCSKXvZJzBS7xFvxWAxzr0BsFPvEadGTrBrcy+JXfKw1TeSKz+KuS5zZD3FH5LCB7GQItJfVizfUn5fliyKuu2P4DIknYpFYnQyBtkiNuG79PM46q1zXVR5+XUR4h6ierMD9YkTciLNGJZvpFN+pNS9roRwOiJX/22as7voo9KPRz4hZpLQccePiEeKsxk6IZjEa471FnPhiZroNgQH8rRS+RGo4lsDt4hoN+p6rJq1VrBAlRMHoFS9ZeLo4iE8vbaXZ41AtJoqFbNLh85f4IFpjCbS0rhFbSXOZ2MLnIC8fYJdDUc/m8KxFcyH19j5OmVnG8hBtPXcuA6Jwoj5YwRS4ibjZvMRO6lMf02CMOrPUnSSVlzz8bb5/m+AwmdWYwCWc1jF23MhOLpPWEDt8QD/za/20nEZqdYDolFIK4wTgq8v3NfUXsXZL8ShpiZil75TlH3FLfUZuIrNU7aZ28xA1SDACBKDANdEsmu+o6fviuQlsQ5BDqPsYcevEPJw7J/mjJB8xv4la2DVqczkkpfxkq+bw/G3uJrAJUZEWUshMHeH4903BX5ESJs5aE3iWkxTg5jCDv4eUTKkFacZpa0HXAXF8zNxwvP9gyRb4R4ABACh6sw5kj3RJAAAAAElFTkSuQmCC'); }
.ns-chart-icon-line								{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAglJREFUeNrs2E9Ik3Ecx/GtZss/FEEUmKBmRpcgPBSehIqgS0qk4KHURCFB8dQpEgRPImSgh1F0CDsIIhUoSB4MLx4E0chC0YhALSkn02ll6/2Dz0B2yGdte54H2Q9ejD3bfvs8z+/3fH+/zRuJRDxubgc8Lm/pgOmA6YB7NF8gEIj3M9fRgjMYQzs+u+UK3sMQLmAR9XiDk24IeA29CnhOz8tQjAdOBzyGp5hHNYI6/hbP0aTQjgW8jzwN6XrMaw/xE3045UTAXDTjla5YbPuk4CV45ERAc2Nko+0f73mBAc3JQ3YGPIFWvMbUHu/9ghz47Qx4R1/62EJfP3BYV9uWgGaoGvABoxb6moEXRXYFvISzKi9WdrUf9XjeroB1CKvOWWkLWEWlHQHNvKvSzbFisa+wysxlFW5/KgNe0WTvj7O/boygB7OqjykJ2KjhGo6zvxDKcRdreKJ5nNSAZkm7ipfY/I8+t/BMa7ZpNxLaD2qRN2f5CwWaPwfRleDomJvmHTpwGss4oqUzCzvYwFcVebMRmcRSbMBpZOw6ZjaftzSHEmnmhG+iEzUKZY5901U2o5epnVJ0efyjcjWBQYybgO+1nJla911nEk5SlZhDBY4qzLbm6W8V9QwFP45CTa1S1ErIpzttMsU/LYK79pDRFlHgbS2Tc6oApuXjIm5703997PeAfwUYAPF/bwLaPmJ5AAAAAElFTkSuQmCC'); }
.ns-chart-icon-line.ns-chart-icon-selected		{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAcxJREFUeNrs2M8rw3Ecx3FTNmZZJAr5kQwHpRQp7STlgpKLoiQHTg5ujg7uXElxEgeSRPkTrJXya0xC04jLtOZHX89P3grJvl/bvlvapx5tfff9vr+v73f7vD/fZtE0LSOVR2ZGio90wHTAdMAEBOzEFnyYQ3lCE6o+aMCo9j4C2JH3Jyg2WEc3Izt3SKBNOGWbW7bNJjtgPi7hQ963zxbxirpkBpyWO+X+4bNKhLGH0mQELEEI67/s0y8XsJKMgFNy8sYo+60iCGs8A0ZrM0UYxwa8Ufa9ggM2M/vgoJx0RketB2Qj16yAVozgCLs6au3DgmqzArbAhXnVz3XUOpbXBrMCDiGMJZ21/LhDnxlLnUNay7LBWTcpM34MtkS2mW45Ua/BgurCtuVYP4YTFVCtt7ew/6FoNobgkaCueAcsQwRzMV59rQSciKWORS3yMlufUYkx1MtsPIzh550Fz6dOcIM8lMCOVzwiKE3+FHsIfJ8kT9rXcYGeOC1VNVjDo9RW57rGGc5xI9/Wx1BPRQdYQBcK1B30ynKmet29XEk4zs/FTuQgghBepKlnyd0sRBXa0Yo2OS6kAjbJrU2lUYFmDFjSf33894BvAgwAgDaUkCX6XA8AAAAASUVORK5CYII='); }
.ns-chart-icon-column							{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAFZJREFUeNrsl0EKACAIBDV6q4/ys/aBSIqwDjNnwYFlBTUi5GeafA6CCL6m7wy7+6zyerLYzIgYQQTLW5xwreFEjCCCnJnCE0TECCKYoDzuCCK4ZggwAB/VDDs+ISCpAAAAAElFTkSuQmCC'); }
.ns-chart-icon-column.ns-chart-icon-selected	{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAFBJREFUeNrs1EkKACEMBEAj/v/LmQ94EVwyUH3VQ0HTicxsldNb8QACvs5Y/D+bfKgYEPBHK76+cBUDAjozh0+QigEBN684DrypGLB0PgEGAGvmBkQb18ccAAAAAElFTkSuQmCC'); }
.ns-chart-icon-bar								{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAG1JREFUeNrsl0EKgDAMBLviW/OofDbmBVIs0VRmz4UOKTukiojROcdoHgAB/DrnzCF3L6m6mYknBvAXJcmoNWC27fEFaYD2mhElARDN7KSZVa2UaGZmK6l+4pWVipK8OrHtJig+7gACeJ9LgAEAXRUZezmKF4cAAAAASUVORK5CYII='); }
.ns-chart-icon-bar.ns-chart-icon-selected		{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAE5JREFUeNrs1EEKACEQA0Ej/v/L4xeEZVG0cp5Dk6GTqmonp7fDAxDg7ozFu79UjxcDvEWSaHDjzIQkAM2MBr/PTEgC0MxoEOCjgFOAAQDHegZIzovKQQAAAABJRU5ErkJggg=='); }
.ns-chart-icon-pie								{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAThJREFUeNrsl7EKglAUhjVagggcg2qI5ihHq7nHqCF8hMpHyEewBt8inIVWXyCcmnuF2y+cQETC9JyQ8IdvKbt9XG73/OlKKa3OaWk1TyP494Ltog96npf3cg9MwAj0gQEcEIMIhCAAj+wHbdvmFcxEByaYgWHmvS6YEhtwAz64ACW2g6l0wBIsCj5vEclOn8BT8gzqX8qlswcHWkNM0Cwpl5bcSQn26MxVzRYMJAQnOT+IMknO41pCcMR4va0kBPuMgnMJQYNRcCxxDzp0Cdd2FseM3xtLCEaMgpGEYMgoGEoIBjT4q+ZGa7ELPqiVVI2fV7+4ZnFSmdwKci6tIVYWFFUmt6Tc6dtOWKYPJn3uCO40+K0CZ+6nhfW9k2dwpcG/ovE1Tt1zHyt/4QLa/HFvBBvBz3kJMAAQlDkGmCoL0QAAAABJRU5ErkJggg=='); }
.ns-chart-icon-pie.ns-chart-icon-selected		{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAS5JREFUeNrslzsKwkAQhrNiI2hAO0EtJAdQSx+117DQKyhW1rmDhdewDrHNDVJZ22i//gsjBLFIdmdUNANfEbLZ/Zjs7ENprb1vjor35VEK/rxg1fF7HwSgB9qgCbYgBQmIwBGcbQdQllWswAgMQPfp3e7p+QQOYA/0OzJYA1Mwydl+TJhMh+AiOQdVQblsrMGG+hATHFnKZSWXUoI+zTnXWICOhGDwoiBswszHuYRgj3F5m0kIthkFhxKCTUbBvsRCfQV1JsEbaHBnMGXMYCrxixNGwURCMGIUjCQEj7Txu8aJ+mIXPNOpxDUOhY5fpooLYKo+1PYRUh+5xywqaGhZSob0rSct+MjkCsQ5xGJqq2zGUo734g5t/DPavvqZde6jR/7yVlcK/o3gXYABAGb+vsKRTrRPAAAAAElFTkSuQmCC'); }
.ns-chart-icon-cont,
.ns-chart-icon-continuous						{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAY1JREFUeNrsl7FKw1AUhpOSTbR2c5K4urR9AYmzS7opONjZRZ9A+wR1zuIVREd1cBadHLu6BV9ARVyN/4H/wjGUC7WNuUMOfDQ3uWl+/ntzzklYFEXgc7QCz6MROG9EWZbpcQ8koAtijlfV9Zw8glswqVwgfw/ACUXp+C6N1zkn4XwRewHOwHtVAt9KLul4pQjtsJ4bU6gwqkJoq/RAWbZj0Ach2ADbig7PDTlXh3X0qIqXxPDBA7rg2ls55w8oWJz74LU2GIMHx6rMLLBDR/I/3C/LecqlHqnzCf+vtwiBi9gzVmhfuS9uPoHUpzw4oUjD8TK4nMfJqhL1kEgs0cnYt0pilEhx8t7HUmfUy7PJfepdLRZRzypXJj42C3vgi8fXs+TI/xIoOXGfx2t8s71rt6Q0XvF4B5z72A8eghfVQY19EygVZxd8ciyNxY0rR0Y1NMlSbbbY9K6wFKbcAnflnqAOgVZkl+7ZMphOq9t1fpPkrNvOTioK6g9D7PfQrxwZNh/ujcBGoDt+BBgAGI5P6Ulx3kgAAAAASUVORK5CYII='); }
.ns-chart-icon-cont.ns-chart-icon-selected,
.ns-chart-icon-continuous.ns-chart-icon-selected{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAU1JREFUeNpi/P//P8NgBkwMgxyMOpBSwILGNwBiByDWB2IFKF8ASf4BFB8E4g1AfIHmLgRlEiBOAOL7/zHBXwJ8kJ4GIBaAmkN1zAgk3qOFEgOWEEMOYVxqG4F4AhB/oHYIIoP1QFwAxAZ4fKUADfH1WEL8A1Q/1UIQ5sD5UItJNUAAGsUf0By6n1rRzkAlg2AORQ9NA2o4kJoY5KDzSI78BMQBg8mBMDwfyZFfKAlJWjkQVnQhh6TCYHMguiOvDkYHMqBlnobB6EAQPo7kSAdSaxJ61Pmgev0KEHMD8Qsg1iS2xqFXawZUXcZA2RJAvITUxgK98FKkqJ4/mNIgco1zA8mR/YPNgbDa5hNaA0VhoDMJA5aGMajRy4ckBmoAb0Rr3g2YA2E5ez3UsYOyTwIKKUMgTkQPtcESggw4+kMCg9WBo/3iUQcOSgAQYAB4CYHcqahMxwAAAABJRU5ErkJggg=='); }
.ns-chart-icon-comp,
.ns-chart-icon-comparison						{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAjJJREFUeNrslz1Iw1AUhdOaoSBqHIQMDnGyItJ2kU5SZym0m27tUhx1dKrFxU3XdrGCoJvtII5GFx0cAjp0DE7drOjgINb74Dy4hBibpn9IL3ykL01ezj3v5yahdrutjHKElRGPscB/L1DljUqlEqdDiogRBiHaGrvEBrdErVAoWP0WGCqXy+KYI4oQxePb4bKzLcSeEsdEq18Ovjpc4vECETKcjhpITFDqh9Cw44E1YpdICHeJBWKdMYtzeVzLo4hkdvqxSKp4cBYueM0tG9dnIVg494b/Zogj4sZjVHwLXIUjdhf3i+Hcx1CX2PkU+ov3QuAGkSEiAfqRQhPMfeHmHfoOJNBCpttENGDCFkRW0Z4izoI4GcZkv0B7E1uOEVBoHoiYhJNGt9uMiAbmTBLkMGzmHwvGK6SLJ3Dyilj228lEOp2Wv78g8pH4IOYxNEkk0sQ1foc8hEUzh9+mr0ri9T6I0icE6sxp6bafDfke/SjYT02/Q+zlgMXqcpQtpE+42oTYJjvnjC3iGfPxnFj6JcEIzNBwNNQOE7HZPqlDsA6SLtdzoS1wSBzgnmtizyFKd9nqbLWLyd90cUljWUdYW4Hj8sFPxAqSEhWnzgxoIBGbJ6gqvYkW69wrRBl9IBYxZUzU/pF5YW1hr31HW7xYXHrtkaoy+BCLbg0vvdMohRkUjLpzFIYhUIqMwT1ZBjNudXuY3yQ26rbnm5SqDD+qQH4PaR1XkvFn51jgAOJHgAEAV2uI7c6MEOgAAAAASUVORK5CYII='); }
.ns-chart-icon-comp.ns-chart-icon-selected,
.ns-chart-icon-comparison.ns-chart-icon-selected{ background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAeCAYAAABe3VzdAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAdlJREFUeNrsl7FOhEAQhlljQWJiKCkpzxgVCxMLC+0sLz4BvoGtlRgfQN/gHoGzs/OstNPCGDop7SQak2sMziT/mnHl0IM9JXqTfAcLx/Lv7M7OoIqicNpsM07LbSrwzwucNdohsUmsEAHanrifgQuiT9xMXCFHMRER98Vne/2izc/EhId+rKPo59HwklPiMenhUf89JE6I3LYHpSXEHhFWjCqAx5MSj+d43poHtcAeXjxuBx6mODeEntuadv5Zs9CRFmp6M7QhkDvuEq4FoSzoWoh8Qt+NgqSLxc+L+4xILSztHhHh/IXYqLslKRQLHWIbEcpROzCit45FEMr2TCzX6VOJasYl1oELjw4absZS5B2x2ESgFBpCKHt0SFyBYQ2RMXEg9sq4qUAz9bFQH+0UZGNuyJfoh20LM2NFoDadlzvwsANvPoAcR32t7PlbYg73F0YM0IUzPBwDVaOi9vFCX2CaFJoDjuQjXOPlsm+I8sXg31OtslTye2LUrmg7xot3iCWcc/CdipyvB5J9GOCkqpCKjJOKjfz4O5nkpwmRYWSBElRlkt8olEMUvfPiWh9TntWJ4kkYB1oCsa38JmFPrRK7VSlQtejDXX8PeW0VOP0u/p8C3wQYACD26JNh8mw9AAAAAElFTkSuQmCC'); }


/* trend graphs in popup */
.ns-chart-control-panel-wrapper--chart-popup div.uir-field-wrapper {
	margin: 0;
}

/* controls in portlet */
.ns-chart-control-panel-wrapper--chart-portlet .ns-chart-icon {
	height: 25px;
	width: 33.33333px;
}
.ns-chart-control-panel-wrapper--chart-portlet div.uir-field-wrapper {
	margin: auto 0;
}

.ns-dashboard-column[data-column-type='narrow'] .ns-portlet-wrapper .ns-chart-control-panel-wrapper .dropdownInput,
.ns-dashboard-column[data-column-type='narrow'] .ns-portlet-wrapper .ns-chart-control-panel-wrapper .uir-field-wrapper,
.ns-dashboard-column[data-column-type='narrow'] .ns-portlet-wrapper .ns-chart-control-panel-wrapper .uir-select-input-container,
.ns-dashboard-column[data-column-type='narrow'] .ns-portlet-wrapper .ns-chart-control-panel-wrapper .nldropdown
{
	width: 100% !important;
}

.ns-dashboard-column[data-column-type='narrow'] .ns-portlet-wrapper .ns-chart-control-panel-wrapper .ns-chart-control-area--dropdown
{
	width: 100% !important;
	margin-bottom: 8px;
}
.ns-dashboard-column[data-column-type='narrow'] .ns-portlet-wrapper .ns-chart-control-panel-wrapper .ns-chart-control-area--icons-type
{
	margin-left: 0;
	margin-right: auto;
}

/* NL AppOnly */
html,body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,p,blockquote,th{margin:0;padding:0;}input{padding:0;}img,body,html{border:0;}address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal;}ol,ul {list-style:none;}caption,th {text-align:left;}h1,h2,h3,h4,h5,h6{font-size:100%;}q:before,q:after{content:'';}

.nl-pagetint { background: #FFFFFF url(/images/chiles/pagetint.png) repeat-x 0 0; }
.bannerContainer { margin-bottom: 5px; position: relative; width: 100%; font-size: 11px; }

.bannerTL,
.bannerTR	{ position: absolute; width: 90%; height: 20px; top: -5px; left: -9px; background: transparent url(/images/chiles/banner/banner_bg.png) repeat-x; }

.bannerTR { width: 100%; z-index: 1; left: 9px; }
a.bannerLink { font-size: 11px; }
a.bannerLink:visited { color: #666666; }
.bannerClose { float: right; padding-top: 5px; padding-right: 18px; }

.bannerBL,
.bannerBR { font-size: 2px; position: absolute; width: 90%; height: 2px; top: 15px; left: -9px; background: transparent url(/images/chiles/banner/banner_bot.png) repeat-x; }

.bannerBR { width: 100%; z-index: 1; left: 9px; }
.createnewicontext { font-size: 7.5pt; position: relative; text-decoration: none; left: -3px; top: -1px; margin-right: 4px; }
a.newbar { text-decoration: none; padding: 0; }
a.newbar:hover { text-decoration: underline; }

.ri_personalize,
.ri_event,
.ri_calendarevent,
.ri_call,
.ri_phonecall,
.ri_task,
.ri_customer,
.ri_contact,
.ri_opportunity,
.ri_salesorder,
.ri_cashsale,
.ri_invoice,
.ri_bill,
.ri_case,
.ri_project,
.ri_job,
.ri_activity,
.ri_employeesched,
.ri_employee,
.ri_check,
.ri_generic,
.ri_informationitem,
.ri_group,
.ri_issue,
.ri_expensereport,
.ri_lead,
.ri_item,
.ri_campaign,
.ri_partner,
.ri_letter,
.ri_partnersched,
.ri_prospect,
.ri_purchaseorder,
.ri_payrolladjustment,
.ri_solution,
.ri_shippingitem,
.ri_topic,
.ri_quote,
.ri_estimate,
.ri_vendorcredit,
.ri_email,
.ri_note,
.ri_pdf,
.ri_time,
.ri_weeklytime,
.ri_file,
.ri_transaction,
.ri_workorder,
.ri_journal,
.ri_creditcard,
.ri_payment,
.ri_tax,
.ri_customerpayment,
.ri_inventory,
.ri_build,
.ri_vendor,
.ri_competitor,
.ri_employeecommission,
.ri_itemfulfillment,
.ri_transfer,
.ri_deposit,
.ri_customrecord,
.ri_customerrefund,
.ri_cashrefund,
.returnauthorization,
.ri_generic,
.ri_resourceallocation,
.ri_billingclass { background: url(/images/chiles/newBarIconsSprite1.png) no-repeat; height: 16px; width: 16px; margin-right: 5px; vertical-align: bottom; border: none; }

.ri_personalize { background-position: -25px -7px; margin-right: 0px; width: 12px; height: 16px; vertical-align: baseline; }
.nbm_icon_container_hover .ri_personalize { background-position: -100px -7px; } 

.ri_event,
.ir_calendarevent { background-position: -100px -25px; }

a.newbar .ri_event,
.ri_calendarevent { background-position: -25px -25px; } 

a.newbar:hover .ri_event,
.ri_calendarevent { background-position: -75px -25px; } 

.ri_task { background-position: -100px -50px; }
a.newbar .ri_task { background-position: -25px -50px; }
a.newbar:hover .ri_task { background-position: -75px -50px; }

.ri_phonecall,
.ri_call { background-position: -100px -75px; }

a.newbar .ri_phonecall,
a.newbar .ri_call { background-position: -25px -75px; }

a.newbar:hover .ri_phonecall,
a.newbar:hover .ri_call { background-position: -75px -75px; }

.ri_pdf { background-position: -100px -100px; }
a.newbar .ri_pdf { background-position: -25px -100px; } 
a.newbar:hover .ri_pdf { background-position: -75px -100px; } 

.ri_note { background-position: -100px -125px; }
a.newbar .ri_note { background-position: -25px -125px; } 
a.newbar:hover .ri_note { background-position: -75px -125px; } 

.ri_email { background-position: -100px -150px; }
a.newbar .ri_email { background-position: -25px -150px; } 
a.newbar:hover .ri_email { background-position: -75px -150px; } 

.ri_letter { background-position: -100px -175px; }
a.newbar .ri_letter{ background-position: -25px -175px; }
a.newbar:hover .ri_letter { background-position: -75px -175px; }

.ri_file { background-position: -100px -200px; }
a.newbar .ri_file{ background-position: -25px -200px; }
a.newbar:hover .ri_file { background-position: -75px -200px; }

.ri_time { background-position: -100px -225px; }
a.newbar .ri_time { background-position: -25px -225px; } 
a.newbar:hover .ri_time { background-position: -75px -225px; } 

.ri_weeklytime { background-position: -100px -250px; }
a.newbar .ri_weeklytime { background-position: -25px -250px; } 
a.newbar:hover .ri_weeklytime { background-position: -75px -250px; } 

.ri_activity { background-position: -100px -275px; }
a.newbar .ri_activity { background-position: -25px -275px; }
a.newbar:hover .ri_activity { background-position: -75px -275px; }

.ri_lead { background-position: -100px -300px; }
a.newbar .ri_lead { background-position: -25px -300px; }
a.newbar:hover .ri_lead { background-position: -75px -300px; }

.ri_prospect { background-position: -100px -325px; }
a.newbar .ri_prospect { background-position: -25px -325px; }
a.newbar:hover .ri_prospect { background-position: -75px -325px; }

.ri_customer { background-position: -100px -350px; }
a.newbar .ri_customer { background-position: -25px -350px; }
a.newbar:hover .ri_customer { background-position: -75px -350px; }

.ri_employee { background-position: -100px -375px; }
a.newbar .ri_employee { background-position: -25px -375px; }
a.newbar:hover .ri_employee { background-position: -75px -375px; }

.ri_contact { background-position: -100px -400px; }
a.newbar .ri_contact { background-position: -25px -400px; }
a.newbar:hover .ri_contact { background-position: -75px -400px; }

.ri_quote,
.ri_estimate { background-position: -100px -425px; }

a.newbar .ri_quote,
.ri_estimate { background-position: -25px -425px; }

a.newbar:hover .ri_quote,
.ri_estimate { background-position: -75px -425px; }

.ri_cashsale,
.ri_invoice { background-position: -100px -450px; }

a.newbar .ri_cashsale,
a.newbar .ri_invoice { background-position: -25px -450px; }

a.newbar:hover .ri_cashsale,
a.newbar:hover .ri_invoice { background-position: -75px -450px; }

.ri_opportunity { background-position: -100px -475px; }
a.newbar .ri_opportunity { background-position: -25px -475px; }
a.newbar:hover .ri_opportunity { background-position: -75px -475px; }
.ri_salesorder { background-position: -100px -500px; }
a.newbar .ri_salesorder { background-position: -25px -500px; }
a.newbar:hover .ri_salesorder { background-position: -75px -500px; }
.ri_transaction { background-position: -100px -525px; }
a.newbar .ri_transaction { background-position: -25px -525px; }
a.newbar:hover .ri_transaction { background-position: -75px -525px; }
.ri_item { background-position: -100px -550px; }
a.newbar .ri_item { background-position: -25px -550px; }
a.newbar:hover .ri_item { background-position: -75px -550px; }
.ri_campaign { background-position: -100px -575px; }
a.newbar .ri_campaign { background-position: -25px -575px; }
a.newbar:hover .ri_campaign { background-position: -75px -575px; }
.ri_case { background-position: -100px -600px; }
a.newbar .ri_case { background-position: -25px -600px; }
a.newbar:hover .ri_case { background-position: -75px -600px; }
.ri_issue { background-position: -100px -625px; }
a.newbar .ri_issue { background-position: -25px -625px; }
a.newbar:hover .ri_issue { background-position: -75px -625px; }

.ri_project,
.ri_job { background-position: -100px -650px; }

a.newbar .ri_project,
a.newbar .ri_job { background-position: -25px -650px; }

a.newbar:hover .ri_project,
a.newbar:hover .ri_job { background-position: -75px -650px; }

.ri_customicon { background-repeat: no-repeat; height: 16px; width: 16px; margin-right: 5px; vertical-align: bottom; border: none; }
.ri_customicon			{ background-position: -100px -25px; }
a.newbar .ri_customicon			{ background-position: -25px -25px; }
a.newbar:hover .ri_customicon		{ background-position: -75px -25px; }

.ri_partner			{ background-position: -100px -675px; }
.ri_topic			{ background-position: -100px -700px; }
.ri_solution			{ background-position: -100px -725px; }
.ri_employeesched		{ background-position: -100px -750px; }
.ri_partnersched		{ background-position: -100px -775px; }
.ri_informationitem		{ background-position: -100px -800px; }
.ri_payrolladjustment		{ background-position: -100px -825px; }
.ri_vendorcredit		{ background-position: -100px -850px; }
.ri_shippingitem		{ background-position: -100px -875px; }
.ri_workorder			{ background-position: -100px -900px; }
.ri_journal			{ background-position: -100px -925px; }
.ri_creditcard			{ background-position: -100px -950px; }
.ri_check			{ background-position: -100px -975px; }
.ri_payment			{ background-position: -100px -1000px; }
.ri_tax				{ background-position: -100px -1025px; }
.ri_customerpayment		{ background-position: -100px -1050px; }
.ri_inventory			{ background-position: -100px -1075px; }
.ri_build			{ background-position: -100px -1100px; }
.ri_vendor			{ background-position: -100px -1125px; }
.ri_competitor			{ background-position: -100px -1150px; }
.ri_employeecommission		{ background-position: -100px -1175px; }
.ri_itemfulfillment		{ background-position: -100px -1200px; }
.ri_group			{ background-position: -100px -1225px; }
.ri_transfer			{ background-position: -100px -1250px; }
.ri_deposit			{ background-position: -100px -1275px; }
.ri_customrecord		{ background-position: -100px -1300px; }
.ri_expensereport		{ background-position: -100px -1325px; }
.ri_purchaseorder		{ background-position: -100px -1350px; }
.ri_customerrefund		{ background-position: -100px -1375px; }
.ri_cashrefund			{ background-position: -100px -1400px; }
.ri_returnauthorization		{ background-position: -100px -1425px; }
.ri_generic			{ background-position: -100px -1450px; }
.ri_resourceallocation		{ background-position: -100px -1475px; }
.ri_billingclass		{ background-position: -100px -1500px; }

a.newbar .ri_partner			{ background-position: -25px -675px; }
a.newbar .ri_topic			{ background-position: -25px -700px; }
a.newbar .ri_solution			{ background-position: -25px -725px; }
a.newbar .ri_employeesched		{ background-position: -25px -750px; }
a.newbar .ri_partnersched		{ background-position: -25px -775px; }
a.newbar .ri_informationitem		{ background-position: -25px -800px; }
a.newbar .ri_payrolladjustment		{ background-position: -25px -825px; }
a.newbar .ri_vendorcredit		{ background-position: -25px -850px; }
a.newbar .ri_shippingitem		{ background-position: -25px -875px; }
a.newbar .ri_workorder			{ background-position: -25px -900px; }
a.newbar .ri_journal			{ background-position: -25px -925px; }
a.newbar .ri_creditcard			{ background-position: -25px -950px; }
a.newbar .ri_check			{ background-position: -25px -975px; }
a.newbar .ri_payment			{ background-position: -25px -1000px; }
a.newbar .ri_tax			{ background-position: -25px -1025px; }
a.newbar .ri_customerpayment		{ background-position: -25px -1050px; }
a.newbar .ri_inventory			{ background-position: -25px -1075px; }
a.newbar .ri_build			{ background-position: -25px -1100px; }
a.newbar .ri_vendor			{ background-position: -25px -1125px; }
a.newbar .ri_competitor			{ background-position: -25px -1150px; }
a.newbar .ri_employeecommission		{ background-position: -25px -1175px; }
a.newbar .ri_itemfulfillment		{ background-position: -25px -1200px; }
a.newbar .ri_group			{ background-position: -25px -1225px; }
a.newbar .ri_transfer			{ background-position: -25px -1250px; }
a.newbar .ri_deposit			{ background-position: -25px -1275px; }
a.newbar .ri_customrecord		{ background-position: -25px -1300px; }
a.newbar .ri_expensereport		{ background-position: -25px -1325px; }
a.newbar .ri_purchaseorder		{ background-position: -25px -1350px; }
a.newbar .ri_customerrefund		{ background-position: -25px -1375px; }
a.newbar .ri_cashrefund			{ background-position: -25px -1400px; }
a.newbar .ri_returnauthorization	{ background-position: -25px -1425px; }
a.newbar .ri_generic			{ background-position: -25px -1450px; }
a.newbar .ri_resourceallocation		{ background-position: -25px -1475px; } 

a.newbar:hover .ri_partner		{ background-position: -75px -675px; }
a.newbar:hover .ri_topic		{ background-position: -75px -700px; }
a.newbar:hover .ri_solution		{ background-position: -75px -725px; }
a.newbar:hover .ri_employeesched	{ background-position: -75px -750px; }
a.newbar:hover .ri_partnersched		{ background-position: -75px -775px; }
a.newbar:hover .ri_informationitem	{ background-position: -75px -800px; }
a.newbar:hover .ri_payrolladjustment	{ background-position: -75px -825px; }
a.newbar:hover .ri_vendorcredit		{ background-position: -75px -850px; }
a.newbar:hover .ri_shippingitem		{ background-position: -75px -875px; }
a.newbar:hover .ri_workorder		{ background-position: -75px -900px; }
a.newbar:hover .ri_journal		{ background-position: -75px -925px; }
a.newbar:hover .ri_creditcard		{ background-position: -75px -950px; }
a.newbar:hover .ri_check		{ background-position: -75px -975px; }
a.newbar:hover .ri_payment		{ background-position: -75px -1000px; }
a.newbar:hover .ri_tax			{ background-position: -75px -1025px; }
a.newbar:hover .ri_customerpayment	{ background-position: -75px -1050px; }
a.newbar:hover .ri_inventory		{ background-position: -75px -1075px; }
a.newbar:hover .ri_build		{ background-position: -75px -1100px; }
a.newbar:hover .ri_vendor		{ background-position: -75px -1125px; }
a.newbar:hover .ri_competitor		{ background-position: -75px -1150px; }
a.newbar:hover .ri_employeecommission	{ background-position: -75px -1175px; }
a.newbar:hover .ri_itemfulfillment	{ background-position: -75px -1200px; }
a.newbar:hover .ri_group		{ background-position: -75px -1225px; }
a.newbar:hover .ri_transfer		{ background-position: -75px -1250px; }
a.newbar:hover .ri_deposit		{ background-position: -75px -1275px; }
a.newbar:hover .ri_customrecord		{ background-position: -75px -1300px; }
a.newbar:hover .ri_expensereport	{ background-position: -75px -1325px; }
a.newbar:hover .ri_purchaseorder	{ background-position: -75px -1350px; }
a.newbar:hover .ri_customerrefund	{ background-position: -75px -1375px; }
a.newbar:hover .ri_cashrefund		{ background-position: -75px -1400px; }
a.newbar:hover .ri_returnauthorization	{ background-position: -75px -1425px; }
a.newbar:hover .ri_generic		{ background-position: -75px -1450px; }
a.newbar:hover .ri_resourceallocation	{ background-position: -75px -1475px; } 

.ri_bill			{ background-position: -100px -1350px; }
a.newbar .ri_bill			{ background-position: -25px -1350px; }
a.newbar:hover .ri_bill			{ background-position: -75px -1350px; }




.fgroup_title { font-size: 13px; }

.cross_links,
.cross_links a { color: #D0DAE1; font-weight: bold; }
.cross_links { display: inline-block; height: 100%; padding-top: 0px; }

.cl_separator,
.cl_separator_blank { border-right: 1px solid #D0DAE1; }
.cl_separator_blank { border_color: #FFFFFF; }

.clm_left,
.clm_right,
.clm_body { display: inline-block; height: 100%; width: 5px; vertical-align: top; }
.clm_body { width: auto; }

.clm_roll .clm_left,
.clm_roll .clm_body,
.clm_roll .clm_right,
.clm_tri { background-image: url(/images/chiles/crosslinkmenu.png); color: #5C7499; }
.clm_tri { display: inline-block; background-position: 0 -94px; width: 5px; margin-left: 5px; zoom: 1; }

.clm,
.clm_roll { padding-left: 0px; }
.clm_roll .clm_left { background-position: top left; }
.clm_roll .clm_right { background-position: right -30px; }
.clm_roll .clm_body { background-position: 0 -60px; background-repeat: repeat-x; }
.clm_roll .clm_tri { background-position: 0 -119px; }
.clm_roll a { color: #5C7499; }
#m1 { visibility: hidden; position: absolute; }
.clm_roll #m1 { visibility: visible; position: absolute; border: 1px solid #9ba1a6; background-color: #edf1f7; z-index: 200; left: 153px; top: 96px; }
#m1 a { display: block; padding: 5px 10px; }

.fwmultisel,
.fwmultisel_focus,
.fwpopupsel,
.fwpopupsel_focus { display: inline-block; background-image: url(/images/chiles/sprite_field_widgets_15.png); height: 25px; width: 25px; }

.fwmultisel { background-position: -100px -200px; height: 22px; width: 36px;; vertical-align: top; }
.effectHover .fwmultisel{ background-position: -50px -200px; }
.fwmultisel_focus { background-position: -150px -200px; height: 22px; width: 36px;; vertical-align: top; }
.fwpopupsel { background-position: -100px -100px; }
.effectHover .fwpopupsel { background-position: -50px -100px; }
.effectDisabled .fwpopupsel { background-position: -200px -100px; }
.fwpopupsel_focus { background-position: -150px -100px; }
.fwpopupsel_pos { position: relative; left: -12px; top: 0px; display: inline-block; vertical-align: middle; }
.fwpopupsearch { vertical-align: top; display: inline-block; }

.checkbox_unck,
.checkbox_ck,
.checkbox_read_unck,
.checkbox_read_ck,
.checkbox_disabled_unck,
.checkbox_disabled_ck { padding: 0 8px 0px 0px; display: inline-block; position: relative; top: 4px; left: 3px; zoom: 1; }

.checkbox_unck .checkboximage,
.checkbox_ck .checkboximage,
.checkbox_read_unck .checkboximage,
.checkbox_read_ck .checkboximage,
.checkbox_disabled_unck .checkboximage,
.checkbox_disabled_ck .checkboximage { background-image: url(/images/chiles/sprite_field_widgets_15.png); height: 15px; width: 15px; display: inline-block; position: relative; }

.checkbox_unck .checkboximage { background-position: -50px -550px; }
.checkbox_ck .checkboximage { background-position: -50px -500px; }
.checkbox_read_unck .checkboximage { background-position: -150px -550px; }
.checkbox_read_ck .checkboximage { background-position: -150px -500px; }
.checkbox_disabled_ck .checkboximage { background-position: -200px -500px; }
.checkbox_disabled_unck .checkboximage { background-position: -200px -550px; } 

/* on/off switch */

.onOffLtTop_on { background: url(/images/core/widgets/onoff/dleOn_lt_top.png); background-repeat: no-repeat; background-position: top left; padding: 0px 5px 0px 5px; }
.onOffLtBot_on { background: url(/images/core/widgets/onoff/dleOn_lt_bot.png); background-repeat: no-repeat; background-position: bottom left; }
.onOffRtTop_on { background: url(/images/core/widgets/onoff/dleOn_rt_top.png); background-repeat: no-repeat; background-position: top right; padding: 0px 4px 0px 4px; cursor: pointer; }
.onOffRtBot_on { background: url(/images/core/widgets/onoff/dleOn_rt_bot.png); background-repeat: no-repeat; background-position: bottom right; }
.onOffMidTop_on { background: url(/images/core/widgets/onoff/dleOn_mid_top.png); background-repeat: no-repeat; background-position: top right; }
.onOffMidBot_on { background: url(/images/core/widgets/onoff/dleOn_mid_bot.png); background-repeat: no-repeat; background-position: bottom right; }
.onOffLtTop_off { background: url(/images/core/widgets/onoff/dleOff_lt_top.png); background-repeat: no-repeat; background-position: top left; padding: 0px 5px 0px 5px; cursor: pointer; }
.onOffLtBot_off { background: url(/images/core/widgets/onoff/dleOff_lt_bot.png); background-repeat: no-repeat; background-position: bottom left; }
.onOffRtTop_off { background: url(/images/core/widgets/onoff/dleOff_rt_top.png); background-repeat: no-repeat; background-position: top right; padding: 0px 4px 0px 4px; }
.onOffRtBot_off { background: url(/images/core/widgets/onoff/dleOff_rt_bot.png); background-repeat: no-repeat; background-position: bottom right; }
.onOffMidTop_off { background: url(/images/core/widgets/onoff/dleOff_mid_top.png); background-repeat: no-repeat; background-position: top right; }
.onOffMidBot_off { background: url(/images/core/widgets/onoff/dleOff_mid_bot.png); background-repeat: no-repeat; background-position: bottom right; }
.onOffOnText_on { color: #FFFFFF; font-weight: bold; }
.onOffOffText_on { color: #555555; visibility: hidden; }
.onOffOnText_off { color: #FFFFFF; font-weight: bold; visibility: hidden; }
.onOffOffText_off { color: #555555; }

.table_fields .checkboxSpan { padding-bottom: 4px; display: inline-block; }
.field_widget { padding-left: 5px; }
.field_widget_pos { position: relative; left: -15px; top: 1px; }
.field_widget_helper_pos { position: relative; left: -8px; top: 1px; }
.field_widget_helper_pos .field_widget_pos { left: 0px; top: 0px; }
.field_widget_boxpos { position: relative; vertical-align: top; top: 1px; }
.uir-field-widget a { height: 25px; width: 25px; vertical-align: top; }
.positionTop { vertical-align: top; }

.i_dropdownarrow,
.i_createnew,
.i_dropdownlink,
.i_options,
.i_options_focus,
.i_calendar,
.i_calendar_focus,
.i_timer,
.i_timer_focus,
.i_timecalc,
.i_timecalc_focus,
.i_colorpicker,
.i_colorpicker_focus,
.i_list2,
.i_popupsearch,
.i_dashboard_gray,
.i_dashboard_white,
.i_inventorydetailneeded,
.i_inventorydetailset,
.i_itemvendorpriceneeded,
.i_itemvendorpriceset,
.i_itemvendorpricedisabled,
.i_atphelper { background-image: url(/images/chiles/sprite_field_widgets_15.png); height: 22px; width: 22px; display: inline-block; vertical-align: middle; zoom: 1; }

.i_inventorydetaildisabled { height: 0px; width: 0px; display: inline-block; vertical-align: middle; zoom: 1; }

.i_dropdownarrow,
.effectStatic .i_dropdownarrow { background-position: -100px -50px; }

.i_inventorydetailneeded { background-position: -100px -1150px; }
.i_inventorydetailset { background-position: -100px -1200px; }
.i_itemvendorpriceneeded { background-position: -100px -1300px; }
.i_itemvendorpriceset { background-position: -100px -1350px; }
.i_itemvendorpricedisabled { background-position: -200px -1350px; }
.i_atphelper { background-position: -100px -1250px; }
.ddarrowSpan { position: relative; top: -1px; left: -12px; vertical-align: middle; }
.effectHover .i_dropdownarrow { background-position: -50px -50px; }
.effectHover_focus .i_dropdownarrow { background-position: -150px -50px; }
.effectDisabled .i_dropdownarrow { background-position: -200px -50px; }
.i_createnew { background-position: -50px -600px; }

.i_addresssummaryfield { background: url("/images/sprite_machines.png") no-repeat 0 -897px !important; height: 22px; width: 22px; display: inline-block; vertical-align: middle; zoom: 1; }

.i_createnew:hover,
.effectStatic .i_createnew { background-position: -100px -600px; }

.i_createnew:active { background-position: -150px -600px; }
.effectDisabled .i_createnew { background-position: -200px -600px; }
.i_dropdownlink { background-position: -50px -150px; }

.i_dropdownlink:hover,
.effectStatic .i_dropdownlink { background-position: -100px -150px; }

.i_dropdownlink:active { background-position: -150px -150px; }
.effectDisabled .i_dropdownlink { background-position: -200px -150px; }

.i_options,
.effectStatic .i_options { background-position: -100px -150px; }

.effectHover .i_options { background-position: -50px -150px; }
.i_options_focus { background-position: -150px -150px; }

.i_calendar,
.effectStatic .i_calendar { background-position: -100px -250px; }

.effectHover .i_calendar { background-position: -50px -250px; }
.i_calendar_focus { background-position: -150px -250px; }

.i_timer,
.effectStatic .i_timer { background-position: -100px -850px; }

.effectHover .i_timer { background-position: -50px -850px; }
.i_timer_focus { background-position: -150px -850px; }

.i_timecalc,
.effectStatic .i_timecalc { background-position: -100px -800px; }

.effectHover .i_timecalc { background-position: -50px -800px; }

.i_timecalc:focus,
.i_timecalc:active { background-position: -150px -800px; }

.i_colorpicker,
.effectStatic .i_colorpicker { background-position: -100px -900px; }

.effectHover .i_colorpicker { background-position: -50px -900px; }
.i_colorpicker_focus { background-position: -150px -900px; }

.i_refresh { background: url(/images/chiles/dashboard/portlet_btn_refresh.png) no-repeat; height: 16px; width: 16px; display: inline-block; vertical-align: middle; zoom: 1; }
.i_list2 { background-position: -100px -950px; }
.i_list2:focus { background-position: -150px -950px; }

.i_popupsearch { background-position: -100px -1050px; }
.i_popupsearch:hover,
.effectStatic .i_popupsearch { background-position: -100px -1050px; }

.i_popupsearch:active { background-position: -150px -1050px; }
.i_dashboard_gray,.i_dashboard_white { background-image: url('/uirefresh/img/sprite-record.png'); width: 15px; height: 16px; background-position: -50px -200px; }

.pgm_left,
.pgm_bg,
.pgm_right { background-image: none; }

.pgm_menu,
.pgm_action_menu { font-family: Open Sans,Helvetica,sans-serif; font-size: 8pt; position: relative; top: 5px; }

.pgm_tri { background-image: url(/images/chiles/navmenu/triangle_sm_down.png); height: 5px; width: 5px; position: relative; }

.ac_container,
.ac_container_bot { background-color: #FFFFFF; position: relative; z-index: 1000; display: inline-block; }

.ac_container .pgm_left { background-image: url(/images/chiles/navmenu/pgmenu_top_l.png); }
.ac_container .pgm_bg { background-image: url(/images/chiles/navmenu/pgmenu_top_bg.png); }
.ac_container .pgm_right { background-image: url(/images/chiles/navmenu/pgmenu_top_r.png); }
.ac_container_bot .pgm_left { background-position: left bottom; background-image: url(/images/chiles/navmenu/pgmenu_bot_l.png); }
.ac_container_bot .pgm_bg { background-position: left bottom; background-image: url(/images/chiles/navmenu/pgmenu_bot_bg.png); }
.ac_container_bot .pgm_right { background-position: left bottom; background-image: url(/images/chiles/navmenu/pgmenu_bot_r.png); }

.nbm_text_container,
.nbm_text_container_hover,
.nbm_icon_container,
.nbm_icon_container_hover { position: relative; z-index: 1000; display: inline-block; } 

.nbm_icon_container,
.nbm_icon_container_hover { height: 20px; }

.nbm_text_container_hover .pgm_left,
.nbm_text_container .pgm_left,
.nbm_text_container_hover .pgm_bg,
.nbm_text_container .pgm_bg,
.nbm_text_container_hover .pgm_right,
.nbm_text_container .pgm_right { display: inline-block; padding: 3px 0 1px 0; font-size: 7.5pt; }

.nbm_icon_container_hover .pgm_left,
.nbm_icon_container .pgm_left,
.nbm_icon_container_hover .pgm_bg,
.nbm_icon_container .pgm_bg,
.nbm_icon_container_hover .pgm_right,
.nbm_icon_container .pgm_right { display: inline-block; padding: 2px 0 2px 0; font-size: 7.5pt; }

.nbm_text_container .pgm_left,
.nbm_icon_container .pgm_left { width: 5px; }

.nbm_text_container_hover .pgm_left,
.nbm_icon_container_hover .pgm_left { background-image: url(/images/chiles/navmenu/pgmenu2_top_l.png); width: 5px; }

.nbm_text_container_hover .pgm_bg,
.nbm_icon_container_hover .pgm_bg { background: url(/images/chiles/navmenu/pgmenu2_top_bg.png) repeat-x; }

.pgm_bg .crosslinktext,
.pgm_bg .crosslinktextul { padding: 0px; } 

.nbm_text_container_hover .crosslinktext,
.nbm_text_container_hover .crosslinktextul { color: #000000; } 

.nbm_text_container .menu_tri_silver { background-position: -0px -97px; }
.nbm_text_container_hover .menu_tri_silver { background-position: -50px -97px; }

.nbm_text_container .pgm_right,
.nbm_icon_container .pgm_right { width: 5px; }

.nbm_text_container_hover .pgm_right,
.nbm_icon_container_hover .pgm_right { background-image: url(/images/chiles/navmenu/pgmenu2_top_r.png); width: 5px; }

.ac_parent_div,
.nbm_text_parent_div,
.nbm_icon_parent_div { position: relative; z-index: 20; }

.ac_div { cursor: default; padding: 0; z-index: 400; position: absolute; top: -1px; }
.ac_table { border: 1px solid #B0B0B0; background: #FFFFFF url(/images/chiles/navmenu/pgmenu_main_bg.png) repeat scroll; white-space: nowrap; min-width: 100px; }

.nbm_text_parent_div .ac_table,
.nbm_icon_parent_div .ac_table { border: 1px solid #B0B0B0; background-color: #F2F2F2; background-image: none; white-space: nowrap; min-width: 100px; }

.acSideShadow { background: url(/images/chiles/ams_side.png); }
.acLBotShadow { background: url(/images/chiles/ams_bot.png) left top; }
.acRBotShadow { background: url(/images/chiles/ams_bot.png) right top; }
.ac_text { color: #000000; font-size: 8pt; padding: 3px; text-decoration: none; vertical-align: bottom; }
.ac_text_heading { color: #000000; font-size: 8pt; padding: 3px; text-decoration: none; vertical-align: bottom; }
.ac_text_sel { color: #FFFFFF; font-size: 8pt; padding: 3px; text-decoration: none; vertical-align: bottom; background-color: #999999; }
.ac_text .ac_text_pad { color: #000000; padding: 0 0 0 6px; }
.ac_text_sel .ac_text_pad { color: #FFFFFF; padding: 0 0 0 6px; }
.ac_text .ac_heading_pad { color: #444444; padding: 0 0 0 0px; }
.ac_text_sel .ac_heading_pad { color: #FFFFFF; padding: 0 0 0 0px; }
.ac_separator { height: 3px; margin: 0px 5px; background: transparent url(/images/buttons/chiles/ac_separator.png) repeat-x scroll; }
.oval_button .left_cap { background: transparent url(/images/buttons/chiles/go_left_cap.png) no-repeat scroll left center; }
.oval_button .body { background: transparent url(/images/buttons/chiles/go_body.png) repeat-x; }
.oval_button .right_cap { background: transparent url(/images/buttons/chiles/go_right_cap.png) no-repeat scroll right center; }
.oval_button_sel .left_cap { background: transparent url(/images/buttons/chiles/go_left_cap_dn.png) no-repeat scroll left center; }
.oval_button_sel .body { background: transparent url(/images/buttons/chiles/go_body_dn.png) repeat-x; }
.oval_button_sel .right_cap { background: transparent url(/images/buttons/chiles/go_right_cap_dn.png) no-repeat scroll right center; }
.page_level_ybutton .left_cap { background: transparent url(/images/buttons/chiles/page_left_cap_y.png) no-repeat scroll left center; }
.page_level_ybutton .body { background: transparent url(/images/buttons/chiles/page_body_y.png) repeat-x; }
.page_level_ybutton .right_cap { background: transparent url(/images/buttons/chiles/page_right_cap_y.png) no-repeat scroll left center; } 
.page_level_ybutton_sel .left_cap { background: transparent url(/images/buttons/chiles/page_left_cap_y_dn.png) no-repeat scroll left center; }
.page_level_ybutton_sel .body { background: transparent url(/images/buttons/chiles/page_body_y_dn.png) repeat-x; }
.page_level_ybutton_sel .right_cap { background: transparent url(/images/buttons/chiles/page_right_cap_y_dn.png) no-repeat scroll left center; } 
.page_level_bbutton .left_cap { background: transparent url(/images/buttons/chiles/page_left_cap_b.png) no-repeat scroll left center; }
.page_level_bbutton .body { background: transparent url(/images/buttons/chiles/page_body_b.png) repeat-x; }
.page_level_bbutton .right_cap { background: transparent url(/images/buttons/chiles/page_right_cap_b.png) no-repeat scroll left center; } 
.page_level_bbutton_sel .left_cap { background: transparent url(/images/buttons/chiles/page_left_cap_b_dn.png) no-repeat scroll left center; }
.page_level_bbutton_sel .body { background: transparent url(/images/buttons/chiles/page_body_b_dn.png) repeat-x; }
.page_level_bbutton_sel .right_cap { background: transparent url(/images/buttons/chiles/page_right_cap_b_dn.png) no-repeat scroll left center; } 
.tab_level_button .left_cap { background: transparent url(/images/buttons/chiles/tab_left_cap.png) no-repeat scroll left center; }
.tab_level_button .body { background: transparent url(/images/buttons/chiles/tab_body.png) repeat-x; }
.tab_level_button .right_cap { background: transparent url(/images/buttons/chiles/tab_right_cap.png) no-repeat scroll left center; } 
.tab_level_button_sel .left_cap { background: transparent url(/images/buttons/chiles/tab_left_cap_dn.png) no-repeat scroll left center; }
.tab_level_button_sel .body { background: transparent url(/images/buttons/chiles/tab_body_dn.png) repeat-x; }
.tab_level_button_sel .right_cap { background: transparent url(/images/buttons/chiles/tab_right_cap_dn.png) no-repeat scroll left center; } 
.machine_button_container { border: 1px solid #495875; border-right-style: none; }
.machine_button { border-right: 1px solid #495875; }
.machine_button .left_cap { background: transparent url(/images/buttons/chiles/machine_body.png) repeat-x; }
.machine_button .body { background: transparent url(/images/buttons/chiles/machine_body.png) repeat-x; }
.machine_button .right_cap { background: transparent url(/images/buttons/chiles/machine_body.png) repeat-x; }
.menubg { border-top: 1px solid #AFB5BF; }
.required_icon { position: relative; top: 1px; padding-right: 2px; }

.pt_container,
.pt_head,
.pt_body,
.pt_end,
.pt_prev,
.pt_next { display: block; }

.pt_head,
.pt_end { width: 10px; height: 28px; background-image: url(/images/chiles/pagetitle.png); text-indent: -999999px; outline: none; position: absolute; }

.pt_prev,
.pt_next { width: 20px; height: 20px; background-image: url(/images/chiles/sprite_field_widgets_15.png); text-indent: -999999px; outline: none; float: left; position: relative; }

.pt_container { height: 26px; position: relative; margin-bottom: 5px; z-index: 10; }
.pt_head { background-position: 0 0; left: 0; }
.pt_body { border-bottom: 1px solid #B5B5B5; border-top: 1px solid #B5B5B5; height: 26px; padding-right: 10px; }
.pt_end { background-position: top right; right: 0px; top: 0px; }
.pt_buttons { position: relative; left: 5px; top: 3px; }
.pt_prev { margin-right: 2px; background-position: -50px -400px; }
.pt_prev:active { background-position: -150px -400px; }
.pt_next { background-position: -50px -450px; }
.pt_next:active { background-position: -150px -450px; }
.pt_title { font-size: 17px;	font-weight: bold;	color: #000000;	position: relative;	top: 2px; left: 10px; float: left; }
.pt_bg { background-color: #000000; }
.pt_statusblock {	text-align: center; position: absolute; right: 0px; background-color: #000000; height: 26px; min-width: 180px; padding-right: 15px; }
.pt_statuslabel {	font-size: 8pt;	padding: 0px 10px;	margin: 0px; line-height: 22px; }
.pt_status { font-size: 8pt;	font-weight: bold; line-height: 22px; }
.pt_add_title { float: left; position: relative; left: 30px; height: 100%; }
.quickfind { visibility: hidden; position: absolute; z-index: 1; border-style: solid; border-width: 1px; padding: 3px; border-color: #999999; background-color: #eeeeee; white-space: nowrap; }
.formviewunselected { font-size: 10px; color: #000000; text-decoration: none; border-bottom: 1px; border-color: #999999; border-style: none none dotted none; cursor: pointer; }
.formviewselected { font-weight: bolder; font-size: 11px; color: #000000; text-decoration: none; cursor: default; }
a.formviewunselected:hover { color: #2f5981; }
.button_inline { vertical-align: bottom; padding-bottom: 5px; }
.button_inline table { display: inline; }

.effectHover .input,
.effectHover .inputreq,
.effectHover .inputrt,
.effectHover .inputrtreq { display: inline-block; height: 25px; border-width: 1px; border-style: solid; border-color: #FFFFFF #FFFFFF #D5DEE7 #FFFFFF ; }

.rtpadding { padding-right: 15px; } 

.effectHover_roll .input,
.effectHover_roll .inputreq,
.effectHover_roll .inputrt,
.effectHover_roll .inputrtreq { display: inline-block; height: 25px; border-color: #D5DEE7; }

.effectHover_focus .input,
.effectHover_focus .inputreq,
.effectHover_focus .inputrt,
.effectHover_focus .inputrtreq { display: inline-block; height: 25px; border-color: #D5DEE7; }

.effectStatic .input,
.effectStatic .inputreq,
.effectStatic .inputrt,
.effectStatic .inputrtreq,
.effectDisabled .input,
.effectDisabled .inputreq,
.effectDisabled .inputrt,
.effectDisabled .inputrtreq { border: 1px solid #D5DEE7; height: 25px; }

.nlEffectMultiSelNative .input { height: auto; }
.effectStatic .inputreq { border: 1px solid #D5DEE7; }

.effectHover .textareainput,
.effectHover .textareainputreq,
.effectHover .textarea,
.effectStatic .textareainput,
.effectStatic .textareainputreq,
.effectStatic .textarea { border: 1px solid #D5DEE7; height: auto; }

.table_fields .effectHover,
.table_fields .effectHover_roll,
.table_fields .effectHover_focus,
.table_fields .effectDisabled,
.table_fields .effectStatic { display: inline-block; padding-bottom: 5px; }

.totallingtable td[align="right"],
.totallingtable span.inputtotalling { } 

.totallingTopLeft,
.totallingTopRight,
.totallingBottomLeft,
.totallingBottomRight { display: block; height: 7px; width: 7px; background: url(/images/forms/border/subtab_corner4.png) no-repeat -7px -7px; position: absolute; }

.totallingTopLeft { top: 0; left: 0; }
.totallingTopRight { background-position: 0px -7px; top: 0; right: 0; }
.totallingBottomLeft { bottom: 0; left: 0; background-position: -7px 0; }
.totallingBottomRight { background-position: 0px 0px; bottom: 0; right: 0; }
.inputtotalling { display: inline-block; text-align: right; padding-left: 5px; vertical-align: middle; position: relative; }
.totallingcurrency { vertical-align: middle; }
span input.checkbox { position: absolute; z-index: -10; left: -4px; top: -3px; }
.checkbox_native input.checkbox { position: static; }

.pgBntG img,
.pgBntG_sel img,
.pgBntY img,
.pgBntY_sel img,
.pgBntGDis img,
.pgBntYDis img { display: block; background-image: url(/images/chiles/bntPageSprite.png); }

.tabBnt img,
.tabBnt_sel img,
.tabBntDis img { display: block; background-image: url(/images/chiles/bntTabSprite.png); }

.pgBntG .bntLT { background-position: -30px 0; }
.pgBntG .bntLB { background-position: -30px bottom; }
.pgBntG .bntRT { background-position: -37px 0; }
.pgBntG .bntRB { background-position: -37px bottom; }
.pgBntG .bntBgT { padding: 1px 6px 0; background: url(/images/chiles/btn_page_bg_t.png) repeat-x scroll left top transparent; }
.pgBntG .bntBgB { background: url(/images/chiles/btn_page_bg_b.png) repeat-x scroll left bottom transparent; }
.pgBntG_sel .bntLT { background-position: -45px 0; }
.pgBntG_sel .bntLB { background-position: -45px bottom; }
.pgBntG_sel .bntRT { background-position: -52px 0; }
.pgBntG_sel .bntRB { background-position: -52px bottom; }
.pgBntG_sel .bntBgT { padding: 1px 6px 0; background: url(/images/chiles/btn_page_down_bg_t.png) repeat-x scroll left top transparent; }
.pgBntG_sel .bntBgB { background: url(/images/chiles/btn_page_down_bg_b.png) repeat-x scroll left bottom transparent; }
.pgBntG .multiBnt { border-left: 1px solid #999999; }
.pgBntG_sel .multiBnt { border-left: 1px solid #8a8a8a; }
.pgBntG .multiBntTri { background-position: -30px -31px; }
.pgBntY .bntLT { background-position: 0 0; }
.pgBntY .bntLB { background-position: 0 bottom; }
.pgBntY .bntRT { background-position: -7px 0; }
.pgBntY .bntRB { background-position: -7px bottom; }
.pgBntY .bntBgT { padding: 1px 6px 0; background: url(/images/chiles/btn_page_hilited_bg_t.png) repeat-x scroll left top transparent; }
.pgBntY .bntBgB { background: url(/images/chiles/btn_page_hilited_bg_b.png) repeat-x scroll left bottom transparent; }
.pgBntY_sel .bntLT { background-position: -15px 0; }
.pgBntY_sel .bntLB { background-position: -15px bottom; }
.pgBntY_sel .bntRT { background-position: -22px 0; }
.pgBntY_sel .bntRB { background-position: -22px bottom; }
.pgBntY_sel .bntBgT { padding: 1px 6px 0; background: url(/images/chiles/btn_page_hilited_down_bg_t.png) repeat-x scroll left top transparent; }
.pgBntY_sel .bntBgB { background: url(/images/chiles/btn_page_hilited_down_bg_b.png) repeat-x scroll left bottom transparent; }
.pgBntY .multiBnt { border-left: 1px solid #d7ba7b; }
.pgBntY_sel .multiBnt { border-left: 1px solid #a99340; }
.pgBntY .multiBntTri { background-position: 0 -31px; }

.pgBntGDis .bntLT,
.pgBntYDis .bntLT { background-position: -60px 0; }

.pgBntGDis .bntLB,
.pgBntYDis .bntLB { background-position: -60px bottom; }

.pgBntGDis .bntRT,
.pgBntYDis .bntRT { background-position: -67px 0; }

.pgBntGDis .bntRB,
.pgBntYDis .bntRB { background-position: -67px bottom; }

.pgBntGDis .bntBgT,
.pgBntYDis .bntBgT { color: #999999; padding: 1px 0 0; background: url(/images/chiles/btn_form_disabled_bg_t.png) repeat-x scroll left top transparent; }

.pgBntGDis .bntBgB,
.pgBntYDis .bntBgB { background: url(/images/chiles/btn_form_disabled_bg_b.png) repeat-x scroll left bottom transparent; }

.tabBnt .bntLT,
.tabBnt_sel .bntLT { background-position: 0 0; }

.tabBnt .bntLB,
.tabBnt_sel .bntLB { background-position: 0 bottom; }

.tabBnt .bntRT,
.tabBnt_sel .bntRT { background-position: -20px 0; }

.tabBnt .bntRB,
.tabBnt_sel .bntRB { background-position: -20px bottom; }

.tabBnt .bntBgT,
.tabBnt_sel .bntBgT { padding: 1px 0 0; background: url(/images/chiles/btn_form_bg_t.png) repeat-x scroll left top transparent; }

.tabBnt .bntBgB,
.tabBnt_sel .bntBgB { background: url(/images/chiles/btn_form_bg_b.png) repeat-x scroll left bottom transparent; }

.tabBntDis .bntLT { background-position: -40px 0; }
.tabBntDis .bntLB { background-position: -40px bottom; }
.tabBntDis .bntRT { background-position: -60px 0; }
.tabBntDis .bntRB { background-position: -60px bottom; }
.tabBntDis .bntBgT { color: #999999; padding: 1px 0 0; background: url(/images/chiles/btn_form_disabled_bg_t.png) repeat-x scroll left top transparent; }
.tabBntDis .bntBgB { background: url(/images/chiles/btn_form_disabled_bg_b.png) repeat-x scroll left bottom transparent; }
.machBnt .bntLT { background: url(/images/chiles/bntMachSprite.png) 0 0; }
.machBnt .bntLB { background: url(/images/chiles/bntMachSprite.png) 0 -5px; }
.machBnt .bntRT { background: url(/images/chiles/bntMachSprite.png) 0 -10px; }
.machBnt .bntRB { background: url(/images/chiles/bntMachSprite.png) 0 -15px; }
.machBnt .bntTop { background: url(/images/chiles/bntMachSprite.png) repeat-x scroll 0 -20px; }
.machBnt .bntBg { height: 14px; padding: 0; background: url(/images/chiles/bntMachSprite.png) repeat-x scroll 0 -30px; }
.machBnt .bntBot { background: url(/images/chiles/bntMachSprite.png) repeat-x scroll 0 -25px; }
.machBnt .bntLS { background: url(/images/chiles/bntMachBSprite.png) repeat-y 0 0; }
.machBnt .bntRS { background: url(/images/chiles/bntMachBSprite.png) repeat-y -22px 0; }
.machBnt_sel .bntLT { background: url(/images/chiles/bntMachSprite.png) 0 -65px; }
.machBnt_sel .bntLB { background: url(/images/chiles/bntMachSprite.png) 0 -70px; }
.machBnt_sel .bntRT { background: url(/images/chiles/bntMachSprite.png) 0 -75px; }
.machBnt_sel .bntRB { background: url(/images/chiles/bntMachSprite.png) 0 -80px; }
.machBnt_sel .bntTop { background: url(/images/chiles/bntMachSprite.png) repeat-x scroll 0 -85px; }
.machBnt_sel .bntBg { height: 14px; padding: 0 6px; background: url(/images/chiles/bntMachSprite.png) repeat-x scroll 0 -95px; }
.machBnt_sel .bntBot { background: url(/images/chiles/bntMachSprite.png) repeat-x scroll 0 -90px; }
.machBnt_sel .bntLS { background: url(/images/chiles/bntMachBSprite.png) repeat-y -30px 0; }
.machBnt_sel .bntRS { background: url(/images/chiles/bntMachBSprite.png) repeat-y -52px 0; }
.machBntDis .bntLT { background: url(/images/chiles/bntMachSprite.png) 0 -130px; }
.machBntDis .bntLB { background: url(/images/chiles/bntMachSprite.png) 0 -135px; }
.machBntDis .bntRT { background: url(/images/chiles/bntMachSprite.png) 0 -140px; }
.machBntDis .bntRB { background: url(/images/chiles/bntMachSprite.png) 0 -145px; }
.machBntDis .bntTop { background: url(/images/chiles/bntMachSprite.png) repeat-x scroll 0 -150px; }
.machBntDis .bntBg { height: 14px; padding: 0 6px; color: #808080; background: url(/images/chiles/bntMachSprite.png) repeat-x scroll 0 -160px; }
.machBntDis .bntBot { background: url(/images/chiles/bntMachSprite.png) repeat-x scroll 0 -155px; }
.machBntDis .bntLS { background: url(/images/chiles/bntMachBSprite.png) repeat-y -60px 0; }
.machBntDis .bntRS { background: url(/images/chiles/bntMachBSprite.png) repeat-y -82px 0; }

.portletTitleL,
.portletTitleR,
.portletHeaderBg,
.portletFooterL,
.portletFooterR { background-image: url(/images/chiles/dash_border.png; ); }
img.portletFooterL, img.portletFooterR { vertical-align: bottom; }
img.portletTitleL, img.portletTitleR { vertical-align: top; }

.portletBgColor { background-color: #FFFFFF; }
.portletHeaderBg { background-position: 0 0; background-repeat: repeat-x; }
.portletTitleL { background-position: 0 -30px; }
.portletTitleR { background-position: 0 -60px; }
.portletFooterL { background-position: 0 -100px; }
.portletFooterR { background-position: 0 -90px; }
.portletTitleBg { background: #FFFFFF url(/images/icons/dashboard/chiles/dashboard.png) no-repeat right top; height: 20px; }
.portletTitle { position: relative; left: -3px; top: 2px; }
.portletTitleText { font-weight: bold; white-space: nowrap; font-size: 11px; color: #000000; }
.portletTopCorner { background: url(/images/icons/dashboard/chiles/dashboard.png) no-repeat left top; float: left; padding: 0px 10px 0 0px; height: 20px; font-size: .7em; font-weight: bold; }
.portletContent { background-color: #FFFFFF; }
.portletLeftSide { background: #FFFFFF url(/images/icons/dashboard/chiles/dashboard.png) no-repeat left -20px; padding-left: 3px; } 
.portletRightSide { background: #FFFFFF url(/images/icons/dashboard/chiles/dashboard.png) no-repeat right -20px; padding-right: 3px; } 
.portletFooter { background: #FFFFFF url(/images/icons/dashboard/chiles/dashboard.png) no-repeat right -1597px; height: 3px; } 
.portletBottomCorner { background: #FFFFFF url(/images/icons/dashboard/chiles/dashboard.png) no-repeat left -1597px; float: left; padding: 3px 0px 0 10px; zoom: 1; }
.portletIcons { }
.portletMenuSpan { display: inline-block; margin: 0 2px; width: 18px; height: 15px; }

.portletIconMenu,
.portletIconMenuSel,
.portletIconRefresh,
.portletIconMinimize,
.portletIconMaximize,
.portletIconHide,
.iCalNewEvent,
.iAccepted,
.iTentative,
.iUnavailable,
.iCancelled,
.iTask,
.iCall,
.iRecurring,
.iReminder,
.acceptedIcon,
.tentativeIcon,
.cancelledIcon,
.taskIcon,
.callIcon,
.recurringIcon,
.reminderIcon,
.iArrowLeft,
.iArrowRight,
.iExpand,
.iCollapse,
.iBullet,
.iRefreshLine,
.iGraph,
.iSendEmail { display: inline-block; height: 15px; width: 16px; margin: 0 2px; background: url(/images/chiles/dashboard_icons.png) no-repeat; text-decoration: none; zoom: 1; }

.portletIconMenu { background-position: 0px 0; }
.portletIconMenuSel { background-position: -20px 0; }
.portletIconRefresh { background-position: 0px -20px; }
.portletIconRefresh:hover { background-position: -20px -20px; }
.portletIconRefresh:active { background-position: -40px -20px; }
.portletIconRefreshing { display: inline-block; height: 15px; width: 16px; margin: 0 2px; background: url(/images/chiles/dashboard/anim3-dark.gif) no-repeat; text-decoration: none; zoom: 1; }
.portletIconMinimize { background-position: 0px -40px; }
.portletIconMinimize:hover { background-position: -20px -40px; }
.portletIconMinimize:active { background-position: -40px -40px; }
.portletIconMaximize { background-position: 0px -60px; }
.portletIconMaximize:hover { background-position: -20px -60px; }
.portletIconMaximize:active { background-position: -40px -60px; }
.portletIconHide { background-position: 0px -80px; }
.portletIconHide:hover { background-position: -20px -80px; }
.portletIconHide:active { background-position: -40px -80px; }

.iCalNewEvent { background-position: 0 -180px; }
.iCalNewEvent:active { background-position: -20px -180px; }

.iAccepted,
.acceptedIcon { background-position: -40px -240px; }

.iTentative,
.tentativeIcon { background-position: 0 -260px; }

.iUnavailable,
.unavailableIcon { background-position: -20px -260px; }

.iCancelled,
.cancelledIcon { background-position: 0 -240px; }

.iTask,
.taskIcon { background-position: 0 -220px; }

.iCall,
.callIcon { background-position: -20px -240px; }

.iRecurring,
.recurringIcon { background-position: -40px -220px; }

.iReminder,
.reminderIcon { background-position: -20px -220px; }
.iArrowLeft { margin: 0 20px; background-position: 0 -140px; }

.iArrowLeft:focus,
.iArrowLeft:active { background-position: -20px -140px; }

.iArrowRight { margin: 0 20px; background-position: 0 -160px; }

.iArrowRight:focus,
.iArrowRight:active { background-position: -20px -160px; }

.iExpand { background-position: 0 -120px; }
.iExpand:active { background-position: -20px -120px; }
.iCollapse { background-position: 0 -100px; }
.iCollapse:active { background-position: -20px -100px; }
.iBullet { width: 6px; background-position: 0 -276px; }

.iRefreshLine,
.iGraph,
.iSendEmail { height: 14px; width: 14px; vertical-align: top; margin: 0; }

.iRefreshLine { background-position: 0px -300px; }
.iGraph { background-position: -40px -300px; }
.iSendEmail { background-position: -20px -300px; }
body.body_2010 { padding-left: 9px; padding-right: 9px; }
.plchiles .bgontab { border-left: 1px solid #333333; border-top: 1px solid #333333; padding: 0 7px; }
.plchiles .bgofftab { border-top: 1px solid #AFB5BF; border-bottom: 1px solid #333333; background: url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll -50px 0px; padding: 0 7px; background-color: #FFFFFF; }
.bghovertab { border-top: 1px solid #AFB5BF; background: url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll 0 0; }
.bgtabrborder { border-bottom: 1px solid #333333; border-top: 1px solid #AFB5BF; background-color: #AFB5BF; }
.bgtabrborder_s { border-bottom: 1px solid #333333; border-top: 1px solid #AFB5BF; background-color: #000000; }
.bghovertab { color: #FFFFFF; background-position: -50px 0px; padding: 0 7px; }
.bghovertab .ontab { color: #FFFFFF; }
.bghovertab .offtab { color: #FFFFFF; }
.bghovertab .menu_tri { background-position: 0 -100px; }
.ontab { font-size: 11px; } 
.offtab { font-size: 11px; } 
.plchiles .menu_start { background-repeat: no-repeat; background-position: 0 0; }
.plchiles .menu_end{ background-repeat: no-repeat; background-position: right 0; }

.menu_tri,
.menu_tri_silver { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) no-repeat -50px -100px; display: inline-block; height: 7px; width: 7px; position: relative; left: 3px; top: 2px; }

.menu_tri_silver { background-position: 0 -100px; }
.ontab .menu_tri { background-position: 0 -100px; }

.span_menu_tri_on,
.span_menu_tri_off { display: inline-block; position: relative; width: 12px; }

.span_menu_tri_on .menu_tri,
.span_menu_tri_on .menu_tri_silver { background-position: 0 -100px; } 

.menu_tri_click,
.menu_tri_silver_click { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) no-repeat -50px -100px; display: inline-block; height: 10px; width: 10px; position: relative; left: 3px; top: 2px; padding: 0 0 5px 5px; }
.menu_tri_silver_click { background-position: 0px -100px; }

.menuDiv { position: absolute; }
.menuLeftDiv { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll 0 bottom; padding: 0 0 0 2px; }
.menuRightDiv { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll right bottom; padding: 0 2px 0px 0px; }
.menuBottomDiv { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll 0 bottom; padding: 0 0px 5px 0px; }
.menuTableDiv { border: 1px solid #B5B5B5; background-color: #4D4D4D; padding: 0 1px; }
.menuSeparator { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll 0 -292px; height: 3px; font-size: 0; }
.menuInnerTable { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) no-repeat scroll -20px -340px; position: relative; }
.menuChildIcon { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll -400px -100px; display: inline-block; height: 6px; width: 6px; float: right; font-size: 6px; margin: 3px 5px; zoom: 1; }
.ddmTextOver .menuChildIcon { background-position: -450px -100px; }
.menuDetailIcon { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll -100px -100px; display: inline-block; height: 13px; width: 13px; float: right; margin: 0px 5px; zoom: 1; }
.menuDetailIcon:hover { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll -150px -100px; display: inline-block; height: 13px; width: 13px; float: right; margin: 0px 5px; zoom: 1; }
.menuNewIcon { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll -200px -100px; display: inline-block; height: 13px; width: 13px; float: right; margin: 0px 5px; zoom: 1; }
.menuNewIcon:hover { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll -250px -100px; display: inline-block; height: 13px; width: 13px; float: right; margin: 0px 5px; zoom: 1; }
.menuSearchIcon { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll -300px -100px; display: inline-block; height: 13px; width: 13px; float: right; margin: 0px 5px; zoom: 1; }
.menuSearchIcon:hover { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll -350px -100px; display: inline-block; height: 13px; width: 13px; float: right; margin: 0px 5px; zoom: 1; }
.menuRecentIcon { background: transparent url(/images/tabs/png/chiles/menu_sprite.png) repeat scroll -500px -100px; display: inline-block; height: 16px; width: 16px; margin: 0px 2px; zoom: 1; }
.menuShortcutIcon { background: transparent url(/images/chiles/navmenu/navmenu_icon_shortcuts.png) no-repeat scroll; display: inline-block; height: 16px; width: 16px; margin: 0px; zoom: 1; }
.timeEntryHasMemo { background: url(/images/forms/has_memo.png) no-repeat right top transparent; }

/* put this at the end so it will override other css */

.dottedlink { border-bottom: 1px dotted black; text-decoration: none; }

/* Availability Machine default states for print mode */
.uir-availability-header td { border-bottom: 1px solid #CCCCCC; border-left: 1px solid #CCCCCC; }
.uir-availability-header td:first-child {border-left: none;}
.uir-availability-row td.uir-availability-cell {border-left: 1px solid #FFFFFF; border-bottom: 1px solid #FFFFFF; }
.uir-availability-row td.uir-availability-name + td.uir-availability-cell{border-left: 1px solid #CCCCCC;}

    .ri_customicon1,
    .ri_customicon2,
    .ri_customicon3,
    .ri_customicon4,
    .ri_customicon5,
    .ri_customicon6,
    .ri_customicon7,
    .ri_customicon8,
    .ri_customicon9,
    .ri_customicon10,
    .ri_customicon11,
    .ri_customicon12,
    .ri_customicon13,
    .ri_customicon14,
    .ri_customicon15,
    .ri_customicon16,
    .ri_customicon17,
    .ri_customicon18,
    .ri_customicon19,
    .ri_customicon20,
    .ri_customicon21,
    .ri_customicon22,
    .ri_customicon23,
    .ri_customicon24,
    .ri_customicon25,
    .ri_customicon26,
    .ri_customicon27,
    .ri_customicon28,
    .ri_customicon29,
    .ri_customicon30,
    .ri_customicon31,
    .ri_customicon32,
    .ri_customicon33,
    .ri_customicon34,
    .ri_customicon35,
    .ri_customicon36,
    .ri_customicon37,
    .ri_customicon38,
    .ri_customicon39,
    .ri_customicon40,
    .ri_customicon41,
    .ri_customicon42,
    .ri_customicon43,
    .ri_customicon44,
    .ri_customicon45,
    .ri_customicon46,
    .ri_customicon47,
    .ri_customicon48,
    .ri_customicon49,
    .ri_customicon50,
    .ri_customicon51,
    .ri_customicon52,
    .ri_customicon53,
    .ri_customicon54,
    .ri_customicon55,
    .ri_customicon56,
    .ri_customicon57,
    .ri_customicon58,
    .ri_customicon59,
    .ri_customicon60,
    .ri_customicon61,
    .ri_customicon62,
    .ri_customicon63,
    .ri_customicon64,
    .ri_customicon65,
    .ri_customicon66,
    .ri_customicon67,
    .ri_customicon68,
    .ri_customicon69,
.ri_customicon70 { background: url(/images/chiles/newBarIconsSprite2.png) no-repeat; height: 16px; width: 16px; margin-right: 5px; vertical-align: bottom; border: none; }

    .ri_customicon1			{ background-position: -100px -25px; }
    a.newbar .ri_customicon1		{ background-position: -25px -25px; }
    a.newbar:hover .ri_customicon1	{ background-position: -75px -25px; }
    .ri_customicon2			{ background-position: -100px -50px; }
    a.newbar .ri_customicon2		{ background-position: -25px -50px; }
    a.newbar:hover .ri_customicon2	{ background-position: -75px -50px; }
    .ri_customicon3			{ background-position: -100px -75px; }
    a.newbar .ri_customicon3		{ background-position: -25px -75px; }
    a.newbar:hover .ri_customicon3	{ background-position: -75px -75px; }
    .ri_customicon4			{ background-position: -100px -100px; }
    a.newbar .ri_customicon4		{ background-position: -25px -100px; }
    a.newbar:hover .ri_customicon4	{ background-position: -75px -100px; }
    .ri_customicon5			{ background-position: -100px -125px; }
    a.newbar .ri_customicon5		{ background-position: -25px -125px; }
    a.newbar:hover .ri_customicon5	{ background-position: -75px -125px; }
    .ri_customicon6			{ background-position: -100px -150px; }
    a.newbar .ri_customicon6		{ background-position: -25px -150px; }
    a.newbar:hover .ri_customicon6	{ background-position: -75px -150px; }
    .ri_customicon7			{ background-position: -100px -175px; }
    a.newbar .ri_customicon7		{ background-position: -25px -175px; }
    a.newbar:hover .ri_customicon7	{ background-position: -75px -175px; }
    .ri_customicon8			{ background-position: -100px -200px; }
    a.newbar .ri_customicon8		{ background-position: -25px -200px; }
    a.newbar:hover .ri_customicon8	{ background-position: -75px -200px; }
    .ri_customicon9			{ background-position: -100px -225px; }
    a.newbar .ri_customicon9		{ background-position: -25px -225px; }
    a.newbar:hover .ri_customicon9	{ background-position: -75px -225px; }
    .ri_customicon10			{ background-position: -100px -250px; }
    a.newbar .ri_customicon10		{ background-position: -25px -250px; }
    a.newbar:hover .ri_customicon10	{ background-position: -75px -250px; }
    .ri_customicon11			{ background-position: -100px -275px; }
    a.newbar .ri_customicon11		{ background-position: -25px -275px; }
    a.newbar:hover .ri_customicon11	{ background-position: -75px -275px; }
    .ri_customicon12			{ background-position: -100px -300px; }
    a.newbar .ri_customicon12		{ background-position: -25px -300px; }
    a.newbar:hover .ri_customicon12	{ background-position: -75px -300px; }
    .ri_customicon13			{ background-position: -100px -325px; }
    a.newbar .ri_customicon13		{ background-position: -25px -325px; }
    a.newbar:hover .ri_customicon13	{ background-position: -75px -325px; }
    .ri_customicon14			{ background-position: -100px -350px; }
    a.newbar .ri_customicon14		{ background-position: -25px -350px; }
    a.newbar:hover .ri_customicon14	{ background-position: -75px -350px; }
    .ri_customicon15			{ background-position: -100px -375px; }
    a.newbar .ri_customicon15		{ background-position: -25px -375px; }
    a.newbar:hover .ri_customicon15	{ background-position: -75px -375px; }
    .ri_customicon16			{ background-position: -100px -400px; }
    a.newbar .ri_customicon16		{ background-position: -25px -400px; }
    a.newbar:hover .ri_customicon16	{ background-position: -75px -400px; }
    .ri_customicon17			{ background-position: -100px -425px; }
    a.newbar .ri_customicon17		{ background-position: -25px -425px; }
    a.newbar:hover .ri_customicon17	{ background-position: -75px -425px; }
    .ri_customicon18			{ background-position: -100px -450px; }
    a.newbar .ri_customicon18		{ background-position: -25px -450px; }
    a.newbar:hover .ri_customicon18	{ background-position: -75px -450px; }
    .ri_customicon19			{ background-position: -100px -475px; }
    a.newbar .ri_customicon19		{ background-position: -25px -475px; }
    a.newbar:hover .ri_customicon19	{ background-position: -75px -475px; }
    .ri_customicon20			{ background-position: -100px -500px; }
    a.newbar .ri_customicon20		{ background-position: -25px -500px; }
    a.newbar:hover .ri_customicon20	{ background-position: -75px -500px; }
    .ri_customicon21			{ background-position: -100px -525px; }
    a.newbar .ri_customicon21		{ background-position: -25px -525px; }
    a.newbar:hover .ri_customicon21	{ background-position: -75px -525px; }
    .ri_customicon22			{ background-position: -100px -550px; }
    a.newbar .ri_customicon22		{ background-position: -25px -550px; }
    a.newbar:hover .ri_customicon22	{ background-position: -75px -550px; }
    .ri_customicon23			{ background-position: -100px -575px; }
    a.newbar .ri_customicon23		{ background-position: -25px -575px; }
    a.newbar:hover .ri_customicon23	{ background-position: -75px -575px; }
    .ri_customicon24			{ background-position: -100px -600px; }
    a.newbar .ri_customicon24		{ background-position: -25px -600px; }
    a.newbar:hover .ri_customicon24	{ background-position: -75px -600px; }
    .ri_customicon25			{ background-position: -100px -625px; }
    a.newbar .ri_customicon25		{ background-position: -25px -625px; }
    a.newbar:hover .ri_customicon25	{ background-position: -75px -625px; }
    .ri_customicon26			{ background-position: -100px -650px; }
    a.newbar .ri_customicon26		{ background-position: -25px -650px; }
    a.newbar:hover .ri_customicon26	{ background-position: -75px -650px; }
    .ri_customicon27			{ background-position: -100px -675px; }
    a.newbar .ri_customicon27		{ background-position: -25px -675px; }
    a.newbar:hover .ri_customicon27	{ background-position: -75px -675px; }
    .ri_customicon28			{ background-position: -100px -700px; }
    a.newbar .ri_customicon28		{ background-position: -25px -700px; }
    a.newbar:hover .ri_customicon28	{ background-position: -75px -700px; }
    .ri_customicon29			{ background-position: -100px -725px; }
    a.newbar .ri_customicon29		{ background-position: -25px -725px; }
    a.newbar:hover .ri_customicon29	{ background-position: -75px -725px; }
    .ri_customicon30			{ background-position: -100px -750px; }
    a.newbar .ri_customicon30		{ background-position: -25px -750px; }
    a.newbar:hover .ri_customicon30	{ background-position: -75px -750px; }
    .ri_customicon31			{ background-position: -100px -775px; }
    a.newbar .ri_customicon31		{ background-position: -25px -775px; }
    a.newbar:hover .ri_customicon31	{ background-position: -75px -775px; }
    .ri_customicon32			{ background-position: -100px -800px; }
    a.newbar .ri_customicon32		{ background-position: -25px -800px; }
    a.newbar:hover .ri_customicon32	{ background-position: -75px -800px; }
    .ri_customicon33			{ background-position: -100px -825px; }
    a.newbar .ri_customicon33		{ background-position: -25px -825px; }
    a.newbar:hover .ri_customicon33	{ background-position: -75px -825px; }
    .ri_customicon34			{ background-position: -100px -850px; }
    a.newbar .ri_customicon34		{ background-position: -25px -850px; }
    a.newbar:hover .ri_customicon34	{ background-position: -75px -850px; }
    .ri_customicon35			{ background-position: -100px -875px; }
    a.newbar .ri_customicon35		{ background-position: -25px -875px; }
    a.newbar:hover .ri_customicon35	{ background-position: -75px -875px; }
    .ri_customicon36			{ background-position: -100px -900px; }
    a.newbar .ri_customicon36		{ background-position: -25px -900px; }
    a.newbar:hover .ri_customicon36	{ background-position: -75px -900px; }
    .ri_customicon37			{ background-position: -100px -925px; }
    a.newbar .ri_customicon37		{ background-position: -25px -925px; }
    a.newbar:hover .ri_customicon37	{ background-position: -75px -925px; }
    .ri_customicon38			{ background-position: -100px -950px; }
    a.newbar .ri_customicon38		{ background-position: -25px -950px; }
    a.newbar:hover .ri_customicon38	{ background-position: -75px -950px; }
    .ri_customicon39			{ background-position: -100px -975px; }
    a.newbar .ri_customicon39		{ background-position: -25px -975px; }
    a.newbar:hover .ri_customicon39	{ background-position: -75px -975px; }
    .ri_customicon40			{ background-position: -100px -1000px; }
    a.newbar .ri_customicon40		{ background-position: -25px -1000px; }
    a.newbar:hover .ri_customicon40	{ background-position: -75px -1000px; }
    .ri_customicon41			{ background-position: -100px -1025px; }
    a.newbar .ri_customicon41		{ background-position: -25px -1025px; }
    a.newbar:hover .ri_customicon41	{ background-position: -75px -1025px; }
    .ri_customicon42			{ background-position: -100px -1050px; }
    a.newbar .ri_customicon42		{ background-position: -25px -1050px; }
    a.newbar:hover .ri_customicon42	{ background-position: -75px -1050px; }
    .ri_customicon43			{ background-position: -100px -1075px; }
    a.newbar .ri_customicon43		{ background-position: -25px -1075px; }
    a.newbar:hover .ri_customicon43	{ background-position: -75px -1075px; }
    .ri_customicon44			{ background-position: -100px -1100px; }
    a.newbar .ri_customicon44		{ background-position: -25px -1100px; }
    a.newbar:hover .ri_customicon44	{ background-position: -75px -1100px; }
    .ri_customicon45			{ background-position: -100px -1125px; }
    a.newbar .ri_customicon45		{ background-position: -25px -1125px; }
    a.newbar:hover .ri_customicon45	{ background-position: -75px -1125px; }
    .ri_customicon46			{ background-position: -100px -1150px; }
    a.newbar .ri_customicon46		{ background-position: -25px -1150px; }
    a.newbar:hover .ri_customicon46	{ background-position: -75px -1150px; }
    .ri_customicon47			{ background-position: -100px -1175px; }
    a.newbar .ri_customicon47		{ background-position: -25px -1175px; }
    a.newbar:hover .ri_customicon47	{ background-position: -75px -1175px; }
    .ri_customicon48			{ background-position: -100px -1200px; }
    a.newbar .ri_customicon48		{ background-position: -25px -1200px; }
    a.newbar:hover .ri_customicon48	{ background-position: -75px -1200px; }
    .ri_customicon49			{ background-position: -100px -1225px; }
    a.newbar .ri_customicon49		{ background-position: -25px -1225px; }
    a.newbar:hover .ri_customicon49	{ background-position: -75px -1225px; }
    .ri_customicon50			{ background-position: -100px -1250px; }
    a.newbar .ri_customicon50		{ background-position: -25px -1250px; }
    a.newbar:hover .ri_customicon50	{ background-position: -75px -1250px; }
    .ri_customicon51			{ background-position: -100px -1275px; }
    a.newbar .ri_customicon51		{ background-position: -25px -1275px; }
    a.newbar:hover .ri_customicon51	{ background-position: -75px -1275px; }
    .ri_customicon52			{ background-position: -100px -1300px; }
    a.newbar .ri_customicon52		{ background-position: -25px -1300px; }
    a.newbar:hover .ri_customicon52	{ background-position: -75px -1300px; }
    .ri_customicon53			{ background-position: -100px -1325px; }
    a.newbar .ri_customicon53		{ background-position: -25px -1325px; }
    a.newbar:hover .ri_customicon53	{ background-position: -75px -1325px; }
    .ri_customicon54			{ background-position: -100px -1350px; }
    a.newbar .ri_customicon54		{ background-position: -25px -1350px; }
    a.newbar:hover .ri_customicon54	{ background-position: -75px -1350px; }
    .ri_customicon55			{ background-position: -100px -1375px; }
    a.newbar .ri_customicon55		{ background-position: -25px -1375px; }
    a.newbar:hover .ri_customicon55	{ background-position: -75px -1375px; }
    .ri_customicon56			{ background-position: -100px -1400px; }
    a.newbar .ri_customicon56		{ background-position: -25px -1400px; }
    a.newbar:hover .ri_customicon56	{ background-position: -75px -1400px; }
    .ri_customicon57			{ background-position: -100px -1425px; }
    a.newbar .ri_customicon57		{ background-position: -25px -1425px; }
    a.newbar:hover .ri_customicon57	{ background-position: -75px -1425px; }
    .ri_customicon58			{ background-position: -100px -1450px; }
    a.newbar .ri_customicon58		{ background-position: -25px -1450px; }
    a.newbar:hover .ri_customicon58	{ background-position: -75px -1450px; }
    .ri_customicon59			{ background-position: -100px -1475px; }
    a.newbar .ri_customicon59		{ background-position: -25px -1475px; }
    a.newbar:hover .ri_customicon59	{ background-position: -75px -1475px; }
    .ri_customicon60			{ background-position: -100px -1500px; }
    a.newbar .ri_customicon60		{ background-position: -25px -1500px; }
    a.newbar:hover .ri_customicon60	{ background-position: -75px -1500px; }
    .ri_customicon61			{ background-position: -100px -1525px; }
    a.newbar .ri_customicon61		{ background-position: -25px -1525px; }
    a.newbar:hover .ri_customicon61	{ background-position: -75px -1525px; }
    .ri_customicon62			{ background-position: -100px -1550px; }
    a.newbar .ri_customicon62		{ background-position: -25px -1550px; }
    a.newbar:hover .ri_customicon62	{ background-position: -75px -1550px; }
    .ri_customicon63			{ background-position: -100px -1575px; }
    a.newbar .ri_customicon63		{ background-position: -25px -1575px; }
    a.newbar:hover .ri_customicon63	{ background-position: -75px -1575px; }
    .ri_customicon64			{ background-position: -100px -1600px; }
    a.newbar .ri_customicon64		{ background-position: -25px -1600px; }
    a.newbar:hover .ri_customicon64	{ background-position: -75px -1600px; }
    .ri_customicon65			{ background-position: -100px -1625px; }
    a.newbar .ri_customicon65		{ background-position: -25px -1625px; }
    a.newbar:hover .ri_customicon65	{ background-position: -75px -1625px; }
    .ri_customicon66			{ background-position: -100px -1650px; }
    a.newbar .ri_customicon66		{ background-position: -25px -1650px; }
    a.newbar:hover .ri_customicon66	{ background-position: -75px -1650px; }
    .ri_customicon67			{ background-position: -100px -1675px; }
    a.newbar .ri_customicon67		{ background-position: -25px -1675px; }
    a.newbar:hover .ri_customicon67	{ background-position: -75px -1675px; }
    .ri_customicon68			{ background-position: -100px -1700px; }
    a.newbar .ri_customicon68		{ background-position: -25px -1700px; }
    a.newbar:hover .ri_customicon68	{ background-position: -75px -1700px; }
    .ri_customicon69			{ background-position: -100px -1725px; }
    a.newbar .ri_customicon69		{ background-position: -25px -1725px; }
    a.newbar:hover .ri_customicon69	{ background-position: -75px -1725px; }
    .ri_customicon70			{ background-position: -100px -1750px; }
    a.newbar .ri_customicon70		{ background-position: -25px -1750px; }
    a.newbar:hover .ri_customicon70	{ background-position: -75px -1750px; }





/* NL PerColorTheme */

.blueBG			{ background: #EFEFEF; }
.dkBlueSel		{ FONT-SIZE: 8pt; color: white; background-color: #838383; }
.portlet		{ background-color: #FFFFFF; }
.portletlabel		{ color: #000000; background-color: #FFFFFF; }
.portletlabelDragDrop	{ color: #000000; background-color: #FFFFFF; }
.portletDragDropIcon	{ cursor: move; }
.quickaddDragDropIcon	{ cursor: move; }
.portletDragDropBar	{ color: #FFFFFF; }
.portletFilterBar	{ background-color: #EEEEEE; }
.tasklinkSearchBar	{ color: #DDDDDD; background-color: #EEEEEE; }

/* empty style used for portlet drag and drop */
.portletHandle		{ }
.quickaddHandle		{ }
.bgbar			{ background-color: #000000; }
.bglt			{ background-color: #EEEEEE; }
.bglttext		{ background-color: #EEEEEE; font-size: 8pt; color: #000000; }
.bglttextctr		{ background-color: #EEEEEE; font-size: 8pt ; text-align: center; color: #000000; }
.bglttextrt		{ background-color: #EEEEEE; font-size: 8pt ; text-align: right; color: #000000; }
.bgbutton		{ background-color: #FFFFFF; }
.bgmd			{ background-color: #000000; }
.bgdk			{ background-color: #000000; }
.bgontab		{ background-color: #000000; text-align: center; vertical-align: middle; }
.bgofftab		{ background-color: #FFFFFF; text-align: center; vertical-align: middle; }
.bghovertab		{ background-color: #000000; text-align: center; vertical-align: middle; }

/* list header */
.listheadertd,
.listheadertdleft	{
	vertical-align: top; height: 100%; border-style: solid; border-color: #B4B4B4;
	background: #FFFFFF url(/images/core/list/header_bg_b.png) repeat-x left bottom;
	box-sizing: border-box; -moz-box-sizing: border-box; -webkit-box-sizing: border-box;
}

/* form tab bar */
.bgtabbar		{ background-color: #000000; }
.bdtabblock		{ border-style: solid; border-color: #000000; background-color: #F7F7F7; }
.tabblockcorner		{ background-color: #000000; }
.tabcontent		{ background-color: #F7F7F7; padding: 5px 0; }
.nltabcontent		{ background-color: #F7F7F7; padding: 5px 0; }

.formtabon		{ padding: 3px 8px 3px 8px; background: #000000 url(/images/forms/tab/tab_on.png) repeat-x; }
.formtaboff		{ padding: 3px 8px 3px 8px; background: #FFFFFF url(/images/forms/tab/tab_off.png) repeat-x; }
.formtabtexton		{ text-decoration: none; color: #ffffff; }
.formtabtextoff		{ text-decoration: none; color: #000000; }

/* form tab bar */
.formtabsep		{ width: 1px; background-color: #808080; font-size: 0px; min-width: 1px; }
.formtabseplon		{ position: relative; width: 2px; vertical-align: top; background: #000000 url(/images/forms/tab/tab_on_l.png) repeat-y; }
.formtabseploff		{ position: relative; width: 2px; vertical-align: top; background: #FFFFFF url(/images/forms/tab/tab_off_l.png) repeat-y; }
.formtabsepron		{ position: relative; width: 5px; vertical-align: top; background: #000000 url(/images/forms/tab/tab_on_r.png) repeat-y; }
.formtabseproff		{ position: relative; width: 5px; vertical-align: top; background: #FFFFFF url(/images/forms/tab/tab_off_r.png) repeat-y; }

/* form tab bar corners */
.formtabsepclon		{ position: relative; top: 0px; width: 5px; height: 5px; font-size: 0px; background: #000000 url(/images/forms/tab/tab_on_corner_l.png) no-repeat; }
.formtabsepcloff	{ position: relative; top: 0px; width: 5px; height: 5px; font-size: 0px; background: #FFFFFF url(/images/forms/tab/tab_off_corner_l.png) no-repeat; }
.formtabsepcron		{ position: relative; top: 0px; width: 5px; height: 5px; font-size: 0px; background: #000000 url(/images/forms/tab/tab_on_corner_r.png) no-repeat; }
.formtabsepcroff	{ position: relative; top: 0px; width: 5px; height: 5px; font-size: 0px; background: #FFFFFF url(/images/forms/tab/tab_off_corner_r.png) no-repeat; }

/* form tab bar corners */
.unrollformtabbar	{ font-size: 8pt; background: #000000 url(/images/forms/tab/unrolltab_bg.png) repeat-x; }
.unrollformtabbarul	{ width: 5px; height: 5px; background: url(/images/core/common/corner_r10.png) -5px -5px no-repeat; }
.unrollformtabbarur	{ width: 5px; height: 5px; background: url(/images/core/common/corner_r10.png) 0px -5px no-repeat; }
.unrollformtabbarlr	{ width: 5px; height: 5px; background: url(/images/core/common/corner_r10_inverted.png) 0px -5px no-repeat; }

/* unrolled form tab expanded */
.unrollformtabheaderesep	{ height: 3px; font-size: 0px; background: #000000 url(/images/forms/border/unrollsubtab_header_sep.png) repeat-x; }
.unrollformtablexpand		{ width: 4px; font-size: 0px; background: #000000 url(/images/forms/border/unrolltab_l_expand.png) repeat-y; }
.unrollformtabrexpand		{ width: 4px; font-size: 0px; background: #000000 url(/images/forms/border/unrolltab_r_expand.png) repeat-y; }
.unrollformtabtexpand		{ height: 5px; font-size: 0px; background: #000000 url(/images/forms/border/unrolltab_t_expand.png) repeat-x; }
.unrollformtabbexpand		{ height: 5px; font-size: 0px; background: #000000 url(/images/forms/border/unrolltab_b_expand.png) repeat-x; }
.unrollformtabheaderexpand	{ padding: 5px; background: #000000 url(/images/forms/border/unrolltab_header_expand.png) repeat-x; }

/* corners for unrolled form tab expanded */
.unrollformtabulexpand		{ position: absolute; background: #000000 url(/images/forms/border/unrolltab_square_corner_expand.png) 0 0 no-repeat; }
.unrollformtabulfirstexpand	{ position: absolute; background: #000000 url(/images/forms/border/unrolltab_rounded_corner_expand.png) 0 0 no-repeat; }
.unrollformtaburexpand		{ position: absolute; right: 0px; background: #000000 url(/images/forms/border/unrolltab_square_corner_expand.png) -8px 0px no-repeat; }
.unrollformtaburfirstexpand	{ position: absolute; right: 0px; background: #000000 url(/images/forms/border/unrolltab_rounded_corner_expand.png) -8px 0px no-repeat; }
.unrollformtabllexpand		{ position: absolute; top: -3px; background: #000000 url(/images/forms/border/unrolltab_square_corner_expand.png) 0px -8px no-repeat; }
.unrollformtablllastexpand	{ position: absolute; top: -3px; background: #000000 url(/images/forms/border/unrolltab_rounded_corner_expand.png) 0px -8px no-repeat; }
.unrollformtablrexpand		{ position: absolute; right: 0px; top: -3px; background: #000000 url(/images/forms/border/unrolltab_square_corner_expand.png) -8px -8px no-repeat; }
.unrollformtablrlastexpand	{ position: absolute; right: 0px; top: -3px; background: #000000 url(/images/forms/border/unrolltab_rounded_corner_expand.png) -8px -8px no-repeat; }

/* unrolled form tab collapsed */
.unrollformtabheadercollapse	{ padding: 5px; border-bottom: 1px solid #cccccc; background: #000000 url(/images/forms/border/unrolltab_header_collapse.png) repeat-x; }

/* unrolled form tab collapsed */
.unrollformtablcollapse		{ width: 4px; font-size: 0px; background: #000000 url(/images/forms/tab/unrolltab_bg.png); }
.unrollformtabrcollapse		{ width: 4px; font-size: 0px; background: #000000 url(/images/forms/tab/unrolltab_bg.png); }
.unrollformtabtcollapse		{ height: 4px; font-size: 0px; background: #000000 url(/images/forms/tab/unrolltab_bg.png); }
.unrollformtabbcollapse		{ height: 3px; font-size: 0px; background: #000000 url(/images/forms/tab/unrolltab_bg.png); }

/* corners for unrolled form tab collapsed */
.unrollformtabulcollapse		{ position: absolute; }
.unrollformtabulfirstcollapse		{ position: absolute; }
.unrollformtaburcollapse		{ position: absolute; right: 0px; }
.unrollformtaburfirstcollapse		{ position: absolute; right: 0px; background: #000000 url(/images/forms/border/unrolltab_rounded_corner_collapse.png) -8px 0px no-repeat; }
.unrollformtabllcollapse		{ position: absolute; }
.unrollformtablllastcollapse		{ position: absolute; top: -5px; background: #000000 url(/images/forms/border/unrolltab_rounded_corner_collapse.png) 0px -8px no-repeat; }
.unrollformtablrcollapse		{ position: absolute; right: 0px; }
.unrollformtablrlastcollapse		{ position: absolute; top: -5px; right: 0px; background: #000000 url(/images/forms/border/unrolltab_rounded_corner_collapse.png) -8px -8px no-repeat; }

/*form subtab bar */
.bgsubtabbar			{ background-color: #CBCBCB; }
.subtabblock			{ background-color: #CBCBCB; }
.bdsubtabblock			{ border-style: solid; border-color: #CBCBCB; }
.subtabcontent			{ background-color: #FFFFFF; }
.nlsubtabcontent		{ background-color: #FFFFFF; }
.subtabcornerul			{ width: 4px; height: 4px; font-size: 0px; background: #CBCBCB url(/images/core/common/corner_r4_t_w.png) 0px 0px; }
.subtabcornerur			{ width: 4px; height: 4px; font-size: 0px; background: #CBCBCB url(/images/core/common/corner_r4_t_w.png) -4px 0px; }
.subtabcornerll			{ width: 4px; height: 4px; font-size: 0px; background: #CBCBCB url(/images/core/common/corner_r4_t_w.png) 0px 4px; }
.subtabcornerlr			{ width: 4px; height: 4px; font-size: 0px; background: #CBCBCB url(/images/core/common/corner_r4_t_w.png) 4px 4px; }

.formsubtab			{ color: #666666; padding: 5px; background: #000000 url(/images/forms/tab/subtab_off.png) repeat-x; }

.formsubtabon			{ padding: 2px 5px 2px 5px; background: #000000 url(/images/forms/tab/subtab_on.png) repeat-x; }
.formsubtaboff			{ padding: 2px 5px 2px 5px; border-bottom: 1px solid #808080; background: #FFFFFF url(/images/forms/tab/subtab_off.png) repeat-x; }
.formsubtabtexton		{ text-decoration: none; color: #ffffff; }
.formsubtabtextoff		{ text-decoration: none; color: #000000; }

.formsubtabsep			{ width: 1px; background-color: #808080; font-size: 0px; min-width: 1px; }
.formsubtabcorner		{ background-color: #CBCBCB; }

/* unrolled form subtab expanded */
.unrollformsubtabheaderesep		{ height: 3px; font-size: 0px; background: #000000 url(/images/forms/border/unrollsubtab_header_sep.png) repeat-x; }
.unrollformsubtablexpand		{ width: 3px; font-size: 0px; background: #000000 url(/images/forms/border/unrollsubtab_l_expand.png) repeat-y; }
.unrollformsubtabrexpand		{ width: 3px; font-size: 0px; background: #000000 url(/images/forms/border/unrollsubtab_r_expand.png) repeat-y; }
.unrollformsubtabtexpand		{ height: 4px; font-size: 0px; background: #000000 url(/images/forms/border/unrollsubtab_t_expand.png) repeat-x; }
.unrollformsubtabbexpand		{ height: 4px; font-size: 0px; background: #000000 url(/images/forms/border/unrollsubtab_b_expand.png) repeat-x; }
.unrollformsubtabheaderexpand		{ padding: 5px; background: #000000 url(/images/forms/border/unrollsubtab_header_expand.png) repeat-x; }

/* corners for unrolled form subtab expanded */
.unrollformsubtabulexpand		{ position: absolute; background: #000000 url(/images/forms/border/unrollsubtab_square_corner_expand.png) 0 0 no-repeat; }
.unrollformsubtabulfirstexpand		{ position: absolute; background: #000000 url(/images/forms/border/unrollsubtab_rounded_corner_expand.png) 0 0 no-repeat; }
.unrollformsubtaburexpand		{ position: absolute; right: 0px; background: #000000 url(/images/forms/border/unrollsubtab_square_corner_expand.png) -7px 0px no-repeat; }
.unrollformsubtaburfirstexpand		{ position: absolute; right: 0px; background: #000000 url(/images/forms/border/unrollsubtab_rounded_corner_expand.png) -7px 0px no-repeat; }
.unrollformsubtabllexpand		{ position: absolute; top: -3px; background: url(/images/forms/border/unrollsubtab_square_corner_expand.png) 0px -7px no-repeat; }
.unrollformsubtablllastexpand		{ position: absolute; top: -3px; background: url(/images/forms/border/unrollsubtab_rounded_corner_expand.png) 0px -7px no-repeat; }
.unrollformsubtablrexpand		{ position: absolute; right: 0px; top: -3px; background: url(/images/forms/border/unrollsubtab_square_corner_expand.png) -7px -7px no-repeat; }
.unrollformsubtablrlastexpand		{ position: absolute; right: 0px; top: -3px; background: url(/images/forms/border/unrollsubtab_rounded_corner_expand.png) -7px -7px no-repeat; }

/* unrolled form subtab collapsed */
.unrollformsubtabheadercollapse		{ padding: 5px; border-bottom: 1px solid #cccccc; background: #000000 url(/images/forms/border/unrollsubtab_header_collapse.png) repeat-x; }

/* corners for unrolled form subtab collapsed */
.unrollformsubtabulcollapse		{ position: absolute; }
.unrollformsubtabulfirstcollapse	{ position: absolute; background: #000000 url(/images/forms/border/unrollsubtab_rounded_corner_collapse.png) 0 0 no-repeat; }
.unrollformsubtaburcollapse		{ position: absolute; right: 0px; }
.unrollformsubtaburfirstcollapse	{ position: absolute; right: 0px; background: #000000 url(/images/forms/border/unrollsubtab_rounded_corner_collapse.png) -7px 0px no-repeat; }
.unrollformsubtabllcollapse		{ position: absolute; top: -5px; }
.unrollformsubtablllastcollapse		{ position: absolute; top: -5px; background: #000000 url(/images/forms/border/unrollsubtab_rounded_corner_collapse.png) 0px -7px no-repeat; }
.unrollformsubtablrcollapse		{ position: absolute; top: -5px; right: 0px; }
.unrollformsubtablrlastcollapse		{ position: absolute; top: -5px; right: 0px; background: #000000 url(/images/forms/border/unrollsubtab_rounded_corner_collapse.png) -7px -7px no-repeat; }

.bgontabsub		{ background-color: #000000; border-top: 1px solid #808083; border-bottom: 1px solid #808083; }
.bgofftabsub		{ color: #666666; background-color: #B5B5B5; border-top: 1px solid #808083; border-bottom: 1px solid #808083; border-right: 1px solid #808083; }
.bgontabsubmid		{ background-color: #000000; border-top: 1px solid #808083; border-bottom: 1px solid #808083; }
.bgofftabsubmid		{ color: #666666; background-color: #B5B5B5; border-top: 1px solid #808083; border-bottom: 1px solid #808083; }

.ontabbottom		{ color: #000000; }
.offtabbottom		{ color: #666666; }
.bgon			{ background-color: #000000; }
.bgoff			{ background-color: #FFFFFF; }
.headbar		{ font-size: 8pt; color: #FFFFFF; text-decoration: none; background-color: #000000; }
.headbarsub		{ color: #FFFFFF; }
.headbarsubnolink	{ color: #FFFFFF; }
.crosslinktitle		{ color: #FFFFFF; }
.crosslinktext,
.crosslinktextul,
a.newbar		{ color: #E4EAF4; }
.crumb			{ color: #FFFFFF; text-decoration: none; }
.crumbover		{ color: #FFFFFF; }
.ontab			{ color: #FFFFFF; }
.offtab			{ color: #000000; }
.ontabhover		{ color: #FFFFFF; }
.offtabhover		{ color: #000000; }
.btntext		{ color: #000000; }
.btntexthover		{ color: #000000; }
.smalltextlt		{ font-size: 8pt ; background-color: #EEEEEE; color: #000000; }
.greytitle		{ color: #AAAAAA; }
.tasktitle		{ color: #FFFFFF; }
.tasktitlemed		{ color: #FFFFFF; }
.dletoggletext,
.dleheadertext		{ color: #FFFFFF; text-decoration: underline; font-size: 8pt; font-weight: normal; }
.dleheadertext		{ color: #000000; }
.newbartext		{ color: #FFFFFF; font-size: 8pt; padding: 0 0px 1px; }
.newbartextnolink	{ color: #FFFFFF; text-decoration: none; font-size: 8pt; }
.taskstatus		{ color: #FFFFFF; }

/* The 'texttable' family is the style of text in the body (line item) area of lists and machines (text color on BGLT with a thin white line on the bottom) . */
/* If any of the padding specifications change on the texttable styles, they must change on the texttableInvisible style (for Extreme Lists) */
.texttableinvisible	{ font-size: 8pt; padding: 2px 5px 2px 5px ; border-style: solid; border-width: 1px 1px 0 1px; border-color: white; visibility: hidden; }

/* 'xx' prepend for classes is for extreme lists. This is used for locating, as well as for changing the cursor style */
.texttable		{ font-size: 8pt; color: #000000; background-color: #EEEEEE; padding: 2px 5px 2px 5px ; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white #EEEEEE #EEEEEE #EEEEEE; vertical-align: top; }
.texttablebold		{ font-size: 8pt; color: #000000; background-color: #EEEEEE; padding: 2px 5px 2px 5px; font-weight: bold; border-style: solid; border-width: 1px 0 0 0; border-color: white; vertical-align: top; }
.texttablectr		{ font-size: 8pt; color: #000000; background-color: #EEEEEE; text-align: center; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 0 0 0; border-color: white; vertical-align: top; }
.texttablert		{ font-size: 8pt; color: #000000; background-color: #EEEEEE; text-align: right; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 0 0 0; border-color: white; vertical-align: top; }

tr.highlightline td	{ background-color: #EEEEEE !important; }

/* The suffix "2" is used to represent the class to be used for the second (and beyond) line of a multi-line row. */
/* Right now, for the base case, we use the same style for all lines of a multi-line row. Check out highlighted rows, below. */
.listtext,
.xxlisttext,
.listtext2,
.xxlisttext2		{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px ; background-color: white; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white; vertical-align: top; }
.listtextbold,
.xxlisttextbold,
.listtextbold2,
.xxlisttextbold2	{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: white; font-weight: bold; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white; vertical-align: top; }
.listtextctr,
.xxlisttextctr,
.listtextctr2,
.xxlisttextctr2		{ font-size: 8pt; color: #000000; text-align: center; background-color: white; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white; vertical-align: top; }
.listtextrt,
.xxlisttextrt,
.listtextrt2,
.xxlisttextrt2		{ font-size: 8pt; color: #000000; text-align: right; background-color: white; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white; vertical-align: top; }

.xxlisttext		{ cursor: pointer; }
.xxlisttextbold		{ cursor: pointer; }
.xxlisttextctr		{ cursor: pointer; }
.xxlisttextrt		{ cursor: pointer; }

.gridlisttext,
.xxgridlisttext		{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px ; background-color: #FFFFFF; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white white #CCCCCC #CCCCCC; vertical-align: top; }
.gridlisttextbold,
.xxgridlisttextbold	{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #FFFFFF; font-weight: bold; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white white #CCCCCC #CCCCCC; vertical-align: top; }
.gridlisttextctr,
.xxgridlisttextctr	{ font-size: 8pt; color: #000000; text-align: center; background-color: #FFFFFF; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white white #CCCCCC #CCCCCC; vertical-align: top; }
.gridlisttextrt,
.xxgridlisttextrt	{ font-size: 8pt; color: #000000; text-align: right; background-color: #FFFFFF; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white white #CCCCCC #CCCCCC; vertical-align: top; }

.xxgridlisttext		{ cursor: pointer; }
.xxgridlisttextbold	{ cursor: pointer; }
.xxgridlisttextctr	{ cursor: pointer; }
.xxgridlisttextrt	{ cursor: pointer; }

.gridlisttextnoedit	{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px ; background-color: #F5F5F5; color: #999999; border-style: solid; border-width: 2px 1px 1px 1px; border-color: white white #CCCCCC #CCCCCC; vertical-align: top; }
.gridlisttextboldnoedit	{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px ; font-weight: bold; background-color: #F5F5F5; color: #999999; border-width: 2px 1px 1px 1px; border-style: solid; border-color: white white #CCCCCC #CCCCCC; vertical-align: top; }
.gridlisttextctrnoedit	{ font-size: 8pt; color: #000000; text-align: center; padding: 2px 5px 2px 5px ; background-color: #F5F5F5; color: #999999; border-width: 2px 1px 1px 1px; border-style: solid; border-color: white white #CCCCCC #CCCCCC; vertical-align: top; }
.gridlisttextrtnoedit	{ font-size: 8pt; color: #000000; text-align: right; padding: 2px 5px 2px 5px ; background-color: #F5F5F5; color: #999999; border-width: 2px 1px 1px 1px; border-style: solid; border-color: white white #CCCCCC #CCCCCC; vertical-align: top; }

/* similar to above, but these are the highlight versions */
.listtexthl,
.xxlisttexthl		{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #EEEEEE; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white #EEEEEE #EEEEEE #EEEEEE; vertical-align: top; }
.listtexthlbold,
.xxlisttexthlbold	{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #EEEEEE; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white #EEEEEE #EEEEEE #EEEEEE; vertical-align: top; font-weight: bold; }
.listtexthlctr,
.xxlisttexthlctr	{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #EEEEEE; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white #EEEEEE #EEEEEE #EEEEEE; vertical-align: top; text-align: center; }
.listtexthlrt,
.xxlisttexthlrt		{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #EEEEEE; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white #EEEEEE #EEEEEE #EEEEEE; vertical-align: top; text-align: right; }
.listtexthlwht		{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #FFFFFF; border-style: solid; border-width: 1px 1px 1px 1px; border-color: #CCCCCC white white white; vertical-align: top; }

.listsanitizedhtml table	{ font-size: 8pt; }
.listsanitizedhtml ol		{ list-style: decimal inside none; }
.listsanitizedhtml ul		{ list-style: disc inside none; }

/* These classes are for the second (and beyond) lines of a highlighted, multi-line row. */
.listtexthl2,
.xxlisttexthl2		{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #EEEEEE; border-width: 0; vertical-align: top; }
.listtexthlbold2,
.xxlisttexthlbold2	{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #EEEEEE; border-width: 0; vertical-align: top; font-weight: bold; }
.listtexthlctr2,
.xxlisttexthlctr2	{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #EEEEEE; border-width: 0; vertical-align: top; text-align: center; }
.listtexthlrt2,
.xxlisttexthlrt2	{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #EEEEEE; border-width: 0; vertical-align: top; text-align: right; }
.listtexthlwht2		{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; background-color: #FFFFFF; border-width: 0; vertical-align: top; }

.xxlisttexthl		{ cursor: pointer; }
.xxlisttexthlbold	{ cursor: pointer; }
.xxlisttexthlctr	{ cursor: pointer; }
.xxlisttexthlrt		{ cursor: pointer; }

.editedcell		{ padding: 0 5px 0 0; }
.listtextnonedit,
.listtextnoneditleft,
.listtextnoneditright	{ font-size: 8pt; color: #000000; padding: 1px 4px 1px 4px ; border-style: solid; vertical-align: top; }
.listtextnoneditleft	{ border-width: 2px 0 0 2px; border-color: #000000; }
.listtextnonedit	{ border-width: 2px 0 0 0; border-color: #000000; }
.listtextnoneditright	{ border-width: 2px 2px 0 0; border-color: #000000; }
.listtextnoneditwht	{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px ; background-color: #FFFFFF; border-style: solid; border-width: 2px 1px 1px 1px; border-color: #CCCCCC white #CCCCCC white; vertical-align: top; }
.listtextinvisible	{ font-size: 8pt; padding: 2px 5px 2px 5px ; border-style: solid; border-width: 0 0 1px 0; border-color: white; visibility: hidden; }

.machineButtonRow	{ background-color: #000000; font-size: 8pt; padding: 2px 2px 2px 2px; vertical-align: top; }

/*non-inline edit machine */
.listfocusedrowleft		{ border-width: 2px 0 2px 2px; border-color: #000000; }
.listfocusedrow			{ border-width: 2px 0 2px 0; border-color: #000000; }
.listfocusedrowright		{ border-width: 2px 2px 2px 0; border-color: #000000; }
.listfocusedrowleftright	{ border-width: 2px 2px 2px 2px; border-color: #000000; }

/*inline edit machine */
.listinlinefocusedrow,
.listinlinefocusedrowleft,
.listinlinefocusedrowright,
.listinlinefocusedrowleftright	{ font-size: 8pt; color: #000000; padding: 2px 3px 2px 3px ; border-style: solid; vertical-align: top; background: #ffffff; }
.listinlinefocusedrowleft	{ border-width: 2px 0 2px 2px; border-color: #000000; }
.listinlinefocusedrow		{ border-width: 2px 0 2px 0; border-color: #000000; }
.listinlinefocusedrowright	{ border-width: 2px 2px 2px 0; border-color: #000000; }
.listinlinefocusedrowleftright	{ border-width: 2px 2px 2px 2px; border-color: #000000; }
.listinlinefocusedrowcell	{ cursor: default; padding: 1px 1px 1px 1px; border: 2px solid #cccccc; }
.listinlinefocusedrowcellnoedit	{ padding: 1px 1px 1px 1px; border: 2px solid #ffffff; }
.listcontrol .textbox,
.listcontrol .select		{ height: 25px; border: 2px solid #888888; }
.listcontrol .textarea,
.listcontrol .multiselect	{ width: 100%; border: 2px solid #888888; }

.batchxxgridlisttextbot		{ background-color: #000000; font-size: 8pt ; padding: 2px 5px 2px 5px; color: #000000; border-style: solid dotted dotted dotted; border-width: 1px 1px 1px 1px; border-color: #000000 black black black; vertical-align: top; }
.batchxxgridlisttextmid		{ background-color: #000000; font-size: 8pt ; padding: 2px 5px 2px 5px; color: #000000; border-style: solid dotted solid dotted; border-width: 1px 1px 1px 1px; border-color: #000000 black #000000 black; vertical-align: top; }
.batchxxgridlisttexttop		{ background-color: #000000; font-size: 8pt ; padding: 2px 5px 2px 5px; color: #000000; border-style: dotted dotted solid dotted; border-width: 1px 1px 1px 1px; border-color: black black #000000 black; vertical-align: top; }
.batchxxgridlisttextuni		{ background-color: #000000; font-size: 8pt ; padding: 2px 5px 2px 5px; color: #000000; border-style: dotted; border-width: 1px 1px 1px 1px; border-color: black black black black; vertical-align: top; }

.batchxxlisttextbot,
batchxxlisttextrtbot		{ background-color: white; font-size: 8pt ; padding: 2px 5px 2px 5px; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: white gray gray gray; vertical-align: top; }
.batchxxlisttextmid,
batchxxlisttextrtmid		{ background-color: white; font-size: 8pt ; padding: 2px 5px 2px 5px; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: white gray white gray; vertical-align: top; }
.batchxxlisttexttop,
batchxxlisttextrttop		{ background-color: white; font-size: 8pt ; padding: 2px 5px 2px 5px; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: gray gray white gray; vertical-align: top; }
.batchxxlisttextuni,
batchxxlisttextrtuni		{ background-color: white; font-size: 8pt ; padding: 2px 5px 2px 5px; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: gray gray gray gray; vertical-align: top; }

.batchxxlisttextrtbot		{ text-align: right; }
.batchxxlisttextrtmid		{ text-align: right; }
.batchxxlisttextrttop		{ text-align: right; }
.batchxxlisttextrtuni		{ text-align: right; }

.batchxxlisttexthlbot,
batchxxlisttexthlrtbot		{ background-color: #EEEEEE; font-size: 8pt ; padding: 2px 5px 2px 5px; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: white gray gray gray; vertical-align: top; }
.batchxxlisttexthlmid,
batchxxlisttexthlrtmid		{ background-color: #EEEEEE; font-size: 8pt ; padding: 2px 5px 2px 5px; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: white gray white gray; vertical-align: top; }
.batchxxlisttexthltop,
batchxxlisttexthlrttop		{ background-color: #EEEEEE; font-size: 8pt ; padding: 2px 5px 2px 5px; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: gray gray white gray; vertical-align: top; }
.batchxxlisttexthluni,
batchxxlisttexthlrtuni		{ background-color: #EEEEEE; font-size: 8pt ; padding: 2px 5px 2px 5px; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: gray gray gray gray; vertical-align: top; }

.batchxxlisttexthlrtbot		{ text-align: right; }
.batchxxlisttexthlrtmid		{ text-align: right; }
.batchxxlisttexthlrttop		{ text-align: right; }
.batchxxlisttexthlrtuni		{ text-align: right; }

.printtexttable			{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 0 0 0; border-color: white; vertical-align: top; }
.printtexttablebold		{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px; font-weight: bold; border-style: solid; border-width: 1px 0 0 0; border-color: white; vertical-align: top; }
.printtexttablectr		{ font-size: 8pt; color: #000000; text-align: center; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 0 0 0; border-color: white; vertical-align: top; }
.printtexttablert		{ font-size: 8pt; color: #000000; text-align: right; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 0 0 0; border-color: white; vertical-align: top; }

.seltexttable			{ background-color: #000000; font-size: 8pt; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 0 0 0; border-color: white; vertical-align: top; }
.seltexttablectr		{ background-color: #000000; font-size: 8pt ; text-align: center; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 0 0 0; border-color: white; vertical-align: top; }
.seltexttablert			{ background-color: #000000; font-size: 8pt ; text-align: right; padding: 2px 5px 2px 5px; border-style: solid; border-width: 1px 0 0 0; border-color: white; vertical-align: top; }

.sellisttext,
.sellisttexthl			{ font-size: 8pt; color: #000000; padding: 2px 5px 2px 5px ; background-color: #000000; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white #000000 #000000 #000000; vertical-align: top; }
.sellisttextctr,
.sellisttexthlctr		{ font-size: 8pt; color: #000000; text-align: center; padding: 2px 5px 2px 5px; background-color: #000000; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white #000000 #000000 #000000; vertical-align: top; }
.sellisttextrt,
.sellisttexthlrt		{ font-size: 8pt; color: #000000; text-align: right; padding: 2px 5px 2px 5px; background-color: #000000; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white #000000 #000000 #000000; vertical-align: top; }

.smalltextrt			{ color: #000000; }
.smalltext			{ color: #000000; }
.smalltexthlpad			{ font-size: 8pt ; padding: 1px; background-color: #EEEEEE; }
.smalltexthlbpad		{ font-size: 8pt ; padding: 1px; font-weight: bold; background-color: #EEEEEE; }
.tinytext			{ color: #000000; }
.text				{ color: #000000; }
.textbold			{ color: #000000; }
.textboldul			{ color: #000000; }
.textboldnolink			{ color: #000000; }
.listheadernosort		{ height: 100%; padding: 2px 5px 2px 5px; vertical-align: top; color: #666666; background-color: #DDDDDD; }
.listheadernosortwht		{ height: 100%; padding: 2px 5px 2px 5px; vertical-align: top; color: #666666; background-color: #FFFFFF; }
.listfooter			{ height: 100%; padding: 2px 5px 2px 5px; vertical-align: top; color: #666666; background-color: #EEEEEE; }

.listcornerul			{ width: 4px; height: 4px; font-size: 0px; background: #CBCBCB url(/images/core/common/corner_r3_t_gray_w.png) 0px 0px; }
.listcornerur			{ width: 4px; height: 4px; font-size: 0px; background: #CBCBCB url(/images/core/common/corner_r3_t_gray_w.png) -3px 0px; }
.listcornerll			{ width: 4px; height: 4px; font-size: 0px; background: #CBCBCB url(/images/core/common/corner_r3_t_gray_w.png) 0px 3px; }
.listcornerlr			{ width: 4px; height: 4px; font-size: 0px; background: #CBCBCB url(/images/core/common/corner_r3_t_gray_w.png) 3px 3px; }

.portletheadernosort		{ padding: 2px; border-width: 0; background-color: #DDDDDD; }
.machineheadernosort		{ height: 100%; padding: 2px 5px 2px 5px; vertical-align: top; color: #666666; background-color: #EEEEEE; }

/* Used by the Web store for item and category descriptions and titles */
.medtext			{ color: #000000; }
.medtextbold			{ color: #000000; }
.medtextboldnolink		{ color: #000000; }
.bgltmedtext			{ background-color: #EEEEEE; font-size: 8pt; color: #000000; }

/* drop-down menu styles */
.ddmAnchor			{ text-decoration: none; cursor: default; padding: 0 4px; white-space: nowrap; }
.ddmAnchorDisabled		{ text-decoration: none; font-style: italic; cursor: default; color: #555555; }
.ddmInnerTable			{ border-style: solid; border-width: 1px; border-color: #FFFFFF #8492A5 #8492A5 #FFFFFF; padding-bottom: 2px; }
.ddmText			{ FONT-SIZE: 8pt; text-decoration: none; vertical-align: middle; padding: 4px; font-weight: normal; }
.ddmTextHasChild		{ FONT-SIZE: 8pt; text-decoration: none; vertical-align: middle; font-weight: normal; }
.ddmTextSeperator		{ FONT-SIZE: 8pt; text-decoration: none; }
.ddmSeperator			{ background-color: #8492A5; }
.ddmSeperatorEmpty		{ background-color: #FFFFFF; }
.ddmTextOver			{ background-color: #4D4D4D; color: #FFFFFF; FONT-SIZE: 8pt; text-decoration: none; vertical-align: middle; font-weight: normal; }
.ddmAnchor: hover		{ color: #FFFFFF; }
.ddmDiv				{ position: absolute; background: #000000; border-style: solid; border-width: 1px; border-color: #44546B; padding: 0; cursor: default; }

/* drop down used in multibuttons */
.ddmDivButton			{ position: absolute; background: #fef8e2; border-style: solid; border-width: 1px; border-color: #44546B; padding: 0; cursor: default; }

/* drop down used in multibuttons */
.ddmDivButtonY			{ background: url(/images/chiles/btn_page_hilited_ms.png) left bottom; position: absolute; padding: 0 0 4px; cursor: default; }

/* drop down used in multibuttons */
.ddmDivButtonG			{ background: url(/images/chiles/btn_page_ms.png) left bottom; position: absolute; padding: 0; cursor: default; }
.ddmDivButtonY .menuInnerTable	{ background: #fff9e5 none; border: 1px solid #D3AD68; position: relative; }
.ddmDivButtonG .menuInnerTable	{ background: #f9f9f9 none; border: 1px solid #999999; position: relative; }
.ddmDivButtonY .ddmTextOver	{ background-color: #ddb472; padding: 1px 4px; }
.ddmDivButtonG .ddmTextOver	{ background-color: #b9b9b9; padding: 1px 4px; }
.ddmDivButtonY .ddmText		{ padding: 1px 4px; }
.ddmDivButtonG .ddmText		{ padding: 1px 4px; }

/* drop down used in secondary multibuttons */
.ddmDivButtonSec		{ position: absolute; background: #F1F1F1; border-style: solid; border-width: 1px; border-color: #44546B; padding: 0; cursor: default; }

/* empty - but used as a way to identify elements in the body of the page */
.ddmSpan			{ }
.ddmArrow			{ font-family: WebDings; position: absolute; color: #8492A5; vertical-align: middle; right: 3px; FONT-SIZE: 8pt; }
.ddmArrowOver			{ font-family: WebDings; background-color: #FFFFFF; position: absolute; vertical-align: middle; FONT-SIZE: 8pt; }

/* nl dropdown */
.dropdownInput			{ height: 25px; color: black; font-size: 8pt; padding-left: 3px; border-width: 1px; border-style: solid; border-color: #D5DEE6 #FFFFFF #D5DEE6 #D5DEE6; cursor: default; vertical-align: middle; }
.effectHover .dropdownInput	{ border-color: #FFFFFF #FFFFFF #D5DEE6 #FFFFFF; }
.effectDisabled .dropdownInput	{ background-color: #F5F5F5; color: #808080; }

/* input widgets */
.dropdownDiv			{
	position: absolute; background: #EFEFEF; border: 1px solid #999999; cursor: default; padding: 5px 1px;
	scrollbar-face-color: #dddddd; scrollbar-highlight-color: #dddddd; scrollbar-3dlight-color: #dddddd;
	scrollbar-darkshadow-color: #dddddd; scrollbar-shadow-color: #999999;
	scrollbar-arrow-color: black; scrollbar-track-color: #eeeeee;
	font-size: 8pt; white-space: nowrap;
}

.dropdownNotSelected		{ FONT-SIZE: 8pt; color: black; padding: 3px 4px; padding-right: 6px; }
.dropdownSelected		{ FONT-SIZE: 8pt; color: white; background-color: #777777; padding: 3px 4px; padding-right: 6px; }

/* override user agent style sheet for popup select menus */
.dropdownNotSelected a		{ color: black; }

/* override user agent style sheet for popup select menus */
.dropdownSelected a		{ color: white; }

/* override user agent style sheet for popup selects */
#inner_popup_div a		{ color: black; }

/* for X-list */
/* 'xx' prepend for classes is for extreme lists. This is used for locating, as well as for changing the cursor style */
.portlettextinvisible		{ font-size: 8pt ; color: #000000; border-style: solid; border-width: 1px 1px 1px 1px; border-color: white white white white; visibility: hidden; }
.portlettextctr,
.xxportlettextctr		{ font-size: 8pt ; color: #000000; border-style: solid; border-width: 1px 1px 1px 1px; padding: 0 0 1px 0; border-color: white white white white; text-align: center; }
.portlettextrt,
.xxportlettextrt		{ font-size: 8pt ; color: #000000; border-style: solid; border-width: 1px 1px 1px 1px; padding: 0 0 1px 0; border-color: white white white white; text-align: right; }
.portlettext,
.xxportlettext			{ font-size: 8pt ; color: #000000; border-style: solid; border-width: 1px 1px 1px 1px; padding: 0 0 1px 0; border-color: white white white white; }
.xxportlettextctr		{ cursor: pointer; }
.xxportlettext			{ cursor: pointer; }

.portlettexthlctr,
.xxportlettexthlctr		{ font-size: 8pt ; color: #000000; border-style: solid; border-width: 1px 1px 1px 1px; padding: 0 0 1px 0; background-color: #EEEEEE; border-color: white white white white; text-align: center; }
.portlettexthlrt,
.xxportlettexthlrt		{ font-size: 8pt ; color: #000000; border-style: solid; border-width: 1px 1px 1px 1px; padding: 0 0 1px 0; background-color: #EEEEEE; border-color: white white white white; text-align: right; }
.portlettexthl,
.xxportlettexthl		{ font-size: 8pt ; color: #000000; border-style: solid; border-width: 1px 1px 1px 1px; padding: 0 0 1px 0; background-color: #EEEEEE; border-color: white white white white; }
.xxportlettexthlctr		{ cursor: pointer; }
.xxportlettexthl		{ cursor: pointer; }

/* X-List editing styles */
.listEditSpan			{ FONT-SIZE: 8pt; position: relative; z-index: 0; }
.listEditDiv			{ FONT-SIZE: 8pt; position: absolute; }
.quickaddcontainer		{ display: none; }
.quickadddiv			{ border-style: solid; border-color: #000000; border-width: 1px 0 0 0; background-color: #EEEEEE; }
.quickadddivportlet		{ border-style: solid; border-color: white #EEEEEE #EEEEEE white; border-width: 1px; background-color: #EEEEEE; }
.quickaddportletheader		{ border-style: solid; border-color: #FFFFFF #999999 #999999 #FFFFFF; border-width: 1px; background-color: #FFFFFF; }
.quickaddrow			{ font-size: 8pt; white-space: nowrap; }
.quickaddrowheader		{ font-size: 8pt; white-space: nowrap; font-weight: bold; }

.popupouter			{ font-size: 8pt; border: 0; position: absolute; padding: 0; }
.popupsegment			{ font-size: 8pt; width: 100%; background-color: #D5D5D5; border: 0; }
.popupheadernosort		{ font-size: 8pt; height: 100%; padding: 2px; vertical-align: top; color: #666666; background-color: #EFEFEF; }

.batchxxportlettextbot		{ background-color: white; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: white gray gray gray; }
.batchxxportlettextmid		{ background-color: white; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: white gray white gray; }
.batchxxportlettexttop		{ background-color: white; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: gray gray white gray; }
.batchxxportlettextuni		{ background-color: white; color: gray; border-style: dashed; border-width: 1px 1px 1px 1px; border-color: gray gray gray gray; }

/* reporting styles */
.bg			{ background-color: #FFFFFF; }
.num			{ font-size: 8pt; background-color: #FFFFFF; text-align: right; }
.numb			{ font-size: 8pt; background-color: #FFFFFF; text-align: right ; font-weight: bold; }
.stringb		{ font-size: 8pt; background-color: #FFFFFF ; font-weight: bold; }
.stringbctr		{ font-size: 8pt; background-color: #FFFFFF ; font-weight: bold ; text-align: center; }
.company		{ font-size: 14px; font-weight: bold; text-align: center; }
.rpttitle		{ font-size: 18px; font-weight: bold; text-align: center; }

.rptsubtitle		{ font-size: 14pt; font-weight: bold; text-align: center; }

.regmdtext		{ background-color: #000000; font-size: 8pt; color: #000000 ; padding: 2px; border-style: solid; border-width: 1px 0px 0px 0px; border-color: #8491A4 #000000 #000000 #000000; }
.reglttext		{ background-color: #EEEEEE; font-size: 8pt; color: #000000; padding: 2px; }

.regheaderline1		{ background-color: #cccccc; font-weight: bold; font-family: Open Sans,Helvetica,sans-serif; font-size: 11px; padding: 3px ; border-style: solid; border-width: 1px 0px 0px 1px; border-color: white; }
.regheaderline2		{ background-color: white; font-weight: bold; font-family: Open Sans,Helvetica,sans-serif; font-size: 11px; padding: 3px ; border-style: solid; border-width: 1px 0px 0px 1px; border-color: #cccccc; }

.rptcollapser		{ z-index: 500; border-width: 0 0 0 0; margin: 0 4px 0 0; padding: 0 0 0 0; background-color: transparent; vertical-align: baseline; }
.rpthdline1		{ text-align: center; text-transform: uppercase; font-size: 11px; padding: 0 ; border-style: solid; border-width: 0 0 0 0; border-color: white; }
.rpthdline1end		{ text-align: center; font-weight: bold; font-size: 11px; padding: 0 ; border-style: solid; border-width: 0 0 0 0; border-color: white #cccccc white white; }

.rpthdline2		{  text-align: center; font-weight: bold; font-size: 10px; padding: 0 ; border-style: none; }
.rpthdline2end		{ text-align: center; font-weight: bold; font-size: 10px; padding: 0 ; border-style: solid; border-width: 0 0 0 0; border-color: #cccccc; }

.rpthdline3		{ text-align: center; font-weight: normal; font-size: 9px; padding: 0; border-style: none; }
.rpthdline3end		{ text-align: center; font-weight: normal; font-size: 9px; padding: 0; border-style: solid; border-width: 0 0 0 0; border-color: #cccccc; }

.rptheader		{ padding: 0; margin: 0; border-style: none; }

.rptdata		{ vertical-align: top; font-weight: normal; font-size: 8pt; padding: 2px; border-style: none; min-height: 17px;}
.rptdataend		{ vertical-align: top; font-weight: normal; font-size: 8pt; padding: 2px; border-style: solid; border-width: 0px 1px 0px 0px; border-color: #cccccc; }
.rptsep			{ display: block; border-width: 0 0 0 0; border-top: 1px dotted #e0e0e0; height: 1px; overflow: hidden; width: 100%; padding: 0 0 0 0; margin: 0 0 0 0; background: transparent; }

.rpttotal		{ vertical-align: top; font-weight: normal; font-size: 8pt; padding: 2px; border-style: none; }
.rpttotalend		{ vertical-align: top; font-weight: normal; font-size: 8pt; padding: 2px; border-style: solid; border-width: 0px 1px 0px 0px; border-color: #cccccc; }

.rptgriddata		{ vertical-align: top; font-weight: normal; font-family: Open Sans,Helvetica,sans-serif; font-size: 8pt; margin: 0; padding: 2px; border-style: solid; border-width: 1px 0px 0px 1px; border-color: #cccccc; }
.rptgriddataend		{ vertical-align: top; font-weight: normal; font-family: Open Sans,Helvetica,sans-serif; font-size: 8pt; margin: 0; padding: 2px; border-style: solid; border-width: 1px 1px 0px 1px; border-color: #cccccc; }

.rptgridtotal		{ vertical-align: top; font-weight: normal; font-family: Open Sans,Helvetica,sans-serif; font-size: 8pt; margin: 0; padding: 2px; border-style: solid; border-width: 1px 0px 0px 1px; border-color: #cccccc; }
.rptgridtotalend	{ vertical-align: top; font-weight: normal; font-family: Open Sans,Helvetica,sans-serif; font-size: 8pt; margin: 0; padding: 2px; border-style: solid; border-width: 1px 1px 0px 1px; border-color: #cccccc; }

.rptgridfinal		{ vertical-align: top; font-weight: normal; font-family: Open Sans,Helvetica,sans-serif; font-size: 8pt; margin: 0; padding: 2px; border-style: solid; border-width: 1px 0px 1px 1px; border-color: #cccccc; }
.rptgridfinalend	{ vertical-align: top; font-weight: normal; font-family: Open Sans,Helvetica,sans-serif; font-size: 8pt; margin: 0; padding: 2px; border-style: solid; border-width: 1px 1px 1px 1px; border-color: #cccccc; }

.rptcolheadercliparea	{ border-bottom: none; border-top: 1px solid #000000; background-color: #EEEEEE; }
.rptcolheaderdiv	{ visibility: hidden; overflow: hidden; position: relative; background-color: #000000; color: #262626;}
.rptcolheader		{ position: relative; table-layout: fixed; }
.rptcolheaderrelative	{ position: relative; overflow: visible; bottom: 0px; border-width: 0 0 0 0; height: 0px; width: 1px; }
.rptcolheaderfader	{ position: absolute; overflow: visible; top: 0px; border-width: 0 0 0 0; height: 8px; filter: progid: DXImageTransform.Microsoft.AlphaImageLoader(src='/images/icons/reporting/grade.png', sizingMethod='scale'); }
.rptcolumnheaderlabel	{ padding: 4px; }
.rptcolumnheaderhandle	{width: 7px; background-image: url(/images/reporting/handlebg.gif); background-size: 7px 70%; background-position: center; background-repeat: no-repeat; cursor: e-resize; position: absolute; overflow: hidden;}
.rptbreakoutbox		{ position: absolute; margin: 0 0 0 0; width: auto; padding: 10px; zIndex: 100000; left: 0px; bottom: 0px; white-space: nowrap; opacity: 0.85; background-color: gray; color: white}

.rptcontentviewport	{ border: 0px solid blue; height: 500px; width: 100%; overflow-x: auto; overflow-y: hidden; }
.rptdataarea		{ empty-cells: show; }
.rptdataarea TD		{ overflow: hidden; }
.rptdataarea tr[visibility="hidden"] * { font-size:0 !important; padding-top:0; padding-bottom:0;}
.rptdataareawaiting	{ background-color: #EEEEEE; }
.rptscrollarea		{ position: relative; border: 0px solid red; visibility: hidden; top: 0px; right: 0px; overflow: auto; overflow-x: hidden;width: 18px;}
.rptclipregion		{ position: relative; overflow: hidden; }
.rptannotationbar	{ position: relative; visibility: hidden; width: 20px; top: 0px; left: 0px; padding: 0; margin: 0 0 0 0; border-left: 1px solid #999999; border-right: 1px solid #cccccc; background-color: #EEEEEE; overflow: hidden; }
.rptannotationmarker	{ position: absolute; width: 100%; height: 12px; left: 2px; border-width: 0px; padding: 0px; cursor: hand; cursor: pointer; overflow: visible; white-space: nowrap; }

.rptfooter			{ width: auto; position: absolute; bottom: 0; border-width: 0px; padding-top: 0px; margin: 0 0 0 0; background-color: #EEEEEE; }
.rptfooterfilter		{ background-color: #000000; width: 100%; border-top: 1px solid #000000; }
.rptfooterfilter .smalltextnolink, .rptfooterfilter .rpticonbtn  {font-size: 12px !important; font-weight: normal; color: #777777; text-transform: uppercase;}
.rptfilterow			{ padding: 2px; display: none; }
.rptfooterfilter { border-bottom: 1px solid #cccccc;}
.rptfooterbuttons		{ width: 100%; }
.rptfooterbuttonsleft		{ text-align: left; padding: 3px; }
.rptfooterbuttonsright		{ text-align: right; /* position: absolute; */ right: 5px; }

.rptfooter_ioptions {background-image: url(/uirefresh/img/field/popout.png); height: 22px; width: 22px; display: inline-block; vertical-align: middle; background-repeat: no-repeat; background-position-x: -3px;}

.rptbuttonrow			{ padding: 2px; }
.rpticonbtn			{ font-decoration: none; vertical-align: middle; cursor: pointer; }
.rpticonbtn img			{ border: 0px; }

.rptoptiondialog	{ border: 1px solid black; position: absolute; z-index: 9999; }
.rptoptiondialoginner	{ border-top: 1px solid #ffffff; border-left: 1px solid #ffffff; border-bottom: 1px solid #c6c3c6; border-right: 1px solid #c6c3c6; background-color: #EEEEEE; }
.rptfindbox		{ text-align: right; vertical-align: bottom; background: #000000;}
.rptfindboxrow		{ background-color: #000000; vertical-align: middle; }
.rptfindboxrowimage		{ background-color: #000000; vertical-align: middle; }
.rptfindboxlabel { padding-right: 3px; font-size: 12px; font-weight: normal !important; color: #777777 !important; text-transform: uppercase; text-decoration: none;}

/* "font-size: 3px" works around an IE bug -- it doesn't display the background image if the height of the DIV is less than the font-size (or close to it?) */
.rpthierarchyline	{ font-size: 3px; position: absolute; overflow: hidden; border-width: 0px; border-width: 0px 0px 0px 1px; border-left: 1px solid #000000; margin: 0px; padding: 0px; background-image: url(/images/icons/reporting/graydot.gif); background-repeat: repeat-x; background-position: bottom left; z-index: 0; }

/* report builder preview styles */
.rptpreviewrawtext	{ font-size: 8pt; color: #666666; }
.rptpreviewheader,
.rptpreviewheaderrt	{ font-size: 8pt; color: #666666; cursor: pointer; background-color: #EFEFEF; border-bottom: 1px solid #D4D4D4; border-top: 1px solid #FFFFFF; vertical-align: top; padding-top: 1px; padding-bottom: 1px; }
.rptpreviewheaderrt	{ align: right; }
.rptpreviewtextblack,
.rptpreviewtext,
.rptpreviewtextrt,
.rptpreviewtextgray	{ font-size: 8pt; }
.rptpreviewtext,
.rptpreviewtextrt	{ color: #666666; white-space: nowrap; padding-top: 3px; padding-bottom: 2px; }
.rptpreviewtextrt	{ align: right; }
.rptpreviewtextgray	{ color: #666666; }
.rptpreviewedit		{ background-color: #FFFFFF; border-bottom: 3px solid #999999; }
.rptprevieweditdetail	{ background-color: #FFFFFF; border-right: 3px solid #999999; }
.rptrowhighlight {background-color: #000000;}

/* this only applies to Mozilla. I had to apply the IE opacity using javascript to avoid the "Active content" dialog warning */
.rptpreviewopacity	{ -moz-opacity: 0.2; background-color: #55FD00; }

.navtitle		{ background-color: #000000; font-weight: bold; font-family: Open Sans,Helvetica,sans-serif; font-size: 11px; padding: 3px ; border-style: solid; border-width: 1px 0px 1px 1px; border-color: #ffffff #9c9a97 #9c9a97 #ffffff; }
.navend			{ background-color: #000000; font-weight: bold; font-family: Open Sans,Helvetica,sans-serif; padding: 3px ; border-style: solid; border-style: solid; border-width: 1px 0px 1px 0px; border-color: #ffffff #9c9a97 #9c9a97 #ffffff; }
.navdiv			{ display: none; width: 200px; margin: 0 0 0 0; border-width: 0 0 0 0; border-right: 1px solid #9c9a97; vertical-align: top; }
.navsep			{ color: #CCCCCC; }

.rndbuttoninpt			{ font-size: 8pt; padding: 0; margin: 0; color: #222222; background-color: transparent; border: 0; cursor: pointer; }
.rndbuttoninptsmall		{ width: 100%; font-size: 7pt; padding: 0; color: #565656; background-color: transparent; border: 0; cursor: pointer; }
.rndbuttoninptsmallnormal	{ width: 100%; font-size: 7pt; padding: 0; color: #565656; background-color: transparent; border: 0; cursor: pointer; }
.rndbuttoninptdis		{ width: 100%; font-size: 8pt; padding: 0; color: #999999; background-color: transparent; border: 0; }
.rndbuttoninptsmalldis		{ width: 100%; font-size: 7pt; padding: 0; color: #999999; background-color: transparent; border: 0; }
.rndbuttoncaps			{ background-repeat: no-repeat; background-position: 100%; }
.rndbuttonbody			{ vertical-align: middle; background-repeat: repeat-x; background-position: 100%; }

/* styles for drag and drop in ordered list machines */
.movable		{ cursor: move; }
.movable input		{ cursor: default; }

/* Styles used in Web store checkout */
.checkoutwarning	{ font-family: Open Sans,Helvetica,sans-serif; font-size: 8pt ; font-weight: bold; color: red; border-style: none; vertical-align: top; }
.checkoutprogresstext	{ font-family: Open Sans,Helvetica,sans-serif; font-size: 8pt ; font-weight: normal; color: #000000; border-style: none; }
.checkoutthankstext	{ font-family: Open Sans,Helvetica,sans-serif; font-size: 24pt ; font-weight: bold; color: #000000; border-style: none; }

/* Styles used in webstore cart */
.cartstrikeoutamount	{ text-decoration: line-through; }

/* styles used by entries in the webstore extended shopping cart */
.extcart		{ padding: 2px 5px; font-size: 8pt; }
.extcartborder		{ padding: 2px 5px; border-top: 1px solid #000000; font-size: 8pt; }
.extcartbordershaded	{ padding: 2px 5px; border-top: 1px solid #000000; font-size: 8pt ; background-color: #EEEEEE; }
.extcarttotal		{ padding: 2px 5px; border-top: 1px solid #000000; font-size: 10pt; }
.extcarttotalshaded	{ padding: 2px 5px; border-top: 1px solid #000000; font-size: 10pt ; background-color: #EEEEEE; }

	/* NL InputStyles */

	/* These must be omitted for Netscape 4.x because they break Web store pages pretty thoroughly. */
	textarea		{ overflow: auto; border-style: solid; border-width: 1px; border-color: #999999; resize: both; }
	input			{ border-style: solid; border-width: 1px; border-color: #999999; }
	.inputrt		{ border-style: solid; border-width: 1px; border-color: #999999; }
	.inputreq		{ border-style: solid; border-width: 1px; border-color: #999999; }
	.inputrtreq		{ border-style: solid; border-width: 1px; border-color: #999999; }
	.bgbutton		{ border-style: solid; border-width: 1px; border-color: #999999; }
	.nlbutton		{ border-style: solid; border-width: 1px; border-color: #999999; }



/* NL WebKit Only */

.table_fields .checkbox_unck,
.table_fields .checkbox_ck,
.table_fields .checkbox_read_unck,
.table_fields .checkbox_read_ck,
.table_fields .checkbox_disabled_unck,
.table_fields .checkbox_disabled_ck	{ top: 0px; }

.ddarrowSpan			{ top: -2px; }
.fwpopupsel_pos			{ top: 0px; }
.crosslinktext .menu_tri,
.crosslinktext .menu_tri_silver	{ vertical-align: top; top: 2px; }
.rndbuttoninpt			{ height: 19px; display: block; width: 100%; }

.pgBntY,
.pgBntG,
.pgBntY .bntLT,
.pgBntY .bntLB,
.pgBntY .bntRT,
.pgBntY .bntRB			{ cursor: auto; }

.pgBntY .bntBgT,
.pgBntY .bntBgB,
.pgBntG .bntBgT,
.pgBntG .bntBgB			{ cursor: hand; }


/* Header */
#ns_navigation {
    background: none repeat scroll 0 0 #607799 !important;
}

#ns-header-menu-userrole .ns-menu .ns-menuitem.ns-active > a,
#ns-header-quick-menu .ns-menu .ns-menuitem.ns-active > a,
#ns_navigation .ns-menu .ns-menuitem.ns-active > a {
    color: #24385B !important;
}
#ns-header-menu-userrole .ns-menu .ns-menuitem > a,
#ns-header-quick-menu .ns-menu .ns-menuitem > a,
#ns_navigation .ns-menu .ns-menuitem > a {
    color: #607799 !important;
}

#ns_navigation > .ns-menu > .ns-menuitem.ns-menuitem-selected {
    background-color: #24385B;
}

#ns_navigation > .ns-menu > .ns-menuitem.ns-menuitem-selected > a {
    color: white !important;
}

#ns_navigation > .ns-menu > .ns-menuitem.ns-menuitem-selected.ns-active > a {
    color: #24385B !important;
}

#ns_navigation > .ns-menu > .ns-menuitem.ns-active {
    background-color: #E0E6EF;
}
#ns_navigation > .ns-menu > .ns-menuitem a.ns-menu-icon svg {
    fill: #fff !important;
}
#ns_navigation > .ns-menu > .ns-menuitem.ns-active a.ns-menu-icon svg {
    fill: #24385B !important;
}

#ns_navigation .ns-menuitem.ns-header > a {
    color: white !important;
}

#ns_navigation > .ns-menu > .ns-menuitem.ns-active > .ns-menu > .ns-menuitem.ns-active {
    background-color: #24385B !important;
}

#ns_navigation .ns-submenu-triangle {
	fill: #607799;
}

.ns-menu .ns-menuitem.ns-active > .ns-submenu-triangle {
	fill: #24385B !important;
}

.ns-menu .ns-scroll-button {
	fill: #607799 !important;
}

.ns-menu .ns-scroll-button:hover {
	fill: #24385B !important;
}

.ns-role-menuitem {
    position: relative;
}

.ns-role-menuitem-text {
    margin-right: 50px;
}

.ns-role-accounttype {
    position: absolute;
    right: 11px;
    border: 1px solid #607799;
    box-shadow: 0px 0px 1px #607799;
    border-radius: 10px;
    height: 50%;
    top: 10px;
    line-height: 17px;
    width: 30px;
    text-align: center;
}

.ns-menuseparator a,
.ns-menusimpleseparator a,
.ns-user .ns-menu .uir-menuitem-viewall,
.ns-user .ns-menu .uir-menuitem-signout {
    border-bottom: 1px solid rgba(36,56,91,0.15);
}

.ns-user .ns-menu .uir-menuitem-switchaccount {
	border-top: 1px solid rgba(36,56,91,0.15);
}

#ns-header-quick-menu > .ns-active, #ns-header-quick-menu > .ns-active > a,
#ns_header .ns-help:hover, #ns_header .ns-help:focus,
#ns_header .ns-user > .ns-active, #ns_header .ns-user > .ns-active > a
{
    background-color: #E0E6EF !important;
}

/* Global */
.ns-menuitem.ns-active > a {
    background-color: #E0E6EF !important;
}

.ns-scroll-button:hover {
    background: #E0E6EF !important;
}

/* Dashboard */
@media screen {

	/* Employee center */
	.ns-ec-plugin label input:checked + span {
		background-color: #24385B !important;
	}

    .ns-portlet-wrapper[data-portlet-type='admindocs'] .ns-heading {
        background-color: #E0E6EF;
        color: #24385B;
    }

	.ns-portlet-action-submenu.ns-portlet-action-selected,
	.ns-portlet-action.ns-portlet-action-selected {
		background-color: #E0E6EF !important;
		color: #fff;
	}
    .ns-portlet-action-submenu:hover > ul > li:hover {
        background: #E0E6EF;
    }

    .ns-portlet-action-submenu > ul > li {
        color: #607799;
    }

    .ns-calendar-table .ns-today {
        color: white;
        background-color: #607799 !important;
    }

    .ns-calendar-table .ns-calendar-cell-innerdiv.ns-selected-day {
        border-color: #607799 !important;
    }

	.ns-timeline-table-month-row td {
		background-color: #E0E6EF;
	}

	/* ******************* */
	/* personalize section */
	/* ******************* */
	.ns-dashboard-content-manager h2 {
		background: #607799 !important;
	}

	.ns-dashboard-content-manager .ns-personalize-loading .ns-portlet-subelement-loader {
		background: #E0E6EF;
	}
	.ns-dashboard-content-manager .ns-portlet-loader__icon--circular .ns-portlet-loader__icon--circular__outline {
		stroke: rgba(96,119,153,0.70) !important;
	}
	.ns-dashboard-content-manager .ns-portlet-loader__icon--circular .ns-portlet-loader__icon--circular__runner {
		stroke: #24385B !important;
	}

	.ns-dashboard-content-manager .ns-content-selection {
		background: #E0E6EF !important;
	}

	.ns-dashboard-content-manager .ns-group-selection ul li {
		border-color: #607799 !important;
	}

	.ns-dashboard-content-manager .ns-group-selection ul li.ns-active-group {
		background: #E0E6EF !important;
		border-color: #24385B !important;	/* need to override border color defined in this file */
		color: #24385B !important;
	}

	.ns-dashboard-content-manager .ns-group-selection ul li:hover,
	.ns-dashboard-content-manager .ns-group-selection ul li:focus {
		color: #607799;
	}

	div.ns-content-manager-item {
		background: #E0E6EF;
		border-color: #E0E6EF;
	}

	.ns-portlet-wrapper.ns-highlight {
		box-shadow: 0 0 20px 3px #607799 !important;
	}

	.ns-dashboard-content-manager div.ns-content-manager-item:hover,
	.ns-dashboard-content-manager div.ns-content-manager-item:focus,
	.ns-dashboard-content-manager div.ns-content-manager-item.ns-content-manager-item-active {
		box-shadow: 0 0 8px 0 rgba(36,56,91,0.50);
	}

	div.ns-content-manager-item.ns-locked-cannot-be-removed .ns-item-overlay-lock svg,
	div[data-portlet-type] .ns-portlet-thumbnail .svgicon {
		fill: #24385B;
		stroke: #24385B;
	}

	.ns-dashboard-content-manager .ns-content-manager-item .ns-item-title,
	div.ns-content-manager-item .ns-badge-container,
	div.ns-content-manager-item.ns-close-supported .ns-item-overlay-remove span {
		color: #24385B !important;
	}

	.ns-dashboard-content-manager .ns-portlet-selection .ns-remove-portlet-drop-target {
		background: rgba(96,119,153,0.95);
	}
	body .ui-tooltip.ns-content-manager-tooltip-wrapper {
		background: #24385B;
	}

	.ns-dashboard-content-manager .ns-portlet-selection button.ns-prev-portlet-selection-page svg,
	.ns-dashboard-content-manager .ns-portlet-selection button.ns-next-portlet-selection-page svg {
		fill: #607799;
	}
	.ns-dashboard-content-manager .ns-portlet-selection button.ns-prev-portlet-selection-page:active svg,
	.ns-dashboard-content-manager .ns-portlet-selection button.ns-next-portlet-selection-page:active svg {
		fill: #24385B;
	}

	.ns-content-no-apps-message a,
	.ns-content-no-apps,
	.ns-content-apps-counter,
	.ns-content-apps-link {
		color: #24385B;
	}

}

/* Forms */
td.fgroup_title {
    background: none repeat scroll 0 0 #E0E6EF !important;
}

div.fgroup_title {
    color: #607799 !important;
}

.bgtabbar, .bgon, .tabur,
div.unrollformtabheaderexpand, div.unrollformtabheadercollapse, div.unrollformtabheaderesep {
    color: #24385B !important;
    background: none repeat scroll 0 0 #607799 !important;
}

div.bgsubtabbar, div.unrollformsubtabheaderexpand, div.unrollformsubtabheadercollapse {
    color: #607799 !important;
    background: none repeat scroll 0 0 #E0E6EF !important;
}

.formsubtabtexton, .formsubtabtextoff {
    color: #24385B !important;
}

}

div.unrollformsubtabheaderexpand a, div.unrollformsubtabheadercollapse a
{
    color: #607799 !important;
}

table.totallingtable caption {
    background-color: #607799;
}

table.totallingtable tr td.labelheading
{
	background-color: #607799;
}

/* Popups */
td.uir-popup-select-title-bar,
td.uir-popup-select-title-bar + td.textrt{
    background: #607799 !important;
}

#popup_outerdiv div.uir-filterArea {
    background: #E0E6EF !important;
}

.uir-shuttle-panes-tr #inner_popup_div > table > tbody > tr:hover > td:not(.popupheadernosort),
.uir-shuttle-panes-tr #inner_popup_div > table > tbody > tr:hover > td:not(.popupheadernosort) *
{
    background: #607799 !important;
    color: #FFFFFF;
}

.x-window-tl {
    background-color: #607799 !important;
}

.uir-popup-header {
    background: #607799;
}

/* Lists */
.uir_list_top_button_bar,
.uir-list-control-bar{
    background-color: #E0E6EF !important;
}

/* Unroll/roll tabs */
.uir-unroll-tabs-button a, .uir-tab-layout-button
{
    background-color: #24385B !important;
}

/* Arrangements */
table.uir-field-arrangement > tbody > tr:first-of-type > td
{
    background-color: #E0E6EF !important;
    color: #607799 !important;
}

/* Field-level help */
#body_div * {
    font-size: 14px !important;
}

/* Tab container */
.uir-tab-container,
.uir-tab-container .uir-tab-panel {
	background-color: #607799;
}


    @media screen {     /* NL MediaScreen */
    body,
    td,
    select,
    textarea,
    input,
	button { font-family: Open Sans,Helvetica,sans-serif; }
    .noprint		{ }
    .noprintvisibility	{ }
    .printonly		{ display: none; }
    .scrollarea		{ overflow: auto; }
 }
    @media print {     /* NL MediaPrint */
    body,
    td,
    textarea,
    select,
    input,
	button { font-family: Arial,Helvetica,sans-serif; }
    .noprint		{ display: none; }
    .noprintvisibility	{ visibility: hidden; }
    .printonly		{ }
    .scrollarea		{ }
    
    .navdiv			{ display: none; border-width: 0 0 0 0; }
 }
