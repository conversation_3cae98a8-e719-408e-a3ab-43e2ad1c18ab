/**
 * @NApiVersion 2.x
 * @NScriptType scheduledscript
 * @ModuleScope SameAccount
 * <AUTHOR> 8/1/2019
 * 
 * This scheduled script uses the saved search "Shopify Orders Pending Fulfillment"
 * and creates the item fulfillment record. 
 * 
 * Added script restart capabilities if there is an error whilst the script is running
 * due the inability to fulfill the Sales Order
 * 
 */

define(['N/email', 'N/record', 'N/log', 'N/search', 'N/runtime', 'N/task'], function (email, record, log, search, runtime, task) {

    function create_item_fulfillments() {

        log.debug({
            title: 'Start Script'
        });
        // send email to track system is running
        // 

        var script = runtime.getCurrentScript();
        var orderId, tranId = '';

        /**   For Debug purposes
         *   email.send({
         *     author: 2760804,
         *     recipients: '<EMAIL>',
         *     subject: 'Fulfillment script is running',
         *     body: 'This is test email' 
         * 
         * 
         * 
         });
        */

        try {

            var orderSearch = search.load({
                id: 'customsearch_nau_shopify_pending_fulfill'
            });

            var recordCount = orderSearch.runPaged().count;

            if (recordCount == 0) {

                log.audit({
                    title: 'Process Order Fulfillment',
                    details: 'No Orders To Process'
                });

                return true;
            }

            /** 
             * For debug purposes
             * 
             * email.send({
             *             author: 2760804,
             *             recipients: '<EMAIL>',
             *             subject: 'Fulfillment script is running',
             *             body: recordCount
             });
                */

            log.debug({
                title: 'Process Order Fulfillment',
                details: recordCount
            });

            orderSearch.run().each(function (result) {

                orderId = result.getValue({
                    name: 'internalid'
                });
                tranId = result.getValue({
                    name: 'tranid'
                });

                log.debug({
                    title: 'Process Order',
                    details: orderId
                });

                var fulfillment = record.transform({
                    fromType: record.Type.SALES_ORDER,
                    fromId: orderId,
                    toType: record.Type.ITEM_FULFILLMENT,
                });

                fulfillment.setValue({
                    fieldId: 'shipstatus',
                    value: 'A'
                });

                var fulfillmentLineCount = fulfillment.getLineCount({
                    sublistId: 'item'
                });

                for (var i = 0; i < fulfillmentLineCount; i++) {

                    fulfillment.setSublistValue({
                        sublistId: 'item',
                        line: i,
                        fieldId: 'itemIsFulfilled',
                        value: true
                    });
                }

                var itemFulfillmentId = fulfillment.save({
                    enableSourcing: true,
                    ignoreMandatoryFields: false
                });

                log.audit({
                    title: 'Fulfillment Created :',
                    details: 'Fulfillment Id ' + itemFulfillmentId + ': Created From Order Id ' + orderId
                });


                record.submitFields({
                    type: record.Type.SALES_ORDER,
                    id: orderId,
                    values: {
                        shipcomplete: true
                    }
                });

                return true;
            });

        } catch (err) {

            log.error({
                title: 'Error creating fulfillment ' + err.name,
                details: err.message
            });

            if (err.name == 'VALID_LINE_ITEM_REQD') {



                record.submitFields({
                    type: record.Type.SALES_ORDER,
                    id: orderId,
                    values: {
                        'custbody_nau_error_do_not_fulfill': 'T'
                    }
                });

                email.send({
                    author: 2122456,
                    recipients: ['<EMAIL>'],
                    subject: 'Order ID ' + tranId,
                    body: 'Please investigate unable to filfill order: ' + tranId
                });

                log.error({
                    title: 'Error Processing Fulfillment',
                    details: 'Tran Id' + tranId + 'Details Id' + orderId + 'Script Restarted ' + script
                })
                var script = rescheduleCurrentScript();

                return false;

            }

            // Email Admins Sean and Jayesh if there is an issue that is not caught

            var stEmailSubj = 'ERROR  IN NETSUITE : ' + err.name;
            var stEmailBody = 'The Scheduled Script in NetSuite (create_item_fulfillment_shopify_sch.js) ' +
                'that takes Shopify Sales Orders and fulfills them has encountered the following error ' +
                err.message + ' and may have stopped running. Transaction Number:' + tranId + ' and internalID :' + orderId;


            email.send({

                author: 2122456,
                recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                subject: stEmailSubj,
                body: stEmailBody,
            });

            return false;

        }

        log.debug({
            title: 'End Script'
        });

        return true;
    }
    return {
        execute: create_item_fulfillments
    };


    function rescheduleCurrentScript() {
        var scheduledScriptTask = task.create({
            taskType: task.TaskType.SCHEDULED_SCRIPT
        });
        scheduledScriptTask.scriptId = runtime.getCurrentScript().id;
        scheduledScriptTask.deploymentId = runtime.getCurrentScript().deploymentId;
        var scriptId = scheduledScriptTask.submit();
        return scriptId;
    };

});