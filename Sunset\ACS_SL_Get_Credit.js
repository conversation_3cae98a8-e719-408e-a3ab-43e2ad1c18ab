/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
 define(['N/query', 'N/search'], function(query, search) {

    function onRequest(context) {

        var paymentId = context.request.parameters.payment_id;

        try {
            var sqlQueryData = "SELECT pymt.custrecord_2663_applied_credits, pymt.custrecord_2663_fm_trans_hash_string  FROM CUSTOMRECORD_2663_FILE_ADMIN pymt WHERE pymt.custrecord_2663_fm_trans_hash_string LIKE ? ";
            var resultSet = query.runSuiteQL({
                query: sqlQueryData,
                params: ['%'+paymentId+'%']
            });
        
            var results = resultSet.results;
            var tableTagStart = '<table class="itemtable" style="height: 43px; width: 99.85%">';
            var tableHeader = '<thead><tr><th align="left">Description</th><th align="left">Applied to</th><th align="right"><span align="right">Credit Amount</span></th></tr></thead>';
            var tableBody = '';
            var tableTagEnd = '</table>';
            log.debug('test', results.length);
            for(var i = 0; i < results.length; i++ ){
                var creditIds = JSON.parse(results[i].values[0]);

                for(var j = 0; j < creditIds.length; j++) {

                    var sqlQueryData = "SELECT cred.nextdoc AS cred_id FROM previoustransactionlink cred WHERE cred.nextdoc = ? AND cred.previousdoc IN (SELECT bill.previousdoc AS bill_id FROM previoustransactionlink bill WHERE bill.nextdoc = ?)";
                    var creditResultSet = query.runSuiteQL({
                        query: sqlQueryData,
                        params: [creditIds[j], paymentId]
                    });

                
                    if(creditResultSet.results.length > 0) {
                        var searchObj = search.load({
                            id: 'customsearch_acs_applying_trans'
                        });

                        var applyingTransFilter = search.createFilter({
                            name: 'applyingtransaction',
                            operator: search.Operator.ANYOF,
                            values: creditIds[j]
                        });

                        searchObj.filters.push(applyingTransFilter);
                        
                        searchObj.run().each(function(result){
                            
                            var creditObj = {
                                type: result.getText({ name: 'type', join: 'applyingTransaction', summary: 'GROUP' }),
                                refno: result.getText({ name: 'transactionname', join: 'applyingTransaction', summary: 'GROUP' }),
                                tranno: result.getValue({ name: 'transactionnumber', join: 'applyingTransaction', summary: 'GROUP' }),
                                applying_trans_id: result.getValue({ name: 'internalid', summary: 'GROUP' }),
                                applying_trans_name: result.getValue({ name: 'transactionname', summary: 'GROUP' }),
                                amount: result.getValue({ name: 'amount', join: 'applyingTransaction', summary: 'SUM' })
                            } 

                            tableBody = tableBody + '<tr>';
                            tableBody = tableBody +'<td align="left">' + ' ' + ((creditObj.refno !== null) ? creditObj.refno : creditObj.type) + '</td>';
                            tableBody = tableBody + '<td align="left">' + creditObj.applying_trans_name + '</td>';
                            tableBody = tableBody + '<td align="right">' + creditObj.amount + '</td>';
                            tableBody = tableBody + '</tr>';

                            return true;
                        });
                    }
                }
            }

            var returnStr = '';
            if(tableBody) {
                var returnStr = "<br />" + tableTagStart + tableHeader + tableBody + tableTagEnd + "<br />"; 
            }
            
            context.response.write({
                output: returnStr
            });

        } catch (e) {
            log.debug('Error', e);
        }
    }

    return {
        onRequest: onRequest
    }
});
