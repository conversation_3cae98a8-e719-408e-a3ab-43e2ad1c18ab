/**
 * Credit Memo - Sync customer to the website 
 * 
 * Version    Date            Author           Remarks
 * 1.00       12 Oct 2017     Marcel P		   Initial Version
 *
 */

/**
 * @appliedtorecord creditmemo
 * 
 * @param {String} type Operation types: create, edit, delete, xedit,
 *                      
 * @returns {Void}
 */
function afterSubmit(type) {
  
	try {
		if (type == 'create' || type == 'edit') {

			if (nlapiLookupField('customer', nlapiGetFieldValue('entity'), 'custentity_in8_wc_customer_id')) {
				
				In8Lib.addToQueue('customer', 'customer', nlapiGetFieldValue('entity'), 'edit');	
			}	
		}			
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
	}
}
