/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/file', 'N/ui/serverWidget', 'N/search', 'N/format/i18n', 'N/currency', 'N/url', 'N/record'], function(file, serverWidget, search, formatCurr, currency, url, record) {

    function fieldChanged(newRec, oldRec){

        if(oldRec){
            var newValue = newRec.getValue({ fieldId: 'custbody_acs_inv_grid_templt_data' });
            var oldValue = oldRec.getValue({ fieldId: 'custbody_acs_inv_grid_templt_data' });
            var oldValueChanged = ((oldValue || newValue) && (newValue !== oldValue));

            var newCount = newRec.getLineCount({ sublistId: 'links' });
            var oldCount = oldRec.getLineCount({ sublistId: 'links' });
            var linkCountBool = (newCount != oldCount);
            
            return (oldValueChanged || linkCountBool);
        }

        // if new record, just return true
        return true;
    }
    function beforeLoad(context) {
        if(context.type == context.UserEventType.VIEW){
            var newRec = context.newRecord;
            var oldRec = context.oldRecord;
            
            var newValue = newRec.getValue({ fieldId: 'custbody_acs_inv_grid_templt_data' });
            var gridTemplate = newRec.getValue({ fieldId: 'custbody_gridoe_trantemplatedata' });
            
            if((fieldChanged(newRec, oldRec) || !newValue) && (gridTemplate != '{}')) {
                var newRec = context.newRecord;

                // work around for invoice
                var recObj = record.load({
                    id: newRec.id,
                    type: record.Type.INVOICE,
                    isDynamic: false
                }); 

                var recordLookUp = search.lookupFields({
                    type: search.Type.INVOICE,
                    id: newRec.id,
                    columns: [
                        'currency',
                        'total',
                        'trandate'
                    ]
                });
                
                var currLookUp = search.lookupFields({
                    type: search.Type.CURRENCY,
                    id: recordLookUp.currency[0].value,
                    columns: [
                        'symbol'
                    ]
                });
                
                var currFormatter = formatCurr.getCurrencyFormatter({
                    currency: currLookUp.symbol,
                });

                var recRate = currency.exchangeRate({
                    source: currFormatter.currency,
                    target: 'USD',
                    date: new Date(recordLookUp.trandate)
                });
        
                var recTotal = (parseFloat(recordLookUp.total) / recRate);
                recTotal = Math.floor(recTotal * 100) / 100;

                var parentCount = newRec.getLineCount({
                    sublistId: 'item'
                });

                var linksCount = newRec.getLineCount({
                    sublistId: 'links'
                });

                // URL resolving
                var scheme = 'https://';
                var host = url.resolveDomain({
                    hostType: url.HostType.APPLICATION
                });
                var firstURLPart = scheme + host;

                var pdfPOFields = [];
                var exist = [];
                for(var i = 0; i < parentCount; i++) {
                    var parentId = newRec.getSublistValue({
                        sublistId: 'item',
                        fieldId: 'custcol_gridoe_hidden_parentname',
                        line: i
                    });
                    if(exist.indexOf(parentId) === -1){
                        var fieldLookUp = search.lookupFields({
                            type: search.Type.INVENTORY_ITEM,
                            id: parentId,
                            columns: [
                                'itemid',
                                'vendorname',
                                'displayname',
                                'custitem_psgss_category',
                                'custitem_bo_theme',
                                'custitem_psgss_gender',
                                'custitem11',
                                'custitem_bhpc_prod_img',
                                'custitem_psgss_season',
                                'custitem_psgss_year'
                            ]
                        });

                        // create parent image URL
                        var parentImageURL = (fieldLookUp.custitem_bhpc_prod_img.length) ? firstURLPart + fieldLookUp.custitem_bhpc_prod_img[0].text : "";

                        var pdfFieldsObj = {
                            internalid: parentId,
                            itemid: fieldLookUp.itemid,
                            vendorname: fieldLookUp.vendorname,
                            displayname: fieldLookUp.displayname,
                            custitem_psgss_category: (fieldLookUp.custitem_psgss_category.length) ? fieldLookUp.custitem_psgss_category[0].text : "",
                            custitem_bo_theme: (fieldLookUp.custitem_bo_theme.length) ? fieldLookUp.custitem_bo_theme[0].text : "",
                            custitem_psgss_gender: (fieldLookUp.custitem_psgss_gender.length) ? fieldLookUp.custitem_psgss_gender[0].text : "",
                            custitem_psgss_season: (fieldLookUp.custitem_psgss_season.length) ? fieldLookUp.custitem_psgss_season[0].text : "",
                            custitem_psgss_year: (fieldLookUp.custitem_psgss_year.length) ? fieldLookUp.custitem_psgss_year[0].text : "",
                            custitem11: fieldLookUp.custitem11,
                            custitem_bhpc_prod_img: parentImageURL
                        };
                        pdfFieldsObj.season_year = pdfFieldsObj.custitem_psgss_season + " " + pdfFieldsObj.custitem_psgss_year
                        pdfPOFields.push(pdfFieldsObj);
                        exist.push(parentId);
                    }
                }

                // get bills total
                var billsTotal = 0.00;
                var billsCtr = 0;
                var deposits = [];
                for(var i = 0; i < linksCount; i++){
                    var linkType = recObj.getSublistValue({
                        sublistId: 'links',
                        fieldId: 'type',
                        line: i
                    });

                    if(linkType == 'Deposit Application'){
                        var totalAmt = parseFloat(recObj.getSublistValue({
                            sublistId: 'links',
                            fieldId: 'total',
                            line: i
                        }));

                        var tranDate = recObj.getSublistValue({
                            sublistId: 'links',
                            fieldId: 'trandate',
                            line: i
                        });

                        var deposit = recObj.getSublistValue({
                            sublistId: 'links',
                            fieldId: 'id',
                            line: i
                        });

                        deposits.push(deposit);
                                
                        var rate = currency.exchangeRate({
                            source: currFormatter.currency,
                            target: 'USD',
                            date: new Date(tranDate)
                        });

                        
                        // add bills total 
                        totalAmt = totalAmt / rate;
                        totalAmt = Math.floor(totalAmt * 100)
                        totalAmt = totalAmt / 100;
                        
                        billsTotal = billsTotal - totalAmt;
                        recTotal = recTotal - totalAmt;
                        billsCtr++;
                    }
                }

                // get black logo
                var logoObj = file.load({
                    id: 'Images/BHPC Classic Logo.jpg'
                });

                blackLogoURL = firstURLPart + logoObj.url;

                var pdfData = {
                    parentFields: pdfPOFields,
                    billsCtr: billsCtr,
                    billsTotal: billsTotal,
                    recTotal: recTotal,
                    blackLogoURL: blackLogoURL,
                    test: deposits
                }                

                // invoice workaround -- (adding field doesn't work for some reason)
                var fieldValue = JSON.stringify(pdfData);
                recObj.setValue({ fieldId: 'custbody_acs_inv_grid_templt_data', value: fieldValue });
                recObj.save();

                
            }
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});