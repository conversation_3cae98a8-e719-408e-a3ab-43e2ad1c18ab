<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlfooter">
                <table style="width: 100%; font-size: 8pt;">
                    <tr>
                        <td align="right" style="padding: 0;">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            .tableStyleGeneral {
                font-size: 9pt;
                table-layout: fixed;
                border-collapse: collapse;
            }

            .tableStyleSpecific {
                font-size: 9pt;
                table-layout: fixed;
                border-collapse: collapse;
                width: 100%;
            }

            .thStyle {
                border: 1px solid black;
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            .thStyleOrderItem {
                border: 1px solid black;
                font-weight: bold;
                font-size: 8pt;
                padding: 10px 6px;
                background-color: #e3e3e3;
                color: #333333;
            }

            .tdStyle {
                border: 1px solid black;
            }

            td {
                padding: 2px 2px;
            }

            td p {
                align: left;
            }

			table tr .tblHeader {
				background-color: #e3e3e3;
				font-weight: bold;
				padding: 5px;
			}
			
			table tr .tblHeaderData {
				padding: 5px;
			}
        </style>
    </head>

    <body footer="nlfooter" footer-height="20pt" padding="0.5in 0.1in 0.5in 0.1in" size="Letter-LANDSCAPE">
        <#if record.wavetype == 'Sales Order'>
      	    <#assign sales_order_header_data = record.custpage_so_fields?eval>
        </#if>             
        <#list wavedata as picktasks>
            <#if picktasks_index gt 0>
                <pbr></pbr>
            </#if><!-- start items -->
            <#list picktasks as lineitem>
                <#if lineitem_index==0>
                    <#if record.wavetype == 'Sales Order'>
                        <#list sales_order_header_data as sales_orders>
                            <#if sales_orders.tranid == lineitem.docnum>
                                <#assign headerdata = sales_orders>
                            </#if>
                        </#list>
                    </#if>
                    <table style="width: 100%; font-size: 10pt;">
                        <tr>
                            <td rowspan="3" style="padding: 0; width: 33.3%; vertical-align:top;">
                                <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                        style="float: left; margin: 7px; display: block; height: 50px; width:350px;" />
                                </#if>
                            </td>
                            <td align="center" rowspan="3" style="padding: 0; width: 33.3%; font-size: 20pt; vertical-align:top;">
                                <b>${record@title}</b></td>
                            <td align="right" rowspan="3" style="padding: 0; width: 33.3%; vertical-align:top;">
                                <#if printed><b>**${duplicate}**</b><br /></#if> ${createdlabel}: ${.now?date}
                            </td>
                        </tr>
                    </table>

                    <table class="tableStyleGeneral" style="width: 45%">
                        <tr>
                            <td style="align: center;">${record.name@label}</td>
                            <td style="align: center;">${lineitem.docnum@label}</td>
                        </tr>
                        <tr>
                            <td style="align: center;">
                                <barcode codetype="code128" showtext="true" value="${record.name}" />
                            </td>
                            <td style="align: center;">
                                <barcode codetype="code128" showtext="true" value="${lineitem.docnum}" />
                            </td>
                        </tr>
                    </table>
                    <#if lineitem.picktype=="MULTI">

                        <table class="tableStyleGeneral" style="width:100%;">
                            <tr>
                                <td style="align: right; font-size:13pt"><b>${lineitem.picker@label}:
                                        ${lineitem.picker}</b></td>
                            </tr>
                        </table>
                        <br />
                    </#if>
                    <#if lineitem.picktype=="SINGLE">
                    
                        <table class="tableStyleSpecific">
                            <tr>
                                <td style="align: center; width: 76%">
                                    <#if record.wavetype == 'Sales Order'>
                                        <table style="width:100%;">
                                            <tr class="tblHeader">
                                                <th style="width: 30%">&nbsp;PO #</th>
                                                <th style="width: 20%">&nbsp;Terms</th>
                                                <th style="width: 30%">&nbsp;Location</th>
                                                <th style="width: 20%">&nbsp;Req. Ship Date</th>
                                            </tr>
                                            <tr class="tblHeaderData">
                                                <td>&nbsp;&nbsp;${headerdata.custPO}</td>
                                                <td>&nbsp;&nbsp;${headerdata.term}</td>
                                                <td>&nbsp;&nbsp;${record.location}</td>
                                                <td>&nbsp;&nbsp;${headerdata.reqShipDate?date}</td>
                                            </tr>
                                            <tr class="tblHeader">
                                                <th colspan="2">&nbsp;Shipping Method</th>
                                                <th>&nbsp;Carrier Account</th>
                                                <th>&nbsp;Freight Terms</th>
                                            </tr>
                                            <tr class="tblHeaderData">
                                                <td colspan="2">&nbsp;&nbsp;${headerdata.shipMethod}</td>
                                                <td>&nbsp;&nbsp;${headerdata.carrierAccount}</td>
                                                <td>&nbsp;&nbsp;${headerdata.freightTerm}</td>
                                            </tr>
                                            <tr class="tblHeader">
                                                <th colspan="2">&nbsp;Memo</th>
                                                <th>&nbsp;Shipping Instructions</th>
                                                <th>&nbsp;Est. WT (LBS)</th>
                                            </tr>
                                            <tr class="tblHeaderData">
                                                <td colspan="2">&nbsp;&nbsp;${headerdata.memo}</td>
                                                <td>&nbsp;&nbsp;${headerdata.shipInst}</td>
                                                <td>&nbsp;&nbsp;${headerdata.estsowt}</td>
                                            </tr>
                                        </table>
                                    </#if>
                                </td>
                                <td style="align: center; width: 35%">
                                    <table class="tableStyleSpecific" style="border: 1px solid black">
                                        <tr>
                                            <td style="align: right">${lineitem.customer@label}:</td>
                                            <td style="align: right">${lineitem.customer}</td>
                                        </tr>
                                        <tr>
                                            <td style="align: right; width: 25%">${lineitem.shipaddress1@label}:</td>
                                            <td style="align: right; width: 75%">${lineitem.shipaddress1}</td>
                                        </tr>
                                        <tr>
                                            <td style="align: right">${lineitem.shipaddress2}</td>
                                        </tr>
                                        <tr>
                                            <td style="align: right">${lineitem.shipaddress3}</td>
                                        </tr>
                                        <tr>
                                            <td style="align: right">${lineitem.shipcity@label}:</td>
                                            <td style="align: right">${lineitem.shipcity}</td>
                                        </tr>
                                        <tr>
                                            <td style="align: right">${lineitem.shipstate@label}:</td>
                                            <td style="align: right">${lineitem.shipstate}</td>
                                        </tr>
                                        <tr>
                                            <td style="align: right">${lineitem.shipzip@label}:</td>
                                            <td style="align: right">${lineitem.shipzip}</td>
                                        </tr>
                                        <tr>
                                            <td style="align: right">${lineitem.shipcountry@label}:</td>
                                            <td style="align: right">${lineitem.shipcountry}</td>
                                        </tr>
                                        <tr>
                                            <td style="align: right; width: 35%">Attention:</td>
                                            <td style="align: right; width: 65%">${lineitem.shippingattention}</td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                        <table class="tableStyleGeneral" width="100%">
                            <tr>
                                <td style="align: right; font-size:13pt"><b>${lineitem.picker@label}:
                                        ${lineitem.picker}</b><br /><br />&nbsp;&nbsp;</td>
                            </tr>
                        </table>

                        <#else>

                            <table class="tableStyleSpecific">
                                <thead>
                                    <tr>
                                        <th align="center" class="thStyle" width="20%">${binlabel}</th>
                                        <th align="center" class="thStyle" width="40%">${lineitem.itemname@label}</th>
                                        <th align="center" class="thStyle" width="20%">${lineitem.description@label}
                                        </th>
                                        <th align="center" class="thStyle" width="20%">${lineitem.picktaskname@label}
                                        </th>
                                    </tr>
                                </thead>
                                <tr>
                                    <td align="center" class="tdStyle" line-height="100%" style="width:20%">
                                        <#if lineitem.status=="FAILED">${lineitem.statusname}: ${lineitem.failmessage}
                                            <#else>
                                                <#if lineitem.binName1!=""> ${lineitem.binName1}
                                                    (${lineitem.binQuantity1} ${lineitem.baseunit})<br />
                                                    <#if lineitem.binName2!=""> ${lineitem.binName2}
                                                        (${lineitem.binQuantity2} ${lineitem.baseunit})<br />
                                                        <#if lineitem.binName3!=""> ${lineitem.binName3}
                                                            (${lineitem.binQuantity3} ${lineitem.baseunit})<br />
                                                            <#if lineitem.binName4!=""> ${lineitem.binName4}
                                                                (${lineitem.binQuantity4} ${lineitem.baseunit})<br />
                                                                <#if lineitem.binName5!=""> ${lineitem.binName5}
                                                                    (${lineitem.binQuantity5}
                                                                    ${lineitem.baseunit})<br /></#if>
                                                            </#if>
                                                        </#if>
                                                    </#if>
                                                </#if>
                                        </#if>
                                    </td>
                                    <td align="center" class="tdStyle" line-height="100%" style="width:40%">
                                        <barcode codetype="code128" showtext="true" value="${lineitem.itemname}"
                                            width="100%" height="11%" />
                                    </td>
                                    <td align="center" class="tdStyle" line-height="100%" style="width:20%">
                                        ${lineitem.description}</td>
                                    <td align="center" class="tdStyle" line-height="100%" style="width:20%">
                                        <barcode codetype="code128" showtext="true" value="${lineitem.picktaskname}" />
                                    </td>
                                </tr>
                            </table>
                    </#if>
                </#if>

                <table class="tableStyleSpecific" style="width: 100%;">
                    <#if lineitem_index==0>
                        <#if lineitem.picktype=="SINGLE">
                            <thead>
                                <tr>
                                    <th class="thStyleOrderItem" style="width:31%">${lineitem.itemname@label}</th>
                                    <th class="thStyleOrderItem" style="width:15%">${lineitem.description@label}</th>
                                    <th class="thStyleOrderItem" style="width:5%">${lineitem.linenumber@label}</th>
                                    <th class="thStyleOrderItem" style="width:7%">${lineitem.quantity@label}</th>
                                    <th class="thStyleOrderItem" style="width:7%">${lineitem.units@label}</th>
                                    <th class="thStyleOrderItem" style="width:18%">${binlabel}</th>
                                    <th class="thStyleOrderItem" style="width:20%">${lineitem.picktaskname@label}</th>
                                </tr>
                            </thead>
                            <#else>
                                <thead>
                                    <tr>
                                        <th class="thStyleOrderItem" style="width:19%">${lineitem.docnum@label}</th>
                                        <th class="thStyleOrderItem" style="width:8%">${lineitem.linenumber@label}</th>
                                        <th class="thStyleOrderItem" style="width:8%">${lineitem.quantity@label}</th>
                                        <th class="thStyleOrderItem" style="width:10%">${lineitem.units@label}</th>
                                        <th class="thStyleOrderItem" style="width:15%">${lineitem.customer@label}</th>
                                        <th class="thStyleOrderItem" style="width:13%">${lineitem.shipmethod@label}</th>
                                        <th class="thStyleOrderItem" style="width:40%">${shipto}</th>
                                    </tr>
                                </thead>
                        </#if>
                    </#if>
                    <tr>
                        <#if lineitem.picktype=="SINGLE">
                            <td align="center" class="tdStyle" line-height="100%"
                                style="width:31%; padding: 20px 2px 20px 2px;" vertical-align="middle">
                                <barcode codetype="code128" showtext="true" value="${lineitem.itemname}" width="100%"
                                    height="14%" />
                            </td>
                            <td align="center" class="tdStyle" line-height="100%" style="width:15%"
                                vertical-align="middle">${lineitem.description}</td>
                            <td align="center" class="tdStyle" line-height="100%" style="width:5%"
                                vertical-align="middle">${lineitem.linenumber}</td>
                            <td align="center" class="tdStyle" line-height="100%" style="width:7%"
                                vertical-align="middle">${lineitem.quantity}</td>
                            <td align="center" class="tdStyle" line-height="100%" style="width:7%"
                                vertical-align="middle">${lineitem.units}</td>
                            <td align="center" class="tdStyle" line-height="100%" style="width:18%"
                                vertical-align="middle">
                                <#if lineitem.status=="FAILED">${lineitem.statusname}: ${lineitem.failmessage} <#else>
                                        <#if lineitem.binName1!=""> ${lineitem.binName1} (${lineitem.binQuantity1}
                                            ${lineitem.baseunit})<br />
                                            <#if lineitem.binName2!=""> ${lineitem.binName2} (${lineitem.binQuantity2}
                                                ${lineitem.baseunit})<br />
                                                <#if lineitem.binName3!=""> ${lineitem.binName3}
                                                    (${lineitem.binQuantity3} ${lineitem.baseunit})<br />
                                                    <#if lineitem.binName4!=""> ${lineitem.binName4}
                                                        (${lineitem.binQuantity4} ${lineitem.baseunit})<br />
                                                        <#if lineitem.binName5!=""> ${lineitem.binName5}
                                                            (${lineitem.binQuantity5} ${lineitem.baseunit})<br /></#if>
                                                    </#if>
                                                </#if>
                                            </#if>
                                        </#if>
                                </#if>
                            </td>
                            <td align="center" class="tdStyle" line-height="100%" style="width:20%"
                                vertical-align="middle">
                                <barcode codetype="code128" showtext="true" value="${lineitem.picktaskname}" />
                            </td>
                            <#else>
                                <td align="center" class="tdStyle" line-height="100%" style="width:19%"
                                    vertical-align="middle">
                                    <barcode codetype="code128" showtext="true" value="${lineitem.docnum}" />
                                </td>
                                <td align="center" class="tdStyle" line-height="100%" style="width:8%"
                                    vertical-align="middle">${lineitem.linenumber}</td>
                                <td align="center" class="tdStyle" line-height="100%" style="width:8%"
                                    vertical-align="middle">${lineitem.quantity}</td>
                                <td align="center" class="tdStyle" line-height="100%" style="width:10%"
                                    vertical-align="middle">${lineitem.units}</td>
                                <td align="left" class="tdStyle" line-height="100%" style="width:15%"
                                    vertical-align="middle">${lineitem.customer}</td>
                                <td align="left" class="tdStyle" line-height="100%" style="width:13%"
                                    vertical-align="middle">${lineitem.shipmethod}</td>
                                <td align="left" class="tdStyle" line-height="100%" style="width:40%">
                                    <table style="font-size: 6pt; ">
                                        <tr>
                                            <td>${lineitem.customer@label}</td>
                                            <td>${lineitem.customer}</td>
                                        </tr>
                                        <tr>
                                            <td>${lineitem.shipaddress1@label}:</td>
                                            <td>${lineitem.shipaddress1}</td>
                                        </tr>
                                        <tr>
                                            <td>${lineitem.shipaddress2}</td>
                                        </tr>
                                        <tr>
                                            <td>${lineitem.shipaddress3}</td>
                                        </tr>
                                        <tr>
                                            <td>${lineitem.shipcity@label}:</td>
                                            <td>${lineitem.shipcity}</td>
                                        </tr>
                                        <tr>
                                            <td>${lineitem.shipstate@label}</td>
                                            <td>${lineitem.shipstate}</td>
                                        </tr>
                                        <tr>
                                            <td>${lineitem.shipzip@label}</td>
                                            <td>${lineitem.shipzip}</td>
                                        </tr>
                                        <tr>
                                            <td>${lineitem.shipcountry@label}</td>
                                            <td>${lineitem.shipcountry}</td>
                                        </tr>
                                        <tr>
                                            <td>${lineitem.shippingattention@label}</td>
                                            <td>${lineitem.shippingattention}</td>
                                        </tr>
                                    </table>
                                </td>
                        </#if>
                    </tr>
                </table>
            </#list><!-- end items -->
        </#list>
    </body>
</pdf>