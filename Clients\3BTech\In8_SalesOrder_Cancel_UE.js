/**
 * Sales Order - Cancel status
 * 
 * Version    Date            Author           Remarks
 * 1.00       20 Jun 2016     <PERSON> (In8)   Initial Version
 * 

/**
 * After Submit Event
 * 
 * @param {String} type Operation types 
 *                      
 * @returns {Void}
 */
function afterSubmit(type) {
  
	try {
		if (type == 'edit') {
			var cancelValue = nlapiGetNewRecord().getFieldValue('custbody_so_status_in8'),
				cancelValueOld = nlapiGetOldRecord().getFieldValue('custbody_so_status_in8');
		
			// Checks if the value is different when editing a record
			if (cancelValue != cancelValueOld && cancelValue == 3) {
				nlapiLogExecution('DEBUG', 'In8', 'Order status is changed. Id: ' + nlapiGetRecordId());
				
				In8Lib.addToQueue('salesorderstatus', 'salesorder', nlapiGetRecordId(), 'edit');
			}				
		}		
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error on Sales Order ' + nlapiGetRecordId(), ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
	}
}
