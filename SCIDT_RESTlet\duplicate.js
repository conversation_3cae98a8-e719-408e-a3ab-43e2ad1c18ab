/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       27 May 2020     jdgonzal
 *
 */

/**
 * @param {Object} dataIn Parameter object
 * @returns {Object} Output object
 */

function runSearch() {

    var filterArr = [
        new nlobjSearchFilter('type', null, 'anyof', 'SalesOrd'),
        new nlobjSearchFilter('mainline', null, 'is', true),
        new nlobjSearchFilter('datecreated', null, 'within', '2020-03-01', '2020-05-31')
    ];

    var columnArr = [
        new nlobjSearchColumn('tranid'),
        new nlobjSearchColumn('trandate'),
        new nlobjSearchColumn('entityid', 'customer'),
        new nlobjSearchColumn('amount')
    ];

    var searchObj = nlapiCreateSearch('salesorder', filterArr, columnArr);
    var sResult = searchObj.runSearch();
    var firstThirty = sResult.getResults(0, 30);
    var formattedResult = {};
    for(var i = 0; i < firstThirty.length; i++){
        firstThirty[i].getValue('tranid');
    }

    return firstThirty;
}

function getRESTlet(dataIn) {

    var resultObj = {};

    if(dataIn.empId && dataIn.itemId) {
        return resultObj = {
            'result': 'failed',
            'message': 'script does not know how to handle this request'
        }
    }

    if(dataIn.itemId) {

        var itemObj = nlapiLoadRecord('inventoryitem', dataIn.itemId);

        // get total quantity on all locations
        var totalQuantity = 0;
        // get total locations
        locationCount = itemObj.getLineItemCount('locations');
        // loop through all locations and add them to totalQuantity
        for(var i = 0; i < locationCount; i++){
            totalQuantity += parseInt(itemObj.getLineItemValue('locations', 'quantityavailable', i+1));
        }

        resultObj = {
            'itemId': dataIn.itemId,
            'Item Name': itemObj.getFieldValue('itemid'),
            'Costing Method': itemObj.getFieldValue('costingmethod'),
            'Total Value': itemObj.getFieldValue('totalvalue'),
            'Total Quantity on all Locations': totalQuantity            
        };

    } else if (dataIn.empId) {

        var empObj = nlapiLoadRecord('employee', dataIn.empId);

        resultObj = {
            'empId': dataIn.empId,
            'First name': empObj.getFieldValue('firstname'),
            'Last name': empObj.getFieldValue('lastname'),
            'Email': empObj.getFieldValue('email'),
            'Supervisor': empObj.getFieldText('supervisor')
        };

    } else {

        var searchResult = runSearch();

        resultObj = {
            'SO Result': searchResult
        };
    }
    
	return resultObj;

}

/**
 * @param {Object} dataIn Parameter object
 * @returns {Object} Output object
 */
function postRESTlet(dataIn) {
    var taskObj = nlapiCreateRecord('task');
    taskObj.setFieldValue('title', dataIn.title);
    taskObj.setFieldValue('assignedTo', dataIn.assignedTo);
    var taskId = nlapiSubmitRecord(taskObj);
	return "Successfully added task ID: " + taskId;
}

/**
 * @param {Object} dataIn Parameter object
 * @returns {Void} 
 */
function deleteRESTlet(dataIn) {
	
}

/**
 * @param {Object} dataIn Parameter object
 * @returns {Object} Output object 
 */
function putRESTlet(dataIn) {
	
	return {};
}
