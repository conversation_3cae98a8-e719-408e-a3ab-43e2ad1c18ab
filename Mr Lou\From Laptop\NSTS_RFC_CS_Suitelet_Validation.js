/**
 * Copyright (c) 1998-2017 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * NetSuite, Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with NetSuite.
 * 
 * This script served as client script validation for RMA Case Suitelet
 * 
 * Version    Date            Author			Remarks
 * 1.00       10 Apr 2017     rilagan   		Initial version.
 * 
 */
/**
 * @NApiVersion 2.0
 * @NScriptType clientscript
 * @NModuleScope Public
 * @NAmdConfig ./../configuration.json
 */

 try {
    require.config({
        paths : {
        	"jquery": "./../library/jquery-3.2.1.min.js"
        }
    });

} catch(e) {
}

var HC_MODULES = ['N/format', 'N/search', 'N/record', 'N/currentRecord', 'N/url', 'N/runtime', '../library/NSTS_RFC_Lib_ObjectsAndFunctions.js', 'jquery'];


define(HC_MODULES, 
    function(format, search, record, currRecord, url, runtime, lib, jq) {

	//Add constants here 

	var HC_RFC_LIST_CREATE_TRANS = {  
		CREATE_RMA : '1',
		CREATE_SO : '2' 
	}

	var TRANS_LINES_THRESHOLD = 100;

	var HC_RFC_SAVED_SEARCHES = {  
		SS_RFC_SOURCE_TRANS 	: 'customsearch_nsts_rfc_source_trans_rpt'
	}
	var HC_RFC_SUITELET_SHOW_TRANS = {
	        Title: 'Sales Order / Invoice Search',
	        Fields: {
	            CASE: {
	                ID      : 'custpage_nsts_rfc_case',
	                LABEL   : 'Case',
	                HELP	: 'The case number, based on which the transaction would be created'
	            },
	            CUSTOMER: {
	                ID      : 'custpage_nsts_rfc_customer',
	                LABEL   : 'Customer',
	                HELP	: 'The customer linked to the case and the transactions'
	            },
	            TRANSACTION_FROM: {
	                ID      : 'custpage_nsts_rfc_trans_from',
	                LABEL   : 'Transaction  From',
	                HELP	: 'The oldest date of the transaction that would be included in the search. This date is calculated with reference to the to date and the days differential'
	            },
	            TRANSACTION_TO: {
	                ID      : 'custpage_nsts_rfc_trans_to',
	                LABEL   : 'Transaction To',
	                HELP	: 'The latest date of the transaction that would be included in the search'
	            },
	            SEARCH_TYPE: {
	                ID      : 'custpage_nsts_rfc_search_type',
	                LABEL   : 'Search Transaction Type',
	                HELP	: 'Set the transaction type that would be retrieved. The options are Invoices or Sales Orders'
	            },
	            DATE_CREATE: {
	                ID      : 'custpage_nsts_rfc_date_trans',
	                LABEL   : 'Date on Create Transaction',
	                HELP	: 'Set the Date on the Transaction created from the Case record. The options are System Date, Source Transaction Date or the Case Date'
	            },
	            CREATION_ACTION: {
	                ID      : 'custpage_nsts_rfc_create_trans',
	                LABEL   : 'Create Transaction',
	                HELP	: 'Set the transaction type that should be created. The options are Return Authorization or Sales Order'
	            },
                SHIP_METHOD: {
                    ID      : 'custpage_nsts_rfc_ship_method',
                    LABEL   : 'Shipping Method',
                    HELP	: 'Select which type of shipping service (method) you want to use for this transaction.'
                },
				SHIP_ADDRESS: {
					ID      : 'custpage_nsts_rfc_ship_address',
					LABEL   : 'Shipping Address',
					HELP	: 'Select which type of shipping address you want to use for this transaction.'
                },
	            PAGE_BUTTONS: {
	                ID      : 'custpage_nsts_rfc_page_btns',
	                LABEL   : 'Table Buttons'
	            },
	            TREE_RESULTS: {
	                ID      : 'custpage_nsts_rfc_tree_results',
	                LABEL   : 'Tree Results'
	            },
	            TRANSACTION_NO: {
	                ID      : 'custpage_nsts_rfc_trans_no',
	                LABEL   : 'Transaction No',
	                HELP	: 'Enter the transaction/document number (fieldid: tranid) that is reported in this case.'
	            },
	            SELECTED_TRANS: {
	                ID      : 'custpage_nsts_rfc_sel_trans',
	                LABEL   : 'Selected Trans Internal ID'
	            },
	            DEFAULT_MEMO: {
	                ID      : 'custpage_nsts_rfc_def_memo',
	                LABEL   : 'Default Memo Text',
	                HELP	: 'Set the Default Memo Text in the transaction created.'
	            },
	            SELECTED_LINES: {
	                ID      : 'custpage_nsts_rfc_sel_lines',
	                LABEL   : 'Lines'
	            },
	            SELECTED_TYPE: {
	                ID      : 'custpage_nsts_rfc_sel_rectype',
	                LABEL   : 'Selected Record Type'
	            },
	            CURR_PAGE: {
	                ID      : 'custpage_nsts_rfc_curr_page',
	                LABEL   : 'Curr Page'
	            },
	            LAST_PAGE: {
	                ID      : 'custpage_nsts_rfc_last_page',
	                LABEL   : 'Last Page'
	            },
	            TABLE_PAGE: {
	                ID      : 'custpage_nsts_rfc_t_page',
	                LABEL   : 'Pages'
	            },
	            ACTION_TYPE: {
	                ID      : 'custpage_nsts_rfc_act_type',
	                LABEL   : 'Action Type'
	            },
	            MULT_CURR: {
	                ID      : 'custpage_nsts_mult_curr',
	                LABEL   : 'mult curr'
	            }
	        }
	}

	var HC_RFC_RELATED_RECORDS = {
			ID     :  'customrecord_nsts_rfc_case_related_rec',
			FIELDS : {
				ID											: 'internalid',
				CUSTRECORD_NSTS_RFC_CASE					: 'custrecord_nsts_rfc_case',			
				CUSTRECORD_NSTS_RFC_RELATED_RECORDS			: 'custrecord_nsts_rfc_related_records',					
				CUSTRECORD_NSTS_RFC_CREATED_TRANS			: 'custrecord_nsts_rfc_created_trans'
			}
	}
	
	//Bind JQUERY to checkboxes
	function pageInit(context){
		jq.noConflict();
        console.log('version--'+jq.fn.jquery);

		
        try{
        	jq(document).on('click', 'tr td input[type=checkbox]', function (event) {
        		var recCurrent = context.currentRecord;

                var stTrId = jq(this).closest('tr').attr('id');
                var objRow = jq(this);
                var objTable = jq('#lot2');

                var objLabel = jq('#label-'+stTrId);
                var stSelectedTrans = recCurrent.getValue({fieldId: HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TRANS.ID});
                var stSelectedLines = recCurrent.getValue({fieldId: HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID});
                var bMultCurr = recCurrent.getValue({fieldId: HC_RFC_SUITELET_SHOW_TRANS.Fields.MULT_CURR.ID});
                if(!lib.isEmpty(bMultCurr))
                	bMultCurr = bMultCurr.toLowerCase();
                var bCheck = objRow.is(':checked');
                if(bCheck){
                    console.log('checked tr id: '+stTrId);
                	if(!lib.isEmpty(stSelectedTrans) && !(stTrId.indexOf('uniquekey') >= 0) && !(stTrId.indexOf('checkall') >= 0)){
                    	alert('You can only select one transaction. Please uncheck other transactions.');
                    	return false;
                    }
                	if(stTrId.indexOf('checkall') >= 0) {
                		var objKey = stTrId.split('-');
                		var stId = objKey[1];
                		var stResType = objKey[2];
                		var arrSel = [];
                		objTable.find('tr[id^='+'rfc_tr-'+stId+'-' + stResType+'-uniquekey] input[type=checkbox]').each(function(){
                        	var objTr = jQuery(this);
                        	var stUniqId = objTr.attr('id');
                        	 stUniqId = stUniqId.split('-');
                        	 stUniqId = stUniqId[1];
                    		arrSel.push(stUniqId);
                    		objTr.prop('checked', true);
                        }); 

            			var stSelectedLines = arrSel.join(',');
            			recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID, value: stSelectedLines });
                	}else if(stTrId.indexOf('uniquekey') >= 0) {
                		var objKey = stTrId.split('-');
                		var stKey = objKey[3];
                		stKey = stKey.replace('uniquekey','');
                		var arrKeys = [];
                		if(!lib.isEmpty(stSelectedLines)){
                			var arrLines = stSelectedLines.split(',');
                			arrLines.push(stKey);
                			stSelectedLines = arrLines.join(',');
                			recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID, value: stSelectedLines });
                		}else{
                			recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID, value: stKey });
                		}
                	}else if(stTrId.indexOf('opened') >= 0) {
                		var objRec = stTrId.split('-');
                		stResultId = objRec[1];
                		stResultType = objRec[2];

                		objTable.find('tr[id^='+'rfc_tr-'+stResultId+'-' + stResultType+'-uniquekey]').each(function(){
                             jQuery(this).show();
                        });           

                		var stCheckAllId = 'checkall-'+stResultId;
                		objTable.find('tr[id^='+stCheckAllId+'] input[type=checkbox]').each(function(){
                    		var objTr = jQuery(this);
                    		objTr.prop('checked',false);
                    		objTr.closest('tr').show();
                        }); 
                		recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TRANS.ID, value: stResultId });
                		recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TYPE.ID, value: stResultType });
                	}else{
                		try{

                    		var objRec = stTrId.split('-');
                    		stResultId = objRec[1];
                    		stResultType = objRec[2];
                    		recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TRANS.ID, value: stResultId });
                    		recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TYPE.ID, value: stResultType });
                    		objLabel = jq('#label-'+stResultId);
                    		objLabel.text('Loading');
                    		
                    		var objOrigSearch = search.load({
                		        id: HC_RFC_SAVED_SEARCHES.SS_RFC_SOURCE_TRANS
                		    });
                    		var objCustFilters = [];
                			var objCustColumns = objOrigSearch.columns;
                			var objCustColumns = [];
                			var objSearchColumns = objOrigSearch.columns;
                			for(var i=0;i<objSearchColumns.length;i++){
                				var stName = objSearchColumns[i].name;
                				if(stName.indexOf('formula') > -1  && (objSearchColumns[i].formula)){
                					stName = stName+'_'+i;
                					if(bMultCurr == 'true'){
                    					objCustColumns.push( search.createColumn({
                   						 name: stName,
                   						 formula: objSearchColumns[i].formula,
                   						 join: objSearchColumns[i].join,
                   						 label: objSearchColumns[i].label,
                   						 summary: objSearchColumns[i].summary,
                   						 sort: objSearchColumns[i].sort,
                   						 function: objSearchColumns[i].function						 
                   						 })
                   					 );
                					}else{

                						var stFormula = objSearchColumns[i].formula;
                						if(!lib.isEmpty(stFormula)){

                							stFormula = lib.replaceAll('fxamount','amount',stFormula);
                							objCustColumns.push( search.createColumn({
                								 name: stName,
                								 formula: stFormula,
                								 join: objSearchColumns[i].join,
                								 label: objSearchColumns[i].label,
                								 summary: objSearchColumns[i].summary,
                								 sort: objSearchColumns[i].sort,
                								 function: objSearchColumns[i].function						 
                								 })
                							 );
                						}
                					}
                				}else{
                					objCustColumns.push(objSearchColumns[i]);
                				}
                			}
                    		var objSearchFilters = objOrigSearch.filters;
                    		for(var i=0;i<objSearchFilters.length;i++){
                				var stName = objSearchFilters[i].name;
                				if(stName != 'mainline'){
                    				objCustFilters.push(objSearchFilters[i]);
                    				
                				}
                			}
                    		objCustFilters.push(search.createFilter({
                 	            name: 'mainline',
                 	            operator: search.Operator.IS,
                 	            values: 'F'
                 	        }));

                    		objCustFilters.push(search.createFilter({
                 	            name: 'internalidnumber',
                 	            operator: search.Operator.EQUALTO,
                 	            values: parseInt(stResultId)
                 	        }));

                		    var objCustSearch = search.create({
                		        type: objOrigSearch.searchType,
                				filters: objCustFilters,
                				columns: objCustColumns
                		    });
                    		var stTableTree = '';
                    		var intLine = 0;
                    		var intCols = 0;

                    		var intCurrPad = objRow.closest('td').css('padding-left');
                            var intIndentedPad = parseInt(intCurrPad) + 40 + 'px';
                    		objCustSearch.run().each(function(result){
                    			var stNewId = stTrId+'-uniquekey';
                    			var stUniqueKey = result.getValue({name: 'lineuniquekey', join: ''});
                    			var stItem = result.getText({name: 'item', join: ''});
                    			stNewId = stNewId+stUniqueKey;
                    			stTableTree += '<tr id="'+stNewId+'">';

                        		intCols = 0;

                    			stTableTree += ('<td " style="padding-left: ' + intIndentedPad + ';white-space: nowrap;"><input type="checkbox" id="'+stResultId+'-'+stUniqueKey+'" /></td>');
                    			for (var i = 0; i < objCustSearch.columns.length; i++) {
                    				var objCol = objCustSearch.columns[i];
                    				var stName = objCol.name;
                                    var stJoin = objCol.join;
                                    var stValue = '';
                                    stValue = result.getText({name: stName, join: lib.isEmpty(stJoin) ? '' : stJoin});  
                    				if(lib.isEmpty(stValue))
                                        stValue = result.getValue({name: stName, join: lib.isEmpty(stJoin) ? '' : stJoin});
                    				
                    				if(!lib.isEmpty(stName)){
			                            if(!lib.isEmpty(stValue) && stName.indexOf('formulacurrency') == 0){
			                            	stValue = lib.getAmountWithCommas(stValue);
			                            }
		                            }
                    				if(stName == 'statusref'){
                    					stTableTree += '<td></td>';
                    					intCols++;
                    				}else if(stName == 'trandate'){
                    					stTableTree += '<td></td>';
                    					intCols++;
                    				}else if(stName == 'type'){
                    					stTableTree += '<td></td>';
                    					intCols++;
                    				}else if(stName == 'tranid'){
                        				stTableTree += '<td>'+stItem+'</td>';
                        				intCols++;
                    				}else if(stName != 'lineuniquekey' && stName != 'item'){
                    					stTableTree += '<td>'+stValue+'</td>';
                    					intCols++;
                    				}
                    				
                    			}

                    			stTableTree += '</tr>';
                    			intLine++;
                    			return true;
                    		});
                    		var stCheckAllId = 'checkall-'+stResultId+'-'+stResultType;
                    		var stCheckAll = '<tr id="'+stCheckAllId+'">'+  ('<td " style="padding-left: ' + intIndentedPad + ';white-space: nowrap;"><input type="checkbox" id="cbc'+stCheckAllId+'" />Check/Uncheck All</td>');
                    		for (var i=0; i < intCols;i++){
                    			stCheckAll += '<td></td>';
                    		}
                    		stCheckAll += '</tr>'
                        	objRow.closest('tr').after(stCheckAll +stTableTree);
                    		objRow.closest('tr').attr('id',stTrId+'-opened-');

                    		objLabel.text('');

                		}catch(error){
                			alert('Error encountered: '+error.toString());
                		}                	
                	}
                	
                }else{
                	if(stTrId.indexOf('checkall') >= 0) {
                		var objKey = stTrId.split('-');
                		var stId = objKey[1];
                		var stResType = objKey[2];
                		var arrSel = [];
                		objTable.find('tr[id^='+'rfc_tr-'+stId+'-' + stResType+'-uniquekey] input[type=checkbox]').each(function(){
                        	var objTr = jQuery(this);
                    		objTr.prop('checked',false);
                        }); 
            			recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID, value: '' });
                	}else if((stTrId.indexOf('uniquekey') >= 0)) {
                		var objKey = stTrId.split('-');
                		var stKey = objKey[3];
                		stKey = stKey.replace('uniquekey','');
                		var arrKeys = [];
                		if(!lib.isEmpty(stSelectedLines)){
                			var arrLines = stSelectedLines.split(',');
                			if(!lib.isEmpty(arrLines)){
                				var index = arrLines.indexOf(stKey);    

                				if (index !== -1) {
                					arrLines.splice(index, 1);
                					stSelectedLines = arrLines.join(',');
                				}
                			}
                			recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID, value: stSelectedLines });
                		}
                	}else{
                		var objRec = stTrId.split('-');
                		stResultId = objRec[1];
                		stResultType = objRec[2];
                		objTable.find('tr[id^='+'rfc_tr-'+stResultId+'-' + stResultType+'-uniquekey] input[type=checkbox]').each(function(){
                    		var objTr = jQuery(this);
                    		objTr.prop('checked',false);
                    		objTr.closest('tr').hide();
                        }); 

                		var stCheckAllId = 'checkall-'+stResultId;
                		objTable.find('tr[id^='+stCheckAllId+'] input[type=checkbox]').each(function(){
                    		var objTr = jQuery(this);
                    		objTr.prop('checked',false);
                    		objTr.closest('tr').hide();
                        }); 
                    	 
                		recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID, value: '' });
                		recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TRANS.ID, value: '' });
                		recCurrent.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TYPE.ID, value: '' });
                	}
                   
                }
        	});
        }catch(error){
        	console.log(error.toString());
        }
	}
	
    /**
     * Validates MANDATORY and date fields
     */
    function fieldChanged(context) {

        var stFieldId 		= context.fieldId;
        var currentRecord 	= context.currentRecord;
        
    	var objRecord 	= context.currentRecord;
		
        var stFldId 	= context.fieldId;
        if ((stFldId == HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_FROM.ID) || (stFldId == HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_TO.ID)){
        	
			var stFrom = objRecord.getValue({fieldId: HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_FROM.ID});				
			var stTo = objRecord.getValue({fieldId: HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_TO.ID});
			if(!lib.isEmpty(stFrom) && !lib.isEmpty(stTo)){

				var stTransTo = new Date(stTo);    
				var stTransFrom = new Date(stFrom); 
				if(stTransFrom > stTransTo){
					alert("FROM DATE should be earlier than or same as the TO DATE.");
					objRecord.setValue({fieldId:HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_FROM.ID, value: '' });
	                return false;
				}
				var stDateDiff = new Date();
				stTransTo.setDate(stTransTo.getDate() - 89);
				if (stTransFrom < stTransTo){
					alert("Date difference between transaction to and transaction from should be lesser or equal to 90 days.");
					objRecord.setValue({fieldId: HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_FROM.ID, value: '' });
	                return false;   					
				}
			}
		}
    }

    /******************Return to case record ***********************************************/
    function goBack(){
    	var stURL = url.resolveRecord({
    	    recordType: 'supportcase',
    	    recordId: document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.CASE.ID]['value'],
    	    isEditMode: false,
    	    params: {}
    	});
        window.location.assign(stURL);
    }
    
    /**************************** Set suitelet form to edit mode **************************/
    function editForm(){


    	var stDateCreate = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID);  
    	var stCreateTrans = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID); 
    	var stShipMethod = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID); 
    	var stShipAddress = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_ADDRESS.ID); 
    	
    	
	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.ACTION_TYPE.ID]['value'] = 'edit';
	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID]['value'] = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID);
	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID]['value'] = stDateCreate;
	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID]['value'] = stCreateTrans;
	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID]['value'] = stShipMethod;
	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_ADDRESS.ID]['value'] = stShipAddress;
	 	resetHiddenFields();
   	 	window.ischanged = false;
        document.forms[0].submit();
    }
    
    /*********************************** Reset form fields********************************/
    function resetHiddenFields(){
    		
    	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.CURR_PAGE.ID]['value'] = '';
       	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TRANS.ID]['value'] = '';
       	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.LAST_PAGE.ID]['value'] = '';
       	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID]['value'] = '';

        
    }
    
    /*****************************Validates mandatory fields for searching ******************************/
    function submitForm(){
    	var stTransFrom = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_FROM.ID);
    	var stTransTo= currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_TO.ID);
    	var stTransNo = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_NO.ID);
    	var stSearchType = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.SEARCH_TYPE.ID);
    	var stDateCreate = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID);
    	var stCreateTrans = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID);
    	var stShipMethod = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID);
    	var stShipAddress = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_ADDRESS.ID);
    	
    	if(!lib.isEmpty(stTransNo))
    		stTransNo = stTransNo.trim();
    	if(((!lib.isEmpty(stTransFrom)) && (!lib.isEmpty(stTransTo)) && (!lib.isEmpty(stSearchType))) ||
    		(!lib.isEmpty(stTransNo)) && (!lib.isEmpty(stSearchType))){

    	 	resetHiddenFields();
        	
       	 	window.ischanged = false;
    	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID]['value'] = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID);
    	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID]['value'] = stDateCreate;
    	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID]['value'] = stCreateTrans;
    	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID]['value'] = stShipMethod;
    	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_ADDRESS.ID]['value'] = stShipAddress;
       	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.ACTION_TYPE.ID]['value'] = 'submit';
            document.forms[0].submit();
    	}else{
    		alert('The following combination fields are needed to render search results : (Date From, Date To and Transaction type)/(Transaction Number & Transaction Type)');
    	}
    }


    /*****************************Validates mandatory fields for creating transaction ******************************/
    function createTrans(){
    	var stSelectedTrans = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TRANS.ID); 
    	var stSelectedLines = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID); 
    	var stDateCreate = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID);  
    	var stCreateTrans = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID);  
    	var stShipMethod = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID);  
    	var stShipAddress = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_ADDRESS.ID);  
    	var stAction =  currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.ACTION_TYPE.ID);  
    	
    	if(((!lib.isEmpty(stSelectedLines)) && (!lib.isEmpty(stCreateTrans)) && (!lib.isEmpty(stDateCreate)))){
    		try{
    			var stToCreateTransType = '';
    			if(stCreateTrans == HC_RFC_LIST_CREATE_TRANS.CREATE_RMA)
    				stToCreateTransType = 'Return  Authorization';
    			else
    				stToCreateTransType = 'Sales Order';
    			var alertMessage = stToCreateTransType+ " will be created. Edit the transaction and make necessary changes.";

    			var arrSelectedLines = stSelectedLines.split(',');
    			
    			var intSelectedLines = arrSelectedLines.length;
        		if(intSelectedLines > TRANS_LINES_THRESHOLD)
        			alertMessage += "\n\nThe number of lines selected exceeds max no. of lines ("+ TRANS_LINES_THRESHOLD+"). Creating transaction in the background. A notification email would be sent upon completion";
        		
        		var filters = [];
        		
        		filters.push(search.createFilter({
        			name: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_RELATED_RECORDS,
        			operator: search.Operator.ANYOF,
        			values: [stSelectedTrans]
        		}));
        		
        		var objSearchResults = search.create({
        			type: HC_RFC_RELATED_RECORDS.ID,
        			filters: filters,
        			columns: [search.createColumn({name: HC_RFC_RELATED_RECORDS.FIELDS.ID,
        				summary: search.Summary.COUNT})]
        		});
        		
        		var searchResult = objSearchResults.run().getRange(0, 1);

        		if(!lib.isEmpty(searchResult)){
        			var intRecordCount = searchResult[0].getValue({
        				name: HC_RFC_RELATED_RECORDS.FIELDS.ID,
        				summary: search.Summary.COUNT
        				});
        			
        			if(intRecordCount > 0){
        				alertMessage += "\n\nNote: Case related transactions already exist on the source transaction.";
        			}
        		}
        		
        		//alert(alertMessage);
        		var bConfirm = confirm(alertMessage);
        		if(bConfirm){

            	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.ACTION_TYPE.ID]['value'] = 'create';
            	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID]['value'] = stDateCreate;
            	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID]['value'] = stCreateTrans;
            	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID]['value'] = stShipMethod;
            	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_ADDRESS.ID]['value'] = stShipAddress;
            	 	document.forms[0][ HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID]['value'] = currRecord.get().getValue(HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID);

               	 	window.ischanged = false;
                    document.forms[0].submit();
        		}else{
        			
        		}
    		}catch(e){
    			alert(e.toString());
        	}
    	}else{
    		alert('Please select the transaction lines before proceeding');
    	}
    }
    function testonly(){
    	
    }
    return {
        submitForm : submitForm,
        createTrans : createTrans,
        pageInit: pageInit,
        fieldChanged : fieldChanged,
        editForm: editForm,
        goBack: goBack,
        testonly: testonly
    };
    
});
