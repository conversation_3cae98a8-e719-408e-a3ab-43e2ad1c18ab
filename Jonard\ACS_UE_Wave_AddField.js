/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/ui/serverWidget', 'N/format'], function(record, serverWidget, format) {

    function beforeLoad(context) {
        var recordObj = context.newRecord;
        var waveType = recordObj.getValue({
            fieldId: "wavetype"
        });
        if(waveType == 'SalesOrd'){
            var waveOrdCount = recordObj.getLineCount({
                sublistId: 'waveorders'
            });
            
            var formObj = context.form;        
            
            var soField = formObj.addField({
                id : 'custpage_so_fields',
                type : serverWidget.FieldType.LONGTEXT,
                label : 'so fields'
            });
            var pdfSOFields = [];
            for(var i = 0; i < waveOrdCount; i++){
                var soId = recordObj.getSublistValue({
                    sublistId: 'waveorders',
                    fieldId: 'ordernumberid',
                    line: i
                });

                var soObj = record.load({
                    type: record.Type.SALES_ORDER,
                    id: soId,
                    isDynamic: true
                });

                // docnum
                var tranid = soObj.getValue({
                    fieldId: 'tranid'
                });

                // est so wt (lbs)
                var estsowt = soObj.getValue({
                    fieldId: 'custbody_jt_so_est_fulwt'
                });

                // if(estsowt == ""){
                //     var estsowt = soObj.getValue({
                //         fieldId: 'custbody_jt_so_est_sowt'
                //     });
                // }
                
                // customer po
                var custPO = soObj.getValue({
                    fieldId: 'custbody_jt_item_fllmt_po'
                });
                
                // req. ship date
                var reqShipDate = soObj.getValue({
                    fieldId: 'shipdate'
                });

                reqShipDate = format.format({
                    value: new Date(reqShipDate),
                    type: format.Type.DATE
                });
                
                // memo
                var memo = soObj.getValue({
                    fieldId: 'memo'
                });
                
                // shipping instruction
                var shipInst = soObj.getValue({
                    fieldId: 'custbody_jt_so_cust_ship_instr'
                });

                // term
                var term = soObj.getText({
                    fieldId: 'terms'
                });
                
                // freight term
                var freightTerm = soObj.getText({
                    fieldId: 'custbody_pacejet_freight_terms'
                });

                // carrier account
                var carrierAccount = soObj.getValue({
                    fieldId: 'custbody_jt_so_carrier_acct'
                })
                
                // ship method
                var shipMethod = soObj.getText({
                    fieldId: 'shipmethod'
                })

                var pdfFieldObj = {
                    tranid: tranid,
                    estsowt: estsowt,
                    custPO: custPO,
                    reqShipDate: reqShipDate,
                    memo: memo,
                    shipInst: shipInst,
                    term: term,
                    freightTerm: freightTerm,
                    carrierAccount: carrierAccount,
                    shipMethod: shipMethod
                };
                pdfSOFields.push(pdfFieldObj)
            }
            soField.defaultValue = JSON.stringify(pdfSOFields)
            soField.updateDisplayType({
               displayType : serverWidget.FieldDisplayType.HIDDEN
            });
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});