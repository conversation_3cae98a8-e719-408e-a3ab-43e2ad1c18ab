<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>

        <#if record.currency == 'USD'>
            <#setting locale="en_US">
        <#elseif record.currency == "CAD">
            <#setting locale="en_CA">
        <#elseif record.currency == "GBP">
            <#setting locale="en_GB">
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td>
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    style="float: left; margin: 7px" width="250px" height="85px"  /> </#if>
                        </td>
                        <td align="right"><span class="title">${record@title}</span></td>
                    </tr>
                </table>
                <table class="header" style="width: 100%;">                
                    <tr>
                        <td width="72%" class="addressheader"><b>${record.address@label}</b></td>
                        <td width="28%" align="left"><span class="nameandaddress">${record.subsidiary}</span></td>
                    </tr>
                    <tr>
                        <td width="72%" class="address">${record.address}</td>
                        <td width="28%" align="left">
                            <span class="nameandaddress">${record.custbody_subsidiary_address}</span><br />
                            <span class="nameandaddress">Tax ID ${record.custbody_subsidiary_tax_id}</span><br />
                            <span class="nameandaddress">P: ${record.custbody_subsidiary_telephone}</span><br />
                            <span class="nameandaddress">F: ${record.custbody_subsidiary_fax}</span><br />
                            <span class="nameandaddress">${record.custbody_subsidiary_ap_email}</span><br />
                        </td>
                    </tr>
                    <tr>
                    </tr>
                    <tr>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="25%" footer="nlfooter" footer-height="10pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        <#assign total_owing = 0.00 />
        <#assign total_total = 0.00 />
        <#assign total_payment = 0.00 />
        <#if record.apply?has_content>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start apply sublist -->
                <#list record.apply as apply>
                    <#if apply_index==0>
                        <thead>
                            <tr>
                                <th width="20%" align="center">${apply.applydate@label}</th>
                                <th width="20%" align="left">${apply.refnum@label}</th>
                                <th width="20%" align="right">${apply.total@label}</th>
                                <th width="20%" align="right">${apply.amount@label}</th>
                                <th width="20%" align="right" style="white-space: nowrap;">Amount Owing On Bill</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <#assign total_total = total_total + apply.total />
                        <#assign total_payment = total_payment + apply.amount />
                        <#assign amount_owing = apply.total - apply.amount />
                        <td align="center">${apply.applydate}</td>
                        <td align="left">${apply.refnum}</td>
                        <td align="right">${apply.total}</td>
                        <td align="right">${apply.amount}</td>
                        <td align="right">${amount_owing?string.currency}</td>
                    </tr>
                </#list><!-- end apply -->
            </table>
            <#assign total_due = total_total - total_payment >
            <hr />
        </#if>
        <#if record.credit?has_content>
            <#--  <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <#list record.credit as credit>
                    <#if credit_index==0>
                        <thead>
                            <tr>
                                <th align="center" colspan="3">${credit.creditdate@label}</th>
                                <th colspan="5">${credit.type@label}</th>
                                <th align="right" colspan="3">${credit.refnum@label}</th>
                                <th align="right" colspan="6">${credit.appliedto@label}</th>
                                <th align="right" colspan="4">${credit.amount@label}</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td align="center" colspan="3" line-height="150%">${credit.creditdate}</td>
                        <td colspan="5">${credit.type}</td>
                        <td align="right" colspan="3">${credit.refnum}</td>
                        <td align="right" colspan="6">${credit.appliedto}</td>
                        <td align="right" colspan="4">${credit.amount}</td>
                    </tr>
                </#list>
            </table>

            <hr />  -->
        </#if>
        <table class="total" style="width: 100%; margin-top: 10px;">
            <tr class="totalrow">
                <td width="45%" background-color="#ffffff">&nbsp;</td>
                <td width="6%" align="center"><b>${record.currency}</b></td>
                <td width="9%" align="center"><b>${record.total@label}</b></td>
                <td width="20%" align="right">${record.total}</td>
                <td width="20%" align="right">${total_due?string.currency}</td>
            </tr>
        </table>
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td>
                    Payment Date: ${record.trandate}<br />
                    Payment Reference: ${record.transactionnumber}
                    <#if record.custbody_9997_is_for_ep_eft?string != "Yes">
                        <br />Check Number: ${record.tranid}
                    </#if>
                </td>
            </tr>
        </table>
    </body>
</pdf>