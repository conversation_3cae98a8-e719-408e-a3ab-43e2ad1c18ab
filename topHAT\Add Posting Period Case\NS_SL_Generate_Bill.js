/**
 * Copyright (c) 1998-2020 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of NetSuite, Inc. ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with NetSuite.
 *
 * Suitelet to trigger Bill creation.
 *
 * Version  Date         		Author              Remarks
 * 1.00     20 November 2019  	bjalandoni          Initial draft
 * 1.01     08 January  2020  	ccutib              Call M/R script
 * 1.02 	14 January	2020	bjalandoni			Remove driver, should only filter by contractors. add company name filter
 *                              ccutib              Changed Company Name id values to match text values
 * 1.03     16 January  2020  	ccutib              Add a contractor name field
 * 1.04     20 January  2020    ccutib              Corrected M/R status page parameters.
 * 1.05     17 June  	2020    lmarion             Removed company name.
 * 1.06     18 June  	2020    ccutib              Removes unused modules.
 * 1.07     20 July     2020    ccutib              Add start date.
 * 1.08     03 Aug      2020    ccutib              Make customer mandatory.
 * 1.07		16 February 2021   	jdgonzal			Add Posting Period
 */

/**
 * @NApiVersion 2.0
 * @NScriptType Suitelet
 * @NModuleScope SameAccount
 */
define(function(require) {
	// Native Modules
	var search = require('N/search');
	var task = require('N/task');
	var redirect = require('N/redirect');
	var serverWidget = require('N/ui/serverWidget');
	var format = require('N/format');
	var record = require('N/record');

	/* Custom Module */
	var NSUtil = require('SuiteScripts/NSUtilvSS2.js');
	NSUtil.SCRIPT_PARAMETER_NAMES = {
		custscript_sl_generate_bill_form_title : {
			optional : false,
			id : 'custscript_sl_generate_bill_form_title'
		},
		custscript_sl_generate_bill_button_title : {
			optional : false,
			id : 'custscript_sl_generate_bill_button_title'
		},
		scriptId : {
			optional : false,
			id : 'custscript_sl_generate_bill_scriptid'
		},
		deploymentId : {
			optional : false,
			id : 'custscript_sl_generate_bill_deployment'
		},
		companyNameSearchId : {
			optional : false,
			id : 'custscript_sl_generate_bill_companynames'
		},
		clientScriptPath : {
			optional : true,
			id : 'custscript_sl_generate_bill_client_scr'
		}
	};

	/**
	 * Definition of the Suitelet script trigger point.
	 *
	 * @param {Object} context
	 * @param {ServerRequest} context.request - Encapsulation of the incoming request
	 * @param {ServerResponse} context.response - Encapsulation of the Suitelet response
	 * @Since 2015.2
	 */
	function onRequest(context) {
		var stLogTitle = 'onRequest - Display Requisition';
		var request = context.request;
		var response = context.response;
		var SCRIPT_PARAMS = NSUtil.getScriptParameters();
		try {
			createPage(response, SCRIPT_PARAMS);
			if (request.method == 'POST') {
				getParametersAndCallMR(request, SCRIPT_PARAMS);
			}
		} catch (err) {
			log.error(stLogTitle, err.message);
			throw err;
		}
	}

	function getParametersAndCallMR(request, SCRIPT_PARAMS) {
		var stLogTitle = 'getParametersAndCallMR';
		try {
			var objRequestParams = {
				'custscript_ns_genbill_date_to' : request.parameters.custpage_to_date,
				'custscript_ns_genbill_trandate' : request.parameters.custpage_transaction_date,
				'custscript_ns_genbill_postingperiod' : request.parameters.custpage_posting_period
			};
			if (request.parameters.custpage_from_date) {
				objRequestParams.custscript_ns_genbill_date_from = request.parameters.custpage_from_date;
			}
			if (request.parameters.custpage_customer_id) {
				objRequestParams.custscript_ns_genbill_customer = request.parameters.custpage_customer_id;
			}
			if (request.parameters.custpage_driver_id) {
				objRequestParams.custscript_ns_genbill_driver = request.parameters.custpage_driver_id;
			}
			if (request.parameters.custpage_transaction_memo) {
				objRequestParams.custscript_ns_genbill_memo = request.parameters.custpage_transaction_memo;
			}
			/* validate parameters */
			var scriptTask = task.create({
				taskType : task.TaskType.MAP_REDUCE
			});
			scriptTask.scriptId = SCRIPT_PARAMS.scriptId;
			scriptTask.deploymentId = SCRIPT_PARAMS.deploymentId;
			scriptTask.params = objRequestParams;
			log.debug(stLogTitle, 'objRequestParams= ' + JSON.stringify(objRequestParams));
			var scriptTaskId = scriptTask.submit();
			log.audit(stLogTitle, 'M/R called with task id=' + scriptTaskId);
			var objRedirectOptions = {};
			objRedirectOptions.id = 'LIST_MAPREDUCESCRIPTSTATUS';
			var stToday = format.format({
				value : new Date(),
				type : format.Type.DATE
			});
			objRedirectOptions.parameters = {
				sortcol : 'dcreated',
				sortdir : 'DESC',
				date : 'TODAY',
				datefrom : stToday,
				dateto : stToday,
				scripttype : scriptTask.scriptId
			};
			if (scriptTask.deploymentId) {
				var objDeploymentSearch = search.create({
					type : search.Type.SCRIPT_DEPLOYMENT
				});
				var arrFilters = [];
				arrFilters.push([ 'scriptid', 'is', scriptTask.deploymentId ]);
				objDeploymentSearch.filterExpression = arrFilters;
				var arrDeploymentSearchResults = objDeploymentSearch.run().getRange(0, 1);
				if (arrDeploymentSearchResults && arrDeploymentSearchResults.length) {
					objRedirectOptions.parameters.primarykey = arrDeploymentSearchResults[0].id;
				}
			}
			redirect.toTaskLink(objRedirectOptions);
			log.debug(stLogTitle, 'Redirect params= ' + JSON.stringify(objRedirectOptions));
		} catch (err) {
			log.error(stLogTitle, err.message);
			throw err;
		}
	}
	function createPage(response, SCRIPT_PARAMS) {
		var stLogTitle = 'onRequest - Generate Bill';
		try {
			var stFormTitle = SCRIPT_PARAMS.custscript_sl_generate_bill_form_title;

			var objForm = serverWidget.createForm({
				title : stFormTitle
			});
			if (SCRIPT_PARAMS.clientScriptPath) {
				objForm.clientScriptModulePath = SCRIPT_PARAMS.clientScriptPath;
			}

			var objFilterParams = objForm.addFieldGroup({
				id : 'custpage_main_tab',
				label : "Filter Parameters"
			});

			var dtFrom = objForm.addField({
				id : 'custpage_from_date',
				type : serverWidget.FieldType.DATE,
				label : 'From',
				container : 'custpage_main_tab'
			});
			dtFrom.isMandatory = true;

			var dtTo = objForm.addField({
				id : 'custpage_to_date',
				type : serverWidget.FieldType.DATE,
				label : 'To',
				container : 'custpage_main_tab'
			});
			dtTo.isMandatory = true;

			var stDriver = objForm.addField({
				id : 'custpage_driver_id',
				type : serverWidget.FieldType.SELECT,
				label : 'Contractor',
				source : search.Type.VENDOR,
				container : 'custpage_main_tab'
			});

			var objTransaction = objForm.addFieldGroup({
				id : 'custpage_transaction_data_tab',
				label : "Transaction Data"
			});

			var stCustomer = objForm.addField({
				id : 'custpage_customer_id',
				type : serverWidget.FieldType.SELECT,
				label : 'Customer',
				source : search.Type.CUSTOMER,
				container : 'custpage_main_tab'
			});
			stCustomer.isMandatory = true;

			var dtDate = objForm.addField({
				id : 'custpage_transaction_date',
				type : serverWidget.FieldType.DATE,
				label : 'Date',
				container : 'custpage_transaction_data_tab'
			});
			dtDate.isMandatory = true;
			dtDate.defaultValue = new Date();
			var dtMemo = objForm.addField({
				id : 'custpage_transaction_memo',
				type : serverWidget.FieldType.TEXT,
				label : 'Memo',
				container : 'custpage_transaction_data_tab'
			});

			// JLG - ADDED 02/13/2021

			var fldPostingPeriod = objForm.addField({
				id : 'custpage_posting_period',
				type : serverWidget.FieldType.SELECT,
				label : 'Posting Period',
				container : 'custpage_transaction_data_tab'
			});
			
			fldPostingPeriod.isMandatory = true;

			var postOptions = getPostingPeriod();

			fldPostingPeriod.addSelectOption({
				text: '',
				value: ''
			});

			for(var i = 0; i < postOptions.length; i++) {
				fldPostingPeriod.addSelectOption({
					text: postOptions[i].text,
					value: postOptions[i].value
				});	
			}

			var stButtonTitle = SCRIPT_PARAMS.custscript_sl_generate_bill_button_title;
			objForm.addSubmitButton({
				label : stButtonTitle
			});

			response.writePage(objForm);
		} catch (err) {
			log.error(stLogTitle, err.message);
			throw err;
		}
	}

	
	// JLG - ADDED 02/16/2021
	function getPostingPeriod() {
		var getPost = record.create({
			type: record.Type.VENDOR_BILL,
			isDynamic: true,
			defaultValues: {
				entity: 60438
			}
		});

		var postField = getPost.getField({ fieldId: 'postingperiod' });
		var postOptions = postField.getSelectOptions({ filter: '', operator: 'contains' });

		return postOptions;
	}


	return {
		onRequest : onRequest
	};

});