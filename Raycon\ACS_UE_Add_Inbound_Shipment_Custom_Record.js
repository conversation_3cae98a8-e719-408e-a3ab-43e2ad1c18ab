/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search'], function(record, search) {

    function checkForDuplicates(externalDoc){
        var custRecSearch = search.load({
            id: 'customsearch_acs_inb_ship_ext_doc_ss'
        });
        
        var extDocFilter = search.createFilter({
            name: 'name',
            operator: search.Operator.CONTAINS,
            values: [externalDoc]
        });

        custRecSearch.filters.push(extDocFilter);

        var resultSet = custRecSearch.run();
        var ctr = 0;
        resultSet.each(function(result){
            ctr++;
            return true;
        });

        if(ctr){
            return false;
        } else {
            return true;
        }
    }

    function afterSubmit(context) {
        var recObj = context.newRecord;
        var inboundShipmentID = recObj.id;
        var externalDocNo = recObj.getValue({ fieldId: 'externaldocumentnumber' });

        if(checkForDuplicates(externalDocNo)){
            var newRecObj = record.create({
                type: 'customrecord_inb_shipment_external_doc_n',
                isDynamic: true
            });

            newRecObj.setValue({ fieldId: 'name', value: externalDocNo });
            newRecObj.setValue({ fieldId: 'custrecord_inbound_shipment', value: inboundShipmentID });

            try {
                var savedId =  newRecObj.save();
                log.debug("Success", "Successfully saved custom record with ID: " + savedId);
            } catch (e) {
                log.debug("Error", e);
            }
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
