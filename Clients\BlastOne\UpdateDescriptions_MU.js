function massUpdate(recType, recId) {

	try {			
        var record = nlapiLoadRecord(recType, recId);
        
        // AU
        var shortDescription = record.getFieldValue('custitemau_short_description');

        if (!shortDescription) {            
            shortDescription = record.getFieldValue('storedescription');
        }
        record.setFieldValue('custitemcustitemnz_short_description', shortDescription);
        record.setFieldValue('custitemcustitemmy_short_description', shortDescription);
    
        var longDescription = record.getFieldValue('custitemau_long_description');

        if (!longDescription) {
            longDescription = record.getFieldValue('storedetaileddescription');            
        }
        record.setFieldValue('custitemcustitemnz_long_description', longDescription);        
        record.setFieldValue('custitemcustitemmy_long_description', longDescription);

        nlapiSubmitRecord(record, true, true);
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e) + ' ' + e.stack);
	}
}
