/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/record', 'N/ui/dialog', 'N/search'], function(record, dialog, search) {
    var validRecords = [record.Type.VENDOR_BILL, record.Type.JOURNAL_ENTRY];
    var sublists = ['expense', 'item', 'line']
    function pageInit(context) {
        
    }

    function validateLine(context) {
        log.debug('context', {recType: context.currentRecord.type, sublistId: context.sublistId});

        if(validRecords.indexOf(context.currentRecord.type) !== -1){
            if(sublists.indexOf(context.sublistId) !== -1){
                var currRecObj = context.currentRecord;
                var sublistId = context.sublistId;
                var _class = currRecObj.getCurrentSublistValue({ fieldId: 'class', sublistId: sublistId });
                var _dept = currRecObj.getCurrentSublistValue({ fieldId: 'department', sublistId: sublistId });
                var _account = currRecObj.getCurrentSublistValue({ fieldId: 'account', sublistId: sublistId });
            
                var acctType = search.lookupFields({
                    type: search.Type.ACCOUNT,
                    id: _account,
                    columns: ['type']
                });

                if(acctType.hasOwnProperty('type')){
                    if(acctType.type[0].value == 'Expense' || acctType.type[0].value == 'Income'){
                        log.debug('acct', acctType);

                        if(_class == 5 && !_dept) {

                            dialog.alert({
                                title: 'Warning!',
                                message: '<b>Cost Center</b> cannot be blank if <b>Business Unit</b> is <b>Management Company</b>'
                            });

                            return false;

                        } else if(!_class) {
                            
                            dialog.alert({
                                title: 'Warning!',
                                message: '<b>Business Unit</b> is mandatory'
                            });

                            return false;
                        }
                    }
                }
            }
        }

        return true;

    }

    return {
        pageInit: pageInit,
        validateLine: validateLine
    }
});
