/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
 define(['N/record', 'N/format/i18n'], function(record, format) {

    function onRequest(context) {
        try {
            var invoiceId = context.request.parameters.invid;

            var invObj = record.load({
                type: record.Type.INVOICE,
                id: invoiceId
            });

            var curFormatter = format.getCurrencyFormatter({ currency: invObj.getText({ fieldId: 'currencysymbol' }) });
            var curSymbol = curFormatter.symbol;

            var itemCount = invObj.getLineCount({
                sublistId: 'item'
            });

            var items = [];
            var costCenter = '';
            var currentCostCenter = '';
            var currentConfName = '';
            for(var i = 0; i < itemCount; i++){

                // get cost center and incomm charge for validation of each line
                var costCenter = invObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_cost_center', line: i });
                var confName = invObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_conf_call_name', line: i });
                var contactName = invObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_inv_contact_name', line: i });
                var incommCharge = invObj.getSublistText({ sublistId: 'item', fieldId: 'item', line: i });
                var incommChargeId = invObj.getSublistValue({ sublistId: 'item', fieldId: 'item', line: i });
                var description = invObj.getSublistText({ sublistId: 'item', fieldId: 'description', line: i });

                // if cost incommCharge is blank, skip the line
                if(incommCharge == 0) continue;

                if(confName != '' && confName != currentConfName){
                    if(i > 0) {
                        confDetail.conference_total = curFormatter.format({ number: confDetail.conference_total });
                        costCenterDetails.cost_center_items.push(confDetail);
                    }

                    currentConfName = confName;
                    var confDetail = {
                        conference_name: currentConfName,
                        contact_name: contactName,
                        conference_total: 0.00,
                        conference_details: []
                    }

                }

                // if cost center is not blank and is not the same as current cost center
                if(costCenter != '' && costCenter != currentCostCenter){

                    if(i > 0){
                        costCenterDetails.cost_center_total = curFormatter.format({ number: costCenterDetails.cost_center_total });
                        items.push(costCenterDetails);
                    }

                    currentCostCenter = costCenter;
                    // create new costCenterDetails object
                    var costCenterDetails = {
                        cost_center:         currentCostCenter,
                        cost_center_total:   0.00,
                        single_total: 0.00,
                        cost_center_items: []
                    }
                }

                // if costCenter and confName is not blank and description is blank, this is start of group, skip the line
                if(costCenter != '' && confName != '' && description == '' && incommChargeId != -2) continue;

                // get the amount for totaling per cost center
                var total = parseFloat(invObj.getSublistValue({ sublistId: 'item', fieldId: 'amount', line: i }));

                // get conference call name
                var confCallName = invObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_inv_conf_call_name', line: i});

                if(confCallName === '') {
                    confCallName = invObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_inv_conference_charges', line: i});
                }

                var confCharge = invObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_inv_conference_charges', line: i});

                // create the detail object then push to oostCenterDetails.cost_center_items
                confDetail.conference_details.push({
                    charge: incommCharge,
                    conf_call_name: confCallName,
                    conf_charge: confCharge,
                    lines: invObj.getSublistText({ sublistId: 'item', fieldId: 'custcol11', line: i }),
                    minutes: invObj.getSublistValue({ sublistId: 'item', fieldId: 'quantity', line: i }),
                    call_charge: invObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_call_charge', line: i }),
                    service_charge: invObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_service_charge', line: i }),
                    conference_charge: invObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_service_charge', line: i }),
                    total_call_cost: curFormatter.format({ number: total })
                });

                // confDetail.conference_total += total;
                if(incommCharge == 'Subtotal' || incommChargeId == -2)
                    costCenterDetails.cost_center_total += total;

                costCenterDetails.single_total += total;


            }
            
            // log.debug('cost_center_total', costCenterDetails.cost_center_total);
            confDetail.conference_total = curFormatter.format({ number: confDetail.conference_total });
            costCenterDetails.cost_center_total = curFormatter.format({ number: costCenterDetails.cost_center_total });
            costCenterDetails.cost_center_items.push(confDetail);
            items.push(costCenterDetails);

            log.debug('test', { rec_items: items });

            var prep_data = {
                record_items: items,
                curr_symbol: curSymbol
            }

            var returnStr = "<#assign prep_data =" + JSON.stringify(prep_data) + " />"; 

            context.response.write({
                output: returnStr
            });

        } catch (err) {
            log.debug("Error", err);
        }
    }

    return {
        onRequest: onRequest
    }
});
