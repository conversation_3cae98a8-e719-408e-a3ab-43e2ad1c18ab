/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/ui/serverWidget', 'N/search', 'N/record'], function(https, serverWidget, search, record) {

    function onRequest(context) {
        if(context.request.method == 'GET'){
        // logic for GET
            var getForm = serverWidget.createForm({
                title: 'Item Fullfillment'
            });

            // SO field
            var soField = getForm.addField({
                id: 'custpage_sales_order',
                label: 'Pending Sales Order',
                type: serverWidget.FieldType.SELECT
            });
            soField.isMandatory = true;

            // load search for select field
            var searchObj = search.load({
                id: 24,
                type: search.Type.SALES_ORDER
            });
        
            var myResultSet = searchObj.run();
            
            myResultSet.each(function(result){
                var id = result.id;
                var internalid = result.getText({ name: 'internalid', summary: search.Summary.GROUP });
                var tranid = result.getValue({ name: 'tranid', summary: search.Summary.GROUP });
                
                soField.addSelectOption({
                    value: internalid,
                    text: 'Sales Order #' + tranid 
                });
        
                return true
            });

            // submit button
            getForm.addSubmitButton({
                label: 'Fulfill'
            });

            context.response.writePage({ pageObject: getForm });


        } else {
        // logic for POST
            var requestParams = context.request.parameters;
            var salesOrderId = requestParams.custpage_sales_order;

            var postForm = serverWidget.createForm({
                title: 'Sales Order has been fulfilled!'
            });

            // transform sales order
            var itemFfObj = record.transform({
                fromType: record.Type.SALES_ORDER,
                fromId: salesOrderId,
                toType: record.Type.ITEM_FULFILLMENT
            });

            itemFfObj.setValue({
                fieldId: 'shipstatus',
                value: 'C'
            });

            // create item fulfillment field
            var itemFfField = postForm.addField({
                id: 'custpage_item_fulfilled',
                label: 'Item Fulfilled',
                type: serverWidget.FieldType.SELECT,
                source: 'itemfulfillment'
            });

            itemFfField.defaultValue = itemFfObj.save();

            itemFfField.updateDisplayType({
                displayType : serverWidget.FieldDisplayType.INLINE
            });

            context.response.writePage({
                pageObject: postForm
            });

        }
    }

    return {
        onRequest: onRequest
    }
});
