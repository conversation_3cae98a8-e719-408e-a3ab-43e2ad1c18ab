/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
 define(['N/https', 'N/record', 'N/search', 'N/file', 'N/format'], function(https, record, search, file, format) {


    function sortComponents (a, b) {
        var itemNameA = a.item_name.toUpperCase();
        var itemNameB = b.item_name.toUpperCase();
        if (itemNameA < itemNameB) {
            return -1;
        }
        if (itemNameA > itemNameB) {
            return 1;
        }
    
        // names must be equal
        return 0;
    }

    function onRequest(context) {
        if(context.request.method == https.Method.GET){
            requestParams = context.request.parameters;
            if(requestParams.hasOwnProperty('item_id') && requestParams.hasOwnProperty('is_simple')) {
                itemId = parseInt(requestParams.item_id);
                isSimple = requestParams.is_simple == 'true';
            
                var assmbItemObj = record.load({
                    type: record.Type.ASSEMBLY_ITEM,
                    id: itemId,
                    isDynamic: true
                });
                
                var fileName = assmbItemObj.getValue({ fieldId: 'itemid' });
                var purchDesc = assmbItemObj.getValue({ fieldId: 'purchasedescription' });
                purchDesc = purchDesc.replace(',', ' ');

                var csvFile = file.create({
                    name: fileName + '.csv',
                    contents: 'Purchase Description,'+purchDesc+'\n',
                    fileType: 'CSV'
                });
                
                csvFile.appendLine({
                    value: 'Item #,' + fileName
                }); 
                
                // BUILD THE ASSEMBLY ITEM'S COMPONENT DETAILS
                var assemblyItemComponentArr = buildAssemblyItemComponentDetails(assmbItemObj, isSimple, 0);

                assemblyItemComponentArr.sort(sortComponents);

                csvFile.appendLine({
                    value: 'Item Name/Num,Level,Description,Type,Cost,Qty,Total'
                }); 
                
                csvFile = buildCSVData(csvFile, assemblyItemComponentArr);

                // GET THE TOTAL AMOUNT OF ALL THE ASSEMBLY COMPONENTS
                var componentsTotal = 0.00;
                var qtyTotal = 0;
                for(var i = 0; i < assemblyItemComponentArr.length; i++) {
                    componentsTotal += assemblyItemComponentArr[i].item_total;
                    qtyTotal += assemblyItemComponentArr[i].item_qty;
                }

                // componentsTotal = format.format({
                //     value: componentsTotal,
                //     type: format.Type.FLOAT
                // })

                
                csvFile.appendLine({
                    value: ',,Total Bill of Materials,,,' + qtyTotal + ',' + componentsTotal
                }); 

                context.response.writeFile({
                    file: csvFile,
                    isInline: false
                })

            } else {
                context.response.write({
                    output: 'No item_id'
                });
            }
        }
    }

    function buildCSVData(csvFile, assmbArray) {

        for(var i = 0; i < assmbArray.length; i++) {

            var valueArr = [
                assmbArray[i].item_name,
                assmbArray[i].item_level,
                assmbArray[i].item_desc,
                assmbArray[i].item_type,
                assmbArray[i].item_cost,
                assmbArray[i].item_qty,
                assmbArray[i].item_total,
            ];
            
            csvFile.appendLine({
                value: valueArr.join(',')
            }); 

            if(assmbArray[i].item_components.length) {
                csvFile = buildCSVData(csvFile, assmbArray[i].item_components);
            }

        }

        return csvFile;
    }

    function buildAssemblyItemComponentDetails(assmbItemObj, isSimple, level) {
        var componentLength = assmbItemObj.getLineCount({ sublistId: 'member' });
        var componentsArr = [];
        level += 1;

        // loop through all the components
        for(var i = 0; i < componentLength; i++) {
            
            var componentItemType = assmbItemObj.getSublistValue({ sublistId: 'member', fieldId: 'sitemtype', line: i });
            var componentItemID = assmbItemObj.getSublistValue({ sublistId: 'member', fieldId: 'item', line: i });
            var componentItemDesc = assmbItemObj.getSublistValue({ sublistId: 'member', fieldId: 'memberdescr', line: i });
            var componentItemQty = assmbItemObj.getSublistValue({ sublistId: 'member', fieldId: 'quantity', line: i });
                                
            var compLookUp = search.lookupFields({
                type: search.Type.ITEM,
                id: componentItemID,
                columns: ['cost', 'itemid', 'purchasedescription', 'displayname']
            });
            
            var componentItemDesc = compLookUp.purchasedescription.replace(/[,\n\r]/g, '');


            var componentObj = {
                item_name: compLookUp.itemid,
                item_level: level,
                item_desc: componentItemDesc,
                item_type: componentItemType,
                item_cost: 0.00,
                item_qty: Number(componentItemQty),
                item_total: 0.00
            }
            
            // if component is an assembly item, call self
            if(componentItemType == 'Assembly') {

                var componentAssmbItemObj = record.load({
                    type: record.Type.ASSEMBLY_ITEM,
                    id: componentItemID,
                    isDynamic: true
                });

                var assemblyComponents = buildAssemblyItemComponentDetails(componentAssmbItemObj, isSimple, level);
                assemblyComponents.sort(sortComponents);
                var totalCostAllComponent = 0.00;
                assemblyComponents.forEach(function(element) {
                    totalCostAllComponent += element.item_total;
                });

                componentObj.item_cost = totalCostAllComponent;
                componentObj.item_total = componentObj.item_qty * totalCostAllComponent;

                if(isSimple) {
                    componentObj["item_components"] = [];
                } else {
                    componentObj["item_components"] = assemblyComponents;
                }

            } else {
                
                componentObj.item_cost = Number(compLookUp.cost);
                componentObj.item_total = componentObj.item_qty * componentObj.item_cost;
                componentObj["item_components"] = [];

            }

            componentsArr.push(componentObj);

        }

        return componentsArr;

    }

    return {
        onRequest: onRequest
    }
});
