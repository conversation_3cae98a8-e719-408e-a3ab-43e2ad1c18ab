/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
 define(['N/ui/serverWidget','N/runtime', 'N/search', 'N/query', 'N/redirect', 'N/record'], function(serverWidget, runtime, search, query, redirect, record) {

    function getAccounts() {

        // this is reusing the checks to be printed search and grouping the accounts instead
        var paymentSearch = search.load({
            id: 'customsearch_checks_to_be_printed'
        });

        var groupedAccounts = search.createColumn({
            name: "account",
            summary: "GROUP",
            sort: search.Sort.DESC,
            label: "Account"
        });

        paymentSearch.columns = [groupedAccounts];

        var accounts = [];
        paymentSearch.run().each(function(result){
            var column = {
                name: "account",
                summary: "GROUP",
                label: "Account"
            }

            accounts.push({
                text: result.getText(column),
                value: result.getValue(column)
            });

            return true;
        });

        return accounts;
    }

    function getLastCheckNumber(account) {
        
        var acctRecord = record.load({
            type: record.Type.ACCOUNT,
            id: account,
            isDynamic: false
        });
        
        var currentCheckNo = acctRecord.getValue({ fieldId: 'curdocnum' });

        return String(currentCheckNo).replace(".0", "");

    }

    function getPayments(account) {
        // this is reusing the checks to be printed search and grouping the accounts instead
        var paymentSearch = search.load({
            id: 'customsearch_checks_to_be_printed'
        });
        
        paymentSearch.filters.push(search.createFilter({
            name: 'account',
            operator: search.Operator.ANYOF,
            values: [account]
        }));
        var payments = [];
        paymentSearch.run().each(function(result){
            
            payments.push({
                internalid: result.getValue({ name: "internalid" }),
                trandate: result.getValue({ name: "trandate" }),
                type: result.getValue({ name: "type" }),
                transactionnumber: result.getValue({ name: "transactionnumber" }),
                entity: result.getText({ name: "entity" }),
                currency: result.getText({ name: "currency" }),
                amount: result.getValue({ name: "amount" }),
            });

            return true;
        });

        return payments;
    }

    function renderFilters(form, selected) {

        form.addFieldGroup({
            id: 'filters',
            label: 'Filters'
        })

        // add account fields
        var objAccounts = form.addField({
            id: 'custpage_accounts',
            type: serverWidget.FieldType.SELECT,
            label: 'Accounts',
            container: 'filters'
        });

        var accounts = getAccounts();

        objAccounts.addSelectOption({value: '', text: '' });

        accounts.forEach(function (account) {
            var options = { value: account.value, text: account.text }
            if(selected == account.value) options.isSelected = true;
            objAccounts.addSelectOption(options);
            
        });

    }

    function renderAdditionalFields(form, account) {
        

        form.addFieldGroup({
            id: 'checkNos',
            label: 'Check Numbers'
        })

        // add first check no field
        var objFirstCheckNo = form.addField({
            id: 'custpage_first_check_no',
            type: serverWidget.FieldType.TEXT,
            label: 'First Check Number',
            container: 'checkNos'
        });

        objFirstCheckNo.defaultValue = getLastCheckNumber(account);

        // objFirstCheckNo.isMandatory = true;
        
        // add check number field
        var objCheckNo = form.addField({
            id: 'custpage_check_no',
            type: serverWidget.FieldType.TEXT,
            label: 'Check Number',
            container: 'checkNos'
        }).updateDisplayType({
            displayType : serverWidget.FieldDisplayType.DISABLED
        });

        // add check type field
        var objCheckType = form.addField({
            id: 'custpage_check_type',
            type: serverWidget.FieldType.SELECT,
            label: 'Check Type',
            container: 'filters'
        });


        objCheckType.addSelectOption({value: 1, text: 'Standard' });
    }
    
    function renderSummaryFields(form) {

        form.addFieldGroup({
            id: 'summaryGroup',
            label: 'Summary'
        })

        // add batch count
        form.addField({
            id: 'custpage_batch_count',
            type: serverWidget.FieldType.INTEGER,
            label: 'Batch Count',
            container: 'summaryGroup'
        }).updateDisplayType({
            displayType : serverWidget.FieldDisplayType.INLINE
        });

        
        // add batch amount
        form.addField({
            id: 'custpage_batch_amount',
            type: serverWidget.FieldType.CURRENCY,
            label: 'Batch Amount',
            container: 'summaryGroup'
        }).updateDisplayType({
            displayType : serverWidget.FieldDisplayType.INLINE
        });

    }

    function onRequest(context) {

        if(context.request.method == 'GET'){

            var form = serverWidget.createForm({
                title: 'Print Checks'
            });
            
            renderFilters(form);
            
            form.addSubmitButton({
                label : 'Submit'
            });

            context.response.writePage(form);
            
        } else {

            var account = context.request.parameters.custpage_accounts;

            try {
                var form = serverWidget.createForm({
                    title: 'Print Checks'
                });
                form.clientScriptModulePath = 'SuiteScripts/ACS_CS_Print_Check_Funcs.js';
                
                renderFilters(form, account);
                renderAdditionalFields(form, account);
                renderSummaryFields(form);

                var paymentSublist = form.addSublist({
                    id : 'custpage_payment_sublist',
                    type : serverWidget.SublistType.LIST,
                    label : 'Payments'
                });

                paymentSublist.addField({
                    id: 'print',
                    label: 'Print',
                    type: serverWidget.FieldType.CHECKBOX
                });
                
                paymentSublist.addField({
                    id: 'tran_type',
                    label: 'transaction type',
                    type: serverWidget.FieldType.TEXT
                }).updateDisplayType({
                    displayType : serverWidget.FieldDisplayType.HIDDEN
                });

                paymentSublist.addField({
                    id: 'payment_id',
                    label: 'payment id',
                    type: serverWidget.FieldType.TEXT
                }).updateDisplayType({
                    displayType : serverWidget.FieldDisplayType.HIDDEN
                });

                paymentSublist.addField({
                    id: 'date',
                    type: serverWidget.FieldType.TEXT,
                    label: 'Date'
                }).updateDisplayType({
                    displayType : serverWidget.FieldDisplayType.INLINE
                });

                paymentSublist.addField({
                    id: 'tranno',
                    type: serverWidget.FieldType.TEXT,
                    label: 'Transaction Number'
                }).updateDisplayType({
                    displayType : serverWidget.FieldDisplayType.INLINE
                });

                paymentSublist.addField({
                    id: 'entity',
                    type: serverWidget.FieldType.TEXT,
                    label: 'Payee'
                }).updateDisplayType({
                    displayType : serverWidget.FieldDisplayType.INLINE
                });

                paymentSublist.addField({
                    id: 'currency',
                    type: serverWidget.FieldType.TEXT,
                    label: 'Currency'
                }).updateDisplayType({
                    displayType : serverWidget.FieldDisplayType.INLINE
                });

                paymentSublist.addField({
                    id: 'amount',
                    type: serverWidget.FieldType.CURRENCY,
                    label: 'Amount'
                }).updateDisplayType({
                    displayType : serverWidget.FieldDisplayType.INLINE
                });

                
                paymentSublist.addButton({
                    id: 'custpage_mark_all',
                    label : 'Mark All',
                    functionName: 'markAll(true)'
                });

                
                paymentSublist.addButton({
                    id: 'custpage_unmark_all',
                    label : 'Unmark All',
                    functionName: 'markAll(false)'
                });
                
                
                var payments = getPayments(account);

                for(var i = 0; i < payments.length; i++){
                    
                    paymentSublist.setSublistValue({ 
                        id: 'payment_id',
                        line: i,
                        value: payments[i].internalid
                    });

                    paymentSublist.setSublistValue({ 
                        id: 'tran_type',
                        line: i,
                        value: payments[i].type
                    });
                    
                    paymentSublist.setSublistValue({ 
                        id: 'date',
                        line: i,
                        value: payments[i].trandate
                    });

                    paymentSublist.setSublistValue({ 
                        id: 'tranno',
                        line: i,
                        value: payments[i].transactionnumber
                    });

                    paymentSublist.setSublistValue({ 
                        id: 'entity',
                        line: i,
                        value: payments[i].entity
                    });

                    paymentSublist.setSublistValue({ 
                        id: 'currency',
                        line: i,
                        value: payments[i].currency
                    });

                    paymentSublist.setSublistValue({ 
                        id: 'amount',
                        line: i,
                        value: payments[i].amount
                    });
                }
                
                form.addSubmitButton({
                    label : 'Submit'
                });

                form.addButton({
                    id: 'custpage_print_button',
                    label : 'Print',
                    functionName: 'print()'
                });

                context.response.writePage(form);
            } catch (e) {
                log.error('error', e);
                redirect.toSuitelet({
                    scriptId: "customscript_acs_sl_print_checks",
                    deploymentId: "customdeploy_acs_sl_print_checks"
                });
            }
        }
    }

    return {
        onRequest: onRequest
    }
});
