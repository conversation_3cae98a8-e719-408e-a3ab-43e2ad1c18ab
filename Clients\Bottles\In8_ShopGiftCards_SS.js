function scheduled(type) {

	try {
		var items = getList();

        for (var i = 0; i < items.length; i++) {

            nlapiLogExecution('DEBUG', 'Debug', 'Item: ' + i);

            try {
                syncGiftCard(items[i]);
            } catch(e) {
                nlapiLogExecution('DEBUG', 'Debug', 'Error: ' + e);
            }

            if (nlapiGetContext().getRemainingUsage() < 500) {

                nlapiLogExecution('DEBUG', 'Debug', 'Yield script');
                nlapiYieldScript();
            }
        }
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
	}
}

function syncGiftCard(item) {

    var url = 'https://bottles-fine-wine-llc.myshopify.com/admin/api/2021-10/gift_cards.json'

    // https://shopify.dev/api/admin-rest/2021-10/resources/gift-card#[post]/admin/api/2021-10/gift_cards.json

    var obj = {"gift_card":{"note":"","initial_value":item.value,"code":item.code}};

    // Update the record id. Send to WC
    var jsonText = JSON.stringify(obj);

    var settingsReq = {};

    settingsReq.headers = {};
    settingsReq.headers['Content-Type'] = 'application/json';
    settingsReq.headers['X-Shopify-Access-Token'] = '';

    nlapiLogExecution('DEBUG', 'Shopify', 'URL: ' + url);

    nlapiLogExecution('DEBUG', 'Shopify', 'JSON: ' + jsonText);

    // Call the Products API
    var resp = nlapiRequestURL(url, jsonText, settingsReq.headers, 'POST');

    nlapiLogExecution('DEBUG', 'Shopify', 'Resp: ' + resp.getBody());

    if (resp.getCode() == 200 || resp.getCode() == 201) {

        var body = resp.getBody();

        //nlapiLogExecution('DEBUG', 'Shopify', 'Resp: ' + resp.getBody());

        body = JSON.parse(body);
    }
}

function getList() {

    var list = [];

    //list.push({ value: 0.79, code: '575542134'});
    //list.push({ value: 2.28, code: '575542226'});
    //list.push({ value: 50, code: '575544732'});
    //list.push({ value: 1000, code: '5755508'});
    list.push({ value: 25, code: '7552179'});

    return list;
}