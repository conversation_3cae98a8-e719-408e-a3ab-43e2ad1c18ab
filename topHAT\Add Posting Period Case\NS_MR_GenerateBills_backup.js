/**
 * Copyright (c) 1998-2020 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of NetSuite, Inc. ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with NetSuite.
 */

/**
 * Developed for TopHAT Logistical Solutions, LLC
 * Create Bills off daily route records
 * 
 * Version  Date         Author              Remarks
 * 1.00     08 Jan 2020  ccutib              Initial version.
 * 1.01     14 Jan 2020  ccutib              Create bills for contractors.
 * 1.02     20 Jan 2020  ccutib              Corrected look-up and search result getters.
 * 1.03     10 Feb 2020  ccutib              Itemize by Drivers
 * 1.04     23 Apr 2020  ccutib              Bugs fixed for inclusion of script 3
 * 1.05     15 Jun 2020  ccutib              Resolve circular vendor-master-vendor references
 * 1.06     17 Jun 2020  ccutib              Refer to master contractor saved on the DR instead of looking it up from vendor
 * 1.06     17 Jun 2020  lmarion             Remove companyname parameter. Remove routine that loaded all drivers. Filter by a Master Contractor.
 * 1.07     30 Jul 2020  ccutib              Line > Billable set to no
 *
 */

/**
 * @NApiVersion 2.x
 * @NScriptType MapReduceScript
 * @NModuleScope SameAccount
 */
define([ 'N/runtime', 'N/error', 'N/search', 'N/record', 'N/format' ],
/**
 * @param {runtime} NSRuntime
 * @param {error} NSError
 * @param {search} NSSearch
 * @param {record} NSRecord
 * @param {format} NSFormat
 */
function(NSRuntime, NSError, NSSearch, NSRecord, NSFormat) {
	var _objCurrentScript = NSRuntime.getCurrentScript();
	var _stDeploymentId = _objCurrentScript.deploymentId;
	var _CONSTANTS = {};
	_CONSTANTS.ApprovalStatus = {
		PENDING_APPROVAL : '1',
		APPROVED : '2'
	};
	_CONSTANTS.defaultApprovalStatus = _CONSTANTS.ApprovalStatus.APPROVED;
	var libUtilities = {};
	libUtilities.SCRIPT_PARAMETER_NAMES = {
		inputSearchDailyRoutes : {
			id : 'custscript_ns_genbill_input_search'
		},
		dateFrom : {
			id : 'custscript_ns_genbill_date_from',
			optional : true
		},
		dateTo : {
			id : 'custscript_ns_genbill_date_to',
		},
		customer : {
			optional : true,
			id : 'custscript_ns_genbill_customer'
		},
		vendor : {
			optional : true,
			id : 'custscript_ns_genbill_driver'
		},
		memo : {
			id : 'custscript_ns_genbill_memo'
		},
		defaultLineMemo : {
			id : 'custscript_ns_genbill_line_memo',
			optional : true
		},
		transactionDate : {
			id : 'custscript_ns_genbill_trandate'
		},
		drStatusPaid : {
			id : 'custscript_ns_genbill_dr_status'
		}
	};
	libUtilities.parametersMap = null;
	libUtilities.getScriptParameters = function() {
		if (this.parametersMap) {
			return this.parametersMap;
		}
		this.parametersMap = {};
		var parametersMap = this.parametersMap;
		var scriptContext = _objCurrentScript;
		var stLogTitle = _stDeploymentId + '.getScriptParameters';
		var obj;
		var value;
		var optional = false;
		var id;
		var arrMissingParams = [];
		log.debug(stLogTitle, 'Parameter ids:' + JSON.stringify(this.SCRIPT_PARAMETER_NAMES));
		for ( var key in this.SCRIPT_PARAMETER_NAMES) {
			if (this.SCRIPT_PARAMETER_NAMES.hasOwnProperty(key)) {
				obj = this.SCRIPT_PARAMETER_NAMES[key];
				id = obj.id;
				if (typeof obj === 'string') {
					value = scriptContext.getParameter(obj);
				} else {
					optional = obj.optional;
					value = scriptContext.getParameter(id);
				}
				if (value || value === false || value === 0) {
					parametersMap[key] = value;
				} else if (!optional) {
					arrMissingParams.push(key + '[' + id + ']');
				}
			}
		}
		if (arrMissingParams && arrMissingParams.length) {
			var objError = {};
			objError.name = 'Missing Script Parameter Values';
			objError.message = 'The following script parameters are empty: ' + arrMissingParams.join(', ');
			objError = NSError.create(objError);
			for ( var key in parametersMap) {
				if (parametersMap.hasOwnProperty(key)) {
					objError[key] = parametersMap[key];
				}
			}
			throw objError;
		}
		log.audit(stLogTitle, parametersMap);
		return parametersMap;
	};
	libUtilities.getAllResults = function(objSearch, maxResults) {
		var objResultSet = objSearch.runPaged({
			pageSize : 1000
		});
		var arrReturnSearchResults = [];
		var j = objResultSet.pageRanges.length;
		if (maxResults) {
			j = Math.min(Math.ceil(maxResults / 1000), j);
		}
		for (var i = 0; i < j; i++) {
			var objResultSlice = objResultSet.fetch({
				index : objResultSet.pageRanges[i].index
			});
			arrReturnSearchResults = arrReturnSearchResults.concat(objResultSlice.data);
		}
		if (maxResults) {
			arrReturnSearchResults = arrReturnSearchResults.slice(0, maxResults);
		}
		return arrReturnSearchResults;
	};
	libUtilities.getResultValue = function(objSearchResult, name, join, summary, func) {
		var stValue = null;
		if (objSearchResult && name) {
			if (typeof objSearchResult.getValue === 'function') {
				var options = name;
				if (typeof name === 'string') {
					options = {
						name : name
					};
					if (join) {
						options.join = join;
					}
					if (summary) {
						options.summary = summary;
					}
					if (func) {
						options.func = func;
					}
				}
				stValue = objSearchResult.getValue(options);
			} else {
				if (join) {
					name = name + '.' + join;
				}
				if (objSearchResult.hasOwnProperty('values') || objSearchResult.hasOwnProperty(name)) {
					var objValue = null;
					if (objSearchResult.hasOwnProperty('values')) {
						objValue = objSearchResult.values[name];
					} else {
						objValue = objSearchResult[name];
					}
					stValue = objValue;
					if (objValue && objValue.constructor === Array) {
						stValue = null;
						if (objValue.length) {
							objValue = objValue[0];
						}
					}
					if (objValue && objValue.value) {
						stValue = objValue.value;
					}
				}
			}
		}
		return stValue;
	};
	libUtilities.getResultText = function(objSearchResult, name, join, summary) {
		var stText = null;
		if (objSearchResult && name) {
			if (typeof objSearchResult.getText === 'function') {
				var options = name;
				if (typeof name === 'string') {
					options = {
						name : name
					};
					if (join) {
						options.join = join;
					}
					if (summary) {
						options.summary = summary;
					}
				}
				stText = objSearchResult.getText(options);
			} else {
				if (join) {
					name = name + '.' + join;
				}
				if (objSearchResult.hasOwnProperty('values') || objSearchResult.hasOwnProperty(name)) {
					var objValue = null;
					if (objSearchResult.hasOwnProperty('values')) {
						objValue = objSearchResult.values[name];
					} else {
						objValue = objSearchResult[name];
					}
					stText = objValue;
					if (objValue && objValue.constructor === Array) {
						stText = null;
						if (objValue.length) {
							objValue = objValue[0];
						}
					}
					if (objValue && objValue.text) {
						stText = objValue.text;
					}
				}
			}
		}
		return stText;
	};
	
	function loadDailyRouteSearch(objParameters) {
		var objSavedSearch = NSSearch.load({
			id : objParameters.inputSearchDailyRoutes
		});
		var arrFilters = objSavedSearch.filterExpression;
		if (arrFilters && arrFilters.length) {
			arrFilters.push('and');
		}
		if (objParameters.dateFrom) {
			var arrDateFromToFilter = [ 'custrecord_th_dailyroute_date',
				NSSearch.Operator.WITHIN,
				[ objParameters.dateFrom, objParameters.dateTo ] ];
			arrFilters.push(arrDateFromToFilter);
		} else {
			var arrDateThroughFilter = [ 'custrecord_th_dailyroute_date', NSSearch.Operator.ONORBEFORE, objParameters.dateTo ];
			arrFilters.push(arrDateThroughFilter);
		}
		if (objParameters.vendor) {
			arrFilters.push('and');
			arrFilters.push([ 'custrecord_th_dailyroute_contractdriver', NSSearch.Operator.ANYOF, [ objParameters.vendor] ]);
		}
		if (objParameters.customer) {
			arrFilters.push('and');
			arrFilters.push([ 'custrecord_th_dailyroute_customer', NSSearch.Operator.ANYOF, [ objParameters.customer ] ]);
		}
		objSavedSearch.filterExpression = arrFilters;
		log.debug(_stDeploymentId + '.loadDailyRouteSearch', 'Filters=' + JSON.stringify(arrFilters));
		return objSavedSearch;
	}
	/**
	 * Marks the beginning of the Map/Reduce process and generates input data.
	 *
	 * @typedef {Object} ObjectRef
	 * @property {number} id - Internal ID of the record instance
	 * @property {string} type - Record type id
	 *
	 * @return {Array|Object|Search|RecordRef} inputSummary
	 * @since 2015.1
	 */
	function getInputData() {
		var stLogTitle = _stDeploymentId + '.getInputData';
		log.debug(stLogTitle, '<-----Get input data:STARTED----->');
		try {
			var objParameters = libUtilities.getScriptParameters();
			var objSavedSearch = loadDailyRouteSearch(objParameters);
			return objSavedSearch;
		} finally {
			log.debug(stLogTitle, '<-----Get input data:END----->');
		}
	}
	/**
	 * Executes when the map entry point is triggered and applies to each key/value pair.
	 *
	 * @param {MapSummary} context - Data collection containing the key/value pairs to process through the map stage
	 * @since 2015.1
	 */
	function map(context) {
		var stLogTitle = _stDeploymentId + '.map';
		log.debug(stLogTitle, '<-----MAP: ' + context.value + '----->\n');
		try {
			var objData = JSON.parse(context.value);
			var stKeyContractor = libUtilities.getResultValue(objData, 'custrecord_th_dailyroute_mastercontract');
			context.write(stKeyContractor, context.value);
		} finally {
			log.debug(stLogTitle, '<-----MAP:END----->');
		}
	}
	function groupInvoiceItems(arrData) {
		var mapInvoiceItems = {};
		for (var i = 0, len = arrData.length; i < len; i += 1) {
			var objData = JSON.parse(arrData[i]);
			var stInvoiceItem = libUtilities.getResultValue(objData, 'custrecord_th_dailyroute_item');
			var stInvoiceDriver = libUtilities.getResultValue(objData, 'custrecord_th_dailyroute_contractdriver');
			var stInvoiceItemKey = stInvoiceItem + '_' + stInvoiceDriver;
			if (!mapInvoiceItems.hasOwnProperty(stInvoiceItemKey)) {
				mapInvoiceItems[stInvoiceItemKey] = {
					amount : 0,
					quantity : 1,
					item : stInvoiceItem,
					custcol_th_dailyroute_contractdriver : stInvoiceDriver
				};
			}
			var flInvoicingAmount = parseFloat(libUtilities.getResultValue(objData, 'custrecord_th_dailyroute_payamount')) || 0;
			mapInvoiceItems[stInvoiceItemKey].amount += flInvoicingAmount;
		}
		return mapInvoiceItems;
	}
	function generateVendorBill(stVendor, arrData) {
		var stLogTitle = _stDeploymentId + '.generateVendorBill';
		try {
			var stInvoiceId = null;
			var objParameters = libUtilities.getScriptParameters();
			log.debug(stLogTitle, 'Invoicing ' + arrData.length + ' daily route(s) for vendor ' + stVendor);
			var recInvoice = NSRecord.create({
				type : NSRecord.Type.VENDOR_BILL,
				isDynamic : true,
				defaultValues : {
					entity : stVendor
				}
			});
			var objData = JSON.parse(arrData[0]);
			var stLocationId = libUtilities.getResultValue(objData, 'custrecord_th_dailyroute_location');
			var stMemo = objParameters.memo || '';
			stMemo += ' ';
			recInvoice.setValue({
				fieldId : 'memo',
				value : stMemo
			});
			recInvoice.setValue({
				fieldId : 'trandate',
				value : NSFormat.parse({
					type : NSFormat.Type.DATE,
					value : objParameters.transactionDate
				})
			});
			recInvoice.setValue({
				fieldId : 'approvalstatus',
				value : _CONSTANTS.defaultApprovalStatus
			});
			var mapInvoiceItems = groupInvoiceItems(arrData);
			log.audit(stLogTitle, 'Items=' + JSON.stringify(mapInvoiceItems));
			var line = 0;
			for ( var stInvoiceItemKey in mapInvoiceItems) {
				recInvoice.selectNewLine({
					sublistId : 'item'
				});
				recInvoice.setCurrentSublistValue({
					sublistId : 'item',
					fieldId : 'item',
					value : mapInvoiceItems[stInvoiceItemKey].item
				});
				recInvoice.setCurrentSublistValue({
					sublistId : 'item',
					fieldId : 'custcol_th_dailyroute_contractdriver',
					value : mapInvoiceItems[stInvoiceItemKey].custcol_th_dailyroute_contractdriver
				});
				recInvoice.setCurrentSublistValue({
					sublistId : 'item',
					fieldId : 'location',
					value : stLocationId
				});
				var stItemDescription = recInvoice.getCurrentSublistValue({
					sublistId : 'item',
					fieldId : 'description'
				}) || objParameters.defaultLineMemo || '';
				stItemDescription += ' ';
				recInvoice.setCurrentSublistValue({
					sublistId : 'item',
					fieldId : 'description',
					value : stItemDescription + objParameters.dateTo
				});
				recInvoice.setCurrentSublistValue({
					sublistId : 'item',
					fieldId : 'quantity',
					value : mapInvoiceItems[stInvoiceItemKey].quantity
				});
				recInvoice.setCurrentSublistValue({
					sublistId : 'item',
					fieldId : 'rate',
					value : mapInvoiceItems[stInvoiceItemKey].amount
				});
				recInvoice.setCurrentSublistValue({
					sublistId : 'item',
					fieldId : 'amount',
					value : mapInvoiceItems[stInvoiceItemKey].amount
				});
				recInvoice.setCurrentSublistValue({
					sublistId : 'item',
					fieldId : 'isbillable',
					value : false
				});
				recInvoice.commitLine({
					sublistId : 'item'
				});
				line += 1;
			}
			return recInvoice.save({
				ignoreMandatoryFields : true,
				enableSourcing : false
			});
		} catch (invoiceErr) {
			log.error(stLogTitle, '[Process error] ' + invoiceErr.name + ': ' + invoiceErr.message);
			var stErrMessage = '[Error creating bill for vendor (id=' + stVendor + ')] ' + invoiceErr.toString();
			throw NSError.create({
				name : 'Vendor Bill Error',
				message : stErrMessage
			});
		}
	}
	function flagDailyRoutes(stVendorBillId, arrLines) {
		var stLogTitle = _stDeploymentId + '.flagDailyRoutes';
		var objParameters = libUtilities.getScriptParameters();
		var arrFlaggedLines = [];
		for (var i = 0, len = arrLines.length; i < len; i += 1) {
			var objData = JSON.parse(arrLines[i]);
			var stDailyRouteId = objData.id;
			NSRecord.submitFields({
				type : 'customrecord_th_dailyroute',
				id : stDailyRouteId,
				values : {
					custrecord_th_dailyroute_vendorbill : stVendorBillId,
					custrecord_th_dailyroute_status : objParameters.drStatusPaid
				},
				options : {
					enablesourcing : true,
					ignoreMandatoryFields : true
				}
			});
			arrFlaggedLines.push(stDailyRouteId);
			log.debug(stLogTitle, 'Flagged Daily Route ' + stDailyRouteId + ' with Vendor Bill ' + stVendorBillId + '.');
		}
		return arrFlaggedLines;
	}
	/**
	 * Executes when the reduce entry point is triggered and applies to each group.
	 *
	 * @param {ReduceSummary} context - Data collection containing the groups to process through the reduce stage
	 * @since 2015.1
	 */
	function reduce(context) {
		var stLogTitle = _stDeploymentId + '.reduce';
		log.debug(stLogTitle, '<-----REDUCE:START----->');
		try {
			var stKey = context.key;
			var arrLines = context.values;
			log.debug(stLogTitle, 'Key=' + stKey + ',Values=' + JSON.stringify(context.values));
			var reduceContext = {
				context : context
			};
			var stInvoiceId = generateVendorBill(stKey, arrLines);
			log.audit(stLogTitle, 'Vendor Bill (id=' + stInvoiceId + ') generated for vendor ' + stKey + '.');
			var arrFlaggedLines = flagDailyRoutes(stInvoiceId, arrLines);
			log.audit(stLogTitle, 'Flagged Daily Route IDs=' + arrFlaggedLines.join(','));
			context.write(stKey, stInvoiceId);
		} finally {
			log.debug(stLogTitle, '<-----REDUCE:END----->');
		}
	}
	/**
	* Executes when the summarize entry point is triggered and applies to the result set.
	*
	* @param {Summary} summary - Holds statistics regarding the execution of a map/reduce script
	* @since 2015.1
	*/
	function summarize(summary) {
		var stLogTitle = _stDeploymentId + '.summarize';
		try {
			log.debug(stLogTitle, '<-----SUMMARY:START----->');
			log.audit(stLogTitle, 'Duration: ' + summary.seconds);
			log.audit(stLogTitle, 'Usage Consumed: ' + summary.usage);
			log.audit(stLogTitle, 'Number of Queues: ' + summary.concurrency);
			log.audit(stLogTitle, 'Number of Yields: ' + summary.yields);
			if (summary.inputSummary.error) {
				log.error(stLogTitle, '[Input Error] ' + summary.inputSummary.error);
				return;
			}
			var intMapErrorCount = 0;
			var mapSummary = summary.mapSummary;
			mapSummary.errors.iterator().each(function(key, value) {
				log.error(stLogTitle, '[Could not process Daily Route (id=' + key + ')]. Error=' + value);
				intMapErrorCount += 1;
				return true;
			});
			var intReduceErrorCount = 0;
			var reduceSummary = summary.reduceSummary;
			reduceSummary.errors.iterator().each(function(key, value) {
				log.error(stLogTitle, '[Vendor Bill generation failed for vendor (id=' + key + ')] ' + value);
				intReduceErrorCount += 1;
				return true;
			});
			summary.output.iterator().each(function(key, value) {
				log.debug(stLogTitle, 'Processed daily routes for vendor (id=' + key + '). Vendor Bill=' + value);
				return true;
			});
			if (intReduceErrorCount > 0) {
				var objError = {};
				objError.name = 'Error(s) Creating Bill(s)';
				objError.message = 'Please check the ERROR logs. Encountered ' + intReduceErrorCount + ' error(s).';
				throw NSError.create(objError);
			}
		} finally {
			log.debug(stLogTitle, '<-----SUMMARY:END----->');
		}
	}
	return ({
		getInputData : getInputData,
		map : map,
		reduce : reduce,
		summarize : summarize
	});

});