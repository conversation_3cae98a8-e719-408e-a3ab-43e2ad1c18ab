/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/record', 'N/format'], function(record, format) {

    function onRequest(context) {
        try {
            
            var recId = context.request.parameters.recid;
            var recObj = record.load({
                id: recId,
                type: record.Type.WORK_ORDER,
                isDynamic: true
            });

            var startdate = recObj.getValue({ fieldId: 'startdate' });


            var formattedDate = format.format({
                value: startdate,
                type: format.Type.DATE
            });
            
            var returnStr = "<#assign startdate = '" + formattedDate + "' />"; 
            log.debug('test', returnStr);

            context.response.write({
                output: returnStr
            });

        } catch (e) {
            log.debug("Error", e.message);

            var returnStr = "<#assign startdate = '' />"; 
            
            context.response.write({
                output: returnStr
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
