/**
 * @appliedtorecord recordtype
 *
 * @param {String} type Operation types: create, edit, delete, xedit
 *
 * @returns {Void}
 */
 function beforeSubmit(type) {

    try {
        if (type == 'create' || type == 'edit' || type == 'xedit') {
            if (nlapiGetFieldValue('createdfrom')) {
                var shopifyData = nlapiLookupField('transaction', nlapiGetFieldValue('createdfrom'), ['custbody_in8_shop_id', 'custbody_in8_shop_setting', 'status']);

                if (nlapiGetContext().getExecutionContext() != 'userinterface' && shopifyData.custbody_in8_shop_id && shopifyData.custbody_in8_shop_setting) {

                    var transactionData = getTransactionData(shopifyData.custbody_in8_shop_id, shopifyData.custbody_in8_shop_setting);

                    if (transactionData) {
                        updateRestockData(transactionData);
                    } else {
                        nlapiLogExecution('DEBUG', 'Shopify', 'Could not load Shopify Transaction.');
                    }
                }

                try {
                    if (shopifyData.status == 'pendingRefund') {
                        var r = nlapiTransformRecord('returnauthorization', nlapiGetFieldValue('createdfrom'), 'cashrefund');
                        r.setFieldValue('account', 522);
                        r.setFieldValue('trandate', nlapiGetFieldValue('trandate'));
                        var id = nlapiSubmitRecord(r, true, true);

                        nlapiLogExecution('DEBUG', 'Shopify', 'Cash Refund Id: ' + id);
                    }
                } catch(e1) {
                    nlapiLogExecution('ERROR', 'Error creating Refund', (e1 instanceof nlobjError ? 'Code: ' + e1.getCode() + ' - Details: ' + e1.getDetails() : 'Details: ' + e1));
                }
            }
        }
    } catch (e) {
        if (nlapiGetFieldValue('createdfrom')) {
            nlapiSubmitField(nlapiLookupField('transaction', nlapiGetFieldValue('createdfrom'), 'recordtype'), nlapiGetFieldValue('createdfrom'),
                'custbody_in8_ret_itemreceipt_error', e.toString());
            throw e;
        }
        nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
    }
}

function getTransactionData(shopifyId, settingId) {

    var settings = nlapiLoadRecord('customrecord_in8_shop_settings', settingId);
    var apiVersion = settings.getFieldValue('custrecord_in8_shop_api_version');

    // Call order endpoint
    var url = settings.getFieldValue('custrecord_in8_shop_sett_url') + 'admin/api/' + apiVersion + '/orders/' + shopifyId + '.json';

    var settingsReq = {};
    settingsReq.headers = {};
    settingsReq.headers['Content-Type'] = 'application/json';
    settingsReq.headers['X-Shopify-Access-Token'] = settings.getFieldValue('custrecord_in8_shop_sett_password');

    nlapiLogExecution('DEBUG', 'Shopify', 'URL: ' + url);

    var resp = nlapiRequestURL(url, null, settingsReq.headers, 'GET');

    nlapiLogExecution('DEBUG', 'Shopify', 'Response: ' + resp.getBody());

    if (resp.getCode() == 200 || resp.getCode() == 201) {
        var body = resp.getBody();
        return JSON.parse(body);
    }
}

function updateRestockData(body) {

    // For each line item check the payload
    for (var i = 1; i <= nlapiGetLineItemCount('item'); i++) {
        nlapiSelectLineItem('item', i);

        if (nlapiGetCurrentLineItemValue('item', 'itemreceive', i) == 'T' &&
            nlapiGetCurrentLineItemValue('item', 'custcol_in8_shopify_line_id', i)) {
            // Check the refunds

            var shouldSetLineRestock = setLineRestock(body, nlapiGetCurrentLineItemValue('item', 'custcol_in8_shopify_line_id', i));

            nlapiLogExecution('DEBUG', 'Shopify', 'shouldSetLineRestock line ' + i + ': ' + shouldSetLineRestock);

            //nlapiSetCurrentLineItemValue('item', 'restock', shouldSetLineRestock ? 'T' : 'F');
            nlapiSetLineItemValue('item', 'restock', i, shouldSetLineRestock ? 'T' : 'F');

            // nlapiCommitLineItem('item');
        }
    }
}

function setLineRestock(body, shopifyLineId) {

    nlapiLogExecution('DEBUG', 'Shopify', 'Shopify Line Id: ' + shopifyLineId);

    var order = body.order;

    // Find the Order Refunds
    for (var j = 0; order.refunds && j < order.refunds.length; j++) {

        // Find the Refund Line Items
        for (var k = 0; k < order.refunds[j].refund_line_items.length; k++) {
            var refundLineItem = order.refunds[j].refund_line_items[k];

            nlapiLogExecution('DEBUG', 'Shopify', 'Refund line item id: ' + String(refundLineItem.line_item_id));

            // Compare the line_item_id with the Shopify Line Id in the transaction
            if (String(refundLineItem.line_item_id) == shopifyLineId) {

                nlapiLogExecution('DEBUG', 'Shopify', 'Found line data in payload. Shopify Line Id: ' + refundLineItem.line_item_id + ' - Restock: ' + refundLineItem.restock_type);
                if (refundLineItem.restock_type == 'return') {
                    return true;
                } else {
                    return false;
                }
            }
        }
    }
    nlapiLogExecution('DEBUG', 'Shopify', 'Line item not found in refunds data.');

    // Throw an error
    throw new Error('Line item not found in refunds data. Shopify Line Id: ' + shopifyLineId);

    //return false;
}