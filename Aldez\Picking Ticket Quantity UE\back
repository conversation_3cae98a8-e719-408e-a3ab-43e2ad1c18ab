/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/search','N/ui/message', 'N/record'], function(search, message, record) {

    function checkItemIfGroup(itemId){

        var itemLookUp = search.lookupFields({
            type: search.Type.ITEM,
            id: itemId,
            columns: ['type']
        });

        if(itemLookUp.hasOwnProperty('type')) {
            if(itemLookUp.type[0].value == 'Group') {
                return true;
            }
        }

        return false;
    }

    function removeButtons(context) {
        var recObj = context.newRecord;

        var soStatus = recObj.getValue({
            fieldId: 'status'
        });

        // if pending billing = don't remove buttons.
        if(soStatus == 'Pending Billing') return true;

        var lineCount = recObj.getLineCount({
            sublistId: 'item'
        });
        
        var hasGreater = false;
        var pickTicketBlank = false;

        for(var i = 0; i < lineCount; i++) {

            var itemId = recObj.getSublistValue({
                sublistId: 'item',
                fieldId: 'item',
                line: i
            });
    
            if(itemId == 0) continue; // end of group line

            if(!checkItemIfGroup(itemId)) {

                var pickTicketQty = recObj.getSublistValue({
                    sublistId: 'item',
                    fieldId: 'custcol_pick_ticket_qty',
                    line: i
                });

                var remainingFulfillment = recObj.getSublistValue({
                    sublistId: 'item',
                    fieldId: 'custcol2',
                    line: i
                });

                // if remaining fulfillment is 0, skip.
                // the latter part of the condition means that only skip if the remaining fulfillment is 0 and the pick ticket quantity is also 0.
                if(parseInt(remainingFulfillment) == 0 || (pickTicketQty == '' || pickTicketQty == null)) continue;

                // if there are still remaining fulfillment but the pick ticket quantity is 0, skip.
                // this means that they might not want to fulfill this yet.
                if((parseInt(remainingFulfillment) != 0) && (pickTicketQty == '' || pickTicketQty == null)) continue;

                // if a pick ticket is blank, remove button
                if(pickTicketQty == '' || pickTicketQty == null) {
                    var itemname = recObj.getSublistText({
                        sublistId: 'item',
                        fieldId: 'item',
                        line: i
                    });
                    pickTicketBlank = true;
                    break;
                }

                if(pickTicketQty > remainingFulfillment) {
                    hasGreater = true;
                    break;
                }
            }                
            
        }

        if(pickTicketBlank){
            var formObj = context.form;

            formObj.addPageInitMessage({
                type: message.Type.WARNING,
                message: 'An item has blank pick ticket quantity. Fulfill button and Print Pick Ticket button has been temporarily removed. <br><br> <b>Please edit the transaction to correct the quantities.</b>',
                duration: 10000
            });
            formObj.removeButton('process');
            formObj.removeButton('printpicktick');
        }

        if(hasGreater){
            var formObj = context.form;

            formObj.addPageInitMessage({
                type: message.Type.WARNING,
                message: 'An item has pick ticket quantity is greater than its remaining fulfillment. Fulfill button and Print Pick Ticket button has been temporarily removed. <br><br> <b>Please edit the transaction to correct the quantities.</b>',
                duration: 10000
            });
            formObj.removeButton('process');
            formObj.removeButton('printpicktick');
        }
    }

    function checkIfHasFulfillment(context) {
        
        var recObj = context.newRecord;
        var lineCount = recObj.getLineCount({
            sublistId: 'item'
        });
        var hasFulfillment = false;
        for(var i = 0; i < lineCount; i++) {
            var qtyFulfilled = recObj.getSublistValue({
                sublistId: 'item',
                fieldId: 'quantityfulfilled',
                line: i
            });

            if(qtyFulfilled){
                hasFulfillment = true;
                break;
            }
        }

        return hasFulfillment;
    }

    function beforeLoad(context) {
        if(context.type == context.UserEventType.VIEW) {
            removeButtons(context);
        } else if(context.type == context.UserEventType.CREATE || context.type == context.UserEventType.COPY || context.type == context.UserEventType.EDIT) {
            if(!checkIfHasFulfillment(context)){
                context.form.clientScriptModulePath = 'SuiteScripts/ACS_CS_Unpopulate_Func.js';

                var sublistObj = context.form.getSublist({ id: 'item' });

                sublistObj.addButton({
                    id: 'custpage_clear_pick_tick',
                    label: 'Clear Pick Ticket Qty',
                    functionName: 'unpopulatePickTicketQuantity()'
                });
            }
        }

    }

    function afterSubmit(context) {

        try {
            var recObj = record.load({
                type: record.Type.SALES_ORDER,
                id: context.newRecord.id,
                isDynamic: false
            });
            

            var lineCount = recObj.getLineCount({
                sublistId: 'item'
            });

            var hasChanged = false;
            
            for(var i = 0; i < lineCount; i++) {

                
                var itemId = recObj.getSublistValue({
                    sublistId: 'item',
                    fieldId: 'item',
                    line: i
                });
        
                if(itemId == 0) continue; // end of group line
                
                if(checkItemIfGroup(itemId)) {

                    var pickTicketQuantity = recObj.getSublistValue({
                        sublistId: 'item',
                        fieldId: 'custcol_pick_ticket_qty',
                        line: i
                    });

                    var itemId = recObj.getSublistValue({
                        sublistId: 'item',
                        fieldId: 'item',
                        line: i
                    });

                    var itemRecObj = record.load({
                        type: record.Type.ITEM_GROUP,
                        id: itemId,
                        isDynamic: true
                    }); 
                    
                    var componentLineCount = itemRecObj.getLineCount({
                        sublistId: 'member'
                    });

                    for(var j = 0; j < componentLineCount; j++) {
                        var componentRatio = itemRecObj.getSublistValue({
                            sublistId: 'member',
                            fieldId: 'quantity',
                            line: j
                        });

                        var componentId = itemRecObj.getSublistValue({
                            sublistId: 'member',
                            fieldId: 'item',
                            line: j
                        });

                        var componentLineNo = recObj.findSublistLineWithValue({
                            sublistId: 'item',
                            fieldId: 'item',
                            value: componentId
                        });

                        var ratioPickTicketQty = pickTicketQuantity * componentRatio;

                        recObj.setSublistValue({
                            sublistId: 'item',
                            fieldId: 'custcol_pick_ticket_qty',
                            value: ratioPickTicketQty,
                            line: componentLineNo
                        });
                    }

                    hasChanged = true;

                }

            }

            if(hasChanged) {
                try {
                    recObj.save();
                } catch (e) {
                    log.debug("Error", e);
                }
            }
            
        } catch (e) {
            log.debug("SO Deleted", 'ID: ' + context.oldRecord.id);
        }
        
    }

    return {
        beforeLoad: beforeLoad,
        afterSubmit: afterSubmit
    }
});
