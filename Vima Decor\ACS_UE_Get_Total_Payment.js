/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/ui/serverWidget', 'N/record'], function(serverWidget, record) {

    function beforeLoad(context) {
        if(context.type == context.UserEventType.PRINT) {
            try {
                var recObj = record.load({
                    id: context.newRecord.id,
                    type: record.Type.INVOICE
                });

                var lineCount = recObj.getLineCount({ sublistId: 'links' });
                var totalPayment = 0.00;
                
                if(lineCount) {
                    var totalPayment = recObj.getSublistValue({ sublistId: 'links', fieldId: 'total', line: (lineCount - 1) });
                }

                log.debug('lineCount', lineCount);

                var totalPaymentField = context.form.addField({
                    id:'custpage_total_payments',
                    label:'total payments',
                    type: serverWidget.FieldType.TEXT
                });
                totalPaymentField.defaultValue = totalPayment;
            } catch (e) {
                log.error('Error', e);
            }
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
