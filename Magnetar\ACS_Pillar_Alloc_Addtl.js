/**
 *@NApiVersion 2.1
 *@NScriptType UserEventScript
 */
 define(['N/email', 'N/search', 'N/record'], function(email, search, record) 
 {
     /**
     * Function definition to be triggered before record is loaded.
     *
     * @param {Object} scriptContext
     * @param {Record} scriptContext.newRecord - New record
     * @param {string} scriptContext.type - Trigger type
     * @param {Form} scriptContext.form - Current form
     * @Since 2015.2
     */   
     //This code was built on top of the ACS script for case #3855157, but revamped for logic cleanup and bug fixes - MS Sept. 2020
 

    function checkIfLineAmtIsValid(srcTransactionRec, lineString, index)
    { //exclude 0 amount	   
         var srcAmt;
        if(srcTransactionRec.type == record.Type.JOURNAL_ENTRY || srcTransactionRec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
         {
             srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'debit', line: index});
             if(srcAmt && srcAmt != 0)
             {
                 return true;                                                                         
             }
             else
             {
                 srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'credit', line: index});
                 if(srcAmt && srcAmt != 0)
                 {
                     return true;                                                                         
                 }                                                                           
             }
         }
         else if(srcTransactionRec.type == record.Type.VENDOR_BILL || srcTransactionRec.type == record.Type.VENDOR_CREDIT)
         {               
             srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'amount', line: index}); 
             if(srcAmt && srcAmt != 0)
             {
                 return true;                                                                         
             }     			
         }   
         return false;
    }
 
    function checkIfTransactionNeedsToBeAllocated(context, sublistType)
    {        	     
         var toAllocate = false;
         var isConnectorFailure = false;	
        
         for (var index = 0; index < context.newRecord.getLineCount(sublistType); index++) 
         {              
             if(!checkIfLineAmtIsValid(context.newRecord, sublistType, index))
             {
                 continue;
             }
             if(context.newRecord.getSublistValue({sublistId: sublistType, fieldId: 'custcol_mag_vb_line_isprepaid',line: index}))
             {
                 log.debug('toAllocate', toAllocate);
                 continue; //skip IsPrepaid Line
             }
                
             if(context.newRecord.getSublistValue({sublistId: sublistType, fieldId: 'schedule', line: index}))
             {
                 continue;  //do not allocate here, allocate downstream amortization JE instead
             }
 
             var pillar = context.newRecord.getSublistValue({
                     sublistId: sublistType,
                     fieldId: 'cseg6',
                     line: index
                 });            
                             
             
             if(pillar && pillar == 8) //Special Allocation
             {       
                 var allocType = context.newRecord.getSublistValue({
                     sublistId: sublistType,
                     fieldId: 'cseg7',
                     line: index
                 });                
                 if(allocType && allocType != 7) //Allocation Type is not "*Not Applicable*"
                 {                            
                     toAllocate = true;
                     break; //at least 1 line meets all the criteria which mandates an alloc JE to be generated.
                 }
                 else if(!allocType)
                 {
                     if((context.newRecord.type == record.Type.VENDOR_BILL || context.newRecord.type == record.Type.VENDOR_CREDIT) && (context.newRecord.createdby == 20)) //created by concur			
                     {					
                         isConnectorFailure = true;
                         break;
                     }		
                 }
             }    
             else if(!pillar)
             {
                 if((context.newRecord.type == record.Type.VENDOR_BILL || context.newRecord.type == record.Type.VENDOR_CREDIT) && (context.newRecord.createdby == 20)) //created by concur			
                 {					
                     isConnectorFailure = true;
                     break;
                 }							
             }				
         }
         
         if(isConnectorFailure)
         {
             notifyConnectorFailure();
         }
         return toAllocate;
    }   
      
    
    function checkIfLineMeetsCriteria(lineString, index, srcTransactionRec)
    {         
         if(!checkIfLineAmtIsValid(srcTransactionRec, lineString, index))
         {
             return false;
         }
         
         if(srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'custcol_mag_vb_line_isprepaid', line: index}))
         {            
             return false;  //Skip Is Prepaid Line
         }
 
         if(srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'schedule', line: index}))
         {            
             return false;  //do not allocate here, allocate downstream amortization JE instead
         }
 
         var pillar = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'cseg6', line: index});
         if(!pillar || pillar != 8)  
         {            
             return false; //Skip non "Special Allocation" pillar
         }
               
         //Expense Allocation Type
         var allocType = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'cseg7', line: index});
         if(!allocType || allocType == 7) //Allocation Type "*Not Applicable*"
         {            
             return false;
         }                       
         
         return true;     
    }
   
    
    function round(value, precision) 
    {      
       return Number(Math.round(value + 'e' + precision) + 'e-' +precision);
    }
 
 
    function SetAIJELineFields(srcTransactionRec, lineString, index, allocJERec) //advanced interco JE
    {
         var linesubsidiary = srcTransactionRec.getSublistValue({sublistId: lineString,fieldId: 'linesubsidiary', line: index});        
         allocJERec.setCurrentSublistValue({
             sublistId: 'line',
             fieldId: 'linesubsidiary',
             value: linesubsidiary
         });
 
         var eliminate = srcTransactionRec.getSublistValue({sublistId: lineString,fieldId: 'eliminate', line: index});        
         if(eliminate)
         {
             allocJERec.setCurrentSublistValue({
                 sublistId: 'line',
                 fieldId: 'eliminate',
                 value: eliminate
             });
 
             var duetofromsubsidiary = srcTransactionRec.getSublistValue({sublistId: lineString,fieldId: 'duetofromsubsidiary', line: index});            
             allocJERec.setCurrentSublistValue({
                 sublistId: 'line',
                 fieldId: 'duetofromsubsidiary',
                 value: duetofromsubsidiary
             });
         }
 
         var entity = srcTransactionRec.getSublistValue({sublistId: lineString,fieldId: 'entity', line: index}); //interco vendor or customer        
         if(entity)
         {
             allocJERec.setCurrentSublistValue({
                 sublistId: 'line',
                 fieldId: 'entity',
                 value: entity
             });
         }
    }
 
    function searchForStatJEs(srcTransactionRec, allocType, postingPeriod, dictStatJEs)
    {
         if(allocType in dictStatJEs)
         {
             return dictStatJEs[allocType];
         }
 
         var statsJESrch = search.load({
                 id: 'customsearch_nsacs_statistical_je_search'
             });
                 
         //search for statistical jounals for this allocation type and period
         statsJESrch.filters.push(search.createFilter({
             name: 'cseg7', //allocation type
             operator: 'anyof',
             values: allocType
         }));
         statsJESrch.filters.push(search.createFilter({
             name: 'internalid',
             join: 'accountingperiod',
             operator: 'anyof',
             values: postingPeriod 
         }));
         log.debug('Statistical JE:',statsJESrch);
 
         var statsJEResults = statsJESrch.run();
         var contextObject = {};
         var statsJEs = [];
         statsJEResults.each(function(result){                               
             contextObject.internalid = result.id;
             contextObject.pillar = result.getValue('line.cseg6');
             contextObject.amount = result.getValue('amount');
             statsJEs.push(contextObject);
             contextObject = {};
             return true;
         });
         log.debug('statsJEs:', statsJEs);             
 
         if(statsJEs.length == 0) //no statistical JE found
         {
             notifyNoStatisticalJEError(srcTransactionRec, allocType);
             return;
         }
         else
         {
             dictStatJEs[allocType] = statsJEs;
             return statsJEs;
         }
    }
 
    function createAllocJE(context, srcTransactionRec, isSVB)
    {
         var lineString = '';
 
         if(srcTransactionRec.type == record.Type.JOURNAL_ENTRY || srcTransactionRec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
         {
             lineString = 'line';
         }
         else
         {
             lineString = 'expense';
         }         	          
        
         var allocJERec;
         if(srcTransactionRec.type == record.Type.JOURNAL_ENTRY || context.newRecord.type == record.Type.VENDOR_CREDIT || (context.newRecord.type == record.Type.VENDOR_BILL && !isSVB))
         {
             allocJERec = record.create({
                 type: record.Type.JOURNAL_ENTRY, 
                 isDynamic: true	});    
         }
         else if(srcTransactionRec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY || (context.newRecord.type == record.Type.VENDOR_BILL && isSVB))
         {
             allocJERec = record.create({
                 type: record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY, 
                 isDynamic: true	});
         }	            
     
         //populate header fields for the alloc JE----------------------------
         allocJERec.setValue({
             fieldId: 'subsidiary',
             value: srcTransactionRec.getValue('subsidiary')
         });
         
         allocJERec.setValue({
             fieldId: 'currency',
             value: srcTransactionRec.getValue('currency')
         });
         
         allocJERec.setValue({
             fieldId: 'trandate',
             value: srcTransactionRec.getValue('trandate')
         });
     
         var memoText = '';
         if(context.newRecord.type == record.Type.VENDOR_CREDIT || context.newRecord.type == record.Type.VENDOR_BILL)
         {
             var fieldLookUp = search.lookupFields({
                 type: srcTransactionRec.type,
                 id: srcTransactionRec.id,
                 columns: ['entity'] //vendor 
             });	
             log.debug('fieldLookUp:', fieldLookUp);
         
             if(fieldLookUp.entity.length > 0)
             {
                 var tranNum = search.lookupFields({
                     type: srcTransactionRec.type,
                     id: srcTransactionRec.id,
                     columns: ['transactionnumber']
                 });
                 memoText = 'Alloc | ' + fieldLookUp.entity[0].text +' ' + tranNum.transactionnumber;
             }
             else
             {
                 memoText = 'Alloc | ' + srcTransactionRec.id;
             }
         }	    	    
         else
         {
             var tranNumJE = search.lookupFields({
                 type: srcTransactionRec.type,
                 id: srcTransactionRec.id,
                 columns: ['tranid']
             });
             memoText = 'Alloc | ' + tranNumJE.tranid;
         }
         allocJERec.setValue({
             fieldId: 'memo',
             value: memoText
         });
      
          if(srcTransactionRec.type == record.Type.JOURNAL_ENTRY  || srcTransactionRec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
         {
            var bookje = srcTransactionRec.getValue('bookje');
            if(bookje && bookje == 'T')
            {
              var accountingBook = srcTransactionRec.getValue('accountingbook');
              if(accountingBook) //for allocating amortized book specific JE
              {
                 allocJERec.setValue({
                   fieldId: 'bookje',
                   value: bookje
                   });
                
                 allocJERec.setValue({
                   fieldId: 'accountingbook',
                   value: accountingBook
                   });
              }
            }
         }
            
         allocJERec.setValue({
             fieldId: 'approvalstatus',
             value: 2  //approved		    
         });   
         
         log.debug("alloc JE header populated");
         //populate line items for the alloc JE---------------------------------------
         var dictStatJEs = new Object();
         var postingPeriod = srcTransactionRec.getValue('postingperiod');
         var allocJELineCount = 0;
         for (var index = 0; index < srcTransactionRec.getLineCount(lineString); index++) 
         {
             log.debug('Line index:', index);
             var criteriaMet = false;
             if(isSVB)
             {
                 criteriaMet = checkIfLineMeetsCriteria(lineString, index, srcTransactionRec);
             }
             else
             {
                 criteriaMet = checkIfLineMeetsCriteria(lineString, index, context.newRecord);
             }		    
             if(!criteriaMet)
             {
                 continue;
             }
             
             var allocType = srcTransactionRec.getSublistValue({sublistId: lineString,fieldId: 'cseg7',line: index});
             log.debug('allocType:', allocType);
             
             var statsJEs = searchForStatJEs(srcTransactionRec, allocType, postingPeriod, dictStatJEs);
             if(!statsJEs || statsJEs.length == 0) //no statistical JE found
             {
                 return;			
             }            
 
             //set up allocation lines-------------------------------------------
             var allocValue = 0;
             var srcAmt = 0;
             var srcAmtColumnName;
             var tgtAmtColumnName;
             var allocatedLineSum = 0;
 
             if(srcTransactionRec.type == record.Type.JOURNAL_ENTRY || srcTransactionRec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
             {
                 srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'debit', line: index});
                 if(srcAmt)
                 {
                     srcAmtColumnName = 'debit';
                     tgtAmtColumnName = 'debit'                                                                           
                 }
                 else
                 {
                     srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'credit', line: index});
                     srcAmtColumnName = 'credit';
                     tgtAmtColumnName = 'credit';                                                                            
                 }
             }
             else if(srcTransactionRec.type == record.Type.VENDOR_BILL)
             {
                 srcAmtColumnName = 'amount';
                 tgtAmtColumnName = 'debit';
                 srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: srcAmtColumnName, line: index});                                        
             }
             else if(srcTransactionRec.type == record.Type.VENDOR_CREDIT)
             {
                 srcAmtColumnName = 'amount';
                 tgtAmtColumnName = 'credit';
                 srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: srcAmtColumnName, line: index});                         
             }    
 
             for (var indexArr = 0; indexArr < statsJEs.length; indexArr++) 
             {                        
                 allocJERec.selectNewLine({
                     sublistId: 'line'
                 });
             
                 if(allocJERec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
                 {
                     SetAIJELineFields(srcTransactionRec, lineString, index, allocJERec);    //linesubsidiary must be set first               
                 }
 
                 SetNonPillarAmtLineFieldsForAllocJE(allocJERec, srcTransactionRec, index, allocType, lineString, memoText);
                         
                 allocJERec.setCurrentSublistValue({
                     sublistId: 'line',
                     fieldId: 'cseg6', //pillar
                     value: statsJEs[indexArr].pillar
                 });  
                 
                 if(indexArr == statsJEs.length - 1)
                 {
                     allocValue = round(srcAmt - allocatedLineSum, 2);
                 }
                 else
                 { 
                     allocValue = round((statsJEs[indexArr].amount / 100) * srcAmt, 2);
                     allocatedLineSum += allocValue;
                 }                             
 
                 allocJERec.setCurrentSublistValue({
                     sublistId: 'line',
                     fieldId: tgtAmtColumnName,
                     value: allocValue
                 });                             
             
                 allocJERec.commitLine({
                     sublistId: 'line'
                 });
                 allocJELineCount ++;
             }   
         
             //set up the offset line--------------------------------------------
             allocJERec.selectNewLine({
                     sublistId: 'line'
             });
                            
             if(allocJERec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
             {
                 SetAIJELineFields(srcTransactionRec, lineString, index, allocJERec);    //linesubsidiary must be set first               
             }
             SetNonPillarAmtLineFieldsForAllocJE(allocJERec, srcTransactionRec, index, allocType, lineString, memoText);
             allocJERec.setCurrentSublistValue({
                 sublistId: 'line',
                 fieldId: 'cseg6',
                 value: 8
             });
 
             if(tgtAmtColumnName == 'debit')
             {
                 tgtAmtColumnName = 'credit';
             }
             else
             {
                 tgtAmtColumnName = 'debit';
             }
             allocJERec.setCurrentSublistValue({
                 sublistId: 'line',
                 fieldId: tgtAmtColumnName,
                 value: srcAmt
             });                   
 
             allocJERec.commitLine({
                     sublistId: 'line'
             });
             allocJELineCount ++;            
         }
 
         if(allocJELineCount == 0)
         {
             return;
         }
 
         try
         {
             //save alloc JE-----------------------------------                    
             var savedAllocJERec = allocJERec.save({
                 enableSourcing: true,
                 ignoreMandatoryFields: true
             });
             log.audit('Saved alloc JE ID:', savedAllocJERec);        
             
             record.submitFields({
                 type: srcTransactionRec.type,
                 id: srcTransactionRec.id,
                 values: {
                     'custbody_alloc_transaction': savedAllocJERec                    
                 }
             });
             record.submitFields({
                 type: record.Type.JOURNAL_ENTRY,
                 id: savedAllocJERec,
                 values: {
                     'custbody_src_transaction': srcTransactionRec.id   //prevent further allocation
                 }
             });  
         }
         catch (e) 
         {
             log.error({
                 title: "Error saving allocation JE",
                 details: e.message
                 });
         }
    }

    function modifyAllocJE(context, srcTransactionRec, isSVB, allocJEId)
    {
         var lineString = '';
 
         if(srcTransactionRec.type == record.Type.JOURNAL_ENTRY || srcTransactionRec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
         {
             lineString = 'line';
         }
         else
         {
             lineString = 'expense';
         }         	          
        
         var allocJERec;
         if(srcTransactionRec.type == record.Type.JOURNAL_ENTRY || context.newRecord.type == record.Type.VENDOR_CREDIT || (context.newRecord.type == record.Type.VENDOR_BILL && !isSVB))
         {
             allocJERec = record.load({
                 type: record.Type.JOURNAL_ENTRY, 
                 id: allocJEId,
                 isDynamic: true	});    
         }
         else if(srcTransactionRec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY || (context.newRecord.type == record.Type.VENDOR_BILL && isSVB))
         {
             allocJERec = record.load({
                 type: record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY, 
                 id: allocJEId,
                 isDynamic: true	});
         }	            

         var memoText = '';
         if(context.newRecord.type == record.Type.VENDOR_CREDIT || context.newRecord.type == record.Type.VENDOR_BILL)
         {
             var fieldLookUp = search.lookupFields({
                 type: srcTransactionRec.type,
                 id: srcTransactionRec.id,
                 columns: ['entity'] //vendor 
             });	
             log.debug('fieldLookUp:', fieldLookUp);
         
             if(fieldLookUp.entity.length > 0)
             {
                 var tranNum = search.lookupFields({
                     type: srcTransactionRec.type,
                     id: srcTransactionRec.id,
                     columns: ['transactionnumber']
                 });
                 memoText = 'Alloc | ' + fieldLookUp.entity[0].text +' ' + tranNum.transactionnumber;
             }
             else
             {
                 memoText = 'Alloc | ' + srcTransactionRec.id;
             }
         }	    	    
         else
         {
             var tranNumJE = search.lookupFields({
                 type: srcTransactionRec.type,
                 id: srcTransactionRec.id,
                 columns: ['tranid']
             });
             memoText = 'Alloc | ' + tranNumJE.tranid;
         }

         //remove existing line items in the alloc JE
         log.debug('REMOVE PART');
         var lineCount = allocJERec.getLineCount({ sublistId: 'line' });

         for (var i = (lineCount - 1); i >= 0; i--) {
            log.debug('lines', i);
            allocJERec.removeLine({
                sublistId: 'line',
                line: i,
                ignoreRecalc: true
            });
         }

         //populate line items for the alloc JE---------------------------------------
         var dictStatJEs = new Object();
         var postingPeriod = srcTransactionRec.getValue('postingperiod');
         var allocJELineCount = 0;
         for (var index = 0; index < srcTransactionRec.getLineCount(lineString); index++) 
         {
             log.debug('Line index:', index);
             var criteriaMet = false;
             if(isSVB)
             {
                 criteriaMet = checkIfLineMeetsCriteria(lineString, index, srcTransactionRec);
             }
             else
             {
                 criteriaMet = checkIfLineMeetsCriteria(lineString, index, context.newRecord);
             }		    
             if(!criteriaMet)
             {
                 continue;
             }
             
             var allocType = srcTransactionRec.getSublistValue({sublistId: lineString,fieldId: 'cseg7',line: index});
             log.debug('allocType:', allocType);
             
             var statsJEs = searchForStatJEs(srcTransactionRec, allocType, postingPeriod, dictStatJEs);
             if(!statsJEs || statsJEs.length == 0) //no statistical JE found
             {
                 return;			
             }            
 
             //set up allocation lines-------------------------------------------
             var allocValue = 0;
             var srcAmt = 0;
             var srcAmtColumnName;
             var tgtAmtColumnName;
             var allocatedLineSum = 0;
 
             if(srcTransactionRec.type == record.Type.JOURNAL_ENTRY || srcTransactionRec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
             {
                 srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'debit', line: index});
                 if(srcAmt)
                 {
                     srcAmtColumnName = 'debit';
                     tgtAmtColumnName = 'debit'                                                                           
                 }
                 else
                 {
                     srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: 'credit', line: index});
                     srcAmtColumnName = 'credit';
                     tgtAmtColumnName = 'credit';                                                                            
                 }
             }
             else if(srcTransactionRec.type == record.Type.VENDOR_BILL)
             {
                 srcAmtColumnName = 'amount';
                 tgtAmtColumnName = 'debit';
                 srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: srcAmtColumnName, line: index});                                        
             }
             else if(srcTransactionRec.type == record.Type.VENDOR_CREDIT)
             {
                 srcAmtColumnName = 'amount';
                 tgtAmtColumnName = 'credit';
                 srcAmt = srcTransactionRec.getSublistValue({sublistId: lineString, fieldId: srcAmtColumnName, line: index});                         
             }    
 
             for (var indexArr = 0; indexArr < statsJEs.length; indexArr++) 
             {                        
                 allocJERec.selectNewLine({
                     sublistId: 'line'
                 });
             
                 if(allocJERec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
                 {
                     SetAIJELineFields(srcTransactionRec, lineString, index, allocJERec);    //linesubsidiary must be set first               
                 }
 
                 SetNonPillarAmtLineFieldsForAllocJE(allocJERec, srcTransactionRec, index, allocType, lineString, memoText);
                         
                 allocJERec.setCurrentSublistValue({
                     sublistId: 'line',
                     fieldId: 'cseg6', //pillar
                     value: statsJEs[indexArr].pillar
                 });  
                 
                 if(indexArr == statsJEs.length - 1)
                 {
                     allocValue = round(srcAmt - allocatedLineSum, 2);
                 }
                 else
                 { 
                     allocValue = round((statsJEs[indexArr].amount / 100) * srcAmt, 2);
                     allocatedLineSum += allocValue;
                 }                             
 
                 allocJERec.setCurrentSublistValue({
                     sublistId: 'line',
                     fieldId: tgtAmtColumnName,
                     value: allocValue
                 });                             
             
                 allocJERec.commitLine({
                     sublistId: 'line'
                 });
                 allocJELineCount ++;
             }   
         
             //set up the offset line--------------------------------------------
             allocJERec.selectNewLine({
                     sublistId: 'line'
             });
                            
             if(allocJERec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
             {
                 SetAIJELineFields(srcTransactionRec, lineString, index, allocJERec);    //linesubsidiary must be set first               
             }
             SetNonPillarAmtLineFieldsForAllocJE(allocJERec, srcTransactionRec, index, allocType, lineString, memoText);
             allocJERec.setCurrentSublistValue({
                 sublistId: 'line',
                 fieldId: 'cseg6',
                 value: 8
             });
 
             if(tgtAmtColumnName == 'debit')
             {
                 tgtAmtColumnName = 'credit';
             }
             else
             {
                 tgtAmtColumnName = 'debit';
             }
             allocJERec.setCurrentSublistValue({
                 sublistId: 'line',
                 fieldId: tgtAmtColumnName,
                 value: srcAmt
             });                   
 
             allocJERec.commitLine({
                     sublistId: 'line'
             });
             allocJELineCount ++;            
         }
 
         if(allocJELineCount == 0)
         {
             return;
         }
 
         try
         {
             //save alloc JE-----------------------------------                    
             var savedAllocJERec = allocJERec.save({
                 enableSourcing: true,
                 ignoreMandatoryFields: true
             });
             log.audit('Saved alloc JE ID:', savedAllocJERec);        
             
             record.submitFields({
                 type: srcTransactionRec.type,
                 id: srcTransactionRec.id,
                 values: {
                     'custbody_alloc_transaction': savedAllocJERec                    
                 }
             });
             record.submitFields({
                 type: record.Type.JOURNAL_ENTRY,
                 id: savedAllocJERec,
                 values: {
                     'custbody_src_transaction': srcTransactionRec.id   //prevent further allocation
                 }
             });  
         }
         catch (e) 
         {
             log.error({
                 title: "Error saving allocation JE",
                 details: e.message
                 });
         }
    }
 
    function SetNonPillarAmtLineFieldsForAllocJE(allocJERec, srcTransactionRec, srcTransactionLineIndex, allocType, lineString, memoText)
    {
         allocJERec.setCurrentSublistValue({
             sublistId: 'line',
             fieldId: 'account',
             value: srcTransactionRec.getSublistValue({
                 sublistId: lineString,
                 fieldId: 'account',
                 line: srcTransactionLineIndex
             })
         });
         allocJERec.setCurrentSublistValue({
             sublistId: 'line',
             fieldId: 'department',
             value: srcTransactionRec.getSublistValue({
                 sublistId: lineString,
                 fieldId: 'department',
                 line: srcTransactionLineIndex
             })
         });         
         allocJERec.setCurrentSublistValue({
             sublistId: 'line',
             fieldId: 'location',
             value: srcTransactionRec.getSublistValue({
                 sublistId: lineString,
                 fieldId: 'location',
                 line: srcTransactionLineIndex
             })
         });                                    
         allocJERec.setCurrentSublistValue({
             sublistId: 'line',
             fieldId: 'cseg1', //vintage
             value: srcTransactionRec.getSublistValue({
                 sublistId: lineString,
                 fieldId: 'cseg1',
                 line: srcTransactionLineIndex
             })
         });
         allocJERec.setCurrentSublistValue({
             sublistId: 'line',
             fieldId: 'cseg2', //ownership
             value: srcTransactionRec.getSublistValue({
                 sublistId: lineString,
                 fieldId: 'cseg2',
                 line: srcTransactionLineIndex
             })
         });
         allocJERec.setCurrentSublistValue({
             sublistId: 'line',
             fieldId: 'cseg5', //fund code
             value: srcTransactionRec.getSublistValue({sublistId: lineString,fieldId: 'cseg5',line: srcTransactionLineIndex})            
         });              
         allocJERec.setCurrentSublistValue({
             sublistId: 'line',
             fieldId: 'cseg7', //allocation type
             value: allocType
         });                   
 
         var fieldLookUpAllocType = search.lookupFields({
                     type: 'customrecord_cseg7',
                     id: allocType,
                     columns: ['name']
                 });
                 
         if(fieldLookUpAllocType.name)
         {
             memoText = memoText + ' | ' + fieldLookUpAllocType.name;
         }
                          
         allocJERec.setCurrentSublistValue({
                 sublistId: 'line',
                 fieldId: 'memo',
                 value: memoText
         });        
    }
 
    function notifyConnectorFailure()
    {
        email.send({            
                 author: 20,   //Concur Integration         
                 recipients: ['<EMAIL>'],
                 subject: 'Concur Connector Failed to Post Pillar/Allocation Type',
                 body: 'Concur Invoice or Expense Connector dropped Pillar or Allocation Type info. Please investigate and contact Concur Support.'                
             });
    }
    
    function notifyNoStatisticalJEError(srcTransactionRec, allocType)
    {
         var server = 'https://4579630.app.netsuite.com';
         var author = 953; //id of Michelle: 953
         var recipients = ['<EMAIL>'];
         var cc = ['<EMAIL>'];
         
         var tranNum = search.lookupFields({
             type: srcTransactionRec.type,
             id: srcTransactionRec.id,
             columns: ['transactionnumber']
         });
                  
         if(srcTransactionRec.type == record.Type.JOURNAL_ENTRY)
         {
             email.send({            
                 author: author,            
                 recipients: recipients,
                 cc: cc,
                 subject: 'Failed to select Statisitcal Journal for Journal Entry expense allocation',
                 body: 'Journal Entry <a href="' + server + '/app/accounting/transactions/journal.nl?id=' + srcTransactionRec.id + '">' + tranNum.transactionnumber 
                     + '</a> has been posted into NetSuite, but fails to select a Statistical Journal to complete expense allocation (Allocation Type: ' + allocType + ')',
                 relatedRecords: {
                     transactionId: srcTransactionRec.id
                 }
             });
         }
         else if(srcTransactionRec.type == record.Type.ADV_INTER_COMPANY_JOURNAL_ENTRY)
         {
             email.send({                                
                 author: author,                                
                 recipients: recipients,
                 cc: cc,
                 subject: 'Failed to select Statisitcal Journal for Intercompany Journal Entry expense allocation',
                 body: 'Intercompany Journal Entry <a href="' + server + '/app/accounting/transactions/icjournal.nl?id=' + srcTransactionRec.id + '">' + tranNum.transactionnumber 
                     + '</a> has been posted into NetSuite, but fails to select a Statistical Journal to complete expense allocation (Allocation Type: ' + allocType + ')',
                 relatedRecords: {
                     transactionId: srcTransactionRec.id
                 }
             });
         }
         else if(srcTransactionRec.type == record.Type.VENDOR_BILL)
         {
             email.send({
                 author: author,                                
                 recipients: recipients,
                 cc: cc,
                 subject: 'Failed to select Statisitcal Journal for Vendor Bill expense allocation',
                 body: 'Vendor Bill <a href="' + server + '/app/accounting/transactions/vendbill.nl?id=' + srcTransactionRec.id + '">' + tranNum.transactionnumber 
                     + '</a> has been posted into NetSuite, but fails to select a Statistical Journal to complete expense allocation (Allocation Type: ' + allocType + ')',
                 relatedRecords: {
                     transactionId: srcTransactionRec.id
                 }
             });
         }
         if(srcTransactionRec.type == record.Type.VENDOR_CREDIT){
             email.send({
                 author: author,                                
                 recipients: recipients,
                 cc: cc,
                 subject: 'Failed to select Statisitcal Journal for Vendor Credit expense allocation',
                 body: 'Vendor Credit <a href="' + server + '/app/accounting/transactions/vendcred.nl?id=' + srcTransactionRec.id + '">' + tranNum.transactionnumber 
                     + '</a> has been posted into NetSuite, but fails to select a Statistical Journal to complete expense allocation (Line: ' + allocType + ')',
                 relatedRecords: {
                     transactionId: srcTransactionRec.id
                 }
             });
         }
    }  

    function checkLines(context) {

        var oldRec = context.oldRecord;
        var newRec = context.newRecord;

        // check if the line count is not the same, if true, considered as line change already.
        var oldLineCount = oldRec.getLineCount('expense');
        var newLineCount = newRec.getLineCount('expense');

        log.debug('counts', { oldLineCount: oldLineCount, newLineCount: newLineCount });

        if(oldLineCount != newLineCount) {
            return true;
        }

        var fields = ['amount', 'cseg6', 'cseg7', 'custcol_mag_vb_line_isprepaid', 'account'];

        //loop through each line and compare line by line
        for(var i = 0; i < newLineCount; i++) {
            for(var j = 0; j < fields.length; j++) {
                var oldValue = oldRec.getSublistValue({ sublistId: 'expense', fieldId: fields[j], line: i });
                var newValue = newRec.getSublistValue({ sublistId: 'expense', fieldId: fields[j], line: i });
                log.debug(fields[j], { oldValue: oldValue, newValue: newValue, bool: [oldValue, newValue]})

                if(oldValue != newValue) {
                    return true;
                }
            }
        }

        return false;
    }
 
    function afterSubmit(context) 
    {
        if(context.type != 'edit' && context.type != 'xedit') //create, edit or inline edit
         {
             return;            
         }
         
         if( (context.newRecord.type == record.Type.VENDOR_BILL) && context.newRecord.getValue('approvalstatus')  != 2)  //For VB, which have approval routing enabled, only an approved transaction can be allocated. For Vendor Credit, approval routing is not enabled yet
         {
             return;
         }		
 
         log.debug('afterSubmit context.newRecord.type', context.newRecord.type);
         log.debug('afterSubmit context.Type', context.type);
         var toAllocate = false;
         if(context.newRecord.type == record.Type.VENDOR_BILL || context.newRecord.type == record.Type.VENDOR_CREDIT)
         {            
            var lineChanged = checkLines(context); // check if any of the lines have changed
            log.debug('lineChanged', lineChanged);
            if(lineChanged) {

                
                var isSVB = false;
                if(context.newRecord.type == record.Type.VENDOR_BILL){
                    var vbRecord = record.load({
                        type: record.Type.VENDOR_BILL,
                        id: context.newRecord.id,
                        isDynamic: true,
                    });

                    if(vbRecord.getValue('custbody_svb_schedule_is_intercompany'))
                    {               
                        isSVB = true;          
                    }
                }

                toAllocate = checkIfTransactionNeedsToBeAllocated(context, 'expense');     

                if(context.newRecord.getValue('custbody_alloc_transaction')) {

                    var allocJEId = context.newRecord.getValue('custbody_alloc_transaction');
                    
                    if(!toAllocate) {
                        record.delete({
                            type: record.Type.JOURNAL_ENTRY,
                            id: allocJEId
                        });
                    } else {
                        var srcTransactionRec = context.newRecord;
                        modifyAllocJE(context, srcTransactionRec, isSVB, allocJEId);
                    }

                } else if (toAllocate) {   
            
                    if(context.newRecord.type == record.Type.VENDOR_CREDIT || (context.newRecord.type == record.Type.VENDOR_BILL && !isSVB ))             
                    {                             
                        var srcTransactionRec = context.newRecord;
                        createAllocJE(context, srcTransactionRec, isSVB);                                                                                               
                    }

                }
            }
         }
    }
 
    return {
        afterSubmit: afterSubmit
    }
 });
 