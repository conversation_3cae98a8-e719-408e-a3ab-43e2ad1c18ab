/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/redirect', 'N/ui/serverWidget'], function(redirect, serverWidget) {

    function beforeLoad(context) {
        var request = context.request;
        if(request.parameters.hasOwnProperty("po_redirect")){
            var fromPORedirect = request.parameters.po_redirect;
            
            if(fromPORedirect) {
                var formObj = context.form;        
                
                var poRedirectField = formObj.addField({
                    id : 'custpage_po_redirect',
                    type : serverWidget.FieldType.CHECKBOX,
                    label : 'PO Redirect'
                });
                poRedirectField.updateDisplayType({
                    displayType : serverWidget.FieldDisplayType.HIDDEN
                });
                poRedirectField.defaultValue = 'T';
            }
            
        }

    }

    function beforeSubmit(context) {
        
    }

    function afterSubmit(context) {
        var recObj = context.newRecord;
        try {
            var poRedirectValue = recObj.getValue({ fieldId: 'custpage_po_redirect' });
            log.debug("poRedirectValue", poRedirectValue);
            if(poRedirectValue === 'T' || poRedirectValue === true) {
                redirect.toSuitelet({
                    scriptId: 'customscript_acs_sl_blank_page',
                    deploymentId: 'customdeploy_acs_sl_blank_page'
                });
            }
        } catch (e) {
            log.debug('Saved', 'Not redirect');
        }
        
    }

    return {
        beforeLoad: beforeLoad,
        beforeSubmit: beforeSubmit,
        afterSubmit: afterSubmit
    }
});
