/**
 * @version 1.0.0
 * @NApiVersion 2.x
 * @NScriptType UserEventScript
 */

define(['N/record'], function (record) {
    function afterSubmit(scriptContext) {
      try {
        if (scriptContext.type == 'delete') {
          return;
        }
  
        log.debug('In8', 'Record Id: ' + scriptContext.newRecord.id);
        loadedSalesOrder = record.load({type: 'salesorder', id: scriptContext.newRecord.id, isDynamic: true});
        loadedCustomer = record.load({type: 'customer', id: loadedSalesOrder.getValue('entity')});
  
        var sourceSite = loadedSalesOrder.getValue({fieldId: 'custbody_in8_source_website'}) || '';
  
        log.debug('In8', 'Source Site: ' + sourceSite);
        
        if (sourceSite.indexOf('https://assisianimalhealth.com') > -1) {
          log.debug('In8', 'Order from Assisi website. Do not run this code.');
          return;
        }
        var customerGroup = loadedCustomer.getValue({fieldId: 'custentity21'}); // Could be custentity_in8_customer_group
        log.debug('customerGroup',customerGroup + ', SO: ' + scriptContext.newRecord.id);
        // Veterinary Professional - 10; University/Educational - 4; Non-Profit - 5; Fear Free - 1;
  
        var addDiscount = false;
  
        log.debug('customerGroup.indexOf(1):  '+customerGroup.indexOf('1'));
        log.debug('customerGroup.indexOf(4):  '+customerGroup.indexOf('4'));
        log.debug('customerGroup.indexOf(5):  '+customerGroup.indexOf('5'));
        log.debug('customerGroup.indexOf(10):  '+customerGroup.indexOf('10'));
        
        if(customerGroup.indexOf('1') >= 0){        
          addDiscount =true;
        } 
        if(customerGroup.indexOf('4') >= 0){        
          addDiscount =true;
        } 
        if(customerGroup.indexOf('5') >= 0){        
          addDiscount =true;
        } 
        if(customerGroup.indexOf('10') >= 0){        
          addDiscount =true;
        } 
  
        log.debug('addDiscount:  '+addDiscount);
  
        //if(customerGroup.indexOf('1') || customerGroup.indexOf('4') || customerGroup.indexOf('5') || customerGroup.indexOf('10')) {
        if(addDiscount == true) {
          if(loadedSalesOrder.getValue({fieldId: 'total'}) > 1000) {
            for(var i=0; i<loadedSalesOrder.getLineCount({sublistId: 'item'}); i++) {
              if(loadedSalesOrder.getSublistValue({sublistId: 'item', fieldId: 'item', line: i}) == '106') {
                loadedSalesOrder.removeLine({sublistId: 'item', line: i, ignoreRecalc: false});
                // loadedSalesOrder.commitLine({sublistId: 'item', ignoreRecalc: false});
              }
            }
            log.debug('12% discount added SO:',scriptContext.newRecord.id);
            var additionalDiscount = -1 * (((loadedSalesOrder.getValue({fieldId: 'total'}) - 1000) * 12)/100);
            loadedSalesOrder.selectNewLine({sublistId: 'item'});
            loadedSalesOrder.setCurrentSublistValue({sublistId: 'item', fieldId: 'item', value: '106'});
            loadedSalesOrder.setCurrentSublistValue({sublistId: 'item', fieldId: 'price', value: '-1'});
            loadedSalesOrder.setCurrentSublistValue({sublistId: 'item', fieldId: 'quantity', value: '1'});
            loadedSalesOrder.setCurrentSublistValue({sublistId: 'item', fieldId: 'rate', value: additionalDiscount});
            loadedSalesOrder.commitLine({sublistId: 'item', ignoreRecalc: false});
            var savedSoId = loadedSalesOrder.save();
          }
        }
      } catch(up) {
        log.debug({title: 'Error in afterSubmit', details: up});
      }
    }
  
    return {
      afterSubmit: afterSubmit
    };
  });