require(["N/currentRecord", "N/query", "N/search"], function(record, query, search) {
        
    var recObj = record.get();
    var sqlQueryData = "SELECT id FROM transaction tran WHERE tran.custbody_nx_case = ?";
    var resultSet = query.runSuiteQL({
        query: sqlQueryData,
        params: [287528]
    });

    var results = resultSet.results;

    for(var i = 0; i < results.length; i++ ){
        console.log(results[i].values);
    }
});