//in8_returnly_rma_fullyreturned.js
//https://tstdrv1330281.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=1227&id=9&scrollid=9&whence=

function convertResponse(options) {
    // Load the 3PL API
    var api = new options.in8syncApi({
        configurationId: options.configurationId
    });
    var result = options.data;
    log.debug('map result', result);
    var shopifyID = result.relationships.return_line_items.data[0].attributes.ext_order_id;
    // var returnlyExtLineID = result.relationships.return_line_items.data[0].attributes.ext_order_line_item_id;
    var returnlyTrackingNumber = result.relationships.shipping_labels.data[0].attributes.tracking_number;

    if(shopifyID == 3754455203922) log.audit('shopifyID', shopifyID);

    api.findExistingRecord({
        recordtype: 'salesorder',
        filters: [
            ["type", "anyof", "SalesOrd"],
            "AND",
            ["mainline", "is", "T"],
            "AND",
            ["custbody_in8_shop_source", "isnot", "pos"],
            "AND",
            ["custbody_in8_shop_id", "is", shopifyID]
        ],
        doesnotexist: function () {
            log.debug('does not exist', shopifyID);
        },
        exists: function (transactionExportId) {
            log.audit('IT EXISTS!', 'IT EXISTS!');
            log.audit('shopifyID', shopifyID);
            // log.audit('returnlyExtLineID', returnlyExtLineID);
            log.debug('returnlyTrackingNumber', returnlyTrackingNumber);
            // log.emergency('transactionExportId', transactionExportId)
            var internalid = transactionExportId.getValue({
                fieldId: 'id'
            });
            // var trackingNo = transactionExportId.getValue({
            //     fieldId: 'custbody_in8_returnly_tracking_number'
            // });
            log.debug('internalid', internalid)

            var returnAuthorization = record.transform({
                fromType: 'salesorder',
                fromId: internalid,
                toType: 'returnauthorization'
            });
            returnAuthorization.setValue({
                fieldId: 'externalid',
                value: result.id
            });
            returnAuthorization.setValue({
                fieldId: 'custbody_in8_returnly_rma_number',
                value: result.id
            });
            returnAuthorization.setValue({
                fieldId: 'custbody_in8_returnly_tracking_number',
                value: returnlyTrackingNumber
            });

            var numLines = returnAuthorization.getLineCount({
                sublistId: 'item'
            });
            for (i = 0; i < numLines; i++) {
                var skuName = returnAuthorization.getSublistText({
                    sublistId: 'item',
                    fieldId: 'item',
                    line: i
                });
                var doesExist = false;
                for (a = 0; a < result.relationships.return_line_items.data.length; a++) {
                    if (skuName.indexOf(result.relationships.return_line_items.data[a].attributes.sku) > -1) {
                        returnAuthorization.setSublistValue({
                            sublistId: 'item',
                            fieldId: 'custcol_returnly_ext_order_item_id',
                            line: i,
                            value: result.relationships.return_line_items.data[a].attributes.ext_order_line_item_id
                        });
                        doesExist = true;
                    }
                }
                if (doesExist == false) {
                    returnAuthorization.removeLine({
                        sublistId: 'item',
                        line: i
                    });
                }
            }

            var raID = returnAuthorization.save()
            log.audit('raID', raID)
        },
        internalIdOnly: false
    });

    return {};
}