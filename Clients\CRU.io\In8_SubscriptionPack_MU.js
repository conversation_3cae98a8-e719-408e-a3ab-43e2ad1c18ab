/**
 * In8 - Item Mass Update
 * 
 * Version    Date            Author           Remarks
 * 1.00       31 Jan 2016     Marcel P		   Initial Version
 *
 */

/**
 * @param {String} recType Record type internal id
 * @param {Number} recId Record internal id
 * @returns {Void}
 */
function massUpdate(recType, recId) {

	try {			
		In8SyncSubscription.syncCustomRecord(recType, recId, false);						
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error synchronizing with WooCommerce', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e) + ' ' + e.stack);
	}
}
