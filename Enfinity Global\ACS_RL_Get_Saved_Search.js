/**
 *@NApiVersion 2.x
 *@NScriptType Restlet
 */
define(['N/search'], function(search) {

    function _get(context) {

        var trandate = context.trandate;
        var startdate = context.startdate;
        var enddate = context.enddate;
        var periodname = context.periodname;

        try {
            
            var glSearch = search.load({
                id: 'customsearch783'
            });
            if(trandate) {
                var tranDateFilter = search.createFilter({
                    name: 'trandate',
                    operator: search.Operator.ON,
                    values: [trandate]
                });
                glSearch.filters.push(tranDateFilter);            
            } 

            if(startdate && enddate){
                var tranDateRangeFilter = search.createFilter({
                    name: 'trandate',
                    operator: search.Operator.WITHIN,
                    values: [startdate, enddate]
                });
                glSearch.filters.push(tranDateRangeFilter);       
            }

            if(periodname) {
                var periodnameFilter = search.createFilter({
                    name: 'periodname',
                    join: 'accountingperiod',
                    operator: search.Operator.STARTSWITH,
                    values: [periodname]
                });
                glSearch.filters.push(periodnameFilter);            
            }

            var resultSet = glSearch.run();
            var searchResultCount = glSearch.runPaged().count;
            
            var results = [];
            var slice = [];
            var count = 0;
            var pageSize = 1000;
            var start = 0;

            do {
                slice = resultSet.getRange({ start: start, end: start + pageSize });
                slice.forEach(function(row) {
                    var resultObj = {
                        entityid: row.getValue({ name: 'entityid', join: 'vendor' }),
                        item: row.getText({ name: 'item' }),
                        account: row.getText({ name: 'account' }),
                        class: row.getText({ name: 'class' }),
                        trandate: row.getValue({ name: 'trandate' }),
                        periodname: row.getValue({ name: 'periodname', join: 'accountingperiod' }),
                        type: row.getText({ name: 'type' }),
                        subsidiary: row.getText({ name: 'subsidiary' }),
                        entity: row.getText({ name: 'entity' }),
                        csegcountry_code: row.getText({ name: 'csegcountry_code' }),
                        department: row.getText({ name: 'department' }),
                        memo: row.getValue({ name: 'memo' }),
                        fxamount: row.getValue({ name: 'fxamount' }),
                        currency: row.getText({ name: 'currency' }),
                        applyingtransaction: row.getText({ name: 'applyingtransaction' }),
                        amount: row.getValue({ name: 'amount', join: 'applyingtransaction' })
                    };
                    results.push(resultObj);
                    count++;
                });
                start += pageSize;
            } while (slice.length == pageSize);

            return JSON.stringify({
                success: 1,
                count: searchResultCount,
                result: results
            });

        } catch (e) {
            return JSON.stringify({
                success: 0,
                name: e.name,
                result: e.message
            });
        }
    }

    return {
        get: _get
    }
});
