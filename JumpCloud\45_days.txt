<span style="font-size:11pt"><span style="line-height:normal"><span style="font-family:Calibri,sans-serif">Dear
            Customer,</span></span></span><br />
<br />

<#if invoicegroup??>
<span style="font-size:11pt"><span style="line-height:normal"><span style="font-family:Calibri,sans-serif">This is a
            second email to inform you of an outstanding balance on your account of&nbsp;<b>${invoicegroup.amountdue}</b> for the Invoice Group <b>#${invoicegroup.invoicegroupnumber}</b>&nbsp;with
        the following invoices overdue:</span></span></span><br /><br />
<#else>
<span style="font-size:11pt"><span style="line-height:normal"><span style="font-family:Calibri,sans-serif">This is a
            second email to inform you of an outstanding balance on your account of&nbsp;<b>${customer.overduebalance}</b>&nbsp;with
            the following invoices overdue:</span></span></span><br /><br />
</#if>
<span>
        <#if invoicelist?has_content>
        <table border="1" style="font-family: sans-serif; font-size: 12px">
            <tr style="background-color:#e3e3e3">
                <th>Invoice #</th>
                <th>Description</th>
                <th>Invoice Date</th>
                <th>Due Date</th>
                <th>Invoice Currency</th>
                <th>Amount (Invoice Currency)</th>
            </tr>
                <#list invoicelist as invoice>
                <#if invoice.groupedto == "" || invoicegroup??>
                    <tr>
                    <td>${invoice.tranid}
                            <#if invoice.custbody_3805_dunning_procedure!="">*
                            </#if>
                    </td>

                    <td>${invoice.memo}</td>
                    <td>${invoice.trandate}</td>
                    <td>${invoice.duedate}</td>
                    <td>${invoice.currency}</td>
                    <td>${invoice.fxamountremaining?string.number}</td>

                    </tr>
                </#if>
            </#list>
                <#list invoicegrouplist as invoicegrouprecord>
                <tr style="background-color:#e3e3e3">
                <th>Invoice Group #${invoicegrouprecord.invoicegroupnumber}
                    <#if invoicegrouprecord.custrecord_dl_ig_dunning_procedure!="">*
                    </#if>
                </th>
                </tr>
                <#list invoicelist as invoice>
                <#if invoice.groupedto == invoicegrouprecord.invoicegroupnumber>
                <tr>
                <td>${invoice.tranid}<#if invoice.custbody_3805_dunning_procedure != "">*</#if></td>
                <td>${invoice.memo}</td>
                <td>${invoice.trandate}</td>
                <td>${invoice.duedate}</td>
                <td>${invoice.currency}</td>
                <td>${invoice.fxamountremaining?string.number}</td>
                </tr>
            </#if>
            </#list>
            </#list>

        </table>
        <br />
        <#if invoicegroup??>
        <span style="font-style: italic;">*A separate dunning letter may be sent for each marked invoice.</span>
        <#else>
        <span style="font-style: italic;">*A separate dunning letter may be sent for each marked invoice.</span>
        </#if>

        <#elseif invoice??>
            <!-- for invoice dunning -->

            <table border="1" style="font-family: sans-serif; font-size: 12px">
            <tr style="background-color:#e3e3e3">
                <th>Invoice #</th>
                <th>Description</th>
                <th>Invoice Date</th>
                <th>Due Date</th>
                <th>Currency</th>
                <th>Amount</th>
            </tr>

            <tr>
                <td>${invoice.tranid}</td>
                <td>${invoice.memo}</td>
                <td>${invoice.trandate}</td>
                <td>${invoice.duedate}</td>
                <td>${invoice.currency}</td>
                <td>${invoice.amountremaining}</td>
            </tr>
            </table>
        </#if>
</span>
<br />
<span style="font-size:11pt"><span style="line-height:normal"><span style="font-family:Calibri,sans-serif">If payment
            has been made, please forward the applicable payment details, and we will confirm receipt of payment. All
            overdue invoices are attached below.</span></span></span><br />
<br />
<span style="font-size:11pt"><span style="line-height:normal"><span style="font-family:Calibri,sans-serif">If you wish
            to pay with a credit card, please have your account administrator follow the instructions within
            this&nbsp;<a
                href="https://support.jumpcloud.com/support/s/article/billing-charges-and-invoices1-2019-08-21-10-36-47"
                target="_blank"><span style="color:blue">Billing, Charges, and Invoices link</span></a><span
                lang="EN-US" style="color:#500050">&nbsp;(</span><a
                href="https://support.jumpcloud.com/support/s/article/using-the-jumpcloud-multi-tenant-portal#Payments"
                target="_blank"><span style="color:blue">MTP portal users see here</span></a><span lang="EN-US"
                style="color:#500050">)&nbsp;</span>to set up a credit card payment. Let me know when you have added the
            card and we will process the payment for you.&nbsp;&nbsp;</span></span></span><br />
<br />
<span style="font-size:11pt"><span style="line-height:normal"><span style="font-family:Calibri,sans-serif">If you have
            any questions or concerns, please do not hesitate to reach out. Thank you for your continued
            business.</span></span></span><br />
&nbsp;