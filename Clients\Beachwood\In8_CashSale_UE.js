/**
 * Cash Sale - Before Submit
 * 
 * Version    Date            Author           Remarks
 * 1.00       28 Dec 2016     Marcel P
 *
 */

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      
 * @returns {Void}
 */
function cashSaleBeforeSubmit(type) {
	
	try {		
		// New Cash Sale, class Web Sale
		if ((type == 'create' || type == 'copy') && nlapiGetFieldValue('custbody_in8_wc_order_id')) {		
			nlapiSetFieldValue('undepfunds', 'F');
			nlapiSetFieldValue('account', 1); 
			
			nlapiLogExecution('DEBUG', 'Set', 'Account changed');
		}
	} catch(e) {
		nlapiLogExecution('DEBUG', 'Error', 'Error setting the account. ' + e);
	}
}
