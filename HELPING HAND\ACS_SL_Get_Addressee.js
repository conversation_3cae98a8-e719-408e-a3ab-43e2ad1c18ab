/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/record'], function(record) {

    function onRequest(context) {
        try {
            var entityid = context.request.parameters.entityid;
            var baserecordtype = context.request.parameters.baserecordtype;

            if(baserecordtype == 'vendorpayment'){
                var entityType = record.Type.VENDOR;
            } else if(baserecordtype == 'check'){
                var entityType = record.Type.CUSTOMER;
            }

            log.debug('baserecordtype', {baserecordtype: baserecordtype, entityid: entityid} );
            var entityRecord = record.load({
                id: entityid,
                type: entityType
            });
            
            log.debug(entityRecord.getValue({ fieldId: 'shipaddressee' }));

            var returnStr = '<#assign shipaddressee ="' + entityRecord.getValue({ fieldId: 'shipaddressee' }) + '" />'; 

            context.response.write({
                output: returnStr
            });
        } catch (e) {
            log.error('Error', e);
        }

    }

    return {
        onRequest: onRequest
    }
});
