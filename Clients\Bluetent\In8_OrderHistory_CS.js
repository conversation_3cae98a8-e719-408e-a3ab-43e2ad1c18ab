function payInvoice(id, name, userId, customer, hash, amount) {

    var url = nlapiResolveURL('SUITELET', 'customscript_in8_creditcards_sl', 'customdeploy_in8_creditcards_sl', true);

    url += '&userid=' + userId +
            '&custpage_customer=' + customer +
            '&hash=' + hash +
            '&amount=' + amount + 
            '&amountremaining=' + amount + 
            '&invoice=' + id + 
            '&invoicename=' + escape(name);
        
    // Open the popup
    nlExtOpenWindow(url, '', 500, 400, '', false, 'Pay Invoice');    
}

function search() {
    setWindowChanged(window, false);

    //var url = nlapiResolveURL('SUITELET', 'customscript_in8_order_history_sl', 'customdeploy_in8_order_history_sl');

    //window.location.href = url + getParameters() + '&search=T';

    var url = window.location.href.replace('custpage_tranid', 'z');
    url = url + getParameters();
    window.location.href = url;
}

function getParameters() {

    var parameters = "&custpage_tranid=" + nlapiGetFieldValue('custpage_tranid');

    return parameters;
}