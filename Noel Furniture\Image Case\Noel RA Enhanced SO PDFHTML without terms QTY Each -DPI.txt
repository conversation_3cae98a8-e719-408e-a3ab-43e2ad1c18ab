<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
	<link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
	<#if .locale == "zh_CN">
		<link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
	<#elseif .locale == "zh_TW">
		<link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
	<#elseif .locale == "ja_JP">
		<link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
	<#elseif .locale == "ko_KR">
		<link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
	<#elseif .locale == "th_TH">
		<link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
	</#if>
    <macrolist>
        <macro id="nlheader">
            <table style="width: 100%; font-size: 10pt;"><tr>
	<td rowspan="3" style="padding: 0; width: 100px"><#assign companyLogo = "https://249816.app.netsuite.com/core/media/media.nl?id=6426&c=249816&h=51a7e6e5e647d77a63b5"?html><#if companyInformation.logoUrl?length !=0><@filecabinet nstype="image" src="${companyLogo}" style="width: 300px; height: 75px" /> </#if></td>
	<td align="right" style="padding: 0; width: 400px; font-size: 16pt">${record@title}</td>
	</tr>
	<tr>
	<td align="right" style="font-size: 16pt;">${record.tranid}</td>
	</tr>
	<tr>
	<td align="right">${record.trandate}</td>
	</tr></table>
        </macro>
        <macro id="nlfooter">
            <table style="width: 100%; font-size: 8pt;"><tr>
	<td style="padding: 0;"><barcode codetype="code128" showtext="true" value="${record.tranid}" /></td>
	</tr></table>
        </macro>
    </macrolist>
    <style type="text/css">* {
        <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
        <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
        <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
        <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
        <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
        <#else>font-family: NotoSans, sans-serif;
        </#if>
      }

      table {
        font-size: 9pt;
        table-layout: fixed;
      }

      th {
        font-weight: bold;
        font-size: 8pt;
        vertical-align: middle;
        padding: 5px 6px 3px;
        background-color: #e3e3e3;
        color: #333333;
      }

      td {
        padding: 4px 6px;
      }

      td p {
        align: left
      }
</style>
</head>
<body header="nlheader" header-height="10%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
    <table style="width: 100%; margin-top: 10px;"><tr>
	<td colspan="3" style="font-size: 8pt; padding: 6px 0 2px; font-weight: bold; color: #333333;">Bill To:</td>
	<td colspan="3" style="font-size: 8pt; padding: 6px 0 2px; font-weight: bold; color: #333333;">Ship To:</td>
	</tr>
	<tr>
	<td colspan="3" style="padding: 0;">${record.billaddress}<br />${record.billphone}</td>
	<td colspan="3" style="padding: 0;">${record.shipaddress}</td>
	</tr></table>

<table style=" width: 100%; margin-top: 10px;"><tr>
	<th>Customer #</th>
	<th>Designer</th>
	<th>Order Type</th>
	<th>Delivery Date</th>
	<th>Delivery Method</th>
	</tr>
	<tr>
	<td style="padding-top: 2px;">${record.entity.id}</td>
	<td style="padding-top: 2px;">${record.salesrep}</td>
	<td style="padding-top: 2px;">${record.custbody_noel_saletype}</td>
	<td style="padding-top: 2px;">${record.shipdate}</td>
	<td style="padding-top: 2px;">${record.shipmethod}</td>
	</tr></table>
<#if record.item?has_content>

<table style="width: 100%; margin-top: 5px;"><!-- start items -->
<#list record.item as item>
	<#if item_index==0>
		<thead>
			<tr>
			<th align="center" colspan="3" style="width: 10%">${item.quantity@label}</th>
			<th align="center" style="padding: 10px 6px; width: 20%;">Image</th>
			<th align="center" colspan="12" style="padding: 10px 6px; width: 30%;">Item Description</th>
			<th align="center" style="padding: 10px 6px; width: 13.33%;">Retail</th>
			<th align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">Sale Price</th>
			<th align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">${item.amount@label}</th>
			</tr>
		</thead>
	</#if>
	<#if item.quantity?string!='0'>
		<tr>
			<td align="center" colspan="3" style="padding: 15px 6px; width: 10%;">${item.quantity}</td>
			<#if item.custcol_noel_trnslinefld_imageurl?has_content>
				<#assign image_loc = item.custcol_noel_trnslinefld_imageurl?replace("&amp;", "&#38;")>
				<#assign image_url = "https://249816.app.netsuite.com${image_loc}">
				<#assign test = image_url?index_of("ffef7d41ffc2bce8ba58")>
				<#if image_url?index_of("ffef7d41ffc2bce8ba58") == -1>
					<td align="center" class="parent" style="padding: 10px 6px; width: 20%;"><@filecabinet nstype="image" src="${image_url}" dpi="500"/></td>
				<#else>
					<td align="center" class="parent" style="padding: 10px 6px; width: 10%;"><@filecabinet nstype="image" src="${image_url}" style="width: 100px; height: 100px;"/></td>
				</#if>
			<#else>
				<#assign image_url = "https://249816.app.netsuite.com/core/media/media.nl?id=3203&c=249816_SB1&h=20c1ec7e1d2c1009fe41"?html>
				<td align="center" class="parent" style="padding: 10px 6px; width: 10%;"><@filecabinet nstype="image" src="${image_url}" style="width: 100px; height: 100px;"/></td>
			</#if>
			<td align="left" colspan="12" style="padding: 10px 6px; width: 40%;"><span style="font-weight: bold; line-height: 150%; color: #333333;">${item.item}</span><br />${item.description}</td>
			<td align="center" style="padding: 10px 6px; width: 13.33%;">${(item.custcol_noel_retail_price_rate)?string.currency}</td>
			<td align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">${item.rate}</td>
			<td align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">${item.amount}</td>
		</tr>
	</#if>
</#list>
<!-- end items -->
</table>
<hr style="width: 100%; color: #d3d3d3; background-color: #d3d3d3; height: 1px;" /></#if>
<table width="100%"><tr width="100%">
	<td width="55%">
	<table align="left" style="width: 100%; margin-top: 0px;"><tr>
		<td align="left" style="font-weight: bold; color: #333333;">Signature below indicates that merchandise was received in good condition. Please check merchandise carefully. Noel Furniture is not liable for damage once merchandise is removed from premises.</td>
		</tr>
		<tr>
		<td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
		</tr>
		<tr>
		<td align="left" style="font-weight: bold; color: #333333;">SIGNATURE____________________________________________________________</td>
		</tr>
		<tr>
		<td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
		</tr>
		<tr>
		<td align="left" style="font-weight: bold; color: #333333;">DATE____________________________________________</td>
		</tr>
		<tr>
		<td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
		</tr></table>
	</td>
	<td width="45%">
	<table align="left" style="page-break-inside: avoid; width: 100%; margin-top: 0px;"><tr>
		<td align="left" style="font-weight: bold; color: #333333;">${record.subtotal@label}</td>
		<td align="right">${record.subtotal}</td>
		</tr>
		<tr>
		<td align="left" style="font-weight: bold; color: #333333;">${record.taxtotal@label} (${record.taxrate}%)</td>
		<td align="right">${record.taxtotal}</td>
		</tr>
		<tr style="background-color: #e3e3e3; line-height: 200%;">
		<td align="left" style="font-weight: bold; color: #333333;">${record.total@label}</td>
		<td align="right">${record.total}</td>
		</tr>
		<tr>
		<td align="left" style="font-weight: bold; color: #333333;">Deposit</td>
		<td align="right">${record.custbody2}</td>
		</tr>
		<tr>
		<td align="left" style="font-weight: bold; color: #333333;">Payment Method</td>
		<td align="right">${record.custbody_noel_payment_method}</td>
		</tr>
		<tr style="background-color: #e3e3e3; line-height: 200%;">
		<td align="left" style="font-weight: bold; color: #333333;">Balance Due</td>
		<td align="right">${record.custbody_nsacs_amountdue}</td>
		</tr></table>
	</td>
	</tr></table>

<table style="width: 100%; margin-top: 0px;"><tr>
	<td align="center">THANK YOU FOR SHOPPING LOCAL</td>
	</tr>
	<tr>
	<td align="center">67 CENTS OF EVERY DOLLAR SPENT REMAINS IN OUR LOCAL COMMUNITY</td>
	</tr>
	<tr>
	<td align="center" style="font-weight: bold; color: #333333;">No&euml;l Home | 2727 Southwest Freeway| Houston, TX 77098 |713.874.5200</td>
	</tr>
	<tr>
	<td align="center" style="font-weight: bold; color: #333333;">See Terms On Reverse</td>
	</tr></table>
</body>
</pdf>