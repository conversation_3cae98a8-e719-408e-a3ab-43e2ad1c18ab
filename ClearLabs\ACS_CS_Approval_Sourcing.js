/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord', 'N/record'], function(currRecord, record) {

    function saveRecord(context) {
        var currRecordObj = context.currentRecord;
        if(currRecordObj.id){
            var approvalStatus = currRecordObj.getValue({ fieldId: 'custpage_sas_approval_status' });
            var nextApprover = currRecordObj.getValue({ fieldId: 'custpage_sas_next_approver' });

            var recordObj = record.load({
                id: currRecordObj.id,
                type: record.Type.SALES_ORDER,
                isDynamic: true
            });

            recordObj.setValue({ fieldId: 'custbody_acs_approval_status', value: approvalStatus });
            recordObj.setValue({ fieldId: 'custbody_acs_next_approver', value: nextApprover });
            recordObj.save();
            log.debug({
                title: 'test',
                details: {
                    approval_status: approvalStatus,
                    next_approver: nextApprover
                }
            });
        }
        return true;
    }

    return {
        saveRecord: saveRecord
    }
});
