<nsResponse>
<record recordType="customrecord_2663_payment_file_format" id="83" perm="4" fields="_eml_nkey_,_multibtnstate_,selectedtab,nsapiPI,nsapiSR,nsapiVF,nsapiFC,nsapiPS,nsapiVI,nsapiVD,nsapiPD,nsapiVL,nsapiRC,nsapiLI,nsapiLC,nsapiCT,nsbrowserenv,wfPI,wfSR,wfVF,wfFC,wfPS,type,id,externalid,whence,customwhence,entryformquerystring,_csrf,wfinstances,name,owner,isinactive,custrecord_2663_payment_file_type,custrecord_2663_format_country,custrecord_2663_include_all_currencies,custrecord_2663_ref_fields,custrecord_2663_entity_ref_fields,custrecord_2663_field_validator,custrecord_2663_native_format,custrecord_2663_max_lines,custrecord_2663_format_currency,custrecord_13272_sorting_sepadd,custrecord_2663_update_entity_details,custrecord_12144_free_marker_body_st,custrecord_2663_free_marker_body,custrecord_2663_output_file_extension,custrecord_2663_output_file_encoding,custrecord_12194_localized_format,nameorig,scriptid,ownerid,custrecord_2663_file_fields,custrecord_2663_use_free_marker,custrecord_12194_app_id,rectype,linenumber,version,submitnext_t,submitnext_y,nluser,nlrole,nldept,nlloc,nlsub,baserecordtype,custpage_dad,custpage_dad_sublists,custpage_dad_rolename,custpage_2663_format_currency,custpage_translation_strings">
<_csrf>0I4hgqiv63LR6z6J8raZIpcBZKXLUBUXcJqEM1OLid43VkEVs02BJZ_p_FsXy37RYtR7cB3v1A7VKXSk7Dt_0zsP9R00VnL8Ti6405hVICJQXWkLhbzyqmlcwSd7zCpzyiCdEzxucsSEvBJ-OgNu2mzeXZm2ndxWRfGVWRosPA8=</_csrf>
<_eml_nkey_>1115163_SB3~657185~3~N</_eml_nkey_>
<baserecordtype>customrecord_2663_payment_file_format</baserecordtype>
<custpage_dad_sublists>["mediaitem"]</custpage_dad_sublists>
<custpage_translation_strings>{"maxlines":"Maximum Payments in File","maxlineexceed":"Maximum Lines field value exceeded. The value must be less than or equal to ","sameformat":"Format with the same name and type already exists. Please save this format with a different name.","advtmplnoval":"Please enter a value for Bank File Template.","multicurrmsg":"The Payment File Type and Currencies set will no longer be editable. Are you sure you want to continue?","tmplnoval":"Please enter a value for Bank File Template or SuiteTax Bank File Template field.","warnsuitetax":"The Bank File Template field will soon be deprecated. In preparation for this change, we recommend using the SuiteTax Bank File Template field to enter your custom payment template.For more information, see the Payment File Template Change in NetSuite Electronic Bank Payments 19.1 topic in the Help Center."}</custpage_translation_strings>
<custrecord_12194_localized_format>F</custrecord_12194_localized_format>
<custrecord_13272_sorting_sepadd>F</custrecord_13272_sorting_sepadd>
<custrecord_2663_entity_ref_fields><refFields type="BofA - Pain 001.001.03 (Brazil)"> <refField id="custrecord_2663_entity_bic" label="BIC" /> <refField id='custrecord_2663_entity_iban' label='IBAN' /> <refField id='custrecord_2663_entity_acct_no' label='Bank Account Number' /> <refField id='custrecord_2663_entity_bank_code' label='Bank ID' /> <refField id='custrecord_2663_entity_branch_no' label='Branch ID' /> <refField id='custrecord_2663_entity_country_code' label='Country Code' /> </refFields></custrecord_2663_entity_ref_fields>
<custrecord_2663_field_validator><fieldValidatorList> <fieldValidator> <fieldName>custrecord_2663_bic</fieldName> <validatorList> <validator type="len"> <param name="validLength">8|11</param> </validator> <validator type='custom' /> </validatorList> </fieldValidator> <fieldValidator> <fieldName>custrecord_2663_iban</fieldName> <validatorList> <validator type='custom' /> </validatorList> </fieldValidator> <fieldValidator> <fieldName>custrecord_2663_statement_name</fieldName> <validatorList> <validator type="len"> <param name="maxLength">70</param> </validator> </validatorList> </fieldValidator> <fieldValidator> <fieldName>custrecord_2663_bank_address1</fieldName> <validatorList> <validator type="len"> <param name="maxLength">70</param> </validator> </validatorList> </fieldValidator> <fieldValidator> <fieldName>custrecord_2663_bank_address2</fieldName> <validatorList> <validator type="len"> <param name="maxLength">70</param> </validator> </validatorList> </fieldValidator> <fieldValidator> <fieldName>custrecord_2663_entity_bic</fieldName> <validatorList> <validator type="len"> <param name="validLength">8|11</param> </validator> <validator type='custom' /> </validatorList> </fieldValidator> <fieldValidator> <fieldName>custrecord_2663_entity_iban</fieldName> <validatorList> <validator type='custom' /> </validatorList> </fieldValidator> <fieldValidator> <fieldName>custrecord_2663_entity_acct_no</fieldName> <validatorList> <validator type='custom' /> </validatorList> </fieldValidator> <fieldValidator> <fieldName>custrecord_2663_entity_bank_code</fieldName> <validatorList> <validator type="len"> <param name="validLength">3|3</param> </validator> <validator type='custom' /> </validatorList> </fieldValidator> <fieldValidator> <fieldName>custrecord_2663_entity_branch_no</fieldName> <validatorList> <validator type="len"> <param name="validLength">4|4</param> </validator> <validator type='custom' /> </validatorList> </fieldValidator> <fieldValidator> <fieldName>custrecord_2663_processor_code</fieldName> <validatorList> <validator type="len"> <param name="maxLength">20</param> </validator> </validatorList> </fieldValidator> </fieldValidatorList></custrecord_2663_field_validator>
<custrecord_2663_format_country>30</custrecord_2663_format_country>
<custrecord_2663_free_marker_body><#-- format specific processing --> <#function getReferenceNote payment> <#assign paidTransactions = transHash[payment.internalid]> <#assign referenceNote = ""> <#assign paidTransactionsCount = paidTransactions?size> <#if (paidTransactionsCount >= 1)> <#list paidTransactions as transaction> <#if transaction.tranid?has_content> <#if referenceNote?has_content> <#assign referenceNote = referenceNote + ", " + transaction.tranid> <#else> <#assign referenceNote = transaction.tranid> </#if> </#if> </#list> </#if> <#return referenceNote> </#function> <#function removeTaxRegSymbols text> <#assign value = text> <#assign value = value?replace('-','')> <#assign value = value?replace('/','')> <#assign value = value?replace('.','')> <#return value> </#function> <#-- cached values --> <#assign totalAmount = computeTotalAmount(payments)> <#if totalAmount < 5000> <#assign serviceLevel = 'NURG'> <#else> <#assign serviceLevel = 'URGP'> </#if> <#-- template building --> #OUTPUT START# <?xml version="1.0" encoding="UTF-8"?> <Document xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> <CstmrCdtTrfInitn> <GrpHdr> <MsgId>${cbank.custrecord_2663_file_name_prefix}${pfa.name}</MsgId> <CreDtTm>${pfa.custrecord_2663_file_creation_timestamp?date?string("yyyy-MM-dd")}T${pfa.custrecord_2663_file_creation_timestamp?time?string("HH:mm:ss")}</CreDtTm> <NbOfTxs>${payments?size?c}</NbOfTxs> <CtrlSum>${formatAmount(totalAmount,"decLessThan1")}</CtrlSum> <InitgPty> <Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm> <Id> <OrgId> <Othr> <Id>${cbank.custpage_eft_custrecord_2663_bank_comp_id}</Id> <SchmeNm> <Cd>CUST</Cd> </SchmeNm> </Othr> </OrgId> </Id> </InitgPty> </GrpHdr> <PmtInf> <PmtInfId>${pfa.id}-1</PmtInfId> <PmtMtd>TRF</PmtMtd> <BtchBookg>false</BtchBookg> <NbOfTxs>${payments?size?c}</NbOfTxs> <CtrlSum>${formatAmount(totalAmount,"decLessThan1")}</CtrlSum> <PmtTpInf> <SvcLvl> <Cd>${serviceLevel}</Cd> </SvcLvl> </PmtTpInf> <ReqdExctnDt>${pfa.custrecord_2663_process_date?string("yyyy-MM-dd")}</ReqdExctnDt> <Dbtr> <Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm> <PstlAdr> <Ctry>${cbank.custpage_eft_custrecord_2663_country_code}</Ctry> <AdrLine>${cbank.custpage_eft_custrecord_2663_bank_address1} ${cbank.custpage_eft_custrecord_2663_bank_address2}</AdrLine> </PstlAdr> <Id> <OrgId> <Othr> <Id>${cbank.custpage_eft_custrecord_2663_processor_code}</Id> <SchmeNm> <Prtry>CONVENIO</Prtry> </SchmeNm> </Othr> </OrgId> </Id> </Dbtr> <DbtrAcct> <Id> <Othr> <Id>${cbank.custpage_eft_custrecord_2663_acct_num}</Id> </Othr> </Id> </DbtrAcct> <DbtrAgt> <FinInstnId> <BIC>${cbank.custpage_eft_custrecord_2663_bic}</BIC> <Nm>${cbank.custpage_eft_custrecord_2663_bank_name}</Nm> <PstlAdr> <Ctry>${cbank.custpage_eft_custrecord_2663_country_code}</Ctry> </PstlAdr> </FinInstnId> <BrnchId> <Id>${cbank.custpage_eft_custrecord_2663_branch_num}</Id> </BrnchId> </DbtrAgt> <ChrgBr>SHAR</ChrgBr> <#list payments as payment> <#assign ebank = ebanks[payment_index]> <#assign entity = entities[payment_index]> <CdtTrfTxInf> <PmtId> <InstrId>${pfa.id}-${payment.tranid}</InstrId> <EndToEndId>${payment.tranid}</EndToEndId> </PmtId> <Amt> <InstdAmt Ccy="${getCurrencySymbol(cbank.custrecord_2663_currency)}">${formatAmount(getAmount(payment),"decLessThan1")}</InstdAmt> </Amt> <CdtrAgt> <FinInstnId> <ClrSysMmbId><MmbId>${ebank.custrecord_2663_entity_bank_code}${ebank.custrecord_2663_entity_branch_no}</MmbId></ClrSysMmbId> <PstlAdr> <Ctry>${ebank.custrecord_2663_entity_country_code}</Ctry> </PstlAdr> </FinInstnId> </CdtrAgt> <Cdtr> <Nm>${setMaxLength(convertToLatinCharSet(buildEntityName(entity)),70)}</Nm> <PstlAdr> <Ctry>${ebank.custrecord_2663_entity_country_code}</Ctry> </PstlAdr> <Id> <#if entity.isperson?string == "No"> <OrgId> <Othr> <Id>${removeTaxRegSymbols(entity.vatregnumber)}</Id> <SchmeNm> <Cd>TXID</Cd> </SchmeNm> </Othr> </OrgId> <#else> <PrvtId> <Othr> <Id>${removeTaxRegSymbols(entity.vatregnumber)}</Id> <SchmeNm> <Cd>TXID</Cd> </SchmeNm> </Othr> </PrvtId> </#if> </Id> </Cdtr> <CdtrAcct> <Id> <Othr> <Id>${ebank.custrecord_2663_entity_acct_no}</Id> </Othr> </Id> </CdtrAcct> <#if serviceLevel == 'URGP'> <Purp> <Cd>P41</Cd> </Purp> </#if> </CdtTrfTxInf> </#list> </PmtInf> </CstmrCdtTrfInitn> </Document><#rt> #OUTPUT END#</custrecord_2663_free_marker_body>
<custrecord_2663_include_all_currencies>F</custrecord_2663_include_all_currencies>
<custrecord_2663_max_lines>2000</custrecord_2663_max_lines>
<custrecord_2663_native_format>F</custrecord_2663_native_format>
<custrecord_2663_output_file_encoding>1</custrecord_2663_output_file_encoding>
<custrecord_2663_output_file_extension>XML</custrecord_2663_output_file_extension>
<custrecord_2663_payment_file_type>1</custrecord_2663_payment_file_type>
<custrecord_2663_ref_fields><refFields type="BofA - Pain 001.001.03 (Brazil)"> <refField id="custrecord_2663_bank_comp_id" label="Company Id" mandatory="true" helptext="Enter the code the bank uses to identify your company. This is assigned by the bank."/> <refField id="custrecord_2663_branch_num" label="Branch Code" mandatory="true" helptext="Enter the code the bank uses to identify fund transfers from your company. This is assigned by the bank."/> <refField id="custrecord_2663_iban" label="IBAN" mandatory="true" helptext="Enter your company's International Bank Account Number (IBAN)."/> <refField id="custrecord_2663_bank_address1" label="Address Line 1" mandatory="true" helptext="Enter the street address of your company's bank."/> <refField id="custrecord_2663_bank_address2" label="Address Line 2" mandatory="true" helptext="Enter the building, city, or town address of your company's bank."/> <refField id="custrecord_2663_country_code" label="Country" helptext="Select the country where your company's bank is located."/> <refField id="custrecord_2663_statement_name" label="Company Name" mandatory="true" helptext="Enter your company's name."/> <refField id="custrecord_2663_bic" label="BIC" mandatory="true" helptext="Enter the Business Identifier Code (BIC), also called SWIFT code, of your company's bank (eight or 11 characters). "/> <refField id="custrecord_2663_acct_num" label="Bank Account Number" helptext="Enter the bank account number. "/> <refField id="custrecord_2663_bank_code" label="Bank Code" helptext="Enter the bank code or sort code. "/> <refField id="custrecord_2663_bank_name" label="Bank Name" helptext="Enter the bank name. "/> <refField id="custrecord_2663_processor_code" label="Bank Agreement Code" mandatory="false" helptext="Enter your company's agreement code with the bank. You can enter up to 20 characters."/> </refFields></custrecord_2663_ref_fields>
<custrecord_2663_update_entity_details>F</custrecord_2663_update_entity_details>
<custrecord_2663_use_free_marker>T</custrecord_2663_use_free_marker>
<entryformquerystring>rectype=683&id=83&xml=t</entryformquerystring>
<id>83</id>
<isinactive>F</isinactive>
<linenumber>83</linenumber>
<name>BofA - Pain 001.001.03 (Brazil NSACS)</name>
<nameorig>BofA - Pain 001.001.03 (Brazil NSACS)</nameorig>
<nldept>20</nldept>
<nlloc>0</nlloc>
<nlrole>3</nlrole>
<nlsub>5</nlsub>
<nluser>657185</nluser>
<nsapiCT>*************</nsapiCT>
<owner>20171</owner>
<ownerid>20171</ownerid>
<rectype>683</rectype>
<scriptid>val_11293372_1115163_sb3_629</scriptid>
<submitnext_t>customrecordentry</submitnext_t>
<submitnext_y>683</submitnext_y>
<type>custrecordentry</type>
</record>
</nsResponse>