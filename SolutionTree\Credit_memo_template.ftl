<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td><span style="font-size:11px;">${subsidiary.logo}</span></td>
                        <td rowspan="5"><br /><br /><br /><span
                                style="background-color: rgb(255, 255, 255); font-size: 11px;">Phone:
                                ${subsidiary.addrphone}</span><br
                                style="font-size: 13.3333px; background-color: rgb(255, 255, 255);" /><span
                                style="background-color: rgb(255, 255, 255); font-size: 11px;">Fax:
                                ${subsidiary.fax}</span></td>
                        <td rowspan="5"><br /><br /><br /><span
                                style="font-size:11px;">${subsidiary.custrecordweb}</span></td>
                        <td align="right" rowspan="2"><span style="font-size:24px;"><strong>Credit Memo</strong></span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="1" rowspan="4"><span
                                style="font-size: 11px; background-color: rgb(255, 255, 255);">${subsidiary.addrText}</span>
                        </td>
                    </tr>
                    <tr>
                        <td align="right"><span style="font-size:16px;"><span
                                    class="number">#${record.tranid}</span></span></td>
                    </tr>
                    <tr>
                        <td align="right" rowspan="2"><span style="font-size:14px;">${record.trandate}</span></td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <p><br /><br />&nbsp;</p>
            </macro>
        </macrolist>
        <style type="text/css">
            table {
                <#if .locale=="zh_CN">font-family: stsong, sans-serif;
                <#elseif .locale=="zh_TW">font-family: msung, sans-serif;
                <#elseif .locale=="ja_JP">font-family: heiseimin, sans-serif;
                <#elseif .locale=="ko_KR">font-family: hygothic, sans-serif;
                <#elseif .locale=="ru_RU">font-family: verdana;
                <#else>font-family: sans-serif;
                </#if>font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0px;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0px;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="10%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        <br /><br />&nbsp;
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
                <td class="addressheader" colspan="3"><b>${record.shipaddress@label}</b></td>
                <td class="totalboxtop" colspan="5"><b>${record.amountremaining@label?upper_case}</b></td>
            </tr>
            <tr>
                <td class="address" colspan="3" rowspan="2">${record.billaddress}</td>
                <td class="address" colspan="3" rowspan="2">${record.shipaddress}</td>
                <td align="right" class="totalboxmid" colspan="5"><span
                        style="font-size:20px;">${record.amountremaining}</span></td>
            </tr>
            <tr>
                <td align="right" class="totalboxbot" colspan="5">&nbsp;</td>
            </tr>
        </table>

        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th>${record.otherrefnum@label}</th>
                <th>Customer ID</th>
                <th>${record.shipmethod@label}</th>
            </tr>
            <tr>
                <td style="text-align: center;">${record.otherrefnum}</td>
                <td style="text-align: center;">${record.custbody13}</td>
                <td style="text-align: center;">${record.shipmethod}</td>
            </tr>
        </table>
        <#if record.item?has_content>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th colspan="13">${item.item@label}</th>
                                <th colspan="4" rowspan="1" style="text-align: left;">${item.rate@label}</th>
                                <th colspan="4" rowspan="1" style="text-align: left;">${item.quantity@label}</th>
                                <th colspan="4" rowspan="1" style="text-align: left;">Extension</th>
                            </tr>
                        </thead>
                    </#if>
                    <#assign itemrate=item.rate />
                    <#if (itemrate < 0)>
                        <#assign itemratepositive=(itemrate*(-1)) />
                        <#else>
                            <#assign itemratepositive=(itemrate *1) />
                    </#if>
                    <#if (item.custcol_item_type)?contains("Discount")>
                        <#assign itemratec=(itemratepositive*100) />
                        <#if itemratec gt 100>
                            <#--  <#assign itemratedoubleround=(itemratec?number)?round />  -->
                            <#--  <#assign itemratediv=(itemratec/2) />  -->
                            <#assign itemrate="(" + itemratec?string(",##0.00") + ")" />
                        <#else>
                            <#assign itemrate="(" + itemratec?string[",##0.00;; roundingMode=halfUp"] + "%" + ")" />
                        </#if>
                    </#if>
                    <tr>
                        <td colspan="13"><span class="itemname">${item.item}</span><br />${item.description}</td>
                        <td colspan="4" rowspan="1" style="width: 20px; text-align: justify;">${itemrate}</td>
                        <td colspan="4" rowspan="1" style="text-align: justify;">${item.quantity}</td>
                        <td colspan="4" rowspan="1" style="text-align: justify;">${item.amount}</td>
                    </tr>
                </#list><!-- end items -->
            </table>

            <hr />
        </#if><span style="font-size:11px;">${record.message}</span><br /><br /><span
            style="font-size:11px;">${record.custbody8}</span><br /><br /><span
            style="font-size:11px;">${record.custbody16}</span><br />
        <#if record.subsidiary="Marzano Resources"><br /><span style="font-size:11px;">Effective June 11, 2019, Marzano
                Research LLC has changed its name to Marzano Resources LLC. Please update your records accordingly. For
                more information or to obtain an updated W-9, please visit www.MarzanoResources.com.</span></#if>&nbsp;
        <#if record.tax2total?has_content>

            <table class="total" style="width: 100%; margin-top: 10px;">
                <tr>
                    <th><span style="font-size:10px;"><strong>${record.subtotal@label}</strong></span></th>
                    <th><span style="font-size:10px;">GST</span></th>
                    <th><span style="font-size:10px;">PST</span></th>
                    <th><span style="font-size:10px;"><strong>${record.shippingcost@label}</strong></span></th>
                    <th><span style="font-size:10px;"><strong>${record.total@label}</strong></span></th>
                    <th align="right"><span style="font-size:10px;"><b
                                style="text-align: -webkit-right; background-color: rgb(227, 227, 227);">Amount
                                Applied</b></span></th>
                    <th align="right"><span style="font-size:10px;">Residual Amount</span></th>
                </tr>
                <tr>
                    <td><span style="font-size:10px;">${record.subtotal}</span></td>
                    <td><span style="font-size:10px;">${record.taxtotal}</span></td>
                    <td><span style="font-size:10px;">${record.tax2total}</span></td>
                    <td><span style="font-size:10px;">${record.shippingcost}</span></td>
                    <td><span style="font-size:10px;">${record.total}</span></td>
                    <td align="right"><span style="font-size:10px;"><span
                                style="text-align: -webkit-right; background-color: rgb(255, 255, 255);">${record.amountpaid}</span></span>
                    </td>
                    <td align="right"><span style="font-size:10px;"><span style="text-align: -webkit-right;"><span
                                    style="background-color:#FFFFFF;">${record.amountremaining}</span></span></span>
                    </td>
                </tr>
            </table>
            <#else>

                <table class="total" style="width: 100%; margin-top: 10px;">
                    <tr>
                        <th><strong>${record.subtotal@label}</strong></th>
                        <th>Sales&nbsp;<strong>${record.taxtotal@label}</strong></th>
                        <th><strong>${record.shippingcost@label}</strong></th>
                        <th><strong>${record.total@label}</strong></th>
                        <th align="right"><b
                                style="font-size: 10.6667px; text-align: -webkit-right; background-color: rgb(227, 227, 227);">Amount
                                Applied</b></th>
                        <th align="right"><b
                                style="text-align: -webkit-right; background-color: rgb(227, 227, 227);">${record.amountremaining@label}</b>
                        </th>
                    </tr>
                    <tr>
                        <td>${record.subtotal}</td>
                        <td>${record.taxtotal}</td>
                        <td>${record.shippingcost}</td>
                        <td>${record.total}</td>
                        <td align="right"><span
                                style="text-align: -webkit-right; background-color: rgb(255, 255, 255);">${record.amountpaid}</span>
                        </td>
                        <td align="right"><span style="text-align: -webkit-right;"><span
                                    style="background-color:#FFFFFF;">${record.amountremaining}</span></span></td>
                    </tr>
                </table>
        </#if><br /><br />&nbsp;
        <#assign TBDCount=0 />
        <#assign attendeeList=[{"col0":""}] />
        <#attempt>
            <#if record.custpage_json_to_print?has_content && record.custpage_json_to_print
                !='Lorem ipsum dolor sit amet consectetuer ac orci sociis ornare laoreet.'>
                <#assign attendeeList=(''+record.custpage_json_to_print)?eval />
            </#if>
            <#recover>
        </#attempt>
        <#if attendeeList?has_content>
            <table style="width: 100%; margin-top: 10px;">
                <thead>
                    <tr>
                        <th colspan="15" style="padding: 10px 6px;">Attendees</th>
                    </tr>
                </thead>
                <#assign TBDCount=0 />
                <#list attendeeList as invlines>
                    <#assign attendee=invlines.col0 />
                    <#if attendee=='TBD'>
                        <#assign TBDCount=TBDCount + 1 />
                        <#else>
                            <tr>
                                <td align="left" colspan="5">${attendee}</td>
                            </tr>
                    </#if>
                </#list>
                <#if TBDCount !=0>
                    <tr>
                        <td align="left" colspan="5">TBD Count ${TBDCount}</td>
                    </tr>
                </#if>
            </table>
        </#if>
    </body>
</pdf>