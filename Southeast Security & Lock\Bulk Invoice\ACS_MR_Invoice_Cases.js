/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/runtime', 'N/search', 'N/record', 'N/query'], function(runtime, search, record, query) {

    var scriptObj = runtime.getCurrentScript();

    function getTimeEntries(caseId) {
        var sqlQueryData = "SELECT tb.item, tb.hours FROM timebill tb INNER JOIN task tsk ON tb.casetaskevent = tsk.id AND tsk.supportcase = ?";
        var resultSet = query.runSuiteQL({
            query: sqlQueryData,
            params: [caseId]
        });
    
        var results = resultSet.results;
        var timeEntries = [];
        for(var i = 0; i < results.length; i++ ){
            var newItem = true;

            if(timeEntries.length) {
                for(var j = 0; j < timeEntries.length; j++) {
                    if(results[i].values[0] == timeEntries[j].item){
                        timeEntries[j].qty += results[i].values[1];
                        newItem = false;
                    }
                }
            }
            
            if(newItem) {
                timeEntries.push({
                    item: results[i].values[0],
                    qty: results[i].values[1]
                });
            }
        }

        return timeEntries;
    }

    function getCaseDetails(caseId) {

        try{ 
            var caseObj = record.load({
                type: record.Type.SUPPORT_CASE,
                id: caseId,
                isDynamic: true
            });

            var caseDetails = {
                customer: caseObj.getValue({ fieldId: 'custevent_nx_customer' }),
                poNo: caseObj.getValue({ fieldId: 'custevent_nx_case_purchaseorder' }),
                job: caseObj.getValue({ fieldId: 'company' }),
                asset: caseObj.getValue({ fieldId: 'custevent_nx_case_asset' }),
                caseDetails: caseObj.getValue({ fieldId: 'custevent_nx_case_details' })
            };

            return caseDetails;
        } catch (e) {
            log.error('getCaseDetails', e);
        }
    }

    function getInputData() {

        var caseIds = scriptObj.getParameter({ name: 'custscript_case_ids' });

        return JSON.parse(caseIds);

    }

    function map(context) {
        
        var caseId = JSON.parse(context.value);
        var caseObj = getCaseDetails(caseId);
        log.debug('case', {caseId: caseId, caseObj: caseObj});
        try {
            // check the case has any sales order
            var sqlQueryData = "SELECT id FROM transaction tran WHERE tran.custbody_nx_case = ?";
            var resultSet = query.runSuiteQL({
                query: sqlQueryData,
                params: [caseId]
            });
        
            var results = resultSet.results;

            if(results.length > 1) {

            } else if(results.length == 1) {
                var timeEntries = getTimeEntries(caseId);

                var invoiceObj = record.transform({
                    fromType: record.Type.SALES_ORDER,
                    fromId: newRec.id,
                    toType: record.Type.INVOICE,
                    isDynamic: true 
                });
                invoiceObj.save({ enableSourcing: true, ignoreMandatoryFields: true });

            } else if(results.length == 0){

                var timeEntries = getTimeEntries(caseId);
                

                var invoiceObj = record.create({
                    type: record.Type.INVOICE,
                    isDynamic: false
                });

                log.debug('caseId', caseId);

                invoiceObj.setValue({ fieldId: 'entity', value: caseObj.customer });
                invoiceObj.setValue({ fieldId: 'job', value: caseObj.job });
                
                for(var i = 0; i < timeEntries.length; i++) {
                    invoiceObj.setSublistValue({ sublistId: 'item', fieldId: 'item', value: timeEntries[i].item, line: i });
                    invoiceObj.setSublistValue({ sublistId: 'item', fieldId: 'quantity', value: timeEntries[i].qty, line: i });
                    invoiceObj.setSublistValue({ sublistId: 'item', fieldId: 'price', value: 1, line: i });
                }

                var invId = invoiceObj.save();
                log.audit('Invoice Saved Once', 'Invoice ID: ' + invId);

                var invoiceObj = record.load({
                    id: invId,
                    type: record.Type.INVOICE,
                    isDynamic: true
                });
                
                invoiceObj.setValue({ fieldId: 'custbody_nx_case', value: caseId });
                invoiceObj.setValue({ fieldId: 'custbody_nx_asset', value: caseObj.asset });

                var invId = invoiceObj.save();
                log.audit('Invoice Saved Twice', 'Invoice ID: ' + invId);

            }

        } catch (e) {
            log.error('Error occured', e);
        }

    }

    function reduce(context) {
        
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
