<style type="text/css">
    html,body{width:100%;overflow-x:hidden;min-height:100%}.flex{display:flex;-ms-flex-direction:row;-webkit-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;-webkit-flex-wrap:nowrap;flex-wrap:nowrap}.flex>*{-ms-flex:1 0;-webkit-flex:1 0;flex:1 0;min-width:0}.flex>*.-static{-ms-flex:none;-webkit-flex:none;flex:none}.flex.-vertical{flex-direction:column}.flex.-fixed>*{-ms-flex:0 0 20%;-webkit-flex:0 0 20%;flex:0 0 20%}.flex.-pad>*{padding:2rem}.flex.-vtop{align-items:flex-start}.flex.-vcenter{align-items:center}.flex.-vbottom{align-items:flex-end}.flex.-hright{justify-content:flex-end}.flex.-hcenter{justify-content:center}.flex.-hbetween{justify-content:space-between}.flex.-haround{justify-content:space-around}.flex.-bauto>*{-ms-flex:0 0;-webkit-flex:0 0;flex:0 0;flex-basis:auto}.flex .break{display:none;flex-basis:100%}@media (max-width: 512px){.flex.-pad>*{padding:1rem}.flex>.-xs-1{width:25%}.flex>.-xs-2{width:50%}.flex>.-xs-3{width:75%}.flex>.-xs-4{width:100%}.flex.-xs-stack{-ms-flex-flow:wrap;-webkit-flex-flow:wrap;flex-flow:wrap;width:100%;-moz-box-sizing:border-box;-webkit-box-sizing:border-box}.flex.-xs-stack>*{flex-basis:100%;box-sizing:border-box}.flex .break.-xs{display:block}.flex .-xs-order-1{order:1}.flex .-xs-order-2{order:2}}@media (max-width: 512px){.flex.-s-stack{display:block;flex:none;-ms-flex-flow:wrap;-webkit-flex-flow:wrap;flex-flow:wrap;width:100%;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}.flex .break{padding:0;margin:0;width:0;height:0}.flex .break.-s{display:block}}@media screen and (max-width: 1024px){.flex.-m-stack{display:block;flex:none;-ms-flex-flow:wrap;-webkit-flex-flow:wrap;flex-flow:wrap;width:100%;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}.flex .break.-m{display:block}}@media (min-width: 513px){.flex>.-s-1{flex-grow:1}.flex>.-s-2{flex-grow:2}.flex>.-s-3{flex-grow:3}.flex>.-s-4{flex-grow:4}.flex>.-s-5{flex-grow:5}.flex>.-s-6{flex-grow:6}.flex>.-s-7{flex-grow:7}.flex>.-s-8{flex-grow:8}.flex>.-s-9{flex-grow:9}.flex>.-s-10{flex-grow:10}.flex>.-s-11{flex-grow:11}.flex>.-s-12{flex-grow:12}}@media screen and (max-width: 1024px){.flex.-m-stack-and-below{-ms-flex-flow:wrap;-webkit-flex-flow:wrap;flex-flow:wrap;width:100%;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}.flex.-m-stack-and-below>*{flex-basis:100%}}@media (min-width: 768px){.flex>.-m-1{flex-grow:1}.flex>.-m-2{flex-grow:2}.flex>.-m-3{flex-grow:3}.flex>.-m-4{flex-grow:4}.flex>.-m-5{flex-grow:5}.flex>.-m-6{flex-grow:6}.flex>.-m-7{flex-grow:7}.flex>.-m-8{flex-grow:8}.flex>.-m-9{flex-grow:9}.flex>.-m-10{flex-grow:10}.flex>.-m-11{flex-grow:11}.flex>.-m-12{flex-grow:12}.flex>.-m-13{flex-grow:13}.flex>.-m-14{flex-grow:14}.flex>.-m-15{flex-grow:15}.flex>.-m-16{flex-grow:16}}@media (min-width: 1025px){.flex>.-l-1{flex-grow:1}.flex>.-l-2{flex-grow:2}.flex>.-l-3{flex-grow:3}.flex>.-l-4{flex-grow:4}.flex>.-l-5{flex-grow:5}.flex>.-l-6{flex-grow:6}.flex>.-l-7{flex-grow:7}.flex>.-l-8{flex-grow:8}.flex>.-l-9{flex-grow:9}.flex>.-l-10{flex-grow:10}.flex>.-l-11{flex-grow:11}.flex>.-l-12{flex-grow:12}.flex>.-l-13{flex-grow:13}.flex>.-l-14{flex-grow:14}.flex>.-l-15{flex-grow:15}.flex>.-l-16{flex-grow:16}}.grid{display:-webkit-box;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap}.grid>*{box-sizing:border-box}.grid.-pad>*{padding:2rem}.grid.-vcenter{-webkit-box-align:center;-ms-flex-align:center;align-items:center}@media (max-width: 512px){.grid>*{width:100%}.grid.-xs-1>*{width:100%}.grid.-xs-2>*{width:50%}.grid.-xs-3>*{width:33.33333%}.grid.-xs-4>*{width:25%}}@media (min-width: 513px){.grid.-s-1>*{width:100%}.grid.-s-2>*{width:50%}.grid.-s-3>*{width:33.33333%}.grid.-s-4>*{width:25%}.grid.-s-5>*{width:20%}.grid.-s-6>*{width:16.66667%}.grid.-s-7>*{width:14.28571%}.grid.-s-8>*{width:12.5%}.grid.-s-9>*{width:11.11111%}.grid.-s-10>*{width:10%}.grid.-s-11>*{width:9.09091%}.grid.-s-12>*{width:8.33333%}}@media (min-width: 768px){.grid.-m-1>*{width:100%}.grid.-m-2>*{width:50%}.grid.-m-3>*{width:33.33333%}.grid.-m-4>*{width:25%}.grid.-m-5>*{width:20%}.grid.-m-6>*{width:16.66667%}.grid.-m-7>*{width:14.28571%}.grid.-m-8>*{width:12.5%}.grid.-m-9>*{width:11.11111%}.grid.-m-10>*{width:10%}.grid.-m-11>*{width:9.09091%}.grid.-m-12>*{width:8.33333%}}@media (min-width: 1025px){.grid.-l-1>*{width:100%}.grid.-l-2>*{width:50%}.grid.-l-3>*{width:33.33333%}.grid.-l-4>*{width:25%}.grid.-l-5>*{width:20%}.grid.-l-6>*{width:16.66667%}.grid.-l-7>*{width:14.28571%}.grid.-l-8>*{width:12.5%}.grid.-l-9>*{width:11.11111%}.grid.-l-10>*{width:10%}.grid.-l-11>*{width:9.09091%}.grid.-l-12>*{width:8.33333%}}body{margin:0}.-wrap-200{max-width:200px;margin:auto}.-wrap-20{max-width:20rem;margin:auto}.-wrap-400{max-width:400px;margin:auto}.-wrap-40{max-width:40rem;margin:auto}.-wrap-600{max-width:600px;margin:auto}.-wrap-60{max-width:60rem;margin:auto}.-wrap-800{max-width:800px;margin:auto}.-wrap-80{max-width:80rem;margin:auto}.-wrap-1000{max-width:1000px;margin:auto}.-wrap-100{max-width:100rem;margin:auto}.-wrap-1200{max-width:1200px;margin:auto}.-wrap-120{max-width:120rem;margin:auto}.-pad-all{padding:1rem}.-pad-all-2{padding:2rem}.-pad-1{padding:1rem 0}.-pad-2{padding:2rem 0}.-pad-4{padding:4rem 0}.-pad-6{padding:6rem 0}.-pad-8{padding:8rem 0}.-mar-1{margin:1rem 0}.spacer.-t{padding:1rem}.spacer.-s{padding:2rem}.spacer.-m{padding:3rem}.spacer.-l{padding:5rem}.spacer.-e{padding:8rem}.pos{position:absolute}.pos.-top{top:80px}.pos.-bot{bottom:80px}.pos.-middle{top:50%;transform:translateY(-50%)}.pos.-left{left:80px}.pos.-right{right:80px}.-hide{display:none}@media (max-width: 512px){.-xs-hide{display:none}}@media (max-width: 512px){.-s-hide{display:none}}@media (max-width: 767px){.-show-m{display:none}}@media screen and (max-width: 1024px){.-m-hide{display:none}}@media (min-width: 768px){.-show-s-or-less{display:none}}@media (min-width: 768px){.-show-less-than-m{display:none}}@media (min-width: 1025px){.-l-hide{display:none}}.-scroll-y{overflow-y:auto;max-width:100%}.-scroll-x{overflow-x:auto;max-height:100%}html{font-size:14px}@media (max-width: 512px){html{font-size:12px}}html,textarea,input{font-family:'Open Sans', Helvetica, Arial, sans-serif}h1,h2,h3,h4,h5,h6,td,li,p,figcaption{font-size:1rem;font-weight:normal;margin:1rem 0}ul,ol{margin:0}@font-face{font-family:"Novecentowide-Light";src:url("/assets/fonts/Novecentowide-Light.woff") format("woff")}@font-face{font-family:"Novecentowide-Normal";src:url("/assets/fonts/Novecentowide-Normal.woff") format("woff")}p,td,li,address,time{line-height:1.45rem;font-size:1rem}p.intro,td.intro,li.intro,address.intro,time.intro{line-height:2.35rem}p{margin:1rem 0}a{color:#e35222;text-decoration:none;font-size:1rem}a.-none{color:inherit;text-decoration:none;font-size:inherit}.header{font-size:3.998rem;font-family:'Raleway', 'Open Sans', Helvetica, Arial, sans-serif;margin:0;text-transform:uppercase}@media (max-width: 512px){.header{font-size:3rem}}.header-sub{font-size:1.5rem}.header-sub-bold{font-size:1.5rem;font-weight:700}.title{font-size:2.3rem;text-transform:uppercase;font-family:"Novecentowide-Light", 'Open Sans', Helvetica, Arial, sans-serif}.title-bold{font-size:2.3rem;font-weight:700}.title-yellow{font-size:2.3rem;color:#fcb330}.title-sub{font-size:1.15rem;margin:0;font-weight:700}.title-sub-yellow{font-size:1.15rem;margin:0;font-weight:700;color:#fcb330}.quote{font-family:'Raleway', 'Open Sans', Helvetica, Arial, sans-serif;font-size:1.5rem;font-style:italic;line-height:1.6rem;font-weight:300}.code{color:#e35222}.cutline,small,figcaption{font-size:0.80rem}.cutline-bold{font-size:0.80rem;font-weight:700}.-text-right{text-align:right}.-text-center{text-align:center}.-text-left{text-align:left}.-bold{font-weight:bold}.-light{font-weight:300}.-italic{font-style:italic}hr{margin:1rem 0}.-left-pad{padding-left:2rem}.-slider-txt{text-shadow:-2px 2px 2px #30302d;padding-left:2rem;font-family:'Raleway', 'Open Sans', Helvetica, Arial, sans-serif;font-weight:300;color:white;text-transform:uppercase;font-size:2.6rem}.section-header{border-bottom:2px solid #fcb330;padding:1rem 0;margin:2rem 0 1rem 0}.breadcrumbs{padding:0 0 .25rem 0;background-color:#221e1b}.breadcrumbs ul{list-style-type:none;color:#818181;padding:0}.breadcrumbs ul li{padding:0 1rem;display:inline}.breadcrumbs ul li a{color:#818181;font-size:0.80rem}.breadcrumbs ul li a.-active{color:#fff}.breadcrumbs ul li a:hover{color:#fff}input[type=button],button,.button{-webkit-appearance:none;display:inline-block;outline:none;border:solid 1px #a5a4a4;font:inherit;font-size:.9rem;-moz-transition:box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1),background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),color 0.2s cubic-bezier(0.4, 0, 0.2, 1);-o-transition:box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1),background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),color 0.2s cubic-bezier(0.4, 0, 0.2, 1);-webkit-transition:box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1),background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),color 0.2s cubic-bezier(0.4, 0, 0.2, 1);transition:box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1),background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),color 0.2s cubic-bezier(0.4, 0, 0.2, 1);will-change:box-shadow;cursor:pointer;padding:1rem;border-radius:none;text-decoration:inherit;text-transform:uppercase;font-weight:700;margin:1rem 0;background-color:#a5a4a4;color:#f1f1f1}input[type=button]::-moz-focus-inner,button::-moz-focus-inner,.button::-moz-focus-inner{border:0}input[type=button]:active,input[type=button]:focus,input[type=button].-active,button:active,button:focus,button.-active,.button:active,.button:focus,.button.-active{box-shadow:none}input[type=button]:hover,button:hover,.button:hover{background-color:#e35222;color:white;border:solid 1px #e35222}input[type=button].-dark,button.-dark,.button.-dark{background-color:#221e1b;color:#cecbc5;border:solid 1px #221e1b}input[type=button].-dark:hover,button.-dark:hover,.button.-dark:hover{background-color:#e35222;color:white;border:solid 1px #e35222}input[type=button].-yellow,button.-yellow,.button.-yellow{background-color:#fcb330;color:#fff;border:solid 1px #fcb330}input[type=button].-yellow:hover,button.-yellow:hover,.button.-yellow:hover{background-color:#a5a4a4;color:#fff;border:solid 1px #a5a4a4}input[type=button].-white,button.-white,.button.-white{background-color:#fff;color:#a5a4a4;border:solid 1px #fff}input[type=button].-white:hover,button.-white:hover,.button.-white:hover{background-color:#a5a4a4;color:#fff;border:solid 1px #a5a4a4}input[type=button].-white-outline,button.-white-outline,.button.-white-outline{box-shadow:none;background:none;user-select:none;color:white;border:solid 1px white}input[type=button].-white-outline:hover,input[type=button].-white-outline.-active,button.-white-outline:hover,button.-white-outline.-active,.button.-white-outline:hover,.button.-white-outline.-active{background-color:white;color:#221e1b;border:solid 1px white}input[type=button].-outline,button.-outline,.button.-outline{box-shadow:none;background:none;user-select:none;color:#a5a4a4;border:solid 1px #a5a4a4}input[type=button].-outline:hover,input[type=button].-outline.-active,button.-outline:hover,button.-outline.-active,.button.-outline:hover,.button.-outline.-active{background-color:#a5a4a4;color:white;border:solid 1px #a5a4a4}input[type=button].-outline.-fg-primary1,button.-outline.-fg-primary1,.button.-outline.-fg-primary1{color:#e35222}input[type=button].-block,button.-block,.button.-block{display:block;width:100%}input[type=button].-circular,button.-circular,.button.-circular{display:inline-block;border-radius:50%}input[type=button].-circular>*,button.-circular>*,.button.-circular>*{display:block;fill:currentColor}.buttons button+button,.buttons button+.button,.buttons .button+button,.buttons .button+.button{margin-left:1rem}header{padding:1rem 0 0 0;background:#221e1b}@media (max-width: 767px){header{position:fixed;top:0;left:0;right:0;height:4rem;padding:1rem;z-index:10}header img{height:4rem}}.social{display:inline-block}.social a{margin:.25rem;padding:.5rem;border-radius:50px;background-color:#3c3b3a;color:#221e1b;font-size:0;display:inline-block;vertical-align:middle}.social a.you-tube:hover{background-color:#b00;color:white}.social a.twitter:hover{background-color:#00aced;color:white}.social a.facebook:hover{background-color:#3b5998;color:white}.social a.pinterest:hover{background-color:#cb2027;color:white}.social a.linked-in:hover{background-color:#007bb6;color:white}.social a.google-plus:hover{background-color:#dd4b39;color:white}.social img,.social svg{width:15px;height:15px;vertical-align:middle}.social+.button{margin:0;padding:.5rem 1rem;border-radius:none;color:#221e1b;background-color:#3c3b3a;border:1px solid #3c3b3a}.social+.button:hover{background-color:#e35222;color:white;border:solid 1px #e35222}.cards>.card{margin:2rem}.card{display:flex;flex-direction:column;border-radius:2px;background-color:#fff;overflow:hidden}.card .media{position:relative;flex:0 0}.card .media img{display:block;border-radius:2px 2px 0 0}.card .media .content{position:absolute;bottom:20px;left:20px;right:20px}.card .content{padding:20px;flex:1 0;display:flex;align-items:center}.card .content>*:first-child{margin-top:0}.card .content>*:last-child{margin-bottom:0}.card .toolbar{padding:10px 20px;background:rgba(0,0,0,0.03);border-top:1px solid rgba(0,0,0,0.05);flex:0 0}.card .toolbar ul{margin:0;padding:0;list-style:none}.card .toolbar ul li{margin:0 10px 0 0;padding:0;display:inline-block}.-fg-primary1{color:#e35222}.-fg-primary2{color:#221e1b}.-fg-secondary1{color:#fcb330}.-fg-secondary2{color:#30302d}.-fg-secondary3{color:#a5a4a4}.-fg-tertiary1{color:#cecbc5}.-fg-tertiary2{color:#e3e3e3}.-fg-tertiary3{color:#f1f1f1}.-fg-facebook{color:#3b5998}.-fg-flickr{color:#0063db}.-fg-github{color:#4183c4}.-fg-googleplus{color:#dd4b39}.-fg-instagram{color:#517fa4}.-fg-kickstarter{color:#76cc1e}.-fg-linkedin{color:#007bb6}.-fg-pinterest{color:#cb2027}.-fg-twitter{color:#00aced}.-fg-vimeo{color:#aad450}.-fg-youtube{color:#b00}.-fg-white{color:#fff}.-fg-green{color:#5cb85c}.-fg-red{color:#de3226}.-fg-black{color:#000}.-fg-black-reverse{color:#cfcfcf}.-fg-brown{color:#5a4640}.-bg-primary1{background-color:#e35222}.-bg-primary2{background-color:#221e1b}.-bg-secondary1{background-color:#fcb330}.-bg-secondary2{background-color:#30302d}.-bg-secondary3{background-color:#a5a4a4}.-bg-tertiary1{background-color:#cecbc5}.-bg-tertiary2{background-color:#e3e3e3}.-bg-tertiary3{background-color:#f1f1f1}.-bg-facebook{background-color:#3b5998}.-bg-flickr{background-color:#0063db}.-bg-github{background-color:#4183c4}.-bg-googleplus{background-color:#dd4b39}.-bg-instagram{background-color:#517fa4}.-bg-kickstarter{background-color:#76cc1e}.-bg-linkedin{background-color:#007bb6}.-bg-pinterest{background-color:#cb2027}.-bg-twitter{background-color:#00aced}.-bg-vimeo{background-color:#aad450}.-bg-youtube{background-color:#b00}.-bg-white{background-color:#fff}.-bg-green{background-color:#5cb85c}.-bg-red{background-color:#de3226}.-bg-black{background-color:#000}.-bg-black-reverse{background-color:#cfcfcf}.-bg-brown{background-color:#5a4640}.curtain{position:relative;overflow:hidden}.curtain>.fg{position:absolute;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.75);color:#fff;transition:all linear 0.2s;transform:translateY(-100%)}.curtain>.fg>div{position:absolute;top:50%;transform:translateY(-50%);left:0;right:0;text-align:center}.curtain:hover>.fg{transform:none}form>div{margin:1.5rem 0}form>div h4{font-weight:bold}form>div>label.flex{margin:0.5rem 0}form>div>label.flex>span:first-child{padding-right:1rem}form>div>label.flex.radio{display:inline-block}form>div>label.flex.radio+.radio{margin-left:1rem}label,input,select{cursor:pointer}input[type=text],input[type=password],input[type=date],input[type=email],select,textarea{display:block;width:100%;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;height:34px;padding:6px 12px;font-size:1rem;line-height:1.42857143;background-color:#fff;background-image:none;border:1px solid #ccc;border-radius:0;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);-webkit-transition:border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;-o-transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s;-moz-transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s;transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s}input[type=text]:focus,input[type=password]:focus,input[type=date]:focus,input[type=email]:focus,select:focus,textarea:focus{border-color:#fcb330;outline:0;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 8px rgba(252,179,48,0.6);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 8px rgba(252,179,48,0.6)}textarea{height:auto}form label{display:block}form.-inline label,form.-inline input{display:inline-block;width:auto}form.-table{display:table;width:100%}form.-table>*{display:table-row}form.-table>*>*{display:table-cell;vertical-align:middle;padding:0.5rem;text-align:right}form.-table>*>*:last-child{text-align:left}.blocks>*{display:block}.-spin{-webkit-animation-name:spin;-webkit-animation-duration:2.6s;-webkit-animation-iteration-count:infinite;-webkit-animation-timing-function:linear;-moz-animation-name:spin;-moz-animation-duration:2.6s;-moz-animation-iteration-count:infinite;-moz-animation-timing-function:linear;-ms-animation-name:spin;-ms-animation-duration:2.6s;-ms-animation-iteration-count:infinite;-ms-animation-timing-function:linear;-o-animation-name:spin;animation-name:spin;-o-animation-duration:2.6s;animation-duration:2.6s;-o-animation-iteration-count:infinite;animation-iteration-count:infinite;-o-animation-timing-function:linear;animation-timing-function:linear}@-moz-keyframes spin{from{-moz-transform:rotate(0deg)}to{-moz-transform:rotate(360deg)}}@-webkit-keyframes spin{from{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(360deg)}}@keyframes spin{from{-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);-webkit-transform:rotate(360deg);transform:rotate(360deg)}}img,svg{max-width:100%;height:auto}svg{fill:currentColor}.ratio{display:block;height:0;position:relative;overflow:hidden}.ratio.-size-1x1{padding:0 0 100% 0}.ratio.-size-16x9{padding:0 0 56.25% 0}.ratio.-size-4x3{padding:0 0 75% 0}.ratio>*{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;height:100%}img.-block{display:block;width:100%}.-depth-1,.card{box-shadow:0 2px 2px 0 rgba(0,0,0,0.14),0 3px 1px -2px rgba(0,0,0,0.2),0 1px 5px 0 rgba(0,0,0,0.12)}.-depth-2{box-shadow:0 3px 4px 0 rgba(0,0,0,0.14),0 3px 3px -2px rgba(0,0,0,0.2),0 1px 8px 0 rgba(0,0,0,0.12)}.-depth-3{box-shadow:0 4px 5px 0 rgba(0,0,0,0.14),0 1px 10px 0 rgba(0,0,0,0.12),0 2px 4px -1px rgba(0,0,0,0.2)}.-depth-4{box-shadow:0 6px 10px 0 rgba(0,0,0,0.14),0 1px 18px 0 rgba(0,0,0,0.12),0 3px 5px -1px rgba(0,0,0,0.2)}.-depth-5{box-shadow:0 8px 10px 1px rgba(0,0,0,0.14),0 3px 14px 2px rgba(0,0,0,0.12),0 5px 5px -3px rgba(0,0,0,0.2)}.-depth-6{box-shadow:0 16px 24px 2px rgba(0,0,0,0.14),0 6px 30px 5px rgba(0,0,0,0.12),0 8px 10px -5px rgba(0,0,0,0.2)}.-depth-7{box-shadow:0 9px 46px 8px rgba(0,0,0,0.14),0 11px 15px -7px rgba(0,0,0,0.12),0 24px 38px 3px rgba(0,0,0,0.2)}table{border-collapse:collapse;background:#fff;border:1px solid;border-color:#eee;width:100%}table th{font-weight:bold;padding:8px;background-color:#a5a4a4;color:white;font-size:1rem}table td{padding:8px;vertical-align:text-top;font-size:.95rem;text-align:center}table thead{text-align:left}table thead tr{border-bottom:2px solid;border-color:#eee}table tbody tr{border-bottom:1px solid;border-color:#eee}table.-striped tbody tr:nth-of-type(2n+1){background-color:#f9f9f9}table.-hover tbody tr:hover{background-color:#f5f5f5}.pdp-details{margin-top:2rem}.pdp-details p{padding:.5rem 0}.side-bar{padding:2rem 1rem}.side-bar .resources{padding:2rem}.side-bar .resources .res-title{border-bottom:3px solid #403A34;color:#221E1B;display:block;font-size:16px;margin-bottom:1rem}.side-bar .resources ul{list-style-type:none;text-align:left;margin:1rem;padding:0}.side-bar .resources ul li{padding:10px 0 10px 10px;border-bottom:1px solid #CCCCCC;list-style-image:url(/assets/media/images/footer-bullet.png);padding:.5rem}.side-bar .resources a{color:#a5a4a4}.side-bar .resources a:hover{color:#e35222}.side-bar-2{flex-grow:1.3;padding:1.5rem}.side-bar-2 div{margin:1rem}.side-bar-2 aside>ul.accordions{list-style-type:none;text-align:left;padding:0}.side-bar-2 aside>ul.accordions>li{padding:1rem;border-bottom:2px solid white;color:#a5a4a4;font-size:.9rem;line-height:1.45rem}.side-bar-2 a{color:#a5a4a4;text-decoration:none;font-size:1rem}.side-bar-2 a.-active{color:#e35222;text-decoration:none;font-size:1rem}.main{padding:4rem}.side-nav{padding:1rem}.side-nav ul{list-style-type:none;text-align:left;margin:1rem;padding:0}.side-nav ul li{padding:1rem;border-bottom:2px solid white;color:#a5a4a4;font-size:.9rem}.side-nav ul li img,.side-nav ul li svg{width:10px;background-color:#cecbc5;padding:.05rem}.side-nav ul a{color:#a5a4a4}.side-nav ul a:hover{color:#e35222;cursor:pointer}@media screen and (max-width: 1024px){.side-bar{display:none}.side-bar-2{display:none}}@media (max-width: 512px){.main{padding:2rem}}.footer a{color:#a5a4a4}.footer a:hover{color:#e35222}.footer .links{border-top:4px solid #e35222}.footer .links p{font-size:.85rem;padding-bottom:1rem}.footer .links ul{font-size:.85rem;-webkit-padding-start:1rem}.footer .links li{border-bottom:1px solid #3c3b3a;list-style-image:url(/assets/media/images/footer-bullet.png);width:135px;padding:.5rem}.footer .bottom-footer .cutline{margin-top:.5rem}.footer .social{text-align:right}.footer .social a{margin:.25rem;padding:.5rem;border-radius:50px;background-color:#3c3b3a;color:#221e1b;font-size:0;display:inline-block}.footer .social a.you-tube:hover{background-color:#b00;color:white}.footer .social a.twitter:hover{background-color:#00aced;color:white}.footer .social a.facebook:hover{background-color:#3b5998;color:white}.footer .social a.pinterest:hover{background-color:#cb2027;color:white}.footer .social a.linked-in:hover{background-color:#007bb6;color:white}.footer .social a.google-plus:hover{background-color:#dd4b39;color:white}.footer .social img,.footer .social svg{width:15px;height:15px;vertical-align:middle}@media screen and (max-width: 1024px){.footer .bottom-footer{text-align:center}.footer .years{display:none}.footer .social{padding-top:.25rem;padding-bottom:2rem;text-align:center}}@media (max-width: 650px){.footer .links{display:none}}.open-bar .quicklinks{padding:1rem 0}.open-bar .quicklinks a{color:#f1f1f1;padding:1rem;text-transform:uppercase}.open-bar .dealer{min-width:400px;background-color:#91350d;padding:.5rem 0;text-transform:uppercase;text-align:right}.open-bar .dealer form{margin:0}.open-bar .dealer form label{margin:0 1rem}.open-bar .dealer form label input{background-color:#a43d0f;color:#e35222;font-size:10px;border:1px solid #e35222;width:100px;margin:0;border-radius:0}@media (max-width: 512px){.quickview,.quicklinks,.dealer{text-align:center}}.generic{padding:2rem}.generic h5 img{float:right;padding-left:1rem}.generic .designs{margin:2rem;padding:2rem}.generic .results{background-color:#fff}.generic .results ul{list-style:none}.generic .results ol{counter-reset:li}.generic .results ol>li{position:relative;margin:0 2rem 1rem 2rem;padding:.5rem 1rem;list-style:none;border-top:2px solid #a5a4a4;background:#f6f6f6}.generic .results ol>li:before{content:counter(li);counter-increment:li;position:absolute;top:-2px;left:-2em;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;width:2em;margin-right:8px;padding:4px;border-top:2px solid #a5a4a4;color:#fff;background:#a5a4a4;font-weight:bold;font-family:"Helvetica Neue", Arial, sans-serif;text-align:center}.generic .results li ol,.generic .results li ul{margin-top:6px}.generic .results ol ol li:last-child{margin-bottom:0}.product{padding:.5rem 1rem}.product .product-img{margin-bottom:0.5rem;display:block;width:100%}.product:hover{background-color:#e3e3e3;cursor:pointer}@media (max-width: 512px){.product{text-align:center}}.product{padding:.5rem 1rem}.product .product-img{margin-bottom:0.5rem;display:block;width:100%}.product:hover{background-color:#e3e3e3;cursor:pointer}@media (max-width: 512px){.product{text-align:center}}.detail-grid>div{padding:2rem .5rem;border:1px solid #000;margin:1rem}.-title-block{background-color:rgba(34,30,27,0.75);position:absolute;right:0;top:250px;padding:2rem 4rem}@media (max-width: 512px){.-title-block{top:84px;padding:2rem 1rem}}.-button-block{padding-bottom:2rem}.-text-block{padding:0 6rem;position:absolute;max-width:600px}@media screen and (max-width: 1024px){.-text-block{position:initial;padding:0 2rem}}.site-index ul{list-style:none;padding:0;margin:0}.site-index li{margin-top:.7rem;padding-left:16px}.press-center{margin:0 1rem}.press-center>div>div>div{margin-right:1.5rem}@media (max-width: 512px){.press-center{margin:0}}img.resource-img{padding:.45rem;background-color:white;border:1px solid #e3e3e3;box-shadow:3px 3px 5px rgba(165,164,164,0.5)}aside ul{list-style:none;margin:0;padding:0}.category .tab{max-height:44px}.category .quicklinks{float:right;position:relative;float:right;height:185px}.category .quicklinks ul{list-style-type:none;text-align:left;padding:1rem 2rem}.home .thumbnails>div{margin:.5rem;height:250px}.sharethis-inline-share-buttons{color:#fff;margin-top:1rem}.sharethis-inline-share-buttons>div{display:inline-block !important}.sharethis-inline-share-buttons svg{fill:#fff}.text-block.-dark strong,.text-block.-dark b{color:#fcb330}.pagination{text-transform:uppercase}.pagination button{display:inline-block;height:44px;min-width:44px;line-height:44px;margin:0;padding:0}.pagination .pages{text-align:center;padding:0 1.5rem}.pagination .pages button{width:44px}.page-result{padding:2rem 0}.page-result a{display:inline-block}.page-result:first-child{padding-top:0}.page-result+.page-result{border-top:1px solid #cecbc5}
.accordion{position:relative}.accordion ._toggle{display:block;cursor:pointer}.accordion ._toggle>.on{display:none}.accordion .pane{padding:0 2rem;overflow:hidden;max-height:0;transition:all ease-in 0.6s;box-sizing:border-box}.accordion .on,.accordion .off{text-align:center;width:20px;font-weight:bold}.accordion.-active>._toggle>.on{display:inline-block}.accordion.-active>._toggle>.off{display:none}.accordion.-active>.pane{max-height:500px;display:table}
.carousel{position:relative;background-color:#cecbc5;font-size:0;width:100%;height:50vh;overflow:hidden;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none;user-select:none}.carousel .stage{height:100%;position:relative;white-space:nowrap;-moz-transition:all ease-in-out 0.555s;-o-transition:all ease-in-out 0.555s;-webkit-transition:all ease-in-out 0.555s;transition:all ease-in-out 0.555s}.carousel .stage>div,.carousel .stage>.scene{height:100%;width:100%;position:relative;display:inline-block;white-space:normal;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none;user-select:none;background-size:cover;background-position:center center}.carousel .stage>div img,.carousel .stage>.scene img{width:100%}.carousel .stage>div .pos,.carousel .stage>.scene .pos{position:absolute}.carousel .stage>div .pos.-top,.carousel .stage>.scene .pos.-top{top:80px}.carousel .stage>div .pos.-bot,.carousel .stage>.scene .pos.-bot{bottom:80px}.carousel .stage>div .pos.-middle,.carousel .stage>.scene .pos.-middle{top:50%;transform:translateY(-50%)}.carousel .stage>div .pos.-left,.carousel .stage>.scene .pos.-left{left:80px}.carousel .stage>div .pos.-right,.carousel .stage>.scene .pos.-right{right:80px}.carousel.-stack .stage>div,.carousel.-stack .stage>.scene{display:block;position:absolute;top:0;left:0}.carousel .next,.carousel .previous{width:44px;position:absolute;top:0;bottom:0;cursor:pointer}.carousel .next img,.carousel .next svg,.carousel .previous img,.carousel .previous svg{position:absolute;top:50%;-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);-webkit-transform:translateY(-50%);transform:translateY(-50%);width:100%;height:auto;padding:12px;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}.carousel .next img:active,.carousel .next svg:active,.carousel .previous img:active,.carousel .previous svg:active{margin-top:2px}.carousel .next:hover,.carousel .previous:hover{background:rgba(0,0,0,0.1)}.carousel .pagination{position:absolute;bottom:1rem;left:0;right:0;font-size:0;text-align:center}.carousel .pagination a{margin:0;display:inline-block;box-sizing:border-box;padding:10px}.carousel .pagination a:before{content:"";display:block;width:24px;height:24px;background:#30302d;border:3px solid #221e1b;border-radius:50%;box-sizing:border-box}.carousel .pagination a.-active:before,.carousel .pagination a:hover:before{background:#f2f2f2;background:-moz-linear-gradient(top, #f2f2f2 0%, #919191 100%);background:-webkit-linear-gradient(top, #f2f2f2 0%, #919191 100%);background:linear-gradient(to bottom, #f2f2f2 0%, #919191 100%);filter:progid:DXImageTransform.Microsoft.gradient( startColorstr='#f2f2f2', endColorstr='#919191',GradientType=0 )}.carousel .previous{left:0}.carousel .next{right:0}.carousel.-light .next,.carousel.-light .previous{color:#cfcfcf}.inline-carousel .scene{vertical-align:top}
.carousel-continuous{position:relative;background-color:#cecbc5;font-size:0;width:100%;height:75vh;max-height:1024px;overflow:hidden;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none;user-select:none}@media (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape){.carousel-continuous{height:50vh}}@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait){.carousel-continuous{height:30vh}}@media (max-width: 47rem){.carousel-continuous{height:90vh}}@media (max-width: 47rem) and (orientation: portrait){.carousel-continuous{padding:0}}.carousel-continuous .stage{width:100%;height:100%;position:relative}@media (min-width: 768px){.carousel-continuous .stage{top:0;bottom:0;left:0;right:0}}@media (max-width: 47rem) and (orientation: portrait){.carousel-continuous .stage{position:absolute;top:0;bottom:0;left:0;right:0}}.carousel-continuous .stage>.scene,.carousel-continuous .stage>div{position:absolute;top:0;left:0;height:100%;width:100%;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none;user-select:none;background-size:cover;background-position:center center;font-size:1rem;box-sizing:border-box;vertical-align:text-top;text-align:center}.carousel-continuous .stage>.scene img,.carousel-continuous .stage>div img{width:100%}@media (max-width: 47rem) and (orientation: portrait){.carousel-continuous .stage>.scene img,.carousel-continuous .stage>div img{position:relative}}.carousel-continuous .stage>.scene .image-overlay,.carousel-continuous .stage>div .image-overlay{padding:2rem;background-color:rgba(0,0,0,0.2);width:30%;min-width:300px}.carousel-continuous .stage>.scene .image-overlay h1,.carousel-continuous .stage>div .image-overlay h1{margin:0}.carousel-continuous .stage>.scene .image-overlay p,.carousel-continuous .stage>div .image-overlay p{margin:0 !important}.carousel-continuous .stage>.scene .image-overlay button,.carousel-continuous .stage>.scene .image-overlay a,.carousel-continuous .stage>div .image-overlay button,.carousel-continuous .stage>div .image-overlay a{font-size:1rem;color:black}@media (min-width: 768px) and (max-width: 1024px){.carousel-continuous .stage>.scene .image-overlay,.carousel-continuous .stage>div .image-overlay{padding:1rem}.carousel-continuous .stage>.scene .image-overlay h1,.carousel-continuous .stage>div .image-overlay h1{font-size:2rem}.carousel-continuous .stage>.scene .image-overlay p,.carousel-continuous .stage>div .image-overlay p{font-size:.75rem !important}}@media (max-width: 47rem){.carousel-continuous .stage>.scene .image-overlay,.carousel-continuous .stage>div .image-overlay{min-height:150px}.carousel-continuous .stage>.scene .image-overlay h1,.carousel-continuous .stage>div .image-overlay h1{padding-top:1rem;font-size:1.75rem}.carousel-continuous .stage>.scene .image-overlay p,.carousel-continuous .stage>div .image-overlay p{font-size:1rem !important;line-height:1rem !important}.carousel-continuous .stage>.scene .image-overlay button,.carousel-continuous .stage>.scene .image-overlay a,.carousel-continuous .stage>div .image-overlay button,.carousel-continuous .stage>div .image-overlay a{padding:.5rem;font-size:.75rem;min-width:50px}}@media (max-width: 47rem) and (orientation: portrait){.carousel-continuous .stage>.scene .image-overlay,.carousel-continuous .stage>div .image-overlay{height:150px;width:100%;padding:0;background-color:#221e1b}.carousel-continuous .stage>.scene .image-overlay h1,.carousel-continuous .stage>div .image-overlay h1{margin:.5rem}}.carousel-continuous .stage>.scene .pos,.carousel-continuous .stage>div .pos{position:absolute}.carousel-continuous .stage>.scene .pos.-top,.carousel-continuous .stage>div .pos.-top{top:3rem}.carousel-continuous .stage>.scene .pos.-bottom,.carousel-continuous .stage>div .pos.-bottom{bottom:3rem}.carousel-continuous .stage>.scene .pos.-middle,.carousel-continuous .stage>div .pos.-middle{top:50%;transform:translateY(-50%)}.carousel-continuous .stage>.scene .pos.-left,.carousel-continuous .stage>div .pos.-left{left:3rem;text-align:left}.carousel-continuous .stage>.scene .pos.-right,.carousel-continuous .stage>div .pos.-right{right:3rem;text-align:right}.carousel-continuous .stage>.scene .pos.-center,.carousel-continuous .stage>div .pos.-center{left:50%;transform:translateX(-50%);text-align:center}@media (max-width: 47rem) and (orientation: portrait){.carousel-continuous .stage>.scene .pos,.carousel-continuous .stage>div .pos{text-align:center !important;width:100%;top:initial !important;left:0 !important;right:0 !important;bottom:0 !important;transform:translateY(0) !important}}.carousel-continuous .stage>.scene.-animate,.carousel-continuous .stage>div.-animate{-moz-transition:all ease-in-out .555s;-o-transition:all ease-in-out .555s;-webkit-transition:all ease-in-out .555s;transition:all ease-in-out .555s}.carousel-continuous .stage>.scene.-left,.carousel-continuous .stage>div.-left{transform:translateX(-100%)}.carousel-continuous .stage>.scene.-right,.carousel-continuous .stage>div.-right{transform:translateX(100%)}.carousel-continuous .next,.carousel-continuous .previous{width:44px;position:absolute;top:0;bottom:0;cursor:pointer}.carousel-continuous .next img,.carousel-continuous .next svg,.carousel-continuous .previous img,.carousel-continuous .previous svg{position:absolute;top:50%;-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);-webkit-transform:translateY(-50%);transform:translateY(-50%);width:100%;height:auto;padding:12px;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}.carousel-continuous .next img:active,.carousel-continuous .next svg:active,.carousel-continuous .previous img:active,.carousel-continuous .previous svg:active{margin-top:2px}.carousel-continuous .next:hover,.carousel-continuous .previous:hover{background:rgba(0,0,0,0.1)}.carousel-continuous .previous{left:0}.carousel-continuous .next{right:0}.carousel-continuous .pagination{position:absolute;bottom:1rem;left:0;right:0;font-size:0;text-align:center}.carousel-continuous .pagination a{margin:0;display:inline-block;box-sizing:border-box;padding:.5rem}.carousel-continuous .pagination a:before{content:"";display:block;width:24px;height:24px;background:#30302d;border:3px solid #221e1b;border-radius:50%;box-sizing:border-box}.carousel-continuous .pagination a.-active:before,.carousel-continuous .pagination a:hover:before{background:#f2f2f2;background:-moz-linear-gradient(top, #f2f2f2 0%, #919191 100%);background:-webkit-linear-gradient(top, #f2f2f2 0%, #919191 100%);background:linear-gradient(to bottom, #f2f2f2 0%, #919191 100%);filter:progid:DXImageTransform.Microsoft.gradient( startColorstr='#f2f2f2', endColorstr='#919191',GradientType=0 )}@media (min-width: 768px) and (max-width: 1024px){.carousel-continuous .pagination{bottom:0.5rem}}@media (max-width: 47rem) and (orientation: portrait){.carousel-continuous .pagination{bottom:11.5rem;padding-bottom:1rem}}@media (max-width: 47rem) and (orientation: landscape){.carousel-continuous .pagination{bottom:0.5rem}}.carousel-continuous.-light .next,.carousel-continuous.-light .previous{color:#cfcfcf}
*[data-ajax-modal],*[data-modal]{cursor:pointer}#modals{display:none;position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.7)}#modals.-active{display:block}#modals.-light{background-color:rgba(255,255,255,0.85)}#modals .modal{display:none;box-sizing:border-box;position:absolute;left:0;right:0;top:50%;background:#fff;box-shadow:0 0 20px rgba(0,0,0,0.4)}#modals .modal.-active{display:block;-moz-transform:translate3d(0, -50%, 0);-ms-transform:translate3d(0, -50%, 0);-o-transform:translate3d(0, -50%, 0);-webkit-transform:translate3d(0, -50%, 0);transform:translate3d(0, -50%, 0)}#modals .modal.modal-video{width:100%;max-width:752px;padding:2rem;left:50%;transform:translate3d(-50%, -50%, 0)}#modals .modal.modal-home{height:100%}@media (max-width: 512px){#modals .modal.modal-home .-modal-image-left,#modals .modal.modal-home .-modal-image-right{text-align:center;height:275px}}@media (min-width: 513px){#modals .modal.modal-home .-modal-image-left,#modals .modal.modal-home .-modal-image-right{height:500px}}#modals .modal.modal-home .-modal-image-left{background-image:url("/assets/media/images/salamander-commercial.jpg");background-size:cover;background-position:right}@media (min-width: 513px){#modals .modal.modal-home .-modal-image-left{text-align:right}}@media (max-width: 512px){#modals .modal.modal-home .-modal-image-left{background-position:top right}}#modals .modal.modal-home .-modal-image-right{background-image:url("/assets/media/images/salamander-residential.jpg");background-size:cover;background-position:left}@media (min-width: 513px){#modals .modal.modal-home .-modal-image-right{text-align:left}}#modals .modal.modal-home svg{max-width:1.5rem}@media (min-width: 768px){#modals .modal.modal-home{max-height:100%}}#modals .modal .close{width:48px;height:48px;padding:1rem;box-sizing:border-box;position:absolute;top:-48px;right:0;color:#a5a4a4}#modals .modal .close:hover{cursor:pointer}@media (max-width: 767px){#modals .modal{position:fixed;bottom:0;top:0;overflow-y:auto;padding:0 1rem;z-index:100}#modals .modal.-active{-moz-transform:none;-ms-transform:none;-o-transform:none;-webkit-transform:none;transform:none}#modals .modal .close{position:fixed;top:0}}@media (min-width: 768px){#modals .modal{max-height:calc(80% - 48px);overflow-y:auto}}@media (max-width: 512px){#modals{z-index:10}}
.tab{position:relative;font:inherit;font-weight:400;color:#a5a4a4;padding:1rem;text-transform:uppercase}.tabs .tab{display:inline-block;outline:0;border:solid 1px #cecbc5;box-shadow:none;cursor:pointer;margin:0}.tabs .tab+.tab{border-left:0}.tabs .tab:hover{background:#e3e3e3;color:#221e1b;border-color:#cecbc5;z-index:2}.tabs .tab.-active,.tabs .tab:active{color:#221e1b;background:#fcb330;border:solid 1px #fcb330}@media (max-width: 767px){.tabs .tab.-active svg,.tabs .tab:active svg{display:initial}}.tabs .tab svg{display:none;vertical-align:middle;margin-left:1rem;transition:all linear 0.2s;transform:rotate(-90deg);transform-origin:center;height:10px;width:10px}.tabs .tab.-show svg{transform:rotate(0deg)}.tabs.specs{background-color:#e3e3e3;color:#30302d}.tabs.specs:hover{background:#e35222;color:white}.tabs.specs.-active,.tabs.specs:active{background-color:#a5a4a4;color:white}.tabs-dropdown svg{display:none}.tab-panel{display:none;padding:3rem}@media (max-width: 512px){.tab-panel{padding:1rem}}.tab-panel.-active{display:block}@media (min-width: 768px){.tabs .tab svg{display:none}.tabs .tab{background:#fff;background:-moz-linear-gradient(top, #fff 1%, #e3e3e3 100%);background:-webkit-linear-gradient(top, #fff 1%, #e3e3e3 100%);background:linear-gradient(to bottom, #fff 1%, #e3e3e3 100%);filter:progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#e3e3e3',GradientType=0 )}.tabs .tab.-active:after{content:"";position:absolute;left:50%;bottom:-20px;width:0;height:0;border-top:solid 10px #fcb330;border-bottom:solid 10px transparent;border-left:solid 10px transparent;border-right:solid 10px transparent;-ms-transform:translateX(-50%);-webkit-transform:translateX(-50%);transform:translateX(-50%)}.tabs .tab.-show svg{transform:rotate(-90deg)}.tabs-dropdown{display:none}}@media (max-width: 767px){.tabs .tab{display:none}.always-show-tabs .tabs .tab{display:block;text-align:center;border:solid 1px #cecbc5 !important}.always-show-tabs .tabs .tab img{display:block;width:100%}.tabs .tab.-active{display:block}.tabs-dropdown{text-align:center;height:0;transform:scaleY(0);transform-origin:top;opacity:0;transition:all ease-in 0.2s;overflow:hidden}.tabs-dropdown.-show{height:auto;opacity:1;transform:none}.tabs-dropdown .tab{color:#a5a4a4;background-color:#f1f1f1}.tabs-dropdown .tab:after{content:"";display:block;position:absolute;bottom:0;left:10%;right:10%;height:1px;overflow:hidden;background:#cecbc5}.tabs-dropdown .tab:last-child:after{display:none}.tabs-dropdown .tab.-active{display:none}.tabs-dropdown .tab.-active:after{display:none}}
.visualizer{max-width:1200px;margin:auto}.visualizer .details{max-width:362px;padding:2rem 2rem 4rem 2rem}.visualizer .photo{background-size:contain;background-repeat:no-repeat;background-position:center center;box-shadow:-3px 0 3px rgba(0,0,0,0.1);border-left:1px solid #e3e3e3;min-height:300px;position:relative}.visualizer .photo .share{float:right;min-width:175px}.visualizer .photo .share>div{padding:.25rem 1rem}.visualizer .photo .more{position:absolute;bottom:1rem;left:50%;transform:translate3d(-50%, 0, 0);border:1px solid #cecbc5;background:#f1f1f1;padding:4px}.visualizer .photo .more>div{padding:4px;border:4px solid #fff;background-color:#f1f1f1}.visualizer .photo .more>div a{display:block}.visualizer .options{padding:0;border-left:1px solid #e3e3e3}.visualizer .options .swatches{border-bottom:1px solid #e3e3e3}.visualizer .options .swatches>div{margin:auto}.visualizer .options .swatches img{max-width:44px;margin:.25rem;border:2px solid white}.visualizer .options .swatches img:hover,.visualizer .options .swatches img.-active{border:2px solid #e35222;cursor:pointer}
.slider{padding:0 0 75% 0;position:relative;background-color:#cecbc5;font-size:0;width:100%;height:50vh;overflow:hidden;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none;user-select:none;box-sizing:border-box}.slider.-large{height:70vh}.slider .slides{position:absolute;top:0;right:0;bottom:0;left:0;white-space:nowrap;-moz-transition:all ease-in-out 0.555s;-o-transition:all ease-in-out 0.555s;-webkit-transition:all ease-in-out 0.555s;transition:all ease-in-out 0.555s}.slider .slides>div,.slider .slides>.scene{height:100%;width:100%;position:relative;display:inline-block;white-space:normal;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none;user-select:none;background-size:cover;background-position:center center;vertical-align:top}.slider .pagination{position:absolute;bottom:1rem;left:0;right:0;font-size:0;text-align:center}.slider .pagination a{margin:0;display:inline-block;box-sizing:border-box;padding:10px}.slider .pagination a:before{content:"";display:block;width:24px;height:24px;background:#30302d;border:3px solid #221e1b;border-radius:50%;box-sizing:border-box}.slider .pagination a.-active:before,.slider .pagination a:hover:before{background:#f2f2f2;background:-moz-linear-gradient(top, #f2f2f2 0%, #919191 100%);background:-webkit-linear-gradient(top, #f2f2f2 0%, #919191 100%);background:linear-gradient(to bottom, #f2f2f2 0%, #919191 100%);filter:progid:DXImageTransform.Microsoft.gradient( startColorstr='#f2f2f2', endColorstr='#919191',GradientType=0 )}.slider .head{position:absolute;right:0;bottom:0;left:0;max-width:1000px;background:url(/assets/media/images/slider-innerbox-bg.png);margin:auto}.slider .head .arrows{text-align:right}.slider .head .arrows .next,.slider .head .arrows .previous{background-color:rgba(255,255,255,0.5);border-radius:50%;padding:.5rem;height:28px;width:28px}.slider .head .arrows .next:hover,.slider .head .arrows .previous:hover{cursor:pointer;background-color:#e3e3e3}.slider .head .arrows .button{display:block}@media (max-width: 512px){.slider .head{text-align:center}.slider .head .arrows{text-align:center;padding-top:0}}.slider .text{width:50%;max-width:340px;padding:2rem;position:absolute;font-family:'Raleway', 'Open Sans', Helvetica, Arial, sans-serif}.slider .text.-left{margin-left:1rem;left:0}.slider .text.-center{left:50%;transform:translateX(-50%)}.slider .text.-right{margin-right:1rem;right:0}.slider .text.-top{top:0;margin-top:1rem}.slider .text.-middle{top:50%;transform:translateY(-50%)}.slider .text.-bottom{bottom:0;margin-bottom:5rem}.slider .text.-top.-middle{transform:translate3d(-50%, -50%, 0)}.slider .text.-bg-dark{background:rgba(29,24,20,0.8);color:#a5a4a4}.slider .text .title{font-size:3.5rem;line-height:3.5rem;text-transform:uppercase}.slider .text .title-sub{font-size:1.5rem;line-height:2.5rem;margin:1rem 0}.slider .text a{color:#e35222;font-size:1.5rem;line-height:2.5rem}.slider.-simple{width:auto;height:auto;padding:0}.slider.-simple .slides{position:relative}.slider.-simple .slides>div,.slider.-simple .slides>.scene{height:auto}@media (min-width: 768px){.slider{padding:0 0 41.25% 0;height:0}}@media (max-width: 512px){.slider .text{padding:1rem}.slider .text.-top,.slider .text.-middle,.slider .text.-center,.slider .text.-right{right:auto;top:auto;transform:none;left:0;bottom:0}.slider .text .title{font-size:1.9rem;line-height:1.9rem}.slider .text .title-sub{display:none}}
.fader{position:relative;background-color:#cecbc5;font-size:0;width:100%;overflow:hidden;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none;user-select:none}.fader .scenes>div{transition:all cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.55s}.fader .scenes>div.-fade-out{opacity:0}.fader .scenes>div:nth-child(n+2){position:absolute;top:0;left:0;right:0}.fader .scenes>div img{display:block;width:100%;height:auto}
body{overflow-x:hidden}.navigation>div{padding:0 1rem}@media (max-width: 767px){.navigation>div{padding:0}}nav a{color:#a5a4a4}header ul{list-style:none;margin:0;padding:0}header .main-nav>nav>ul>li>a{font-family:'Raleway', 'Open Sans', Helvetica, Arial, sans-serif;font-weight:600;text-transform:uppercase;font-size:1rem;padding:1rem .8rem}header .main-nav>nav>ul>li:hover>a{background-color:#e35222;color:white;padding:1rem .8rem}header .toggle{width:6rem;height:6rem;box-sizing:border-box;padding:1.5rem;color:#cecbc5;position:absolute;top:0;right:0}header .toggle.-on .off{display:none}header .toggle.-off .on{display:none}header .toggle svg{box-sizing:border-box;color:#f1f1f1;padding:0.5rem;width:100%;height:100%}@media (min-width: 768px){header nav a{display:inline-block}header nav ul ul{display:none}header nav li{margin:0}header nav>ul>li{position:relative;display:inline-block;vertical-align:middle}header nav>ul>li>a svg{display:none}header nav>ul>li.search>a{font-size:0;width:20px;height:20px}header nav>ul>li.search>a svg{display:block;color:#a5a4a4;width:20px;height:20px}header nav>ul>li ul{background:#fff;position:absolute;min-width:250px}header nav>ul>li>ul{top:100%;left:0;text-align:left;white-space:nowrap;box-shadow:0 0 20px rgba(0,0,0,0.4)}header nav>ul>li>ul a{display:block;padding:0.75rem;position:relative;padding-right:24px}header nav>ul>li>ul a svg{width:14px;height:14px;position:absolute;right:4px;top:50%;transform:translateY(-50%)}header nav>ul>li>ul li:hover>a{color:#e35222}header nav>ul>li>ul>li{display:block;position:relative}header nav>ul>li>ul>li>ul{top:0;left:100%;box-shadow:0 0 10px rgba(0,0,0,0.25)}header nav.top{text-align:right;padding-top:.5rem}header nav.top>ul>li>a{padding:0.75rem}header nav.top>ul>li:hover>a{color:#e35222}header nav.top>ul>li>ul{background:#221e1b}header nav.top>ul>li>ul a:hover{background:#30302d}header nav ul>li:hover>ul{z-index:1;display:block}nav#mobile{display:none}}@media (max-width: 767px){body{padding-top:6rem}nav{background:#fff}nav#mobile{transition:all linear 0.2s;transform:translateX(100%);transform-origin:top;position:fixed;top:6rem;right:0;bottom:0;left:0;overflow:hidden}nav#mobile.-show{transform:none;height:auto}nav#mobile ul{list-style:none;margin:0;padding:0}nav#mobile li{margin:0}nav#mobile a{display:block;box-sizing:border-box;height:48px;line-height:48px;text-transform:uppercase}nav#mobile>ul{position:relative;height:100%}nav#mobile>ul>li>a{position:relative;color:#e35222;border-bottom:1px solid #e3e3e3}nav#mobile>ul>li>a svg{fill:#30302d}nav#mobile>ul>li>a svg:nth-of-type(2){color:red;position:absolute;right:0;top:0;width:12px;height:12px;padding:18px}nav#mobile>ul>li>a.-active{background:#e35222;color:#fff}nav#mobile>ul>li>a.-active+ul{z-index:9999;transform:translateX(0)}nav#mobile>ul>li>ul{position:absolute;top:0;right:0;bottom:0;left:48px;border-left:1px solid #cecbc5;background-color:#f1f1f1;transform:translateX(100%);transition:all cubic-bezier(0.42, 0, 0.58, 1) 0.255s;overflow-y:auto}nav#mobile>ul>li>ul svg{padding:4px;width:12px;height:12px;transition:all linear 0.2s}nav#mobile>ul>li>ul>li>a{padding-left:1rem;border-bottom:1px solid #cecbc5;border-left:none;border-right:none}nav#mobile>ul>li>ul>li>a.-callout{background:#fff;color:#e35222}nav#mobile>ul>li>ul>li:first-child a{border-top:1px solid #cecbc5}nav#mobile>ul>li>ul>li:last-child>a{border-bottom:none}nav#mobile>ul>li>ul>li>ul{position:relative;overflow:hidden;max-height:0;background-color:#e3e3e3;transition:all cubic-bezier(0.42, 0, 0.58, 1) 0.355s}nav#mobile>ul>li>ul>li>ul>li a{padding-left:2.5rem}nav#mobile>ul>li>ul>li>ul>li:last-child a{border-bottom:1px solid #cecbc5}nav#mobile>ul>li>ul>li a.-active>svg{transform:rotate(90deg)}nav#mobile>ul>li>ul>li a.-active+ul{max-height:1000px}nav#mobile svg{padding:14px;width:20px;height:20px;vertical-align:middle}}.-rotate-90{transform:rotate(90deg);transform-origin:center}.-rotate-180{transform:rotate(180deg);transform-origin:center}
</style>

<script language='JavaScript' type='text/javascript'>window.status='Loading...';</script>    <link rel="stylesheet" type="text/css" href="/ext-3.3.1/resources/css/ext-all-notheme.css?NS_VER=2019.2.0&minver=162" />
    <link rel="stylesheet" type="text/css" href="/ext-3.3.1/resources/css/xtheme-gray.css?NS_VER=2019.2.0&minver=162" />
    <link rel="stylesheet" type="text/css" href="/ext-3.3.1/resources/css/htmleditorplugins.css?NS_VER=2019.2.0&minver=162" />
    <link rel="stylesheet" type="text/css" href="/assets/legacy_timeselector/2344002951.css?NS_VER=2019.2.0&minver=162" />
    <link rel="stylesheet" type="text/css" href="/ext-3.3.1/resources/css/ext-override.css?NS_VER=2019.2.0&minver=162" />
    <link rel="stylesheet" type="text/css" href="/assets/reskin_core/3939430636.css?NS_VER=2019.2.0&minver=162" />
    <link rel="stylesheet" type="text/css" href="/assets/reskin_menu/720962082.css?NS_VER=2019.2.0&minver=162" />
    <link rel="stylesheet" type="text/css" href="/assets/reskin_popups/441208107.css?NS_VER=2019.2.0&minver=162" />
    <link rel="stylesheet" type="text/css" href="/assets/reskin_forms/2409777743.css?NS_VER=2019.2.0&minver=162" />
    <link rel="stylesheet" type="text/css" href="/assets/reskin_button/1980038069.css?NS_VER=2019.2.0&minver=162" />
    <script type='text/javascript' src='/ext-3.3.1/adapter/ext/ext-base.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/ext-3.3.1/ext-all.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/ext-3.3.1/ext-netsuite-ie9-fix.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/ext-3.3.1/extensions/plugins/Ext.ux.form.HtmlEditorExtensions.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/ui/jquery/jquery-3.4.1.min.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/ui/jquery/jquery_isolation.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/javascript/NLUtil.jsp?NS_VER=2019.2.0&minver=162&JSP_VER=2&locale=en_US'></script>
    <script type='text/javascript' src='/javascript/NLUtil.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/assets/legacy_timeselector/3214590533.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/javascript/NLCalendar.jsp?NS_VER=2019.2.0&minver=162&JSP_VER=2&locale=en_US'></script>
    <script type='text/javascript' src='/assets/help_service/1473679203.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/assets/legacy_apputil/2679683718.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/javascript/NLAppUtil.jsp?NS_VER=2019.2.0&minver=162&JSP_VER=2&locale=en_US'></script>
    <script type='text/javascript' src='/javascript/NLUIWidgets.jsp?NS_VER=2019.2.0&minver=162&JSP_VER=2&locale=en_US'></script>
    <script type='text/javascript' src='/assets/legacy_widgets/3279167104.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/assets/legacy_popup/129055756.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/javascript/NLPopup.jsp?NS_VER=2019.2.0&minver=162&JSP_VER=2&locale=en_US'></script>
    <script type='text/javascript' src='/assets/help/1664606291.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/javascript/NLAPI.jsp?NS_VER=2019.2.0&minver=162&JSP_VER=2&locale=en_US'></script>
    <script type='text/javascript' src='/assets/reskin_core/2391265970.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/assets/reskin_menu/3777302654.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/assets/reskin_forms/77548657.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/javascript/workflow/nextgen/runtime.jsp?NS_VER=2019.2.0&minver=162&JSP_VER=2&locale=en_US'></script>
    <script type='text/javascript' src='/assets/ui_messaging_api/2244232583.js?NS_VER=2019.2.0&minver=162'></script>
    <script type='text/javascript' src='/assets/ui_messaging_reskin/3019205048.js?NS_VER=2019.2.0&minver=162'></script>
<script language='JavaScript' type='text/javascript'>window.status='';</script><script type='text/javascript' src='/core/elements/compound/NLMapping.nl__t=null&NS_VER=2019.2.0&minver=162&locale=en_US.nlqs'></script>

<div class="-m-4 -bg-white -pad-all-2">

    <div class="-bg-tertiary3 -pad-all-2">
                
        <input id="custpage_tranid" name="custpage_tranid" placeholder="Sales order # / Estimate # / Invoice #" type="text"
            value="{custpage_tranid}" />
        <input id="custpage_ref_code" name="custpage_ref_code" placeholder="Payment Submission Reference Code" type="text"
            value="{custpage_ref_code}" />
        <input type="button" value="Find" onclick="find()">

        <#if records?has_content>            
            <table>
                <tbody>
                    <tr class="title">
                        <td width="20%">Date</td>
                        <td width="25%">Transaction Name</td>
                        <td width="10%" class="-text-center">Status</td>
                        <td width="10%" class="-text-center">Amount</td>
                        <td width="15%" class="-text-center">Minimum Payment Due</td>
                        <td width="15%" class="-text-center">Total Paid to Date</td>                        
                        <td width="15%" class="-text-center"></td>
                    </tr>
                    <#list records as item>
                        <tr>
                            <td colspan="6">&nbsp;</td>
                        </tr>
                        <tr>
                            <td>${item.trandate}</td>
                            <td>${item.transactionname}<br></td>
                            <td class="-text-center">${item.status}</td>
                            <td class="-text-center">${item.amount}</td>
                            <td class="-text-center"><#if item.recordtype == 'invoice'>${item.amountremaining}<#else>${item.custbody257}</#if></td>
                            <td class="-text-center"><#if item.recordtype == 'invoice'>${item.amountpaid}<#else>${deposits[0].applyingtransaction.amount}</#if></td>
                            <td class="-text-center">{pay}</td>
                        </tr>
                    </#list>
                </tbody>
            </table>
        <#else>
            <#if {search}>
                <br/>
                <b>Order could not be found. Please make sure you entered the correct information.</b>
                <br/>
            </#if>
        </#if>
        <br/>
        <div>
            
        </div>
    </div>
</div>

<script>
    function find() {
        var newUrl = window.location.href;

        var custpage_ref_code = window.document.getElementById('custpage_ref_code').value;
        var custpage_tranid = window.document.getElementById('custpage_tranid').value;

        if (newUrl.indexOf('custpage_ref_code') > -1) {
            newUrl = newUrl.replace(/(custpage_ref_code=).*?(&)/, '$1' + custpage_ref_code + '$2');
        } else {
            newUrl = newUrl + '&custpage_ref_code=' + custpage_ref_code;
        }

        if (newUrl.indexOf('custpage_tranid') > -1) {
            newUrl = newUrl.replace(/(custpage_tranid=).*?(&)/, '$1' + custpage_tranid + '$2');
        } else {
            newUrl = newUrl + '&custpage_tranid=' + custpage_tranid + '&a=0';
        }

        window.location.href = newUrl;
    }

    function payInvoice(id, name, userId, customer, hash, amount, recordType) {

        var url = 'https://414084.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=542&deploy=1&compid=414084&h=8d870e49dfa7d915ce31';

        var title = '';

        url += '&userid=' + userId +
            '&custpage_customer=' + customer +
            '&hash=' + hash +
            '&amount=' + amount +
            '&amountremaining=' + amount +
            '&invoicename=' + escape(name);

        if (recordType == 'invoice') {
            url += '&invoice=' + id;
            title = 'Pay Invoice';
        } else {
            url += '&transaction=' + id;
            title = 'Pay';
        }

        // Open the popup
        nlExtOpenWindow(url, '', 500, 400, '', false, title);        
    }

        
</script>