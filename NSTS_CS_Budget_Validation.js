/**
* Copyright (c) 1998-2017 NetSuite, Inc.
* 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
* All Rights Reserved.
* 
 * This software is the confidential and proprietary information of NetSuite, Inc. ("Confidential Information").
* You shall not disclose such Confidential Information and shall use it only in accordance with the terms of the license agreement
* you entered into with NetSuite.
* 
 * Version Type       Date              Author    Remarks
* 
/**
*@NApiVersion 2.x
*@NScriptType ClientScript
*/

define(['N/record', 'N/runtime', 'N/search', 'N/error', 'N/ui/dialog', 'N/format', 'N/log', 'N/currentRecord'],
    function (record, runtime, search, error, dialog, format, log, currentRecord) {

        /*var objScript = runtime.Script;
        var budgetCopySrchId = objScript.getParameter('custscript_');
        var ytdActualsSrchId = objScript.getParameter('custscript_');   
        var periodActualsSrchId = objScript.getParameter('custscript_');
        var lineLevelKey = objScript.getParameter('custscript_nsts_nfp_line_lev_key');*/
        lineLevelKey = 'custcol_cseg_npo_program,location,custcol_cseg_npo_grant,department';
        budgetLevelKey = 'custrecord_nfp_budg_program,custrecord_nfp_budg_location,custrecord_nfp_budg_grant,custrecord_nfp_budg_department,custrecord_nfp_budg_account';
        budgetLevelKeyDesc = 'Program,Account';
        budgetCopySrchId = 'customsearch_nfp_budg_copy_search';
        ytdActualsSrchId = 'customsearch_nfp_budg_trans_ytd';
        periodActualsSrchId = 'customsearch_nfp_budg_period';
        var lineLevelKeyList = lineLevelKey.split(',');
        var budgetLevelKeyList = budgetLevelKey.split(',');
        var validattionMsg = '';
        var NSUtil = (typeof NSUtil === 'undefined') ? {} : NSUtil;

        /**
         * Evaluate if the given string or object value is empty, null or undefined.
         * @param {String} stValue - string or object to evaluate
         * @returns {Boolean} - true if empty/null/undefined, false if not
         * <AUTHOR>
         * @memberOf NSUtil
         */
        NSUtil.isEmpty = function (stValue) {
            return ((stValue === '' || stValue == null || stValue == undefined) || (stValue.constructor === Array && stValue.length == 0) || (stValue.constructor === Object && (function (v) {
                for (var k in v)
                    return false;
                return true;
            })(stValue)));
        };

        /**
         * Evaluate if the given string is an element of the array, using reverse looping
         * @param {String} stValue - String value to find in the array
         * @param {String[]} arrValue - Array to be check for String value
         * @returns {Boolean} - true if string is an element of the array, false if not
         * @memberOf NSUtil
         */
        NSUtil.inArray = function (stValue, arrValue) {
            for (var i = arrValue.length - 1; i >= 0; i--) {
                if (stValue == arrValue[i]) {
                    break;
                }
            }
            return (i > -1);
        };

        /**
         * Round decimal number
         * @param {Number} flDecimalNumber - decimal number value
         * @param {Number} intDecimalPlace - decimal places
         *
         * @returns {Number} - a floating point number value
         * <AUTHOR> and lochengco
         */
        NSUtil.roundDecimalAmount = function (flDecimalNumber, intDecimalPlace) {
            //this is to make sure the rounding off is correct even if the decimal is equal to -0.995
            var bNegate = false;
            if (flDecimalNumber < 0) {
                flDecimalNumber = Math.abs(flDecimalNumber);
                bNegate = true;
            }

            var flReturn = 0.00;
            intDecimalPlace = (intDecimalPlace == null || intDecimalPlace == '') ? 0 : intDecimalPlace;

            var intMultiplierDivisor = Math.pow(10, intDecimalPlace);
            flReturn = Math.round((parseFloat(flDecimalNumber) * intMultiplierDivisor)) / intMultiplierDivisor;
            flReturn = (bNegate) ? (flReturn * -1) : flReturn;

            return flReturn.toFixed(intDecimalPlace);
        };

        /**
         * Converts string to float. If value is infinity or can't be converted to a number, 0.00 will be returned.
         * @param {String} stValue - any string
         * @returns {Number} - a floating point number
         * <AUTHOR>
         */
        NSUtil.forceFloat = function (stValue) {
            var flValue = parseFloat(stValue);

            if (isNaN(flValue) || (stValue == Infinity)) {
                return 0.00;
            }

            return flValue;
        };

        /**
         * Get all of the results from the search even if the results are more than 1000.
         * @param {String} stRecordType - the record type where the search will be executed.
         * @param {String} stSearchId - the search id of the saved search that will be used.
         * @param {nlobjSearchFilter[]} arrSearchFilter - array of nlobjSearchFilter objects. The search filters to be used or will be added to the saved search if search id was passed.
         * @param {nlobjSearchColumn[]} arrSearchColumn - array of nlobjSearchColumn objects. The columns to be returned or will be added to the saved search if search id was passed.
         * @returns {nlobjSearchResult[]} - an array of nlobjSearchResult objects
         * <AUTHOR> - initial version
         * <AUTHOR> - used concat when combining the search result
         */
        NSUtil.search = function (stRecordType, stSearchId, arrSearchFilter, arrSearchColumn) {
            if (stRecordType == null && stSearchId == null) {
                error.create({
                    name: 'SSS_MISSING_REQD_ARGUMENT',
                    message: 'search: Missing a required argument. Either stRecordType or stSearchId should be provided.',
                    notifyOff: false
                });
            }

            var arrReturnSearchResults = new Array();
            var objSavedSearch;

            var maxResults = 1000;

            if (stSearchId != null) {
                objSavedSearch = search.load({
                    id: stSearchId
                });

                // add search filter if one is passed
                if (arrSearchFilter != null) {
                    if (arrSearchFilter[0] instanceof Array || (typeof arrSearchFilter[0] == 'string')) {
                        objSavedSearch.filterExpression = objSavedSearch.filterExpression.concat(arrSearchFilter);
                    } else {
                        objSavedSearch.filters = objSavedSearch.filters.concat(arrSearchFilter);
                    }
                }

                // add search column if one is passed
                if (arrSearchColumn != null) {
                    objSavedSearch.columns = objSavedSearch.columns.concat(arrSearchColumn);
                }
            } else {
                objSavedSearch = search.create({
                    type: stRecordType
                });

                // add search filter if one is passed
                if (arrSearchFilter != null) {
                    if (arrSearchFilter[0] instanceof Array || (typeof arrSearchFilter[0] == 'string')) {
                        objSavedSearch.filterExpression = arrSearchFilter;
                    } else {
                        objSavedSearch.filters = arrSearchFilter;
                    }
                }

                // add search column if one is passed
                if (arrSearchColumn != null) {
                    objSavedSearch.columns = arrSearchColumn;
                }
            }

            var objResultset = objSavedSearch.run();
            var intSearchIndex = 0;
            var arrResultSlice = null;
            do {
                arrResultSlice = objResultset.getRange(intSearchIndex, intSearchIndex + maxResults);
                if (arrResultSlice == null) {
                    break;
                }

                arrReturnSearchResults = arrReturnSearchResults.concat(arrResultSlice);
                intSearchIndex = arrReturnSearchResults.length;
            }
            while (arrResultSlice.length >= maxResults);

            return arrReturnSearchResults;
        };

        /*function pageInit(context)
        {
            var stLogTitle = 'pageInit';
            log.debug(stLogTitle, '** START **');
            try{
                var currentRecord = context.currentRecord;
                var recordID = currentRecord.id;

            }           
            catch(error){                   
                log.debug(stLogTitle, 'error: '+ error);
            }
        }

        function fieldChanged(context) {
            
            var sublistName = context.sublistId;
            var fieldName = context.fieldId;
            var line = context.line;
            var stLogTitle = 'fieldChanged';
            log.debug(stLogTitle, '** START **');
            try{
                if (fieldName == '')
                {

                }

            }
            catch(error){                   
                log.debug(stLogTitle, 'error: '+ error);
            }
        }

        function validateField(context) {
            
            var sublistName = context.sublistId;
            var fieldName = context.fieldId;
            var line = context.line;
            var stLogTitle = 'validateField';
            log.debug(stLogTitle, '** START **');
            try{
                if (fieldName == '')
                {

                }
            }
            catch(error){                   
                log.debug(stLogTitle, 'error: '+ error);
            }
            return true;
        }

        function validateLine(context) {
            var currentRecord = context.currentRecord;
            var sublistName = context.sublistId;
            var line = context.line;
            var stLogTitle = 'validateLine';
            log.debug(stLogTitle, '** START **');
            try {

            }
            catch(error){                   
                log.debug(stLogTitle, 'error: '+ error);
            }
            return true;
        }
    */
        function saveRecord(context) {
            var currentRecord = context.currentRecord;
            var stLogTitle = 'saveRecord';
            log.debug(stLogTitle, '** START ** ' + new Date());
            try {
                if (!validateAndSave()) {
                    log.debug(stLogTitle, '** END ** ' + new Date());

                    return false;
                }

                if (validattionMsg != '')
                    currentRecord.setValue('custbody_nfp_bdg_po_val_exception', validattionMsg);
                else
                    currentRecord.setValue('custbody_nfp_bdg_po_val_exception', '');

            } catch (error) {
                log.debug(stLogTitle, 'error: ' + error);
            }
            log.debug(stLogTitle, '** END ** ' + new Date());
            return true;
        }

        /*
            This function return true if stValue is empty
        */
        function isEmpty(stValue) {
            if ((stValue == '') || (stValue == null) || (stValue == undefined)) {
                return true;
            }
            return false;
        }

        function validateAndSave() {
            var retVal = true;
            validattionMsg = '';
            var objRecord = currentRecord.get();
            var currentRecordId = objRecord.id;
            var consCurrentData = {};
            if (objRecord.getLineCount({
                    sublistId: 'item'
                }) + objRecord.getLineCount({
                    sublistId: 'expense'
                }) > 0) {
                consCurrentData = getConsCurrentData(objRecord, consCurrentData);
                if (consCurrentData == false)
                return false;
            }
            log.debug({
                title: 'validateAndSave',
                title: 'Final consCurrentData : ' + JSON.stringify(consCurrentData) + ' : ' + new Date()
            });
            var dispMsg = '';
            var keys = Object.keys(consCurrentData);
            for (var intPos = 0; intPos < keys.length; intPos++) {
                var key = keys[intPos];
                var value = consCurrentData[key];
                if (value < 0) {
                    dispMsg += '\n' + budgetLevelKeyDesc + ' : ' + Math.abs(value.toFixed(0));
                }
            }
            if (dispMsg != '') {
                validattionMsg = 'This Transaction will result in out of budget scenario for the following  - ' + dispMsg;
                retVal = confirm(validattionMsg);
            }
            //alert('This Transaction will result in out of budget scenario for the following  - \nSegment 1, Segment 2 � (Amount 2000 ; Budget =12000$, Encumbered = 1000$, Actual = 10000$)\n Segment 1, Segment 4 � (Amount 3000 ; Budget =12000$, Encumbered = 1000$, Actual = 11000$)');
            //alert('Validation successfull..');
            return retVal;
        }

        function getConsCurrentData(objRecord, retData) {
            var linekey = [];
            var arrFilters = [],
                arrColumns = [];
            try {
                for (var intKeyPos = 0; intKeyPos < lineLevelKeyList.length; intKeyPos++) {
                    var key = lineLevelKeyList[intKeyPos];
                    arrColumns.push(search.createColumn({
                        name: key,
                        summary: 'GROUP'

                    }));
                }
                arrColumns.push(search.createColumn({
                    name: 'account',
                    summary: 'GROUP'
                }));
                var subListId = 'item';
                var intLineCount = objRecord.getLineCount({
                    sublistId: subListId
                });
                for (var intPos = 0; intPos < intLineCount; intPos++) {
                    var keyValuesList = [];
                    for (var intKeyPos = 0; intKeyPos < lineLevelKeyList.length; intKeyPos++) {
                        var key = lineLevelKeyList[intKeyPos];
                        var value = objRecord.getSublistValue({
                            fieldId: key,
                            sublistId: subListId,
                            line: intPos
                        });
                        keyValuesList.push(value);

                    }
                    var amount = objRecord.getSublistValue({
                        fieldId: 'amount',
                        sublistId: subListId,
                        line: intPos
                    });
                    var account = objRecord.getSublistValue({
                        fieldId: 'custcol_nfp_bdg_purch_item_acc',
                        sublistId: subListId,
                        line: intPos
                    });
                    keyValuesList.push(account);
                    var keyValues = keyValuesList.join('-');
                    
                if (retData[keyValues] == undefined || retData[keyValues] == 'undefined') {
                        retData[keyValues] = 0;
                       linekey.push(keyValues);
                    }
                    retData[keyValues] = parseFloat(retData[keyValues]) + parseFloat(amount);
                }
                subListId = 'expense';
                intLineCount = objRecord.getLineCount({
                    sublistId: subListId
                });
                for (var intPos = 0; intPos < intLineCount; intPos++) {
                    var keyValuesList = [];
                    for (var intKeyPos = 0; intKeyPos < lineLevelKeyList.length; intKeyPos++) {
                        var key = lineLevelKeyList[intKeyPos];
                        var value = objRecord.getSublistValue({
                            fieldId: key,
                            sublistId: subListId,
                            line: intPos
                        });
                        keyValuesList.push(value);

                    }
                    var amount = objRecord.getSublistValue({
                        fieldId: 'amount',
                        sublistId: subListId,
                        line: intPos
                    });
                    var account = objRecord.getSublistValue({
                        fieldId: 'account',
                        sublistId: 'expense',
                        line: intPos
                    });

                    keyValuesList.push(account);
                    var keyValues = keyValuesList.join('-');
                    if (retData[keyValues] == undefined || retData[keyValues] == 'undefined') {
                        retData[keyValues] = 0;
                    }
                    retData[keyValues] = parseFloat(retData[keyValues]) + parseFloat(amount);
                }

                log.debug({
                    title: 'getConsCurrentData',
                    title: 'Current record retdata: ' + JSON.stringify(retData) + ' : ' + new Date()
                });
                if (!NSUtil.isEmpty(objRecord.id)) {
                    arrFilters.push(search.createFilter({
                        name: 'internalid',
                        operator: search.Operator.NONEOF,
                        values: [objRecord.id]
                    }));
                }
                arrFilters.push(search.createFilter({
                    name: 'subsidiary',
                    operator: search.Operator.ANYOF,
                    values: [objRecord.getValue('subsidiary')]
                }));
                //  arrFilters.push(search.createFilter({name : 'currency',operator : search.Operator.ANYOF,values : [objRecord.getValue('currency')] }));

                var txnList = NSUtil.search('transaction', ytdActualsSrchId, arrFilters, arrColumns);
                log.debug({
                    title: 'getConsCurrentData',
                    details: 'txnList : ' + (txnList != null ? txnList.length : 0) + ' : ' + new Date()
                });

                for (var intIndex = 0; txnList != null && intIndex < txnList.length; intIndex++) {
                    var keyValuesList = [];
                    for (var intKeyPos = 0; intKeyPos < lineLevelKeyList.length; intKeyPos++) {
                        var key = lineLevelKeyList[intKeyPos];
                        var value = txnList[intIndex].getValue({
                            name: key,
                            summary: 'GROUP'
                        });
                        keyValuesList.push(value);
                    }
                    var account = txnList[intIndex].getValue({
                        name: 'account',
                        summary: 'GROUP'
                    });

                    keyValuesList.push(account);
                    var amount = getFormulaValue(txnList[intIndex], 'Actual+Encumbered for YTD');
                    if (NSUtil.isEmpty(amount))
                        amount = 0;
                    //log.debug( { title: 'Txn', details: 'account: ' + account + ',amount: ' + amount })
                    var keyValues = keyValuesList.join('-');
                    //alert('Txn : ' + keyValues + ' : ' + retData[keyValues]);
                    if (!NSUtil.isEmpty(retData[keyValues]) && !isNaN(parseFloat(retData[keyValues])) && parseFloat(retData[keyValues]) > 0) {
                        retData[keyValues] = parseFloat(retData[keyValues]) + parseFloat(amount);
                    }
                }
                //alert('After getting current record + txn : ' + JSON.stringify(retData));
                arrFilters = [], arrColumns = [];
                for (var intKeyPos = 0; intKeyPos < budgetLevelKeyList.length; intKeyPos++) {
                    var key = budgetLevelKeyList[intKeyPos];
                    log.debug({ title: 'keyValues', title: 'keyValues : ' + key });
                    arrColumns.push(search.createColumn({
                        name: key

                    }));
                }
                arrColumns.push(search.createColumn({
                    name: 'custrecord_nfp_budg_amount'
                }));

                arrFilters.push(search.createFilter({
                    name: 'custrecord_nfp_budg_subsidiary',
                    operator: search.Operator.ANYOF,
                    values: [objRecord.getValue('subsidiary')]
                }));
                //  arrFilters.push(search.createFilter({name : 'custrecord_nfp_budg_currency',operator : search.Operator.ANYOF,values : [objRecord.getValue('currency')] }));
                var budgetList = NSUtil.search('customrecord_budget_copy', budgetCopySrchId, arrFilters, arrColumns);
                log.debug({
                    title: 'getConsCurrentData',
                    details: 'budgetList : ' + (budgetList != null ? budgetList.length : 0) + ' : ' + new Date()
                });
              var budget_check_alert=objRecord.getValue('custbody_ignore_no_budget_check');
              var budget_check_count=0;
                var budgetdata={};
                for (var intIndex = 0; budgetList != null && intIndex < budgetList.length; intIndex++) {
                    var keyValuesList = [];
                    for (var intKeyPos = 0; intKeyPos < budgetLevelKeyList.length; intKeyPos++) {
                        var key = budgetLevelKeyList[intKeyPos];
                        var value = budgetList[intIndex].getValue({
                            name: key
                        });
                        //log.debug({ title: 'value budget', title: 'value : ' + value });
                        keyValuesList.push(value);
                    }
                    var amount = budgetList[intIndex].getValue({
                        name: 'custrecord_nfp_budg_amount'
                    });
               
                    var keyValues = keyValuesList.join('-');
             
               
                    //sjain start
                    var budget_exist = false;
                   
                    for (var intKeyPos1 = 0; intKeyPos1 < linekey.length; intKeyPos1++) {
                       
                        var linkey_check = linekey[intKeyPos1];
                        
                        if (keyValues == linkey_check) {
                           
                            if (budgetdata[linkey_check] == undefined || budgetdata[linkey_check] == 'undefined') {
                                budgetdata[linkey_check] = 0;
                                budget_check_count = budget_check_count + 1;
                            }
                         
                       }
                    }
                  
              
                    
                    //sjain end
                    //alert( 'Budget - ' + keyValues + ' : ' + retData[keyValues]);
                    if (!NSUtil.isEmpty(retData[keyValues]) && parseFloat(retData[keyValues]) > 0) {
                        retData[keyValues] = parseFloat(amount) - parseFloat(retData[keyValues]);
                    }
                }
              
                if (budget_check_count != linekey.length && budget_check_alert == false ) {
                    alert('Budget does not exsist for one of the combinations');
                    return false;
                }

            } catch (ex) {
                alert('Error getting data : ' + ex.toString());
            }
            //alert('Out retdata : ' + JSON.stringify(retData));
            return retData;
        }

        function getFormulaValue(srchRow, label) {
            var retVal = '';
            var cols = srchRow.columns;
            cols.forEach(function (c) {
                if (c.label == label) {
                    retVal = srchRow.getText(c) || srchRow.getValue(c);
                    if (retVal == '- None -')
                        retVal = '';
                }
            });
            return retVal;
        }

        return {

            /*pageInit: pageInit,
            fieldChanged: fieldChanged,
            fieldChanged: fieldChanged,
            validateField: validateField,
            validateLine: validateLine,*/
            saveRecord: saveRecord,
            getConsCurrentData: getConsCurrentData,
            validateAndSave: validateAndSave
        };
    }
);