/**
 * Sales Order - Before Submit
 * 
 * Version    Date            Author           Remarks
 * 1.00       23 Aug 2017     Marcel P		   Initial Version
 *
 */

/**
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      
 * @returns {Void}
 */
function beforeSubmit(type) {
	
	try {		
		// On creation. WooCommerce orders
		if (type == 'create' && nlapiGetFieldValue('custbody_in8_wc_order_id')) {		
			
			// If the total is greater or equal to 1000, update the memo field
			if (parseFloat(nlapiGetFieldValue('total')) >= 1000) {
				
				nlapiSetFieldValue('memo', 'SIGNATURE REQUIRED');
			}
		}
	} catch(e) {
		nlapiLogExecution('DEBUG', 'Error', 'Error executing script. ' + e);
	}
}
