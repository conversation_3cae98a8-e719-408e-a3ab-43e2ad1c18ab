/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function afterSubmit(context) {
        var recObj = record.load({
            type: record.Type.VENDOR_BILL,
            id: context.newRecord.id
        });

        var lineCount = recObj.getLineCount({
            sublistId: 'expense'
        });
        if(lineCount < 100) {
            for(var i = 0; i < lineCount; i++){
                var custJobId = recObj.getSublistValue({
                    sublistId: 'expense',
                    fieldId: 'customer',
                    line: i
                });

                if(custJobId){
                    var custJob = record.load({
                        type: record.Type.JOB,
                        id: custJobId,
                        isDynamic: true,
                    });
                    
                    var specialty = custJob.getValue({ fieldId: 'cseg_specialty_' });
                    var profession = custJob.getValue({ fieldId: 'custentity_profession' });
                    var clientManager = custJob.getValue({ fieldId: 'custentity_client_manager' });
                    var recruiter = custJob.getValue({ fieldId: 'custentity_recruiter_sourced' });
                    var projStart = custJob.getValue({ fieldId: 'startdate' });
                    var projEnd = custJob.getValue({ fieldId: 'enddate' });
                    recObj.setSublistValue({
                        sublistId: 'expense',
                        fieldId: 'cseg_specialty_',
                        line: i,
                        value: specialty
                    });
                    recObj.setSublistValue({
                        sublistId: 'expense',
                        fieldId: 'department',
                        line: i,
                        value: profession
                    });
                    recObj.setSublistValue({
                        sublistId: 'expense',
                        fieldId: 'custcolclient_manager',
                        line: i,
                        value: clientManager
                    });
                    recObj.setSublistValue({
                        sublistId: 'expense',
                        fieldId: 'custcolrecruiter',
                        line: i,
                        value: recruiter
                    });
                    recObj.setSublistValue({
                        sublistId: 'expense',
                        fieldId: 'custcol_project_start_date',
                        line: i,
                        value: projStart
                    });
                    recObj.setSublistValue({
                        sublistId: 'expense',
                        fieldId: 'custcol_project_end_date',
                        line: i,
                        value: projEnd
                    });
                }
            }
        }
        recObj.save();

    }

    return {
        afterSubmit: afterSubmit
    }
});
