<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%; margin: 7px;">
                    <tr>
                        <td align="left" rowspan="5" style="padding: 0;">
                            <#if record.department=="TMG - C Décor"><img src="${record.subsidiary.custrecord_vima_logo}"
                                    style="float: left; height:80%; width:80%;" />
                                <#else><img src="${record.subsidiary.custrecord_vima_logo}"
                                        style="float: left; height:20%; width: 20%;" />
                            </#if>
                            <table>
                                <tr>
                                    <td colspan="1">
                                        ${record.subsidiary}<br />${companyInformation.mainaddress_text}<br />(512)-243-5645
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td align="right"><span class="title"
                                style="float:center"><b>${record@title}</b></span><br /><br /><span
                                style="float: left; font-size:8pt;"><b>Order
                                    #</b>:&nbsp;${record.tranid}<br /><br /><b>Date</b>:&nbsp;${record.trandate}<br /><br /><b>Sales
                                    Rep</b>:&nbsp;${record.salesrep}<br /><br /><b>PO
                                    Number</b>:&nbsp;${record.otherrefnum}<br /><br /><b>Reference
                                    No.</b>:&nbsp;${record.custbody_reference} </span></td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
            }

            th {
                font-weight: bold;
                font-size: 9pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            table.header td {
                padding: 0px;
                font-size: 9pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
            }

            td.totalboxtop {
                font-size: 12pt;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="18%" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
        <table style="width: 100%; margin-top: 10px;  margin: 7px;">
            <tr>
                <td colspan="5" style="font-size: 9pt; padding: 6px 0 2px;"><b>Customer:</b>&nbsp;
                    ${record.entity.altname?keep_after(" : ")}</td>
            </tr>
            <tr>
                <td colspan="4" style="font-size: 9pt; padding: 6px 0 2px; font-weight: bold; color: #333333;">
                    ${record.billaddress@label}</td>
                <td colspan="4" style="font-size: 9pt; padding: 6px 0 2px; font-weight: bold; color: #333333;">
                    ${record.shipaddress@label}</td>
                <td colspan="5" style="font-size: 12pt; background-color: #e3e3e3; font-weight: bold;">
                    ${record.total@label?upper_case}</td>
            </tr>
            <tr>
                <td colspan="4" style="padding: 0;line-height:10">
                    <#if record.billingaddress.addressee?has_content>${record.billingaddress.addressee}<br /></#if>
                    <#if record.billingaddress.attention?has_content> ${record.billingaddress.attention}<br /></#if>
                    <#if record.billingaddress.addr1?has_content> ${record.billingaddress.addr1}<br /></#if>
                    <#if record.billingaddress.addr2?has_content> ${record.billingaddress.addr2}<br /></#if>
                    <#if record.billingaddress.city?has_content>
                        ${record.billingaddress.city},${record.billingaddress.state}&nbsp;${record.billingaddress.zip}<br />
                    </#if> ${record.entity.phone}
                </td>
                <td colspan="4" style="padding: 0;line-height:10">
                    <#if record.shippingaddress.addressee?has_content>${record.shippingaddress.addressee}<br /></#if>
                    <#if record.shippingaddress.attention?has_content> ${record.shippingaddress.attention}<br /></#if>
                    <#if record.shippingaddress.addr1?has_content> ${record.shippingaddress.addr1}<br /></#if>
                    <#if record.shippingaddress.addr2?has_content> ${record.shippingaddress.addr2}<br /></#if>
                    <#if record.shippingaddress.city?has_content>
                        ${record.shippingaddress.city},${record.shippingaddress.state}&nbsp;${record.shippingaddress.zip}<br />
                    </#if> ${record.custbody_ns_shiptophone}
                </td>
                <td align="right" colspan="5" style="font-size: 28pt; padding-top: 20px; background-color: #e3e3e3;">
                    ${record.total}</td>
            </tr>
        </table>

        <table class="body" style="width: 668px; margin-top: 10px;">
            <tr>
                <th colspan="7" style="width: 119px;"><span style="font-size:11px;">Order Contact</span></th>
                <th style="width: 94px;"><span style="font-size:11px;">${record.terms@label}</span></th>
                <th colspan="5" style="width: 74px;"><span style="font-size:11px;">Due Date</span></th>
                <th colspan="7" style="width: 125px;"><span style="font-size:11px;">${record.shipmethod@label}</span>
                </th>
                <th style="width: 174px;"><span style="font-size:11px;">Tracking</span></th>
            </tr>
            <tr>
                <td colspan="7" style="width: 119px;"><span
                        style="font-size:11px;">${record.custbody_tmg_so_ordered_by}</span></td>
                <td style="width: 94px;"><span style="font-size:11px;">${record.terms}</span></td>
                <td colspan="5" style="width: 74px;"><span style="font-size:11px;">${record.duedate}</span></td>
                <td colspan="7" style="width: 125px;"><span style="font-size:11px;">${record.shipmethod}</span></td>
                <td style="width: 174px;"><span style="font-size:11px;">${record.custbody_ns_backendtracking}</span>
                </td>
            </tr>
        </table>
        <#if record.item?has_content>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <#if record.department=="TMG - C Décor">
                                    <th align="left" colspan="4">${item.item.custitem_acct_code@label}</th>
                                </#if>
                                <th colspan="12">${item.item@label}</th>
                                <th align="center" colspan="4">${item.quantity@label}</th>
                                <th align="center" colspan="4"><b>Units</b></th>
                                <th align="right" colspan="5">${item.rate@label}</th>
                                <th align="right" colspan="5">${item.amount@label}</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <#if record.department=="TMG - C Décor">
                            <td align="left" colspan="4">${item.item.custitem_acct_code}</td>
                        </#if>
                        <td colspan="12" style="font-size:7pt"><span
                                style="font-weight: bold; line-height: 150%; color: #333333;">${item.item}</span><br />${item.description}
                        </td>
                        <td align="right" colspan="4" line-height="150%">${item.quantity}</td>
                        <td align="right" colspan="4" line-height="150%">${item.item.custitem_units_of_measure}</td>
                        <td align="right" colspan="5">${item.rate}</td>
                        <td align="right" colspan="5">${item.amount}</td>
                    </tr>
                </#list><!-- end items -->
            </table>

            <hr style="width: 100%; color: #d3d3d3; background-color: #d3d3d3; height: 1px;" />
        </#if>
        <table style="page-break-inside: avoid; width: 100%; margin-top: 10px;">
            <tr>
                <#if record.message?has_content>
                    <td align="left" width="30%"><b>${record.message@label}:</b><br />${record.message}</td>
                </#if>
                <td colspan="4">&nbsp;</td>
            </tr>
            <tr>
                <td colspan="6">&nbsp;</td>
                <td align="right" colspan="3" style="font-weight: bold; color: #333333;">${record.subtotal@label}</td>
                <td align="right" colspan="3">${record.subtotal}</td>
            </tr>
            <tr>
                <td colspan="6">&nbsp;</td>
                <td align="right" colspan="3" style="font-weight: bold; color: #333333;">${record.shippingcost@label}
                </td>
                <td align="right" colspan="3">${record.shippingcost}</td>
            </tr>
            <tr>
                <td colspan="6">&nbsp;</td>
                <td align="right" colspan="3" style="font-weight: bold; color: #333333;">${record.taxtotal@label}
                    (${record.taxrate}%)</td>
                <td align="right" colspan="3">${record.taxtotal}</td>
            </tr>
            <tr style="background-color: #e3e3e3; line-height: 200%;">
                <td background-color="#ffffff" colspan="6">&nbsp;</td>
                <td align="right" colspan="3" style="font-weight: bold; color: #333333;">Total</td>
                <td align="right" colspan="3">${record.total}</td>
            </tr>
            <#assign total_payments=record.custpage_total_payments?number>
            <tr>
                <td colspan="6">&nbsp;</td>
                <td align="right" colspan="3" style="font-weight: bold; color: #333333;">Payment Received</td>
                <td align="right" colspan="3">${total_payments?string.currency}</td>
            </tr>
            <tr>
                <td colspan="6">&nbsp;</td>
                <td align="right" colspan="3" style="font-weight: bold; color: #333333;">Balance Due</td>
                <td align="right" colspan="3">${record.amountremaining}</td>
            </tr>
        </table>
        &nbsp;

        <div style="text-align: right;">&nbsp;</div>
    </body>
</pdf>