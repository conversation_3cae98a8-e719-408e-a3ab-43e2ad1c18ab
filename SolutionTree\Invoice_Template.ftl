<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

	<head>
		<link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
			src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
			src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
		<#if .locale=="zh_CN">
			<link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
				src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
			<#elseif .locale=="zh_TW">
				<link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
					src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
				<#elseif .locale=="ja_JP">
					<link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
						src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
					<#elseif .locale=="ko_KR">
						<link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
							src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
						<#elseif .locale=="th_TH">
							<link name="NotoSansThai" type="font" subtype="opentype"
								src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
		</#if>
		<macrolist>
			<macro id="nlheader">
				<table class="header" style="width: 100%;">
					<tr>
						<td>${subsidiary.logo}</td>
						<td rowspan="5"><br /><br /><br /><span
								style="background-color: rgb(255, 255, 255); font-size: 11px;">Phone:
								${subsidiary.addrphone}</span><br
								style="font-size: 13.3333px; background-color: rgb(255, 255, 255);" /><span
								style="background-color: rgb(255, 255, 255); font-size: 11px;">Fax:
								${subsidiary.fax}</span></td>
						<td rowspan="5"><br /><br /><br /><span
								style="font-size:11px;">${subsidiary.custrecordweb}</span></td>
						<td align="right" rowspan="2"><span style="font-size:16px;"><span
									class="title">${record@title}</span></span></td>
					</tr>
					<tr>
						<td colspan="1" rowspan="4"><span
								style="font-size: 11px; background-color: rgb(255, 255, 255);">${subsidiary.addrText}</span>
						</td>
					</tr>
					<tr>
						<td align="right"><span style="font-size:16px;"><span
									class="number">#${record.tranid}</span></span></td>
					</tr>
					<tr>
						<td align="right" rowspan="2"><span style="font-size:14px;">${record.trandate}</span></td>
					</tr>
				</table>
			</macro>
			<macro id="nlfooter">
				<p><br /><br />&nbsp;</p>
			</macro>
		</macrolist>
		<style type="text/css">
			table {
				<#if .locale=="zh_CN">font-family: stsong, sans-serif;
				<#elseif .locale=="zh_TW">font-family: msung, sans-serif;
				<#elseif .locale=="ja_JP">font-family: heiseimin, sans-serif;
				<#elseif .locale=="ko_KR">font-family: hygothic, sans-serif;
				<#elseif .locale=="ru_RU">font-family: verdana;
				<#else>font-family: sans-serif;
				</#if>font-size: 9pt;
				table-layout: fixed;
			}

			th {
				font-weight: bold;
				font-size: 8pt;
				vertical-align: middle;
				padding: 5px 6px 3px;
				background-color: #e3e3e3;
				color: #333333;
			}

			td {
				padding: 4px 6px;
			}

			b {
				font-weight: bold;
				color: #333333;
			}

			table.header td {
				padding: 0px;
				font-size: 10pt;
			}

			table.footer td {
				padding: 0px;
				font-size: 8pt;
			}

			table.itemtable th {
				padding-bottom: 10px;
				padding-top: 10px;
			}

			table.body td {
				padding-top: 2px;
			}

			table.total {
				page-break-inside: avoid;
			}

			tr.totalrow {
				background-color: #e3e3e3;
				line-height: 200%;
			}

			td.totalboxtop {
				font-size: 12pt;
				background-color: #e3e3e3;
			}

			td.addressheader {
				font-size: 8pt;
				padding-top: 6px;
				padding-bottom: 2px;
			}

			td.address {
				padding-top: 0px;
			}

			td.totalboxmid {
				font-size: 28pt;
				padding-top: 20px;
				background-color: #e3e3e3;
			}

			td.totalboxbot {
				background-color: #e3e3e3;
				font-weight: bold;
			}

			span.title {
				font-size: 28pt;
			}

			span.number {
				font-size: 16pt;
			}

			span.itemname {
				font-weight: bold;
				line-height: 150%;
			}

			hr {
				width: 100%;
				color: #d3d3d3;
				background-color: #d3d3d3;
				height: 1px;
			}
		</style>
	</head>

	<body header="nlheader" header-height="8%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in"
		size="Letter">
		&nbsp;
		<table style="width: 100%; margin-top: 0px; margin-left: 30px;">
			<tr>
				<td class="addressheader" colspan="9" rowspan="1"><strong>${record.billaddress@label}</strong></td>
				<td class="addressheader" colspan="5" rowspan="1"><strong>${record.shipaddress@label}</strong></td>
				<td class="totalboxtop" colspan="4"><b>${record.amountremaining@label?upper_case}</b></td>
			</tr>
			<tr>
				<td class="address" colspan="9" rowspan="2">${record.billaddress}</td>
				<td class="address" colspan="5" rowspan="2">${record.shipaddress}</td>
				<td align="right" class="totalboxmid" colspan="4"><span
						style="font-size:20px;">${record.amountremaining}</span></td>
			</tr>
			<tr>
				<td align="right" class="totalboxbot" colspan="4"><b>${record.duedate@label}:</b> ${record.duedate}</td>
			</tr>
		</table>

		<table class="body" style="width: 100%; margin-top: 10px;">
			<tr>
				<th>${record.otherrefnum@label}</th>
				<th>${record.duedate@label}</th>
				<th>${record.terms@label}</th>
				<th>Customer ID</th>
				<th>${record.shipmethod@label}</th>
			</tr>
			<tr>
				<td>${record.otherrefnum}</td>
				<td>${record.duedate}</td>
				<td>${record.terms}</td>
				<td>${record.custbody13}</td>
				<td>${record.shipmethod}</td>
			</tr>
		</table>
		<#if record.item?has_content>

			<table class="itemtable" style="width: 100%; margin-top: 10px;">
				<!-- start items -->
				<#list record.item as item>
					<#if item_index==0>
						<thead>
							<tr>
								<th colspan="9" style="width: 275px;">${item.item@label}</th>
								<th colspan="4" rowspan="1" style="text-align: left; width: 115px;">ISBN</th>
								<th colspan="4" rowspan="1" style="text-align: left;">${item.rate@label}</th>
								<th colspan="4" rowspan="1" style="text-align: left;">${item.quantity@label}</th>
								<th colspan="4" rowspan="1" style="text-align: left;">Extension</th>
							</tr>
						</thead>
					</#if>
					<#assign itemrate=item.rate />
					<#if (itemrate < 0)>
						<#assign itemratepositive=(itemrate*(-1)) />
						<#else>
							<#assign itemratepositive=(itemrate *1) />
					</#if>
					<#if (item.custcol_item_type)?contains("Discount")>
                        <#assign itemratec=(itemratepositive*100) />
                        <#if itemratec gt 100>
                            <#--  <#assign itemratedoubleround=(itemratec?number)?round />  -->
                            <#--  <#assign itemratediv=(itemratec/2) />  -->
                            <#assign itemrate="(" + itemratec?string(",##0.00") + ")" />
                        <#else>
                            <#assign itemrate="(" + itemratec?string[",##0.00;; roundingMode=halfUp"] + "%" + ")" />
                        </#if>
					</#if>
					<tr>
						<td colspan="9" style="width: 275px;"><span
								class="itemname">${item.item}</span><br />${item.description}</td>
						<td colspan="4" rowspan="1" style="width: 115px; text-align: justify;">${item.upccode}</td>
						<td colspan="4" rowspan="1" style="width: 20px; text-align: justify;">${itemrate}</td>
						<td colspan="4" rowspan="1" style="text-align: justify;">${item.quantity}</td>
						<td colspan="4" rowspan="1" style="text-align: justify;">${item.amount}</td>
					</tr>
				</#list><!-- end items -->
			</table>

			<hr />
		</#if><span style="font-size:11px;">${record.message}</span><br /><br /><span
			style="font-size:11px;">${record.custbody8}</span><br /><br /><span
			style="font-size:11px;">${record.custbody16}</span><br /><br />
		<#if record.subsidiary="Marzano Resources"><span style="font-size:11px;">To pay for this invoice via credit
				card, please visit payments.marzanoresources.com.</span></#if>
		<#if record.subsidiary="Solution Tree Inc."><span style="font-size:11px;">To pay for this invoice via credit
				card, please visit payments.solutiontree.com.</span></#if>
		<#if record.subsidiary="Solution Tree Canada"><span style="font-size:11px;">To pay for this invoice via credit
				card, please visit payments.solutiontree.com.ca.</span></#if><br /><br />
		<#if record.subsidiary="Marzano Resources"><span style="font-size:11px;">Effective June 11, 2019, Marzano
				Research LLC has changed its name to Marzano Resources LLC. Please update your records accordingly. For
				more information or to obtain an updated W-9, please visit www.MarzanoResources.com.</span></#if>
		<br /><br /><span style="font-size:11px;">Tracking Number(s):</span>&nbsp;&nbsp;<span
			style="font-size:11px;">${record.linkedtrackingnumbers}</span>
		<#if record.tax2total?has_content>

			<table class="total" style="width: 100%; margin-top: 10px;">
				<tr>
					<th><strong>${record.subtotal@label}</strong></th>
					<th>GST</th>
					<th>PST</th>
					<th><strong>${record.shippingcost@label}</strong></th>
					<th><strong>${record.total@label}</strong></th>
					<th align="right"><b
							style="text-align: -webkit-right; background-color: rgb(227, 227, 227);">${record.amountpaid@label}</b>
					</th>
					<th align="right"><b
							style="text-align: -webkit-right; background-color: rgb(227, 227, 227);">${record.amountremaining@label}</b>
					</th>
				</tr>
				<tr>
					<td>${record.subtotal}</td>
					<td>${record.taxtotal}</td>
					<td>${record.tax2total}</td>
					<td>${record.shippingcost}</td>
					<td>${record.total}</td>
					<td align="right"><span
							style="text-align: -webkit-right; background-color: rgb(255, 255, 255);">${record.amountpaid}</span>
					</td>
					<td align="right"><span style="text-align: -webkit-right;"><span
								style="background-color:#FFFFFF;">${record.amountremaining}</span></span></td>
				</tr>
			</table>
			<br /><span style="font-size:11px;">GST Registration Number - *********</span>
			<#else>

				<table class="total" style="width: 100%; margin-top: 10px;">
					<tr>
						<th><strong>${record.subtotal@label}</strong></th>
						<th>Sales&nbsp;<strong>${record.taxtotal@label}</strong></th>
						<th><strong>${record.shippingcost@label}</strong></th>
						<th><strong>${record.total@label}</strong></th>
						<th align="right"><b
								style="text-align: -webkit-right; background-color: rgb(227, 227, 227);">${record.amountpaid@label}</b>
						</th>
						<th align="right"><b
								style="text-align: -webkit-right; background-color: rgb(227, 227, 227);">${record.amountremaining@label}</b>
						</th>
					</tr>
					<tr>
						<td>${record.subtotal}</td>
						<td>${record.taxtotal}</td>
						<td>${record.shippingcost}</td>
						<td>${record.total}</td>
						<td align="right"><span
								style="text-align: -webkit-right; background-color: rgb(255, 255, 255);">${record.amountpaid}</span>
						</td>
						<td align="right"><span style="text-align: -webkit-right;"><span
									style="background-color:#FFFFFF;">${record.amountremaining}</span></span></td>
					</tr>
				</table>
		</#if><br /><br />&nbsp;
		<#assign TBDCount=0 />
		<#assign attendeeList=[{"col0":""}] />
		<#attempt>
			<#if record.custpage_json_to_print?has_content && record.custpage_json_to_print
				!='Lorem ipsum dolor sit amet consectetuer ac orci sociis ornare laoreet.'>
				<#assign attendeeList=(''+record.custpage_json_to_print)?eval />
			</#if>
			<#recover>
		</#attempt>
		<#if attendeeList?has_content>
			<table style="width: 100%; margin-top: 10px;">
				<thead>
					<tr>
						<th colspan="15" style="padding: 10px 6px;">Attendees</th>
					</tr>
				</thead>
				<#assign TBDCount=0 />
				<#list attendeeList as invlines>
					<#assign attendee=invlines.col0 />
					<#if attendee=='TBD'>
						<#assign TBDCount=TBDCount + 1 />
						<#else>
							<tr>
								<td align="left" colspan="5">${attendee}</td>
							</tr>
					</#if>
				</#list>
				<#if TBDCount !=0>
					<tr>
						<td align="left" colspan="5">TBD Count ${TBDCount}</td>
					</tr>
				</#if>
			</table>
		</#if>
	</body>
</pdf>