/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search', 'N/ui/serverWidget'], function(record, search, serverWidget) {

    function beforeLoad(context) {
        
        // this script populates cust_body_acs_charge_data_for_pdf to get charges data from a saved search
        // the custom field is used for displaying data in PDF
        if((context.type == context.UserEventType.PRINT) || (context.type == context.UserEventType.VIEW)){
            try {
                var newRec = context.newRecord;
                var recObj = record.load({
                    id: newRec.id,
                    type: record.Type.INVOICE,
                    isDynamic: true
                });

                var chargeSearchObj = search.create({
                    type: "charge",
                    filters:
                    [
                        ["use","anyof","Actual"],
                        "AND", 
                        ["customer.internalid","anyof",recObj.getValue({ fieldId: 'entity' })], 
                        "AND", 
                        ["job.internalidnumber","equalto",recObj.getValue({ fieldId: 'job' })], 
                        "AND", 
                        ["postingperiod","abs",recObj.getValue({ fieldId: 'postingperiod' })]
                    ],
                    columns:
                    [
                        search.createColumn({name: "id", label: "Charge ID"}),
                        search.createColumn({
                            name: "chargedate",
                            sort: search.Sort.ASC,
                            label: "Date"
                        }),
                        search.createColumn({name: "billingitem", label: "Item"}),
                        search.createColumn({name: "description", label: "Description"}),
                        search.createColumn({name: "quantity", label: "Quantity"}),
                        search.createColumn({
                            name: "chargeemployee",
                            sort: search.Sort.ASC,
                            label: "Employee"
                        }),
                        search.createColumn({
                            name: "altname",
                            join: "chargeEmployee",
                            label: "Name"
                        })
                    ]
                });
                var pdfFieldObj = {};
                var vendors = [];
                chargeSearchObj.run().each(function(result){
                    var desc = result.getText({ name: 'description' });
                    var date = result.getValue({ name: 'chargedate' });
                    var days_worked = result.getValue({ name: 'quantity' });
                    var activity = result.getText({ name: 'billingitem' });
                    var chargeemployee = result.getValue({ name: 'chargeemployee' });
                    var employee = "";
                    
                    if(chargeemployee.substring(0,1) != 'V'){
                        var employee = result.getValue({ name: 'altname' });
                    } else {
                        if(vendors.indexOf(chargeemployee) === -1) {
                            vendors.push(chargeemployee);
                        }
                    }

                    if(!pdfFieldObj.hasOwnProperty(chargeemployee)){
                        pdfFieldObj[chargeemployee] = [];
                    }

                    pdfFieldObj[chargeemployee].push({
                        date: ((date) ? date : ""),
                        days_worked: ((days_worked) ? days_worked : ""),
                        activity: ((activity) ? activity : ""),
                        description: ((desc) ? desc : ""),
                        chargeemployee: chargeemployee,
                        employeename: ((employee) ? employee : "")
                    });
                    // pdfArrField.push(pdfFieldObj);
                    return true;
                });

                // LOOP pdfFieldObj AND INSERT VENDOR EMPLOYEE NAME FROM getVendorDetails

                var vendorDetails = getVendorDetails(vendors);
                var totals = {}
                for(var i = 0; i < vendorDetails.length; i++){
                    var vendorcode = vendorDetails[i].vendorid;
                    var vendorname = vendorDetails[i].name;
                    for(var x = 0; x < pdfFieldObj[vendorcode].length; x++){
                        pdfFieldObj[vendorcode][x].employeename = vendorname;
                        if(!totals.hasOwnProperty(vendorcode)){
                            totals[vendorcode] = parseInt(pdfFieldObj[vendorcode][x].days_worked);
                        } else {
                            totals[vendorcode] += parseInt(pdfFieldObj[vendorcode][x].days_worked);
                        }
                    }
                }

                var formObj = context.form;
                var customField = formObj.addField({
                    id : 'custpage_acs_charge_data_for_pdf',
                    type : serverWidget.FieldType.LONGTEXT,
                    label : 'charge data for pdf'
                });
                customField.defaultValue = JSON.stringify({ employees: pdfFieldObj, totals: totals });
                customField.updateDisplayType({
                   displayType : serverWidget.FieldDisplayType.HIDDEN
                });

            } catch (e) {
                log.debug({ title: 'error', details: e });
            }
        }
    }

    // get vendor details because the search cannot directly get the vendor record itself
    function getVendorDetails(vendorArr){

        // construct dynamic filter per vendor
        var filters = [];
        for(var i = 0; i < vendorArr.length; i++){
            if(i != 0){
                filters.push("OR");
            }
            filters.push(["entityid","is",vendorArr[i]]);
        }
        
        var vendorSearchObj = search.create({
            type: "vendor",
            filters: filters,
            columns:
            [
               search.createColumn({
                  name: "entityid",
                  sort: search.Sort.ASC,
                  label: "ID"
               }),
               search.createColumn({name: "altname", label: "Name"}),
            ]
         });
         var vendorDetails = [];
         vendorSearchObj.run().each(function(result){
            vendorDetails.push({ vendorid: result.getValue({name: 'entityid'}), name: result.getValue({ name: 'altname' })});
            return true;
         });


         return vendorDetails;
    }

    return {
        beforeLoad: beforeLoad
    }
});
