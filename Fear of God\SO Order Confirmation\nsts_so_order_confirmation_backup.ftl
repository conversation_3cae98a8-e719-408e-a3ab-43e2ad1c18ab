<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
    <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
    <#if .locale == "zh_CN">
        <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
    <#elseif .locale == "zh_TW">
        <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
    <#elseif .locale == "ja_JP">
        <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
    <#elseif .locale == "ko_KR">
        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
    <#elseif .locale == "th_TH">
        <link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
    </#if>
    <macrolist>
        <macro id="nlheader">
            <table class="header" style="width: 100%;"><tr>
    <td rowspan="3"><img src="https://4782866.app.netsuite.com/core/media/media.nl?id=4598&amp;c=4782866&amp;h=469566f458e329f6e340" style="float: left; margin: 7px; width: 15%; height: 15%" /></td><td></td><td></td>
    </tr>
    <tr></tr>
    <tr><td style="font-size: 16px; font-weight: bold" align="center">Order Confirmation</td><td></td></tr>
    <tr></tr>
</table>
            <table style="padding-bottom: 5px; width: 100%;">
                <tr>
                    <td style="font-size: 9pt; font-weight: bold" align="left">FROM</td>
                    <td style="font-size: 9pt; font-weight: bold" align="left">SHIP TO</td>
                    <td style="font-size: 9pt; font-weight: bold" align="left">BILL TO</td>
                    <td style="font-size: 9pt; font-weight: bold" align="left"></td>
                </tr>
                <tr>
                    <td style="font-size: 9pt; border: 1px solid black">${companyInformation.mainaddress_text}</td>
                    <td style="font-size: 9pt; border: 1px solid black">${record.shipaddress}</td>
                    <td style="font-size: 9pt; border: 1px solid black">${record.billaddress}</td>
                    <td style="font-size: 9pt">
                        <table style="width: 100%">
                            <tr>
                                <td style="font-weight: bold; font-size: 9pt; border-left: 1px solid black; border-right: 1px solid black; border-top: 1px solid black" align="center">ORDER #</td>
                            </tr>
                            <tr>
                                <td style="font-size: 9pt; border-left: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black" align="center">${record.tranid}</td>
                            </tr>
                            <tr>
                                <td style="font-size: 9pt; border-left: 1px solid black; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">Customer PO #</td>
                            </tr>
                            <tr>
                                <td style="font-size: 9pt; border-left: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black" align="center">${record.otherrefnum}</td>
                            </tr>
                            <tr>
                                <td style="font-size: 9pt; border-left: 1px solid black; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold" align="center">DIVISION</td>
                            </tr>
                            <tr>
                                <td style="font-size: 9pt; border-left: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black" align="center">${record.csegfog_division}</td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <table style="padding-bottom: 5px; width: 100%;">
                <tr>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="3">ORDER DATE</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="4">START SHIP DATE</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="2">DEPT #</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="4">SELLING PERIOD</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="3">SALES REP</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="3">ORDER TYPE</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="6">CUSTOMER #</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="2">SEASON</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="3">CONTACT</td>
                </tr>
                <tr>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="3">${record.trandate}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="4">${record.custbodyfog_start_ship_date}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="2">${record.custbodyfog_department_number}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="4">${record.custbodyfog_selling_period}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="3">${record.salesrep}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="3">${record.custbodyfog_order_type}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="6">${record.entity}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="2">${record.custbodyfog_season_header}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="3">${record.custbodyfog_contact}</td>
                </tr>
            </table>
            <table style="padding-bottom: 5px; width: 100%;">
                <tr>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="3">CANCEL DATE</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="4">CURRENCY</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="2">STORE</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="4">TERMS</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="3">WAREHOUSE</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="3">FOB POINT</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="6">SHIP VIA</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="2">FACTOR</td>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center" colspan="3">GROUP</td>
                </tr>
                <tr>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="3">${record.custbodyfog_cancel_date}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="4">${record.currency}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="2">${record.custbodfog_store}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="4">${record.terms}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="3">${record.location}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="3">${record.custbodyfog_fob_point}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="6">${record.shipmethod}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="2">${record.custbodyfog_factor}</td>
                    <td style="font-size: 9pt; border: 1px solid black" colspan="3">${record.custbodyfog_group_header}</td>
                </tr>
            </table>
            <table style="padding-bottom: 5px; width: 100%;">
                <tr>
                    <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="left">SPECIAL INSTRUCTIONS</td>
                </tr>
                <tr>
                    <td style="font-size: 9pt; border: 1px solid black" align="left">${record.custbodyfog_special_instructions}</td>
                </tr>
            </table>
        </macro>
        <macro id="nlfooter">
        </macro>
    </macrolist>
    <style type="text/css">* {
        <#if .locale == "zh_CN">
            font-family: NotoSans, NotoSansCJKsc, sans-serif;
        <#elseif .locale == "zh_TW">
            font-family: NotoSans, NotoSansCJKtc, sans-serif;
        <#elseif .locale == "ja_JP">
            font-family: NotoSans, NotoSansCJKjp, sans-serif;
        <#elseif .locale == "ko_KR">
            font-family: NotoSans, NotoSansCJKkr, sans-serif;
        <#elseif .locale == "th_TH">
            font-family: NotoSans, NotoSansThai, sans-serif;
        <#else>
            font-family: NotoSans, sans-serif;
        </#if>
        }
        table {
            font-size: 9pt;
            table-layout: fixed;
        }
        th {
            font-weight: bold;
            font-size: 8pt;
            vertical-align: middle;
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
            color: #333333;
        }
        td {
            padding: 4px 6px;
        }
        td p { align:left }
        b {
            font-weight: bold;
            color: #333333;
        }
        table.header td {
            padding: 0;
            font-size: 9pt;
        }
        table.footer td {
            padding: 0;
            font-size: 8pt;
        }
        table.itemtable th {
            padding-bottom: 10px;
            padding-top: 10px;
        }
        table.body td {
            padding-top: 2px;
        }
        table.total {
            page-break-inside: avoid;
        }
        tr.totalrow {
            background-color: #e3e3e3;
            line-height: 200%;
        }
        td.totalboxtop {
            font-size: 12pt;
            background-color: #e3e3e3;
        }
        td.addressheader {
            font-size: 8pt;
            padding-top: 6px;
            padding-bottom: 2px;
        }
        td.address {
            padding-top: 0;
        }
        td.totalboxmid {
            font-size: 28pt;
            padding-top: 20px;
            background-color: #e3e3e3;
        }
        td.totalboxbot {
            background-color: #e3e3e3;
            font-weight: bold;
        }
        span.title {
            font-size: 28pt;
        }
        span.number {
            font-size: 16pt;
        }
        span.itemname {
            font-weight: bold;
            line-height: 150%;
        }
        hr {
            width: 100%;
            color: #d3d3d3;
            background-color: #d3d3d3;
            height: 1px;
        }
</style>
</head>
<body header="nlheader" header-height="50%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter-Landscape">
<#if JSON.missingsizes != ''>
<table style="padding-bottom: 15px; width: 100%; margin-left: -15px; margin-right: -15px;">
    <tr>
        <td style="background-color: #ff0000; font-size: 10pt; font-weight: bold; height: 20px;" align="center" colspan="3">These sizes are missing from the product size rank list: ${JSON.missingsizes}</td>
    </tr>
</table>
</#if>
<#assign totalUnits = 0>
<#assign totalAmount = 0>

<#list JSON.division as division>
<#assign counter = 0>
<#assign counter1 = 0>
<table style="padding-bottom: 15px; width: 100%;">
    <#list division.sizes as sizes>
        <#assign counter = counter + 1>
    </#list>
    <#assign counter1 = '"' + counter + '"'>
    <tr>
        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 40px" align="left" rowspan="2">SELL PRD</td>
        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 60px" align="left" rowspan="2">STYLE</td>
        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 50px" align="left" rowspan="2">FABRIC</td>
        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 65px" align="left" rowspan="2">CONTENT</td>
        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 50px" align="left" rowspan="2">COLOR</td>
        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 50px" align="left" rowspan="2">GROUP</td>
        <#if counter != 0>
            <td style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center" rowspan="1" colspan=${counter1}>SIZES &amp; UNITS</td>
        <#else>
            <td style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center" rowspan="1">SIZES &amp; UNITS</td>
        </#if>
        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 45px" align="center" rowspan="2">UNITS</td>
        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 60px" align="center" rowspan="2">PRICE</td>
        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 60px" align="center" rowspan="2">MSRP</td>
        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 60px" align="center" rowspan="2">TOTAL</td>
    </tr>
    <tr>
        <#if counter != 0>
            <#list division.sizes as sizes>
                <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3" align="center" rowspan=
                    "1">${sizes}</td>
            </#list>
        <#else>
            <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3" align="center" rowspan=
                    "1"></td>
        </#if>
    </tr>

    <#assign total = 0>
    <#list division.item as item>
    <#assign totalQty = 0>
    <#if item.quantity != "0">
        <#assign totalQty = item.quantity?number>
    </#if>
    <tr>
        <td style="font-size: 7pt; border: 1px solid black; width: 40px" align="left">${item.sellPrd}</td>
        <td style="font-size: 7pt; border: 1px solid black; width: 60px" align="left">${item.name}</td>
        <td style="font-size: 7pt; border: 1px solid black; width: 50px" align="left">${item.fabric}</td>
        <td style="font-size: 7pt; border: 1px solid black; width: 65px" align="left">${item.composition}</td>
        <td style="font-size: 7pt; border: 1px solid black; width: 50px" align="left">${item.color}</td>
        <td style="font-size: 7pt; border: 1px solid black; width: 50px" align="left">${item.group}</td>
        <#if counter != 0>
            <#list item.allsizes as sizes>
                <td style="font-size: 7pt; border: 1px solid black" align="center">${sizes.quantity}</td>
                <#assign totalQty = totalQty + sizes.quantity?number>
            </#list>
        <#else>
            <td style="font-size:7pt; border: 1px solid black" align="center"></td>
        </#if>
        <#assign totalAmt = item.rate?number * totalQty>
        <#assign totalUnits = totalUnits + totalQty?number>
        <#assign totalAmount = totalAmount + totalAmt?number>
        <td style="font-size: 7pt; border: 1px solid black; width: 45px" align="center">${totalQty}</td>
        <td style="font-size: 7pt; border: 1px solid black; width: 60px" align="center">$${(item.rate?number)?string(",##0.00")}</td>
        <td style="font-size: 7pt; border: 1px solid black; width: 60px" align="center">$${item.msrp}</td>
        <td style="font-size: 7pt; border: 1px solid black; width: 60px" align="center">$${totalAmt?string(",##0.00")}</td>
    </tr>
    </#list>
</table>
</#list>
<table style="padding-bottom: 15px; width: 100%; page-break-inside: avoid">
    <tr>
        <td style="font-size: 9pt" align="left" colspan="16"></td>
        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3" align="left" colspan="3">Total Units:</td>
        <td style="font-size: 9pt; border: 1px solid black" align="right" colspan="4" rowspan="1">${totalUnits}</td>
    </tr>
    <tr>
        <td style="font-size: 9pt" align="left" colspan="16"></td>
        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3" align="left" colspan="3">Subtotal:</td>
        <td style="font-size: 9pt; border: 1px solid black" align="right" colspan="4" rowspan="1">${record.subtotal}</td>
    </tr>
    <tr>
        <td style="font-size: 9pt" align="left" colspan="16"></td>
        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3" align="left" colspan="3">Discount Total:</td>
        <td style="font-size: 9pt; border: 1px solid black" align="right" colspan="4" rowspan="1">${record.discounttotal}</td>
    </tr>
    <tr>
        <td style="font-size: 9pt" align="left" colspan="16"></td>
        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3" align="left" colspan="3">Tax Total:</td>
        <td style="font-size: 9pt; border: 1px solid black" align="right" colspan="4" rowspan="1">${record.taxtotal}</td>
    </tr>
    <tr>
        <td style="font-size: 9pt" align="left" colspan="16"></td>
        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3" align="left" colspan="3">Shipping Cost:</td>
        <td style="font-size: 9pt; border: 1px solid black" align="right" colspan="4" rowspan="1">${record.altshippingcost}</td>
    </tr>
    <tr>
        <td style="font-size: 9pt" align="left" colspan="16"></td>
        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3" align="left" colspan="3">Total Amount:</td>
        <td style="font-size: 9pt; border: 1px solid black" align="right" colspan="4" rowspan="1">${record.total}</td>
    </tr>
</table>

<table style="width: 100%; border: 1px solid black">
    <tr style="padding-bottom: 40px">
        <td style="font-size: 7pt" align="left" colspan="16">No Changes can be made to Sales Orders after 5 days of buyer signing order confirmations. No discounts and returns on this order; order must be paid in full. Any goods deemed faulty will only be accepted if returned within 2 weeks of shipment. By signing this order confirmation, you agree to be bound by the Terms and Conditions of Business of Fear of God. Signature (Head Buyer / Director)</td>
    </tr>
    <tr>
        <td style="font-size: 9pt" align="left" colspan="10"></td>
        <td style="font-size: 8pt; border-top: 1px solid black" align="left" colspan="4">Signature</td>
        <td style="font-size: 8pt; border-top: 1px solid black" align="left" colspan="2">Date</td>
    </tr>
</table>


</body>
</pdf>