var beforeSubmit = function () {
    try {
        var ShopifyOrderId = nlapiGetFieldValue('custbody_in8_shop_id');
        var shopifySettingId = nlapiGetFieldValue('custbody_in8_shop_setting');

        if (!ShopifyOrderId || !shopifySettingId) return;

        nlapiLogExecution('DEBUG', 'ShopifyOrderId: ', ShopifyOrderId);
        nlapiLogExecution('DEBUG', 'shopifySettingId: ', shopifySettingId);

        var domainName = nlapiLookupField('customrecord_in8_shop_settings',
            shopifySettingId,
            'custrecord_in8_shopify_domain_ref');

        var shopifyOrderLog = nlapiSearchRecord('customrecord_in8_shopify_requests', null,
            [new nlobjSearchFilter('custrecord_in8_shopify_req_id', null, 'is', ShopifyOrderId),
                new nlobjSearchFilter('custrecord_in8_shopify_domain', null, 'is', domainName)
            ],
            new nlobjSearchColumn('custrecord_in8_shop_req_body')) || [];

        if (shopifyOrderLog.length < 1) return;

        var shopifyOrderRequestBody = shopifyOrderLog[0].getValue('custrecord_in8_shop_req_body');

        nlapiLogExecution('DEBUG', 'shopifyOrderRequestBody', shopifyOrderRequestBody);

        if (!shopifyOrderRequestBody) return;

        var jsonBody = JSON.parse(shopifyOrderRequestBody);
        var note = jsonBody.note;

        if (!note) return;

        var sku_detail = getSKUDetails(note);
        nlapiLogExecution('DEBUG', 'sku_detail', JSON.stringify(sku_detail));

        // Line items
        for (var i = 1; i <= nlapiGetLineItemCount('item'); i++) {
            var itemSKU = nlapiGetLineItemValue('item', 'custcol_dpi_itemno', i) || '';

            itemSKU = itemSKU.toLowerCase();

            if (sku_detail[itemSKU]) {
                nlapiLogExecution('DEBUG', 'In8', 'Match on line ' + i);

                nlapiSetLineItemValue('item', 'custcol_danby_ext_note_sales_order', i, sku_detail[itemSKU].note);

                if (sku_detail[itemSKU].location) {
                    nlapiSetLineItemValue('item', 'location', i, getLocationId(sku_detail[itemSKU].location));
                }

                nlapiSetFieldValue('custbody_dpi_approvalno', sku_detail[itemSKU].ackno);
            }
        }
    } catch (ex) {
        nlapiLogExecution('DEBUG', 'Exception', ex);
    }
}

var getSKUDetails = function (note) {
    //SKU:DBC120CBLS-S:123456789012-L:1103-A:123456

    var sku_detail = {};

    nlapiLogExecution('DEBUG', 'note typeof ' + typeof note, note);
    var lines = note.toString().split('SKU:');

    for (var i = 0; i < lines.length; i++) {
        var sku = '';
        
        if (!lines[i]) {
            continue;
        }
        var k1 = lines[i].indexOf('-s');
        sku = lines[i].substr(0, k1).trim();

        if (!sku_detail[sku]) {
            sku_detail[sku] = {};
        }
        if (sku) {
            // k = lines[i].indexOf('-s:');        
            // if (k > -1) {
            //     var k1 = lines[i].indexOf('-l', k + 1);
            //     serial = lines[i].substr(k+3, k1 - k - 3).trim();
            // }
    
            k = lines[i].indexOf('-l:');        
            if (k > -1) {
                var k1 = lines[i].indexOf('-a', k + 1);
                sku_detail[sku]['location'] = lines[i].substr(k+3, k1 - k - 3).trim();
            }
    
            k = lines[i].indexOf('-a:');        
            if (k > -1) {
                sku_detail[sku]['ackno'] = lines[i].substr(k+3, lines[i].length - k - 3).trim();
            }
        }        
        sku_detail[sku].note = lines[i];
    }
    
    return sku_detail;

    /*for (var i = 0; i < lines.length; i++) {
        var line = lines[i].split('-');

        for (var j = 0; j < line.length; j++) {
            var temp;
            var sku;

            if (line[j].indexOf('SKU:') > -1) {
                temp = line[j].split(':');
                sku = temp[1];
                if (!sku_detail[sku]) {
                    sku_detail[sku] = {};
                }
            }
            if (line[j].indexOf('l:') > -1) {
                sku_detail[sku]['location'] = line[j].split(':')[1];
            }
            if (line[j].indexOf('a:') > -1) {
                sku_detail[sku]['ackno'] = line[j].split(':')[1];
            }
            sku_detail[sku].note = lines[i];
        }
    }

    return sku_detail;*/
}

function getLocationId(location) {

    var s = nlapiSearchRecord('location', null, [
        new nlobjSearchFilter('name', null, 'contains', ' ' + location + ' '),
        new nlobjSearchFilter('isinactive', null, 'is', 'F')
    ], [
        new nlobjSearchColumn('internalid')
    ]) || [];

    if (s.length) {
        return s[0].getValue('internalid');
    }
    return null;
}