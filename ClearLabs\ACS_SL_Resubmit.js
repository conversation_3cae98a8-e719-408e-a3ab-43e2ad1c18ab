/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/record'], function(record) {

    function onRequest(context) {
        var reloadRecObj = record.load({
            type: record.Type.PURCHASE_ORDER,
            id: 179326,
            isDynamic: true
        });

        reloadRecObj.setValue({ fieldId: 'memo', value: 'test' });

        reloadRecObj.save();

    }

    return {
        onRequest: onRequest
    }
});
