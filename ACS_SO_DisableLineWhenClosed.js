/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord'], function(currentRecord) {

    function fieldChanged(context) {
        disableFields(currentRecord);
    }

    function lineInit(context) {
        disableFields(currentRecord);    
    }

    function disableFields(currRecord){
        var rec = currRecord.get();
        var fieldsToDisable = ['item','olditemid','description','quantityavailable','quantity','quantitycommitted','quantitybackordered','price','pricelevels','rate','rateschedule','marginal','oqpbucket','amount','amounthasbeenset','location','islinefulfilled','origlocation','quantityfulfilled','quantitybilled','createpo','warnnodropship','kithasdropship','custcol_arrival_date','commitinventory','istaxable','taxableamt','options','giftcertnumber','oldgiftcertnumber','giftcertkey','giftcertimmutable','gcfields','giftcertfrom','giftcertrecipientname','giftcertrecipientemail','giftcertmessage','class','custcol_cntm_create_to','custcol_cntm_error_field','custcol_cntm_to_reference','cseg4','custcol_2663_isperson','cseg7','custcol_2663_lastname','cseg5','cseg6','cseg1','custcol_cntm_last_location','cseg8','custcol_2663_firstname','custcol_2663_companyname','cseg3','cseg2','noautoassignlocation','locationautoassigned','generateaccruals','costestimaterate','billvariancestatus','billvariancestatusallbook','costestimatetype','costestimatetypelist','costestimate','fulfillable','freegiftpromotion','initquantity','initoqpbucket','isopen','line','lineuniquekey','linked','linkedshiprcpt','linkedordbill','itempicked','itempacked','linkeddropship','discline','printitems','noprint','ingroup','includegroupwrapper','groupclosed','groupsetup','itemtype','itemsubtype','isnoninventory','keepnoautoassignlocationsettofalse','sequencenumber','revrecdeferred','daysbeforeexpiration','custreferralcode','id','isposting','minqty','matrixtype','isdropshiporderline','isspecialorderline','origquantity','dropshiporderhasbeenshiprecv','origunits','dropshipwarningdisplayed','linenumber','ddistrib','reorder','onorder','backordered','units','povendor','porate','poratesched','pomarginal','pooverallqtydisc','poqtygroup','poid'];
        var selectedItemIndex = rec.getCurrentSublistIndex({
            sublistId: 'item'
        });
        var isClosed = rec.getCurrentSublistValue({
            sublistId: 'item',
            fieldId: 'isclosed'
        });

        if(isClosed){

            rec.setCurrentSublistValue({
                sublistId: 'item',
                fieldId: 'description',
                value: '********** CLOSED LINE ************',
                ignoreFieldChange: true,
                forceSyncSourcing: true
            });

            rec.setCurrentSublistValue({
                sublistId: 'item',
                fieldId: 'amount',
                value: '0.00',
                ignoreFieldChange: true,
                forceSyncSourcing: true
            });  

            rec.setCurrentSublistValue({
                sublistId: 'item',
                fieldId: 'rate',
                value: '0.00',
                ignoreFieldChange: true,
                forceSyncSourcing: true
            });            

            for(var i = 0; i < fieldsToDisable.length; i++) {
                var fieldObj = rec.getSublistField({
                    sublistId: 'item',
                    fieldId: fieldsToDisable[i],
                    line: selectedItemIndex
                });
                if(fieldObj){
                    fieldObj.isDisabled = true;
                }
            }
        }
    }

    return {
        fieldChanged: fieldChanged,
        lineInit: lineInit
    }
});
