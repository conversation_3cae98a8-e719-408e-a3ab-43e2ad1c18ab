/**
 * * Copyright (c) 1998-2020 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of NetSuite, Inc. ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with NetSuite.
 *
 *
 *
 * Version    Date            		Author           Remarks
 * 1.0		  April 27, 2020        bjalandoni		 Initial Version
 * 1.1		  April 30, 2020        ccutib           Add external calc
 * 1.2		  May 29, 2020			b<PERSON><PERSON><PERSON>		 Added null checking for Customer, Vendor Rates;
 * 1.1		  July 30, 2020         ccutib           Also trigger when DR Status is empty
 *                                                   Rate script should not clear approval status
 *
 * @NApiVersion 2.0
 * @NScriptType UserEventScript
 * @NModuleScope Public
 */
define(function (require) {
    var search = require('N/search');
    var runtime = require('N/runtime');
    var error = require('N/error');
	// TODO: convert switch case values inside library to script parameters
    var libComputeAmount = require('SuiteScripts/libComputeAmount.js');
    var NSUtil = {};
    NSUtil.isEmpty = function (stValue) {
		return stValue === '' || stValue == null || stValue == undefined || stValue.constructor === Array && stValue.length == 0
			|| stValue.constructor === Object && function (v) {
				for (var k in v) { return false };
				return true;
			}(stValue);
    };
    NSUtil.search = function (stRecordType, stSearchId, arrSearchFilter, arrSearchColumn) {
        if (stRecordType == null && stSearchId == null) {
            error.create({
                name: 'SSS_MISSING_REQD_ARGUMENT',
                message: 'search: Missing a required argument. Either stRecordType or stSearchId should be provided.',
                notifyOff: false
            });
        }
        var arrReturnSearchResults = new Array();
        var objSavedSearch;
        var maxResults = 1000;
        if (stSearchId != null) {
            objSavedSearch = search.load({
                    id: stSearchId
                });
            // add search filter if one is passed
            if (arrSearchFilter != null) {
                if (arrSearchFilter[0]instanceof Array || typeof arrSearchFilter[0] == 'string') {
                    objSavedSearch.filterExpression = objSavedSearch.filterExpression.concat(arrSearchFilter);
                } else {
                    objSavedSearch.filters = objSavedSearch.filters.concat(arrSearchFilter);
                }
            }
            // add search column if one is passed
            if (arrSearchColumn != null) {
                objSavedSearch.columns = objSavedSearch.columns.concat(arrSearchColumn);
            }
        } else {
            objSavedSearch = search.create({
                    type: stRecordType
                });
            // add search filter if one is passed
            if (arrSearchFilter != null) {
                if (arrSearchFilter[0]instanceof Array || typeof arrSearchFilter[0] == 'string') {
                    objSavedSearch.filterExpression = arrSearchFilter;
                } else {
                    objSavedSearch.filters = arrSearchFilter;
                }
            }
            // add search column if one is passed
            if (arrSearchColumn != null) {
                objSavedSearch.columns = arrSearchColumn;
            }
        }
        var objResultset = objSavedSearch.run();
        var intSearchIndex = 0;
        var arrResultSlice = null;
        do {
            arrResultSlice = objResultset.getRange(intSearchIndex, intSearchIndex + maxResults);
            if (arrResultSlice == null) {
                break;
            }
            arrReturnSearchResults = arrReturnSearchResults.concat(arrResultSlice);
            intSearchIndex = arrReturnSearchResults.length;
        } while (arrResultSlice.length >= maxResults);
        return arrReturnSearchResults;
    };
    NSUtil.getScriptParameters = function () {
        var stLogTitle = 'getScriptParameters';
        var parametersMap = {};
        var scriptContext = runtime.getCurrentScript();
        var obj;
        var value;
        var optional;
        var id;
        var arrMissingParams = [];
        for (var key in this.SCRIPT_PARAMETER_NAMES) {
            if (this.SCRIPT_PARAMETER_NAMES.hasOwnProperty(key)) {
                obj = this.SCRIPT_PARAMETER_NAMES[key];
                if (typeof obj === 'string') {
                    value = scriptContext.getParameter(obj);
                } else {
                    id = obj.id;
                    optional = obj.optional;
                    value = scriptContext.getParameter(id);
                }
                if (value || value === false || value === 0) {
                    parametersMap[key] = value;
                } else if (!optional) {
                    arrMissingParams.push(key + '[' + id + ']');
                }
            }
        }
        if (arrMissingParams && arrMissingParams.length) {
            var objError = {};
            objError.name = 'Missing Script Parameter Values';
            objError.message = 'The following script parameters are empty: ' + arrMissingParams.join(', ');
            objError = error.create(objError);
            for (var key in parametersMap) {
                if (parametersMap.hasOwnProperty(key)) {
                    objError[key] = parametersMap[key];
                }
            }
            throw objError;
        }
        log.audit(stLogTitle, parametersMap);
        return parametersMap;
    };
    NSUtil.SCRIPT_PARAMETER_NAMES = {
        custscript_ue_contractorrate_search: {
            optional: false,
            id: 'custscript_ue_contractorrate_search'
        },
        custscript_ue_customerrate_search: {
            optional: false,
            id: 'custscript_ue_customerrate_search'
        },
        custscript_dr_status_pending: {
            optional: false,
            id: 'custscript_dr_status_pending'
        },
        custscript_dr_status_assigned: {
            optional: false,
            id: 'custscript_dr_status_assigned'
        },
        custscript_dr_approval_stat_approved: {
            optional: false,
            id: 'custscript_dr_approval_stat_approved'
        },
        
        // algortithm
        
        custscript_dr_alggo_baserate: {
            optional: false,
            id: 'custscript_dr_alggo_baserate'
        },
        
        custscript_dr_alggo_flatamount: {
            optional: false,
            id: 'custscript_dr_alggo_flatamount'
        },
        
        custscript_dr_alggo_percentageroute: {
            optional: false,
            id: 'custscript_dr_alggo_percentageroute'
        },
        
        custscript_dr_alggo_perfbasedrate: {
            optional: false,
            id: 'custscript_dr_alggo_perfbasedrate'
        },
        
        custscript_dr_alggo_perfbasedrate_guramt: {
            optional: false,
            id: 'custscript_dr_alggo_perfbasedrate_guramt'
        },
        
        custscript_dr_alggo_ratepermileage: {
            optional: false,
            id: 'custscript_dr_alggo_ratepermileage'
        },
        custscript_contractor_rate_not_found: {
            optional: false,
            id: 'custscript_contractor_rate_not_found'
        },
        custscript_customer_rate_not_found: {
            optional: false,
            id: 'custscript_customer_rate_not_found'
        },
        custscript_raterule_calculation_error: {
            optional: false,
            id: 'custscript_raterule_calculation_error'
        },
        custscript_amount_calculation_error: {
            optional: false,
            id: 'custscript_amount_calculation_error'
        },
        custscript_customer_calculation_error: {
            optional: false,
            id: 'custscript_customer_calculation_error'
        },
        custscript_empty_amount_qty_error: {
            optional: false,
            id: 'custscript_empty_amount_qty_error'
        },
        custscript_dr_approval_status_pending: {
            optional: false,
            id: 'custscript_dr_approval_status_pending'
        }
        
        
        
    };
    
	function validateIfRateChanged(newRecord) {
		var isRateChanged = false;
		if (newRecord) {
			var flNewPayAmount = newRecord.getValue({
				fieldId : 'custrecord_th_dailyroute_contractoramt'
			});
			var flOldPayAmount = newRecord.getValue({
				fieldId : 'custrecord_th_dailyroute_payamount'
			});
			var flNewInvoiceAmount = newRecord.getValue({
				fieldId : 'custrecord_th_dailyroute_custamount'
			});
			var flOldInvoiceAmount = newRecord.getValue({
				fieldId : 'custrecord_th_dailyroute_invoiceamount'
			});
			log.debug('validateIfRateChanged', 'Pay Amount=' + flOldPayAmount + '~' + flNewPayAmount + '| Inv Amount=' + flOldInvoiceAmount + '~' + flNewInvoiceAmount);
			if ((flNewPayAmount && !flOldPayAmount) ||
					(!flNewPayAmount && flOldPayAmount) ||
					flNewPayAmount && flOldPayAmount && flNewPayAmount != flOldPayAmount) {
				isRateChanged = true;
			}
		}
		return isRateChanged;
	}
	function assignInvoiceAmount(recDailyRoute, _SCRIPT_PARAM) {
		var flContractorAmount = recDailyRoute.getValue({
			fieldId : 'custrecord_th_dailyroute_contractoramt'
		});
		var flCustomerAmount = recDailyRoute.getValue({
			fieldId : 'custrecord_th_dailyroute_custamount'
		});
		log.debug('assignInvoiceAmount', 'Amounts: Contractor= ' + flContractorAmount + '| Customer=' + flCustomerAmount);
		recDailyRoute.setValue({
			fieldId : 'custrecord_th_dailyroute_payamount',
			value : flContractorAmount
		});
		recDailyRoute.setValue({
			fieldId : 'custrecord_th_dailyroute_invoiceamount',
			value : flCustomerAmount
		});
		recDailyRoute.setValue({
			fieldId : 'custrecord_th_dailyroute_status',
			value : _SCRIPT_PARAM.custscript_dr_status_assigned
		});
	}

    function getRateKeys(recDailyRoute) {
        var stLogTitle = 'beforeSubmit :: getRateKeys';
        log.debug(stLogTitle, '======= beforeSubmit :: getRateKeys ======');
        var stContractDriver = recDailyRoute.getValue('custrecord_th_dailyroute_contractdriver');
        var stCustomer = recDailyRoute.getValue('custrecord_th_dailyroute_customer');
        var stItem = recDailyRoute.getValue('custrecord_th_dailyroute_item');
        var stRoute = recDailyRoute.getValue('custrecord_th_dailyroute_routeid');
        var stContractor = recDailyRoute.getValue('custrecord_th_dailyroute_mastercontract');
        log.debug(stLogTitle,
            ' stContractDriver 	: ' + stContractDriver +
            ' stCustomer 		: ' + stCustomer +
            ' stItem 			: ' + stItem +
            ' stRoute 			: ' + stRoute +
            ' stContractor 		: ' + stContractor);
        /* if contractor is empty, look for the master contractor */
        if (NSUtil.isEmpty(stContractor)) {
            var objMasterContractor = search.lookupFields({
                    type: 'vendor',
                    id: stContractDriver,
                    columns: ['custentity_tophat_master_vendor']
                });
            if (!NSUtil.isEmpty(objMasterContractor) && !NSUtil.isEmpty(objMasterContractor.custentity_tophat_master_vendor)) {
                stContractor = objMasterContractor.custentity_tophat_master_vendor;
            } else {
                stContractor = stContractDriver;
            }
            log.debug(stLogTitle, 'Master Contractor :: stContractor = ' + stContractor);
        }
        /* create rate keys object */
        var objRateKeys = {
            'stContractDriver': stContractDriver,
            'stCustomer': stCustomer,
            'stItem': stItem,
            'stRoute': stRoute,
            'stContractor': stContractor,
        };
        return objRateKeys;
    }
    function getContractorRate(objRateKeys, _SCRIPT_PARAM) {
        var stLogTitle = 'beforeSubmit :: getContractorRate';
        log.debug(stLogTitle, '======= beforeSubmit :: getContractorRate ======');
        var stSearchID = _SCRIPT_PARAM.custscript_ue_contractorrate_search;
        var arrFilters = [];
        var arrFilter1 = search.createFilter({
			name: 'custrecord_th_rate_contractor',
			operator: search.Operator.ANYOF,
			values: objRateKeys['stContractor'],
		});
        arrFilters.push(arrFilter1);
        var arrFilter2 = search.createFilter({
			name: 'custrecord_th_rate_customer',
			operator: search.Operator.ANYOF,
			values: objRateKeys['stCustomer'],
		});
        arrFilters.push(arrFilter2);
        var arrFilter3 = search.createFilter({
			name: 'custrecord_th_rate_item',
			operator: search.Operator.ANYOF,
			values: objRateKeys['stItem'],
		});
        arrFilters.push(arrFilter3);
        if (!NSUtil.isEmpty(objRateKeys['stRoute'])) {
            var arrFilter4 = search.createFilter({
                    name: 'custrecord_th_rate_route',
                    operator: search.Operator.ANYOF,
                    values: objRateKeys['stRoute'],
                });
            arrFilters.push(arrFilter4);
        }
        /*v 1.2 bjalandoni */
        var arrContractorRate = [];
        arrContractorRate = NSUtil.search(null, stSearchID, arrFilters);
        log.debug(stLogTitle, 'Contrator Rate Seach= ' + JSON.stringify(arrContractorRate));
        return arrContractorRate;
    }
    function getCustomerRate(objRateKeys, _SCRIPT_PARAM) {
        var stLogTitle = 'beforeSubmit :: getCustomerRate';
        log.debug(stLogTitle, '======= beforeSubmit :: getCustomerRate ======');
        var stSearchID = _SCRIPT_PARAM.custscript_ue_customerrate_search;
        var arrFilters = [];
        var arrFilter2 = search.createFilter({
                name: 'custrecord_th_custrate_customer',
                operator: search.Operator.ANYOF,
                values: objRateKeys['stCustomer'],
            });
        arrFilters.push(arrFilter2);
        var arrFilter3 = search.createFilter({
                name: 'custrecord_th_custrate_item',
                operator: search.Operator.ANYOF,
                values: objRateKeys['stItem'],
            });
        arrFilters.push(arrFilter3);
        if (!NSUtil.isEmpty(objRateKeys['stRoute'])) {
            var arrFilter4 = search.createFilter({
                    name: 'custrecord_th_custrate_route',
                    operator: search.Operator.ANYOF,
                    values: objRateKeys['stRoute'],
                });
            arrFilters.push(arrFilter4);
        }
        /*v 1.2 bjalandoni */
        var arrCustomerRate = [];
        arrCustomerRate = NSUtil.search(null, stSearchID, arrFilters);
        log.debug(stLogTitle, 'Customer Rate Seach= ' + JSON.stringify(arrCustomerRate));
        return arrCustomerRate;
    }
    
    function resetFields(recDailyRoute, _SCRIPT_PARAM){
    	/* clear fields in Vendor */
		recDailyRoute.setValue('custrecord_th_dailyroute_payrate', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_addstops', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_addstopsrate', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_addmileage', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_addmileagerate', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_contractoramt', '');
		
		/* clear fields in Vendor */
		recDailyRoute.setValue('custrecord_th_dailyroute_custinvrate', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_custaddmileage', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_custaddmileager', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_custaddstops', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_custaddstopsrat', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_custamount', '');
		
		/* clear fields in Vendor */
		recDailyRoute.setValue('custrecord_th_dailyroute_message', '');
    }
    
    function assignRate(scriptContext) {
        try{
        	var stLogTitle = 'beforeSubmit :: assignRate';
            log.debug(stLogTitle, '======= beforeSubmit :: assignRate ======');
            var _SCRIPT_PARAM = NSUtil.getScriptParameters();
            var recDailyRoute = scriptContext.newRecord;
    		recDailyRoute.setValue('custrecord_th_dailyroute_message', '');
    		var isRateExternallyChanged = recDailyRoute.getValue({
    			fieldId : 'custrecord_th_dailyroute_external'
    		}) === true;
        	var isRateInternallyChanged = false;
			var stRateStatus = recDailyRoute.getValue({
				fieldId : 'custrecord_th_dailyroute_status'
			});
			var stApprovalStatus = recDailyRoute.getValue({
				fieldId : 'custrecord_th_dailyroute_approvalstatus'
			});
			var isPendingRate = _SCRIPT_PARAM.custscript_dr_status_pending;
			var isRateAssigned = _SCRIPT_PARAM.custscript_dr_status_assigned;
			var isApproved = stApprovalStatus == _SCRIPT_PARAM.custscript_dr_approval_stat_approved;
			log.audit(stLogTitle, 'DR Status=' + stRateStatus + '| Approval Status=' + stApprovalStatus);
			var stCustomer = recDailyRoute.getValue('custrecord_th_dailyroute_customer');

			// Items requiring approval are treated as if they were entered externally, and rate look-up is by-passed.
			var isItemRequireApproval = recDailyRoute.getValue('custrecord_th_dailyroute_req_approval');
			if (isItemRequireApproval) {
				isRateExternallyChanged = true;
			}
			
			// Get Contractor/Customer Amounts entered directly by user.
			var flContractorAmount = recDailyRoute.getValue('custrecord_th_dailyroute_contractoramt');
			var flCustomerAmount = recDailyRoute.getValue('custrecord_th_dailyroute_custamount');		

			// Calculate Rates
        	if (!stRateStatus || stRateStatus == isPendingRate || stRateStatus == isRateAssigned) {
            	if (!isRateExternallyChanged) {
    				var objRateKeys 		= getRateKeys(recDailyRoute);
    				
    				/* bjalandoni - july 27, 2020 reset fields*/
    				resetFields(recDailyRoute, _SCRIPT_PARAM);
    				
    				var objContractorRate 	= getContractorRate(objRateKeys, _SCRIPT_PARAM);
    				/* V 1.2 bjalandoni Check whether contractor or customer rate is empty */
    				if (NSUtil.isEmpty(objContractorRate)){
    					recDailyRoute.setValue('custrecord_th_dailyroute_message', _SCRIPT_PARAM.custscript_contractor_rate_not_found);
        				recDailyRoute.setValue('custrecord_th_dailyroute_status', _SCRIPT_PARAM.custscript_dr_status_pending);
        				return;
    				}
    				
    				var objCustomerRate 	= getCustomerRate(objRateKeys, _SCRIPT_PARAM);
    				/* V 1.2 bjalandoni Check whether contractor or customer rate is empty */
    				if (NSUtil.isEmpty(objCustomerRate)){
    					recDailyRoute.setValue('custrecord_th_dailyroute_message', _SCRIPT_PARAM.custscript_customer_rate_not_found);
        				recDailyRoute.setValue('custrecord_th_dailyroute_status', _SCRIPT_PARAM.custscript_dr_status_pending);
        				return;
    				}
    				
    				log.debug(stLogTitle, 'Start to calculate internal rate...');

    				var flContractorAmount 	= libComputeAmount.computeContractorRate(recDailyRoute, objContractorRate, _SCRIPT_PARAM);
    				log.debug(stLogTitle, 'Contractor Amount = ' + flContractorAmount);
    				var flCustomerAmount 	= libComputeAmount.computeCustomerRate(recDailyRoute, objCustomerRate, _SCRIPT_PARAM);
    				log.debug(stLogTitle, 'Customer Amount = ' + flCustomerAmount);
    				
    				/* check the returned CONTRACTOR AMOUNT */
    				if (flContractorAmount != null && flContractorAmount >= 0) {
    					recDailyRoute.setValue({
    						fieldId : 'custrecord_th_dailyroute_contractoramt',
    						value : flContractorAmount
    					});
    					isRateInternallyChanged = true;
    				}
    				else if (flContractorAmount = null || flContractorAmount < 0){
    					var stCurrentMsg = recDailyRoute.getValue('custrecord_th_dailyroute_message');
    					recDailyRoute.setValue('custrecord_th_dailyroute_message', stCurrentMsg + ' ' + _SCRIPT_PARAM.custscript_amount_calculation_error);
        				recDailyRoute.setValue('custrecord_th_dailyroute_status', _SCRIPT_PARAM.custscript_dr_status_pending);
        				return;
    				}
    				
    				/* check the returned CUSTOMER AMOUNT */
    				if (flCustomerAmount != null && flCustomerAmount >= 0) {
    					recDailyRoute.setValue({
    						fieldId : 'custrecord_th_dailyroute_custamount',
    						value : flCustomerAmount
    					});
    					isRateInternallyChanged = true;
    				}
    				else if (flCustomerAmount = null || flCustomerAmount < 0){
    					var stCurrentMsg = recDailyRoute.getValue('custrecord_th_dailyroute_message');
    					recDailyRoute.setValue('custrecord_th_dailyroute_message', stCurrentMsg + ' ' + _SCRIPT_PARAM.custscript_customer_calculation_error);
        				recDailyRoute.setValue('custrecord_th_dailyroute_status', _SCRIPT_PARAM.custscript_dr_status_pending);
        				return;
    				}  				
    				
    				/* check if QUANTITY, CUSTOMER AMOUNT, CONTRACTOR AMOUNT and TOTAL ROUTE VALUE are EMPTY*/
    				var intQuantity = parseFloat(recDailyRoute.getValue('custrecord_th_dailyroute_qty'));
    				var intTotalRoute = parseFloat(recDailyRoute.getValue('custrecord_th_dailyroute_totalroutevalue'));
    				log.debug(stLogTitle, 'intQuantity= '  + intQuantity + ' intTotalRoute= ' + intTotalRoute);
    				
    				if (flContractorAmount == 0 && flCustomerAmount == 0 && intQuantity == 0 && intTotalRoute == 0){
    					recDailyRoute.setValue('custrecord_th_dailyroute_message', stCurrentMsg + ' ' + _SCRIPT_PARAM.custscript_empty_amount_qty_error);
        				recDailyRoute.setValue('custrecord_th_dailyroute_status', _SCRIPT_PARAM.custscript_dr_status_pending);
        				return;
    				}  				
    			}
		
				log.debug(stLogTitle, 'Rate changed external/internal? ' + isRateExternallyChanged + '/' + isRateInternallyChanged);
				if (isRateExternallyChanged || isRateInternallyChanged) {
					assignInvoiceAmount(recDailyRoute, _SCRIPT_PARAM);
				}
			}
        }
        catch (err){
        	log.error(stLogTitle, err.message);
        }
    }
    function beforeSubmit(scriptContext) {
        var stLogTitle = 'beforeSubmit';
        log.debug(stLogTitle, '======= beforeSubmit:begin ======');
        try {
            if (scriptContext.type == scriptContext.UserEventType.CREATE
				|| scriptContext.type == scriptContext.UserEventType.EDIT) {
                assignRate(scriptContext);
            }
        }
        catch (err){
        	log.error(stLogTitle, err.message);
        }	
        	finally {
			log.debug(stLogTitle, '======= beforeSubmit:end ======');
		}
    }
    return {
        beforeSubmit: beforeSubmit,
    };
});