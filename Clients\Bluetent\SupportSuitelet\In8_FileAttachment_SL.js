function suitelet(request, response)
{

    try
    {
        var form = nlapiCreateForm('New attachment');

        if(request.getMethod() == "GET")
        {
            var caseId = request.getParameter('caseid');

            if(!caseId)
                throw nlapiCreateError('NOT_FOUND', "Invalid parameters");


            // case
            var caseField = form.addField("custpage_caseid", 'text', '', null);
            caseField.setDefaultValue(caseId);
            caseField.setDisplayType('hidden');

            //file
            var fileField = form.addField('custpage_file', 'file', 'File', null);
            fileField.setMandatory(true);

            form.addSubmitButton('Send');
        }
        else
        {

            var file = request.getFile("custpage_file")
            file.setFolder(nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_attachmentfolder'));

            var id = nlapiSubmitFile(file)

            nlapiAttachRecord("file", id, "supportcase", request.getParameter('custpage_caseid'))

            var field = form.addField('custpage_message', 'inlinehtml', 'Message');
            field.setDefaultValue("<h2>File sent!</h2>");

        }

        response.writePage(form);
    }
    catch(e)
    {
        nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e) + ' ' + e.stack);

        var form = nlapiCreateForm("Error");

        var code = "UNEXPECTED_ERROR";
        var message = "";

        if(e instanceof nlobjError)
        {
            code = e.getCode();
            message = e.getDetails();
        }
        else
        {
            message = e.message;
        }

        var field = form.addField('custpage_message', 'inlinehtml', 'Message');
        field.setDefaultValue("<h2>Code: " + code +" <br/>Message: " + message + "</h2>");

        response.writePage(form);
    }
}