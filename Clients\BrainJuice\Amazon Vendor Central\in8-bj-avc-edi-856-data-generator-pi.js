/**
 * @NApiVersion 2.x
 * @NModuleScope Public
 * @NScriptType plugintypeimpl
*/

define([
    'N/record',
    'N/search'
], function(record, search) {
    function _validate(options) {
      const recordId = options.recordId;
      const recordType = options.recordType;

      if (!recordId) {
        throw 'NO_DATA_SOURCE_FOUND';
      }
      if (!recordType) {
        throw 'NO_RECORD_TYPE_FOUND';
      }
    }
    function _getPackages(itemFulfillmentId) {
      var packMap = {};
      search.create({
        type: 'customrecordhj_tc_package_contents',
        filters: [
          search.createFilter({
            name: 'custrecord_hj_packagecontents_sublist',
            operator: search.Operator.ANYOF,
            values: itemFulfillmentId
          })
        ],
        columns: [
          search.createColumn({ name: 'custrecord_in8_np_pack_sscc_code' }),
          search.createColumn({ name: 'altname', join: 'custrecord_in8_avc_np_pack_sscc' }),
          search.createColumn({ name: 'custrecordhj_pkg_trackingnumber' })
        ]
      })
      .run()
      .each(function(result) {
        var packId = result.id;
        if (!packMap[packId]) {
          var ssccCode = result.getValue({ name: 'custrecord_in8_np_pack_sscc_code' });
          packMap[packId] = {
            sscc: ssccCode || result.getValue({ name: 'altname', join: 'custrecord_in8_avc_np_pack_sscc' }),
            trackingNumber: result.getValue({ name: 'custrecordhj_pkg_trackingnumber' }),
            items: []
          };
        }
        return true;
      });
      search.create({
        type: 'customrecordhj_tc_pkgcontentslineitem',
        filters: [
          search.createFilter({
            name: 'custrecord_hj_packagecontents_sublist',
            join: 'custrecordhj_tc_pkgcont_lineitemparent',
            operator: search.Operator.ANYOF,
            values: itemFulfillmentId
          })
        ],
        columns: [
          search.createColumn({ name: 'upccode', join: 'custrecordhj_tc_pkgcontents_lineitemitem' }),
          search.createColumn({ name: 'custrecordhj_tc_pkgcont_lineitemparent' }),
          search.createColumn({ name: 'custrecordhj_tc_pkgcontents_lineitemwt' }),
          search.createColumn({ name: 'custrecordhj_tc_pkgcontents_lineitemqty' })
        ]
      })
      .run()
      .each(function(result) {
        var packId = result.getValue({ name: 'custrecordhj_tc_pkgcont_lineitemparent' });
        if (!packMap[packId]) {
          packMap[packId] = {
            items: []
          };
        }
        packMap[packId].items.push({
          upc: result.getValue({ name: 'upccode', join: 'custrecordhj_tc_pkgcontents_lineitemitem' }),
          weight: result.getValue({ name: 'custrecordhj_tc_pkgcontents_lineitemwt' }),
          quantity: result.getValue({ name: 'custrecordhj_tc_pkgcontents_lineitemqty' })
        });
        return true;
      });
      var packList = Object.keys(packMap).reduce(function(res, packId) {
        res.push(packMap[packId]);
        return res;
      }, []);
      log.debug({ title: 'Pack List', details: packList });
      return { packList: packList };
    }
    function execute(options) {
      _validate(options);

      const recordId = options.recordId;
      const recordType = options.recordType;
      const jobId = options.jobId;
      const settingsId = options.settingsId;
      var dataSource = { recordsList: [], searchResultsList: [], customList: [] };
      log.debug({ title: 'Data Source Options', details: options });

      if (recordId) {
        const rec = record.load({ type: recordType, id: recordId });
        dataSource.recordsList.push({
          templateName: 'record',
          record: rec
        })
      }
      if (jobId) {
        const jobRec = record.load({ type: 'customrecord_in8_edi_job', id: jobId });
        dataSource.recordsList.push({
          templateName: 'jobRec',
          record: jobRec
        });
      }
      if (settingsId) {
        const settingsRec = record.load({ type: 'customrecord_in8_edi_settings', id: settingsId });
        dataSource.recordsList.push({
          templateName: 'setRec',
          record: settingsRec
        });
      }

      var packList = _getPackages(recordId);
      if (packList && packList.length !== 0) {
        dataSource.customList.push({
          templateName: 'packageRec',
          data: packList
        });
      }

      return dataSource;
    }
    return {
      execute: execute
    }
  });