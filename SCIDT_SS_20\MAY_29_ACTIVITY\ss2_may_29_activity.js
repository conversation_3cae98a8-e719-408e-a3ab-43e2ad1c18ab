/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord'], function(currentRecord) {

    function pageInit(context) {
        if(context.mode == 'edit'){
            var record = context.currentRecord;

            alert('Record Type: ' + record.type);
            alert('Record ID:' + record.id);
            
            for(var i = 0; i < 5; i++){
                record.selectNewLine({ sublistId: 'item' });
        
                record.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'item',
                    value: 9,
                    forceSyncSourcing: true,
                });
        
                record.commitLine({ sublistId: 'item' });
            }
        
            alert('New Item Total ' + record.getLineCount({ sublistId: 'item' }));
        }
    }

    function saveRecord(context) {
        
    }

    function validateField(context) {
        
    }

    function fieldChanged(context) {
        
    }

    function postSourcing(context) {
        
    }

    function lineInit(context) {
        
    }

    function validateDelete(context) {
        
    }

    function validateInsert(context) {
        
    }

    function validateLine(context) {
        
    }

    function sublistChanged(context) {
        
    }

    return {
        pageInit: pageInit,
        saveRecord: saveRecord,
        validateField: validateField,
        fieldChanged: fieldChanged,
        postSourcing: postSourcing,
        lineInit: lineInit,
        validateDelete: validateDelete,
        validateInsert: validateInsert,
        validateLine: validateLine,
        sublistChanged: sublistChanged  
    }
});
