/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord', 'N/url'], function(currentRecord, url) {

    function pageInit(context){
    }

    function redirectToPDF(){
        rec = currentRecord.get();
        var outputUrl = url.resolveScript({
            scriptId: 'customscripttinknockers_pdf_suitelet',
            deploymentId: 'customdeploy_tinknockers_pdf_suitelet',
            params: {
                custpage_WOId: rec.id
            }
        });
        window.open(outputUrl);
    }    

    return {
        pageInit : pageInit,
        redirectToPDF : redirectToPDF
    }
});
