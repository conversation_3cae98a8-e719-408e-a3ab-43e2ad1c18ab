/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/render', 'N/record', 'N/format', 'N/search', 'N/file'], function(https, render, record, format, search, file) {

    function onRequest(context) {
        if(context.request.method == https.Method.GET){
            requestParams = context.request.parameters;
            var printType = requestParams.custpage_print_type;
            var itemId = parseInt(requestParams.custpage_item_id);
            
            log.debug('itemId', itemId);
            if(printType == "ID") {
                // retrieve the xml template file from file cabinet
                var xmlTemplateFile = file.load('SuiteScripts/Noel_ID_label.xml');
            } else if (printType == "Price") {
                var xmlTemplateFile = file.load('SuiteScripts/Noel_Price_Label.xml');
            }


            var renderer = render.create();

            // insert the template file content to the renderer
            renderer.templateContent = xmlTemplateFile.getContents();

            // ADD THE RECORD TO BE USED BASED ON PARAMETERS GIVEN
            var recObj = record.load({
                type: record.Type.INVENTORY_ITEM,
                id: itemId,
                isDynamic: true
            });

            var itemId = recObj.getValue({
                fieldId: 'itemid'
            });
            
            var vendorCode = recObj.getValue({
                fieldId: 'displayname',
            });

            var salesDescription = recObj.getValue({
                fieldId: 'salesdescription',
            });

            var line = recObj.findSublistLineWithValue({
                sublistId: 'price',
                fieldId: 'pricelevelname',
                value: 'Retail Price'
            });

            var srPrice = recObj.getSublistValue({
                sublistId: "price",
                fieldId: "price_1_",
                line: line
            });

            var line = recObj.findSublistLineWithValue({
                sublistId: 'price',
                fieldId: 'pricelevelname',
                value: 'Sale Price 2 - Noel'
            });

            var noelPrice = recObj.getSublistValue({
                sublistId: "price",
                fieldId: "price_1_",
                line: line
            });
            

            var item = {
                displayname: vendorCode,
                itemid: itemId,
                salesdescription: salesDescription.replace('&', '&#38;'),
                noelprice: noelPrice,
                srp: srPrice
            }

            var items = [item];
            
            renderer.addCustomDataSource({
                format: render.DataSource.OBJECT,
                alias: "JSON",
                data: { items: items }
            });


            // RENDER AS STRING
            var PriceTag_IdLabel = renderer.renderAsString();

            log.debug(PriceTag_IdLabel);

            // SEND RESPONSE XML TO RENDER AS PDF
            context.response.renderPdf({
                xmlString: PriceTag_IdLabel
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
