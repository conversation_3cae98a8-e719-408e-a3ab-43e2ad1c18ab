<#-- format specific processing -->

<#function getChequeNumber payment>
	<#assign value = "">
    <#if payment.recordtype == "cashrefund">
        <#assign value = payment.otherrefnum>
    <#else>
    	<#assign value = payment.tranid>
    </#if>
    <#return value>
</#function>

<#-- template building -->

#OUTPUT START#
Account Number, Check Number, Check Amount, Issue Date, Payee Data, Issue Flag
<#list payments as payment>
    <#assign entity = entities[payment_index]>
${setPadding(cbank.custpage_pp_custrecord_2663_acct_num,"left","0",13)},${setPadding(getChequeNumber(payment),"left","0",10)},${setPadding(formatAmount(getAmount(payment),"dec"), "left", "0", 11)},${setPadding(payment.trandate?string("MMddyyyy"),"left", "0", 8)},${setLength(buildEntityName(entity,true),40)},I
</#list>
#OUTPUT END#