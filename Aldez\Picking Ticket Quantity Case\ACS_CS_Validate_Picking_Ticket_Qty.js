/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/ui/dialog', 'N/search', 'N/record', 'N/currentRecord'], function(dialog, search, record, currentRecord) {

    function pageInit(context) {
        
    }

    function fieldChanged(context) {
        if(context.sublistId == 'item' && context.fieldId == 'custcol_pick_ticket_qty'){
            var recObj = currentRecord.get();
            
            var lineCount = recObj.getLineCount({
                sublistId: context.sublistId
            });

            if(lineCount != 0) {

                var remainingFulfillment = recObj.getCurrentSublistValue({
                    sublistId: context.sublistId,
                    fieldId: 'custcol2'
                });
                var pickTicketQuantity = recObj.getCurrentSublistValue({
                    sublistId: context.sublistId,
                    fieldId: context.fieldId
                });

                // send a warning dialog when the pick ticket quantity is greater than remaining fulfillment then pick ticket quantity back to zero.
                if(parseInt(remainingFulfillment) < parseInt(pickTicketQuantity)){
                    dialog.alert({
                        title: 'Warning!',
                        message: 'Pick Ticket Quantity cannot exceed the Remaining fulfillments Quantity!'
                    });
                    
                    recObj.setCurrentSublistValue({
                        sublistId: context.sublistId,
                        fieldId: context.fieldId,
                        value: 0,
                        ignoreFieldChange: true
                    });

                // send a warning when the user inputs negative amounts
                } else if (parseInt(pickTicketQuantity) < 0) {
                    dialog.alert({
                        title: 'Warning!',
                        message: 'Pick Ticket Quantity cannot be negative!'
                    });
                    
                    recObj.setCurrentSublistValue({
                        sublistId: context.sublistId,
                        fieldId: context.fieldId,
                        value: 0,
                        ignoreFieldChange: true
                    });
                
                } else {

                    var itemId = recObj.getCurrentSublistValue({
                        sublistId: context.sublistId,
                        fieldId: 'item'
                    });
                    // check if item type is item group
                    var itemLookUp = search.lookupFields({
                        type: search.Type.ITEM,
                        id: itemId,
                        columns: ['type']
                    });
                    
                    // if item type is group, get the components and quantity for ratio
                    if(itemLookUp.type[0].value == 'Group') {
                        
                        var itemRecObj = record.load({
                            type: record.Type.ITEM_GROUP,
                            id: itemId,
                            isDynamic: true
                        });

                        var currIndex = recObj.getCurrentSublistIndex({
                            sublistId: context.sublistId
                        });

                        var componentLineCount = itemRecObj.getLineCount({
                            sublistId: 'member'
                        });

                        recObj.commitLine({ sublistId: context.sublistId });
                        
                        var hasGreater = false;

                        for(var i = 0; i < componentLineCount; i++) {
                            var componentRatio = itemRecObj.getSublistValue({
                                sublistId: 'member',
                                fieldId: 'quantity',
                                line: i
                            });

                            var componentId = itemRecObj.getSublistValue({
                                sublistId: 'member',
                                fieldId: 'item',
                                line: i
                            });

                            var componentLineNo = recObj.findSublistLineWithValue({
                                sublistId: context.sublistId,
                                fieldId: 'item',
                                value: componentId
                            });

                            // var ratioPickTicketQty = '';

                            // if(pickTicketQuantity != '') {
                                var ratioPickTicketQty = pickTicketQuantity * componentRatio;
                            // }

                            if(lineCount != 0) {
                                
                                recObj.selectLine({
                                    sublistId: context.sublistId,
                                    line: componentLineNo
                                });

                                var currentRemainingFulfillment = recObj.getCurrentSublistValue({
                                    sublistId: context.sublistId,
                                    fieldId: 'custcol2'
                                });

                                console.log(currentRemainingFulfillment);

                                if(parseInt(currentRemainingFulfillment) > parseInt(ratioPickTicketQty)) {

                                    recObj.setCurrentSublistValue({
                                        sublistId: context.sublistId,
                                        fieldId: context.fieldId,
                                        value: ratioPickTicketQty,
                                        ignoreFieldChange: true
                                    });
                                    
                                    if(i != (componentLineCount - 1))
                                        recObj.commitLine({ sublistId: context.sublistId });
                                } else {
                                    hasGreater = true;
                                }
                            }
                        }
                    }

                    if(hasGreater) {
                        dialog.alert({
                            title: 'Warning!',
                            message: 'Pick Ticket Quantity cannot exceed the Remaining fulfillments Quantity!'
                        });
                        
                        recObj.selectLine({
                            sublistId: context.sublistId,
                            line: currIndex
                        });
                        
                        recObj.setCurrentSublistValue({
                            sublistId: context.sublistId,
                            fieldId: context.fieldId,
                            value: '',
                            ignoreFieldChange: true
                        });

                        recObj.commitLine({ sublistId: context.sublistId });

                        for(var i = 0; i < componentLineCount; i++) {

                            recObj.selectLine({
                                sublistId: context.sublistId,
                                line: currIndex + i + 1
                            });
                                
                            recObj.setCurrentSublistValue({
                                sublistId: context.sublistId,
                                fieldId: context.fieldId,
                                value: '',
                                ignoreFieldChange: true
                            });

                            recObj.commitLine({ sublistId: context.sublistId });

                        }

                        recObj.selectLine({
                            sublistId: context.sublistId,
                            line: currIndex
                        });

                    }
                }
            }
        }
    }

    return {
        pageInit: pageInit,
        fieldChanged: fieldChanged
    }
});
