/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/query'], function(query) {

    function pageInit(context) {
    }

    function fieldChanged(context) {
        
        var recObj = context.currentRecord;

        // if(context.sublistId == 'apply' && context.fieldId == 'apply'){
        //     var totalDeductions = recObj.getValue({ fieldId: 'custpage_total_deductions' });
        //     var applied = recObj.getSublistValue({ sublistId: context.sublistId, fieldId: context.fieldId, line: context.line });
            
        //     // TODO: CHANGE FIELD ID
        //     var transactionId = recObj.getSublistValue({ sublistId: context.sublistId, fieldId: 'internalid', line: context.line });
        //     var discount = getDiscount(transactionId);
            
        //     if(applied){
        //         totalDeductions = Number(totalDeductions) + Number(discount);
        //     } else {
        //         totalDeductions = Number(totalDeductions) - Number(discount);
        //     }

        //     recObj.setValue({ fieldId: 'custpage_total_deductions', value: totalDeductions });

        // }

        if(context.sublistId == 'custpage_deductions' && context.fieldId == 'deduction_amount') {
            // var totalDeductions = recObj.getValue({ fieldId: 'custpage_total_deductions' });
            // var enteredDeductions = recObj.getValue({ fieldId: 'custpage_entered_deductions' });
            // enteredDeductions = Number(enteredDeductions) + Number(discount);

            // if(totalDeductions >= enteredDeductions) {
            //     recObj.setValue({ fieldId: 'custpage_entered_deductions', value: enteredDeductions });
            // } else {
            //     alert("You have entered more deductions than the total deduction you can enter!");
            //     var discount = recObj.setCurrentSublistValue({ sublistId: context.sublistId, fieldId: context.fieldId, value: 0.00 });
            //     return false;
            // }
            var deductionLineCount = recObj.getLineCount({ sublistId: context.sublistId });
            if(deductionLineCount) {
                var enteredDeductions = 0.00;
                for(var i = 0; i < deductionLineCount; i++) {
                    enteredDeductions += Number(recObj.getSublistValue({ sublistId: context.sublistId, fieldId: context.fieldId, line: i }));
                    log.debug('enteredDeductions', enteredDeductions);

                }
                recObj.setValue({ fieldId: 'custpage_entered_deductions', value: enteredDeductions });

            }
        }

    }

    function sublistChanged(context) {
        var recObj = context.currentRecord;
        if(context.sublistId == 'custpage_deductions') {
            var deductionLineCount = recObj.getLineCount({ sublistId: context.sublistId });
            if(deductionLineCount) {
                var enteredDeductions = 0.00;
                for(var i = 0; i < deductionLineCount; i++) {
                    enteredDeductions += Number(recObj.getSublistValue({ sublistId: context.sublistId, fieldId: 'deduction_amount', line: i }));
                    log.debug('enteredDeductions', enteredDeductions);

                }
                recObj.setValue({ fieldId: 'custpage_entered_deductions', value: enteredDeductions });
            }
        }
    }

    function getAccountPreviousAmount(accountId, recObj) {

        var deductionData = JSON.parse(recObj.getValue({ fieldId: 'custbody_deduction_data' }));
        deductionData.forEach(function(deductionLine){
            if(deductionLine.account == accountId){
                return
            }
        });

    }

    return {
        pageInit: pageInit,
        fieldChanged: fieldChanged,
        sublistChanged: sublistChanged
    }
});
