/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
 define(['N/currentRecord', 'N/url'], function(currentRecord, url) {

    function pageInit(context){
    }

    function renderPdf(isSimple){
        rec = currentRecord.get();
        var outputUrl = url.resolveScript({
            scriptId: 'customscript_acs_sl_print_bom',
            deploymentId: 'customdeploy_acs_sl_print_bom',
            params: {
                item_id: rec.id,
                is_simple: isSimple
            }
        });
        window.open(outputUrl);
    }    

    function renderCSV(isSimple){
        rec = currentRecord.get();
        var outputUrl = url.resolveScript({
            scriptId: 'customscript_acs_sl_csv_bom',
            deploymentId: 'customdeploy_acs_sl_csv_bom',
            params: {
                item_id: rec.id,
                is_simple: isSimple
            }
        });
        window.open(outputUrl);
    }

    return {
        pageInit : pageInit,
        renderPdf : renderPdf,
        renderCSV: renderCSV
    }
});
