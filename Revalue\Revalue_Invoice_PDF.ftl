<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<#setting url_escaping_charset="UTF-8">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="3" style="width: 60%;">
                            <table>
                                <tr>
                                    <td>
                                        <#if companyInformation.logoUrl?length !=0><img
                                                src="${companyInformation.logoUrl}" style="float: left; margin: 7px" />
                                        </#if>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="nameandaddress"
                                            style="font-size: 13pt;">${companyInformation.companyName}</span><br /><span
                                            class="nameandaddress"
                                            style="font-size: 9pt;">${companyInformation.companyName}</span><br /><span
                                            class="nameandaddress"
                                            style="font-size: 9pt;">${companyInformation.addressText}</span><br /><span
                                            class="nameandaddress" style="font-size: 9pt;">(678)
                                            671-5400</span><br /><span class="nameandaddress"
                                            style="font-size: 9pt;">www.revaluefitness.com</span></td>
                                </tr>
                            </table>
                        </td>
                        <td align="left" style="width: 40%;"><span
                                class="title">${record@title}</span><br /><br /><br />&nbsp;
                            <table style="width: 100%;">
                                <tr style="width: 50%;">
                                    <td align="left"><span class="small"><b>Date </b></span></td>
                                    <td align="left"><span class="small">${record.trandate}</span></td>
                                </tr>
                                <tr style="width: 50%;">
                                    <td align="left"><span class="small"><b>Invoice # </b></span></td>
                                    <td align="left"><span class="small">${record.tranid}</span></td>
                                </tr>
                                <tr style="width: 50%;">
                                    <td align="left">&nbsp;</td>
                                    <td align="left">&nbsp;</td>
                                </tr>
                                <tr style="width: 50%;">
                                    <td align="left"><span class="small"><b>${record.terms@label}</b></span></td>
                                    <td align="left"><span class="small">${record.terms}</span></td>
                                </tr>
                                <tr style="width: 50%;">
                                    <td align="left"><span class="small"><b>${record.duedate@label}</b></span></td>
                                    <td align="left"><span class="small">${record.duedate}</span></td>
                                </tr>
                                <tr style="width: 50%;">
                                    <td align="left"><span class="small"><b>${record.salesrep@label}</b></span></td>
                                    <td align="left"><span class="small">${record.salesrep}</span></td>
                                </tr>
                                <tr style="width: 50%;">
                                    <td align="left"><span
                                            class="small"><b>${record.custbody_cust_phone@label}</b></span></td>
                                    <td align="left"><span class="small">${record.custbody_cust_phone}</span></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td><span class="impmessage" style="line-height: 250%">IMPORTANT: Acceptance by the signatory
                                confirms that all goods indicated on Invoice were received in good condition.</span>
                        </td>
                    </tr>
                    <tr>
                        <td><span class="small" style="line-height: 250%">Print Name: </span></td>
                    </tr>
                    <tr>
                        <td><span class="small" style="line-height: 250%">Signature: _____________________ </span></td>
                    </tr>
                    <tr>
                        <td><span class="disclaimer">All sales are final for all used and pre-owned items sold by
                                Revalue Fitness Equipment. All used and pre-owned items are sold as-is unless a
                                transferable warranty is explicitly provided at time of purchase. All new items are
                                warranted solely by the manufacturer. All new items requiring repair should be sent to
                                the manufacturer. Revalue Fitness Equipment shall not be liable if the manufacturer or
                                any third-party service provider fails to perform warranty service. THERE IS NO IMPLIED
                                WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE ON ANY ITEM SOLD BY
                                REVALUE FITNESS EQUIPMENT. Revalue Fitness Equipment shall not be responsible for any
                                consequential or incidental damage resulting from the sale or use of any merchandise
                                purchased from us. </span></td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0px;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0px;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                font-weight: bold;
            }

            span.title {
                font-size: 19pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            span.small {
                font-size: 9pt;
            }

            span.impmessage {
                font-weight: bold;
                font-size: 9pt;
            }

            span.disclaimer {
                font-size: 7.5pt;
                font-style: italic;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="17%" footer="nlfooter" footer-height="100pt"
        padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
                <td class="addressheader" colspan="3"><b>${record.shipaddress@label}</b></td>
            </tr>
            <tr>
                <td class="address" colspan="3" rowspan="2">${record.billaddress}<#if record.type=='purchord'>
                        <br /><br />${record.entity.phone}</#if>
                </td>
                <td class="address" colspan="3" rowspan="2">${record.shipaddress}<#if record.type !='purchord'>
                        <br /><br />${record.custbody_cust_phone}</#if>
                </td>
                <#if record.status != 'Paid In Full'>
                    <#assign link=record.custbody_stripe_invoice_payment_link?replace('<[^>]+>','','r')>
                    <td align="right" colspan="5"><img href='${link}' src="https://5324603-sb1.app.netsuite.com/core/media/media.nl?id=29033&amp;c=5324603_SB1&amp;h=a7e0e1a895becbb3dcb3" width="30%" height="30%"/></td>
                <#else>
                    <td align="right" colspan="5"><span style="font-size: 13pt">Paid in Full</span></td>
                </#if>
            </tr>
            <tr>
                <td align="right" class="totalboxbot" colspan="5">&nbsp;</td>
            </tr>
        </table>
        <br />
        <#if record.item?has_content>
            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th colspan="3">Item</th>
                                <th colspan="12">Description</th>
                                <th align="right" colspan="4">${item.quantity@label}</th>
                                <th align="right" colspan="3">${item.amount@label}</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td colspan="3" line-height="150%">${item.custcol_itemdisplayname}</td>
                        <td colspan="12"><span class="itemname">${item.item}</span></td>
                        <td align="right" colspan="4">${item.quantity}</td>
                        <td align="right" colspan="3">${item.amount}</td>
                    </tr>
                </#list><!-- end items -->
            </table>

            <hr />
        </#if>
        <table class="total" style="width: 100%; margin-top: 10px;">
            <tr>
                <td colspan="4">&nbsp;</td>
                <td align="right"><b>${record.subtotal@label}</b></td>
                <td align="right">${record.subtotal}</td>
            </tr>
          	<tr>
                <td colspan="4">&nbsp;</td>
                <td align="right" style="white-space: nowrap;"><b>${record.discountitem@label} (${record.discountrate})</b></td>
                <td align="right">${record.discounttotal}</td>
            </tr>
            <tr>
                <td colspan="4">&nbsp;</td>
                <td align="right"><b>${record.taxtotal@label} (${record.taxrate}%)</b></td>
                <td align="right">${record.taxtotal}</td>
            </tr>
            <tr class="totalrow">
                <td background-color="#ffffff" colspan="4">&nbsp;</td>
                <td align="right"><b>${record.total@label}</b></td>
                <td align="right">${record.total}</td>
            </tr>
        </table>
    </body>
</pdf>