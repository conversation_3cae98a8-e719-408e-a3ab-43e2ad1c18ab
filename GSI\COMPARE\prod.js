/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
 define([], function() {

    function beforeLoad(context) {
        if(context.type == context.UserEventType.CREATE){
            var formObj = context.form;
            var shipMethodField = formObj.getField({
                id: 'shipmethod'
            });
            shipMethodField.defaultValue = '';
        }
    }

    return {
        beforeLoad: beforeLoad,
    }
});
