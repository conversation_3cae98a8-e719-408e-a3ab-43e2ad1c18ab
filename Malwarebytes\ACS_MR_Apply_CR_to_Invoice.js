/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/workflow'], function(search, record, workflow) {

    function getInputData() {
        
        try {

            // retrieve files created today
            var searchObj = search.load({
                id: 'customsearch_acs_cm_split_ord'
            });
    
            return searchObj;
        } catch (e) {
            log.error('getInputData error', e);
        }
    }

    function map(context) {
        try {
            var contextValues = JSON.parse(context.value);
            var cmId = contextValues.values.internalid.value;
            var cmSfOrderId = contextValues.values.custbody_sf_order_id;
            // var cmAmount = Math.abs(contextValues.values.amount);

            var cmObj = record.load({
                type: record.Type.CREDIT_MEMO,
                id: cmId,
                isDynamic: false
            });

            var lineCount = cmObj.getLineCount({ sublistId: 'apply' });
            if(lineCount) {
                for(var i = 0; i < lineCount; i++) {
                    var isApplied = cmObj.getSublistValue({ sublistId: 'apply', fieldId: 'apply', line: i });
                    if(!isApplied) {
                        var invId = cmObj.getSublistValue({ sublistId: 'apply', fieldId: 'internalid', line: i });
                        var soLookUpObj = search.lookupFields({
                            type: search.Type.TRANSACTION,
                            id: invId,
                            columns: ['custbody_sf_order_id']
                        });

                        var invSfOrderId = soLookUpObj.custbody_sf_order_id;

                        if(invSfOrderId == cmSfOrderId) {
                            cmObj.setSublistValue({ sublistId: 'apply', fieldId: 'apply', value: true, line: i });
                            var cmAmount = Math.abs(cmObj.getValue({ fieldId: 'unapplied' }));

                            var cmId = cmObj.save();

                            log.audit("Success", "Successfully applied CM to SO");

                            var invAmount = Math.abs(cmObj.getSublistValue({ sublistId: 'apply', fieldId: 'total', line: i }));
                            log.debug('invAmount', {invAmount: invAmount, cmAmount: cmAmount, cmId: cmId, invId: invId});
                            
                            if(invAmount > cmAmount) {

                                workflow.trigger({
                                    recordType: record.Type.INVOICE,
                                    recordId: invId,
                                    workflowId: 'customworkflow_acs_email_inv_2',
                                    actionId: 'workflowaction_send_email_trigger'
                                });

                            } else if (invAmount < cmAmount) {
                                
                                workflow.trigger({
                                    recordType: record.Type.CREDIT_MEMO,
                                    recordId: cmId,
                                    workflowId: 'customworkflow_acs_email_cm_2',
                                    actionId: 'workflowaction_send_email_trigger'
                                });

                            }

                        }


                    }
                }
            }
            
        } catch(e) {
            log.error('Map - Error map', e);
        }
    }

    function reduce(context) {
        
    }

    function summarize(summary) {
       
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
