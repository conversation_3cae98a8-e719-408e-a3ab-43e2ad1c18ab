/**
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
define(['N/file', 'N/runtime', 'N/task', 'N/search'], function (file, runtime, task, search) {

    function pad(pad, str, padLeft) {
        if (typeof str === 'undefined')
            return pad;
        if (padLeft) {
            return (pad + str).slice(-pad.length);
        } else {
            return (str + pad).substring(0, pad.length);
        }
    }

    function execute(context) {
        
        var scriptObj = runtime.getCurrentScript();

        // file cabinet queue folder id
        var queueFolderID = scriptObj.getParameter({ name: 'custscript_queue_folder_id' });

        // search id
        var searchId = scriptObj.getParameter({ name: 'custscript_search_id' });
        var sendFile = scriptObj.getParameter({ name: 'custscript_send_file_aft_create' });

        var date = new Date;

        var dateTime = pad("00", (date.getMonth() + 1), true) + pad("00", date.getDate(), true) + date.getFullYear() + '_' + pad("00", date.getHours(), true) + pad("00", date.getMinutes(), true);
        var fileName = "SMS-" + pad("**********", '**********', true) + "_" + dateTime + ".txt";

        var positivePaySearch = search.load({
            id: searchId
        });
        var contents = '';
        positivePaySearch.run().each(function(result) {
            
            contents += pad(Array(11).join(" "), result.getValue(result.columns[0]), true) + ","; // Account Number
            contents += result.getValue(result.columns[1]) + ","; // Serial Number
            contents += result.getValue(result.columns[2]).trim() + ","; // Amount
            contents += result.getValue(result.columns[3]) + ","; // Issue Date
            contents += result.getText(result.columns[4]) + ","; // Payee Name
            contents += result.getValue(result.columns[5]); // Transaction Code
            contents += "\r\n";

            return true;
            
        });

        if(contents != '') {

            var fileObj = file.create({
                name: fileName,
                fileType: file.Type.PLAINTEXT,
                contents: contents,
                folder: queueFolderID
            });

            var fileId = fileObj.save();

            log.audit('File Created', { createdFileID: fileId });

            if(sendFile) {

                var sftpTask = task.create({
                    taskType: task.TaskType.SCHEDULED_SCRIPT,
                    scriptId: 'customscript_acs_ss_send_pp_file',
                    deploymentId: 'customdeploy_acs_ss_send_pp_file',
                    params: {
                        custscript_manual_file_id: fileId
                    }
                }).submit();

                log.audit('sftpTask', sftpTask);
            }

        }

    }

    return {
        execute: execute
    }
});
