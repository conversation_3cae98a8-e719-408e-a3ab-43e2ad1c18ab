function getInput(options){
    // Load the 3PL API
    var api = new options.in8syncApi({ configurationId: options.configurationId });
    var results=[];
    var today = new Date();
    var yesterday = new Date(today)
    	yesterday.setDate(yesterday.getDate() - 7)
    var yesterdayISO = yesterday.toISOString();
    var payload = {
        status: "refunded",
        min_updated_at:yesterdayISO
      };
//https://api.returnly.com/returns/21992267
    // GET the Payload
    api.get({//url:"https://api.returnly.com/returns/22216974",
       url: "https://api.returnly.com/returns?" + api.jsonQS(payload),
      //url: "https://api.returnly.com/returns/?min_updated_at=2021-04-25T16%3A39%3A49.180Z&status=delivered",
        headers: {
            'accept': 'application/json',
            'content-type': 'application/json',
            'X-Api-Token': api.getApiKeyOnly()
        },
        OK: function (response) {
            var datRes = response.body;
            datRes = JSON.parse(datRes)
            results = datRes.data
           log.emergency('results',results)
        },
        Failed: function (response) {
            log.emergency('error', response)
        }
    });

    return results;
}