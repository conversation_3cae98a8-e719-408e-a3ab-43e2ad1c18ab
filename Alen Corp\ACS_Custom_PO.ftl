<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="2" width="55%">
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    float="left" width="140px" height="53px" /> </#if>
                        </td>
                        <td width="45%" align="left"></td>
                    </tr>
                    <tr>
                        <td align="left"><span class="title">${record@title}</span></td>
                    </tr>
                    <tr>
                        <td><span class="nameandaddress">${companyInformation.addressText}</span></td>
                        <td>
                            <table width="100%">
                                <tr>
                                    <td line-height="150%">&nbsp;</td>
                                    <td line-height="150%">&nbsp;</td>
                                </tr>
                                <tr>
                                    <td line-height="150%"><b>Date</b></td>
                                    <td line-height="150%">${record.trandate}</td>
                                </tr>
                                <tr>
                                    <td line-height="150%"><b>PO #</b></td>
                                    <td line-height="150%">${record.tranid}</td>
                                </tr>
                                <tr>
                                    <td line-height="150%"><b>Payment</b></td>
                                    <td line-height="150%">${record.terms}</td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td>
                            <barcode codetype="code128" showtext="true" value="${record.tranid}" />
                        </td>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }
            
            table.itemtable {
                font-size: 7pt;
            }

            table.itemtable th {
                font-size: 7.5pt;
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.itemtable td {
                border: 1;
                border-color: #e3e3e3;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 9pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                font-size: 8.5pt;
                padding-top: 0;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="18%" footer="nlfooter" footer-height="20pt" padding="0.4in 0.4in 0.4in 0.4in"
        size="Letter">
        <table style="width: 80%;">
            <tr>
                <td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
                <td class="addressheader" colspan="3"><b>${record.shipaddress@label}</b></td>
            </tr>
            <tr>
                <td class="address" colspan="3" rowspan="2">${record.billaddress}</td>
                <td class="address" colspan="3" rowspan="2">${record.shipaddress}</td>
            </tr>
        </table>
        <#if record.item?has_content>

            <#--  <table class="itemtable" style="width: 102%; margin-top: 10px; padding-left: -15px;">  -->
            <table class="itemtable" style="width: 100%; margin-top: 25px;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th width="4%" align="center">#</th>
                                <th width="20%" align="center">${item.item@label}</th>
                                <th width="24%" align="center">Purch. ${item.description@label}</th>
                                <th width="13%" align="center">Req. Ship Date</th>
                                <th width="5%" align="center">Qty</th>
                                <th width="12%" align="center">Unit Price</th>
                                <th width="12%" align="center">${item.amount@label}</th>
                                <th width="7%" align="center">${item.inventorydetail@label}</th>
                            </tr>
                        </thead>
                    </#if>
                    <#assign line_no = item_index + 1>
                    <tr>
                        <td align="center">${line_no}</td>
                        <td>${item.item} ${item.custcol_acs_item_display_name}</td>
                        <td>${item.custcol_acs_item_purch_desc}</td>
                        <td align="center">${item.custcol_sps_itemscheduledate1}</td>
                        <td align="center">${item.quantity}</td>
                        <td align="right">${item.rate}</td>
                        <td align="right">${item.amount}</td>
                        <td align="right">${item.inventorydetail?keep_before(",")}</td>
                    </tr>
                </#list><!-- end items -->
            </table>
        </#if>

        <hr />
        <table class="total" style="width: 100%;">
            <tr class="totalrow">
                <td background-color="#ffffff" colspan="4">&nbsp;</td>
                <td background-color="#ffffff" align="right"><b>${record.total@label}</b></td>
                <td background-color="#ffffff" align="right">${record.total}</td>
            </tr>
        </table>
    </body>
</pdf>