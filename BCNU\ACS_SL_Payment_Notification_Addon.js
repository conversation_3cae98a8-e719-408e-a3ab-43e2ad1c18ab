/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/record'], function(record) {

    function onRequest(context) {
        try {
            var recid = context.request.parameters.recid;

            var paymentObj = record.load({
                type: record.Type.VENDOR_PAYMENT,
                id: recid
            });

            var count = paymentObj.getLineCount({ sublistId: 'credit' });

            var test = [];
            for(var i = 0; i < count; i++){
                var appliedTo =  paymentObj.getSublistValue({ sublistId: 'credit', fieldId: 'appliedto', line: count });
                log.debug("test", appliedTo);

                test.push(appliedTo);

            }

            var returnStr = "<#assign prep_data =" + JSON.stringify({ test: test }) + " />"; 

            context.response.write({
                output: returnStr
            });


        } catch (e) {
            log.debug("error", e);
        }
    }

    return {
        onRequest: onRequest
    }
});
