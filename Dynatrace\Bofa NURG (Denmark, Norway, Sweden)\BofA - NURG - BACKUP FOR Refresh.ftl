<#-- format specific processing -->
<#function getReferenceNote payment>
    <#assign paidTransactions = transHash[payment.internalid]>
    <#assign referenceNote = "">
    <#assign paidTransactionsCount = paidTransactions?size>
    <#if (paidTransactionsCount >= 1)>
    	<#list paidTransactions as transaction>
    		<#if transaction.tranid?has_content>
    			<#if referenceNote?has_content>
    				<#assign referenceNote = referenceNote + ", " + transaction.tranid>
    			<#else>
    				<#assign referenceNote = transaction.tranid>
    			</#if>
		    </#if>
		</#list>
    </#if>
	<#return referenceNote>
</#function>
<#-- cached values -->
<#assign totalAmount = computeTotalAmount(payments)>
<#-- template building -->
#OUTPUT START#
<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
<CstmrCdtTrfInitn>
<GrpHdr>
<MsgId>${cbank.custrecord_2663_file_name_prefix}${pfa.name}</MsgId>
<CreDtTm>${pfa.custrecord_2663_file_creation_timestamp?date?string("yyyy-MM-dd")}T${pfa.custrecord_2663_file_creation_timestamp?time?string("HH:mm:ss")}</CreDtTm>
<NbOfTxs>${payments?size?c}</NbOfTxs>
<CtrlSum>${formatAmount(totalAmount,"decLessThan1")}</CtrlSum>
<InitgPty>
<Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm>
<Id>
<OrgId>
<Othr>
<Id>${cbank.custpage_eft_custrecord_2663_bank_comp_id}</Id>
<SchmeNm>
<Cd>CUST</Cd>   
</SchmeNm>  
</Othr>
</OrgId>
</Id>
</InitgPty>
</GrpHdr>
<PmtInf>
<PmtInfId>${pfa.id}-1</PmtInfId>
<PmtMtd>TRF</PmtMtd>
<BtchBookg>false</BtchBookg>
<NbOfTxs>${payments?size?c}</NbOfTxs>
<CtrlSum>${formatAmount(totalAmount,"decLessThan1")}</CtrlSum>
<PmtTpInf>
<SvcLvl>
<Cd>NURG</Cd>
</SvcLvl>
</PmtTpInf>
<ReqdExctnDt>${pfa.custrecord_2663_process_date?string("yyyy-MM-dd")}</ReqdExctnDt>
<Dbtr>
<Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm>
<PstlAdr>
<StrtNm>${cbank.custpage_eft_custrecord_2663_bank_address1} ${cbank.custpage_eft_custrecord_2663_bank_address2}</StrtNm>
<CtrySubDvsn>${cbank.custpage_eft_custrecord_2663_bank_state}</CtrySubDvsn> 
<Ctry>${cbank.custpage_eft_custrecord_2663_country_code}</Ctry>
</PstlAdr>
</Dbtr>
<DbtrAcct>
<Id>
<Othr>
<Id>${cbank.custpage_eft_custrecord_2663_acct_num}</Id>
</Othr>
</Id>
<Tp>
<Cd>CACC</Cd>
</Tp>
<Ccy>CAD</Ccy>
</DbtrAcct>
<DbtrAgt>
<FinInstnId>
<BIC>${cbank.custpage_eft_custrecord_2663_bic}</BIC>
<Nm>${cbank.custpage_eft_custrecord_2663_bank_name}</Nm>
<PstlAdr>
<Ctry>${cbank.custpage_eft_custrecord_2663_country_code}</Ctry>
</PstlAdr>
</FinInstnId>
</DbtrAgt>
<ChrgBr>SHAR</ChrgBr>
<#list payments as payment>
    <#assign ebank = ebanks[payment_index]>
    <#assign entity = entities[payment_index]>
<CdtTrfTxInf>
<PmtId>
<InstrId>${pfa.id}-${payment.tranid}</InstrId>
<EndToEndId>${pfa.id}-${payment.tranid}</EndToEndId>
</PmtId>
<Amt>
<InstdAmt Ccy="${getCurrencySymbol(cbank.custrecord_2663_currency)}">${formatAmount(getAmount(payment),"decLessThan1")}</InstdAmt>
</Amt>
<CdtrAgt>
<FinInstnId>
<ClrSysMmbId><MmbId>${ebank.custrecord_2663_entity_bank_code}</MmbId></ClrSysMmbId>
<PstlAdr>
<CtrySubDvsn>${ebank.custrecord_2663_entity_state}</CtrySubDvsn>
<Ctry>${ebank.custrecord_2663_entity_country_code}</Ctry>
</PstlAdr>
</FinInstnId>
</CdtrAgt>
<Cdtr>
<Nm>${setMaxLength(convertToLatinCharSet(buildEntityName(entity)),70)}</Nm>
<PstlAdr>
<StrtNm>${entity.billaddress1}</StrtNm>
<TwnNm>${entity.billcity}</TwnNm>
<CtrySubDvsn>${entity.billstate}</CtrySubDvsn>
<Ctry>${ebank.custrecord_2663_entity_country_code}</Ctry>
</PstlAdr>
</Cdtr>
<CdtrAcct>
<Id>
<Othr>
<Id>${ebank.custrecord_2663_entity_acct_no}</Id>
</Othr>
</Id>
</CdtrAcct>
</CdtTrfTxInf>
</#list>
</PmtInf>
</CstmrCdtTrfInitn>
</Document><#rt>
#OUTPUT END#