function scheduled(type) {

    try {
        var s = nlapiSearchRecord('customrecord_in8_storage', null, [
            new nlobjSearchFilter('custrecord_in8_storage_status', null, 'is', 1)
        ], [
            new nlobjSearchColumn('internalid'),
            new nlobjSearchColumn('custrecord_in8_storage_customer'),
            new nlobjSearchColumn('custrecord_in8_storage_amount'),
            new nlobjSearchColumn('custrecord_in8_storage_amount_cad'),
            new nlobjSearchColumn('custrecord_in8_storage_ref_date')            
        ]) || [];

        for (var i = 0; i < s.length; i++) {
            processInvoice(s[i].getValue('internalid'), s[i].getValue('custrecord_in8_storage_customer'),
             s[i].getValue('custrecord_in8_storage_amount'),  s[i].getValue('custrecord_in8_storage_amount_cad'), s[i].getValue('custrecord_in8_storage_ref_date'));

            if (nlapiGetContext().getRemainingUsage() < 500) {

                nlapiLogExecution('DEBUG', 'Debug', 'Yield script');
                nlapiYieldScript();
            }
        }

    } catch (e) {
        nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
    }
}

function processInvoice(internalId, customer, amount, amountCAD, date) {

    try {
        nlapiSubmitField('customrecord_in8_storage', internalId, 'custrecord_in8_storage_status', 2);

        //throw nlapiCreateError("Record ID", "TEST ERRROR");

        var record = nlapiCreateRecord('invoice', {
            recordmode: 'dynamic',
            entity: customer
        });
        record.setFieldValue('entity', customer);

        var currency = nlapiLookupField('customer', customer, 'currency');

        var date = nlapiStringToDate(date);

        var month = date.getMonth() + 1;

        var item = null;

        if (month >= 8 && month <= 10) {
            item = 513;
        } else if (month >= 2 && month <= 4) {
            item = 516;
        } else if (month >= 5 && month <= 7) {
            item = 518;
        } else if (month >= 11 || month == 1) {
            item = 517;
        }

        // Select the correct item
        record.selectNewLineItem('item');
        record.setCurrentLineItemValue('item', 'item', item);
        record.setCurrentLineItemValue('item', 'quantity', 1);
        record.setCurrentLineItemValue('item', 'rate', currency == 1 ? amountCAD : amount);

        //if (record.getFieldValue('shipcountry') == 'US') {
            record.setCurrentLineItemValue('item', 'taxcode', 12);    
        // } else {
        //     record.setCurrentLineItemValue('item', 'taxcode', 18);
        // }
        record.commitLineItem('item');

        id = nlapiSubmitRecord(record, true, true);

        nlapiLogExecution('DEBUG', 'in8', 'Invoice created: ' + id);

        // Send email
        sendEmail(customer, id);

        nlapiSubmitField('customrecord_in8_storage', internalId, ['custrecord_in8_storage_status', 'custrecord_in8_storage_result'], [3, 'Invoice created: ' + id]);
    } catch (e) {
        nlapiSubmitField('customrecord_in8_storage', internalId, ['custrecord_in8_storage_status', 'custrecord_in8_storage_result'], [4, (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e)]);
    }
}

function sendEmail(customer, transaction) {

    var template = 7;

    var emailMerger = nlapiCreateEmailMerger(template);

    emailMerger.setEntity('customer', customer);

    var mergeResult = emailMerger.merge();

    var emailSubject = mergeResult.getSubject();
    var emailBody = mergeResult.getBody();

    var records = {
        transaction: transaction
    };

    nlapiLogExecution('DEBUG', 'in8', 'It will send email to: ' + customer);

    var file = nlapiPrintRecord('TRANSACTION', transaction, 'PDF', null);

    //file.setFolder(273782);
    //var fileId = nlapiSubmitFile(file);        

    nlapiSendEmail(-5, customer, emailSubject, emailBody, null, null, records, file, null, null, null);
}