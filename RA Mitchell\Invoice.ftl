<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="3">
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    style="height: 70px; width: 350px; float: left; margin: 7px;" /> </#if>
                        </td>
                        <td align="right"><b style="font-size: 20px;">Invoice</b></td>
                    </tr>
                    <tr>
                        <td align="right">Invoice Number: ${record.tranid}</td>
                    </tr>
                    <tr>
                        <td align="right">Invoice Date: ${record.trandate}</td>
                    </tr>
                    <tr>
                        <td align="center"><span style="font-size:10px;">103 Pope&#39;s Island ~ New Bedford, MA
                                02740-7252</span></td>
                    </tr>
                    <tr>
                        <td align="center"><span style="font-size:10px;">www.ramitchell.com ~ <EMAIL></span>
                        </td>
                    </tr>
                    <tr>
                        <td align="center"><span style="font-size:10px;">Phone: ************ Fax: ************</span>
                        </td>
                    </tr>
                    <tr>
                        <td align="center"><span style="font-size:10px;">Generator Systems, Engines, Pumps, Parts and
                                Service</span></td>
                    </tr>
                    <tr>
                        <td align="center"><span style="font-size:10px;">WBE/DBE Certified</span></td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>

                <table>
                    <tr class="totalrow">
                        <td align="center" background-color="#ffffff" colspan="9"
                            style="font-size: 8px; line-height: 8px;">NOTE: FREIGHT IS NOT INCLUDED UNLESS SPECIFIED OR
                            QUOTED. PRICES ARE F.O.B. New Bedford or Factory. PLEASE INQUIRE IF YOU HAVE ANY QUESTIONS.
                            Invoices over 30 days will be subject to a charge of 1 1/2 % per month. (18% Annual
                            Percentage Rate) - Minimum Charge of $.50. Purchaser will be responsible for costs of
                            collection, including reasonable attorney&#39;s fees. SPECIAL ORDER / &quot;NONSTOCK&quot;
                            ITEMS ARE SUBJECT TO A 15% - 25% FACTORY RESTOCKING FEE IF RETURNED. SOME ITEMS ARE NOT ABLE
                            TO BE RETURNED IF OPENED/USED. ELECTRICAL COMPONENTS CAN NOT BE RETURNED. RETURN FREIGHT
                            WILL BE DEDUCTED FROM CUSTOMER&#39;S CREDIT IF APPLICABLE.</td>
                    </tr>
                    <tr class="totalrow">
                        <td align="center" background-color="#ffffff" colspan="9" style="line-height: 12px;">
                            <b>AUTHORIZED DISTRIBUTOR/DEALER - JOHN DEERE, LISTER-PETTER, DEUTZ, MP PUMPS, KOHLER
                                GENERATORS, MURPHY ENGINE CONTROLS AND MORE. Check out our new website -
                                www.ramitchell.com</b></td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0px;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0px;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="15%" footer="nlfooter" footer-height="10%" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        <table height="50" style="margin-top: 10px; margin-left: 50px; font-size:15px" width="816px">
            <tr>
                <td class="addressheader" colspan="3"><b>Sold To:</b></td>
                <td class="addressheader" colspan="3"><b>Ship To:</b></td>
            </tr>
            <tr>
                <td class="address" colspan="3" rowspan="2">
                    ${record.billaddressee}<br />${record.billaddr1}<br />${record.billcity}, ${record.billstate}
                    ${record.billzip}</td>
                <td class="address" colspan="3" rowspan="2">
                    ${record.shipaddressee}<br />${record.shipaddr1}<br />${record.shipcity}, ${record.shipstate}
                    ${record.shipzip}</td>
            </tr>
        </table>
        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th style="width: 25%;">Customer PO</th>
                <th style="width: 25%;">Customer SO</th>
                <th style="width: 25%;">Payment Terms</th>
                <th style="width: 25%;">Memo</th>
            </tr>
            <tr>
                <td>${record.otherrefnum}</td>
                <td>${record.createdfrom}</td>
                <td>${record.terms}</td>
                <td>${record.memo}</td>
            </tr>
        </table>

        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th style="width: 25%;">Sales Rep ID</th>
                <th style="width: 25%;">Shipping Method</th>
                <th style="width: 25%;">Ship Date</th>
            </tr>
            <tr>
                <td style="height: 1px;">${record.salesrep}</td>
                <td style="height: 1px;">${record.shipmethod}</td>
                <td style="height: 1px;">${record.shipdate}</td>
            </tr>
        </table>
        <#if record.item?has_content>

            <table class="itemtable" style="margin-top: 10px; width: 100%;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th colspan="1" style="width: 5%;">QTY</th>
                                <th colspan="2" style="width: 5%;">B/O</th>
                                <th colspan="3" style="width: 35%;">Item</th>
                                <th colspan="4" style="width: 25%;">Serial Number</th>
                                <th colspan="6" style="width: 15%;">Unit Price</th>
                                <th colspan="7" style="width: 15%; text-align: right;">Total</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td align="left" colspan="1" line-height="150%" style="width: 5%;">${item.quantity}</td>
                        <td align="left" colspan="2" style="width: 5%;">${item.quantityremaining}</td>
                        <td align="left" colspan="3" style="width: 35%;">
                            <strong>${item.item}</strong><br />${item.description}</td>
                        <td align="left" colspan="4" style="width: 25%;">${item.inventorydetail}</td>
                        <td align="left" colspan="6" style="width: 15%;">${item.rate}</td>
                        <td align="right" colspan="7" style="width: 15%;">${item.amount}</td>
                    </tr>
                </#list><!-- end items -->
            </table>

            <hr />
        </#if>
        <table class="total" style="width: 100%; margin-top: 10px;">
            <tr>
                <td colspan="4">&nbsp;</td>
                <td align="right" colspan="2"><b>${record.subtotal@label}</b></td>
                <td align="right">${record.subtotal}</td>
            </tr>
            <tr>
                <td colspan="4">&nbsp;</td>
                <td align="right" colspan="2"><b>Sales Tax:</b></td>
                <td align="right">${record.taxtotal}</td>
            </tr>
            <tr class="totalrow">
                <td align="left" background-color="#ffffff" colspan="4">&nbsp;</td>
                <td align="right" colspan="2"><b>${record.total@label}</b></td>
                <td align="right">${record.total}</td>
            </tr>
            <tr>
                <td align="left" background-color="#ffffff" colspan="4">&nbsp;</td>
                <td align="right" colspan="2"><strong>Payments</strong></td>
                <td align="right">${record.amountpaid}</td>
            </tr>
            <tr>
                <td align="left" background-color="#ffffff" colspan="4">&nbsp;</td>
                <td align="right" colspan="2"><strong>Amount Remaining</strong></td>
                <td align="right"><b><i>${record.amountremaining}</i></b></td>
            </tr>
            <tr>
                <td align="left" background-color="#ffffff" colspan="4">${record.custbody2}</td>
            </tr>
        </table>
    </body>
</pdf>