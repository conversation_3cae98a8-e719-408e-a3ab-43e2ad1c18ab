<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            td p {
                align: left
            }

        </style>
    </head>

    <body padding="0.5cm 0.5cm 0.5cm 0.5cm" size="letter">
    
        <#list records as check>

            <div style="position: relative;font-family: Helvetica,sans-serif;left: -3pt;height: 253pt;width: 612pt;page-break-before: avoid;font-size: 8pt;">
            
                <table style="position: absolute;overflow: hidden;left: 333pt;top: 3.6pt;height: 13pt;width: 70pt;">
                    <tr>
                        <td>${check.trandate}</td>
                    </tr>
                </table>
                
                <table style="position: absolute;overflow: hidden;left: 417pt;top: 3.6pt;height: 7pt;width: 40pt;font-size: 8pt;">
                    <tr>
                        <td align="center">&nbsp;</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 520.6pt;top: -4.4pt;height: 20pt;width: 90pt; font-size: 7pt;">
                    <tr>
                        <td>${check.entity}</td>
                    </tr>
                </table>
                <#if check.apply?has_content>

                    <table style="position: absolute;overflow: hidden;left: 10pt;top: 46pt;width: 100%;">
                        <#list check.apply as apply>
                            <tr>
                                <td width="13%">${apply.refnum}</td>
                                <td width="13.5%">${apply.applydate}</td>
                                <td width="13%">${apply.amount}</td>
                                <td width="52%" align="right">${apply.total}</td>
                            </tr>
                        </#list>
                    </table>
                </#if>

                <table style="position: absolute;overflow: hidden;left: 10pt;top: 206.8pt;height: 46pt;width: 100%;">
                    <tr>
                        <td width="13%">&nbsp;</td>
                        <td width="13.5%">&nbsp;</td>
                        <td width="13%">&nbsp;</td>
                        <td width="52%" align="right">${check.total}</td>
                    </tr>
                </table>
            </div>

            <div style="position: relative;font-family: Helvetica,sans-serif;top: 5pt;left: -3pt;height: 260pt;width: 612pt;page-break-before: avoid;font-size: 8pt;">
            
                <table style="position: absolute;overflow: hidden;left: 333pt;top: 14.4pt;height: 13pt;width: 70pt;">
                    <tr>
                        <td>${check.trandate}</td>
                    </tr>
                </table>
                
                <table style="position: absolute;overflow: hidden;left: 417pt;top: 18pt;height: 7pt;width: 40pt;font-size: 8pt;">
                    <tr>
                        <td align="center">&nbsp;</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 520.6pt;top: 10pt;height: 20pt;width: 90pt; font-size: 7pt;">
                    <tr>
                        <td>${check.entity}</td>
                    </tr>
                </table>
                <#if check.apply?has_content>

                    <table style="position: absolute;overflow: hidden;left: 10pt;top: 46pt;width: 100%;">
                        <#list check.apply as apply>
                            <tr>
                                <td width="13%">${apply.refnum}</td>
                                <td width="13.5%">${apply.applydate}</td>
                                <td width="13%">${apply.amount}</td>
                                <td width="52%" align="right">${apply.total}</td>
                            </tr>
                        </#list>
                    </table>
                </#if>

                <table style="position: absolute;overflow: hidden;left: 10pt;top: 214pt;height: 46pt;width: 100%;">
                    <tr>
                        <td width="13%">&nbsp;</td>
                        <td width="13.5%">&nbsp;</td>
                        <td width="13%">&nbsp;</td>
                        <td width="52%" align="right">${check.total}</td>
                    </tr>
                </table>
            </div>
            
            <div style="position: relative;font-family: Helvetica,sans-serif;height: 5pt;left: -3pt;height: 260pt;width: 612pt;page-break-before: avoid;font-size: 9.5pt;">

                <table style="position: absolute;overflow: hidden;left: 390pt;top: 86.96pt;height: 18pt;width: 108pt;">
                    <tr>
                        <td>${check.trandate}</td>
                    </tr>
                </table>

                
                <table style="position: absolute;overflow: hidden;left: 500pt;top: 86.96pt;height: 7pt;width: 40pt;">
                    <tr>
                        <td align="center">&nbsp;</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 443pt;top: 142.96pt;height: 18pt;width: 145pt;">
                    <tr>
                        <td align="center">${check.total}
                        </td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 59.2pt;top: 168.08pt;height: 14pt;width: 400pt;font-size: 11pt;">
                    <tr>
                        <td>${check.entity}</td>
                    </tr>
                </table>
                <table style="position: absolute;overflow: hidden;left: 59.2pt;top: 182.08pt;width: 400pt;">
                    <tr>
                        <td>${check.address?string?replace(check.entity + "<br />","")}</td>
                    </tr>
                </table>
            </div>
        </#list>
    </body>
</pdf>