function reviewEmployee(empId, taskId){
    var empObj = nlapiLoadRecord('employee', empId);
    var taskObj = nlapiLoadRecord('task', taskId);
    taskObj.setFieldValue('status', 'PROGRESS');
    nlapiSubmitRecord(taskObj, true);

    window.location.reload();
}

function approveEmployee(empId, taskId){
    var empObj = nlapiLoadRecord('employee', empId);
    var taskObj = nlapiLoadRecord('task', taskId);
    taskObj.setFieldValue('status', 'COMPLETE');
    nlapiSubmitRecord(taskObj, true);
    empObj.setFieldValue('custentity_employee_status', 2);
    nlapiSubmitRecord(empObj, true);

    window.location.reload();
}

function rejectEmployee(empId, taskId){
    var empObj = nlapiLoadRecord('employee', empId);
    var taskObj = nlapiLoadRecord('task', taskId);
    taskObj.setFieldValue('status', 'COMPLETE');
    nlapiSubmitRecord(taskObj, true);
    empObj.setFieldValue('custentity_employee_status', 3);
    nlapiSubmitRecord(empObj, true);

    window.location.reload();
}