/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define([], function() {

    function beforeLoad(context) {
        var getForm = context.form;
        getForm.clientScriptModulePath = 'SuiteScripts/TinKnockers_Test_Behavior.js';
        getForm.addButton({
            id: 'custpage_mainbtn',
            label: 'Test Print',
            functionName: 'redirectToPDF()'
        });
    }

    function beforeSubmit(context) {
        
    }

    function afterSubmit(context) {
        
    }

    return {
        beforeLoad: beforeLoad,
        beforeSubmit: beforeSubmit,
        afterSubmit: afterSubmit
    }
});
