function suitelet(request, response)
{
    var secretKey = 'ac8420dd-5069-4877-8d19-c017c12706b2';
    var hash = String(CryptoJS.SHA256(secretKey + (request.getParameter('custpage_employee') || "1")));

    var form = nlapiCreateForm('How to call Employee Suitelet?');

    form.addFieldGroup('custpage_instructions', 'Instructions');

    var message = '<html><body><br/><span style="font-size: 15px">Base URL: {externalurl}<br/>Parameters:<br/>-\t<b>internalid</b> : Employee Internal Id<br/>-\t<b>hash</b>: String <span style="color:red">"secretkey"</span>+<span style="color:blue">"internalid"</span> ' + 
    '(Example: <span style="color:red">abcdefeghijklmnop</span><span style="color:blue">123456</span>) encrypted on SHA256 method <br/><br/>Secret Key: <span style="color:green; font-weight: bold">“ac8420dd-5069-4877-8d19-c017c12706b2”</span><br/><br/>' + 
    //'Sample URL: <a href="{externalurl}&internalid={userid}&hash={hash}">{externalurl}&internalid={userid}&hash={hash}</a><br/><br/>' +
    'Dashboard URL: <a href="{externalurl1}&internalid={userid}&recordid={userid}&hash={hash}&customform=10&dash=T">{externalurl1}&internalid={userid}&recordid={userid}&hash={hash}&customform=10&dashboard=T</a><br/>' +
    '</span></body></html>';

    var baseurl = nlapiResolveURL('SUITELET', 'customscript_in8_customersearch_sl', 'customdeploy_in8_customersearch_sl', true);
    var baseurl1 = nlapiResolveURL('SUITELET', 'customscript_in8_externalcustomer_sl', 'customdeploy_in8_externalcustomer_sl', true);

    message = message.replace(/{externalurl}/g, baseurl);
    message = message.replace(/{externalurl1}/g, baseurl1);
    message = message.replace(/{userid}/g, request.getParameter('custpage_employee'));
    message = message.replace(/{userid}/g, request.getParameter('custpage_employee'));
    message = message.replace(/{hash}/g, hash);
    message = message.replace(/{hash}/g, hash);

    form.addField('custpage_info', 'inlinehtml', '', null, 'custpage_instructions').setDefaultValue(message);


    if(request.getMethod() == "GET")
    {
        form.addFieldGroup('custpage_generate', 'Enter the fields below to generate a valid Sample URL');

        var emplField = form.addField('custpage_employee', 'select', 'Select a User to Create a Sample URL:', 'employee', 'custpage_generate');
        emplField.setMandatory(true);

        form.addSubmitButton('Hash');

    }

    response.writePage(form);

}