/**
 *@NApiVersion 2.1
 *@NScriptType Suitelet
 */
 define(['N/https', 'N/render', 'N/record', 'N/format', 'N/search'], function(https, render, record, format, search) {

    function onRequest(context) {
        if(context.request.method == https.Method.GET){
            requestParams = context.request.parameters;
            var int_InvoiceID = parseInt(requestParams.custpage_invoice);

            // CREATE TEMPLATE RENDERER OBJECT
            let renderer = render.create();

            // SELECT TEMPLATE TO BE USED VIA SCRIPT ID
            renderer.setTemplateByScriptId({
                scriptId: "CUSTTMPL_130_249816_236"
            });

            // ADD THE RECORD TO BE USED BASED ON PARAMETERS GIVEN
            let obj_Invoice = record.load({
                type: record.Type.INVOICE,
                id: int_InvoiceID,
                isDynamic: true
            });

            let int_EmployeeID = obj_Invoice.getValue({ fieldId: 'salesrep' });
            let int_CustomerID = obj_Invoice.getValue({ fieldId: 'entity' });

            renderer.addRecord({
                templateName: 'record',
                record: obj_Invoice
            });

            let str_EmployeeMobilePhone;
            
            if(int_EmployeeID) {

                str_EmployeeMobilePhone = search.lookupFields({
                    type: search.Type.EMPLOYEE,
                    id: int_EmployeeID,
                    columns: ['mobilephone']
                }).mobilephone;

            } 

            let str_SalesOrderNumber = obj_Invoice.getText({ fieldId: 'createdfrom' }).replace("Sales Order #SO", "");
            let str_InvoiceNumber = obj_Invoice.getText({ fieldId: 'tranid' });

            let obj_ShippingAddress = obj_Invoice.getSubrecord('shippingaddress');
            
            let obj_CustomerLookup = search.lookupFields({
                type: search.Type.CUSTOMER,
                id: int_CustomerID,
                columns: ['mobilephone', 'phone']
            });

            let str_CustomerMobile = ((obj_CustomerLookup.mobilephone) ? obj_CustomerLookup.mobilephone : 'N/A');
            let str_CustomerPhone = ((obj_CustomerLookup.phone) ? obj_CustomerLookup.phone : 'N/A');
            let str_ShipAddress1 = obj_ShippingAddress.getValue({ fieldId: 'addr1' });
            let str_ShipCity = obj_ShippingAddress.getValue({ fieldId: 'city' });
            let str_ShipState = obj_ShippingAddress.getValue({ fieldId: 'state' });
            let str_ShipZip = obj_ShippingAddress.getValue({ fieldId: 'zip' });
            
            renderer.addCustomDataSource({
                format: render.DataSource.OBJECT,
                alias: "JSON",
                data: {
                    sales_rep_number: str_EmployeeMobilePhone,
                    so_inv_number: `${str_SalesOrderNumber}/${str_InvoiceNumber}`,
                    cust_mobile: str_CustomerMobile,
                    cust_phone: str_CustomerPhone,
                    cust_ship_addr_1: str_ShipAddress1,
                    cust_ship_city: str_ShipCity,
                    cust_ship_state: str_ShipState,
                    cust_ship_zip: str_ShipZip
                }
            });

            // RENDER AS STRING
            let str_InvoiceWorksheetXML = renderer.renderAsString();

            // SEND RESPONSE XML TO RENDER AS PDF
            context.response.renderPdf({
                xmlString: str_InvoiceWorksheetXML
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
