/**
 * Items Sync - Sync NetSuite Items with WooCommerce Products
 * 
 * Version    Date            Author           		Remarks
 * 1.0        01 Apr 2017     <PERSON> (InEight)	Initial Version
 *
 */

var In8SyncSubscription = ( function() {

	var _record = null,
		_recordId = null,
		_recordType = null,
		_isNewRecord = false,
		_errorsList = [],
		settings,
		isMultiSite = false,
		isOneWorldInst = isOneWorld(),
		isValidationActive = false,
		isCustomRecord = false;		
	
	var syncCustomRecord = function(recordType, recordId, isNewRecord) {
		
		isCustomRecord = true;
		
		syncRecord(recordType, recordId, isNewRecord, true);
	};
		
	/**
	 * Sync Products
	 *                      
	 * @returns {Void} 
	 */
	var syncRecord = function(recordType, recordId, isNewRecord, isCustomMapping) {
		
		var wcMappings,		
			record = null,
			recordIds = [],
			wcFields,
			categories,
			sites = [],
			parameters,
			results = { success : false, message : '' },
			wcId,
			i = 0;		
		
		// Assign global variables
		_record = nlapiLoadRecord(recordType, recordId);							
		
		_recordType = recordType;
		_recordId = recordId;	
		_isNewRecord = isNewRecord;
				
		recordIds.push(recordId);
		
		record = _record;
		
		// Get active Parameters for this record
		//parameters = In8Lib.getParameters('item');
		parameters = In8Lib.getParameters(isCustomMapping ? recordType : 'item');
		
		if (!parameters) {
			nlapiLogExecution('DEBUG', 'In8Sync', 'Item ' + _recordId + ' was not synced because there is not any active parameter.');
		} else {
			isMultiSite = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_multisite') == 'T'; //parameters.length > 1;
			
			// Checks if the current record needs to be synced
			for (i = 0; i < parameters.length; i++) {
				
				// Get Settings
				var settingId = parameters[i].getValue('custrecord_in8_sync_setting');
				
				if (!settingId) {
					//throw nlapiCreateError('E001', '.', true);
					nlapiLogExecution('DEBUG', 'In8Sync', 'Parameter without related Setting. Please look at the Parameters');
					continue;
				}
				
				settings = In8Lib.getSettings(settingId);
				
				if (settings.consumerKey == null || settings.consumerSecret == null ||
					settings.consumerKey == '' || settings.consumerSecret == '') {
					nlapiLogExecution('DEBUG', 'In8Sync', 'Invalid consumer key or consumer secret.');		
					continue;
				}
								
				//nlapiLogExecution('DEBUG', 'In8Sync', 'Product has WooCommerce Id so it will get synced. Id: ' + wcId);
				
				// For each parameter available, check the conditions
				// If the record has the WooCommerce Id Or if it meets the conditions or if it is inactive, sync it
				if (In8Lib.checkConditions(parameters[i].getValue('internalid'), isCustomMapping ? recordType : 'item', recordType, recordIds) || 
						_record.getFieldValue('custrecord_in8_wc_id') || _record.getFieldValue('custrecord_in8_display_web_site') == 'T') {
										
					// Perform the Item synchronization
					wcId = performSync(record, parameters[i]);
					
					nlapiLogExecution('DEBUG', 'In8Sync', '-----------------------------------------------------------------------------------------');
					
					if (wcId) {
						results.success = true;
						
						results.message += '- Item "' + record.getFieldValue('itemid') + '" synced successfully.<br>WooCommerce Id: <b>' + wcId + '</b>';
						
						if (settings.wp_url) {
							results.message += ' - <a href="' + settings.wp_url + '/post.php?post=' + wcId + '&action=edit" target="_blank">Open Item in WooCommerce</a><BR>';	
						}												
					} else {
						results.success = false;
						
						results.message += '- Item "' + record.getFieldValue('itemid') + '" was not synced.<BR>';
					}					 					
				} else {
					nlapiLogExecution('DEBUG', 'Sync', 'Item ' + _recordId + ' was not synced to the web site (setting Id ' + settingId + ') due to the conditions.');
					
					results.message += '- Item "' + record.getFieldValue('itemid') + '" was not synced due to the conditions<BR>';
				}		
			}
		}
		
		return results;
	};
	
	/**
	 * Perform integration with a site  
	 *                      
	 * @returns {Object} Object
	 */
	var performSync = function(record, parameter) {
		
		var wcMappings,		
			wcFields,
			syncParams,
			roles,
			settingId,
			wcId;
					
		// Get Mappings
		wcMappings = In8Lib.getWcFieldsMapping(parameter.getValue('internalid'));
				
		settingId = parameter.getValue('custrecord_in8_sync_setting');		
		
		// Customizations to sync subscription pack
			
		syncSubscription(wcMappings, _record, parameter.getValue('internalid'));	
	};
	
	var syncSubscription = function(wcMappings, record, syncParams) {
		
		var productObject, 
			i = 0, 
			id,
			PRODUCT_ID = nlapiGetContext().getSetting('SCRIPT', 'custscript_woo_subscription_item_id'),
			variation,
			jsonText,
			customFields = {},
			wcResults,
			results;
		
		// Get the product record			
		
		productObject = {};
		
		productObject.product = {};
		productObject.product.id = PRODUCT_ID;
		
		id  = record.getFieldValue('custrecord_in8_wc_id');
					
		// Add a new variation
		variation = {};
		
		variation.id = id;
		
		variation.sku = record.id;

		productObject.product.variations = [];
		
		//variation = getSyncSubscriptionItem(variation, wcMappings, record, syncParams);
		
		// Set the fields
		In8Lib.setWcFieldsObject(wcMappings, variation, false, record, false, isCustomRecord, null, false);
		
		getSubscriptionPrice(record, variation);
				
		var attributesSearch = getAttributesSearch(syncParams);
		
		if (attributesSearch) {
			
			variation.attributes = getSubscripAttributesOptions(attributesSearch, record, variation);			
		}
				
		productObject.product.variations.push(variation);						
					
		// Update the record id. Send to WC
		jsonText = JSON.stringify(productObject);
		
		logJSONData('Send data to WC', jsonText);		
					
	    // Call the Products API
	    results = nlapiRequestURL(settings.url + '/products/' + PRODUCT_ID + '/?consumer_key=' + settings.consumerKey + '&consumer_secret=' + settings.consumerSecret, jsonText, settings.headers);

	    logJSONData('Received data from WC', results.getBody());
	    
	    wcResults = JSON.parse(formatJSONData(results.getBody()));
    
		if (!id) {						
			for (i = 0; i < wcResults.product.variations.length; i++) {				
				if (wcResults.product.variations[i].sku == record.id) {
					id = wcResults.product.variations[i].id;
					break;
				}
			}						
			if (id) {
				// Update the Id
				nlapiSubmitField(_recordType, _recordId, 'custrecord_in8_wc_id', String(id));	
			}										
		}			

		if (id) {
			// Set the custom field data
			In8Lib.setWcFieldsObject(wcMappings, customFields, true, record, false, isCustomRecord, null, true, _record);
			
			// Set dates
			var dates = record.getFieldValues('custrecord_ps_delivery_months');
			var reoccurrence = record.getFieldText('custrecord_ps_reoccurrence');
			//var day = '01';
			
			//if (reoccurrence == 'Quarterly') {
			//	day = '15'
			//}
			
			if (dates && dates.length > 0) {
				for (i = 0; i < dates.length; i++) {						
					customFields['_cc_date_' + (i + 1)] = '15/' + (dates[i] < 10 ? '0' : '') + dates[i];						
				}	
			}				
			
			customFields['_subscription_payment_sync_date'] = 1; //parseInt(day);
						
			// Set the subscription price
			customFields['_subscription_price'] = variation.regular_price;
			
			customFields['_variation_description'] = variation.description;
				
			// Custom fields
			jsonText = JSON.stringify(customFields);
			
			nlapiLogExecution('DEBUG', 'In8Sync', 'Sent data Custom Fieds: ' + jsonText);
		
			// Send custom fields
			results = nlapiRequestURL(settings.url + '/meta_post/?id=' + id + '&consumer_key=' + settings.consumerKey + '&consumer_secret=' + settings.consumerSecret + '&data=' + Base64.encode(jsonText), null, settings.headers);
	
			nlapiLogExecution('DEBUG', 'In8Sync', 'Received data Custom fields: ' + results.getBody());	
		}					
	};
	
	var getSyncSubscriptionItem = function(variation, wcMappings, record, syncParams) {
				
				
				
		return variation;
	};
	
	var getSubscripAttributesOptions = function(searchResults, record, variation) {
		
		var attributes = [],
			attribute = {},	
			attributesLabels,
			recmach,
			valueToCompare,
			valueIfTrue,
			valueIfFalse,
			sourceField,
			i = 0, i1 = 0;
		
		if (!searchResults) {
			return null;
		}
		
		for (i = 0; searchResults && i < searchResults.length; i++) {
			
			valueToCompare 	= searchResults[i].getValue('custrecord_in8_sync_attrib_value_compare');
			valueIfTrue 	= searchResults[i].getValue('custrecord_in8_sync_attrib_value_true');
			valueIfFalse 	= searchResults[i].getValue('custrecord_in8_sync_attrib_value_false');
			sourceField 	= searchResults[i].getValue('custrecord_in8_sync_attrib_field_source');
			
			if (valueToCompare) {
				
				var sourceValue = record.getFieldValue(sourceField);
				
				if (sourceValue) sourceValue = sourceValue.toLowerCase();
			
				//nlapiLogExecution('DEBUG', 'In8Sync', 'Source Value: ' + sourceValue +  ' - value compare: ' + valueToCompare);
				
				if (sourceValue == valueToCompare.toLowerCase()) {
					
					//nlapiLogExecution('DEBUG', 'In8Sync', 'Value match: ' + searchResults[i].getValue('custrecord_in8_sync_attrib_name') + ' - ' + valueIfTrue);
					
					attributes.push(getAttributeObject(searchResults[i], valueIfTrue, variation));
				} else {
					if (valueIfFalse != '') {
						attributes.push(getAttributeObject(searchResults[i], valueIfFalse, variation));
					}								
				}							
		
			} else {
				attributes.push(getAttributeObject(searchResults[i], record.getFieldText(sourceField), variation));
			}
			//attribute = {};
			
			//attribute.name = searchResults[i].getValue('custrecord_in8_sync_attrib_name');
			//attribute.slug = searchResults[i].getValue('custrecord_in8_sync_attrib_slug');
			
			//attribute.option = record.getFieldText(sourceField); 
						
			/*if (attribute.slug == 'months') {
				//attribute.option = '4';
				
				switch (attribute.option) {
					case 'Monthly':
						attribute.option = '12';
						break;
					case 'Quarterly':
						attribute.option = '4';
						break;
					case 'Biannual':
						attribute.option = '2';
						break;
					case 'Annual':
						attribute.option = '1';
						break;
					case 'Single':
						attribute.option = '1';
						break;
				}
			}*/
			
			
			//attributes.push(attribute);																							
		}
		return attributes;
	};
	
	var getAttributeObject = function(result, value, variation) {
		
		var attribute = {};
		
		attribute.name = result.getValue('custrecord_in8_sync_attrib_name');
		attribute.slug = result.getValue('custrecord_in8_sync_attrib_slug');
		
		if (attribute.slug == 'bottles') {				
			/*var quantity = 0,
				recmac = 'recmachcustrecord_ps_subscription_pack',
				i1 = 0;
			
			for (i1 = 1; i1 <= record.getLineItemCount(recmac); i1++) {
				quantity += parseInt(record.getLineItemValue(recmac, 'custrecord_ps_item_quantity', i1));
			}*/
			
			attribute.option = variation.bottles;
		} else {
			attribute.option = value;	
		}
				 		
		return attribute;
	}
	
	var getSubscriptionPrice = function(record, variation) {
		
		var price = 0,
			currentPrice = 0,
			recmac = 'recmachcustrecord_ps_subscription_pack',
			recordItem,
			recordItemComponent,
			quantity,
			quantityTotal = 0,
			i1 = 0,
			i;
		
		variation.description = '';
		
		for (i1 = 1; i1 <= record.getLineItemCount(recmac); i1++) {
			//quantity += parseInt();
			
			// Load the Item group
			recordItem = nlapiLoadRecord(nlapiLookupField('item', record.getLineItemValue(recmac, 'custrecord_ps_subscription_pack_item', i1), 'recordtype'), record.getLineItemValue(recmac, 'custrecord_ps_subscription_pack_item', i1));
			
			quantity = record.getLineItemValue(recmac, 'custrecord_ps_item_quantity', i1);
			
			if (recordItem && recordItem.getLineItemCount('member') > 0) {
				
				// Load each member
				for (i = 1; i <= recordItem.getLineItemCount('member'); i++) {
					
					quantity = recordItem.getLineItemValue('member', 'quantity', i);
					
					quantityTotal += parseInt(quantity);
					
					nlapiLogExecution('DEBUG', 'In8Sync', 'Loading data from record: ' +  recordItem.getLineItemValue('member', 'item', i));
					
					recordItemComponent = nlapiLoadRecord(nlapiLookupField('item', recordItem.getLineItemValue('member', 'item', i), 'recordtype'), recordItem.getLineItemValue('member', 'item', i));
					
					variation.description += (i > 1 || i1 > 1 ? '\n' : '') + quantity + ' x ' + (recordItemComponent.getFieldValue('storedisplayname') ? recordItemComponent.getFieldValue('storedisplayname') : recordItem.getLineItemValue('member', 'memberdescr', i));
					
					// Gets the price
					currentPrice = In8Lib.getPricing(3, recordItemComponent, null, 1) ? parseFloat(In8Lib.getPricing(3, recordItemComponent, null, 1)) : 0;
					
					price += (currentPrice * quantity);
					
					nlapiLogExecution('DEBUG', 'In8Sync', 'Item pricing: ' + currentPrice);					
				}				
			}
		}	
		
		// According to the quantity, set the bottles field
		if (quantityTotal == 6) {
			variation.bottles = 'Six Bottles';
		} else if (quantityTotal == 12) {
			variation.bottles = 'Twelve Bottles';
		}		
		
		variation.regular_price = price;
	};
	
	/**
	 * Get Attributes
	 *    
	 * @returns {Array} Attributes
	 */
	var getAttributesSearch = function(syncParams) {
		
		var filters = [],
			columns = [],
			searchResults,
			i = 0;
	
		//filters[i++] = new nlobjSearchFilter('custrecord_in8_sync_recordtype', 'custrecord_in8_sync_attributes', 'is', 'item');
		filters[i++] = new nlobjSearchFilter('custrecord_in8_sync_attributes', null, 'is', syncParams);
	
		i = 0;
	
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_field_source');
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_field_value');
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_field_sublist');
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_name');
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_position');
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_visible');
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_slug');
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_item_type');											
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_value_compare');
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_value_true');
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_value_false');		
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_type');
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_wc_id');		
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_translation_f');		
		columns[i++] = new nlobjSearchColumn('custrecord_in8_sync_attrib_source_parent');

		columns[i++] = new nlobjSearchColumn('internalid');
		
		searchResults = nlapiSearchRecord('customrecord_in8_sync_attributes', null, filters, columns);
		
		return searchResults;
	};
	
	var getWcIdForSetting = function(record, settingId) {
		
		var recmac = 'recmachcustrecord_in8_wc_ids_item',
			i = 0;
		
		for (i = 1; i <= record.getLineItemCount(recmac); i++) {
			
			if (record.getLineItemValue(recmac, 'custrecord_in8_wc_ids_setting', i) == settingId) {
				return record.getLineItemValue(recmac, 'custrecord_in8_wc_ids_id', i)
			}
		}
		return null;
	};
	
	var logJSONData = function(description, jsonText) {
		
		var str = jsonText;
		var chunks = [];
		var chunkSize = 3000;

		while (str) {
		    if (str.length < chunkSize) {
		        chunks.push(str);
		        break;
		    }
		    else {
		        chunks.push(str.substr(0, chunkSize));
		        str = str.substr(chunkSize);
		    }
		}
		
		for (var i = 0; i < chunks.length; i++) {
			nlapiLogExecution('DEBUG', description + ' - ' + (i + 1) + '/' + chunks.length, chunks[i]);	
		}		
	};
	
	return {
		syncRecord : syncRecord,
		syncCustomRecord: syncCustomRecord,
		errorsList : _errorsList,
	};	
})();
