<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

  <head>
    <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
      src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
      src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
    <#if .locale=="zh_CN">
      <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
        src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
      <#elseif .locale=="zh_TW">
        <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
          src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
        <#elseif .locale=="ja_JP">
          <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
            src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
          <#elseif .locale=="ko_KR">
            <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
              src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
            <#elseif .locale=="th_TH">
              <link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}"
                src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
    </#if>
    <macrolist>
      <macro id="nlheader">
        <table style="width: 100%; font-size: 10pt;">
          <tr>
            <td rowspan="3" style="padding: 0; width: 100px">
              <#if companyInformation.logoUrl?length !=0><img
                  src="http://249816.shop.netsuite.com/core/media/media.nl?id=6426&amp;c=249816&amp;h=51a7e6e5e647d77a63b5"
                  style="width: 300px; height: 75px" /> </#if>
            </td>
            <td align="right" style="padding: 0; width: 400px; font-size: 16pt">${record@title}</td>
          </tr>
          <tr>
            <td align="right" style="font-size: 16pt;">${record.tranid}</td>
          </tr>
          <tr>
            <td align="right">${record.trandate}</td>
          </tr>
        </table>
      </macro>
      <macro id="nlfooter">
        <table class="footer" style="width: 100%;">
          <tr>
            <td>
              <barcode codetype="code128" showtext="true" value="${record.tranid}" />
            </td>
          </tr>
        </table>
      </macro>
    </macrolist>
    <style type="text/css">
      * {
        <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
        <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
        <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
        <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
        <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
        <#else>font-family: NotoSans, sans-serif;
        </#if>
      }

      table {
        font-size: 9pt;
        table-layout: fixed;
      }


      table.worksheet {
        font-size: 12pt;
      }

      th {
        font-weight: bold;
        font-size: 8pt;
        vertical-align: middle;
        padding: 5px 6px 3px;
        background-color: #e3e3e3;
        color: #333333;
      }

      td {
        padding: 4px 6px;
      }

      td p {
        align: left
      }

      b {
        font-weight: bold;
        color: #333333;
      }

      table.header td {
        padding: 0px;
        font-size: 10pt;
      }

      table.footer td {
        padding: 0px;
        font-size: 8pt;
      }

      table.itemtable th {
        padding-bottom: 10px;
        padding-top: 10px;
      }

      table.body td {
        padding-top: 2px;
      }

      table.total {
        page-break-inside: avoid;
      }

      tr.totalrow {
        background-color: #e3e3e3;
        line-height: 200%;
      }

      td.totalboxtop {
        font-size: 12pt;
        background-color: #e3e3e3;
      }

      td.addressheader {
        font-size: 8pt;
        padding-top: 6px;
        padding-bottom: 2px;
      }

      td.address {
        padding-top: 0px;
      }

      td.totalboxmid {
        font-size: 28pt;
        padding-top: 20px;
        background-color: #e3e3e3;
      }

      td.totalboxbot {
        background-color: #e3e3e3;
        font-weight: bold;
      }

      span.title {
        font-size: 28pt;
      }

      span.number {
        font-size: 16pt;
      }

      span.itemname {
        font-weight: bold;
        line-height: 150%;
      }

      hr {
        width: 100%;
        color: #d3d3d3;
        background-color: #d3d3d3;
        height: 1px;
      }
    </style>
  </head>

  <body header="nlheader" header-height="10%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in"
    size="Letter">
    <table style="width: 100%; margin-top: 10px;">
      <tr>
        <td colspan="3" style="font-size: 8pt; padding: 6px 0 2px; font-weight: bold; color: #333333;">Bill To:</td>
        <td colspan="3" style="font-size: 8pt; padding: 6px 0 2px; font-weight: bold; color: #333333;">Ship To:</td>
      </tr>
      <tr>
        <td class="address" colspan="3" style="padding: 0;">${record.billaddress}<br />${record.billphone}</td>
        <td class="address" colspan="3" rowspan="2">${record.shipaddress}</td>
      </tr>
      <tr></tr>
    </table>

    <table style="width: 100%; margin-top: 10px;">
      <tr>
        <th>Customer #</th>
        <th>Designer</th>
        <th>Order Type</th>
        <th>Delivery Date</th>
        <th>Delivery Method</th>
      </tr>
      <tr>
        <td style="padding-top: 2px;">${record.entity.id}</td>
        <td style="padding-top: 2px;">${record.salesrep}</td>
        <td style="padding-top: 2px;">${record.custbody_noel_saletype}</td>
        <td style="padding-top: 2px;">${record.shipdate}</td>
        <td style="padding-top: 2px;">${record.shipmethod}</td>
      </tr>
    </table>
    <#if record.item?has_content>

      <table style="width: 100%; margin-top: 5px;">
        <!-- start items -->
        <#list record.item as item>
          <#if item_index==0>
            <thead>
              <tr>
                <th align="center" colspan="3" style="width: 10%">${item.quantity@label}</th>
                <th align="center" style="padding: 10px 6px; width: 20%;">Image</th>
                <th align="center" colspan="12" style="padding: 10px 6px; width: 30%;">Item Description</th>
                <th align="center" style="padding: 10px 6px; width: 13.33%;">Retail</th>
                <th align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">Sale Price</th>
                <th align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">${item.amount@label}</th>
              </tr>
            </thead>
          </#if>
          <#if item.custcol_noel_trnslinefld_imageurl?has_content>
            <#assign image_loc=item.custcol_noel_trnslinefld_imageurl?replace("&amp;", "&#38;" )>
              <#assign image_url="https://249816.app.netsuite.com${image_loc}">
                <#else>
                  <#assign
                    image_url="https://249816.app.netsuite.com/core/media/media.nl?id=3203&c=249816_SB1&h=20c1ec7e1d2c1009fe41"
                    ?html>
          </#if>
          <tr>
            <td align="center" colspan="3" style="padding: 15px 6px; width: 10%;">${item.quantity}</td>
            <td align="center" style="padding: 10px 6px; width: 10%;"><img src="${image_url}"
                style="width: 100px; height: 100px" /></td>
            <td align="left" colspan="12" style="padding: 10px 6px; width: 40%;"><span
                style="font-weight: bold; line-height: 150%; color: #333333;">${item.item}</span><br />${item.description}
            </td>
            <td align="center" style="padding: 10px 6px; width: 13.33%;">
              ${(item.custcol_noel_retail_price_rate*item.quantity)?string.currency}</td>
            <td align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">${item.rate}</td>
            <td align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">${item.amount}</td>
          </tr>
        </#list><!-- end items -->
      </table>

      <hr style="width: 100%; color: #d3d3d3; background-color: #d3d3d3; height: 1px;" />
    </#if>
    <table width="100%">
      <tr width="100%">
        <td width="55%">
          <table align="left" style="width: 100%; margin-top: 0px;">
            <tr>
              <td align="left" style="font-weight: bold; color: #333333;">Signature below indicates that merchandise was
                received in good condition. Please check merchandise carefully. Noel Furniture is not liable for damage
                once merchandise is removed from premises.</td>
            </tr>
            <tr>
              <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
            </tr>
            <tr>
              <td align="left" style="font-weight: bold; color: #333333;">
                SIGNATURE____________________________________________________________</td>
            </tr>
            <tr>
              <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
            </tr>
            <tr>
              <td align="left" style="font-weight: bold; color: #333333;">
                DATE____________________________________________
              </td>
            </tr>
            <tr>
              <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
            </tr>
          </table>
        </td>
        <td width="45%">
          <table align="left" style="page-break-inside: avoid; width: 100%; margin-top: 0px;">
            <tr>
              <td align="left" style="font-weight: bold; color: #333333;">${record.subtotal@label}</td>
              <td align="right">${record.subtotal}</td>
            </tr>
            <tr>
              <td align="left" style=" font-weight: bold; color: #333333;">${record.taxtotal@label} (${record.taxrate}%)
              </td>
              <td align="right">${record.taxtotal}</td>
            </tr>
            <tr style="background-color: #e3e3e3; line-height: 200%;">
              <td align="left" style="font-weight: bold; color: #333333;">${record.total@label}</td>
              <td align="right">${record.total}</td>
            </tr>
            <#if record.custbody12!=0>
              <tr>
                <td align="left" style="font-weight: bold; color: #333333;">Deposit</td>
                <td align="right">${record.custbody12}</td>
              </tr>
            </#if>
            <#if record.custbody11!=0>
              <tr>
                <td align="left" style="font-weight: bold; color: #333333;">Credit Memo</td>
                <td align="right">${record.custbody11}</td>
              </tr>
            </#if>
            <#if record.custbody13!=0>
              <tr>
                <td align="left" style="font-weight: bold; color: #333333;">Payment</td>
                <td align="right">${record.custbody23}</td>
              </tr>
            </#if>
            <tr style="background-color: #e3e3e3; line-height: 200%;">
              <td align="left" style="font-weight: bold; color: #333333;">Balance Due</td>
              <td align="right">${record.amountremaining}</td>
            </tr>
          </table>
        </td>
      </tr>
    </table>

    <table style="width: 100%; margin-top: 0px;">
      <tr>
        <td align="center">THANK YOU FOR SHOPPING LOCAL</td>
      </tr>
      <tr>
        <td align="center">67 CENTS OF EVERY DOLLAR SPENT REMAINS IN OUR LOCAL COMMUNITY</td>
      </tr>
      <tr>
        <td align="center" style="font-weight: bold; color: #333333;">No&euml;l Home | 2727 Southwest Freeway| Houston,
          TX
          77098 |713.874.5200</td>
      </tr>
      <tr>
        <td align="center" style="font-weight: bold; color: #333333;">See Terms On Reverse</td>
      </tr>
    </table>
  </body>

  <body>
    <table style="width: 100%; margin-top: 10px;">
      <thead>
        <tr>
          <th align="center" style="padding: 10px 6px; font-size: 20pt;">NOEL DELIVERY SET UP WORKSHEET </th>
        </tr>
      </thead>
    </table>

    <table style="width: 100%; margin-top: 10px;">
      <tr>
        <td width="40%" style="align: center;"></td>
        <td width="40%" style="align: center;"></td>
        <td width="20%" style="align: center;"></td>
      </tr>
      <tr>
        <td style="align: center;">___________________________________________________</td>
        <td style="align: center;">___________________________________________________</td>
        <td style="align: center;">_______________________</td>
      </tr>
      <tr>
        <td style="align: center; font-size: 11pt;"><strong>Delivery Day/ Date</strong></td>
        <td style="align: center; font-size: 11pt;"><strong>City/Area</strong></td>
        <td style="align: center; font-size: 11pt;"><strong>Time</strong></td>
      </tr>
    </table>

    <table style="width: 100%; margin-top: 10px;">
      <tr>
        <td width="40%" style="align: left; font-size: 11pt; "><strong>Designer:</strong> _____________________________
        </td>
        <td width="20%" style="align: left; font-size: 11pt; "><strong>Going:</strong> Yes / No</td>
        <td width="40%" style="align: left; font-size: 11pt; "><strong>Driver:</strong>
          _________________________________</td>
      </tr>
      <tr>
        <td colspan="2" style="align: left; font-size: 11pt;"><strong>Designer Cell:</strong>
          ________________________________________________</td>
      </tr>
      <tr>
        <td colspan="2" style="align: left; font-size: 11pt; padding: 0px 0px 0px 0px;">
          <table width="100%">
            <tr>
              <td width="32%" style="align: left; font-size: 11pt;"><strong>Sale/Invoice #'s:</strong></td>
              <td width="68%" style="align: left; font-size: 11pt;"> __________________________________________</td>
            </tr>
            <tr>
              <td style="align: left; font-size: 11pt;"></td>
              <td style="align: left; font-size: 11pt;"> __________________________________________</td>
            </tr>
          </table>
        </td>
        <td style="align: left; font-size: 11pt; border: solid; color: red;">Balance Due:</td>
      </tr>
    </table>

    <br />

    <table style="width: 100%; margin-top: 10px;">
      <tr>
        <td colspan="4" width="40%" style="align: left; font-size: 11pt; "><strong>Customer Name:</strong>
          ________________________________________________________________________________________</td>
      </tr>
      <tr>
        <td colspan="4" width="40%" style="align: left; font-size: 11pt; "><strong>Phone Numbers:</strong>
          _____________________________________________//_________________________________________</td>
      </tr>
      <tr>
        <td colspan="4" width="40%" style="align: left; font-size: 11pt; "><strong>Delivery Address:</strong>
          _______________________________________________________________________________________</td>
      </tr>
      <tr>
        <td colspan="4" width="40%" style="align: left; font-size: 11pt; "><strong>City:</strong>
          ___________________________________<strong>State:</strong> _____________________________<strong>Zip:</strong>
          __________________________</td>
      </tr>
      <tr>
        <td colspan="4" width="40%" style="align: left; font-size: 11pt; "></td>
      </tr>
      <tr>
        <td width="25%" style="align: center; font-size: 11pt; "><strong>HOUSE</strong></td>
        <td width="25%" style="align: center; font-size: 11pt; "><strong>APARTMENT</strong></td>
        <td width="25%" style="align: center; font-size: 11pt; "><strong>TOWNHOUSE</strong></td>
        <td width="25%" style="align: center; font-size: 11pt; "><strong>HIGH RISE</strong></td>
      </tr>
      <tr>
        <td colspan="4" width="40%" style="align: left; font-size: 11pt; "></td>
      </tr>
      <tr>
        <td colspan="4" width="40%" style="align: left; font-size: 11pt; "><strong>NAME OF SUBDIVISION/BUILDING/COMPLEX
          </strong>__________________________________________________________</td>
      </tr>
      <tr>
        <td colspan="4" width="40%" style="align: left; font-size: 11pt; "></td>
      </tr>
      <tr>
        <td colspan="4" width="40%" style="align: left; font-size: 10pt; "><strong>Building Contact Name &amp;
            Number:</strong> __________________________________________________________ <strong>Guarded Gate: Yes or
            No</strong></td>
      </tr>
      <tr>
        <td colspan="4" width="40%" style="align: left; font-size: 10pt; "><strong>Insurance Required: Yes or
            No</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>Elevator Available:&nbsp;&nbsp;&nbsp;Yes or
            No</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>Reservations
            Confirmed:</strong>&nbsp;&nbsp; ______________</td>
      </tr>
    </table>

    <table style="width: 100%; height: 150px; margin-top: 10px;">
      <tr>
        <td style="align: left; font-size: 10pt; border: solid;"><strong>Best Directions from Our Store: </strong> </td>
      </tr>
    </table>

    <table style="width: 100%; margin-top: 10px;">
      <tr>
        <td style="align: center; font-size: 9pt; "><span style="background-color: #ffff00;"><strong>GATED
              COMMUNITY:</strong> Yes / No&nbsp;&nbsp;&nbsp;<strong>IDENTIFICATION REQUIRED:</strong>&nbsp;&nbsp;Yes /
            No&nbsp;&nbsp;&nbsp;<strong>INSURANCE REQUIRED:</strong> Yes / No </span></td>
      </tr>
      <tr>
        <td style="align: center; font-size: 9pt; "></td>
      </tr>
      <tr>
        <td style="align: center; font-size: 9pt; "><strong>ENTRY:</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SINGLE
          DOOR&nbsp;&nbsp;&nbsp;&nbsp;DOUBLE DOOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>ROOM CLEARED:</strong> Yes
          / No <strong>DISPOSAL NEEDED:</strong> Yes / No</td>
      </tr>
      <tr>
        <td style="align: center; font-size: 9pt; "></td>
      </tr>
      <tr>
        <td style="align: center; font-size: 9pt; ">
          <strong>FLOOR:</strong>&nbsp;&nbsp;&nbsp;&nbsp;1ST&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2ND&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3RD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>STAIRS:</strong>&nbsp;&nbsp;&nbsp;&nbsp;STRAIGHT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;TURNS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SPIRAL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;NARROW&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;N/A
        </td>
      </tr>
      <tr>
        <td style="align: center; font-size: 9pt; "></td>
      </tr>
      <tr>
        <td style="align: left; font-size: 9pt; "><span style="background-color: #ffff00;">THERE IS A $50 PER HALF HOUR
            CHARGE TO MOVE ADDITIONAL FURNITURE. 1 HOUR MINIMUM. Noel Delivery Team cannot remove or haul personal trash
            from your home. Furniture items removed may incur a disposal fee.</span></td>
      </tr>
      <tr>
        <td style="align: left; font-size: 9pt; "><strong>Ask Designer for additional information. </strong><span
            style="background-color: #ffff00;">______________Initial</span></td>
      </tr>
      <tr>
        <td style="align: center; font-size: 9pt; "></td>
      </tr>
      <tr>
        <td style="align: left; font-size: 9pt; "><span style="background-color: #ffff00;">NOEL FURNITURE WILL ASSUME NO
            LIABILITY FOR DAMAGES INCURRED WHEN ADDITIONAL FURNITURE ITEMS ARE MOVED AT THE REQUEST OF CUSTOMER.
            ______________Initial</span> </td>
      </tr>
      <tr>
        <td style="align: center; font-size: 9pt; "></td>
      </tr>
      <tr>
        <td style="align: left; font-size: 9pt; "><strong>CUSTOMER
            SIGNATURE</strong>__________________________________________________________________ <strong>DATE</strong>
          _______________________________ </td>
      </tr>
      <tr>
        <td style="align: left; font-size: 9pt; ">ALL MERCHANDISE RECEIVED IN GOOD CONDITION, EXCEPT AS NOTED BELOW:
        </td>
      </tr>
      <tr>
        <td style="border-bottom: solid; height: 13pt;"></td>
      </tr>
      <tr>
        <td style="border-bottom: solid; height: 13pt;"></td>
      </tr>
      <tr>
        <td style="border-bottom: solid; height: 13pt;"></td>
      </tr>
    </table>
    <div>
      <table
        style="position: absolute; overflow: hidden; left: 55pt; top: -660pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${record.shipdate?string.full}</td>
        </tr>
      </table>
      <table
        style="position: absolute; overflow: hidden; left: 65pt; top: -615pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${record.salesrep}</td>
        </tr>
      </table>

      <table
        style="position: absolute; overflow: hidden; left: 85pt; top: -597pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${JSON.sales_rep_number}</td>
        </tr>
      </table>


      <table
        style="position: absolute; overflow: hidden; left: 115pt; top: -580pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${JSON.so_inv_number}</td>
        </tr>
      </table>

      <table
        style="position: absolute; overflow: hidden; left: 110pt; top: -508pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${record.entity}</td>
        </tr>
      </table>

      <table
        style="position: absolute; overflow: hidden; left: 110pt; top: -490pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${JSON.cust_phone}</td>
        </tr>
      </table>

      <table
        style="position: absolute; overflow: hidden; left: 345pt; top: -490pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${JSON.cust_mobile}</td>
        </tr>
      </table>

      <table
        style="position: absolute; overflow: hidden; left: 110pt; top: -474pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${JSON.cust_ship_addr_1}</td>
        </tr>
      </table>

      <table
        style="position: absolute; overflow: hidden; left: 70pt; top: -456pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${JSON.cust_ship_city}</td>
        </tr>
      </table>

      <table
        style="position: absolute; overflow: hidden; left: 250pt; top: -456pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${JSON.cust_ship_state}</td>
        </tr>
      </table>

      <table
        style="position: absolute; overflow: hidden; left: 415pt; top: -456pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${JSON.cust_ship_zip}</td>
        </tr>
      </table>


      <table
        style="position: absolute; overflow: hidden; left: 180pt; top: -331pt; height: 18pt; width: 250pt; font-size: 9pt; ">
        <tr>
          <td>${record.custbody_noel_invoice_deliveryspecial}</td>
        </tr>
        <tr>
          <td>${record.custbody_noel_deliverydetails}</td>
        </tr>
      </table>

    </div>
  </body>
</pdf>