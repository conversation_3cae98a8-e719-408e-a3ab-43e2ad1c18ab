/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/file'], function(record, file) {

    function beforeSubmit(context) {
        
        var oldRecObj = context.oldRecord;
        var newRecObj = context.newRecord;

        if(context.type == context.UserEventType.CREATE ) {

            var newFile = newRecObj.getValue({ fieldId: 'custitem_test_image' });

            if(newFile) {
                var fileId = newRecObj.getValue({ fieldId: 'custitem_test_image' });
                var fileName = file.load({ id: fileId }).name;
                newRecObj.setValue({ fieldId: 'custitem_test_image_2', value: fileName });
            }

        } else if (context.type == context.UserEventType.EDIT) {

            var oldFile = oldRecObj.getValue({ fieldId: 'custitem_test_image' });
            var newFile = newRecObj.getValue({ fieldId: 'custitem_test_image' });

            if(oldFile != newFile) {
                var fileName = newRecObj.getText({ fieldId: 'custitem_test_image' });
                newRecObj.setValue({ fieldId: 'custitem_test_image_2', value: fileName });
            }
        }
    }

    return {
        beforeSubmit: beforeSubmit
    }
});
