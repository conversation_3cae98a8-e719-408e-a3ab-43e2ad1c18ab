<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
	<link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
	<#if .locale == "zh_CN">
		<link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
	<#elseif .locale == "zh_TW">
		<link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
	<#elseif .locale == "ja_JP">
		<link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
	<#elseif .locale == "ko_KR">
		<link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
	<#elseif .locale == "th_TH">
		<link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
	</#if>
    <macrolist>
        <macro id="nlheader">
            <table cellpadding="0" cellspacing="0" class="header" style="height:100px;width:100%;"><tr>
	<td rowspan="3"><#if companyInformation.logoUrl?length != 0><img src="${companyInformation.logoUrl}" style="float: left; margin: 0px 7px; width: 250px; height: 110px;" /> </#if><br />&nbsp;</td>
	<td align="right"><span style="font-family:arial,helvetica,sans-serif;"><span class="title">${record@title}</span></span></td>
	</tr>
	<tr>
	<td align="right"><span class="number">#<span style="font-family:arial,helvetica,sans-serif;">${record.tranid}</span></span></td>
	</tr>
	<tr>
	<td align="right"><span style="font-family:arial,helvetica,sans-serif;">${record.trandate}</span></td>
	</tr></table>
        </macro>
        <macro id="nlfooter">
            <table class="footer" style="width: 100%;"><tr>
	<td><barcode codetype="code128" showtext="true" value="${record.tranid}"/></td>
	<td align="right">of <totalpages/></td>
	</tr></table>
        </macro>
    </macrolist>
    <style type="text/css">* {
		<#if .locale == "zh_CN">
			font-family: NotoSans, NotoSansCJKsc, sans-serif;
		<#elseif .locale == "zh_TW">
			font-family: NotoSans, NotoSansCJKtc, sans-serif;
		<#elseif .locale == "ja_JP">
			font-family: NotoSans, NotoSansCJKjp, sans-serif;
		<#elseif .locale == "ko_KR">
			font-family: NotoSans, NotoSansCJKkr, sans-serif;
		<#elseif .locale == "th_TH">
			font-family: NotoSans, NotoSansThai, sans-serif;
		<#else>
			font-family: NotoSans, sans-serif;
		</#if>
		}
		table {
			font-size: 9pt;
			table-layout: fixed;
		}
        th {
            font-weight: bold;
            font-size: 8pt;
            vertical-align: middle;
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
            color: #333333;
        }
        td {
            padding: 4px 6px;
        }
		td p { align:left }
        b {
            font-weight: bold;
            color: #333333;
        }
        table.header td {
            padding: 0px;
            font-size: 10pt;
        }
        table.footer td {
            padding: 0px;
            font-size: 8pt;
        }
        table.itemtable th {
            padding-bottom: 10px;
            padding-top: 10px;
        }
        table.body td {
            padding-top: 2px;
        }
        table.total {
            page-break-inside: avoid;
        }
        tr.totalrow {
            background-color: #e3e3e3;
            line-height: 200%;
        }
        td.totalboxtop {
            font-size: 12pt;
            background-color: #e3e3e3;
        }
        td.addressheader {
            font-size: 8pt;
            padding-top: 6px;
            padding-bottom: 2px;
        }
        td.address {
            padding-top: 0px;
        }
        td.totalboxmid {
            font-size: 28pt;
            padding-top: 20px;
            background-color: #e3e3e3;
        }
        td.totalboxbot {
            background-color: #e3e3e3;
            font-weight: bold;
        }
        span.title {
            font-size: 28pt;
        }
        span.number {
            font-size: 16pt;
        }
        span.itemname {
            font-weight: bold;
            line-height: 150%;
        }
        hr {
            width: 100%;
            color: #d3d3d3;
            background-color: #d3d3d3;
            height: 1px;
        }
</style>
</head>
<body header="nlheader" header-height="10%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
    <table style="font:arial;margin-top:10px;width:100%;"><tr>
	<td class="addressheader" colspan="4">
	<p><span style="font-family:arial,helvetica,sans-serif;"><strong style="font-family: arial, helvetica, sans-serif; font-size: 10.6667px; background-color: rgb(255, 255, 255);">Remit To</strong></span></p>
	</td>
	<td class="addressheader" colspan="3">
	<p><font face="arial, helvetica, sans-serif"><b>Bill To</b></font></p>
	</td>
	<td class="addressheader" colspan="3" rowspan="1"><strong style="font-size: 10.6667px; background-color: rgb(255, 255, 255);">Ship To</strong></td>
	<td class="totalboxtop" colspan="5"><span style="font-family:arial,helvetica,sans-serif;"><b>${record.total@label?upper_case}</b></span></td>
	</tr>
	<tr>
	<td class="address" colspan="4" rowspan="2">
	<p><span style="font-size:12px;"><span style="font-family:arial,helvetica,sans-serif;">${companyInformation.mainaddress_text}</span></span></p>
	</td>
	<td class="address" colspan="3" rowspan="2">
	<p><span style="font-size:12px;"><span style="font-family:arial,helvetica,sans-serif;">${record.billaddress}</span></span></p>
	</td>
	<td class="address" colspan="3" rowspan="2"><span style="font-size:12px;"><span style="font-family:arial,helvetica,sans-serif;">${record.shipaddress}</span></span></td>
	<td align="right" class="totalboxmid" colspan="5"><span style="font-family:arial,helvetica,sans-serif;">${record.total}</span></td>
	</tr></table>

<table align="left" class="body" style="margin-top:10px;width:100%;"><tr>
	<th>${record.otherrefnum@label}</th>
	<th><span style="color: rgb(51, 51, 51); font-family: arial, helvetica, sans-serif; font-size: 10.6667px; font-weight: 700; text-align: center; background-color: rgb(227, 227, 227);">Project</span></th>
	<th colspan="2">Memo</th>
	<th>${record.duedate@label}</th>
	</tr>
	<tr>
	<td>${record.otherrefnum}</td>
	<td>${record.project}</td>
	<td colspan="2">${record.memo}</td>
	<td>${record.duedate}</td>
	</tr></table>
<#if record.item?has_content>

<table class="itemtable" style="width: 100%; margin-top: 10px;"><!-- start items --><#list record.item as item><#if item_index==0>
<thead>
	<tr>
	<th colspan="12">${item.item@label}</th>
	<th colspan="3">${item.quantity@label}</th>
	<th align="right" colspan="4">${item.rate@label}</th>
	<th align="right" colspan="4"><span style="font-family:arial,helvetica,sans-serif;">${item.amount@label}</span></th>
	<th align="right" colspan="4"><span style="font-family:arial,helvetica,sans-serif;">${item.taxrate1@label}</span></th>
	<th align="right" colspan="4"><span style="font-family:arial,helvetica,sans-serif;">Tax Amount</span></th>
	</tr>
</thead>
</#if><tr>
    <#assign taxamount = item.amount * (item.taxrate1/100)>
	<td colspan="12"><span style="font-family:arial,helvetica,sans-serif;"><span class="itemname">${item.item}</span></span><span style="font-family:arial,helvetica,sans-serif;">&nbsp;-&nbsp;${item.description}</span></td>
	<td colspan="3" style="text-align: center;">${item.quantity}</td>
	<td align="right" colspan="4"><span style="font-family:arial,helvetica,sans-serif;">${item.rate}</span></td>
	<td align="right" colspan="4"><span style="font-family:arial,helvetica,sans-serif;">${item.amount}</span></td>
	<td align="right" colspan="4"><span style="font-family:arial,helvetica,sans-serif;">${item.taxrate1}</span></td>
    <#setting number_format="0.##">
	<td align="right" colspan="4"><span style="font-family:arial,helvetica,sans-serif;">${taxamount}</span></td>
	</tr>
	</#list><!-- end items --></table>

<hr /></#if>
<table class="total" style="width: 100%; margin-top: 10px;"><tr>
	<td colspan="4">&nbsp;</td>
	<td align="right"><b>${record.subtotal@label}</b></td>
	<td align="right">${record.subtotal}</td>
	</tr>
	<tr>
	<td colspan="4">&nbsp;</td>
	<td align="right"><b>Tax</b></td>
	<td align="right">${record.taxtotal}</td>
	</tr>
	<tr class="totalrow">
	<td background-color="#ffffff" colspan="4">&nbsp;</td>
	<td align="right"><b>${record.total@label}</b></td>
	<td align="right">${record.total}</td>
	</tr>
	<tr>
	<td background-color="#ffffff" colspan="4">&nbsp;</td>
	<td align="right">&nbsp;</td>
	<td align="right">&nbsp;</td>
	</tr>
	<tr>
	<td background-color="#ffffff" colspan="4">&nbsp;</td>
	<td align="right"><strong>Balance Due</strong></td>
	<td align="right">${record.amountremaining}</td>
	</tr></table>
</body>
</pdf>