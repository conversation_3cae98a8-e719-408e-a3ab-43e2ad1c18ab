
/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response){


    if(request.getMethod() == 'GET'){
    
        if(request.getParameter('customer')){
            var defaultCustomer = request.getParameter('customer');
        } else {
            defaultCustomer = nlapiGetContext().getSetting('SCRIPT', 'custscript4');
            nlapiLogExecution('DEBUG', 'TEST', defaultCustomer);
        }
        var defaultItem = nlapiGetContext().getSetting('SCRIPT', 'custscript5');
        var form = nlapiCreateForm('May 25 Activity', true);

        // customer
        form.addField('custpage_customer', 'select', 'Customer', '-2').setMandatory(true).setDefaultValue(defaultCustomer);

        // item sublist
        var sublistObj = form.addSubList('custpage_item_sublist', 'inlineeditor', 'Item Sublist');
        var itemFieldObj = addInventoryItem(sublistObj);
        itemFieldObj.setMandatory(true).setDefaultValue(defaultItem);
        sublistObj.addField('custpage_quantity', 'integer', 'Quantity').setDefaultValue('1');
        
        form.addSubmitButton('Submit');

        response.writePage(form);

    } else {
        // PROCESS HERE
        var itemCount = request.getLineItemCount('custpage_item_sublist');
        var negativeInteger = -1;
        if(itemCount == -1){
            response.write('You found a bug!');
            return false;
        }
    
        var soObj = nlapiCreateRecord('salesorder');
        var customerSelected = request.getParameter('custpage_customer');
        soObj.setFieldValue('entity', customerSelected);
        for(var i = 0; i < itemCount; i++){
            nlapiLogExecution('DEBUG', 'execution', i+1);
            var selectedItem = request.getLineItemValue('custpage_item_sublist','custpage_item',i+1);
            var selectedQuantity = request.getLineItemValue('custpage_item_sublist','custpage_quantity',i+1);  
            soObj.selectNewLineItem('item');
            soObj.setCurrentLineItemValue('item','item', selectedItem);
            soObj.setCurrentLineItemValue('item', 'quantity', selectedQuantity);
            soObj.commitLineItem('item');
        }
        var soId = nlapiSubmitRecord(soObj, customerSelected);
        sendEmail(soId, customerSelected);
        var form = nlapiCreateForm('Sales Order Created! (Created ID: ' + soId + ')', true);
        form.setScript('customscriptclient_script_go_to_record');
        form.addButton('custpage_goto_record', 'Go to Record', 'redirectToRecord(' + soId + ')');
        
        response.writePage(form);

    }

}

function sendEmail(soId, entId){
    var body = 'Sales Order Created! (Created ID: ' + soId + ')';
    var subject = 'Sales order for created';
    var sender = nlapiGetUser();
    var recipient = entId;
    var record = [];
    record['transaction'] = soId;
    
    nlapiSendEmail(sender,recipient,subject,body,null,null,record);
}

function addInventoryItem(sublistObj){
    var fieldObj = sublistObj.addField('custpage_item', 'select', 'Item');
    fieldObj.addSelectOption('', '', true);
    var inventoryItem = nlapiLoadSearch('item', 9);
    var sResult = inventoryItem.runSearch();
    sResult.forEachResult(function(result){
        var itemId = result.getId();
        var itemName = result.getValue('itemid');
        fieldObj.addSelectOption(itemId, itemName);
        return true;
    });
    
    return fieldObj;
}
