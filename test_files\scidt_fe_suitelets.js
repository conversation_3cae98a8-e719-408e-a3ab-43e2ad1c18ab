/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       23 May 2020     jdgonzal
 *
 */

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response){

    if(request.getMethod() == 'GET'){

        var defCustomer = request.getParameter('customer');

        var form = nlapiCreateForm("My Fake Sales Order Creator", true);
        // CUSTOMER
        form.addField('custpage_customer', 'select', 'Customer', '-2').setMandatory(true).setDefaultValue(defCustomer);
        
        // ITEM SUBLIST
        var sublistObj =  form.addSubList('custpage_itemsublist', 'inlineeditor', 'Item Sublist');
        sublistObj.addField('custpage_item', 'select', 'Item', '-10');
        sublistObj.addField('custpage_quantity', 'integer', 'Quantity');
        
        
        form.addSubmitButton('Submit');
        form.addButton('custpage_button', 'Custom Button');

        response.writePage(form);

    } else {
        // PROCESS HERE
        var customerSelected = request.getParameter('custpage_customer');
        nlapiLogExecution('DEBUG', 'Selected Customer', customerSelected);
        var selectedItem = request.getLineItemValue('custpage_itemsublist','custpage_item',1);
        nlapiLogExecution('DEBUG', 'Selected Item', selectedItem);
        var selectedQuantity = request.getLineItemValue('custpage_itemsublist','custpage_quantity',1);
        nlapiLogExecution('DEBUG', 'Selected Quantity', selectedQuantity);
        

        var soObj = nlapiCreateRecord('salesorder');
        soObj.setFieldValue('entity', customerSelected);
        soObj.setLineItemValue('item','item',1,selectedItem);
        soObj.setLineItemValue('item','quantity',1,selectedQuantity);
        var soId = nlapiSubmitRecord(soObj);

        response.write("<h1>"+soId+"</h1>");
    }

}
