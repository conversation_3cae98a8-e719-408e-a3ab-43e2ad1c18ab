/**
 *@NApiVersion 2.1
 *@NScriptType Suitelet
 */
define(['N/record', 'N/query'], function(record, query) {

    function checkIfCustomCheckNo(account, checkNo) {

        
        var acctRecord = record.load({
            type: record.Type.ACCOUNT,
            id: account,
            isDynamic: false
        });
        
        var currentCheckNo = acctRecord.getValue({ fieldId: 'curdocnum' });

        if(checkNo == Number(currentCheckNo)) {
            return false;
        }

        return true;

        // return String(currentCheckNo).replace(".0", "");

		// var sqlQueryData = "SELECT MAX(pymt.number) FROM transaction pymt RIGHT JOIN transactionaccountingline acct ON pymt.id = acct.transaction AND acct.account = ? WHERE type = 'VendPymt'";
		
		// var resultSet = query.runSuiteQL({
		// 	query: sqlQueryData,
        //     params: [account]
		// });
        
        // var results = resultSet.results;
        // if(checkNo == Number(results[0].values[0] + 1)) {
        //     return false;
        // }

        // return true;
    }

    function onRequest(context) {
        
        if(context.request.method == 'GET'){

            
            var paymentIds = context.request.parameters.payment_ids;
            var accountId = context.request.parameters.account_id;
            var tranTypes = context.request.parameters.tran_types;
            var checkNo = Number(context.request.parameters.check_no);
            paymentIds = paymentIds.split(',');
            tranTypes = tranTypes.split(',');

            var customCheckNo = checkIfCustomCheckNo(accountId, checkNo);

            for(var i = 0; i < paymentIds.length; i++) {

                var recObj = record.load({
                    type: tranTypes[i],
                    id: paymentIds[i],
                    isDynamic: true
                });

                recObj.setValue({ fieldId: 'tobeprinted', value: false });

                if(customCheckNo){
                    recObj.setValue({ fieldId: 'tranid', value: String(checkNo).replace('.0', '') });
                    checkNo++;
                }
                
                recObj.save();

            }

        }
    }

    return {
        onRequest: onRequest
    }
});
