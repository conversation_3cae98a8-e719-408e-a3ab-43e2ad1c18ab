/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
 define(['N/search', 'N/email', 'N/record', 'N/render'], function(search, email, record, render) {


    function sendToSingleEmail(values, emailAddress, billingMethod, custObj) {

        var transactionFiles = [];
        var largestDay = 0;
        
        // loop through invoices
        for(var i = 0; i < values.length; i++){
            var parsedValue = JSON.parse(values[i]);
            
            // get the largest day to be used to determine which email template to use
            if(largestDay < parsedValue.age){
                largestDay = parsedValue.age;
            }
            
            // render pdf files
            var transactionFile = render.transaction({
                entityId: parseInt(parsedValue.internalid),
                printMode: render.PrintMode.PDF,
                inCustLocale: true
            });
            transactionFiles.push(transactionFile);
        }

        // get the template depending on the oldest invoice and billing method
        var templateId = getTemplateDetails(largestDay, billingMethod);

        // invoke email template to be used
        var mergeResult = render.mergeEmail({
            templateId: templateId,
            entity: custObj,
            recipient: null,
            supportCaseId: null,
            transactionId: parseInt(parsedValue.internalid),
            customRecord: null
        });

        // send email
        if(email){
            email.send({
                author: 26304,
                recipients: emailAddress,
                subject: mergeResult.subject,
                body: mergeResult.body,
                attachments: transactionFiles,
                relatedRecords: {
                    entityId: custObj.id
                }
            });
        } else {
            log.debug('No Recepient', "Customer ID: " + custObj.id + " has no email based on the scenario chosen");
        }

    }

    function getTemplateDetails(largestDay, billMethod){

        // AM COLLECTIONS: 1st request (Individual) -- id: 54
        // AM COLLECTIONS: 1st Request (Company) -- id: 236
        // AM COLLECTIONS: 2nd Request -- id: 57
        // AM COLLECTIONS: Final Request -- id: 59

        if(largestDay >= 90){ 
            return 59; // AM COLLECTIONS: Final Request
        }

        if(billMethod == 2 && (largestDay <= 89)){ 
            return 236; // AM COLLECTIONS: 1st Request (Company)
        }

        if(largestDay >= 30 && largestDay <= 59) {
            return 54; // AM COLLECTIONS: 1st request (Individual)
        } else if(largestDay >= 60 && largestDay <= 89) {
            return 57; // AM COLLECTIONS: 2nd Request
        }

        return templateId;

    }

    function getInputData() {
        var mySearch = search.load({
            id: 'customsearch_acs_invoice_age'
        });

        return mySearch;
    }

    function map(context) {
        // accept data from previous stage
        var keyFromInput = context.key;
        var valueFromInput = context.value;
        
        var recordObj = JSON.parse(valueFromInput);

        var values = {
            internalid: recordObj.id,
            age: recordObj.values.duedate,
            email: recordObj.values.formulatext
        };

        context.write({
            key: recordObj.values.entity.value,
            value: values
        });
    }

    function reduce(context) {
        try {
        
            var reduceKey = context.key;
            var reduceValues = context.values;

            var custObj = record.load({
                type: record.Type.CUSTOMER,
                id: reduceKey
            });

            var billingMethod = custObj.getValue({ fieldId: 'custentity_ammethod' });

            if(billingMethod == 1){

                var emailAmContact = custObj.getValue({ fieldId: 'custentity_electronic_billing' });

                if(emailAmContact){

                    // get the email
                    var emailAddress = JSON.parse(reduceValues[0]).email;
                    sendToSingleEmail(reduceValues, emailAddress, billingMethod, custObj);

                } else {
                    // loop through invoices and email to each contact
                    for(var i = 0; i < reduceValues.length; i++){
                        var parsedValue = JSON.parse(reduceValues[i]);
                        
                        emailAddress = parsedValue.email;
                        
                        // get the template depending on the age of the invoice
                        var templateId = getTemplateDetails(parsedValue.age, billingMethod);
                        
                        // render pdf files
                        var transactionFile = render.transaction({
                            entityId: parseInt(parsedValue.internalid),
                            printMode: render.PrintMode.PDF,
                            inCustLocale: true
                        });

                        // invoke email template to be used
                        var mergeResult = render.mergeEmail({
                            templateId: templateId,
                            entity: custObj,
                            recipient: null,
                            supportCaseId: null,
                            transactionId: parseInt(parsedValue.internalid),
                            customRecord: null
                        });
                        
                        // send email
                        try {
                            email.send({
                                author: 26304,
                                recipients: emailAddress,
                                subject: mergeResult.subject,
                                body: mergeResult.body,
                                attachments: [transactionFile],
                                relatedRecords: {
                                    entityId: custObj.id
                                }
                            });
                            log.debug('Success', "Successfully sent email to email: " + emailAddress)
                        } catch (errObj) {
                            log.debug('Error', errObj);
                        }
                    }

                }

            } else {

                // get the email
                var emailAddress = JSON.parse(reduceValues[0]).email;

                sendToSingleEmail(reduceValues, emailAddress, billingMethod, custObj);

            }

        } catch (errorObj) {
            log.debug({
                title: 'reduce 1',
                details: errorObj
            });
        }
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
