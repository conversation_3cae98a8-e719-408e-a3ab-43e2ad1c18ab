<#-- format specific processing -->
<#function getVoidCheckIndicator payment>
<#assign value = "O">
<#assign reversalDate = payment.reversaldate>
<#if reversalDate?has_content>
<#assign value = "V">
</#if>
<#return value>
</#function>
<#function getChequeNumber payment>
<#assign value = "">
<#if payment.recordtype == "cashrefund">
<#assign value = payment.otherrefnum>
<#else>
<#assign value = payment.tranid>
</#if>
<#return value>
</#function>
<#-- template building -->
#OUTPUT START#
Bank Account,Check Number,Amount,Date,Vendor Name
<#list payments as payment>
<#assign entity = entities[payment_index]>
${setPadding(cbank.custpage_pp_custrecord_2663_acct_num,"left","0",12)},${setPadding(getChequeNumber(payment),"left","0",10)},${setPadding(formatAmount(getAmount(payment)),"left","0",12)},${setLength(payment.trandate?string("MMddyy"),6)},${setLength(buildEntityName(entity,true),50)}
</#list>
#OUTPUT END#