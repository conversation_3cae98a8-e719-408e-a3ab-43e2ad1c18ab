/**
 * @NApiVersion 2.x
 * @NScriptType Suitelet
 */
 define(['N/ui/serverWidget', 'N/runtime', 'N/crypto', 'N/encode', 'N/sftp', 'N/file'], function(ui, runtime, crypto, encode, sftp, file) {
    function onRequest(option) {
        var connection = sftp.createConnection({
            username: 'NetSuite_svc',
            keyId: 'custkey_theroi_test_key',
            url: 'ftps.theroi.com',
            port: Number(22),
            hostKey: 'AAAAB3NzaC1yc2EAAAADAQABAAACAQC2Tftx5KMephIFfFr3443/4Yv8BqygdzI+4GSIAhG0bZki12Y8a8slGSwdUTxemMVqxGBo/OkrJSN7YLkksl5zFVxJvv+auClP+L7GcnoNj5pGLB9Qzy6zJVNvD9Jre26lVRIxpSyEALGTdEDbNcmYSmDRBHkAfRZaxmOcsfljlnyQeWXUcFIPQAVGBFOOO+tMOOHcOHi+zRZ+b42ZKL9i3NhEGY30vkxeUD8Ki61sfsCVOzzKt/eq2MPcdJAYZGxPTOXTlPWQxxSLBSK5FM4lwMPI8O7BPwMpFMhjyUAsFHXP3JRlL6wnikdAEkokvpL+DTJI0CdzlZbcG+4/faOPtIA/oTRydfURl8tei5/FroGmVGcb5PsdF/4FyE1Zxlwwp/VHUjuwlQAH/D8OxYcJ39/ZYxAVk16dx1W4Ie5s/3BhJJf8jylErdqa9L024sRIGbIKWIfE6WYoUBO3Lb3IVTC/7bjjpX6Lu911p/lPSnuR+gfgfWVbfjLJZC9sEeVD+53LXVUS3lwrT5WbCw3jK4FowNIqXyNjByY8m96QDQsOn62UdXYJq8h35aL3PIaDkZYcLgEe9pWxd54vDtkh4bvxG2akniqw49AnLhzfnTKPXjsP1I8LfFF1xSmnyqjU+ykiiPWrRv1xq65Eo3kVVYWl5eEQR1yND1fk2q2HfQ=='
        });

        log.debug("test", connection.list({
            path: '/'
        }));

        
        // var sftUrl = "ftp.theroi.com";
        
        
        // var connection = sftp.createConnection({
        //     username : '<EMAIL>',
        //     //passwordGuid : digestField,
        //     keyId: "custkey_theroi_test_key",
        //     url : sftUrl,
        //     port : 22,
        //     //directory : "/",
        //     hostKey: "AAAAB3NzaC1yc2EAAAADAQABAAACAQC2Tftx5KMephIFfFr3443/4Yv8BqygdzI+4GSIAhG0bZki12Y8a8slGSwdUTxemMVqxGBo/OkrJSN7YLkksl5zFVxJvv+auClP+L7GcnoNj5pGLB9Qzy6zJVNvD9Jre26lVRIxpSyEALGTdEDbNcmYSmDRBHkAfRZaxmOcsfljlnyQeWXUcFIPQAVGBFOOO+tMOOHcOHi+zRZ+b42ZKL9i3NhEGY30vkxeUD8Ki61sfsCVOzzKt/eq2MPcdJAYZGxPTOXTlPWQxxSLBSK5FM4lwMPI8O7BPwMpFMhjyUAsFHXP3JRlL6wnikdAEkokvpL+DTJI0CdzlZbcG+4/faOPtIA/oTRydfURl8tei5/FroGmVGcb5PsdF/4FyE1Zxlwwp/VHUjuwlQAH/D8OxYcJ39/ZYxAVk16dx1W4Ie5s/3BhJJf8jylErdqa9L024sRIGbIKWIfE6WYoUBO3Lb3IVTC/7bjjpX6Lu911p/lPSnuR+gfgfWVbfjLJZC9sEeVD+53LXVUS3lwrT5WbCw3jK4FowNIqXyNjByY8m96QDQsOn62UdXYJq8h35aL3PIaDkZYcLgEe9pWxd54vDtkh4bvxG2akniqw49AnLhzfnTKPXjsP1I8LfFF1xSmnyqjU+ykiiPWrRv1xq65Eo3kVVYWl5eEQR1yND1fk2q2HfQ=="
        // });
        // connection.upload({
        //     directory : '/',
        //     filename : fil.name,
        //     file : fil,
        //     replaceExisting : true
        // });
    }
    return {
        onRequest: onRequest
    };
});