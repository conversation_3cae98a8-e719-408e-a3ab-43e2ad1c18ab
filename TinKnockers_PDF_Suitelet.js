/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/render', 'N/record'], function(https, render, record) {

    function onRequest(context) {
        if(context.request.method == https.Method.GET){
            requestParams = context.request.parameters;
            workOrderId = parseInt(requestParams.custpage_WOId);
            // var test = render.transaction({
            //     entityId: parseInt(workOrderId),
            //     printMode: render.PrintMode.PDF
            // });
            
            var renderer = render.create();
            renderer.setTemplateByScriptId({
                scriptId: "CUSTTMPL_104_T2243445_496"
            });
            var myContent = renderer.addRecord({
                templateName: 'record',
                record: record.load({
                    type: record.Type.TRANSACTION,
                    id: workOrderId
                })
            });
            var packingSlipXML = renderer.renderAsString();
            var packingSlipPDF = render.xmlToPdf({
                xmlString: packingSlipXML
            });
            
            context.response.writeFile({
                file: packingSlipPDF,
                isInline: true
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
