/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define([], function() {

    function pageInit(context) {
        
    }

    function fieldChanged(context) {
        var checked = context.currentRecord.getValue({ fieldId: 'custbody_record_changed' });
    
        if(!checked) {
            var currentRecord = context.currentRecord;
            currentRecord.setValue({ fieldId: 'custbody_record_changed', value: true });
        }
    }

    return {
        pageInit: pageInit,
        fieldChanged: fieldChanged,
    }
});
