/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search'], function(record, search) {

    function afterSubmit(context) {
        var recordId = context.newRecord.id;
        var recordObj = record.load({
            type: record.Type.TRANSFER_ORDER,
            id: recordId,
            isDynamic: true
        });
        var orderStatus = recordObj.getText({ fieldId: 'orderstatus' })

        if(orderStatus == 'Pending Fulfillment'){
            var itemFulfillmentObj = record.transform({
                fromType: record.Type.TRANSFER_ORDER,
                fromId: recordId,
                toType: record.Type.ITEM_FULFILLMENT,
                isDynamic: true 
            });
            itemFulfillmentObj.setValue({ fieldId:'shipstatus', value: 'C' });
            itemFulfillmentObj.save({ enableSourcing: true, ignoreMandatoryFields: true });
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
