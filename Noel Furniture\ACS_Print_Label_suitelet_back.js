/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/render', 'N/record', 'N/format'], function(https, render, record, format) {

    function onRequest(context) {
        if(context.request.method == https.Method.GET){
            requestParams = context.request.parameters;
            var purchaseOrderID = parseInt(requestParams.custpage_POId);

            // CREATE TEMPLATE RENDERER OBJECT
            var renderer = render.create();

            // SELECT TEMPLATE TO BE USED VIA SCRIPT ID
            renderer.setTemplateByScriptId({
                scriptId: "CUSTTMPL_ACS_CUSTOM_RCV_LBL"
            });

            // ADD THE RECORD TO BE USED BASED ON PARAMETERS GIVEN
            var recObj = record.load({
                type: record.Type.PURCHASE_ORDER,
                id: purchaseOrderID,
                isDynamic: true
            });

            renderer.addRecord({
                templateName: 'record',
                record: recObj
            });
            
            renderer.addCustomDataSource({
                format: render.DataSource.OBJECT,
                alias: "date",
                data: { datenow: format.format({ value: new Date(), type: format.Type.DATE })}
            });

            var lineCount = recObj.getLineCount({ sublistId: "item" });
            var height = (lineCount * 4) + 1;

            renderer.addCustomDataSource({
                format: render.DataSource.OBJECT,
                alias: "height",
                data: { pageheight: height }
            });

            // RENDER AS STRING
            var packingSlipXML = renderer.renderAsString();

            // SEND RESPONSE XML TO RENDER AS PDF
            context.response.renderPdf({
                xmlString: packingSlipXML
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
