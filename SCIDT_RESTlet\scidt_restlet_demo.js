/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       27 May 2020     jdgonzal
 *
 */

/**
 * @param {Object} dataIn Parameter object
 * @returns {Object} Output object
 */

 //1317
 //1318

function runSearch() {

    var filterArr = [
        new nlobjSearchFilter('type', null, 'anyof', 'SalesOrd'),
        new nlobjSearchFilter('mainline', null, 'is', true),
        new nlobjSearchFilter('datecreated', null, 'within', '2020-03-01', '2020-05-31')
    ];

    var columnArr = [
        new nlobjSearchColumn('tranid'),
        new nlobjSearchColumn('trandate'),
        new nlobjSearchColumn('entityid', 'customer'),
        new nlobjSearchColumn('amount')
    ];

    var searchObj = nlapiCreateSearch('salesorder', filterArr, columnArr);
    var sResult = searchObj.runSearch();
    var firstThirty = sResult.getResults(0, 30);
    var formattedResult = {};
    for(var i = 0; i < firstThirty.length; i++){
        var tranid = firstThirty[i].getValue('tranid');
        formattedResult["so_no_" + tranid] = {
            "SO Number": tranid,
            "Date Created": firstThirty[i].getValue('trandate'),
            "Customer Name": firstThirty[i].getValue('entityid', 'customer'),
            "Amount": firstThirty[i].getValue('amount')
        };
    }

    return formattedResult;
}

function sendEmail(id, body, subject, hasAttachment, connRecordId){

    if(hasAttachment && connRecordId){
        var attachment = createPDF(connRecordId);
        var records = new Object();
        records['transaction'] = connRecordId;
        return nlapiSendEmail(2, id, subject, body, null, null, records, attachment);
    }
    
    return nlapiSendEmail(2, id, subject, body, null, null);
    
}

function createPDF(connRecordId){
    var pdfObj = nlapiPrintRecord('TRANSACTION', connRecordId, 'DEFAULT');
    return pdfObj;
}

function getRESTlet(dataIn) {

    var resultObj = {};

    if(dataIn.empId && dataIn.itemId) {
        return resultObj = {
            'result': 'failed',
            'message': 'script does not know how to handle this request'
        }
    }

    if(dataIn.itemId) {

        var itemObj = nlapiLoadRecord('inventoryitem', dataIn.itemId);

        resultObj = {
            'itemId': dataIn.itemId,
            'Item Name': itemObj.getFieldValue('itemid'),
            'Costing Method': itemObj.getFieldValue('costingmethod'),
            'Total Value': itemObj.getFieldValue('totalvalue'),
            'Total Quantity on all Locations': itemObj.getFieldValue('totalquantityonhand')            
        };

    } else if (dataIn.empId) {

        var empObj = nlapiLoadRecord('employee', dataIn.empId);

        resultObj = {
            'empId': dataIn.empId,
            'First name': empObj.getFieldValue('firstname'),
            'Last name': empObj.getFieldValue('lastname'),
            'Email': empObj.getFieldValue('email'),
            'Supervisor': empObj.getFieldText('supervisor')
        };

    } else {

        var searchResult = runSearch();

        resultObj = {
            'SO Result': searchResult
        };
    }

    var arr = ["a", "b"]
    return arr;
    
	return resultObj;

}

/**
 * @param {Object} dataIn Parameter object
 * @returns {Object} Output object
 */
function postRESTlet(dataIn) {
    if(dataIn.empId) {
        if(!dataIn.hasOwnProperty('note')){
            return "No note has been given!";
        }

        var empObj = nlapiLoadRecord('employee', dataIn.empId);
        var entityid = empObj.getFieldValue('entityid');
        
        // get current comment value
        var prevCommentValue = empObj.getFieldValue('comments');

        // set new comment value
        empObj.setFieldValue('comments', dataIn.note);
        
        // commit changes
        nlapiSubmitRecord(empObj);

        // send email
        var subject = entityid + " has been updated";
        var body = "Greetings!\n\n";
        body = body + "Employee " + entityid + " has been updated\n\n";
        body = body + "Old Note Value: " + prevCommentValue + "\n";
        body = body + "New Note Value: " + dataIn.note + "\n\n";
        body = body + "This is an automated message, please do not reply.";
        sendEmail(dataIn.empId, body, subject, false, '');

        return "Successfully updated " + entityid;

    } else if (dataIn.soId){
        
        var soObj = nlapiLoadRecord('salesorder', dataIn.soId);
        var customerId = soObj.getFieldValue('entity')
        var itemFulfillmentObj = nlapiTransformRecord('salesorder', dataIn.soId, 'itemfulfillment');
        var fulfillmentId = nlapiSubmitRecord(itemFulfillmentObj, true);

        // send email
        var subject = "Sales Order with Internal ID " + dataIn.soId + " has been transformed to Item Fulfillment";
        var body = "Greetings!\n\n";
        body = body + "Sales Order with Internal ID " + dataIn.soId + " has been transformed to Item Fulfillment with the ID test" + fulfillmentId +"\n";
        body = body + "Please see attached for the Item Fulfillment PDF\n\n";
        body = body + "This is an automated message, please do not reply.";
        sendEmail(customerId, body, subject, true, fulfillmentId);

        return "Successfully transformed SO to Item Fulfillment (" + fulfillmentId + ")";

    } else {
        return "Parameters given is not recognized by system";
    }
}

/**
 * @param {Object} dataIn Parameter object
 * @returns {Void} 
 */
function deleteRESTlet(dataIn) {
	
}

/**
 * @param {Object} dataIn Parameter object
 * @returns {Object} Output object 
 */
function putRESTlet(dataIn) {
	
	return {};
}
