<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="1"><img
                                src="https://6499846.app.netsuite.com/core/media/media.nl?id=13&amp;c=6499846&amp;h=ea89f5503f2f62bcc710"
                                style="width: 138px; height: 75px;" /></td>
                        <td align="right"><img
                                src="https://6499846.app.netsuite.com/core/media/media.nl?id=12&amp;c=6499846&amp;h=83777b3f3b56216878d6"
                                style="width: 150px; height: 75px;" /></td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div style="text-align: center;">
                                <hr />
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">&nbsp;</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td style="height: 29px;">
                        </td>
                        <td align="right" style="height: 29px;">www. mooringtech.com - (877) 532-8088&nbsp; &nbsp;
                            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                padding-top: 15px;
                font-size: 16pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="7.5%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        &nbsp;
        <table class="header" style="width: 100%;">
            <tr>
                <td rowspan="3"><span class="nameandaddress">${companyInformation.addressText}</span></td>
                <td align="right"><span class="title">${record@title}</span></td>
            </tr>
            <tr>
                <td align="right"><span class="number">#${record.tranid}</span></td>
            </tr>
            <tr>
                <td align="right">${record.trandate}</td>
            </tr>
        </table>

        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader" width="30%"><b>${record.billaddress@label}</b></td>
                <td class="addressheader" width="30%"><b>${record.shipaddress@label}</b></td>
                <td align="right" colspan="2"></td>
            </tr>
            <tr>
                <td class="address">${record.billingaddress_text}</td>
                <td class="address">${record.shippingaddress_text}</td>
                <td class="totalboxtop"><b>${record.total@label?upper_case}</b></td>
                <td align="right" class="totalboxtop"><b>${record.total}</b></td>
            </tr>
        </table>

        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th>${record.salesrep@label}</th>
                <th>${record.shipmethod@label}</th>
            </tr>
            <tr>
                <td>${user.firstname}&nbsp;${user.lastname}</td>
                <td rowspan="3">${record.shipmethod}</td>
            </tr>
            <tr>
                <td>${user.phone}</td>
            </tr>
            <tr>
                <td>${user.email}</td>
            </tr>
        </table>
        <br /><span style="font-size:12px;"><strong>Please be advised</strong> - Panasonic TOUGHBOK laptops, tablets,
            handhelds, accessories and vehicle mounting solutions can often carry unknown lead-times of <strong>30 to 60
                days or longer</strong>. ETA&#39;s can change or take time to initially obtain. Part numbers or SKUs
            frequently evolve or are modified abruptly. It is common for accessories to be on backorder and to ship
            separately at a future date. Please communicate all the time-related and urgent needs with your sales rep.
            <strong>Sales tax will be applicable when shipping within the state of GA to an end-user that is not
                tax-exempt.</strong></span><br />
        <#if record.item?has_content>
            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th colspan="12" style="width: 422px;">${item.item@label}</th>
                                <th colspan="1" style="width: 32px;">${item.istaxable@label}</th>
                                <th style="width: 65px;">${item.quantity@label}</th>
                                <th align="right" colspan="4" style="width: 75px;">Price</th>
                                <th align="right" colspan="4" style="width: 134px;">Extended Price</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td colspan="12" style="width: 422px;"><span
                                class="itemname">${item.item}</span><br />${item.description}</td>
                        <td colspan="1" style="width: 32px;">${item.istaxable}</td>
                        <td style="width: 65px; text-align: center;">${item.quantity}</td>
                        <td align="right" colspan="4" style="width: 75px;">${item.rate}</td>
                        <td align="right" colspan="4" style="width: 134px;">${item.amount}</td>
                    </tr>
                </#list><!-- end items -->
            </table>

            <hr />
        </#if>
        <table width="100%">
            <tr>
                <td width="40%">
                    <table class="total" style="width: 100%; margin-top: 10px;">
                        <tr>
                            <td><#if record.memo != "">Comments:</#if></td>
                        </tr>
                        <tr>
                            <td><#if record.memo != "">${record.custbody_acs_comments}</#if></td>
                        </tr>
                    </table>
                </td>
                <td width="60%">
                    <table class="total" style="width: 100%; margin-top: 10px;">
                        <tr>
                            <td colspan="2">&nbsp;</td>
                            <td align="right"><b>${record.subtotal@label}</b></td>
                            <td align="right">${record.subtotal}</td>
                        </tr>
                        <tr>
                            <td colspan="2">&nbsp;</td>
                            <td align="right"><b>${record.shippingcost@label}</b></td>
                            <td align="right">${record.shippingcost}</td>
                        </tr>
                        <tr>
                            <td colspan="2">&nbsp;</td>
                            <td align="right"><b>${record.taxtotal@label} (${record.taxrate}%)</b></td>
                            <td align="right">${record.taxtotal}</td>
                        </tr>
                        <tr class="totalrow">
                            <td background-color="#ffffff" colspan="2">&nbsp;</td>
                            <td align="right"><b>${record.total@label}</b></td>
                            <td align="right">${record.total}</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
</pdf>