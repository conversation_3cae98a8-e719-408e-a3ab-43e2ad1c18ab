/**
 * Sales Order Script
 * 
 * Version    Date            Author           Remarks
 * 1.00       15 Nov 2016     Marcel P		   Initial Version
 *
 */

/**
 * When creating a Sales Order through In8Sync, assigns some additional fields
 * 
 * @appliedtorecord Sales Order
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      
 * @returns {Void}
 */
function beforeSubmit(type){
 
	try {
		if (type == 'create' || type == 'edit') {
			
			// Only order from WooCommerce
			if (nlapiGetFieldValue('custbody_in8_wc_order_id')) {
				
				var cust = nlapiLookupField('customer', nlapiGetFieldValue('entity'), ['custentitycustentity_ccc_isretail', 'custentity_ccc_woo_retail', 'custentity_customer_location']);
				
				if (cust) {
					if (cust.custentitycustentity_ccc_isretail == 'T' || cust.custentity_ccc_woo_retail == 'T') {
						nlapiLogExecution('DEBUG', 'Order ' + nlapiGetFieldValue('custbody_in8_wc_order_id') + ' is retail.');
						nlapiSetFieldValue('custbody_is_retail', 'T');
						nlapiSetFieldValue('custbody_is_wholesale', 'F');						
					} else {
						nlapiLogExecution('DEBUG', 'Order ' + nlapiGetFieldValue('custbody_in8_wc_order_id') + ' is not retail.');
						nlapiSetFieldValue('custbody_is_retail', 'F');
						nlapiSetFieldValue('custbody_is_wholesale', 'T');
					}
					
					nlapiSetFieldValue('location', cust.custentity_customer_location);
				}					
			}			
		}		
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error on Sales Order ', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
	}
}
