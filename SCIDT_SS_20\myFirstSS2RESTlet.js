/**
 *@NApiVersion 2.x
 *@NScriptType Restlet
 */
define(['N/search', 'N/record'], function(search, record) {

    function _get(context) {

        var filterArr = [
            search.createFilter({ name: 'supervisor', operator: search.Operator.ANYOF, values: '@NONE@' })
        ];

        var columnArr = [
            search.createColumn({ name: 'entityid' }),
            search.createColumn({ name: 'altname' }),
            search.createColumn({ name: 'email' }),
            search.createColumn({ name: 'lastmodifieddate' }),
        ]

        var searchObj = search.create({
            type: search.Type.EMPLOYEE,
            filters: filterArr,
            columns: columnArr
        });

        var searchResult = searchObj.run().getRange({ start: 0, end: 6 });

        var returnarr = [];
        for(var i = 0; i < searchResult.length; i++){
            var entity = searchResult[i].getValue({ name: 'altname' });
            var email = searchResult[i].getValue({ name: 'email' });
            var lastmoddate = searchResult[i].getValue({ name: 'lastmodifieddate' });

            returnarr.push({
                'Employee': entity,
                'Email': email,
                'Last Modified Date': lastmoddate
            });
        }

        return returnarr;
    }

    function _post(context) {

        var empRec = record.load({
            type: record.Type.EMPLOYEE,
            id: context.empid,
            isDynamic: true
        });
    
        empRec.selectNewLine({ sublistId: 'addressbook' });
        var addressSubRec = empRec.getCurrentSublistSubrecord({
            sublistId: 'addressbook',
            fieldId: 'addressbookaddress'
        });
    
        addressSubRec.setValue({
            fieldId: 'country',
            value: context.country
        });
    
        addressSubRec.setValue({
            fieldId: 'city',
            value: context.city
        });

        addressSubRec.setValue({
            fieldId: 'state',
            value: context.state
        });
    
        addressSubRec.setValue({
            fieldId: 'zipcode',
            value: context.zipcode
        });

        empRec.setCurrentSublistValue({
            sublistId: 'addressbook',
            fieldId: 'defaultshipping',
            value: true
        });
    
        empRec.commitLine({ sublistId: 'addressbook' });

        empRec.save();

        return { 'result': 'nice' };

    }

    function _put(context) {
        
    }

    function _delete(context) {
        
    }

    return {
        get: _get,
        post: _post,
        put: _put,
        delete: _delete
    }
});
