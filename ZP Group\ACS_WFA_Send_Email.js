/**
 *@NApiVersion 2.x
 *@NScriptType WorkflowActionScript
 */
define(['N/email', 'N/search', 'N/runtime', 'N/render'], function(email, search, runtime, render) {


    function getEmailContent(tranId, templateId) {

        var mergeResult = render.mergeEmail({
            templateId: templateId,
            entity: null,
            recipient: null,
            supportCaseId: null,
            transactionId: parseInt(tranId),
            customRecord: null
        });

        return {
            subject: mergeResult.subject,
            body: mergeResult.body
        }

    }

    function onAction(scriptContext) {
        
        var recObj = scriptContext.newRecord;
        var roleId = runtime.getCurrentScript().getParameter('custscript_role');
        var templateId = runtime.getCurrentScript().getParameter('custscript_email_template_id');
        var author = runtime.getCurrentScript().getParameter('custscript_email_author');

        var emailContents = getEmailContent(recObj.id, templateId);

        var employeeSearchObj = search.create({
            type: "employee",
            filters:
            [
               ["role","anyof",roleId]
            ],
            columns:
            [
               search.createColumn({name: "email", label: "Email"})
            ]
         });
         var searchResultCount = employeeSearchObj.runPaged().count;
         if(searchResultCount) {
            employeeSearchObj.run().each(function(result){
                var emailAddress = result.getValue({ name: 'email' });

                email.send({
                    author: author,
                    recipients: emailAddress,
                    subject: emailContents.subject,
                    body: emailContents.body,
                    attachments: [],
                    relatedRecords: {
                        transactionId: recObj.id
                    }
                });

                return true;
            });

            
        }

    }

    return {
        onAction: onAction
    }
});
