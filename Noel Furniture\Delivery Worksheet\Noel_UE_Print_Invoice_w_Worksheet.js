/**
 *@NApiVersion 2.1
 *@NScriptType UserEventScript
 */
define([], function() {

    function beforeLoad(context) {
        if(context.type == context.UserEventType.VIEW){
            var getForm = context.form;
            getForm.clientScriptModulePath = 'SuiteScripts/Noel_CS_Print_Invoice_w_Worksheet_redirect.js';
            getForm.addButton({
                id: 'custpage_print_inv_work',
                label: 'Print Invoice w/ Delivery Worksheet',
                functionName: 'redirectToPDF()'
            });
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
