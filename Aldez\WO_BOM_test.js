/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define([], function() {

    function beforeLoad(context) {
        if(context.type == context.Type.PRINT) {
            var recObj = context.newRecord;

            var expectedShipDate = recObj.getValue({ fieldId: 'startdate' });
            var expectedShipDate = recObj.setValue({ fieldId: 'custbody_acs_start_date', value: expectedShipDate });

            var startDate = recForm.getField({ fieldId: 'custbody_acs_start_date' });
            startDate.defaultValue
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
