/**
 * Vend Integration - Sales Order User Event
 *
 * User Event deployed to the Sales Order and automates the Cash Sale creation
 *
 * Version    Date            Author           Remarks
 * 1.00       23 Jul 2018     Marcel <PERSON> (In8)   Initial Version
 *
 */

function beforeSubmit(type) {

	var vendId = nlapiGetFieldValue('custbody_in8_vend_id');

	if (type == 'create' && vendId) {
		try {
			nlapiLogExecution('DEBUG', 'In8', 'Vend transaction - Before submit');

			setAddresses();			
		} catch(e) {
			nlapiLogExecution('ERROR', 'Error setting address. Vend Id: ' + vendId, ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
		}
	}
}

function setAddresses() {
    var subrecord;
    
    nlapiRemoveSubrecord('shippingaddress');
    nlapiSetFieldValue('shipaddresslist', '');

    subrecord = nlapiCreateSubrecord('shippingaddress');

    subrecord.setFieldValue('zip', '84104');
    subrecord.setFieldValue('addr1', '74 Main St');
    subrecord.setFieldValue('addr2', '');
    subrecord.setFieldValue('city', 'Salt Lake City');
    subrecord.setFieldValue('state', 'UT');

    subrecord.commit();
}