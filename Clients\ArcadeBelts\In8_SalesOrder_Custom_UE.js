/**
* Sales Order Custom Script
*
* Version		Date			Author				Remarks
* 1.0			06 Mar 2019		<PERSON> (In8Sync)	Initial Version
*
*/

/**
* @appliedtorecord recordtype
* 
* @param {String} type Operation types: create, edit, delete, xedit
*
* @returns {Void}
*/
function beforeSubmit(type) {

    try {
        if (type == 'create' || type == 'edit') {
            if (nlapiGetFieldValue('custbody_in8_wc_order_id')) {
                
                if (Number(nlapiGetFieldValue('total')) == 0 && !nlapiGetFieldValue('paymentmethod')) {
                    nlapiSetFieldValue('terms', 18);
                }
            }
        }
    } catch(e) {
        nlapiLogExecution('ERROR', 'Error', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
    }
}
