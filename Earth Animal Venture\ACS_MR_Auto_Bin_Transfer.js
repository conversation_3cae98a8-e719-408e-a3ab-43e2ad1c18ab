/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/runtime', 'N/url'], function(search, record, runtime, url) {

    function getInputData() {
        var searchObj = search.load({
            id: 'customsearch_acs_custom_quality_search'
        });

        return searchObj;
    }

    function map(context) {
        try {
            var stLogTitle = 'Map';
            var valueFromInput = context.value;

            var scriptObj = runtime.getCurrentScript();
            // get the bin transfer destination from the script parameters
            var binTransfDestination = scriptObj.getParameter('custscript_acs_bin_transf_dest');

            // get the location for the bin transfer
            var binTransfLocation = scriptObj.getParameter('custscript_acs_bin_transf_loc');

            var recordObj = JSON.parse(valueFromInput);
            var queueId = recordObj.values.internalid;
            var itemId = recordObj.values.custrecord_qm_queue_item.value;
            var lotnumber = recordObj.values["serialnumber.CUSTRECORD_QM_QUEUE_TRANSACTION_INV"];
            var quantity = recordObj.values["binnumberquantity.CUSTRECORD_QM_QUEUE_TRANSACTION_INV"];

            // get the inventory number id
            var lotNumberID = getInventoryNumberId(lotnumber, itemId, binTransfLocation);

            var binTransfObj = record.create({
                type: record.Type.BIN_TRANSFER,
                isDynamic: true
            });

            // set the location
            binTransfObj.setValue({
                fieldId: 'location',
                value: binTransfLocation
            });

            // set the memo
            binTransfObj.setValue({
                fieldId: 'memo',
                value: 'From ' + recordObj.values.custrecord_qm_queue_transaction_inv.text.replace('Assembly Build ', '')
            });

            // set the inventory fields
            binTransfObj.selectNewLine({ sublistId: 'inventory' });
            binTransfObj.setCurrentSublistValue({
                sublistId: 'inventory',
                fieldId: 'item',
                value: itemId
            });
            binTransfObj.setCurrentSublistValue({
                sublistId: 'inventory',
                fieldId: 'quantity',
                value: quantity
            });

            // create the inventory detail
            var inventoryDetail = binTransfObj.getCurrentSublistSubrecord({
                sublistId: 'inventory',
                fieldId: 'inventorydetail'
            });
            inventoryDetail.selectNewLine({
                sublistId: 'inventoryassignment'
            });
            inventoryDetail.setCurrentSublistValue({
                sublistId: 'inventoryassignment',
                fieldId: 'issueinventorynumber',
                value: lotNumberID
            });
            inventoryDetail.setCurrentSublistValue({
                sublistId: 'inventoryassignment',
                fieldId: 'tobinnumber',
                value: binTransfDestination
            });
            inventoryDetail.setCurrentSublistValue({
                sublistId: 'inventoryassignment',
                fieldId: 'quantity',
                value: quantity
            });
            inventoryDetail.commitLine({
                sublistId: 'inventoryassignment'
            });

            binTransfObj.commitLine({
                sublistId: 'inventory'
            });
            var binTransfId = binTransfObj.save();

            log.debug(stLogTitle, "Successfully created Bin Transfer ID: " + binTransfId);

            var outputUrl = url.resolveRecord({
                recordType: record.Type.BIN_TRANSFER,
                recordId: binTransfId,
                isEditMode: false
            });

            record.submitFields({
                type: record.Type.ASSEMBLY_BUILD,  
                id: recordObj.values.custrecord_qm_queue_transaction_inv.value,
                values: {
                    custbody_auto_bin_transfer_link: outputUrl
                },
                options: {
                    enableSourcing: false,
                    ignoreMandatoryFields : true
                }
            });

            record.submitFields({
                type: 'customrecord_qm_queue',  
                id: queueId.value,
                values: {
                    custrecord_acs_bin_transferred: true
                },
                options: {
                    enableSourcing: false,
                    ignoreMandatoryFields : true
                }
            });
        } catch (err) {
            log.debug(stLogTitle + " - Error", err);
        }

    }

    function getInventoryNumberId(lotnumber, item, binTransfLocation){
        try {
            var inventoryNumberId;
            var inventorynumberSearchObj = search.create({
                type: "inventorynumber",
                filters:
                [
                    ["item","anyof",item], 
                    "AND", 
                    ["location","anyof",binTransfLocation], 
                    "AND", 
                    ["inventorynumber","startswith",lotnumber]
                ],
                columns:
                [
                    search.createColumn({name: "internalid", label: "Internal ID"})
                ]
            });
            inventorynumberSearchObj.run().each(function(result){
                inventoryNumberId = result.getValue({ name: 'internalid' });
                return false;
            });

            return inventoryNumberId;
        } catch (err) {
            log.debug('fn: getInventoryNumberId', err);
        }
    }

    function reduce(context) {
        
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        // reduce: reduce,
        summarize: summarize
    }
});
