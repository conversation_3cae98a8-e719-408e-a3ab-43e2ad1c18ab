/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/record', 'N/search'], function(record, search) {

    function onRequest(context) {

        var recId = context.request.parameters.recid;

        var returnStr = "<#assign credit = " + JSON.stringify(x) + " />"; 

        context.response.write({
            output: returnStr
        });
    }

    return {
        onRequest: onRequest
    }
});
