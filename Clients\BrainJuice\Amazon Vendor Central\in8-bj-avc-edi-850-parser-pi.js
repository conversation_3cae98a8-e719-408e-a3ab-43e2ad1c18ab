/**
 * @NApiVersion 2.x
 * @NModuleScope Public
 * @NScriptType plugintypeimpl
*/

define([
    'N/search',
    'N/runtime',
    'SuiteBundles/Bundle 360774/com.in8sync2.amazonvendorcentral/src/common/gateway/in8-avc-item',
    'SuiteBundles/Bundle 360774/com.in8sync2.amazonvendorcentral/src/common/gateway/in8-avc-ship-address',
    'SuiteBundles/Bundle 360774/com.in8sync2.amazonvendorcentral/src/common/gateway/in8-avc-edi-settings',
    'SuiteBundles/Bundle 360774/com.in8sync2.amazonvendorcentral/src/common/gateway/in8-avc-record',
    'SuiteBundles/Bundle 360774/com.in8sync2.amazonvendorcentral/src/common/util/in8-avc-file-handler',
    'SuiteBundles/Bundle 360774/com.in8sync2.amazonvendorcentral/src/common/util/in8-avc-date-util'
  ], function(search, runtime, itemMod, shipAddressMod, settingsMod, allRecordMod, fileHandler, dateUtil) {
    const Status = {
      PENDING_APPROVAL: 'A'
    };
    function getItemIdMap(productIds, fieldId) {
      var itemFieldId = fieldId || 'itemid';
      var prodIdString = productIds.map(function(prodId) { return "'" + prodId + "'";}).join(',');
      return search.create({
        type: search.Type.ITEM,
        filters: [
          search.createFilter({ name: 'formulanumeric', formula: "CASE WHEN {" + itemFieldId + "} IN (" + prodIdString + ") THEN 1 ELSE 0 END", operator: search.Operator.EQUALTO, values: '1' })
        ],
        columns: [ itemFieldId ]
      })
      .run()
      .getRange({ start: 0, end: 1000 })
      .reduce(function(res, result) {
        var productId = result.getValue({ name: fieldId });
        res[productId] = result.id;
        return res;
      }, {});
    }
    function updateItemIds(items, fieldId) {
      var itemProductIds = items.map(function(prod) {
        return prod.item;
      });
      var itemIds = getItemIdMap(itemProductIds, fieldId);
      items.forEach(function(itemObj) {
        var productId = itemObj.item;
        itemObj.item = productId ? itemIds[productId] : '';
      });
      return items;
    }
    function execute(options) {
      log.debug({ title: 'Parser options', details: options });
      const fileContent = options.fileContent;
      var items = [];
      var salesOrder = {
        recordType: options.recordType
      };
      var segments = fileHandler.parseContent(fileContent);
      log.debug({ title: 'EDI 850 segments', details: segments });

      segments.forEach(function(segment) {
        const headerId = segment[0];
        const values = segment;
        if (headerId == 'ISA') {
          const senderCode = values[6].trim();
          const entity = settingsMod.getEntityWithRetailerCode(senderCode, options.fileType);
          if (!entity) throw 'ENTITY_NOT_FOUND';
          salesOrder.entity = entity;
          if (options.form) {
            salesOrder.customform = options.form;
          }
          if (options.class) {
            salesOrder.class = options.class;
          }
          return;
        }
        if (headerId == 'BEG') {
          const otherrefnum = values[3];
          salesOrder.otherrefnum = otherrefnum;
          const internalId = allRecordMod.findByPONumber(options.recordType, otherrefnum);
          if (internalId) {
            throw 'Sales Order with PO number already exists.'
          }
          salesOrder.trandate = dateUtil.parseDateFromStringYYYYMMDD(values[5]);
          return;
        }
        if (headerId == 'REF') {
          var refQualifier = values[1];
          if (refQualifier == 'CR') {
            salesOrder.custbody_in8_avc_customer_ref_num = values[2];
          }
          if (refQualifier == 'PD') {
            salesOrder.custbody_in8_avc_promo_deal_num = values[2];
          }
          return;
        }
        if (headerId == 'FOB') {
          salesOrder.custbody_in8_avc_ship_payment_method = values[1];
          salesOrder.custbody_in8_avc_transpo_terms_code = values[5];
          return;
        }
        if (headerId == 'CSH') {
          salesOrder.custbody_in8_avc_sales_req_code = values[1];
          return;
        }
        if (headerId == 'DTM') {
          var refQualifier = values[1];
          if (refQualifier == '063') {
            var latestShipDate = dateUtil.parseDateFromStringYYYYMMDD(values[2]);
            salesOrder.custbody_in8_avc_latest_ship_date = latestShipDate;
          }
          if (refQualifier == '064') {
            var earliestShipDate = dateUtil.parseDateFromStringYYYYMMDD(values[2]);
            salesOrder.custbody_in8_avc_earliest_ship_date = earliestShipDate;
          }
          return;
        }
        if (headerId == 'PKG') {
          salesOrder.custbody_in8_avc_container_size = values[5];
        }
        if (headerId == 'N1') {
          if (values[3] == '15' /** Standard Address Number */) {
            var amazonSAN = values[4];
            var shipAddress = shipAddressMod.getAddress(amazonSAN);
            salesOrder.custbody_in8_avc_ship_to_code = shipAddress.id;
            salesOrder.addresses = {
              shipaddresslist: {
                country: shipAddress.country,
                addressee: shipAddress.amazonCode,
                //attention: shipAddress.addressee,
                address1: shipAddress.addr1,
                city: shipAddress.city,
                state: shipAddress.state,
                zip: shipAddress.zip,
                override: false
              }
            };
          }
          return;
        }
        if (headerId == 'PO1') {
          items.push({
            // item: itemMod.getItemId(values[7], options.itemFieldId),
            item: values[7],
            quantity: Number(values[2]),
            price: '-1',
            rate: Number(values[4]),
            custcol_in8_edi_trnx_line: values[1],
            custcol_in8_avc_uom_code: values[3],
            custcol_in8_avc_unit_price_code: values[5],
            custcol_in8_avc_product_id_qualifier: values[6],
            custcol_in8_avc_product_id: values[7]
          });
          return;
        }
      });
      salesOrder.orderstatus = Status.PENDING_APPROVAL;
      salesOrder.custbody_in8_edi_job = options.jobId;
      log.debug({ title: 'Remaining1', details: runtime.getCurrentScript().getRemainingUsage() });
      salesOrder.item = updateItemIds(items, options.itemFieldId);
      log.debug({ title: 'Remaining1', details: runtime.getCurrentScript().getRemainingUsage() });
      log.debug({ title: 'Sales Order from EDI 850', details: salesOrder });
      return salesOrder;
    }
    return {
      execute: execute
    };
  });