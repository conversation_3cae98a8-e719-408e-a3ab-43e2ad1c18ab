/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
 define(['N/ui/dialog', 'N/search'], function(dialog, search) {

    function pageInit(context) {
        
    }

    function getDefaultBins(itemId, locId) {

        try {
            var defaultBins = [];

            var binNumberSearch = search.load({
                id: 'customsearch_acs_inventory_detail_search'
            });

            var itemIdFilter = search.createFilter({
                name: 'item',
                operator: search.Operator.ANYOF,
                values: [itemId]
            });
            var locIdFilter = search.createFilter({
                name: 'location',
                operator: search.Operator.ANYOF,
                values: [locId]
            });
            binNumberSearch.filters.push(itemIdFilter);
            binNumberSearch.filters.push(locIdFilter);

            var resultSet = binNumberSearch.run();

            resultSet.each(function(result){
                var binnumber = result.getText({ name: 'binnumber', summary: 'GROUP' });
                var itemcount = result.getValue({ name: 'itemcount', summary: 'SUM' });
        
                if(itemcount > 0){
                    defaultBins.push(binnumber);
                }
        
                return true
            });

            return defaultBins;
        } catch (e) {
            return [];
        }
    }

    function validateField(context) {
        if(context.fieldId == 'item') {
            var quoteObj = context.currentRecord;
            var location = quoteObj.getValue({ fieldId: 'location' });

            if(location == "") {
                dialog.alert({
                    title: "Notice",
                    message: "Please select Location first before adding items."
                });

                return false;
            }

            return true;
        } 

        return true;
    }

    function postSourcing(context) {
        if(context.fieldId == 'item') {

            var quoteObj = context.currentRecord;
            var itemId = quoteObj.getCurrentSublistValue({
                sublistId: context.sublistId,
                fieldId: context.fieldId
            });

            var tranLocation = quoteObj.getValue({ fieldId: 'location' });

            defaultBins = getDefaultBins(itemId, tranLocation);

            if(defaultBins.length){
                dialog.alert({
                    title: "Default BINS has stocks!",
                    message: "This item has stocks in default bins: <br><ul><b><li>" + defaultBins.join("</li><li>") + "</li></b></ul>"
                });
            }
        }
    }

    return {
        pageInit: pageInit,
        validateField: validateField,
        postSourcing: postSourcing
    }
});