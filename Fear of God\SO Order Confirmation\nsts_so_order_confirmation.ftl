<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="3"><img src="${companyInformation.pagelogo?html}" style="float: left; margin: 7px; width: 18%; height: 21%" /></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr></tr>
                    <tr>
                        <td style="font-size: 16px; font-weight: bold" align="center">Order Confirmation</td>
                        <td></td>
                    </tr>
                    <tr></tr>
                </table>
                <table style="padding-bottom: 5px; width: 100%;">
                    <tr>
                        <td style="font-size: 8pt; font-weight: bold" align="left">FROM</td>
                        <td style="font-size: 8pt; font-weight: bold" align="left">SHIP TO</td>
                        <td style="font-size: 8pt; font-weight: bold" align="left">BILL TO</td>
                        <td style="font-size: 8pt; font-weight: bold" align="left"></td>
                    </tr>
                    <tr>
                        <td style="font-size: 8pt; border: 1px solid black">${companyInformation.mainaddress_text}</td>
                        <td style="font-size: 8pt; border: 1px solid black">${record.shipaddress}</td>
                        <td style="font-size: 8pt; border: 1px solid black">${record.billaddress}</td>
                        <td style="font-size: 9pt">
                            <table style="width: 100%">
                                <tr>
                                    <td style="font-weight: bold; line-height: 50%; font-size: 8pt; border-left: 1px solid black; border-right: 1px solid black; border-top: 1px solid black"
                                        align="center">ORDER #</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 8pt; line-height: 50%; border-left: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black"
                                        align="center">${record.tranid}</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 8pt; line-height: 50%; border-left: 1px solid black; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold"
                                        align="center">Customer PO #</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 8pt; line-height: 50%; border-left: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black"
                                        align="center">${record.otherrefnum}</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 8pt; line-height: 50%; border-left: 1px solid black; border-right: 1px solid black; border-top: 1px solid black; font-weight: bold"
                                        align="center">DIVISION</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 8pt; line-height: 50%; border-left: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black"
                                        align="center">${record.csegfog_division}</td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                <table style="padding-bottom: 5px; width: 100%;">
                    <thead>
                        <tr>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="3">ORDER DATE</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="4">START SHIP DATE</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="2">DEPT #</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="7">SALES REP</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="3">ORDER TYPE</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="6">CUSTOMER #</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="2">SEASON</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="3">CONTACT</th>
                        </tr>
                    </thead>
                    <tr>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="3">${record.trandate}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="4">
                            ${record.custbodyfog_start_ship_date}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="2">
                            ${record.custbodyfog_department_number}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="7">${record.salesrep}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="3">${record.custbodyfog_order_type}
                        </td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="6">${record.entity}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="2">
                            ${record.custbodyfog_season_header.custrecord_ns_seasoncode}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="3">${record.custbodyfog_contact}
                        </td>
                    </tr>
                </table>
                <table style="padding-bottom: 5px; width: 100%;">
                    <thead>
                        <tr>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="3">CANCEL DATE</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="4">CURRENCY</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="2">STORE</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="4">PAYMENT TERMS</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="3">WAREHOUSE</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="3">FOB POINT</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="6">SHIP VIA</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="2">FACTOR</th>
                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="center"
                                colspan="3">GROUP</th>
                        </tr>
                    </thead>
                    <tr>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="3">
                            ${record.custbodyfog_cancel_date}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="4">${record.currency}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="2">${record.custbodfog_store}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="4">${record.terms}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="3">${record.location}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="3">${location.country}
                        </td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="6">${record.shipmethod}</td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="2">${record.custbodyfog_factor}
                        </td>
                        <td style="font-size: 8pt; border: 1px solid black" colspan="3">
                            ${record.custbodyfog_group_header}</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 8pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 8pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="40%" footer="nlfooter" footer-height="0.2in" padding="0.2in 0.2in 0.2in 0.2in"
        size="Letter-Landscape">
        <#if JSON.missingsizes !=''>
            <table style="padding-bottom: 15px; width: 100%; margin-left: -15px; margin-right: -15px;">
                <tr>
                    <td style="background-color: #ff0000; font-size: 10pt; font-weight: bold; height: 20px;"
                        align="center" colspan="3">These sizes are missing from the product size rank list:
                        ${JSON.missingsizes}</td>
                </tr>
            </table>
        </#if>
        <#if record.custbodyfog_special_instructions?has_content>
            <table style="padding-bottom: 5px; width: 100%;">
                <tr>
                    <td style="font-size: 8pt; font-weight: bold; border: 1px solid black" align="left">SPECIAL
                        INSTRUCTIONS</td>
                </tr>
                <tr>
                    <td style="font-size: 8pt; border: 1px solid black" align="left">
                        ${record.custbodyfog_special_instructions}</td>
                </tr>
            </table>
        </#if>
        <#assign totalUnits=0>
            <#assign totalAmount=0>

                <#list JSON.division as division>
                    <#assign counter=0>
                        <#assign counter1=0>
                            <table style="padding-bottom: 15px; width: 100%;">
                                <#list division.sizes as sizes>
                                    <#assign counter=counter + 1>
                                </#list>
                                <#assign counter1='"' + counter + '"'>
                                    <thead>
                                    <tr>
                                        <th style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 40px"
                                            align="left" rowspan="2">SELL PRD</th>
                                        <th style="font-size: 7pt; font-weight: bold; border: 1px solid black; width: 60px"
                                            align="left" rowspan="2">STYLE</th>
                                        <th style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 50px"
                                            align="left" rowspan="2">FABRIC</th>
                                        <th style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 65px"
                                            align="left" rowspan="2">CONTENT</th>
                                        <th style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 50px"
                                            align="left" rowspan="2">COLOR</th>
                                        <th style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 50px"
                                            align="left" rowspan="2">COLOR NAME</th>
                                        <#if counter !=0>
                                            <th style="font-size: 8pt; font-weight: bold; border: 1px solid black"
                                                align="center" rowspan="1" colspan=${counter1}>SIZES &amp; UNITS</th>
                                            <#else>
                                                <th style="font-size: 8pt; font-weight: bold; border: 1px solid black"
                                                    align="center" rowspan="1">SIZES &amp; UNITS</th>
                                        </#if>
                                        <th style="font-size: 7pt; font-weight: bold; border: 1px solid black; width: 45px"
                                            align="center" rowspan="2">UNITS</th>
                                        <th style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 55px"
                                            align="center" rowspan="2">PRICE</th>
                                        <th style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 60px"
                                            align="center" rowspan="2">MSRP</th>
                                        <th style="font-size: 8pt; font-weight: bold; border: 1px solid black; width: 60px"
                                            align="center" rowspan="2">TOTAL</th>
                                    </tr>
                                    <tr>
                                        <#if counter !=0>
                                            <#list division.sizes as sizes>
                                                <th style="font-size: 8pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                                                    align="center" rowspan="1">${sizes}</th>
                                            </#list>
                                            <#else>
                                                <th style="font-size: 8pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                                                    align="center" rowspan="1"></th>
                                        </#if>
                                    </tr>
                                    </thead>

                                    <#assign total=0>
                                        <#list division.item as item>
                                            <#assign totalQty=0>
                                                <#if item.quantity !="0">
                                                    <#assign totalQty=item.quantity?number>
                                                </#if>
                                                <#assign composition = item.composition />
                                                <#if item.composition?length gt 200>
                                                    <#assign composition = item.composition?substring(0,197) + "..." />
                                                </#if>

                                                <tr>
                                                    <td style="font-size: 7pt; border: 1px solid black; width: 40px"
                                                        align="left">${item.sellPrd}</td>
                                                    <td style="font-size: 6pt; border: 1px solid black; width: 75px; white-space: nowrap;"
                                                        align="left">${item.name}</td>
                                                    <td style="font-size: 7pt; border: 1px solid black; width: 50px;"
                                                        align="left">${item.fabric}</td>
                                                    <td style="font-size: 7pt; border: 1px solid black; width: 65px"
                                                        align="left">${composition}</td>
                                                    <td style="font-size: 7pt; border: 1px solid black; width: 50px"
                                                        align="left">${item.color}</td>
                                                    <td style="font-size: 7pt; border: 1px solid black; width: 50px"
                                                        align="left">${item.colordes}</td>
                                                    <#if counter !=0>
                                                        <#list item.allsizes as sizes>
                                                            <td style="font-size: 7pt; border: 1px solid black"
                                                                align="center">${sizes.quantity}</td>
                                                            <#assign totalQty=totalQty + sizes.quantity?number>
                                                        </#list>
                                                        <#else>
                                                            <td style="font-size:7pt; border: 1px solid black"
                                                                align="center"></td>
                                                    </#if>
                                                    <#assign totalAmt=item.rate?number * totalQty>
                                                        <#assign totalUnits=totalUnits + totalQty?number>
                                                            <#assign totalAmount=totalAmount + totalAmt?number>
                                                                <td style="font-size: 7pt; border: 1px solid black; width: 40px"
                                                                    align="center">${totalQty}</td>
                                                                <td style="font-size: 6pt; border: 1px solid black; width: 55px"
                                                                    align="center">
                                                                    $${(item.rate?number)?string(",##0.00")}</td>
                                                                <td style="font-size: 7pt; border: 1px solid black; width: 60px"
                                                                    align="center">$${item.msrp}</td>
                                                                <td style="font-size: 7pt; border: 1px solid black; width: 60px"
                                                                    align="center">$${totalAmt?string(",##0.00")}</td>
                                                </tr>
                                        </#list>
                            </table>
                </#list>
                <table style="padding-bottom: 15px; width: 100%; page-break-inside: avoid">
                    <tr>
                        <td style="font-size: 9pt" align="left" colspan="16"></td>
                        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                            align="left" colspan="3">Total Units:</td>
                        <td style="font-size: 8pt; border: 1px solid black" align="right" colspan="4" rowspan="1">
                            ${totalUnits}</td>
                    </tr>
                    <tr>
                        <td style="font-size: 9pt" align="left" colspan="16"></td>
                        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                            align="left" colspan="3">Subtotal:</td>
                        <td style="font-size: 8pt; border: 1px solid black" align="right" colspan="4" rowspan="1">
                            ${record.subtotal}</td>
                    </tr>
                    <tr>
                        <td style="font-size: 9pt" align="left" colspan="16"></td>
                        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                            align="left" colspan="3">Discount Total:</td>
                        <td style="font-size: 8pt; border: 1px solid black" align="right" colspan="4" rowspan="1">
                            ${record.discounttotal}</td>
                    </tr>
                    <tr>
                        <td style="font-size: 9pt" align="left" colspan="16"></td>
                        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                            align="left" colspan="3">Tax Total:</td>
                        <td style="font-size: 8pt; border: 1px solid black" align="right" colspan="4" rowspan="1">
                            ${record.taxtotal}</td>
                    </tr>
                    <tr>
                        <td style="font-size: 9pt" align="left" colspan="16"></td>
                        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                            align="left" colspan="3">Shipping Cost:</td>
                        <td style="font-size: 8pt; border: 1px solid black" align="right" colspan="4" rowspan="1">
                            ${record.altshippingcost}</td>
                    </tr>
                    <tr>
                        <td style="font-size: 9pt" align="left" colspan="16"></td>
                        <td style="font-size: 8pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                            align="left" colspan="3">Total Amount:</td>
                        <td style="font-size: 8pt; border: 1px solid black" align="right" colspan="4" rowspan="1">
                            ${record.total}</td>
                    </tr>
                </table>

                <table style="width: 100%; border: 1px solid black">
                    <tr style="padding-bottom: 40px">
                        <td style="font-size: 7pt" align="left" colspan="16">No Changes can be made to Sales Orders
                            after 5 days of buyer signing order confirmations. No discounts and returns on this order;
                            order must be paid in full. Any goods deemed faulty will only be accepted if returned within
                            2 weeks of shipment. By signing this order confirmation, you agree to be bound by the Terms
                            and Conditions of Business of Fear of God. Signature (Head Buyer / Director)</td>
                    </tr>
                    <tr>
                        <td style="font-size: 9pt" align="left" colspan="10"></td>
                        <td style="font-size: 8pt; border-top: 1px solid black" align="left" colspan="4">Signature</td>
                        <td style="font-size: 8pt; border-top: 1px solid black" align="left" colspan="2">Date</td>
                    </tr>
                </table>


    </body>
</pdf>