<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
<#if .locale == "ru_RU">
    <link name="verdana" type="font" subtype="opentype" src="${nsfont.verdana}" src-bold="${nsfont.verdana_bold}" bytes="2" />
</#if>
    <macrolist>
        <macro id="nlheader">
		
<#if record.item?has_content>

<#list record.item as item><#if item_index==0>

<#assign poline = item.custcol_360_po_line>
</#if>
</#list>
</#if>

		

<table class="header" style="width: 100%;"><tr>
	<td width="500px" rowspan="7">
    <img src="https://system.na3.netsuite.com/core/media/media.nl?id=154&amp;c=4768130&amp;h=1f7ec539a1e3f8baa175" style="width: 128px; height: 64px;" />
    <span class="nameandaddress">${companyInformation.companyName}</span><br/><span class="nameandaddress">${companyInformation.addressText} <br/>
     Tel: (*************</span>
    </td>
	<td colspan="2" align="right"><span class="title">${record@title}</span></td>
	</tr>
	<tr>
	<td colspan="2" align="right"><span class="number">#${record.tranid}</span></td>
	</tr>
    <tr>
	<td align="right">Account</td>
    <td align="right">${record.entity.custentityaccount_number?string.computer}</td>
	</tr>
	<tr>
	<td align="right">Date</td>
    <td align="right">${record.trandate}</td>
	</tr>
    <tr>
    <td align="right">Terms</td>
    <td align="right">${record.terms}</td>
    </tr>
    <tr>
    <td align="right">Due Date</td>
	<td align="right">${record.duedate}</td>
    </tr>
    <tr>
    <td align="right">PO</td>
	<#if poline != ''>
	<#else>
	<td align="right">${record.otherrefnum}</td>
	</#if>
    </tr>
    </table>

        </macro>
        <macro id="nlfooter">
            <table class="footer" style="width: 100%;"><tr>
	<td><barcode codetype="code128" showtext="true" value="${record.tranid}"/></td>
	<td align="right"><pagenumber/> of <totalpages/></td>
	</tr></table>
        </macro>
    </macrolist>
    <style type="text/css">table {
        <#if .locale == "zh_CN">
            font-family: stsong, sans-serif;
        <#elseif .locale == "zh_TW">
            font-family: msung, sans-serif;
        <#elseif .locale == "ja_JP">
            font-family: heiseimin, sans-serif;
        <#elseif .locale == "ko_KR">
            font-family: hygothic, sans-serif;
        <#elseif .locale == "ru_RU">
            font-family: verdana;
        <#else>
            font-family: sans-serif;
        </#if>
            font-size: 9pt;
            table-layout: fixed;
        }
        th {
            font-weight: bold;
            font-size: 8pt;
            vertical-align: middle;
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
            color: #333333;
        }
        td {
            padding: 4px 6px;
        }
        b {
            font-weight: bold;
            color: #333333;
        }
        table.header td {
            padding: 0px;
            font-size: 10pt;
        }
        table.footer td {
            padding: 0px;
            font-size: 8pt;
        }
        table.itemtable th {
            padding-bottom: 10px;
            padding-top: 10px;
        }
        table.body td {
            padding-top: 2px;
        }
        table.total {
            page-break-inside: avoid;
        }
        tr.totalrow {
            background-color: #e3e3e3;
            line-height: 200%;
        }
        td.totalboxtop {
            font-size: 12pt;
            background-color: #e3e3e3;
        }
        td.addressheader {
            font-size: 8pt;
            padding-top: 6px;
            padding-bottom: 2px;
        }
        td.address {
            padding-top: 0px;
        }
        td.totalboxmid {
            font-size: 28pt;
            padding-top: 20px;
            background-color: #e3e3e3;
        }
        td.totalboxbot {
            background-color: #e3e3e3;
            font-weight: bold;
        }
        span.title {
            font-size: 28pt;
        }
        span.number {
            font-size: 16pt;
        }
        span.itemname {
            font-weight: bold;
            line-height: 150%;
        }
        hr {
            width: 100%;
            color: #d3d3d3;
            background-color: #d3d3d3;
            height: 1px;
        }
</style>
</head>
<body header="nlheader" header-height="18%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
    <table style="width: 100%; margin-top: 10px;"><tr>
	<td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
	<td class="addressheader" colspan="3"><b>${record.shipaddress@label}</b></td>
	<td class="totalboxtop" colspan="5"><b>Amount Due</b></td>
	</tr>
	<tr>
	<td class="address" colspan="3" rowspan="2">${record.billaddress}</td>
	<td class="address" colspan="3" rowspan="2">${record.shipaddress}</td>
	<td align="right" class="totalboxmid" colspan="5">${record.custbody_currency_symbol}${record.amountremaining?string?replace('[^\\.,0-9]','','r')}</td>
	</tr>
	<tr>
	<td align="right" class="totalboxbot" colspan="5"><b>${record.duedate@label}:</b> ${record.duedate}</td>
	</tr></table>

<#if record.item?has_content>

<#assign grpname = "">
<#assign grpserial = "">
<#assign grprate = 0>
<#assign grpamount = 0>
<#assign grpqty = 0>
<#assign grppo = "">

<table class="itemtable" style="width: 100%; margin-top: 10px;"><!-- start items --><#list record.item?sort_by("custcol_inv_sorting") as item><#if item_index==0>
<thead>
	<tr>

<#assign grpname = item.item>
<#assign grprate = item.rate>
<#assign grpamount = item.amount>
<#assign grppo = item.custcol_360_po_line>
<#assign grpsort = item.custcol_inv_sorting>
<#assign grpdesc = item.description>
<#assign grpitem = item.item>

<#assign showpoline = "No">


<#if item.custcol_rr_billingperiodstart?has_content>
<#assign grpbillperiod = item.custcol_rr_billingperiodstart>
<#assign grpbillperiod = grpbillperiod + "-" + item.custcol_rr_billingperiodend>
<#else>
<#assign grpbillperiod = ''>
</#if>
	

<#if poline != ''>
	<th align="center" colspan="5">PO #</th>
	<#assign showpoline = "Yes">
</#if>

	<th align="center" colspan="2">${item.quantity@label}</th>
	<th colspan="8">${item.item@label}</th>
	<th colspan="7">Billing Period</th>
    <th colspan="7">Serial Numbers</th>
	<th align="right" colspan="3">${item.rate@label}</th>
	<th align="right" colspan="3">${item.amount@label}</th>
	</tr>
</thead>
</#if>



<#if item.custcol_inv_sorting == grpsort>

<#assign grpqty = grpqty + item.quantity>
<#assign grpamount = grpamount + item.amount>
	
	<#if item.custcol_360_serialized_inventory != ''>
	<#assign grpserial = grpserial + item.custcol_360_serialized_inventory>
	<#else>
	<#assign grpserial = grpserial + item.custcol_customer_vehicle>
	</#if>

<#else>

<#assign grpsort = item.custcol_inv_sorting>

<tr>
<#if showpoline = "Yes">
<td colspan="5">${grppo}</td>
</#if>

	<td align="center" colspan="2" line-height="150%">${grpqty}</td>
	<td colspan="8">${grpdesc}</td>
	<td colspan="7">
    <#if grpbillperiod != ''>
      ${grpbillperiod}
	</#if>
  	</td>
	

	<td align="right" colspan="7">	<#if grpserial != ''>${grpserial} </#if></td>
	
	<td align="right" colspan="3"><#setting locale="en_US">
${record.custbody_currency_symbol}${grprate?string?replace('[^\\.,0-9]','','r')}</td>

<!--

	<td align="right" colspan="3"><#setting locale="en_US">
${record.custbody_currency_symbol}${grpamount?string?replace('[^\\.,0-9]','','r')}</td>
<-->

	<td align="right" colspan="3"><#setting locale="en_US">
${record.custbody_currency_symbol}${(grpqty * grprate)?string(",##0.00")?replace('[^\\.,0-9]','','r')}</td>

</tr>





<#assign grprate = item.rate>
<#assign grpamount = 0>
<#assign grpqty = item.quantity>
<#assign grpserial = "">
<#assign grpitem = item.item>
<#assign grpdesc = item.description>

	<#if item.custcol_rr_billingperiodstart?has_content>
	<#assign grpbillperiod = item.custcol_rr_billingperiodstart>
	<#assign grpbillperiod = grpbillperiod + "-" + item.custcol_rr_billingperiodend>
	<#else>
	<#assign grpbillperiod = ''>
	</#if>

</#if>


<!--
<tr>
	<#if showpoline = "Yes">
	<td colspan="5">${item.custcol_360_po_line}</td>
	</#if>
	<td align="center" colspan="2" line-height="150%">${item.quantity}</td>
	<td colspan="8">${item.description}</td>
	<td colspan="7">
    <#if item.custcol_rr_billingperiodstart?has_content>
      ${item.custcol_rr_billingperiodstart} - ${item.custcol_rr_billingperiodend}
   </#if>
  	</td>
	
	<#if item.custcol_360_serialized_inventory != ''>
	<td align="right" colspan="7">${item.custcol_360_serialized_inventory}</td>
	<#else>
	<td align="right" colspan="7">${item.custcol_customer_vehicle}</td>
	</#if>
	
	<td align="right" colspan="3"><#setting locale="en_US">
${record.custbody_currency_symbol}${item.rate?string?replace('[^\\.,0-9]','','r')}</td>
	<td align="right" colspan="3"><#setting locale="en_US">
${record.custbody_currency_symbol}${item.amount?string?replace('[^\\.,0-9]','','r')}</td>
	</tr>
<-->

	</#list><!-- end items -->
	
	<tr>
<#if showpoline = "Yes">
<td colspan="5">
${grppo}</td>
</#if>

	<td align="center" colspan="2" line-height="150%">${grpqty}</td>
	<td colspan="8">${grpdesc}</td>
	<td colspan="7">
    <#if grpbillperiod != ''>
      ${grpbillperiod}
	</#if>
  	</td>
	
	<td align="right" colspan="7">	<#if grpserial != ''>${grpserial} </#if></td>
	
	<td align="right" colspan="3"><#setting locale="en_US">
${record.custbody_currency_symbol}${grprate?string?replace('[^\\.,0-9]','','r')}</td>
	<td align="right" colspan="3"><#setting locale="en_US">
${record.custbody_currency_symbol}${(grpqty * grprate)?string(",##0.00")?replace('[^\\.,0-9]','','r')}</td>
</tr>

	
</table>

<hr /></#if>




<table class="total" style="width: 100%; margin-top: 10px;"><tr>
	<td colspan="4">&nbsp;</td>
	<td align="right"><b>${record.subtotal@label}</b></td>
	<td align="right"><#setting locale="en_US">
${record.custbody_currency_symbol}${record.subtotal?string?replace('[^\\.,0-9]','','r')}</td>
	</tr>
	<tr>
	<td colspan="4">&nbsp;</td>
	<td align="right"><b>${record.taxtotal@label} (${record.taxrate}%)</b></td>
	<td align="right"><#setting locale="en_US">
${record.custbody_currency_symbol}${record.taxtotal?string?replace('[^\\.,0-9]','','r')}</td>
	</tr>
	<tr class="totalrow">
	<td background-color="#ffffff" colspan="4">&nbsp;</td>
	<td align="right"><b>${record.total@label}</b></td>
	<td align="right"><#setting locale="en_US">
${record.custbody_currency_symbol}${record.total?string?replace('[^\\.,0-9]','','r')}</td>
	</tr>

<#if record.custbody_cm_applied &gt; 0>
 	<tr class="totalrow">
	<td background-color="#ffffff" colspan="4">&nbsp;</td>
	<td align="right"><b>Credits Applied</b></td>
	<td align="right"><#setting locale="en_US">
${record.custbody_currency_symbol}${record.custbody_cm_applied?string(",##0.00")?replace('[^\\.,0-9]','','r')}</td>
	</tr>

	<#else>

 	<tr class="totalrow">
	<td background-color="#ffffff" colspan="4">&nbsp;</td>
	<td align="right"><b>Credits Applied</b></td>
	<td align="right"><#setting locale="en_US">
${record.custbody_currency_symbol}0.00</td>
	</tr>
	
	</#if>

	
<#if record.custbody_payment_applied &gt; 0>	
 	<tr class="totalrow">
	<td background-color="#ffffff" colspan="4">&nbsp;</td>
	<td align="right"><b>Amount Paid</b></td>
	<td align="right"><#setting locale="en_US">
${record.custbody_currency_symbol}${record.custbody_payment_applied?string(",##0.00")?replace('[^\\.,0-9]','','r')}</td>
	</tr>
	
<#else>	

 	<tr class="totalrow">
	<td background-color="#ffffff" colspan="4">&nbsp;</td>
	<td align="right"><b>Amount Paid</b></td>
	<td align="right"><#setting locale="en_US">
${record.custbody_currency_symbol}0.00</td>
	</tr>
	
</#if>
	
  	<tr class="totalrow">
	<td background-color="#ffffff" colspan="4">&nbsp;</td>
	<td align="right"><b>Amount Due</b></td>
	<td align="right"><#setting locale="en_US">
${record.custbody_currency_symbol}${record.amountremaining?string?replace('[^\\.,0-9]','','r')}</td>
	</tr></table>
</body>
</pdf>