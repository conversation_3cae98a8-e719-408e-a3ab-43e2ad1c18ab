{"type": "error.SuiteScriptError", "name": "SSS_INVALID_FORM_ELEMENT_NAME", "message": "You have entered an invalid form element name. It must be prefixed with \"custpage\", unique, lowercase, and cannot contain any non-alphanumeric characters (except for the underscore character) in order to be added to the form or sublist.", "stack": ["addField(N/serverWidget)", "createForm(/SuiteBundles/Bundle 179378/com.suitesolutions.nstsrfc/src/suitelet/NSTS_RFC_SL_SearchTrans.js:395)", "searchTransSuitelet(/SuiteBundles/Bundle 179378/com.suitesolutions.nstsrfc/src/suitelet/NSTS_RFC_SL_SearchTrans.js:120)"], "cause": {"type": "internal error", "code": "SSS_INVALID_FORM_ELEMENT_NAME", "details": "You have entered an invalid form element name. It must be prefixed with \"custpage\", unique, lowercase, and cannot contain any non-alphanumeric characters (except for the underscore character) in order to be added to the form or sublist.", "userEvent": null, "stackTrace": ["addField(N/serverWidget)", "createForm(/SuiteBundles/Bundle 179378/com.suitesolutions.nstsrfc/src/suitelet/NSTS_RFC_SL_SearchTrans.js:395)", "searchTransSuitelet(/SuiteBundles/Bundle 179378/com.suitesolutions.nstsrfc/src/suitelet/NSTS_RFC_SL_SearchTrans.js:120)"], "notifyOff": false}, "id": "", "notifyOff": false, "userFacing": false}