/**
 * @NApiVersion 2.x
 * @NScriptType UserEventScript
 */

define([
    'N/search'
], function(search) {
    function getShopifySettings(settingsId) {
        var SHOPIFY_SETTINGS = 'customrecord_in8_shop_settings';
        var Field = {
            INACTIVE: 'isinactive',
            INTERNAL_ID: 'internalid',
            DISCOUNT_ITEM: 'custrecord_in8_shop_sett_discount_item',
            SHIPPING_DISCOUNT_ITEM: 'custrecord_in8_shop_sett_ship_discount'
        };
        return search.create({
            type: SHOPIFY_SETTINGS,
            filters:[
                [ Field.INTERNAL_ID, search.Operator.ANYOF, settingsId ],
                'AND',
                [ Field.INACTIVE, search.Operator.IS, false ]
            ],
            columns: [
                Field.DISCOUNT_ITEM,
                Field.SHIPPING_DISCOUNT_ITEM
            ]
        })
        .run()
        .getRange({ start: 0, end: 1 })
        .reduce(function(res, result) {
            return {
                discountItem: result.getValue({ name: Field.DISCOUNT_ITEM }),
                shippingDiscountItem: result.getValue({ name: Field.SHIPPING_DISCOUNT_ITEM }),
            };
        }, {});
    }
    function removeHeaderDiscount(newRecord, discountItemList) {
        var discount = newRecord.getValue({ fieldId: 'discountitem' });
        if (!discount || discountItemList.indexOf(discount) == -1) return;

        newRecord.setValue({ fieldId: 'discountitem', value: '' });
    }
    function removeLineDiscount(newRecord, discountItemList) {
        var discountLine = -1;
        do {
            discountLine = discountItemList.reduce(function(discountLineIndex, discountItem) {
                var index = newRecord.findSublistLineWithValue({ sublistId: 'item', fieldId: 'item', value: discountItem });
                if (index !== -1) {
                    newRecord.removeLine({ sublistId: 'item', line: index });
                    return index;
                }
                return discountLineIndex;
            }, -1);
        } while(discountLine !== -1);
    }
    function beforeSubmit(context) {
        if (context.type != 'create') return;

        var newRecord = context.newRecord;

        var settingsId = newRecord.getValue({ fieldId: 'custbody_in8_shop_setting' });
        if (!settingsId) return;

        var shopOrderId = newRecord.getValue({ fieldId: 'custbody_in8_shop_id' });
        if (!shopOrderId) return;

        var shopSettings = getShopifySettings(settingsId);
        removeHeaderDiscount(newRecord, [
            shopSettings.discountItem
        ]);
        removeLineDiscount(newRecord, [
            shopSettings.discountItem,
            shopSettings.shippingDiscountItem
        ]);
    }
    return {
        beforeSubmit: beforeSubmit
    };
});