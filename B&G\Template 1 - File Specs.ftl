<#-- format specific processing -->

<#function getVoidCheckIndicator payment>
    <#assign value = "">
    <#assign reversalDate = payment.reversaldate>
    <#if reversalDate?has_content>
        <#assign value = "V">
    </#if>
    <#return value>
</#function>

<#function getReferenceNote payment>
    <#assign value = "">
    <#if payment.recordtype == 'cashrefund'>
        <#return payment.otherrefnum>
    </#if>
    <#return payment.tranid>
</#function>

<#-- template building -->
<#assign totalAmount = 0>
#OUTPUT START#
<#list payments as payment>
<#assign amount = getAmount(payment)>
<#assign entity = entities[payment_index]>
I,${getReferenceNote(payment)},${buildEntityName(entity,true)?replace(",", " ")},${payment.trandate?string("MM/dd/yy")},${formatAmount(getAmount(payment),"dec")}
</#list>
#OUTPUT END#