/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/ui/serverWidget', 'N/search', 'N/file', 'N/format'], function(https, serverWidget, search, file, format) {

    function onRequest(context) {

        if(context.request.method == https.Method.GET){

            var getForm = serverWidget.createForm({
                title: 'Export Saved Search to CSV'
            });
            
            // search id field
            var subjectField = getForm.addField({
                id: 'custpage_search_id',
                label: 'Search ID',
                type: serverWidget.FieldType.TEXT
            });
            subjectField.isMandatory = true;

            // submit button
            getForm.addSubmitButton({
                label: 'Send'
            });

            context.response.writePage({ pageObject: getForm });


        } else if(context.request.method == https.Method.POST) {
            
            var requestParams = context.request.parameters;
            var searchId = requestParams.custpage_search_id;
                    
            var searchObj = search.load({
                id: searchId
            });

            var csvFile = file.create({
                name: 'csv_' + searchObj.id,
                fileType: file.Type.CSV,
                folder : -20
            });
            
            var ret = 'Internal ID, ';
            var resultSet = searchObj.run();
            var cols = resultSet.columns;
            for(i = 0; i < cols.length; i++){
                ret += cols[i].label + ', ';
            }
            csvFile.appendLine({
                value: ret
            });

            resultSet.each(function(result){
                var ret = result.id + ', ';       
                for(i = 0; i < cols.length; i++){
                    if(cols[i].name.indexOf('date') !== -1){
                        formattedDate = format.format({
                            value: new Date(result.getValue({ name: cols[i].name })),
                            type: format.Type.DATE
                        });
                        ret += formattedDate += ', ';
                    } else if(result.getText({ name: cols[i].name, join: cols[i].join })){
                        ret += result.getText({ name: cols[i].name, join: cols[i].join }) + ', ';
                    } else {
                        ret += result.getValue({ name: cols[i].name, join: cols[i].join }) + ', ';
                    }
                }
                csvFile.appendLine({
                    value: ret
                });
                return true
            });

            csvFile.save();

            var postForm = serverWidget.createForm({
                title: 'Successfuly extracted Saved Search'
            });

            context.response.writePage({
                pageObject: postForm
            });

        } else {
            return {
                message: "Unsupported request method!"
            };
        }
    }

    return {
        onRequest: onRequest
    }
});
