/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
 define(['N/record', 'N/search'], function(record, search) {

    function afterSubmit(context) {
        
        if(context.type == context.UserEventType.CREATE) {

            try {
                var caseRecord = context.newRecord;

                var category = caseRecord.getValue({
                    fieldId: 'category'
                });

                if(category == 17) {

                    var taskRecord = record.create({
                        type: record.Type.TASK,
                        isDynamic: false
                    });
                    
                    // set form
                    taskRecord.setValue({
                        fieldId: 'customform',
                        value: 119
                    });

                    // set employee
                    taskRecord.setValue({
                        fieldId: 'assigned',
                        value: 248322
                    });

                    // set case
                    taskRecord.setValue({
                        fieldId: 'supportcase',
                        value: caseRecord.id
                    });

                    //set date
                    var date = new Date();
                    date.setDate(date.getDate() + 7);
                    taskRecord.setValue({
                        fieldId: 'duedate',
                        value: date
                    });

                    // set task type
                    taskRecord.setValue({
                        fieldId: 'custevent147',
                        value: 1
                    });

                    // get Customer
                    var customerId = caseRecord.getValue({
                        fieldId: 'company'
                    });

                    var customerWebAddress = search.lookupFields({
                        type: search.Type.CUSTOMER,
                        id: customerId,
                        columns: ['url']
                    });

                    // set website field
                    taskRecord.setValue({
                        fieldId: 'custevent93',
                        value: ((customerWebAddress.url) ? customerWebAddress.url : 'https://www.redspotinteractive.com/' )
                    });

                    // get case title
                    var caseTitle = caseRecord.getValue({
                        fieldId: 'title'
                    });

                    // set task title
                    taskRecord.setValue({
                        fieldId: 'title',
                        value: caseTitle
                    });

                    // set customer
                    taskRecord.setValue({
                        fieldId: 'company',
                        value: customerId
                    });

                    var message = caseRecord.getValue({ fieldId: 'incomingmessage' });

                    // get message from case
                    // var message = getMessageFromCase(caseRecord.id);

                    // set message
                    taskRecord.setValue({
                        fieldId: 'custevent52',
                        value: message
                    });

                    var taskId = taskRecord.save({
                        enableSourcing: true,
                        ignoreMandatoryFields: true
                    });

                    log.debug('Successfully created Task!', 'Task ID: ' + taskId);

                }
            } catch (e) {
                log.error("Error", e);
            }
        }

    }

    function getMessageFromCase(caseId) {

        var searchObj = search.load({
            id: 'customsearch_acs_message_search'
        });

        var idFilter = search.createFilter({
            name: 'internalidnumber',
            join: 'case',
            operator: search.Operator.EQUALTO,
            values: caseId
        }); 

        searchObj.filters.push(idFilter);

        var message;

        searchObj.run().each(function(result){

            message = result.getValue({ name: 'message' });

            return true;
        });

        return message;
    }

    return {
        afterSubmit: afterSubmit
    }
});
