<#-- format specific processing -->
<#function buildEntityBillingAddress entity>
<#assign address="">
<#if entity.billaddress1?has_content>
<#assign address=entity.billaddress1>
<#elseif entity.shipaddress1?has_content>
<#assign address=entity.shipaddress1>
<#elseif entity.address1?has_content>
<#assign address=entity.address1>
</#if>
<#return address>
</#function>

<#function buildUnstructuredInfo payment>
<#assign name=cbank.custpage_eft_custrecord_2663_statement_name>
<#assign paymentTrans=transHash[payment.internalid]>
<#assign tranId="">
<#if paymentTrans?size==1>
<#assign transaction=paymentTrans[0]>
<#assign tranId=transaction.tranid>
</#if>
<#if tranId?has_content>
<#assign info="/POP:Payment " + tranId + " from " + name>
<#else>
<#assign info="/POP:Payment from " + name>
</#if>
<#return info>
</#function>

<#function buildUnstructuredInfoUS ebank>
<#assign info = ''>

<#if ebank.custrecord_purpose?has_content>
<#assign info = info + '/POP:' + ebank.custrecord_purpose>
</#if>

<#if ebank.custrecord_contact_phone?has_content>
<#assign info = info + '/PHON:' + ebank.custrecord_contact_phone>
</#if>

<#if ebank.custrecord_contact_name?has_content>
<#assign info = info + '/CONT:' + ebank.custrecord_contact_name>
</#if>

<#if ebank.custrecord_bank_branch_nm?has_content>
<#assign info = info + '/BBB:' + ebank.custrecord_bank_branch_nm>
</#if>

<#if ebank.custrecord_arg_tax_id_corp?has_content>
<#assign info = info + '/CUIT:' + ebank.custrecord_arg_tax_id_corp>
</#if>

<#if ebank.custrecord_arg_tax_id_ind?has_content>
<#assign info = info + '/CUIL:' + ebank.custrecord_arg_tax_id_ind>
</#if>

<#if ebank.custrecord_brz_tax_id_corp?has_content>
<#assign info = info + '/CPF:' + ebank.custrecord_brz_tax_id_corp>
</#if>

<#if ebank.custrecord_brz_tax_id_ind?has_content>
<#assign info = info + '/CNPJ:' + ebank.custrecord_brz_tax_id_ind>
</#if>

<#if ebank.custrecord_chl_tax_id_corp?has_content>
<#assign info = info + '/RUT:' + ebank.custrecord_chl_tax_id_corp>
</#if>

<#if ebank.custrecord_chl_tax_id_ind?has_content>
<#assign info = info + '/RUN:' + ebank.custrecord_chl_tax_id_ind>
</#if>

<#if ebank.custrecord_col_tax_id_corp?has_content>
<#assign info = info + '/NIT:' + ebank.custrecord_col_tax_id_corp>
</#if>

<#if ebank.custrecord_col_tax_id_ind?has_content>
<#assign info = info + '/CED:' + ebank.custrecord_col_tax_id_ind>
</#if>

<#if ebank.custrecord_co_ri_tax_id?has_content>
<#assign info = info + '/CED:' + ebank.custrecord_co_ri_tax_id>
</#if>

<#if ebank.custrecord_guat_tax_id_corp?has_content>
<#assign info = info + '/NIT:' + ebank.custrecord_guat_tax_id_corp>
</#if>

<#if ebank.custrecord_guat_tax_id_ind?has_content>
<#assign info = info + '/DPI:' + ebank.custrecord_guat_tax_id_ind>
</#if>

<#if ebank.custrecord_kaz_tax_id?has_content>
<#assign info = info + '/TRN:' + ebank.custrecord_kaz_tax_id>
</#if>

<#if ebank.custrecord_kaz_purp_code?has_content>
<#assign info = info + '/KNP:' + ebank.custrecord_kaz_purp_code>
</#if>

<#if ebank.custrecord_kaz_eco_code?has_content>
<#assign info = info + '/KBE:' + ebank.custrecord_kaz_eco_code>
</#if>

<#if ebank.custrecord_rus_tax_id?has_content>
<#assign info = info + '/INN:' + ebank.custrecord_rus_tax_id>
</#if>

<#if ebank.custrecord_rus_trans_type_code?has_content>
<#assign info = info + '/VO:' + ebank.custrecord_rus_trans_type_code>
</#if>

<#if ebank.custrecord_rus_vat_stat?has_content>
<#assign info = info + '/VAT:' + ebank.custrecord_rus_vat_stat>
</#if>

<#if ebank.custrecord_rus_father_name?has_content>
<#assign info = info + '/PATRON:' + ebank.custrecord_rus_father_name>
</#if>

<#if ebank.custrecord_ven_tax_id?has_content>
<#assign info = info + '/RIF:' + ebank.custrecord_rus_father_name>
</#if>

<#return info>

</#function>

<#-- cached values -->
<#assign totalAmount=computeTotalAmount(payments)>

<#function getSvcLvlGt payments ebanks>
<#list payments as payment>
    <#assign ebank=ebanks[payment_index]>

    <#if ebank.custrecord_wire_transfer == true>
        <#return 'URGP'>
    </#if>

    <#if getCurrencySymbol(payment.currency) != 'USD'>
        <#return 'URGP'>
    </#if>
    <#if getCurrencySymbol(payment.currency) != 'USD'>
        <#if ebank.custrecord_2663_entity_bic?has_content>
            <#return 'URGP'>
        </#if>
    <#else>
        <#return 'NURG'>
    </#if>
</#list>
<#return 'NURG'>
</#function>

<#function getSvcLvl payment ebank>
<#if ebank.custrecord_wire_transfer>
    <#return 'URGP'>
</#if>
<#if getCurrencySymbol(payment.currency) != 'USD'>
    <#return 'URGP'>
</#if>
<#if getCurrencySymbol(payment.currency) != 'USD'>
    <#if ebank.custrecord_2663_entity_bic?has_content>
        <#return 'URGP'>
    </#if>
<#else>
    <#return 'NURG'>
</#if>

<#return 'NURG'>
</#function>


<#-- template building -->
#OUTPUT START#
<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03">
    <CstmrCdtTrfInitn>
        <GrpHdr>
            <MsgId>${pfa.id}</MsgId>
            <CreDtTm>
                ${pfa.custrecord_2663_file_creation_timestamp?date?string("yyyy-MM-dd")}T${pfa.custrecord_2663_file_creation_timestamp?time?string("HH:mm:ss")}
            </CreDtTm>
            <NbOfTxs>${payments?size?c}</NbOfTxs>
            <CtrlSum>${setMaxLength(formatAmount(totalAmount,"decLessThan1"),18)}</CtrlSum>
            <InitgPty>
                <Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}
                </Nm>
                <Id>
                    <OrgId>
                        <Othr>
                            <Id>ACHIEVERSXML</Id>
                        </Othr>
                    </OrgId>
                </Id>
            </InitgPty>
        </GrpHdr>
        <#assign countriesWithState = ['US', 'AU', 'CA']>
        <PmtInf>
            <PmtInfId>${setMaxLength(convertToLatinCharSet(pfa.custrecord_2663_ref_note),24)}</PmtInfId>
            <PmtMtd>TRF</PmtMtd>
            <BtchBookg>true</BtchBookg>
            <NbOfTxs>${setMaxLength(payments?size?c,15)}</NbOfTxs>
            <CtrlSum>${setMaxLength(formatAmount(totalAmount,"decLessThan1"),18)}</CtrlSum>
            <#if payments?size == 1>
            <PmtTpInf>
                <SvcLvl>
                    <Cd>${getSvcLvlGt(payments, ebanks)}</Cd>
                </SvcLvl>
            </PmtTpInf>
            </#if>
            <ReqdExctnDt>${(pfa.custrecord_2663_process_date?long + 1 * ********)?number_to_date?string("yyyy-MM-dd")}</ReqdExctnDt>
            <Dbtr>
                <Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm>
                <PstlAdr>
                    <#if cbank.custrecord_dbtr_zip?has_content>
                    <PstCd>${cbank.custrecord_dbtr_zip}</PstCd>
                    </#if>
                    <#if cbank.custrecord_dbtr_city?has_content>
                    <TwnNm>${cbank.custrecord_dbtr_city}</TwnNm>
                    </#if>
                    <#if cbank.custrecord_dbtr_state?has_content>
                    <CtrySubDvsn>${cbank.custrecord_dbtr_state}</CtrySubDvsn>
                    </#if>
                    <Ctry>US</Ctry>	 
                    <#if cbank.custrecord_dbtr_address1?has_content>
                    <AdrLine>${cbank.custrecord_dbtr_address1}</AdrLine>
                    </#if>
                </PstlAdr>
            </Dbtr>
            <DbtrAcct>
                <Id>
                    <Othr>
                        <Id>${cbank.custpage_eft_custrecord_2663_acct_num}</Id>
                    </Othr>
                </Id>
                <Tp>
                    <Cd>CACC</Cd>
                </Tp>
                <Ccy>USD</Ccy>
            </DbtrAcct>
            <DbtrAgt>
                <FinInstnId>
                    <ClrSysMmbId>
                        <MmbId>${cbank.custpage_eft_custrecord_2663_branch_num}</MmbId>
                    </ClrSysMmbId>
                    <PstlAdr>
                        <Ctry>${cbank.custpage_eft_custrecord_2663_country_code}</Ctry>
                    </PstlAdr>
                </FinInstnId>
            </DbtrAgt>
            <#assign pmtSize = payments?size>
            <#list payments as payment>
            <#assign ebank=ebanks[payment_index]>
            <#assign entity=entities[payment_index]>
            <CdtTrfTxInf>
                <PmtId>
                    <EndToEndId>${setMaxLength(payment.memomain,12)}</EndToEndId>
                </PmtId>
                <#if pmtSize gt 1>
                <PmtTpInf>
                    <SvcLvl>
                        <Cd>${getSvcLvl(payment, ebank)}</Cd>
                    </SvcLvl>
                </PmtTpInf>
                </#if>
                <Amt>
                    <InstdAmt Ccy="${getCurrencySymbol(payment.currency)}">${setMaxLength(formatAmount(getAmount(payment),"decLessThan1"),16)}</InstdAmt>
                </Amt>
                <CdtrAgt>
                    <FinInstnId>
                        <#if ebank.custrecord_2663_entity_country_code != 'US'>
                            <#if ebank.custrecord_2663_entity_bic?has_content>
                            <BIC>${ebank.custrecord_2663_entity_bic}</BIC>
                            </#if>
                        <#else>
                            <#if ebank.custrecord_aba_routing?has_content>
                        <ClrSysMmbId>
                            <MmbId>${ebank.custrecord_aba_routing}</MmbId>
                        </ClrSysMmbId>
                            </#if>
                        </#if>
                        <Nm>${setMaxLength(ebank.custrecord_2663_entity_bank_name,140)}</Nm>
                    </FinInstnId>
                </CdtrAgt>
                <Cdtr>
                    <Nm>${buildEntityName(entity,true)}</Nm>
                    <PstlAdr>
                        <#if entity.zipcode?has_content>
                        <PstCd>${entity.zipcode}</PstCd>
                        </#if>
                        <#if entity.city?has_content>
                        <TwnNm>${entity.city}</TwnNm>
                        </#if>
                        <#if entity.state?has_content>
                            <#if countriesWithState?seq_index_of(entity.state) != -1>
                        <CtrySubDvsn>${entity.state}</CtrySubDvsn>
                            </#if>
                        </#if>
                        <#if entity.billcountrycode?has_content>
                        <Ctry>${entity.billcountrycode}</Ctry>
                        </#if>
                        <#if entity.address1?has_content>
                        <AdrLine>${entity.address1}</AdrLine>
                        </#if>
                    </PstlAdr>
                </Cdtr>
                <CdtrAcct>
                    <#if ebank.custrecord_2663_entity_acct_no?has_content || ebank.custrecord_2663_entity_iban?has_content>
                    <Id>
                        <#if ebank.custrecord_2663_entity_iban?has_content>
                        <IBAN>${ebank.custrecord_2663_entity_iban}</IBAN>
                        </#if>
                        <#if ebank.custrecord_2663_entity_acct_no?has_content>
                        <Othr>
                            <Id>${ebank.custrecord_2663_entity_acct_no}</Id>
                        </Othr>
                        </#if>
                    </Id>
                    </#if>
                    <Tp>
                        <Cd>CACC</Cd>
                    </Tp>
                </CdtrAcct>
                <#assign ustrd = buildUnstructuredInfoUS(ebank)>
                <#if ustrd != ''>
                <RmtInf>
                    <Ustrd>${ustrd}</Ustrd>
                </RmtInf>
                </#if>
            </CdtTrfTxInf>
            </#list>
        </PmtInf>
    </CstmrCdtTrfInitn>
</Document>
<#rt>
#OUTPUT END#