/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define([], function() {

    function beforeLoad(context) {
        if(context.type == context.UserEventType.VIEW){
            var getForm = context.form;
            getForm.clientScriptModulePath = 'SuiteScripts/Noel_CS_Print_Labels_Redirect.js';
            getForm.addButton({
                id: 'custpage_print_id_label',
                label: 'Print ID Label',
                functionName: 'redirectToPDF("ID")'
            });
            
            getForm.addButton({
                id: 'custpage_print_price_tag',
                label: 'Print Price Tag',
                functionName: 'redirectToPDF("Price")'
            });
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
