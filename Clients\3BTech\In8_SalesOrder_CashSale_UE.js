/**
 * Sales Order - Fulfill and Bill automatically
 * 
 * Version    Date            Author           Remarks
 * 1.00       20 Jun 2016     Marcel P		   Initial Version
 * 

/**
 * After Submit Event
 * 
 * @param {String} type Operation types 
 *                      
 * @returns {Void}
 */
function afterSubmit(type) {
  
	var salesOrderId = nlapiGetRecordId(),
		salesOrderRecord,
		cashSaleRecord,
		status,
		id;
	
	try {
		if (type == 'edit' || type == 'create') {
			
			status = nlapiLookupField('salesorder', salesOrderId, 'status');
			
			//nlapiLogExecution('DEBUG', 'Sales order', 'Status: ' + status);
			
			// Hautebrush custom form and has a WC Order Id and the order is not billed
			if ((nlapiGetFieldValue('customform') == 294 || nlapiGetFieldValue('customform') == 342 || 
					nlapiGetFieldValue('customform') == 350 || nlapiGetFieldValue('customform') == 383 || nlapiGetFieldValue('customform') == 560) 
					&& nlapiGetFieldValue('custbody_in8_wc_order_id') && !hasCashSale() && status != 'closed' && status != 'cancelled') { 
			
				nlapiLogExecution('DEBUG', 'In8', 'Create a Cash Sale. Sales Order ' + salesOrderId);
				
				// Creates the Cash Sale record
				cashSaleRecord = nlapiTransformRecord('salesorder', salesOrderId, 'cashsale');

                cashSaleRecord.setFieldValue('ccapproved', 'T');
                //cashSaleRecord.setFieldValue('chargeit', 'T');
                
                // Viotek order
                if (nlapiGetFieldValue('custbody_marketplace') == 17) {
                	
                	nlapiLogExecution('DEBUG', 'In8', 'Set account for Viotek order');
                	cashSaleRecord.setFieldValue('account', 411);
                	
                } else if (nlapiGetFieldValue('custbody_marketplace') == 13) {
                	
                	// Hautebrush order
                	nlapiLogExecution('DEBUG', 'In8', 'Set account for Hautebrush order');
                	cashSaleRecord.setFieldValue('account', 1152);
                	
                } else if (nlapiGetFieldValue('custbody_marketplace') == 18) { 
                	
                	// Rollibot order
                	nlapiLogExecution('DEBUG', 'In8', 'Set account for Rollibot order');
                	cashSaleRecord.setFieldValue('account', 1224);
                	
                } else if (nlapiGetFieldValue('custbody_marketplace') == 27) { 
                	
                	// Wearator
                	nlapiLogExecution('DEBUG', 'In8', 'Set account for Wearator order');
                	cashSaleRecord.setFieldValue('account', 3443);
                	
                } else {
                	nlapiLogExecution('DEBUG', 'In8', 'Invalid source website. No account was set.');
                }
                
				id = nlapiSubmitRecord(cashSaleRecord, true, true);
				
				nlapiLogExecution('DEBUG', 'In8', 'Created Cash Sale: ' + id);
			}				
		}		
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error on Sales Order ' + salesOrderId, ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
	}
}

function hasCashSale() {
	
	var filters = [],
	    columns = [],
	    searchResults,
	    i = 0;
	
	filters[i++] = new nlobjSearchFilter('createdfrom', null, 'is', nlapiGetRecordId());
	filters[i++] = new nlobjSearchFilter('mainline', null, 'is', 'T');
	
	i = 0;
	
	columns[i++] = new nlobjSearchColumn('internalid');
	
	searchResults = nlapiSearchRecord('cashsale', null, filters, columns);
	
	return searchResults;
}
