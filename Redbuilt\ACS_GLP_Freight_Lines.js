/**
 *
 */
function customizeGlImpact(transactionRecord, standardLines, customLines, book) {
    
    // 33400 freight out: https://300389-sb1.app.netsuite.com/app/accounting/account/account.nl?id=357

    // debit 
    // 40410 Cost of Sales-Freight: https://300389-sb1.app.netsuite.com/app/accounting/account/account.nl?id=385

    // credit
    // 20062 Freight Out Accrual: https://300389-sb1.app.netsuite.com/app/accounting/account/account.nl?id=616
    
    try {
        var soTranNo = transactionRecord.getFieldText('tranid');

        var linecount = standardLines.getCount();
        if (linecount == 0) return;

        for (var i=0; i<linecount; i++) {
            var line =  standardLines.getLine(i);
            // if ( !line.isPosting() ) continue; // not a posting item
            if ( line.getId() == 0 ) continue; // summary lines; ignore

            if( line.getAccountId() == 357 && (parseFloat(line.getCreditAmount()) > 0) ){


                var costCenter = line.getDepartmentId();
                var productFamily = line.getClassId();
                var businessArea = line.getLocationId();
                var entityId = line.getEntityId();
                var memo = soTranNo;
                var creditAmount = line.getCreditAmount();

                
                // create 40410 Cost of Sales-Freight line
                var newCustomLine = customLines.addNewLine();
                newCustomLine.setAccountId(385);
                newCustomLine.setDepartmentId(costCenter);
                newCustomLine.setClassId(productFamily);
                newCustomLine.setLocationId(businessArea);
                newCustomLine.setEntityId(entityId);
                newCustomLine.setMemo(memo);
                newCustomLine.setDebitAmount(creditAmount);

                // create 20062 Freight Out Accrual line
                var newCustomLine = customLines.addNewLine();
                newCustomLine.setAccountId(616);
                newCustomLine.setDepartmentId(135);
                newCustomLine.setClassId(productFamily);
                newCustomLine.setLocationId(businessArea);
                newCustomLine.setEntityId(entityId);
                newCustomLine.setMemo(memo);
                newCustomLine.setCreditAmount(creditAmount);

            } else if(line.getAccountId() == 357 && (parseFloat(line.getDebitAmount()) > 0)){

                var costCenter = line.getDepartmentId();
                var productFamily = line.getClassId();
                var businessArea = line.getLocationId();
                var entityId = line.getEntityId();
                var memo = soTranNo;
                var debitAmount = line.getDebitAmount();

                
                // create 40410 Cost of Sales-Freight line
                var newCustomLine = customLines.addNewLine();
                newCustomLine.setAccountId(385);
                newCustomLine.setDepartmentId(costCenter);
                newCustomLine.setClassId(productFamily);
                newCustomLine.setLocationId(businessArea);
                newCustomLine.setEntityId(entityId);
                newCustomLine.setMemo(memo);
                newCustomLine.setCreditAmount(debitAmount);

                // create 20062 Freight Out Accrual line
                var newCustomLine = customLines.addNewLine();
                newCustomLine.setAccountId(616);
                newCustomLine.setDepartmentId(135);
                newCustomLine.setClassId(productFamily);
                newCustomLine.setLocationId(businessArea);
                newCustomLine.setEntityId(entityId);
                newCustomLine.setMemo(memo);
                newCustomLine.setDebitAmount(debitAmount);
                
            }

        }
    } catch (e) {
        if ( e instanceof nlobjError ){
            nlapiLogExecution('ERROR', 'Unexpected Error: ' + e.getDetails());
        } else {
            nlapiLogExecution('ERROR', 'Unexpected Error: ' + e.toString());
        }
    }

}