/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search','N/file', 'N/runtime', 'N/log', 'N/record', 'N/task'], function(search, file, runtime, log, record, task) {

    function getInputData() {
        var stLogTitle = 'getInputData';
        try {
            log.debug(stLogTitle, '** START **');
            
            var scriptObj = runtime.getCurrentScript();
            var saveSearchID = scriptObj.getParameter('custscript_search_to_use');

            var searchTask = task.create({
                taskType: task.TaskType.SEARCH
            });
            searchTask.savedSearchId = saveSearchID;

            var dateTime = getFormattedTime();
            var filePath = 'Celigo Payment Files/GGH_' + dateTime + '.csv';
            searchTask.filePath = filePath;
            var searchTaskId = searchTask.submit();
            log.debug('searchTaskId', searchTaskId);

            var searchObj = search.load({
                id: saveSearchID
            });

            searchObj.columns.push(search.createColumn({
                name: "internalid",
                summary: "GROUP",
                label: "Internal ID"
             }));

            return searchObj;

        } catch (error) {
            log.error(stLogTitle, 'error: ' + error);
        }
    }

    function map(context) {
        try{
            var stLogTitle = 'map';
            
            var valueFromInput = context.value;
            var recordObj = JSON.parse(valueFromInput);
            log.debug(stLogTitle, recordObj);
            
            var recId = record.submitFields({
                type: record.Type.VENDOR_PAYMENT,  
                id: recordObj.values["GROUP(internalid)"].value,
                values: {
                    custbody_nsacs_csv_export_status: 1
                },
                options: {
                    enableSourcing: false,
                    ignoreMandatoryFields : true
                }
            });

            log.debug(stLogTitle, recId);

        } catch (e) {
            log.debug("Error", e);
        }
    }

    function reduce(context) {
        
    }

    function summarize(summary) {

    }

    function getFormattedTime() {
        var today = new Date();
        var y = today.getFullYear();
        // JavaScript months are 0-based.
        var m = today.getMonth() + 1;
        var d = today.getDate();
        var h = today.getHours();
        var mi = today.getMinutes();
        var s = today.getSeconds();
        return m + "-" + d + "-" + y + "_" + h + "-" + mi + "-" + s;
    }

    return {
        getInputData: getInputData,
        map: map,
        // reduce: reduce,
        summarize: summarize
    }
});
