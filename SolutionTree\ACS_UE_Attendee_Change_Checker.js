/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
 define(['N/runtime', 'N/search', 'N/record'], function(runtime, search, record) {

    function afterSubmit(context) {

        // if record is new, autoamtically populate the modified field
        // on the event that the user deletes a record, do nothing.
        if(context.type == context.UserEventType.CREATE) {
            var newRecObj = context.newRecord;

            record.submitFields({
                type: 'customrecord76',
                id: newRecObj.id,
                values: {
                    custrecord_acs_last_modified: new Date()
                },
                options: {
                    enableSourcing: false,
                    ignoreMandatoryFields : true
                }
            });

            return true;
        } else if (context.type == context.UserEventType.DELETE) {
            return true;
        }

        // get all the fields to exclude
        const INCLUDED_FIELDS = runtime.getCurrentScript().getParameter('custscript_acs_included_fields').split(",");

        var newRecObj = context.newRecord;
        var oldRecObj = context.oldRecord;

        // loop through all the fields and check if changed
        for(var i = 0; i < INCLUDED_FIELDS.length; i++) {

            var oldValue = oldRecObj.getValue({ fieldId: INCLUDED_FIELDS[i] });

            // if the old value is null, don't do any further work
            if(oldValue == null) continue;


            var newValue = newRecObj.getValue({ fieldId: INCLUDED_FIELDS[i] });

            // if old value is not the same as the new value, insert date to custom modified date field
            if(oldValue != newValue){
                
                record.submitFields({
                    type: 'customrecord76',
                    id: newRecObj.id,
                    values: {
                        custrecord_acs_last_modified: new Date()
                    },
                    options: {
                        enableSourcing: false,
                        ignoreMandatoryFields : true
                    }
                });
            }
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
