/**
 * Copyright (c) 2017, 2020, Oracle and/or its affiliates.
 *
 * @NApiVersion 2.x
 * @NScriptType MapReduceScript
 */

define(['../../lib/WD_SAP_RecordWrapper',
        '../../lib/WD_SAP_SearchWrapper',
        '../../lib/WD_SAP_RuntimeWrapper',
        '../../lib/WD_SAP_FormatWrapper',
        '../../lib/WD_SAP_TaskWrapper',
        '../../lib/WD_SAP_Timer',
        '../../dao/WD_SAP_DAO_MappingSetup',
        '../../dao/WD_SAP_DAO_MirrorApproval',
        '../../lib/WD_SAP_CompositeApprovalRule'],

function(record,
         search,
         runtime,
         format,
         task,
         Timer,
         MappingSetupDAO,
         MirrorApprovalDAO,
         CompositeApprovalRule) {

    function getInputData() {
        var timer = new Timer().start();
        var list = [];

        try {
            var mirrorRecId = runtime.getCurrentScript().getParameter('custscript_sas_appruleeval_mirror_rec');
            var options = {
                isPendingRuleEvaluation: true
            };

            if (mirrorRecId) {
                options.internalId = mirrorRecId;
            }

            list = new MirrorApprovalDAO().getList(options).listToArray();
        } catch (e) {
            list = [];
            log.error('GETINPUTDATA_STAGE_ERROR', e.toString());
        }

        log.debug('GETINPUTDATA_STAGE > data', list);
        log.debug('GETINPUTDATA_STAGE > duration (ms)', timer.stop().read());

        return list;
    }

    function map(context) {
        var timer = new Timer().start();

        try {
            var mirrorRecord = JSON.parse(context.value);
            var recordType = mirrorRecord.recordScriptId;
            var recordId = mirrorRecord.recordIntId;

            var mappingSetup = new MappingSetupDAO().getList({
                scriptId: recordType
            }).first();

            var originalRecord = record.load({
                type: recordType,
                id: recordId,
                isDynamic: true
            });

            var mirrorDate = mirrorRecord.recordDate || originalRecord.getValue('trandate') || mirrorRecord.dateCreated.split(' ')[0];

            var recordDate = format.format({
                value: mirrorDate,
                type: format.getType().DATE
            });

            var subsidiary = runtime.isOneWorld() ? originalRecord.getValue(mappingSetup.subsidiaryFld) : '';

            var rules = new CompositeApprovalRule().getRunningRules({
                mappingSetupId: mappingSetup.id,
                subsidiaryId: subsidiary,
                recordDate: recordDate,
                record: originalRecord
            });

            if (rules.length > 0) {
                util.each(rules, function(rule) {
                    context.write(mirrorRecord.id, {
                        id: rule.id,
                        recordId: recordId,
                        savedSearch: rule.saved_search,
                        priority: rule.priority
                    });
                });
            } else {
                var mirrorRecord = new MirrorApprovalDAO().loadRecord(mirrorRecord.id);
                mirrorRecord.setValue(MirrorApprovalDAO.Fields.PENDING_RULE_EVALUATION, false);
                mirrorRecord.save();
            }
        } catch (e) {
            log.error('MAP_STAGE_ERROR', e.toString());
        }

        log.debug('MAP_STAGE > duration (ms)', timer.stop().read());
    }

    function reduce(context) {
        var timer = new Timer().start();

        try {
            var mirrorId = context.key;
            var rawRules = context.values;
            var rules = [];
            var ruleToUse;

            util.each(rawRules, function(r) {
                rules.push(JSON.parse(r || '{}'));
            });

            rules.sort(function(a, b) {
                var aPriority = parseFloat(a.priority || '0');
                var bPriority = parseFloat(b.priority || '0');
                return ((b.priority == '') || (aPriority > bPriority)) ? 1 : (((a.priority == '') || (aPriority < bPriority)) ? -1 : 0);
            });

            util.each(rules, function(rule) {
                if (!ruleToUse) {
                    if (rule.savedSearch) {
                        var savedSearch = search.load({
                            id: rule.savedSearch
                        });

                        savedSearch.filters.push(search.createFilter({
                            name: 'internalid',
                            operator: search.getOperator().IS,
                            values: rule.recordId
                        }));

                        var result = savedSearch.run().getRange(0, 1);

                        if (result.length > 0) {
                            ruleToUse = rule.id;
                        }
                    } else {
                        ruleToUse = rule.id;
                    }
                }
            });

            var mirrorRecord = new MirrorApprovalDAO().loadRecord(mirrorId);
            mirrorRecord.setValue(MirrorApprovalDAO.Fields.PENDING_RULE_EVALUATION, false);
            mirrorRecord.setValue(MirrorApprovalDAO.Fields.APPROVAL_RULE, ruleToUse || '');
            mirrorRecord.save();
        } catch (e) {
            log.error('REDUCE_STAGE_ERROR', e.toString());
        }

        log.debug('REDUCE_STAGE > duration (ms)', timer.stop().read());
    }

    function summarize() {
        var timer = new Timer().start();

        try {
            var list = new MirrorApprovalDAO().getList({
                isPendingRuleEvaluation: true
            });

            if (list.length > 0) {
                try {
                    var script = runtime.getCurrentScript();

                    task.create({
                        taskType: task.getTaskType().MAP_REDUCE,
                        scriptId: script.id,
                        deploymentId: script.deploymentId
                    }).submit();
                } catch (e) {
                    if (e.name != 'MAP_REDUCE_ALREADY_RUNNING') {
                        throw e;
                    }
                }
            }
        } catch (e) {
            log.error('SUMMARIZE_STAGE_ERROR', e.toString());
        }

        log.debug('SUMMARIZE_STAGE > duration (ms)', timer.stop().read());
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    };

});
