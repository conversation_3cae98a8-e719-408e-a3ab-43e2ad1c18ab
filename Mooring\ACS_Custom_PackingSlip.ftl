<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="1"><img
                                src="https://6499846.app.netsuite.com/core/media/media.nl?id=13&amp;c=6499846&amp;h=ea89f5503f2f62bcc710"
                                style="width: 138px; height: 75px;" /></td>
                        <td align="right"><img
                                src="https://6499846.app.netsuite.com/core/media/media.nl?id=12&amp;c=6499846&amp;h=83777b3f3b56216878d6"
                                style="width: 150px; height: 75px;" /></td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div style="text-align: center;">
                                <hr />
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">&nbsp;</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer">
                    <tr>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                padding-top: 15px;
                font-size: 16pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="7.5%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        &nbsp;
        <table class="header" style="width: 100%;">
            <tr>
                <td rowspan="3"><span class="nameandaddress">${companyInformation.addressText}</span><br /><span class="nameandaddress">${companyInformation.fax}</span><br /><EMAIL></td>
                <td align="right"><span class="title">${record@title}</span></td>
            </tr>
            <tr>
                <td align="right"><span class="number">Order ${record.createdfrom?keep_after("Sales Order")}</span></td>
            </tr>
            <tr>
                <td align="right">${record.trandate}</td>
            </tr>
        </table>
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader"><b>${salesorder.shipaddress@label}</b></td>
            </tr>
            <tr>
                <td class="address">${salesorder.shipaddress}</td>
            </tr>
        </table>

        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th>${record.trandate@label}</th>
                <th>${salesorder.linkedtrackingnumbers@label}</th>
                <th>${record.shipmethod@label}</th>
            </tr>
            <tr>
                <td>${record.trandate}</td>
                <td>${salesorder.linkedtrackingnumbers}</td>
                <td>${record.shipmethod}</td>
            </tr>
        </table>
        <#if salesorder.item?has_content>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <thead>
                    <tr>
                        <th colspan="12">${salesorder.item[0].item@label}</th>
                        <th colspan="3">${salesorder.item[0].options@label}</th>
                        <th align="right" colspan="4">${salesorder.item[0].quantityordered@label}</th>
                        <th align="right" colspan="4">${salesorder.item[0].quantityremaining@label}</th>
                        <th align="right" colspan="4">${salesorder.item[0].quantity@label}</th>
                    </tr>
                </thead>
                <#list salesorder.item as tranline>
                    <tr>
                        <td colspan="12"><span class="itemname">${tranline.item}</span><br />${tranline.description}
                        </td>
                        <td colspan="3">${tranline.options}</td>
                        <td align="right" colspan="4">${tranline.quantityordered}</td>
                        <td align="right" colspan="4">${tranline.quantityremaining}</td>
                        <td align="right" colspan="4">${tranline.quantity}</td>
                    </tr>
                </#list>
            </table>
        </#if>
        <#if preferences.RETURNFORM && returnForm??>

            <hr />
            <div class="returnform">
                <table style="width: 100%; margin-top: 10px;">
                    <tr>
                        <td><span class="nameandaddress">${companyInformation.companyName}</span></td>
                        <td align="right"><span class="number">${returnForm@title}</span></td>
                    </tr>
                </table>

                <table style="width: 100%; margin-top: 10px;">
                    <tr>
                        <td class="addressheader" colspan="4">${returnForm.returnAddress}</td>
                        <th>${returnForm.rmaNum}</th>
                        <th colspan="2">${returnForm.customerName}</th>
                        <th>${salesorder.tranid@label}</th>
                    </tr>
                    <tr>
                        <td class="address" colspan="4" rowspan="2">&nbsp;</td>
                        <td>&nbsp;</td>
                        <td colspan="2">${salesorder.entity}</td>
                        <td>${salesorder.tranid}</td>
                    </tr>
                    <tr>
                        <td colspan="4">&nbsp;</td>
                    </tr>
                </table>

                <table class="itemtable" style="width: 100%; margin-top: 10px;">
                    <thead>
                        <tr>
                            <th colspan="2">${returnForm.item}</th>
                            <th colspan="1">${returnForm.quantity}</th>
                            <th colspan="5">${returnForm.reason}</th>
                        </tr>
                    </thead>
                    <tr>
                        <td colspan="8">&nbsp;</td>
                    </tr>
                </table>
            </div>
        </#if>
    </body>
</pdf>