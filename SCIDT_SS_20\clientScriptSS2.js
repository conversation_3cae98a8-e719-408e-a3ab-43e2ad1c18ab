define(['N/ui/dialog'], function(dialog) {
    function feelings(){
        var buttons = [
            { label: 'Weh', value: 0 },
            { label: 'Lul', value: 1 },
            { label: 'di nga', value: 2}
        ]
        var result = dialog.create({
            title: '<PERSON><PERSON><PERSON> sad',
            message: 'hehehehe sad',
            buttons: buttons
        }).then((result) => {
            if(result == 0){
                dialog.alert({
                    title: result,
                    message: 'WEH DI TOTOO YAN'
                });
            } else if(result == 1) {
                dialog.alert({
                    title: result,
                    message: 'ULUL BUGOK'
                });
            } else {
                dialog.alert({
                    title: result,
                    message: 'DI NGAAAAA'
                });
            }
        }).catch((reason) => {
            dialog.alert({
                title: 'YIKESSSS',
                message: 'OH NO ' + reason
            });
        });
    }

    return {
        feelings: feelings
    }
});
