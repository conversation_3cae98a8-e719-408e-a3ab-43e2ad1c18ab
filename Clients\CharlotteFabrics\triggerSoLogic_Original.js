function createInvoiceBasedInApprove(event,form){
try{
if(event!='approve')return;
var so = nlapiGetNewRecord();
var source  = so.getFieldValue('source');
var cod_type=so.getFieldValue('custbody_vendortermspo')||null;
if(so.getFieldValue('custbody_order_processed')=='T')return;

  // Changed the next line by In8Sync: If order is created from WooCommerce, should process it
  if(source!='Web (Charlotte Fabrics)') {    
    if (!so.getFieldValue('custbody_in8_wc_order_id')) {
	    return;      
    }
  }

  so.setFieldValue('custbody_order_processed','T');
var entity = so.getFieldValue('entity');
var codFee = so.getFieldValue('handlingcost')||null;
var lineItems = so.getLineItemCount('item');
var invoice = nlapiTransformRecord('customer', entity, 'invoice');
invoice.setFieldValue('createdfrom',so.getId());
invoice.setFieldValue('custbody_test_order',so.getId());
var shippingFee=so.getFieldValue('shippingcost')||null;
if(shippingFee)shippingFee=parseInt(shippingFee);
invoice.setFieldValue('shippingcost',shippingFee);
invoice.setFieldValue('shipcarrier',so.getFieldValue('shipcarrier'));
invoice.setFieldValue('shipmethod',so.getFieldValue('shipmethod'));
invoice.setFieldValue('shippingcost',so.getFieldValue('shippingcost'));
invoice.setFieldValue('discountitem',so.getFieldValue('discountitem'));
invoice.setFieldValue('discountrate',so.getFieldValue('discountrate'));   


for (var i = 1; i <=lineItems; i++) {
     
     var amount = so.getLineItemValue('item', 'amount', i);
     var custcol_eighths=so.getLineItemValue('item', 'custcol_eighths', i);
     var custcol_wholeyards=so.getLineItemValue('item', 'custcol_wholeyards', i);
     var side_mark=so.getLineItemValue('item', 'custcol8', i);
     var rate = so.getLineItemValue('item', 'rate', i);
     var quantity = so.getLineItemValue('item', 'quantity', i);
     var item = so.getLineItemValue('item', 'item', i);

    invoice.selectNewLineItem('item');
    invoice.setCurrentLineItemValue('item', 'amount', amount);
    invoice.setCurrentLineItemValue('item', 'quantity', quantity);
    invoice.setCurrentLineItemValue('item', 'item', item);
    invoice.setCurrentLineItemValue('item', 'rate', rate);
    invoice.setCurrentLineItemValue('item', 'custcol_eighths', custcol_eighths);
    invoice.setCurrentLineItemValue('item', 'custcol_wholeyards', custcol_wholeyards);
    invoice.setCurrentLineItemValue('item', 'custcol8', side_mark);
    invoice.commitLineItem('item');  
}
var arrayFields=[
"custbody_bf_other_pymt_type"
,"custbody_bf_terms_amt"
,"custbody_bf_credit_type"
,"custbody_bf_credit_amt"
,"custbody_bf_cod_type"
,"custbody_order_cod_amt"
,"shipaddress"
,"billaddress"
]
for (var i = 0; i < arrayFields.length; i++) {
      invoice.setFieldValue(arrayFields[i],so.getFieldValue(arrayFields[i]));
}

var id = nlapiSubmitRecord(invoice,true,true);
so.setFieldValue('custbody_invoice_number',id);
var net30=so.getFieldValue('custbody_vendortermspo')||null; 
var linesPayment = so.getLineItemCount('paymentevent');

if(cod_type!=null && codFee && linesPayment<=0){
  
  var t = nlapiLoadRecord('invoice',id);
  if(cod_type != "Net 30"){     
      codFee=parseInt(codFee);
      nlapiLogExecution('ERROR','codFee',codFee);
      t.setFieldValue('handlingcost',codFee);
      t.setFieldValue('althandlingcost',codFee);  
      nlapiSubmitRecord(t);
      
      so.setFieldValue('custbody_order_cod_amt',so.getFieldValue('total'));
      return;
  }
  
}



if(net30){
  if(net30=="Net 30")return;
}


//createPayment(entity,id,so);
}catch(e){
   nlapiLogExecution('ERROR','ERROR',e);
}

}

function createPayment(){
        
        try{
          var salesorder=nlapiLoadRecord('salesorder',nlapiGetRecordId());
          var source = salesorder.getFieldValue('source');
          var net30=salesorder.getFieldValue('custbody_vendortermspo')||null; 
          if(net30){
 			 if(net30=="Net 30")return;
			}

		  // Changed the next line by In8Sync: If order is created from WooCommerce, should process it
          if(source!='Web (Charlotte Fabrics)') {    
            if (!salesorder.getFieldValue('custbody_in8_wc_order_id')) {
                return;      
            }
            // Do not create payment
            if (net30 != 'Credit Card') {
            	return;
            }
          }
          
		  if(salesorder.getFieldValue('custbody_payment_process')=='T')return;
		   salesorder.setFieldValue('custbody_payment_process','T');		  
          //var searchInvoice=nlapiSearchRecord('invoice',null,["custbody_test_order","is",salesorder.getId()],null)||null;
          var invoiceNumber=salesorder.getFieldValue('custbody_invoice_number');
           nlapiLogExecution('ERROR','invoiceNumber--',invoiceNumber);
          if(!invoiceNumber)return;
          //var invoice = nlapiLoadRecord('invoice',invoiceNumber);
          var payment = nlapiTransformRecord('customer',salesorder.getFieldValue('entity'),'customerpayment'); //nlapiCreateRecord('customerpayment');
            payment.setFieldValue('customer',salesorder.getFieldValue('entity'));
            payment.setFieldValue('payment',salesorder.getFieldValue('total'));
            var lines = payment.getLineItemCount('apply');
            
            for (var x = 1; x <= lines; x++) {
              
                 if(payment.getLineItemValue('apply','internalid',x)==invoiceNumber){
                     payment.selectLineItem('apply', x);
                     payment.setCurrentLineItemValue('apply','apply','T');
                     payment.setCurrentLineItemValue('apply','total',salesorder.getFieldValue('total'));
                     payment.commitLineItem('apply');           
                 }

            }
       payment.setFieldValue('custbody_test_order',salesorder.getId());
       var id = nlapiSubmitRecord(payment,true,true);
       nlapiSubmitRecord(salesorder,true,true);       
       nlapiLogExecution('ERROR','payment--',id);
       unsetCreditCard(salesorder.getFieldValue('entity'));
   }catch(e){
    nlapiLogExecution('ERROR','ERROR',e);
   }

            
}

function unsetCreditCard(customerId){
try{
  
var customer = nlapiLoadRecord('customer',customerId);
var countCreditCrds = customer.getLineItemCount('creditcards');
  if(countCreditCrds>=1){
    try{
        for (var xi = 1; xi <= countCreditCrds; xi++) {
               customer.selectLineItem('creditcards', xi);
               customer.setCurrentLineItemValue('creditcards','ccdefault','F');                
               customer.commitLineItem('creditcards');
      };
    }catch(e){
    nlapiLogExecution('ERROR', 'ERROR', e);
    } 

    }//end if
    nlapiSubmitRecord(customer,false,true);

}catch(e){
 nlapiLogExecution('ERROR', 'ERROR', e);
}
    
}

function getAddress(){
try{


var record = nlapiGetNewRecord();
var address=record.getFieldValue('custbody_default_address')||null;
var billing_address = record.getFieldValue('custbody_default_billing_address')||null;

if(!address)return;
var customer = nlapiLoadRecord('customer',record.getFieldValue('entity'));
  
var addressbook = customer.getLineItemCount('addressbook');

for (var i = 1; i <= addressbook; i++) {
   var addressCompare =  customer.getLineItemValue('addressbook','internalid',i);
   if(addressCompare==address){
    customer.selectLineItem('addressbook', i);
      customer.setCurrentLineItemValue('addressbook', 'defaultshipping', "T");                    
      customer.commitLineItem('addressbook');
    
   }

   if(addressCompare==billing_address){
      customer.selectLineItem('addressbook', i);
      customer.setCurrentLineItemValue('addressbook', 'defaultbilling', "T");       
      customer.commitLineItem('addressbook'); 
   }
   
   
}
 nlapiSubmitRecord(customer,false,true);
}catch(e){
	nlapiLogExecution('ERROR','ERROR',e);
}
}
