/**
 * Estimate Script
 * 
 * Version    Date            Author           Remarks
 * 1.00       24 Nov 2017     Marcel P		   Initial Version
 *
 */

/**
 * @appliedtorecord estimate
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      
 * @returns {Void}
 */
function beforeSubmit(type) {
 
	try {
        if (type == 'create' && nlapiGetContext().getExecutionContext() == 'userinterface') {
            
            // If WooCommerce Id has value, empty the field
            if (nlapiGetFieldValue('custbody_in8_wc_order_id')) {
                
                nlapiSetFieldValue('custbody_in8_wc_order_id', '');
                
                nlapiLogExecution('DEBUG', 'Debug', 'WC Field id cleaned.');
            }            
        }        
    } catch(e) {
        nlapiLogExecution('ERROR', 'Error', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
    }
}
