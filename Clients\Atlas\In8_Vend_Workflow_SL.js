/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/log', 'N/search', 'N/record', 'N/https', 'N/runtime'], function (log, search, record, https, runtime) {

    function onRequest(context) {

        log.debug('Body', context.request.body);

        var obj = JSON.parse(context.request.body);

        if (obj.event_type == 'sale.ready_for_payment') {
            readyForPaymentCustom(context, obj);
        } else if (obj.event_type == 'sale.line_items.added') {
            saleLineItemAdded(context, obj);
        }
    }

    function readyForPaymentCustom(context, obj) {

        var items = {};

        if (obj.sale.custom_fields && obj.sale.custom_fields.length) {

        } else {
            var requiredCustomFields = [];
            var values = [];

            items.actions = [];

            var salesReps = getSalesReps();
            var isReturn = isReturnSale(obj.sale);

            if (isReturn) {
                var returnReasons = getReturnReasons();

                for (var i = 0; i < returnReasons.length; i++) {
                    values.push({
                        "value": returnReasons[i].getValue('internalid'),
                        "title": returnReasons[i].getValue('name')
                    });
                }
                requiredCustomFields.push({
                    'name': 'return-reason',
                    'values': values
                });
            }

            values = [{
                "value": "0",
                "title": "- NONE -"
            }];

            for (var i = 0; i < salesReps.length; i++) {
                values.push({
                    "value": salesReps[i].getValue('internalid'),
                    "title": salesReps[i].getValue('entityid')
                });
            }

            requiredCustomFields.push({
                "name": "salesrep",
                "values": values
            });

            items.actions.push({
                "type": "require_custom_fields",
                "title": "Information about this sale",
                "message": "Please enter the required fields.",
                "entity": "sale",
                "required_custom_fields": requiredCustomFields,
            });
        }
        context.response.setHeader({
            name: 'Content-Type',
            value: 'application/json'
        });

        log.debug('Results', JSON.stringify(items));

        context.response.write(JSON.stringify(items));
    }

    function saleLineItemAdded(context, obj) {

        var items = {};
        var currentIndex = obj.line_items.length - 1;

        if (obj.line_items[currentIndex].custom_fields.length) {
            for (var i = 0; i < obj.line_items[currentIndex].custom_fields.length; i++) {
                setCustomField(obj.line_items[currentIndex].id, obj.line_items[currentIndex].custom_fields[i], "line_item");
            }
        } else {
            var itemId = obj.line_items[currentIndex].product_id;

            var item = findProductId(itemId);

            if (item.length) {
                // Add Tagalong items
                var tagalongItems = item[0].getValue({
                    name: 'custitem28',
                    join: 'custrecord_in8_vend_ids_item'
                });

                log.debug('tagalongItems', tagalongItems);

                if (tagalongItems) {
                    addTagalongItems(items, tagalongItems);
                }

                var requiredCustomFields = [];

                var isSerialItem = item[0].getValue({
                    name: 'isserialitem',
                    join: 'custrecord_in8_vend_ids_item'
                });
                var item = item[0].getValue({
                    name: 'custrecord_in8_vend_ids_item'
                });

                var location = getLocation(obj);
                log.debug('In8', 'Location: ' + location);

                if (isSerialItem) {
                    // Get Serial Numbers
                    var serialNumbers = getSerialNumbers(location, item);

                    log.debug('Serial Numbers', serialNumbers);

                    if (serialNumbers.length) {
                        var values = [];

                        if (serialNumbers.length == 1) {
                            values.push({
                                "value": '-',
                                "title": 'No Serial #'
                            });
                        }

                        for (var i = 0; i < serialNumbers.length; i++) {
                            values.push({
                                "value": serialNumbers[i].getValue('internalid'),
                                "title": serialNumbers[i].getValue('inventorynumber')
                            });
                        }

                        requiredCustomFields.push({
                            "name": 'serial_number',
                            "values": values
                        });
                    } else {
                        var values = [];
                        values.push({
                            "value": '-',
                            "title": 'No Serial # in NetSuite'
                        });
                        values.push({
                            "value": '--',
                            "title": 'No Serial #. Please do not put on order. Contact admin.'
                        });
                        requiredCustomFields.push({
                            "name": 'serial_number',
                            "values": values
                        });
                    }
                }

                //var binNumbers = getBinNumbers(location, item);

                var itemLookup = search.lookupFields({
                    type: 'item',
                    id: item,
                    columns: ['recordtype']
                });

                //if (binNumbers.length) {
                var itemRec = record.load({
                    type: itemLookup.recordtype,
                    id: item
                });

                var binNumbers = getBins(itemRec);

                // log.debug('Bins Storage and Atlas', binNumbers);

                var binCount = itemRec.getLineCount({ sublistId: 'binnumber' });

                //log.debug('binCount', binCount);

                if (binCount > 0) {
                    // var customField = {
                    //     name: 'bin-number',
                    //     string_value: '0'
                    // }
                    // // Set default value
                    // setCustomField(obj.line_items[currentIndex].id, customField, "line_item");

                    var values = [];
                    // values.push({
                    //     "value": '0',
                    //     "title": 'STORE'
                    // });
                    var excludeItems = ['DC STAGING'];

                    //for (var i = 0; i < binNumbers.length; i++) {
                    for (var i = 0; i < itemRec.getLineCount({ sublistId: 'binnumber' }); i++) {
                        //var binNumberText = binNumbers[i].getText({ name: 'binnumber', summary: 'GROUP' });
                        var binNumber = itemRec.getSublistValue({
                            sublistId: 'binnumber',
                            fieldId: 'binnumber',
                            line: i
                        });
                        var binNumberText = itemRec.getSublistText({
                            sublistId: 'binnumber',
                            fieldId: 'binnumber',
                            line: i
                        });
                        var available = Number(itemRec.getSublistValue({
                            sublistId: 'binnumber',
                            fieldId: 'onhandavail',
                            line: i
                        }));

                        if (binNumbers.indexOf(binNumber) == -1) {
                            continue;
                        }
                        if (excludeItems.indexOf(binNumberText) > -1) {
                            continue;
                        }
                        if (available > 0) {
                            values.push({
                                "value": binNumberText,
                                "title": binNumberText + ' (' + available + ')'
                                //"title": binNumberText + ' (' + binNumbers[i].getValue({ name: 'itemcount', summary: 'SUM' }) + ')'
                            });
                        }
                    }

                    if (values.length == 1) {
                        values = [{
                            "value": "0",
                            "title": "- NONE -"
                        }].concat(values);
                    }
                    if (values.length > 1) {
                        requiredCustomFields.push({
                            "name": 'bin-number',
                            "values": values
                        });
                    }
                }

                if (requiredCustomFields.length) {
                    var entityId = obj.line_items[currentIndex].id;

                    if (!items.actions) {
                        items.actions = [];
                    }
                    items.actions.push({
                        "type": "require_custom_fields",
                        "title": "Choose options",
                        "message": "Enter the item custom fields",
                        "entity": "line_item",
                        "entity_id": entityId,
                        "required_custom_fields": []
                    });

                    items.actions[items.actions.length - 1].required_custom_fields = requiredCustomFields;
                }
            }
        }
        context.response.setHeader({
            name: 'Content-Type',
            value: 'application/json'
        });

        log.debug('Results', JSON.stringify(items));

        context.response.write(JSON.stringify(items));
    }

    function getBins(itemRec) {
        // Get Bins that are STORAGE and ATLAS location
        var binNumbers = [];
        var binNumbersResults = [];

        for (var i = 0; i < itemRec.getLineCount({ sublistId: 'binnumber' }); i++) {
            binNumbers.push(itemRec.getSublistValue({
                sublistId: 'binnumber',
                fieldId: 'binnumber',
                line: i
            }));
        }

        if (binNumbers.length) {
            var filters =  [['internalid', 'anyof', binNumbers], 'AND',  ['type', 'is', 'STORAGE'], 'AND', ['location', 'is', 4]];
            var columns = ['internalid'];

            var s = search.create({
                type: 'bin',
                filters: filters,
                columns: columns
            });
            var res = s.run().getRange({
                start: 0,
                end: 1000
            });
            for (var i = 0; i < res.length; i++) {
                binNumbersResults.push(res[i].getValue('internalid'));
            }
        }
        return binNumbersResults;
    }

    function addTagalongItems(items, tagalongItems) {
        if (!items.actions) {
            items.actions = [];
        }
        // Get Vend Ids
        var filters = [search.createFilter({
            name: 'custrecord_in8_vend_ids_item',
            operator: 'anyof',
            values: tagalongItems.split(',')
        })];

        var columns = ['internalid', 'custrecord_in8_vend_ids_id'];

        var itemSearch = search.create({
            type: 'customrecord_in8_vend_ids',
            filters: filters,
            columns: columns
        });
        var itemResults = itemSearch.run().getRange({
            start: 0,
            end: 100
        });

        if (itemResults.length) {
            for (var i = 0; i < itemResults.length; i++) {
                items.actions.push({
                    "type": "add_line_item",
                    "product_id": itemResults[i].getValue('custrecord_in8_vend_ids_id'),
                    "quantity": "1",
                })
            }
        }
    }

    function setCustomField(entityId, customField, entity) {
        var settingRec = record.load({
            type: 'customrecord_in8_vend_settings',
            id: 1
        });

        var obj = {
            "entity": entity,
            "entity_id": entityId,
            "values": [{
                "name": customField.name,
                "string_value": customField.string_value,
            }]
        }
        log.debug('Custom Field Obj', JSON.stringify(obj));

        var resBody = JSON.parse(https.post({
            url: settingRec.getValue({
                fieldId: 'custrecord_in8_vend_sett_store_url'
            }) + '/api/2.0/workflows/custom_fields/values',
            headers: {
                'Authorization': 'Bearer ' + settingRec.getValue({
                    fieldId: 'custrecord_in8_vend_sett_access_token'
                }),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(obj)
        }).body);

        log.debug('Custom Field Resp', resBody);
    }

    function getSalesReps() {

        var employeeSearchObj = search.create({
            type: "employee",
            filters: [
                ["salesrep", "is", "T"]
            ],
            columns: [
                search.createColumn({
                    name: "entityid",
                    sort: search.Sort.ASC,
                    label: "Name"
                }),
                search.createColumn({
                    name: "internalid"
                })
            ]
        });

        var itemResults = employeeSearchObj.run().getRange({
            start: 0,
            end: 100
        });
        return itemResults;
    }

    function isReturnSale(sale) {

        for (var i = 0; i < sale.line_items.length; i++) {
            if (Number(sale.line_items[i].quantity) < 0) {
                return true;
            }
        }
        return false;
    }

    function getReturnReasons() {

        var s = search.create({
            type: "customlist637",
            filters: [],
            columns: [
                search.createColumn({
                    name: "internalid"
                }),
                search.createColumn({
                    name: "name"
                })
            ]
        });

        var results = s.run().getRange({
            start: 0,
            end: 100
        });
        return results;
    }

    function getSerialNumbers(location, item) {

        log.debug({
            title: 'Filters',
            details: [
                ["item", "anyof", item], "AND",
                ["location", "is", location], "AND",
                ["quantityavailable", "greaterthan", "0"]
            ]
        })
        var inventorynumberSearchObj = search.create({
            type: "inventorynumber",
            filters: [
                ["item", "anyof", item], "AND",
                ["location", "is", location], "AND",
                ["quantityavailable", "greaterthan", "0"]
            ],
            columns: [
                search.createColumn({
                    name: "inventorynumber",
                    sort: search.Sort.ASC,
                    label: "Number"
                }),
                search.createColumn({
                    name: "internalid"
                }),
            ]
        });
        var itemResults = inventorynumberSearchObj.run().getRange({
            start: 0,
            end: 100
        });
        return itemResults;
    }

    function getBinNumbers(location, item) {

        var filters = [
            ["item","anyof",item],
            "AND",
            ["binnumber.location","anyof", 4],
            "AND",
            ["itemcount","greaterthan","0"],
            "AND",
            ["binnumber.type","anyof","STORAGE"]
         ];

        log.debug({
            title: 'Filters',
            details: filters
        })
        var s = search.create({
            type: "inventorydetail",
            filters: filters,
            columns: [
                search.createColumn({
                    name: "inventorynumber",
                    summary: "GROUP"
                }),
                search.createColumn({
                    name: "binnumber",
                    summary: "GROUP"
                }),
                search.createColumn({
                    name: "itemcount",
                    summary: "SUM"
                })
            ]
        });
        var itemResults = s.run().getRange({
            start: 0,
            end: 100
        });
        return itemResults;
    }

    function getLocation(obj) {

        var outletId = obj.sale.outlet_id;
        var registerId = obj.sale.register_id;

        var s = search.create({
            type: "customrecord_in8_vend_locations",
            filters: [
                ["custrecord_in8_vend_outletid", "is", outletId], "AND",
                ["custrecord_in8_vend_location_register", "is", registerId]
            ],
            columns: [
                search.createColumn({
                    name: "custrecord_in8_vend_location"
                }),
            ]
        });
        var res = s.run().getRange({
            start: 0,
            end: 1
        });
        if (res.length) {
            return res[0].getValue('custrecord_in8_vend_location');
        }
        return null;
    }

    function findProductId(vendId) {

        var filters = [search.createFilter({
            name: 'custrecord_in8_vend_ids_id',
            operator: 'is',
            values: vendId
        })];
        var columns = ['internalid', 'custrecord_in8_vend_ids_item',
            'custrecord_in8_vend_ids_item.isserialitem',
            'custrecord_in8_vend_ids_item.custitem28'
        ];

        var itemSearch = search.create({
            type: 'customrecord_in8_vend_ids',
            filters: filters,
            columns: columns
        });
        var itemResults = itemSearch.run().getRange({
            start: 0,
            end: 1
        });

        return itemResults;
    }

    return {
        onRequest: onRequest
    }
});