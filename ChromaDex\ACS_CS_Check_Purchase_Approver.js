/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/ui/dialog', 'N/runtime', 'N/https'], function(dialog, runtime, https) {

    function pageInit(context) {
        
    }

    function saveRecord(context) {
        var loggedUser = runtime.getCurrentUser();
        var dataFromRestlet = https.get('/app/site/hosting/restlet.nl?script=1092&deploy=1&userId=' + loggedUser.id);
        log.debug(dataFromRestlet.body);

        //  dialog.alert({
        //     title: 'Warning!',
        //     message: 'You cannot proceed to saving this file because you do not have a Purchase Approver. Please contact your administrator!'
        // }).then(function proceed(result) {
        //     return false;
        // });
    }

    return {
        pageInit: pageInit,
        saveRecord: saveRecord
    }
});