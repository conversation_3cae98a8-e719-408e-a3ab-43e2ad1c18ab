/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function beforeLoad(context) {
        if(context.type == context.UserEventType.CREATE) {
            var recObj = context.newRecord;

            var origRecObj = record.load({
                type: record.Type.SALES_ORDER,
                id: recObj.getValue({ fieldId: 'createdfrom' }),
                isDynamic: true
            })

            var origLineCount = origRecObj.getLineCount({
                sublistId: 'item'
            });

            for(var i = 0; i < origLineCount; i++) {

                var itemId = origRecObj.getSublistValue({
                    sublistId: 'item',
                    fieldId: 'item',
                    line: i
                });

                var itemText = origRecObj.getSublistText({
                    sublistId: 'item',
                    fieldId: 'item',
                    line: i
                });
                
                var fulfillLineNo = recObj.findSublistLineWithValue({
                    sublistId: 'item',
                    fieldId: 'item',
                    value: itemId
                });

                if(fulfillLineNo != -1) {

                    var pickTicketQty = origRecObj.getSublistValue({
                        sublistId: 'item',
                        fieldId: 'custcol_pick_ticket_qty',
                        line: i
                    });


                    recObj.setSublistValue({
                        sublistId: 'item',
                        fieldId: 'quantity',
                        line: fulfillLineNo,
                        value: pickTicketQty,
                        ignoreFieldChange: true
                    });
                }
            }
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
