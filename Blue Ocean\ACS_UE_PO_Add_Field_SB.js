/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/file', 'N/ui/serverWidget', 'N/search', 'N/format/i18n', 'N/currency', 'N/url', 'N/record'], function(file, serverWidget, search, formatCurr, currency, url, record) {

    function beforeLoad(context) {
        if(context.type == context.UserEventType.VIEW){
            var recordObj = context.newRecord;
            var recordLookUp = search.lookupFields({
                type: search.Type.PURCHASE_ORDER,
                id: recordObj.id,
                columns: [
                    'custbody_gridoe_productgrpdata'
                ]
            });
            if(recordLookUp.custbody_gridoe_productgrpdata.length > 2){
                var formObj = context.form;        
                
                var poField = formObj.addField({
                    id : 'custpage_po_grid_template_data',
                    type : serverWidget.FieldType.LONGTEXT,
                    label : 'po grid template data'
                });

                
                // REMOVE ONCE VAT FIELD IS IN RECORD
                var testVendorAcctVat = formObj.addField({
                    id : 'custpage_po_vendor_acct_no',
                    type : serverWidget.FieldType.LONGTEXT,
                    label : 'Vendor Account No'
                });
                testVendorAcctVat.defaultValue = "4562044 (Legacy: 0T806) VAT:GB303097824";
                testVendorAcctVat.updateDisplayType({
                displayType : serverWidget.FieldDisplayType.HIDDEN
                });
                    
                var recordLookUp = search.lookupFields({
                    type: search.Type.PURCHASE_ORDER,
                    id: recordObj.id,
                    columns: [
                        'currency',
                        'total',
                        'trandate',
                        'subsidiary'
                    ]
                });
                
                var currLookUp = search.lookupFields({
                    type: search.Type.CURRENCY,
                    id: recordLookUp.currency[0].value,
                    columns: [
                        'symbol'
                    ]
                });
                
                var currFormatter = formatCurr.getCurrencyFormatter({
                    currency: currLookUp.symbol,
                });

                var recRate = currency.exchangeRate({
                    source: currFormatter.currency,
                    target: 'USD',
                    date: new Date(recordLookUp.trandate)
                });
        
                var recTotal = (parseFloat(recordLookUp.total) / recRate);
                recTotal = Math.floor(recTotal * 100) / 100;

                var parentCount = recordObj.getLineCount({
                    sublistId: 'item'
                });

                var linksCount = recordObj.getLineCount({
                    sublistId: 'links'
                });

                // URL resolving
                var scheme = 'https://';
                var host = url.resolveDomain({
                    hostType: url.HostType.APPLICATION
                });
                var firstURLPart = scheme + host;

                var pdfPOFields = [];
                var exist = [];
                for(var i = 0; i < parentCount; i++) {
                    var parentId = recordObj.getSublistValue({
                        sublistId: 'item',
                        fieldId: 'custcol_scm_item_parent',
                        line: i
                    });
                    if(exist.indexOf(parentId) === -1){
                        var fieldLookUp = search.lookupFields({
                            type: search.Type.INVENTORY_ITEM,
                            id: parentId,
                            columns: [
                                'itemid',
                                'vendorname',
                                'displayname',
                                'custitem_psgss_category',
                                'custitem_bo_theme',
                                'custitem_psgss_gender',
                                'custitem11',
                                'custitem_bhpc_prod_img',
                                'custitem_psgss_season',
                                'custitem_psgss_year'
                            ]
                        }); 

                        // create parent image URL
                        var parentImageURL = (fieldLookUp.custitem_bhpc_prod_img.length) ? firstURLPart + fieldLookUp.custitem_bhpc_prod_img[0].text : "";

                        var pdfFieldsObj = {
                            internalid: parentId,
                            itemid: fieldLookUp.itemid,
                            vendorname: fieldLookUp.vendorname,
                            displayname: fieldLookUp.displayname,
                            custitem_psgss_category: (fieldLookUp.custitem_psgss_category.length) ? fieldLookUp.custitem_psgss_category[0].text : "",
                            custitem_bo_theme: (fieldLookUp.custitem_bo_theme.length) ? fieldLookUp.custitem_bo_theme[0].text : "",
                            custitem_psgss_gender: (fieldLookUp.custitem_psgss_gender.length) ? fieldLookUp.custitem_psgss_gender[0].text : "",
                            custitem_psgss_season: (fieldLookUp.custitem_psgss_season.length) ? fieldLookUp.custitem_psgss_season[0].text : "",
                            custitem_psgss_year: (fieldLookUp.custitem_psgss_year.length) ? fieldLookUp.custitem_psgss_year[0].text : "",
                            custitem11: fieldLookUp.custitem11,
                            custitem_bhpc_prod_img: parentImageURL
                        };
                        pdfFieldsObj.season_year = pdfFieldsObj.custitem_psgss_season + " " + pdfFieldsObj.custitem_psgss_year
                        pdfPOFields.push(pdfFieldsObj);
                        exist.push(parentId);
                    }
                }

                // get bills total
                var billsTotal = 0.00;
                var billsCtr = 0;
                for(var i = 0; i < linksCount; i++){
                    var linkType = recordObj.getSublistValue({
                        sublistId: 'links',
                        fieldId: 'type',
                        line: i
                    });

                    if(linkType == 'Vendor Prepayment'){
                        var totalAmt = parseFloat(recordObj.getSublistValue({
                            sublistId: 'links',
                            fieldId: 'total',
                            line: i
                        }));

                        var tranDate = recordObj.getSublistValue({
                            sublistId: 'links',
                            fieldId: 'trandate',
                            line: i
                        });
                                
                        var rate = currency.exchangeRate({
                            source: currFormatter.currency,
                            target: 'USD',
                            date: new Date(tranDate)
                        });

                        
                        // add bills total 
                        totalAmt = totalAmt / rate;
                        totalAmt = Math.floor(totalAmt * 100)
                        totalAmt = totalAmt / 100;
                        
                        billsTotal = billsTotal - totalAmt;
                        recTotal = recTotal - totalAmt;
                        billsCtr++;
                    }
                }

                // get black logo
                var logoObj = file.load({
                    id: 'Images/BHPC Classic Logo.jpg'
                });

                blackLogoURL = firstURLPart + logoObj.url;

                // get subsidiary info

                var subsidiaryObj = record.load({
                    type: 'subsidiary',
                    id: recordLookUp.subsidiary[0].value
                });

                var subsidairy = {
                    subsidiaryName: subsidiaryObj.getValue({ fieldId: 'name' }),
                    address: subsidiaryObj.getValue({ fieldId: 'mainaddress_text' })
                }


                var pdfData = {
                    parentFields: pdfPOFields,
                    billsCtr: billsCtr,
                    billsTotal: billsTotal,
                    recTotal: recTotal,
                    blackLogoURL: blackLogoURL,
                    subsidiary: subsidairy
                }

                poField.defaultValue = JSON.stringify(pdfData);
                poField.updateDisplayType({
                   displayType : serverWidget.FieldDisplayType.HIDDEN
                });
            }
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
