<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 13pt;
                margin-top: 10px;
                table-layout: fixed;
                page-break-inside: avoid;
            }

            td p {
                align: left
            }
            
        </style>
    </head>

    <body padding="0.1in 0.1in 0.1in 0.1in" size="6in 4in">
        <#list JSON.items as item>
            <#assign salesorder = record.createdfrom?replace('Sales Order ', '')>
                <table width="100%" style="margin-top: 20px; padding-top: 20px; padding-left: 15px;">
                    <tr>
                        <td>PO: <b>${record.tranid}</b></td>
                        <td><b>${record.entity}</b></td>
                    </tr>
                    <tr>
                        <td colspan="2">Display Name: <b>${item.displayname}</b></td>
                    </tr>
                    <tr>
                        <td>Item ID: <b>${item.itemid}</b></td>
                    </tr>
                    <tr>
                        <td><barcode bar-width="1" codetype="code128" showtext="true" value="${item.itemid?html}"></barcode></td>
                    </tr>
                    <tr>
                        <td>Sales Description: <b>${item.salesdescription}</b></td>
                    </tr>
                    <tr>
                        <td>Sales Order: <b>${salesorder}</b></td>
                    </tr>
                    <tr>
                        <td>Customer: <b>${item.customer}</b></td>
                    </tr>
                    <tr>
                        <td>Salesperson: <b>${record.custbody5}</b></td>
                    </tr>
                    <tr>
                        <td>Received Date: <b>${date.datenow}</b></td>
                    </tr>
                </table>
        </#list>
    </body>
</pdf>