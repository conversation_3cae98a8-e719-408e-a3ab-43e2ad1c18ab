<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="3"><img
                                src="https://4782866.app.netsuite.com/core/media/media.nl?id=4598&amp;c=4782866&amp;h=469566f458e329f6e340"
                                style="float: left; margin: 7px; width: 15%; height: 15%" /></td>
                    </tr>
                </table>
                <table style="padding-bottom: 15px; width: 100%; border: 1px solid black">
                    <tr>
                        <td style="font-size: 9pt" align="left">${record.csegfog_division}</td>
                        <td style="font-size: 9pt; font-weight: bold" align="left">PO #: ${record.tranid}</td>
                        <td style="font-size: 9pt;" align="left"></td>
                    </tr>
                </table>

                <table style="padding-bottom: 5px; width: 100%;">
                    <tr>
                        <td style="font-size: 9pt; font-weight: bold" align="left">BILL TO</td>
                        <td style="font-size: 9pt; font-weight: bold" align="left">SHIP TO</td>
                        <td style="font-size: 9pt; font-weight: bold" align="left">VENDOR</td>
                    </tr>
                    <tr>
                        <td style="font-size: 9pt; border: 1px solid black">${JSON.company_address}</td>
                        <td style="font-size: 9pt; border: 1px solid black">${record.shipaddress}</td>
                        <td style="font-size: 9pt; border: 1px solid black">${record.entity}</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 9pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="23%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter-Landscape">
        <#if JSON.missingsizes !=''>
            <table style="padding-bottom: 15px; width: 100%; margin-left: -15px; margin-right: -15px;">
                <tr>
                    <td style="background-color: #ff0000; font-size: 10pt; font-weight: bold; height: 20px;"
                        align="center" colspan="3">These sizes are missing from the product size rank list:
                        ${JSON.missingsizes}</td>
                </tr>
            </table>
        </#if>
        <table style="padding-bottom: 5px; width: 100%;">
            <tr>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">ISSUE DATE</td>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">DIVISION</td>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">SEASON</td>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">PO TYPE</td>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">VENDOR #</td>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">CURRENCY</td>
            </tr>
            <tr>
                <td style="font-size: 9pt; border: 1px solid black">${record.custbodyfog_issue_date}</td>
                <td style="font-size: 9pt; border: 1px solid black">${record.csegfog_division}</td>
                <td style="font-size: 9pt; border: 1px solid black">${record.custbodyfog_season_header}</td>
                <td style="font-size: 9pt; border: 1px solid black">${record.custbodyfog_po_type_field}</td>
                <td style="font-size: 9pt; border: 1px solid black">${record.vendor.entitynumber}</td>
                <td style="font-size: 9pt; border: 1px solid black">${record.currency}</td>
            </tr>
        </table>
        <table style="padding-bottom: 5px; width: 100%;">
            <tr>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">DEPT #</td>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">USER PO</td>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">WAREHOUSE</td>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">SHIP VIA</td>
                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="center">DELIVERY TERMS
                </td>
            </tr>
            <tr>
                <td style="font-size: 9pt; border: 1px solid black">${record.custbodyfog_department_number}</td>
                <td style="font-size: 9pt; border: 1px solid black">${record.custbodyfog_user_po}</td>
                <td style="font-size: 9pt; border: 1px solid black">${record.location}</td>
                <td style="font-size: 9pt; border: 1px solid black">${record.shipmethod}</td>
                <td style="font-size: 9pt; border: 1px solid black">${record.custbodyfog_delivery_terms_field}</td>
            </tr>
        </table>

        <#assign totalUnits=0>
            <#assign totalAmount=0>

                <#list JSON.division as division>
                    <#assign counter=0>
                        <#assign counter1=0>
                            <table style="padding-bottom: 15px; width: 100%;">
                                <#list division.sizes as sizes>
                                    <#assign counter=counter + 1>
                                </#list>
                                <#assign counter1='"' + counter + '"'>
                                    <tr>
                                        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; width: 100px"
                                            align="left" rowspan="2">SKU</td>
                                        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; width: 75px"
                                            align="left" rowspan="2">COLOR</td>
                                        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; width: 75px"
                                            align="left" rowspan="2">COLOR DESC</td>
                                        <#if counter !=0>
                                            <td style="font-size: 9pt; font-weight: bold; border: 1px solid black"
                                                align="center" rowspan="1" colspan=${counter1}>SIZES &amp; UNITS</td>
                                            <#else>
                                                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black"
                                                    align="center" rowspan="1">SIZES &amp; UNITS</td>
                                        </#if>
                                        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; width: 75px"
                                            align="center" rowspan="2">UNITS</td>
                                        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; width: 75px"
                                            align="center" rowspan="2">PRICE</td>
                                        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; width: 100px"
                                            align="center" rowspan="2">EXT. PRICE</td>
                                    </tr>
                                    <tr>
                                        <#if counter !=0>
                                            <#list division.sizes as sizes>
                                                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                                                    align="center" rowspan="1">${sizes}</td>
                                            </#list>
                                            <#else>
                                                <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                                                    align="center" rowspan="1"></td>
                                        </#if>
                                    </tr>

                                    <#assign total=0>
                                        <#list division.item as item>
                                            <#assign totalQty=0>
                                                <#if item.quantity !="0">
                                                    <#assign totalQty=item.quantity?number>
                                                </#if>
                                                <tr>
                                                    <td style="font-size: 9pt; border: 1px solid black; width: 100px"
                                                        align="left">${item.name}</td>
                                                    <td style="font-size: 9pt; border: 1px solid black; width: 75px"
                                                        align="left">${item.color}</td>
                                                    <td style="font-size: 9pt; border: 1px solid black; width: 75px"
                                                        align="left">${item.colordes}</td>
                                                    <#if counter !=0>
                                                        <#list item.allsizes as sizes>
                                                            <td style="font-size: 9pt; border: 1px solid black"
                                                                align="center">${sizes.quantity}</td>
                                                            <#assign totalQty=totalQty + sizes.quantity?number>
                                                        </#list>
                                                        <#else>
                                                            <td style="font-size: 9pt; border: 1px solid black"
                                                                align="center"></td>
                                                    </#if>
                                                    <td style="font-size: 9pt; border: 1px solid black; width: 75px"
                                                        align="center">${totalQty}</td>
                                                    <#assign totalAmt=item.rate?number * totalQty>
                                                        <#assign totalUnits=totalUnits + totalQty?number>
                                                            <#assign totalAmount=totalAmount + totalAmt?number>
                                                                <td style="font-size: 9pt;  border: 1px solid black; width: 75px"
                                                                    align="center">
                                                                    $${(item.rate?number)?string(",##0.00")}</td>
                                                                <td style="font-size: 9pt; border: 1px solid black; width: 100px"
                                                                    align="center">$${totalAmt?string(",##0.00")}</td>
                                                </tr>
                                        </#list>
                            </table>
                </#list>
                <table style="padding-bottom: 15px; width: 100%;">
                    <tr>
                        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black" align="left" colspan="3"
                            rowspan="4">SPECIAL INSTRUCTIONS</td>
                        <td style="font-size: 9pt; border: 1px solid black" align="left" colspan="12" rowspan="4">
                            ${record.custbodyfog_special_instructions}</td>
                        <td style="font-size: 9pt; font-weight: bold;" align="left" colspan="1" rowspan="4"></td>
                        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                            align="left" colspan="3" rowspan="1">Total Units:</td>
                        <td style="font-size: 9pt; border: 1px solid black" align="right" colspan="4" rowspan="1">
                            ${totalUnits}</td>
                    </tr>
                    <tr>
                        <td style="font-size: 9pt; font-weight: bold; border: 1px solid black; background-color: #e3e3e3"
                            align="left" colspan="3" rowspan="1">Total Amount:</td>
                        <td style="font-size: 9pt; border: 1px solid black" align="right" colspan="4" rowspan="1">
                            $${totalAmount?string(",##0.00")}</td>
                    </tr>
                    <tr>
                        <td style="font-size: 9pt; font-weight: bold; height: 10px" align="left" rowspan="1"></td>
                    </tr>
                    <tr>
                        <td style="font-size: 9pt; font-weight: bold; height: 10px" align="left" rowspan="1"></td>
                    </tr>

                </table>


    </body>
</pdf>