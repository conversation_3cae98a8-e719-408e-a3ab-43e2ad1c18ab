/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/email'], function(email) {

    function afterSubmit(context) {
        var emailArr = ['<EMAIL>', '<EMAIL>'];

        for(var i = 0; i < emailArr.length; i++){
            email.send({
                author: 2,
                recipients: emailArr[i],
                subject: 'Email Test from Invoice ' + (i+1),
                body: 'email body ' + emailArr[i]
            });
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
