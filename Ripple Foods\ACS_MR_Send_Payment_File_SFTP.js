/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NModuleScope SameAccount
 */
 define(['N/file', "N/sftp", 'N/runtime', 'N/search', 'N/record', 'N/email', 'N/url', 'N/https', 'N/format/i18n'],
    function (file, sftp, runtime, search, record, email, url, https, format) {

        function pad(pad, str, padLeft) {
            if (typeof str === 'undefined')
                return pad;
            if (padLeft) {
                return (pad + str).slice(-pad.length);
            } else {
                return (str + pad).substring(0, pad.length);
            }
        }
        
        
        function decryptFile(contents) {
            
            var scriptID = 'customscript_15152_encryption_suitelet';
            var deployementID = 'customdeploy_15152_encryption_suitelet';

            var decryptParameters = {
                action : 'decrypt',
                actionInput : contents,
                featureKey : 'NACHA'
            };

            var suiteletURL = url.resolveScript({
                scriptId: scriptID,
                deploymentId: deployementID,
                returnExternalUrl : true
            });

            var response = https.post({
                url: suiteletURL,
                body: decryptParameters
            });
            var body = JSON.parse(response.body);

            if(body.isSuccess){
                return body.actionOutput;
            }
            
            return false;
            

        }

        var pfaIds = [];

        /**
         * Marks the beginning of the Map/Reduce process and generates input data.
         *
         * @typedef {Object} ObjectRef
         * @property {number} id - Internal ID of the record instance
         * @property {string} type - Record type id
         *
         * @return {Array|Object|Search|RecordRef} inputSummary
         * @since 2015.1
         */
        function getInputData() {

            try {

                // retrieve files created today
                var searchObj = search.load({
                    id: 'customsearch_acs_payment_file_today'
                });
        
                return searchObj;
            } catch (e) {
                log.error('getInputData error', e);
            }

        }

        /**
         * Executes when the map entry point is triggered and applies to each key/value pair.
         *
         * @param {MapSummary} context - Data collection containing the key/value pairs to process through the map stage
         * @since 2015.1
         */
        function map(context) {

            try {

                var scriptObj = runtime.getCurrentScript();

                // get PFA and File ID
                var pfaID = context.key;
                var contextValues = JSON.parse(context.value);
                var fileId = contextValues.values.custrecord_2663_file_ref.value;
                var fileSent = contextValues.values.custrecord_acs_file_sent;

                // get SFTP Credentials here using script parameters
                var sftpHostKey = scriptObj.getParameter({ name: 'custscript_sftp_host_key' });
                var sftpUsername = scriptObj.getParameter({ name: 'custscript_sftp_username' });
                var sftpPort = scriptObj.getParameter({ name: 'custscript_sftp_port' });
                var sftpPassword = scriptObj.getParameter({ name: 'custscript_sftp_password' });
                var sftpServerUrl = scriptObj.getParameter({ name: 'custscript_sftp_server_url' });
                var archiveId = scriptObj.getParameter({ name: 'custscript_archive_id' });

                // load file object
                var origFileObj = file.load({
                    id: fileId
                });
            
                // var fileContent = decryptFile(origFileObj.getContents());
                var fileContent = origFileObj.getContents();
                if(fileContent) {

                    // // set the name based on bank's requirements and move to 'sent folder'

                    // var fileObj = file.create({
                    //     name: origFileObj.name,
                    //     fileType: file.Type.PLAINTEXT,
                    //     contents: fileContent,
                    //     folder: archiveId
                    // });

                    // var newFileID = fileObj.save();

                    // // reload file
                    // var fileToBeSentObj = file.load({
                    //     id: newFileID
                    // });

                    // log.audit("File Saved", "ID :" + newFileID);
                    
                    // log.audit("Start SFTP Transmission", "-------- start SFTP transmission --------");

                    // // connect to sftp server using script parameters
                    // var sftpConnection = sftp.createConnection({
                    //     username: sftpUsername,
                    //     passwordGuid: sftpPassword,
                    //     url: sftpServerUrl,
                    //     port: Number(sftpPort),
                    //     hostKey: sftpHostKey
                    // });

                    // log.debug('SFTP Connection', sftpConnection);

                    // // send file to server
                    // // sftpConnection.upload({
                    // //     file: fileToBeSentObj,
                    // //     replaceExisting: true
                    // // });
                    
                    // log.debug('File has been transmitted successfully!', { fileId: fileToBeSentObj.id, fileName: fileToBeSentObj.name });
                            
                    // record.submitFields({
                    //     type: 'customrecord_2663_file_admin',
                    //     id: pfaID,
                    //     values: {
                    //         custrecord_acs_file_sent: true,
                    //         custrecord_acs_date_sent: new Date()
                    //     }
                    // });

                    // pfaIds.push(pfaID);

                }
            } catch(e) {
                log.error('Map - Error during sftp upload', e);
            }
        }

        /**
         * Executes when the reduce entry point is triggered and applies to each group.
         *
         * @param {ReduceSummary} context - Data collection containing the groups to process through the reduce stage
         * @since 2015.1
         */
        function reduce(context) {

        }


        /**
         * Executes when the summarize entry point is triggered and applies to the result set.
         *
         * @param {Summary} summary - Holds statistics regarding the execution of a map/reduce script
         * @since 2015.1
         */
        function summarize(summary) {
                

            var scriptObj = runtime.getCurrentScript();
            var emailSuccess = scriptObj.getParameter({ name: 'custscript_email_success_transmit' });
            var emailAuthor = scriptObj.getParameter({ name: 'custscript_email_author' });

            if(emailSuccess && emailAuthor) {
                
                try {

                    var date = new Date;

                    var dateString = pad("00", (date.getMonth() + 1), true) + "/" + pad("00", date.getDate(), true) + "/" + date.getFullYear();

                    // Successful payment file transmissions
                    var emailBodySuccess = "Good Day!<br><br>NetSuite has sent the following files to SVB for payment processing. Payment Date: <b>"+dateString+"</b><br><br>";

                    emailBodySuccess += '<table border="1">';
                    emailBodySuccess += '<tr>';
                    emailBodySuccess += '<td>Payment ID</td>';
                    emailBodySuccess += '<td>CTX or PPD</td>';
                    emailBodySuccess += '<td>Total Amount Paid</td>';
                    emailBodySuccess += '</tr>';
                    var hasCompleted = 0;
                    var curFormatter = format.getCurrencyFormatter({ currency: 'USD' });

                    summary.mapSummary.keys.iterator().each(function (key, executionCount, completionState){

                        if (completionState === 'COMPLETE'){

                            var paymentSearch = search.lookupFields({
                                type: 'customrecord_2663_file_admin',
                                id: parseInt(key),
                                columns: ['name', 'custrecord_2663_bank_account', 'custrecord_2663_payments_for_process_amt', 'custrecord_2663_payments_for_process_dis']
                            });

                            var amtArr = JSON.parse(paymentSearch.custrecord_2663_payments_for_process_amt);
                            var discArr = JSON.parse(paymentSearch.custrecord_2663_payments_for_process_dis);

                            var amount = amtArr.reduce((partial_sum, a) => partial_sum + parseFloat(a), 0);
                            var discount = discArr.reduce((partial_sum, a) => partial_sum + parseFloat(a), 0);
                            var amount = amount - discount;
                            
                            var paymentId = paymentSearch.name;
                            var ctxOrCcd = paymentSearch.custrecord_2663_bank_account[0].text;
                            var ctxOrCcd = ((ctxOrCcd.indexOf('CTX') !== -1) ? 'CTX' : 'CCD');
                            var totalAmount = curFormatter.format({ number: Number(amount) });

                            emailBodySuccess += '<tr>';
                            emailBodySuccess += '<td>' + paymentId + '</td>';
                            emailBodySuccess += '<td>' + ctxOrCcd + '</td>';
                            emailBodySuccess += '<td align="right">' + totalAmount + '</td>';
                            emailBodySuccess += '</tr>';
                            hasCompleted++;
                        }
                        
                        return true;
                        
                    });

                    emailBodySuccess += '</table>';
                    emailBodySuccess += '<br><br>Transmitted files can be seen in the File Cabinet under SFTP Payment File Archive.<br><br>Script Used: <b>ACS MR | Send Payment File SFTP (id: customscript_acs_mr_send_pmt_file)</b>';


                    // Failed payment file transmissions
                    var emailBodyFailed = "Good Day!<br><br>The following payment file administration records has failed transmission to SVB. Attempted Payment Date: <b>"+dateString+"</b>";
                    
                    emailBodyFailed += '<ul>';
                    var hasFailed = 0;
                    // summary.mapSummary.keys.iterator().each(function (key, executionCount, completionState){
                    summary.mapSummary.errors.iterator().each(function (key, error, executionNo) {

                        emailBodyFailed += '<li>https://4440897.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=168&id=' + key + '</li>';
                        hasFailed++;
                        
                        return true;
                        
                    });

                    emailBodyFailed += '</ul>';
                    emailBodyFailed += '<br><br>Script Used: <b>ACS MR | Send Payment File SFTP (id: customscript_acs_mr_send_pmt_file)</b>';
                    
                    if(hasCompleted) {

                        log.debug('hasCompleted', emailBodySuccess);

                        email.send({
                            author: emailAuthor,
                            recipients: emailSuccess,
                            subject: 'Payment Transmission Successful',
                            body: emailBodySuccess
                        });
                    }

                    if(hasFailed) {

                        // log.debug('hasCompleted', emailBodySuccess);

                        email.send({
                            author: emailAuthor,
                            recipients: emailSuccess,
                            subject: 'Payment Transmission Failed',
                            body: emailBodyFailed
                        });
                    }

                } catch (e) {
                    log.error('Summarize - Error while trying to send email', e);
                }
            }

        }

        return {
            getInputData: getInputData,
            map: map,
            reduce: reduce,
            summarize: summarize
        };

    });