<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<#if record??>
    <pdf>

        <head>
            <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
                src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
                src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
            <#if .locale=="zh_CN">
                <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                    src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
                <#elseif .locale=="zh_TW">
                    <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                        src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                    <#elseif .locale=="ja_JP">
                        <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                            src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                        <#elseif .locale=="ko_KR">
                            <link name="NotoSansCJKkr" type="font" subtype="opentype"
                                src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}"
                                bytes="2" />
                            <#elseif .locale=="th_TH">
                                <link name="NotoSansThai" type="font" subtype="opentype"
                                    src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}"
                                    bytes="2" />
            </#if>
            <macrolist>
                <macro id="nlheader">
                    <table class="header" style="width: 100%;">
                        <tr>
                            <td rowspan="2" colspan="2">
                                <#if companyInformation.logoUrl?length !=0><img src="https://4928883.app.netsuite.com/core/media/media.nl?id=6176&c=4928883&h=65c5b01478fb8091c8d8"
                                        style="margin: 3px" width="80%" height="80%" /> </#if><br /><span
                                    class="nameandaddress" style="margin-top: -100px">${companyInformation.companyName}</span><br /><span
                                    class="nameandaddress">${companyInformation.addressText}</span>
                            </td>
                            <td colspan="2" align="right"><span class="title">${record@title}</span></td>
                        </tr>
                        <tr>
                            <td align="right">Date<br /><br />${record.amountDue@label}</td>
                            <td align="right">${record.trandate}<br /><br />${record.amountDue}</td>
                        </tr>
                    </table>
                </macro>
                <macro id="nlfooter">
                    <table class="footer" style="width: 100%;">
                        <tr>
                            <td align="left" width="85%">
                                If you have any questions please contact Frani Cruz at (317) 808-1718 or <EMAIL>. <br /> Thank you!
                            </td>
                            <td align="right" width="15%">
                                <pagenumber /> of
                                <totalpages />
                            </td>
                        </tr>
                    </table>
                </macro>
            </macrolist>
            <style type="text/css">
                * {
                    <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                    <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                    <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                    <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                    <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                    <#else>font-family: NotoSans, sans-serif;
                    </#if>
                }

                table {
                    font-size: 9pt;
                    table-layout: fixed;
                }

                th {
                    font-weight: bold;
                    font-size: 8pt;
                    vertical-align: middle;
                    padding: 5px 6px 3px;
                    background-color: #e3e3e3;
                    color: #333333;
                }

                td {
                    padding: 4px 6px;
                }

                td p {
                    align: left
                }

                b {
                    font-weight: bold;
                    color: #333333;
                }

                table.header td {
                    padding: 0;
                    font-size: 10pt;
                }

                table.footer td {
                    padding: 0;
                    font-size: 8pt;
                }

                table.itemtable th {
                    padding-bottom: 10px;
                    padding-top: 10px;
                }

                table.itemtable td {
                    border: 1px;
                    border-color: #e3e3e3;
                    font-size: 8pt;
                }

                table.body td {
                    padding-top: 2px;
                }

                td.addressheader {
                    font-size: 8pt;
                    padding-top: 6px;
                    padding-bottom: 2px;
                }

                td.address {
                    padding-top: 0px;
                }

                span.title {
                    font-size: 28pt;
                }

                span.number {
                    font-size: 16pt;
                }

                hr {
                    border-top: 1px dashed #d3d3d3;
                    width: 100%;
                    color: #ffffff;
                    background-color: #ffffff;
                    height: 1px;
                }
            </style>
        </head>

        <body header="nlheader" header-height="19%" footer="nlfooter" footer-height="30pt"
            padding="0.3in 0.3in 0.3in 0.3in" size="Letter">
            <table style="width: 100%;">
                <tr>
                    <td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
                </tr>
                <tr>
                    <td class="address" colspan="3">${record.billaddress}</td>
                </tr>
            </table>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <thead>
                    <tr>
                        <th colspan="3" style="width: 65px;">${record.lines.datecol@label}</th>
                        <th colspan="12" style="width: 132px;">${record.lines.description@label}</th>
                        <th style="width: 85px;">Registration #</th>
                        <th style="width: 106px;">Shipper</th>
                        <th style="width: 76px;">Reference #</th>
                        <th style="width: 56px;">PO #</th>
                        <th align="right" colspan="3" style="width: 81px;">${record.lines.charge@label}</th>
                        <th align="right" colspan="4" style="width: 79px;">${record.lines.balance@label}</th>
                    </tr>
                </thead>
                <#assign balance = 0.00>
                <#list record.lines as line>
                    <#if line.description != 'Balance Forward'>
                        <#assign amount_remaining = line.amountremaining>

                        <#if balance gt line.balance>
                            <#assign amount_remaining = "(" + line.amountremaining + ")">
                        </#if>

                        <tr>
                            <td colspan="3" style="width: 65px;">${line.datecol}</td>
                            <td colspan="12" style="width: 132px;">${line.description}</td>
                            <td style="width: 123px;">${line.custbody_gri_regnum}</td>
                            <td style="width: 106px;">${line.custbody1?keep_after(" ")}</td>
                            <td style="width: 76px;">${line.custbody_gri_ref1}</td>
                            <td style="width: 56px;">${line.otherrefnum}</td>
                            <td align="right" colspan="3" style="width: 81px;">${amount_remaining}</td>
                            <td align="right" colspan="4" style="width: 79px;">${line.balance}</td>
                        </tr>

                        <#assign balance = line.balance>
                    <#else>
                        <#assign balance = line.balance>
                    </#if>
                </#list>
            </table>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <tr>
                    <th>${record.aging1@label}</th>
                    <th>${record.aging2@label}</th>
                    <th>${record.aging3@label}</th>
                    <th>${record.aging4@label}</th>
                    <th>${record.aging5@label}</th>
                    <th>${record.agingbal@label}</th>
                </tr>
                <tr>
                    <td>${record.aging1}</td>
                    <td>${record.aging2}</td>
                    <td>${record.aging3}</td>
                    <td>${record.aging4}</td>
                    <td>${record.aging5}</td>
                    <td>${record.agingbal}</td>
                </tr>
            </table>
            <#if preferences.RETURNFORM && remittanceSlip??>

                <hr />
                <div class="remittanceSlip">
                    <table style="width: 100%; margin-top: 10px;">
                        <tr>
                            <td><span class="nameandaddress">${companyInformation.companyName}</span></td>
                            <td align="right"><span class="number">${remittanceSlip@title}</span></td>
                        </tr>
                    </table>

                    <table style="width: 100%; margin-top: 10px;">
                        <tr>
                            <th>${remittanceSlip.customername@label}</th>
                            <th>${record.trandate@label}</th>
                            <th>${record.amountDue@label}</th>
                            <th>${remittanceSlip.amountPaid@label}</th>
                        </tr>
                        <tr>
                            <td>${companyInformation.addressText}</td>
                            <td>${record.trandate}</td>
                            <td align="right">${record.amountDue}</td>
                            <td>&nbsp;</td>
                        </tr>
                    </table>

                    <table style="width: 100%; margin-top: 10px;">
                        <tr>
                            <th>${remittanceSlip.ccinfo@label}</th>
                            <th>${remittanceSlip.companyaddress@label}</th>
                        </tr>
                        <tr>
                            <td>${remittanceSlip.ccinfo}</td>
                            <td>${companyInformation.addressText}</td>
                        </tr>
                    </table>
                </div>
            </#if>
        </body>
    </pdf>
    <#else>
        <pdf>

            <head></head>

            <body>

                <p>Multi currency customer setting was detected. Please use Multi Currency Advanced Printing Template
                </p>
            </body>
        </pdf>
</#if>