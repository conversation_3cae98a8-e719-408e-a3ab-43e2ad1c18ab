<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
<#if .locale == "ru_RU">
    <link name="verdana" type="font" subtype="opentype" src="${nsfont.verdana}" src-bold="${nsfont.verdana_bold}" bytes="2" />
</#if>
    <macrolist>
        <macro id="nlheader">
            <table class="header" style="width: 100%;"><tr>
	<td rowspan="3"><#if companyInformation.logoUrl?length != 0><img src="${companyInformation.logoUrl}" style="margin: 7px; width: 64px; height: 32px;" /> </#if> <span class="nameandaddress">${companyInformation.companyName}</span><br /><span class="nameandaddress">${companyInformation.addressText}</span></td>
	<td align="right"><span class="title">${record@title}</span></td>
	</tr>
	<tr>
	<td align="right"><span class="number">#${record.tranid}</span></td>
	</tr>
	<tr>
	<td align="right">${record.trandate}</td>
	</tr></table>
        </macro>
        <macro id="nlfooter">
            <table class="footer" style="width: 100%;"><tr>
	<td><barcode codetype="code128" showtext="true" value="${record.tranid}"/></td>
	<td align="right"><pagenumber/> of <totalpages/></td>
	</tr></table>
        </macro>
    </macrolist>
    <style type="text/css">table {
        <#if .locale == "zh_CN">
            font-family: stsong, sans-serif;
        <#elseif .locale == "zh_TW">
            font-family: msung, sans-serif;
        <#elseif .locale == "ja_JP">
            font-family: heiseimin, sans-serif;
        <#elseif .locale == "ko_KR">
            font-family: hygothic, sans-serif;
        <#elseif .locale == "ru_RU">
            font-family: verdana;
        <#else>
            font-family: sans-serif;
        </#if>
            font-size: 9pt;
            table-layout: fixed;
        }
        th {
            font-weight: bold;
            font-size: 8pt;
            vertical-align: middle;
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
            color: #333333;
        }
        td {
            padding: 4px 6px;
        }
        b {
            font-weight: bold;
            color: #333333;
        }
        table.header td {
            padding: 0px;
            font-size: 10pt;
        }
        table.footer td {
            padding: 0px;
            font-size: 8pt;
        }
        table.itemtable th {
            padding-bottom: 10px;
            padding-top: 10px;
        }
        table.body td {
            padding-top: 2px;
        }
        table.total {
            page-break-inside: avoid;
        }
        tr.totalrow {
            background-color: #e3e3e3;
            line-height: 200%;
        }
        td.totalboxtop {
            font-size: 12pt;
            background-color: #e3e3e3;
        }
        td.addressheader {
            font-size: 8pt;
            padding-top: 6px;
            padding-bottom: 2px;
        }
        td.address {
            padding-top: 0px;
        }
        td.totalboxmid {
            font-size: 28pt;
            padding-top: 20px;
            background-color: #e3e3e3;
        }
        td.totalboxbot {
            background-color: #e3e3e3;
            font-weight: bold;
        }
        span.title {
            font-size: 28pt;
        }
        span.number {
            font-size: 16pt;
        }
        span.itemname {
            font-weight: bold;
            line-height: 150%;
        }
        hr {
            width: 100%;
            color: #d3d3d3;
            background-color: #d3d3d3;
            height: 1px;
        }
</style>
</head>
<body header="nlheader" header-height="15%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
    <table style="width: 100%; margin-top: 10px;"><tr>
	<td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
	<td class="addressheader" colspan="3"><b>${record.shipaddress@label}</b></td>
	<td class="totalboxtop" colspan="5"><b>${record.total@label?upper_case}</b></td>
	</tr>
	<tr>
	<td class="address" colspan="3" rowspan="2">${record.billaddress}</td>
	<td class="address" colspan="3" rowspan="2">${record.shipaddress}</td>
	<td align="right" class="totalboxmid" colspan="5">${record.total}</td>
	</tr>
	<tr>
	<td align="right" class="totalboxbot" colspan="5"><b>${record.duedate@label}:</b> ${record.duedate}</td>
	</tr></table>

<table class="body" style="width: 100%; margin-top: 10px;"><tr>
	<th>${record.terms@label}</th>
	<th>${record.duedate@label}</th>
	<th>${record.otherrefnum@label}</th>
	<th>${record.salesrep@label}</th>
	<th>${record.shipmethod@label}</th>
	<th>${record.partner@label}</th>
	</tr>
	<tr>
	<td>${record.terms}</td>
	<td>${record.duedate}</td>
	<td>${record.otherrefnum}</td>
	<td>${record.salesrep}</td>
	<td>${record.shipmethod}</td>
	<td>${record.partner}</td>
	</tr></table>
  
<#assign lastItem></#assign>
<#assign rowDesc></#assign>
<#assign totalQty>0</#assign>
<#assign totalAmt>0</#assign>
<#assign cntr>0</#assign>
<#assign itemRate>0</#assign>
  
<#if record.item?has_content>

<table class="itemtable" style="width: 100%; margin-top: 10px;"><!-- start items --><#list record.item?sort_by("item.internalid") as item><#if item_index==0>
<thead>
	<tr>
	<th align="center" colspan="3">${item.quantity@label}</th>
	<th colspan="12">${item.item@label}</th>
	<th colspan="3">${item.options@label}</th>
	<th align="right" colspan="4">${item.rate@label}</th>
	<th align="right" colspan="4">${item.amount@label}</th>
	</tr>
</thead>
</#if>

<#function toNumber val>
    <#if val?has_content && val?length gt 0 >
        <#return val?html?replace('[^0-9.]','','r')?number >
    <#else>
        <#return 0 >
    </#if>
</#function>

         
<#if lastItem != item.item && item_index != 0>
<tr>

	<td align="center" colspan="3" line-height="150%">${totalQty}</td>
	<td colspan="12"><span class="itemname">${lastItem}</span><br />${rowDesc}</td>
	<td colspan="3"></td>
	<td align="right" colspan="4">${itemRate}</td>
	<td align="right" colspan="4">${totalAmt}</td>
  
<#assign totalQty>0</#assign>
<#assign totalAmt>0</#assign>
<#assign rowDesc></#assign>
  
</tr>
</#if>

<#assign totalQty>${toNumber(item.quantity) + toNumber(totalQty)}</#assign>
<#assign totalAmt>${toNumber(item.amount) + toNumber(totalAmt)}</#assign>
<#assign rowDesc>${rowDesc + "<br/>" + item.description}</#assign>
<#assign itemRate>${item.rate}</#assign>
          
<#if !item_has_next>
<tr>

	<td align="center" colspan="3" line-height="150%">${totalQty}</td>
	<td colspan="12"><span class="itemname">${item.item}</span><br />${rowDesc}</td>
	<td colspan="3"></td>
	<td align="right" colspan="4">${item.rate}</td>
	<td align="right" colspan="4">${totalAmt}</td>
	
</tr>
</#if>
          
<#assign lastItem>${item.item}</#assign>

</#list><!-- end items --></table>

<hr /></#if>
<table class="total" style="width: 100%; margin-top: 10px;"><tr>
	<td colspan="4">&nbsp;</td>
	<td align="right"><b>${record.subtotal@label}</b></td>
	<td align="right">${record.subtotal}</td>
	</tr>
	<tr>
	<td colspan="4">&nbsp;</td>
	<td align="right"><b>${record.taxtotal@label} (${record.taxrate}%)</b></td>
	<td align="right">${record.taxtotal}</td>
	</tr>
	<tr class="totalrow">
	<td background-color="#ffffff" colspan="4">&nbsp;</td>
	<td align="right"><b>${record.total@label}</b></td>
	<td align="right">${record.total}</td>
	</tr></table>
</body>
</pdf>