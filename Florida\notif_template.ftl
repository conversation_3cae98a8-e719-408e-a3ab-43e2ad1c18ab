<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="3">
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    style="float: left; margin: 7px; width: 53%; height: 60%" /> </#if> <span
                                class="nameandaddress">${companyInformation.companyName}</span><br /><span
                                class="nameandaddress">${companyInformation.addressText}</span>
                        </td>
                        <td align="right"><span class="title">${record@title}</span></td>
                    </tr>
                    <tr>
                        <td align="right"><span class="number">#${record.tranid}</span></td>
                    </tr>
                    <tr>
                        <td align="right">${record.trandate}</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="15%" footer="nlfooter" footer-height="10pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader" colspan="6"><b>${record.address@label}</b></td>
                <td class="totalboxtop" colspan="5"><b>${record.total@label?upper_case}</b></td>
            </tr>
            <tr>
                <td class="address" colspan="6">${record.address}</td>
                <td align="right" class="totalboxmid" colspan="5">${record.total}</td>
            </tr>
        </table>
        <#if record.apply?has_content>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start apply sublist -->
                <#list record.apply as apply>
                    <#if apply_index==0>
                        <thead>
                            <tr>
                                <th align="center" colspan="3">Date</th>
                                <th colspan="5">Bill</th>
                                <th colspan="4">Memo</th>
                                <th align="right" colspan="3">Bill Amount</th>
                                <th align="right" colspan="4">Amount</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td align="center" colspan="3" line-height="150%">${apply.applydate}</td>
                        <td colspan="5">${apply.refnum}</td>
                        <td colspan="4">${apply.memo}</td>
                        <td align="right" colspan="3">${apply.total}</td>
                        <td align="right" colspan="4">${apply.amount}</td>
                    </tr>
                </#list><!-- end apply -->
            </table>

            <hr />
        </#if>
        <#if record.credit?has_content>
            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start credit sublist -->
                <#list record.credit as credit>
                    <#if credit_index==0>
                        <thead>
                            <tr>
                                <th align="center" colspan="3">Date</th>
                                <th colspan="4">Reference No</th>
                                <th colspan="5">Applied Bill</th>
                                <th colspan="4">Memo</th>
                                <th align="right" colspan="4">Amount</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td align="center" colspan="3" line-height="150%">${credit.creditdate}</td>
                        <td colspan="4">${credit.refnum}</td>
                        <td colspan="5">${credit.appliedto}</td>
                        <td colspan="4">${credit.memo}</td>
                        <td align="right" colspan="4">${credit.amount}</td>
                    </tr>
                </#list><!-- end credit-->
            </table>

            <hr />
        </#if>
        <table class="total" style="width: 100%; margin-top: 10px;">
            <tr class="totalrow">
                <td background-color="#ffffff" colspan="4">&nbsp;</td>
                <td align="right"><b>${record.total@label}</b></td>
                <td align="right">${record.total}</td>
            </tr>
        </table>
    </body>
</pdf>