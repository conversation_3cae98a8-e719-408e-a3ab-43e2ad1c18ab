// Email Plugin entry point
function process(email) {
	var subject  = email.getSubject();
	var textbody = email.getTextBody();
    var attachments = email.getAttachments();
    for (var indexAtt in attachments)
    {
        var file = attachments[indexAtt];
        file.setFolder(2306); // Please set at this point folder ID you would like to use from your NetSuite File Cabinet
        var id = nlapiSubmitFile(file);
        
        var fileSaved = nlapiLoadFile(id);
        var fileText = fileSaved.getValue();
    }

	nlapiLogExecution('DEBUG', 'Subject', subject);
	nlapiLogExecution('DEBUG', 'Textbody', textbody);
	nlapiLogExecution('DEBUG', 'File Value', fileText);


};