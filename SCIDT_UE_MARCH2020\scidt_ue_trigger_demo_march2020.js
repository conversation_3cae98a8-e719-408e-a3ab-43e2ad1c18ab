/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       21 May 2020     jdgonzal
 *
 */

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @param {String} type Operation types: create, edit, view, copy, print, email
 * @param {nlobjForm} form Current form
 * @param {nlobjRequest} request Request object
 * @returns {Void}
 */
function userEventBeforeLoad(type, form, request){

    form.setScript('customscript15');

    form.addField('custpage_mycustomfield', 'text', 'Test Label').setMandatory(true);
    var selectFieldObj = form.addField('custpage_fakecustomers', 'select', 'Fake Customers', 'customer');

    var sublistObj = form.addSubList('custpage_fakesublist', 'inlineeditor', 'Fake Item Sublist');

    sublistObj.addField('custpage_fakeitemlist', 'select', 'Fake Item', 'item');
    var x = 'YES SIRRRRRR';
    form.addButton('custpage_pressme', 'Press Me Now', "sayHello('"+x+"')");


    // nlapiSetFieldValue('comments', 'value from UE BL');

    // var curId = nlapiGetUser();

    // nlapiLogExecution("DEBUG", "curId", curId);

    // if(type == "edit"){
    //     var recId = nlapiGetRecordId();
    //     nlapiLogExecution("DEBUG", "recId", recId);
    //     if(recId != curId) {
    //         throw nlapiCreateError("ERR-01", "You cannot edit other employees record!");
    //     }
    // }

    // nlapiLogExecution("DEBUG", "DEBUG TEST", "DEBUG START");
    // nlapiLogExecution("AUDIT", "AUDIT TEST", "AUDIT START");
    // nlapiLogExecution("ERROR", "ERROR TEST", "ERROR START");
    // nlapiLogExecution("EMERGENCY", "EMERGENCY TEST", "EMERGENCY START");
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      approve, reject, cancel (SO, ER, Time Bill, PO & RMA only)
 *                      pack, ship (IF)
 *                      markcomplete (Call, Task)
 *                      reassign (Case)
 *                      editforecast (Opp, Estimate)
 * @returns {Void}
 */
function userEventBeforeSubmit(type){
 
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit,
 *                      approve, cancel, reject (SO, ER, Time Bill, PO & RMA only)
 *                      pack, ship (IF only)
 *                      dropship, specialorder, orderitems (PO only) 
 *                      paybills (vendor payments)
 * @returns {Void}
 */
function userEventAfterSubmit(type){
  
}
