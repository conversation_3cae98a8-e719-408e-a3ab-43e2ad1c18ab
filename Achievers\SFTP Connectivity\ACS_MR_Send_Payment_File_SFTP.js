/**
 * @NApiVersion 2.x
 * @NScriptType MapReduceScript
 * @NModuleScope SameAccount
 */
 define(['N/file', "N/sftp", 'N/runtime', 'N/search', 'N/record', 'N/email', 'N/url', 'N/https'],
    function (file, sftp, runtime, search, record, email, url, https) {

        function pad(pad, str, padLeft) {
            if (typeof str === 'undefined')
                return pad;
            if (padLeft) {
                return (pad + str).slice(-pad.length);
            } else {
                return (str + pad).substring(0, pad.length);
            }
        }
        
        function decryptFile(contents) {
            
            var scriptID = 'customscript_15152_encryption_suitelet';
            var deployementID = 'customdeploy_15152_encryption_suitelet';

            var decryptParameters = {
                action : 'decrypt',
                actionInput : contents,
                featureKey : 'NACHA'
            };

            var suiteletURL = url.resolveScript({
                scriptId: scriptID,
                deploymentId: deployementID,
                returnExternalUrl : true
            });

            var response = https.post({
                url: suiteletURL,
                body: decryptParameters
            });
            var body = JSON.parse(response.body);

            if(body.isSuccess){
                return body.actionOutput;
            }
            
            return false;
            

        }

        var pfaIds = [];

        /**
         * Marks the beginning of the Map/Reduce process and generates input data.
         *
         * @typedef {Object} ObjectRef
         * @property {number} id - Internal ID of the record instance
         * @property {string} type - Record type id
         *
         * @return {Array|Object|Search|RecordRef} inputSummary
         * @since 2015.1
         */
        function getInputData() {

            try {

                // retrieve files created today
                var searchObj = search.load({
                    id: 'customsearch_acs_payment_files_created'
                });
        
                return searchObj;
            } catch (e) {
                log.error('getInputData error', e);
            }

        }

        /**
         * Executes when the map entry point is triggered and applies to each key/value pair.
         *
         * @param {MapSummary} context - Data collection containing the key/value pairs to process through the map stage
         * @since 2015.1
         */
        function map(context) {

            try {

                var scriptObj = runtime.getCurrentScript();

                // get PFA and File ID
                var pfaID = context.key;
                var contextValues = JSON.parse(context.value);
                var fileId = contextValues.values.custrecord_2663_file_ref.value;

                // get SFTP Credentials here using script parameters
                var sftpHostKey = scriptObj.getParameter({ name: 'custscript_sftp_host_key' });
                var sftpUsername = scriptObj.getParameter({ name: 'custscript_sftp_username' });
                var sftpPort = scriptObj.getParameter({ name: 'custscript_sftp_port' });
                var sftpPassword = scriptObj.getParameter({ name: 'custscript_sftp_password' });
                var sftpKey = scriptObj.getParameter({ name: 'custscript_sftp_key_id' });
                var sftpServerUrl = scriptObj.getParameter({ name: 'custscript_sftp_server_url' });
                var archiveId = scriptObj.getParameter({ name: 'custscript_archive_id' });

                // load file object
                var origFileObj = file.load({
                    id: fileId
                });

                var fileContent = origFileObj.getContents();
                if(fileContent) {
                           
                    var date = new Date;

                    var dateTime = pad("00", (date.getMonth() + 1), true) + pad("00", date.getDate(), true) + date.getFullYear() + pad("00", date.getHours(), true) + pad("00", date.getMinutes(), true);
                    var fileName = "XML_TEST_" + dateTime + ".xml";

                    // set the name based on bank's requirements and move to 'sent folder'

                    var fileObj = file.create({
                        name: fileName,
                        fileType: file.Type.PLAINTEXT,
                        contents: fileContent,
                        folder: archiveId
                    });

                    var newFileID = fileObj.save();

                    // reload file
                    var fileToBeSentObj = file.load({
                        id: newFileID
                    });

                    log.audit("File Saved", "ID :" + newFileID);
                    
                    log.audit("Start SFTP Transmission", "-------- start SFTP transmission --------");

                    // connect to sftp server using script parameters
                    var sftpConnection = sftp.createConnection({
                        username: sftpUsername,
                        passwordGuid: sftpPassword,
                        keyId: sftpKey,
                        url: sftpServerUrl,
                        port: Number(sftpPort),
                        hostKey: sftpHostKey
                    });

                    log.debug('SFTP Connection', sftpConnection);

                    // send file to server
                    sftpConnection.upload({
                        file: fileToBeSentObj,
                        directory: '/',
                        replaceExisting: true
                    });
                    
                    log.audit('File has been transmitted successfully!', { fileId: fileToBeSentObj.id, fileName: fileToBeSentObj.name });
                            
                    record.submitFields({
                        type: 'customrecord_2663_file_admin',
                        id: pfaID,
                        values: {
                            custrecord_acs_file_sent: true,
                            custrecord_acs_date_sent: new Date()
                        }
                    });

                    pfaIds.push(pfaID);

                }
            } catch(e) {
                log.error('Map - Error during sftp upload', e);
            }
        }

        /**
         * Executes when the reduce entry point is triggered and applies to each group.
         *
         * @param {ReduceSummary} context - Data collection containing the groups to process through the reduce stage
         * @since 2015.1
         */
        function reduce(context) {

        }


        /**
         * Executes when the summarize entry point is triggered and applies to the result set.
         *
         * @param {Summary} summary - Holds statistics regarding the execution of a map/reduce script
         * @since 2015.1
         */
        function summarize(summary) {
                
            // var scriptObj = runtime.getCurrentScript();
            // var emailSuccess = scriptObj.getParameter({ name: 'custscript_email_success_transmit' });
            // var emailAuthor = scriptObj.getParameter({ name: 'custscript_email_author' });

            // if(emailSuccess && emailAuthor) {

            //     var emailBody = "Good Day!<br><br>Payment has been successfully transmitted. Transmitted files can be seen in the File Cabinet under SFTP Payment File Archive.<br>";

            //     emailBody += '<table border="1">';
            //     var hasCompleted = 0;
            //     summary.mapSummary.keys.iterator().each(function (key, executionCount, completionState){

            //         log.debug('summarize', key);

            //         if (completionState === 'COMPLETE'){
            //             emailBody += '<li>https://<acct_id>.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=168&id=' + key + '</li>';
            //             hasCompleted++;
            //         }
                    
            //         return true;
                    
            //     });

            //     emailBody += '</table>';
                
            //     try {
            //         if(hasCompleted) {
            //             email.send({
            //                 author: emailAuthor,
            //                 recipients: emailSuccess,
            //                 subject: 'Payment Transmission Successful',
            //                 body: emailBody
            //             });
            //         }
            //     } catch (e) {
            //         log.error('Summarize - Error while trying to send email', e);
            //     }
            // }

        }

        return {
            getInputData: getInputData,
            map: map,
            reduce: reduce,
            summarize: summarize
        };

    });