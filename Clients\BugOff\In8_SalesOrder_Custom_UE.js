/**
 * Sales Order customization
 * 
 * Version    Date            Author           Remarks
 * 1.00       24 Aug 2017     Marcel P		   Initial Version
 *
 */

/**
 * @appliedtorecord salesorder
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      
 * @returns {Void}
 */
function beforeSubmit(type) {
    
    try {        
        // On creation. WooCommerce orders
        if (type == 'create' && nlapiGetFieldValue('custbody_in8_wc_order_id')) {        
            
        	// If created from is an Estimate
            var createdFrom = nlapiGetFieldValue('createdfrom');
            
            if (createdFrom) {
            	if (nlapiLookupField('transaction', createdFrom, 'recordtype') == 'estimate') {
            		            		
            		nlapiLogExecution('DEBUG', 'In8', 'Set order status to Pending approval.');
            		
            		// Set to Pending approval
            		nlapiSetFieldValue('orderstatus', 'A'); 
            	}
            }
        }
        if (type == 'edit' && nlapiGetFieldValue('custbody_in8_wc_order_id')) {        
            
            if (nlapiGetFieldValue('paymentmethod') && nlapiGetFieldValue('orderstatus') == 'A') {
            	
            	nlapiLogExecution('DEBUG', 'In8', 'Set order status to approved.');
            	
            	// Set to Pending fulfillment
            	nlapiSetFieldValue('orderstatus', 'B');             	
            }
        }
        
    } catch(e) {
        nlapiLogExecution('ERROR', 'Error', 'Error executing script. ' + e);
    }
}
