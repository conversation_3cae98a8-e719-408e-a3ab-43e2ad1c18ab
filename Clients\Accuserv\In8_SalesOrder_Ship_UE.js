/**
 * Sales Order - Check Ship Date Field - User Event
 * 
 * Version    Date            Author           Remarks
 * 1.00       19 Aug 2016     <PERSON> (In8)   Initial Version
 *
 */

/**
 * @appliedtorecord Sales Order
 * 
 * @param {String} type Operation types: create, edit, delete, xedit,
 *                      
 * @returns {Void}
 */
function afterSubmit(type) {
  
	try {					
		if (type == 'edit') {			
			var shipDate = nlapiGetNewRecord().getFieldValue('shipdate'),
				shipDateOld = nlapiGetOldRecord().getFieldValue('shipdate');
		
			// Checks if the ship date is different when editing a record
			if (shipDate != shipDateOld) {
				
				nlapiLogExecution('DEBUG', 'In8', 'Record Id: ' + nlapiGetRecordId());
				
				nlapiLogExecution('DEBUG', 'In8', 'Ship date is different. Ship Date: ' + shipDate + ' - Old Ship Date: ' + shipDateOld);
				
				// Send an email
				sendEmail();	
				
				nlapiLogExecution('DEBUG', 'In8', '-----------------------------------------------------------------');
			}	
		}		
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error ',( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
	}
}

/**
 * Send Email to the sales rep when the ship date gets changed
 *                      
 * @returns {Void}
 */
function sendEmail() {
	
	var message,
		shipDate = nlapiGetNewRecord().getFieldValue('shipdate'),
		shipDateOld = nlapiGetOldRecord().getFieldValue('shipdate'),
		tranId = nlapiGetFieldValue('tranid'),
		entity = nlapiGetNewRecord().getFieldValue('entity'),
		entityText = nlapiGetNewRecord().getFieldText('entity'),
		url = 'https://system.na1.netsuite.com/app/accounting/transactions/salesord.nl?id=' + nlapiGetRecordId(),
		urlCustomer = 'https://system.na1.netsuite.com/app/common/entity/custjob.nl?id=' + entity;
	
	// Build URLs
	url = '<a href="' + url + '" target="_blank">' + tranId + '</a>';
	urlCustomer = '<a href="' + urlCustomer + '" target="_blank">' + entity + ' - ' + entityText + '</a>';
		
	// Body Message
	message = 'The ship date on Sales Order <B>' + tranId + '<B> has been changed from "' + shipDateOld + '" to "' + shipDate + '". <BR><BR>' + 
				'Click here to pull up the order in Netsuite ' + url + '<BR><BR>' + 
				'Please make a note of this change and communicate as needed to the customer ' + urlCustomer + '. <BR><BR>' +
				'Thanks, <BR>' +  
				'Netsuite System Admin <BR><BR>';	
	
	// Check if the Sales Team sublist has employees
	if (nlapiGetNewRecord().getLineItemCount('salesteam') <= 0) {
		// Check the Sales Rep email		
		if (nlapiGetNewRecord().getFieldValue('salesrep')) {
			var employeeRecord = nlapiLookupField('employee', nlapiGetNewRecord().getFieldValue('salesrep'), ['email', 'custentity_in8_unsubscribe_notifications']);
			
			sendEmployeeEmail(employeeRecord, message, nlapiGetNewRecord().getFieldValue('salesrep'));	
		}				
	} else {
		for (var i = 1; i <= nlapiGetNewRecord().getLineItemCount('salesteam'); i++) {		
			// Check the Sales Rep email
			if (nlapiGetNewRecord().getLineItemValue('salesteam', 'employee', i)) {
				var employeeRecord = nlapiLookupField('employee', nlapiGetNewRecord().getLineItemValue('salesteam', 'employee', i), ['email', 'custentity_in8_unsubscribe_notifications']);
				
				sendEmployeeEmail(employeeRecord, message, nlapiGetNewRecord().getLineItemValue('salesteam', 'employee', i));	
			}			
		}	
	}	
}

/**
 * Send the email
 * 
 * @param {String} employeeEmail Employee Email
 * @param {String} message Message to be sent
 *                      
 * @returns {Void}
 */
function sendEmployeeEmail(employeeRecord, message, internalId) {
	
	if (employeeRecord.email) {		
		
		if (employeeRecord.custentity_in8_unsubscribe_notifications != 'T') {
			
			//var url = 'https://system.na1.netsuite.com/app/site/hosting/scriptlet.nl?script=673&deploy=1&internalid=' + internalId;
			
			// Unsubscribe link
			var url = 'https://system.netsuite.com/app/site/hosting/scriptlet.nl?script=255&deploy=1&internalid=' + internalId;			
				
			message += 'Click <a href="' + url + '">here</a> to unsubscribe.';
						
			nlapiLogExecution('DEBUG', 'In8', 'Sending email to ' + employeeRecord.email);
			
			nlapiLogExecution('DEBUG', 'In8', 'Message to be sent: ' + message);			
			
			// Send the email
			nlapiSendEmail(nlapiGetUser(), employeeRecord.email, 'NetSuite Sales Order Ship Date changed', message, null, null, null, null, null, null, null);	
		} else {
			nlapiLogExecution('DEBUG', 'In8', 'User opted to not receive notifications.');
		}							
	} else {
		nlapiLogExecution('DEBUG', 'In8', 'Sales Rep email is blank. Do not send the email');
	}
}
