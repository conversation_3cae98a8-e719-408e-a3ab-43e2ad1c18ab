/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/record', 'N/search', 'N/workflow'], function(record, search, workflow) {

    function getInputData() {
        var mySearch = search.load({
            id: 'customsearch_acs_bill_payments_3_bsns_do'
        });

        return mySearch;
    }

    function map(context) {
        // accept data from previous stage
        var keyFromInput = context.key;
        var valueFromInput = context.value;

        var recordObj = JSON.parse(valueFromInput);
        try {
            context.write({
                key: recordObj.id,
                value: recordObj
            });
    
        } catch (errorObj) {
            log.debug({
                title: 'error 1',
                details: errorObj
            });
        }
        // pass data to the next stage
    }

    function reduce(context) {
        
        var reduceKey = context.key;

        try {
            workflow.trigger({
                recordType: record.Type.VENDOR_PAYMENT,
                recordId: reduceKey,
                workflowId: 'customworkflow_acs_bill_payment_appr',
                actionId: 'workflowaction13253'
            });
            log.audit("Audit", "Rejected Vendor Payment ID: " + reduceKey);
        } catch (e) {
            log.debug("Reduce - Error", e);
        }
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
