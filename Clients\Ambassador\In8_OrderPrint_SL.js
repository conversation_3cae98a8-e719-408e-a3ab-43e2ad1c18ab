/**
 * Order History Suitelet
 * 
 * Version    Date            Author           Remarks
 * 1.00       05 Sep 2019     <PERSON>   Initial Version
 *
 */

//var secretKey = 'ac8420dd-5069-4877-8d19-c017c12706b2';
var secretKey = 'c42bbaaf-d96a-4d0d-a30b-5704dae8986e';

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response) {

    if (request.getMethod() == 'GET') {
        app.handleGet();
    } else {
        app.handlePost();
    }
}

var app = (function () {

    var FORM_NAME = 'Orders History', // Form Name
        LIST_NAME = 'Orders History', // List Name
        SUBMIT_BUTTON = 'Submit', // Submit button caption
        MAX_RECORDS = 1000, // Maximum number of records to display on the sublist
        MARKALL_ENABLED = false; // Mark all option enabled

    //var isAdmin = false;
    var hasPermissions;
    /**
     * Handles Suitelet GET Method
     * 
     * @returns {Void}
     */
    var handleGet = function () {

        var form,
            subList,
            searchResults;

        try {
            var userId = request.getParameter('userid');
            var hash = request.getParameter('hash');
            var internalId = request.getParameter('custpage_internalid');
            var download = request.getParameter('download');
            var packingSlip = false;

            if (request.getParameter('packingSlip')) {
                download = true;
                packingSlip = true;
            }
            var hashCompare = String(CryptoJS.SHA256(secretKey + internalId));

            if (hash != hashCompare) {
                displayMessage('Invalid access credentials.');
                return;
            }

            var tran = nlapiLookupField('transaction', internalId, ['recordtype', 'tranid']);

            if (download) {
                response.setContentType('PDF', (packingSlip ? 'packingslip' : tran.recordtype) + '_' + tran.tranid + '.pdf');
            } else {
                response.setContentType('PDF', tran.recordtype + '_' + tran.tranid + '.pdf', 'inline');
            }
            response.write(printReport(internalId));
        } catch (e) {
            displayMessage('An error occurred. ' + (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
        }
    };

    function printReport(ifId) {

        var printType = 'TRANSACTION';

        if (request.getParameter('packingSlip')) {
            printType = 'PACKINGSLIP';

            // Find the related fulfillment
            var search = nlapiSearchRecord('itemfulfillment', null, [
                new nlobjSearchFilter('createdfrom', null, 'anyof', ifId)
            ], [
                new nlobjSearchColumn('internalid')
            ]) || [];

            // TODO: handle multiple fulfillments
            for (var i = 0; i < search.length; i++) {
                ifId = search[i].getValue('internalid');
            }
        }

        var file = nlapiPrintRecord(printType, ifId, 'PDF');

        return file.getValue();
    }

    /**
     * Handles Suitelet POST method
     * 
     * @returns {Void} 
     */
    var handlePost = function () {

        for (var i = 1; i <= request.getLineItemCount('custpage_sublist'); i++) {
            if (request.getLineItemValue('custpage_sublist', 'custpage_selected', i) == 'T') {
                // TODO: Process the line
            }
        }

        // Reloads window
        handleGet();
    };

    /**
     * Displays a message
     * 
     * @param {String} message Message
     * @returns {Void}
     */
    var displayMessage = function (message) {

        // Create a NetSuite form
        var form = nlapiCreateForm(FORM_NAME, false),
            html = message;

        // Add a new HTML field to display the HTML contents
        field = form.addField('file', 'inlinehtml', 'label');
        field.setLayoutType('outsidebelow');
        field.setDefaultValue('<font size="2pt">' + html + '</font>');

        form.addButton('custombutton_back', 'Back', 'window.history.back()');

        response.writePage(form);
    };

    return {
        handleGet: handleGet,
        handlePost: handlePost
    };
})();