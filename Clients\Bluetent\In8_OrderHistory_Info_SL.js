var secretKey = 'c42bbaaf-d96a-4d0d-a30b-5704dae8986e';

function suitelet(request, response) {
    var form = nlapiCreateForm('Orders History Suitelet');

    if (request.getMethod() == "GET") {
        var message = '<html><body><br/><span style="font-size: 15px">Base URL:<br/> {externalurl}<br/><br/>' +
            'Parameters:<br/>-\t<b>userid</b> : Contact Internal Id<br/>' +
            '-\t<b>custpage_customer</b> : Customer Internal Id<br/>' +
            '-\t<b>custpage_type</b> : NetSuite transaction Type - SalesOrd (Sales Orders), CustInvc (Invoices), CustCred (Credit Memos), CashSale (Cash Sales)<br/>' +
            '   i.e. for Invoice add the following to the base URL: <i>&custpage_type=CustInvc</i><br/>' +
            '-\t<b>hash</b>: String <span style="color:red">"secretkey"</span>+<span style="color:blue">"userid"</span>+<span style="color:blue">"custpage_customer"</span> encrypted on SHA256 method <br/><br/>Secret Key: <span style="color:green; font-weight: bold">“' + secretKey + '”</span><br/><br/>' +
            '</span></body></html>';

        var baseurl = nlapiResolveURL('SUITELET', 'customscript_in8_order_history_sl', 'customdeploy_in8_order_history_sl', true);

        message = message.replace(/{externalurl}/g, baseurl);

        form.addFieldGroup('custpage_instructions', 'Instructions');

        form.addField('custpage_info', 'inlinehtml', '', '', 'custpage_instructions').setDefaultValue(message);

        form.addFieldGroup('custpage_generate', 'Enter the fields below to generate a valid Sample URL');

        var fld = form.addField('custpage_customer', 'select', 'Customer', 'customer', 'custpage_generate');
        fld.setMandatory(true);

        form.addField('custpage_userid', 'select', 'Contact', 'contact', 'custpage_generate');

        form.addSubmitButton('Generate Link');

        response.writePage(form);
    } else {
        //var secretKey = 'ac8420dd-5069-4877-8d19-c017c12706b2';
        //var secretKey = 'c42bbaaf-d96a-4d0d-a30b-5704dae8986e';

        var userId = request.getParameter('custpage_userid');
        if (!userId) {
            userId = 0
        }
        var hash = String(CryptoJS.SHA256(secretKey + userId + request.getParameter('custpage_customer')));

        var message = '<html><body><br/><span style="font-size: 15px">Base URL:<br/> {externalurl}<br/><br/>' +
            'Parameters:<br/>-\t<b>userid</b> : Contact Internal Id<br/>' +
            '-\t<b>custpage_customer</b> : Customer Internal Id<br/>' +
            '-\t<b>custpage_type</b> : NetSuite transaction Type - SalesOrd (Sales Orders), CustInvc (Invoices), CustCred (Credit Memos), CashSale (Cash Sales)<br/>' +
            '   i.e. for Invoice add the following to the base URL: <i>&custpage_type=CustInvc</i><br/>' +
            '-\t<b>hash</b>: String <span style="color:red">"secretkey"</span>+<span style="color:blue">"userid"</span>+<span style="color:blue">"custpage_customer"</span> encrypted on SHA256 method <br/><br/>Secret Key: <span style="color:green; font-weight: bold">“' + secretKey + '”</span><br/><br/>' +
            'Sample URL: {externalurl}&userid={userid}&custpage_customer={customer}&custpage_type=CustInvc&hash={hash}' +
            '<br/></span></body></html>';

        var baseurl = nlapiResolveURL('SUITELET', 'customscript_in8_order_history_sl', 'customdeploy_in8_order_history_sl', true);

        message = message.replace(/{externalurl}/g, baseurl);
        message = message.replace(/{userid}/g, userId);
        message = message.replace(/{customer}/g, request.getParameter('custpage_customer'));
        message = message.replace(/{hash}/g, hash);

        form.addField('custpage_info', 'inlinehtml', '').setDefaultValue(message);

        response.writePage(form);
    }
}