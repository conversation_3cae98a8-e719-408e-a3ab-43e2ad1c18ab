/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/record', 'N/search'], function(record, search) {

    function getInputData() {
        var mySearch = search.load({
            id: 'customsearch2142'
        });
        
        var resultArray = [];            
        var searchResult = mySearch.run().getRange({ start: 0, end: 500 });

        // iterate each result
        for( var i=0; i < searchResult.length; i++ ) {
            resultArray.push({
                id: searchResult[i].getValue({ name: 'internalid' })
            });    
        }
        return resultArray;

    }

    function map(context) {
        try {
            var orderId = JSON.parse(context.value).id;
            log.debug('orderId', orderId);
            var orderObj = record.load({
                type: 'customrecord_celigo_etail_odr_trnsaction',
                id: orderId
            });
            
            var soId = orderObj.getValue({ fieldId: 'custrecord_celigo_etail_orderid' });
            var amount = orderObj.getValue({ fieldId: 'custrecord_celigo_etail_trans_amount' });

            var soObj = record.load({
                id: soId,
                type: record.Type.SALES_ORDER
            });

            var line = soObj.findSublistLineWithValue({
                sublistId: 'item',
                fieldId: 'item',
                value: 57406
            });

            var giftCardValue = soObj.getSublistValue({
                sublistId: 'item',
                fieldId: 'amount',
                line: line
            });

            if(parseFloat(giftCardValue) != 0.00) {
                var finalAmt = parseFloat(amount) + Math.abs(giftCardValue);
                finalAmt = (finalAmt * -1);
            } else {
                var finalAmt = (amount * -1);
            }

            soObj.setSublistValue({
                sublistId: 'item',
                fieldId: 'amount',
                line: line,
                value: finalAmt
            });

            var soId = soObj.save({
                enableSourcing: false,
                ignoreMandatoryFields: true
            });

            log.audit('Sales Order Updated', 'Sales Order Internal ID - ' + soId);
        } catch (e) {
            log.error('An Error Occured', e);
        }
    }

    function reduce(context) {
        
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
