/**
 *@NApiVersion 2.x
 *@NScriptType WorkflowActionScript
 */
define(['N/email', './crypto/crypto-js.js', 'N/runtime', 'N/file', 'N/url', 'N/render'], function(email, crypto, runtime, file, url, render) {

    var APPROVER_EMAIL;
    var TRANSACTION_NO;
    var RECID;
    var APPROVE_ID;
    var REJECT_ID;
    var REQUESTOR;

    /**
     *
     * loads the content the pass phrase file.
     * 
     */
    function getPassphrase() {
        var pFile = file.load('SuiteScripts/pp.txt')
        return pFile.getContents();
    }

    /**
     * 
     * @param {string} type - "approve" or "reject"
     * 
     * generates the URL for the approval suitelet
     * 
     */
    function generateURL(type) {
        
        var PP = getPassphrase();

        var ecnrApprovalType = crypto.AES.encrypt(type, PP).toString();
        var encrEmail = crypto.AES.encrypt(APPROVER_EMAIL, PP).toString();
        var encrRec = crypto.AES.encrypt(RECID.toString(), PP).toString();
        if(type == 'approve') {
            var encrAction = crypto.AES.encrypt(APPROVE_ID, PP).toString();
        }  else {
            var encrAction = crypto.AES.encrypt(REJECT_ID, PP).toString();
        }

        var appProcessSuitelet = url.resolveScript({
            scriptId: "customscript_acs_sl_po_approval",
            deploymentId: 'customdeploy_acs_sl_po_approval',
            params: {
                em: encodeURIComponent(encrEmail),
                re: encodeURIComponent(encrRec),
                apt: encodeURIComponent(ecnrApprovalType),
                ac: encodeURIComponent(encrAction),
            },
            returnExternalUrl: true
        });

        log.debug("URL", appProcessSuitelet);

        return appProcessSuitelet;

    }

    /**
     * 
     * @param {object} recordObj - object record gotten from scriptContext
     * 
     * Self explanatory. Generates the email body.
     * 
     */
    function generateEmailBody(recObj) {

        var body = 'Good day! <br />';
        body += 'Purchase request #' + TRANSACTION_NO + ' has been submitted for your approval <br /><br />';
        body += '<a href="'+generateURL('approve')+'">Please click here to approve</a><br />';
        body += '<a href="'+generateURL('reject')+'">Please click here to reject</a><br />';

        var transactionHTML = renderTransactionAsHTML(recObj);

        body += transactionHTML;

        return body;
    }
    
    /**
     * 
     * @param {object} recordObj - object record gotten from scriptContext
     * 
     * Generates the html for the email body.
     * 
     */
    function renderTransactionAsHTML(recordObj) {

        var transactionHTML = render.transaction({
            entityId: recordObj.id,
            printMode: render.PrintMode.HTML,
        }).getContents();

        // render as string
        return transactionHTML;

    }

    function onAction(scriptContext) {
        var recObj = scriptContext.newRecord;
        RECID = recObj.id;

        log.debug('test', runtime.getCurrentUser().role);

        // get the necessary data from the parameters set in the action (can be found on workflow states)
        APPROVER_EMAIL = runtime.getCurrentScript().getParameter('custscript_approver_email');
        TRANSACTION_NO = runtime.getCurrentScript().getParameter('custscript_po_tran_no');
        REQUESTOR = runtime.getCurrentScript().getParameter('custscript_requestor');
        APPROVE_ID = runtime.getCurrentScript().getParameter('custscript_approve_action_id');
        REJECT_ID = runtime.getCurrentScript().getParameter('custscript_reject_action_id');

        // generate the file for the email
        // var transactionFile = createAttachment(recObj);

        var emailBody = generateEmailBody(recObj);
        email.send({
            author: 7619,
            recipients: APPROVER_EMAIL,
            subject: 'Purchase Request: ' + TRANSACTION_NO + ' Requires Your Approval',
            body: emailBody,
            relatedRecords: {
                transactionId: recObj.id
            }
        });
    }

    return {
        onAction: onAction
    }
});
