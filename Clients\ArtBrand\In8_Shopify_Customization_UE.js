var beforeSubmit = function () {
    // try {
        var ShopifyOrderId = nlapiGetFieldValue('custbody_in8_shop_id');
        var shopifySettingId = nlapiGetFieldValue('custbody_in8_shop_setting');

        nlapiLogExecution('DEBUG', 'ShopifyOrderId: ', ShopifyOrderId);
        nlapiLogExecution('DEBUG', 'shopifySettingId: ', shopifySettingId);
        if (!ShopifyOrderId || !shopifySettingId) return;

        var domainName = nlapiLookupField('customrecord_in8_shop_settings',
            shopifySettingId,
            'custrecord_in8_shopify_domain_ref');

        var shopifyOrderLog = nlapiSearchRecord('customrecord_in8_shopify_requests', null,
            [new nlobjSearchFilter('custrecord_in8_shopify_req_id', null, 'is', ShopifyOrderId),
                new nlobjSearchFilter('custrecord_in8_shopify_domain', null, 'is', domainName)
            ],
            new nlobjSearchColumn('custrecord_in8_shop_req_body')) || [];

        if (shopifyOrderLog.length < 1) return;

        var shopifyOrderRequestBody = shopifyOrderLog[0].getValue('custrecord_in8_shop_req_body');

        nlapiLogExecution('DEBUG', 'shopifyOrderRequestBody', shopifyOrderRequestBody);

        if (!shopifyOrderRequestBody) return;

        var jsonBody = JSON.parse(shopifyOrderRequestBody);
        var orderLines = jsonBody.line_items || [];

        if (domainName == 'shopartofentertainment.myshopify.com') {
            if (nlapiGetFieldValue('custbody_abs_enteredbyrep')) {
                nlapiSetFieldValue('custbody_entry_source', 'phone');
            } else {
                nlapiSetFieldValue('custbody_entry_source', 'web');
            }
            if (type == 'create') {
                for (var i = orderLines.length - 1; i > -1; i--) {
                    if (!orderLines[i].properties) continue;

                    for (var j = 0; j < orderLines[i].properties.length; j++) {
                        if (orderLines[i].properties[j].name &&
                            orderLines[i].properties[j].name.indexOf('Option') > -1) {
                            if (!orderLines[i].properties[j].value) continue;

                            nlapiLogExecution('DEBUG', 'finding NetSuite item with option value: ' + orderLines[i].properties[j].value);

                            var frameItemToAdd = nlapiSearchRecord('item', null,
                                new nlobjSearchFilter('itemid', null, 'is', orderLines[i].properties[j].value)) || [];

                            if (frameItemToAdd.length < 1) {
                                throw new Error('Item not found in NetSuite with Option value: ' + orderLines[i].properties[j].value);
                            } else {
                                frameItemToAdd = frameItemToAdd[0].getId();
                                for (var line = 1; line <= nlapiGetLineItemCount('item'); line++) {
                                    nlapiLogExecution('DEBUG', 'shopify line id from the order',
                                        nlapiGetLineItemValue('item', 'custcol_in8_shopify_line_id', line));

                                    nlapiLogExecution('DEBUG', 'shopify line id from the payload',
                                        orderLines[i].id);

                                    if (nlapiGetLineItemValue('item', 'custcol_in8_shopify_line_id', line) == orderLines[i].id) {
                                        try {
                                            nlapiLogExecution('DEBUG', 'inserting frame item at line sequence# ' + (line + 1));
                                            nlapiInsertLineItem('item', line + 1);
                                            nlapiLogExecution('DEBUG', 'inserted a new line');

                                            nlapiLogExecution('DEBUG', 'adding frame item', frameItemToAdd);
                                            nlapiSetLineItemValue('item', 'item', line + 1, frameItemToAdd);

                                            nlapiSetLineItemValue('item', 'quantity', line + 1, 1);
                                            nlapiSetLineItemValue('item', 'price', line + 1, -1);
                                            nlapiSetLineItemValue('item', 'rate', line + 1, 0);

                                            // nlapiSetCurrentLineItemValue('item', 'item', frameItemToAdd);

                                            // nlapiSetCurrentLineItemValue('item', 'quantity', 1);
                                            // nlapiSetCurrentLineItemValue('item', 'price', -1);
                                            // nlapiSetCurrentLineItemValue('item', 'rate', 0);

                                            nlapiCommitLineItem('item');
                                            nlapiLogExecution('DEBUG', 'Committed the line.');
                                        } catch (ex) {
                                            nlapiLogExecution('DEBUG', 'Exception while adding line', ex);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        nlapiLogExecution('DEBUG', 'orderLines', JSON.stringify(orderLines));
        var lineIndex_personalizeData = {};

        for (var i = 0; i < orderLines.length; i++) {

            if (!orderLines[i].properties) continue;

            var sku = orderLines[i].sku;

            for (var j = 0; j < orderLines[i].properties.length; j++) {
                if (orderLines[i].properties[j].name &&
                    orderLines[i].properties[j].name.indexOf('Personalize') > -1) {

                    if (!lineIndex_personalizeData[sku]) {
                        lineIndex_personalizeData[sku] = [];
                    }

                    lineIndex_personalizeData[sku].push(orderLines[i].properties[j].name +
                        ' : ' + orderLines[i].properties[j].value);
                }
            }
        }

        nlapiLogExecution('DEBUG', 'Personalize data: ', JSON.stringify(lineIndex_personalizeData));

        if (type == 'create' || type == 'edit') {

            for (currentSKU in lineIndex_personalizeData) {

                var found = false;

                for (var i = 1; i <= nlapiGetLineItemCount('item'); i++) {

                    var sku = nlapiGetLineItemValue('item', 'custcol_abs_item_name_copy', i);

                    if (sku == currentSKU) {
                        //if (!lineIndex_personalizeData[sku]) continue;

                        nlapiSelectLineItem('item', i);

                        for (var j = 0; j < lineIndex_personalizeData[sku].length; j++) {
                            var fieldToSet = 'custcol_personalization_0' + (j + 1);
                            var valueToSet = lineIndex_personalizeData[sku][j];

                            nlapiLogExecution('DEBUG', 'fieldToSet and valueToSet ',
                                'fieldToSet: ' + fieldToSet + ' | valueToSet: ' + valueToSet);

                            nlapiSetCurrentLineItemValue('item', fieldToSet, valueToSet);
                        }

                        nlapiCommitLineItem('item');

                        found = true;
                    }
                }
                if (!found) {
                    // Throw an error
                    throw new Error('SKU ' + currentSKU + ' does not match any line items for personalization. Please check Shopify IDs on the NetSuite Item against Shopify.');
                }
            }
        }
    // } catch (ex) {
    //     nlapiLogExecution('DEBUG', 'ex', ex);
    // }
}