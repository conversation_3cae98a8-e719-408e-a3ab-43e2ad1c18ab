/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/runtime', 'N/error'], function(runtime, error) {

	function saveRecord(context) {
		var currRec = context.currentRecord;

		if(currRec.getValue({ fieldId: 'custbody_acs_custom_next_approver' }) == runtime.getCurrentUser().id){
			throw error.create({
				name: 'SAME_USER_AND_APPROVER',
				message: 'The current logged in user cannot be the approver of this transaction.'
			});
		}

		return true;
	}

	return {
		saveRecord: saveRecord
	}
});
