/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/ui/serverWidget', 'N/search', 'N/query', 'N/record'], function(serverWidget, search, query, record) {

    
    function getDiscount(transactionId) {

        var sqlQueryData = "SELECT custbody5 FROM transaction WHERE id = ?";
        var resultSet = query.runSuiteQL({
            query: sqlQueryData,
            params: [transactionId]
        });

        var results = resultSet.results;
        return results[0].values[0];
    }

    function beforeLoad(context) {
        if(context.type == context.UserEventType.EDIT || context.type == context.UserEventType.CREATE || context.type == context.UserEventType.VIEW) {

            var formObj = context.form;
            
            // START - CREATE DEDUCTIONS SUBTAB //

                var deductionSublist = formObj.addSublist({
                    id : 'custpage_deductions',
                    type : serverWidget.SublistType.INLINEEDITOR,
                    label : 'Deduction',
                    tab: 'applications'
                });

                var expenseField = deductionSublist.addField({
                    id: 'deduction_expense_account',
                    label: 'Expense Account',
                    type: serverWidget.FieldType.SELECT
                });

                deductionSublist.addField({
                    id: 'deduction_description',
                    label: 'Deduction Description',
                    type: serverWidget.FieldType.TEXTAREA
                });
                
                deductionSublist.addField({
                    id: 'deduction_amount',
                    label: 'Amount',
                    type: serverWidget.FieldType.CURRENCY
                });

                // get expense accounts
                var expenseAcctSearch = search.load({
                    id: 'customsearch_acs_expense_accounts'
                });

                // add expense accounts in select       
                expenseField.addSelectOption({value: '', text: '' });
                expenseAcctSearch.run().each(function(result){
                    expenseField.addSelectOption({
                        value: result.getValue({ name: "internalid" }),
                        text: result.getValue({ name: "displayname" })
                    });

                    return true;
                });

            // END - CREATE DEDUCTIONS SUBTAB //

            // START - CREATE DEDUCTION TOTAL/ENTERED TAB //

                // var totalDeductionField = formObj.addField({
                //     id: 'custpage_total_deductions',
                //     label: 'Total Deductions',
                //     type: serverWidget.FieldType.CURRENCY,
                //     container: 'applications'
                // });

                // totalDeductionField.updateDisplayType({
                //     displayType : serverWidget.FieldDisplayType.INLINE
                // });

                var enteredDeductionField = formObj.addField({
                    id: 'custpage_entered_deductions',
                    label: 'Deductions Entered',
                    type: serverWidget.FieldType.CURRENCY,
                    container: 'applications'
                });

                enteredDeductionField.updateDisplayType({
                    displayType : serverWidget.FieldDisplayType.INLINE
                });

            // END - CREATE DEDUCTION TOTAL/ENTERED TAB //

            // START - POPULATE TOTAL DEDUCTIONS //

                // var recObj = context.newRecord;
                // var lineCount = recObj.getLineCount({ sublistId: 'apply' });
                // var totalDeductions = 0.00;
                // for(var i = 0; i < lineCount; i++) {
                //     var applied = recObj.getSublistValue({ sublistId: 'apply', fieldId: 'apply', line: i });
                //     if(!applied) continue; // don't do any work if the line is not applied
                //     var transactionId = recObj.getSublistValue({ sublistId: 'apply', fieldId: 'internalid', line: i });
                //     totalDeductions += getDiscount(transactionId);
                // }
                // totalDeductionField.defaultValue = totalDeductions;

            // END - POPULATE TOTAL DEDUCTIONS //

            // START - POPULATE DEDUCTIONS SUBLIST //
                
                var recObj = context.newRecord;
                var deductionsData = recObj.getValue({ fieldId: 'custbody_deduction_data' });
                var totalEnteredDeductions = 0.00;
                if(deductionsData) {
                    var parsedData = JSON.parse(deductionsData);
                    for(var i = 0; i < parsedData.length; i++) {
                        var deductionLine = parsedData[i];

                        deductionSublist.setSublistValue({
                            id: 'deduction_expense_account',
                            line: i,
                            value: deductionLine.account
                        });

                        deductionSublist.setSublistValue({
                            id: 'deduction_description',
                            line: i,
                            value: deductionLine.description
                        });
                        deductionSublist.setSublistValue({
                            id: 'deduction_amount',
                            line: i,
                            value: deductionLine.amount
                        });
                        totalEnteredDeductions += Number(deductionLine.amount);
                    }

                    enteredDeductionField.defaultValue = totalEnteredDeductions;

                // END - POPULATE DEDUCTIONS SUBLIST //

            }
        }
    }

    function beforeSubmit(context) {        

    }

    function afterSubmit(context) {
        
        if(context.type == context.UserEventType.EDIT || context.type == context.UserEventType.CREATE) {
            var newRecObj = context.newRecord;
            var deductionLineCount = newRecObj.getLineCount({ sublistId: 'custpage_deductions' });
            var deductions = [];
            for(var i = 0; i < deductionLineCount; i++) {
                var account = newRecObj.getSublistValue({ sublistId: 'custpage_deductions', fieldId: 'deduction_expense_account', line: i });
                var description = newRecObj.getSublistValue({ sublistId: 'custpage_deductions', fieldId: 'deduction_description', line: i });
                var amount = newRecObj.getSublistValue({ sublistId: 'custpage_deductions', fieldId: 'deduction_amount', line: i });

                deductions.push({
                    account: account,
                    description: description,
                    amount: amount
                });

            }

            record.submitFields({
                type: newRecObj.type,
                id: newRecObj.id,
                values: {
                    custbody_deduction_data: JSON.stringify(deductions),
                },
                options: {
                    enableSourcing: false,
                    ignoreMandatoryFields : true
                }
            });
        }        

    }

    return {
        beforeLoad: beforeLoad,
        beforeSubmit: beforeSubmit,
        afterSubmit: afterSubmit
    }
});
