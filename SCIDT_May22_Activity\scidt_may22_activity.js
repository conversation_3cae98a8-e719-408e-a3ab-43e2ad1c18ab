/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       21 May 2020     jdgonzal
 *
 */

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 *   
 * @param {String} type Operation types: create, edit, view, copy, print, email
 * @param {nlobjForm} form Current form
 * @param {nlobjRequest} request Request object
 * @returns {Void}
 */
function userEventBeforeLoad(type, form, request){

    if(type == 'create'){
        var fieldObj = form.getField('supervisor');
        fieldObj.setMandatory(true);
        nlapiSetFieldValue('custentity_employee_status', 1);
    }

    if(type == 'view'){
        var empId = nlapiGetRecordId();
        var empObj = nlapiLoadRecord('employee', empId);
        var supervisorId = empObj.getFieldValue('supervisor'); 
        var currentStatus = empObj.getFieldValue('custentity_employee_status');
        var curId = nlapiGetUser();

        if(curId == supervisorId){
            var taskId = empObj.getFieldValue('custentity_idt_task_record');
            var taskObj = nlapiLoadRecord('task', taskId);
            var taskStatus = taskObj.getFieldValue('status');
            form.setScript('customscriptscidt_may22_client');
            if(currentStatus == '1' && taskStatus == 'NOTSTART'){
                form.addButton('custpage_review_emp', 'Review Employee', "reviewEmployee(" + empId + ", " + taskId + ")");
            } else if(currentStatus == '1' && taskStatus == 'PROGRESS'){
                form.addButton('custpage_approve_emp', 'Approve Employee', "approveEmployee(" + empId + ", " + taskId + ")");
                form.addButton('custpage_reject_emp', 'Reject Employee', "rejectEmployee(" + empId + ", " + taskId + ")");
            }
        }
    }
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      approve, reject, cancel (SO, ER, Time Bill, PO & RMA only)
 *                      pack, ship (IF)
 *                      markcomplete (Call, Task)
 *                      reassign (Case)
 *                      editforecast (Opp, Estimate)
 * @returns {Void}
 */
function userEventBeforeSubmit(type){
 
}

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit,
 *                      approve, cancel, reject (SO, ER, Time Bill, PO & RMA only)
 *                      pack, ship (IF only)
 *                      dropship, specialorder, orderitems (PO only) 
 *                      paybills (vendor payments)
 * @returns {Void}
 */
function userEventAfterSubmit(type){
    if(type == 'create'){
        var taskObj = nlapiCreateRecord('task');
        taskObj.setFieldValue('title', 'Employee for approval');
        taskObj.setFieldValue('assigned', nlapiGetFieldValue('supervisor'));
        var taskId = nlapiSubmitRecord(taskObj, true);
        var empObj = nlapiLoadRecord('employee', nlapiGetRecordId());
        empObj.setFieldValue('custentity_idt_task_record', taskId);
        nlapiSubmitRecord(empObj, true);
    }
}
