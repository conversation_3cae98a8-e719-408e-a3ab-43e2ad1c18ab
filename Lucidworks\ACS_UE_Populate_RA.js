/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/runtime'], function(record, runtime) {

    function beforeLoad(context) {

        log.debug('beforeLoad');
        var recObj = record.load({ type: 'journalentry', id: context.newRecord.id });
        var jeLineCheck = recObj.getSublistValue({ sublistId: 'line', fieldId: 'memo', line: 0});

        // if(jeLineCheck.includes('Rev Rec')){
        //     var oldRevPlanID = 0;    
        //     for(var i = 0; i < recObj.getLineCount({ sublistId: 'line' }); i++){
                
        //         // get new revenue plan id
        //         var newRevPlanID = recObj.getSublistValue({ sublistId: 'line', fieldId: 'sourcerevenueplanid', line: 0 });

        //         // if old rev plan id is same with new rev id, skip record loading of rev plan
        //         if(oldRevPlanID != newRevPlanID){
                    
        //             // get rev element object from rev plan
        //             // Revenue Plan Obj -> Revenue Element Obj -> Revenue Arrangement ID
        //             var planObj = record.load({ type: 'revenueplan', id: newRevPlanID });
        //             var newRevElemID = planObj.getValue({ fieldId: 'createdfrom' }); 
        //             var elemObj = record.load({ type: 'revenueelement', id: newRevElemID});
        //             var RevArrID = elemObj.getValue({ fieldId: 'revenuearrangement' });

        //             oldRevPlanID = newRevPlanID;
        //         }

        //         recObj.setSublistValue({
        //             sublistId: 'line',
        //             fieldId: 'custcol_acs_ra',
        //             line: i,
        //             value: RevArrID
        //         });
        //     }        
        //     if(recObj.save()){
        //         log.debug('Success', 'Successfuly saved ' + recObj.id);
        //     }
        // }
    }

    function beforeSubmit(context) {
        log.debug('beforeSubmit');
        var recObj = record.load({ type: 'journalentry', id: context.newRecord.id });
        var jeLineCheck = recObj.getSublistValue({ sublistId: 'line', fieldId: 'memo', line: 0});
    }

    function afterSubmit(context) {
        log.debug('afterSubmit');
        var recObj = record.load({ type: 'journalentry', id: context.newRecord.id });
        var jeLineCheck = recObj.getSublistValue({ sublistId: 'line', fieldId: 'memo', line: 0});
    }

    return {
        beforeLoad: beforeLoad
    }
});
