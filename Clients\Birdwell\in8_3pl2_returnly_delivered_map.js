//in8_returnly_rma_fullyreturned.js
//https://tstdrv1330281.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=1227&id=9&scrollid=9&whence=

function convertResponse(options){
    var api = new options.in8syncApi({ configurationId: options.configurationId });

  log.emergency('options',options)
 var rmaID = options.data.attributes.rma
 var DEFAULT_ACCOUNT = 522 //10102 Cash and Cash Equivalents : Chase - Checking - 1662
 var DEFAULT_LOCATION = 1 //Warehouse 1 - Santa Ana
 var convert={}
 log.emergency('rmaID',rmaID)

    // log.emergency('result',result)
     api.findExistingRecord({
       recordtype: 'returnauthorization',
       filters:[
             ["type","anyof","RtnAuth"],
             "AND",
             ["mainline","is","T"],
             "AND",
             ["status","anyof","RtnAuth:B"],
             "AND",
             ["custbody_in8_returnly_tracking_number","isnotempty",""],
             "AND",
             ["custbody_in8_returnly_rma_number","is",rmaID]
          ],
       doesnotexist: function () {
           log.emergency('does not exist', rmaID);
       },
       exists: function (transactionExportId) {
        log.emergency('transactionExportId',transactionExportId)
         var internalid = transactionExportId.getValue({fieldId: 'id'});
         var trackingNo = transactionExportId.getValue({fieldId: 'custbody_in8_returnly_tracking_number'});
         var itemReceipt = record.transform({
             fromType:'returnauthorization',
             fromId: internalid,
             toType: 'itemreceipt',isDynamic: true
              });
         var itemLineCnt = itemReceipt.getLineCount('item')
                for(var line=0;line<itemLineCnt;line++){
                itemReceipt.selectLine({
                                sublistId: 'item',
                                line:line
                            });
                itemReceipt.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'quantity',
                    value: itemReceipt.getCurrentSublistValue({sublistId: 'item',fieldId: 'quantityremaining'}),
                    ignoreFieldChange: true
                });
                itemReceipt.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'itemreceive',
                    value: true,
                    ignoreFieldChange: true
                });
                itemReceipt.commitLine({sublistId: 'item'})
                }
              try{
                 var itemReceiptID = itemReceipt.save()

            //      var rmaRec = record.load({type: record.Type.RETURN_AUTHORIZATION,id: internalid});
            //      var numLines = rmaRec.getLineCount({sublistId: 'item'});
            //      var itemsArr = []
            //      for(var line=0;line<numLines;line++){
            //          var itemObj = {}
            //          //rmaRec.setSublistValue({sublistId: 'item',fieldId: 'isclosed',line: line,value: true});
            //          itemObj.order_line_item_id= rmaRec.getSublistValue({sublistId: 'item',fieldId: 'custcol_returnly_ext_order_item_id',line: line});
            //          itemObj.quantity= rmaRec.getSublistValue({sublistId: 'item',fieldId: 'quantity',line: line});
            //          itemObj.restock= false
            //          itemsArr.push(itemObj)
            //      }
            //      var postPayload = {}
            //          postPayload.tracking_number= trackingNo
            //          postPayload.items= itemsArr
            //      log.emergency('postPayload',postPayload)
            //    api.post({
            //        url: "https://api.returnly.com/refunds",
            //        headers: {
            //            'accept': 'application/json',
            //            'content-type': 'application/json',
            //            'X-Api-Token': api.getApiKeyOnly(),
            //        },
            //        payload: JSON.stringify(postPayload),
            //        OK: function (response) {
            //             log.emergency('ok response', response);
            //            createOrderExport_1.createOrderExport({
            //                transactionId: itemReceiptID,
            //                settingId: api.getId(),
            //                exportResponse: response.body,
            //                is_exported: true,
            //                Success: function (itemExportId) {


            //                },
            //                Failed: function (e) {
            //                    log.emergency('failed creating transaction export', e);
            //                }
            //            });
            //        },
            //        Failed: function (response) {
            //             log.emergency('failed response', response);
            //            createOrderExport_1.createOrderExport({
            //                transactionId: itemReceiptID,
            //                settingId: api.getId(),
            //                exportResponse: response.body,
            //                is_exported: false,
            //                Success: function (itemExportId) {
            //                    log.emergency('success', 'transaction export id created: '+itemExportId);
            //                },
            //                Failed: function (e) {
            //                    log.emergency('failed creating transaction export', e);
            //                }
            //            });
            //        }
            //    });
                var cashRefundRec = record.transform({
                                 fromType:'returnauthorization',
                                 fromId: internalid,
                                 toType: 'cashrefund',isDynamic: true
                                  });
                              cashRefundRec.setValue({fieldId:'account',value:DEFAULT_ACCOUNT});
                              var cashRefID = cashRefundRec.save()
                              log.emergency('cashRefID',cashRefID)
              }catch(e){
                 log.emergency('ITEM RECEIPT ERROR : '+e.name,e)
              }
       },
       internalIdOnly: false
   });
 return convert;
}