/**
 * Sales Order custom script
 * 
 * Version    Date            Author           Remarks
 * 1.00       24 Nov 2017     Marcel P
 *
 */

/**
 * @appliedtorecord salesorder 
 * 
 * @param {String} type Access mode: create, copy, edit
 * @returns {Void}
 */
function pageInit(type){
	
	try {
        if (type == 'copy') {
            
            // If WooCommerce Id has value, empty the field
            if (nlapiGetFieldValue('custbody_in8_wc_order_id')) {
                
                nlapiSetFieldValue('custbody_in8_wc_order_id', '');
                
                //nlapiLogExecution('DEBUG', 'Debug', 'WC Field id cleaned.');
            }            
        }        
    } catch(e) {
        //nlapiLogExecution('ERROR', 'Error', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
    }
}
