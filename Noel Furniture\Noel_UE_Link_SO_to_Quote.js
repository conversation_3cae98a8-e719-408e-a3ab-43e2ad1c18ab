/**
 *@NApiVersion 2.1
 *@NScriptType UserEventScript
 */
 define(['N/record'], function(record) {

    function afterSubmit(context) {
            let obj_SO = record.load({
                id: context.newRecord.id,
                type: record.Type.SALES_ORDER,
                isDynamic: true
            });

            let int_Quote = obj_SO.getValue({ fieldId: 'createdfrom' });

            if(int_Quote) {
                let obj_Quote = record.submitFields({
                    type: record.Type.ESTIMATE,
                    id: int_Quote,
                    values: {
                        "custbody25": obj_SO.id
                    }
                });
            }
    }

    return {
        afterSubmit: afterSubmit
    }
});
