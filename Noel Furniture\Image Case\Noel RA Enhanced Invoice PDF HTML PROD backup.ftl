<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
	<link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
	<#if .locale == "zh_CN">
		<link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
	<#elseif .locale == "zh_TW">
		<link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
	<#elseif .locale == "ja_JP">
		<link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
	<#elseif .locale == "ko_KR">
		<link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
	<#elseif .locale == "th_TH">
		<link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
	</#if>
    <macrolist>
        <macro id="nlheader">
            <table style="width: 100%; font-size: 10pt;">
              <tr>
                <td rowspan="3" style="padding: 0; width: 100px"><#if companyInformation.logoUrl?length != 0><img src="http://shopping.na3.netsuite.com/core/media/media.nl?id=6426&amp;c=249816&amp;h=51a7e6e5e647d77a63b5" style="width: 300px; height: 75px" /> </#if></td>
                <td align="right" style="padding: 0; width: 400px; font-size: 16pt">${record@title}</td>
              </tr>
              <tr>
                <td align="right" style="font-size: 16pt;">${record.tranid}</td>
              </tr>
              <tr>
                <td align="right">${record.trandate}</td>
              </tr>
          </table>
        </macro>
        <macro id="nlfooter">
            <table class="footer" style="width: 100%;"><tr>
	<td><barcode codetype="code128" showtext="true" value="${record.tranid}"/></td>
	</tr></table>
        </macro>
    </macrolist>
    <style type="text/css">* {
		<#if .locale == "zh_CN">
			font-family: NotoSans, NotoSansCJKsc, sans-serif;
		<#elseif .locale == "zh_TW">
			font-family: NotoSans, NotoSansCJKtc, sans-serif;
		<#elseif .locale == "ja_JP">
			font-family: NotoSans, NotoSansCJKjp, sans-serif;
		<#elseif .locale == "ko_KR">
			font-family: NotoSans, NotoSansCJKkr, sans-serif;
		<#elseif .locale == "th_TH">
			font-family: NotoSans, NotoSansThai, sans-serif;
		<#else>
			font-family: NotoSans, sans-serif;
		</#if>
		}
		table {
			font-size: 9pt;
			table-layout: fixed;
		}
        th {
            font-weight: bold;
            font-size: 8pt;
            vertical-align: middle;
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
            color: #333333;
        }
        td {
            padding: 4px 6px;
        }
		td p { align:left }
        b {
            font-weight: bold;
            color: #333333;
        }
        table.header td {
            padding: 0px;
            font-size: 10pt;
        }
        table.footer td {
            padding: 0px;
            font-size: 8pt;
        }
        table.itemtable th {
            padding-bottom: 10px;
            padding-top: 10px;
        }
        table.body td {
            padding-top: 2px;
        }
        table.total {
            page-break-inside: avoid;
        }
        tr.totalrow {
            background-color: #e3e3e3;
            line-height: 200%;
        }
        td.totalboxtop {
            font-size: 12pt;
            background-color: #e3e3e3;
        }
        td.addressheader {
            font-size: 8pt;
            padding-top: 6px;
            padding-bottom: 2px;
        }
        td.address {
            padding-top: 0px;
        }
        td.totalboxmid {
            font-size: 28pt;
            padding-top: 20px;
            background-color: #e3e3e3;
        }
        td.totalboxbot {
            background-color: #e3e3e3;
            font-weight: bold;
        }
        span.title {
            font-size: 28pt;
        }
        span.number {
            font-size: 16pt;
        }
        span.itemname {
            font-weight: bold;
            line-height: 150%;
        }
        hr {
            width: 100%;
            color: #d3d3d3;
            background-color: #d3d3d3;
            height: 1px;
        }
</style>
</head>
<body header="nlheader" header-height="10%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
    <table style="width: 100%; margin-top: 10px;"><tr>
	<td colspan="3" style="font-size: 8pt; padding: 6px 0 2px; font-weight: bold; color: #333333;">Bill To:</td>
	<td colspan="3" style="font-size: 8pt; padding: 6px 0 2px; font-weight: bold; color: #333333;">Ship To:</td>
	</tr>
	<tr>
	<td class="address" colspan="3" style="padding: 0;">${record.billaddress}<br />${record.billphone}</td>
	<td class="address" colspan="3" rowspan="2">${record.shipaddress}</td>
	</tr>
	<tr></tr></table>

<table style="width: 100%; margin-top: 10px;"><tr>
  	<th>Customer #</th>
  	<th>Designer</th>
	<th>Order Type</th>
  	<th>Delivery Date</th>
	<th>Delivery Method</th>
	</tr>
	<tr>
    <td style="padding-top: 2px;">${record.entity.id}</td>
    <td style="padding-top: 2px;">${record.salesrep}</td>
	<td style="padding-top: 2px;">${record.custbody_noel_saletype}</td>
    <td style="padding-top: 2px;">${record.shipdate}</td>
	<td style="padding-top: 2px;">${record.shipmethod}</td>
	</tr></table>
<#if record.item?has_content>

<table style="width: 100%; margin-top: 5px;"><!-- start items --><#list record.item as item><#if item_index==0>
<thead>
	<tr>
	<th align="center" colspan="3" style="width: 10%">${item.quantity@label}</th>
	<th align="center" style="padding: 10px 6px; width: 20%;">Image</th>
	<th align="center" colspan="12" style="padding: 10px 6px; width: 30%;">Item Description</th>
	<th align="center" style="padding: 10px 6px; width: 13.33%;">Retail</th>
	<th align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">Sale Price</th>
	<th align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">${item.amount@label}</th>
	</tr>
</thead>
</#if><tr>
	<td align="center" colspan="3" style="padding: 15px 6px; width: 10%;">${item.quantity}</td>
	<td align="center" style="padding: 10px 6px; width: 10%;"><img src= "https://system.netsuite.com${item.custcol_noel_trnslinefld_imageurl}" style="width: 100px; height: 100px"/></td>
	<td align="left" colspan="12" style="padding: 10px 6px; width: 40%;"><span style="font-weight: bold; line-height: 150%; color: #333333;">${item.item}</span><br />${item.description}</td>
	<td align="center" style="padding: 10px 6px; width: 13.33%;">${(item.custcol_noel_retail_price_rate*item.quantity)?string.currency}</td>
	<td align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">${item.rate}</td>
	<td align="center" colspan="4" style="padding: 10px 6px; width: 13.33%">${item.amount}</td>
	</tr>
	</#list><!-- end items --></table>

<hr style="width: 100%; color: #d3d3d3; background-color: #d3d3d3; height: 1px;" /></#if>
<table width = "100%">
  <tr width = "100%">
    <td width = "55%">
    <table style="width: 100%; margin-top: 0px;" align = "left">
    <tr>
  	<td align="left" style="font-weight: bold; color: #333333;">Signature below indicates that merchandise was received in good condition. Please check merchandise carefully. Noel Furniture is not liable for damage once merchandise is removed from premises.</td>
  	</tr>
    <tr>
    <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
  	</tr>
  	<tr>
  	<td align="left" style="font-weight: bold; color: #333333;">SIGNATURE____________________________________________________________ </td>
    </tr>
  	<tr>
    <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
  	</tr>
	<tr>
  	<td align="left" style="font-weight: bold; color: #333333;">DATE____________________________________________</td>
    </tr>
  	<tr>
    <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
  	</tr>
  </table>
 </td>
 <td width = "45%">
   <table style="page-break-inside: avoid; width: 100%; margin-top: 0px;" align = "left"><tr>
	<td align="left" style="font-weight: bold; color: #333333;">${record.subtotal@label}</td>
	<td align="right">${record.subtotal}</td>
	</tr>
	<tr>
	<td align="left" style=" font-weight: bold; color: #333333;">${record.taxtotal@label} (${record.taxrate}%)</td>
	<td align="right">${record.taxtotal}</td>
	</tr>
	<tr style="background-color: #e3e3e3; line-height: 200%;">
	<td align="left" style="font-weight: bold; color: #333333;">${record.total@label}</td>
	<td align="right">${record.total}</td>
	</tr>
	<tr>
	<td align="left" style="font-weight: bold; color: #333333;">Deposit</td>
	<td align="right">${record.custbody_noel_invoice_deposit}</td>
	</tr>
  	<tr>
	<td align="left" style="font-weight: bold; color: #333333;">Payment Method</td>
	<td align="right">${record.custbody_noel_payment_method}</td>
	</tr>
	<tr style="background-color: #e3e3e3; line-height: 200%;">
	<td align="left" style="font-weight: bold; color: #333333;">Balance Due</td>
	<td align="right">${(record.total-record.custbody_noel_invoice_deposit)?string.currency}</td>
	</tr></table>
 </td>
 </tr>
 </table>
 <table style="width: 100%; margin-top: 0px;">
	<tr>
  	<td align="center">THANK YOU FOR SHOPPING LOCAL</td>
    </tr>
  	<tr>
  	<td align="center">67 CENTS OF EVERY DOLLAR SPENT REMAINS IN OUR LOCAL COMMUNITY</td>
    </tr>
  	<tr>
  	<td align="center" style="font-weight: bold; color: #333333;">Noël Home | 2727 Southwest Freeway| Houston, TX 77098 |713.874.5200</td>
    </tr>
  	<tr>
  	<td align="center" style="font-weight: bold; color: #333333;">See Terms On Reverse</td>
    </tr>
</table>
</body><body>

<table style="width: 100%; margin-top: 10px;">
<thead>
	<tr>
	<th align="center" style="padding: 10px 6px;">Sale Terms</th>
	</tr>
  </thead>
    <tr>
      <td align="left" style="font-weight: bold;"><u>PLEASE READ THE FOLLOWING TERMS CAREFULLY:</u></td>
    </tr>
  	<tr>
      <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
    </tr>
  	<tr>
      <td align="left" style="font-weight: bold; color: #333333;"><u>MERCHANDISE:</u></td>
    </tr>
  	<tr>
      <td align="left">All sales are final, and no refunds will be issued. In the case of a return that is made within 15 days of the purchase date, a store credit will be issued. <u>The issued store credit is not subject to refund, and will expire one year from the issue date.</u></td>
    </tr>
  	<tr>
      <td align="left">In the case of Clearance Merchandise, those items are not eligible for cancellation, return or, exchange. Clearance merchandise is sold AS IS, and no repairs or service calls will be made.</td>
	</tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
    </tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;"><u>SPECIAL ORDERS:</u></td>
    </tr>
  	<tr>
      <td align="left">Special Orders require a deposit (50% for furniture, 75% for window treatments, 100% for fabric). Orders WILL NOT be processed without the appropriate deposit. The balance due (if any) is due upon arrival to our warehouse.</td>
    </tr>
    <tr>
      <td align="left">Arrival dates for special order items are ESTIMATES and are subject to change (due to fabric shortages, production delays, shipping delays, etc.). Cancellations of special orders due to such delays will not be accepted.</td>
    </tr>
    <tr>
      <td align="left">SPECIAL ORDERS ARE NOT CANCELLABLE AND NOT SUBJECT TO REFUND.</td>
    </tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
    </tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;"><u>FINANCING:</u></td>
    </tr>
  	<tr>
      <td align="left">Noel Furniture offers third party financing. Payments must be made directly to the lender. Noel Furniture cannot adjust or alter due dates or finance charges.</td>
    </tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
    </tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;"><u>DELIVERIES:</u></td>
    </tr>
  	<tr>
      <td align="left">Noel Furniture delivery teams are professional furniture movers. As such, they are fully authorized to refuse to place items into any location which they feel to be unsafe in any way. <u>Safety restrictions forbid delivery team members from removing their shoes.</u> The delivery team is not permitted to await your arrival, and any delivery that is not made due to your absence will be charged an additional delivery fee. If you need to reschedule a delivery, please call the Delivery Department at 713-874-5250 a minimum of 24 hours in advance to avoid $100 change fee.</td>
    </tr>
    <tr>
      <td align="left">The Delivery team is not responsible for moving existing items, and due to insurance restrictions may not hang art or mirrors.</td>
    </tr>
    <tr>
      <td align="left"><u>All merchandise MUST be inspected upon receipt. Noel Furniture will not be responsible for damage to merchandise, flooring, walls, or any item in your home, if it is not noted on the Delivery Receipt at the time of delivery.</u></td>
    </tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
    </tr>
     <tr>
      <td align="left" style="font-weight: bold; color: #333333;"><u>STORAGE POLICY:</u></td>
    </tr>
  	<tr>
      <td align="left">Merchandise must be delivered within 10 days of purchase or arrival in our warehouse. Items stored beyond 10 days, will incur a storage fee of 2.5% of the value of the merchandise. Storage fees are per 30 days, and are not prorated. Fees will be added to the sales contract and must be paid prior to delivery.</td>
    </tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
    </tr>
     <tr>
      <td align="left" style="font-weight: bold; color: #333333;"><u>WAREHOUSE/SHOWROOM PICK-UP:</u></td>
    </tr>
  	<tr>
      <td align="left">Noel Furniture discourages the self-handling of larger merchandise. If you desire to pick up larger items, a $25.00 handling fee will be added to your purchase, and a release of liability must be signed prior to wrapping and loading of the merchandise. This does not apply to accessory items.</td>
    </tr>
    <tr>
      <td align="left">Noel Furniture assumes no liability for items that are picked up by clients. NO RETURNS will be accepted for larger self-handled merchandise.</td>
    </tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;">I HAVE READ, UNDERSTAND, AND AGREE TO THE TERMS AS OUTLINED ABOVE.</td>
    </tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
    </tr>
    <tr>
    <td align="left" style="font-weight: bold; color: #333333;">&nbsp;</td>
  	</tr>
    <tr>
      <td align="left" style="font-weight: bold; color: #333333;">SIGNATURE_________________________________________________________________________                          DATE________________________________________</td>
    </tr>
  </table>
</body>
</pdf>