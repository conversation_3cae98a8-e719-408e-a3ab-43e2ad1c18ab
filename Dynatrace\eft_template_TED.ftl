<#-- format specific processing -->
<#function getReferenceNote payment>
    <#assign paidTransactions = transHash[payment.internalid]>
    <#assign referenceNote = "">
    <#assign paidTransactionsCount = paidTransactions?size>
    <#if (paidTransactionsCount >= 1)>
    	<#list paidTransactions as transaction>
    		<#if transaction.tranid?has_content>
    			<#if referenceNote?has_content>
    				<#assign referenceNote = referenceNote + ", " + transaction.tranid>
    			<#else>
    				<#assign referenceNote = transaction.tranid>
    			</#if>
		    </#if>
		</#list>
    </#if>
	<#return referenceNote>
</#function>

<#function removeTaxRegSymbols text>
<#assign value = text>
<#assign value = value?replace('-','')>
<#assign value = value?replace('/','')>
<#assign value = value?replace('.','')>
<#return value>
</#function>

<#-- cached values -->
<#assign totalAmount = computeTotalAmount(payments)>
<#if totalAmount < 5000>
<#assign serviceLevel = 'NURG'>
<#else>
<#assign serviceLevel = 'URGP'>
<#assign purpCd = 'P41'>
</#if>

<#assign InstrForDbtrAgt = ''>
<#-- template building -->
#OUTPUT START#
<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
<CstmrCdtTrfInitn>
<GrpHdr>
<MsgId>${cbank.custrecord_2663_file_name_prefix}${pfa.name}</MsgId>
<CreDtTm>${pfa.custrecord_2663_file_creation_timestamp?date?string("yyyy-MM-dd")}T${pfa.custrecord_2663_file_creation_timestamp?time?string("HH:mm:ss")}</CreDtTm>
<NbOfTxs>${payments?size?c}</NbOfTxs>
<CtrlSum>${formatAmount(totalAmount,"decLessThan1")}</CtrlSum>
<InitgPty>
<Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm>
<Id>
<OrgId>
<Othr>
<Id>${cbank.custpage_eft_custrecord_2663_bank_comp_id}</Id>
<SchmeNm>
<Cd>CUST</Cd>   
</SchmeNm>  
</Othr>
</OrgId>
</Id>
</InitgPty>
</GrpHdr>
<#list payments as payment>
<#list transHash[payment.internalid] as transaction>
<#if transaction.custbody_nsacs_brazil_payment_type == 'Boleto'>
<#assign purpCd = 'P31'>
<#assign serviceLevel = 'NURG'>
<#assign InstrForDbtrAgt = "/BRTL/" />
<#assign InstrForDbtrAgt = InstrForDbtrAgt + transaction.custbody_nsacs_brazil_payment_ref?string />
<#elseif transaction.custbody_nsacs_brazil_payment_type == ''>
<#assign purpCd = 'T11'>
<#assign serviceLevel = 'URGP'>
<#assign InstrForDbtrAgt = "/BTAX/" />
<#assign InstrForDbtrAgt = InstrForDbtrAgt + transaction.custbody_nsacs_brazil_payment_ref?string />
</#if>
</#list>
<PmtInf>
<PmtInfId>${pfa.id}-1</PmtInfId>
<PmtMtd>TRF</PmtMtd>
<BtchBookg>false</BtchBookg>
<NbOfTxs>${payments?size?c}</NbOfTxs>
<CtrlSum>${formatAmount(totalAmount,"decLessThan1")}</CtrlSum>
<PmtTpInf>
<SvcLvl>
<Cd>${serviceLevel}</Cd>
</SvcLvl>
</PmtTpInf>
<ReqdExctnDt>${pfa.custrecord_2663_process_date?string("yyyy-MM-dd")}</ReqdExctnDt>
<Dbtr>
<Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm>
<PstlAdr>
<Ctry>${cbank.custpage_eft_custrecord_2663_country_code}</Ctry>
<AdrLine>${cbank.custpage_eft_custrecord_2663_bank_address1} ${cbank.custpage_eft_custrecord_2663_bank_address2}</AdrLine>
</PstlAdr>
<Id>
<OrgId>
<Othr>
<Id>${cbank.custpage_eft_custrecord_2663_processor_code}</Id>
<SchmeNm>
<Prtry>CONVENIO</Prtry>
</SchmeNm>
</Othr>
</OrgId>
</Id>
</Dbtr>
<DbtrAcct>
<Id>
<Othr>
<Id>${cbank.custpage_eft_custrecord_2663_acct_num}</Id>
</Othr>
</Id>
</DbtrAcct>
<DbtrAgt>
<FinInstnId>
<BIC>${cbank.custpage_eft_custrecord_2663_bic}</BIC>
<Nm>${cbank.custpage_eft_custrecord_2663_bank_name}</Nm>
<PstlAdr>
<Ctry>${cbank.custpage_eft_custrecord_2663_country_code}</Ctry>
</PstlAdr>
</FinInstnId>
<BrnchId>
<Id>${cbank.custpage_eft_custrecord_2663_branch_num}</Id>
</BrnchId>
</DbtrAgt>
<ChrgBr>SHAR</ChrgBr>
<#assign ebank = ebanks[payment_index]>
<#assign entity = entities[payment_index]>
<CdtTrfTxInf>
<PmtId>
<InstrId>${pfa.id}-${payment.tranid}</InstrId>
<EndToEndId>${payment.tranid}</EndToEndId>
</PmtId>
<Amt>
<InstdAmt Ccy="${getCurrencySymbol(cbank.custrecord_2663_currency)}">${formatAmount(getAmount(payment),"decLessThan1")}</InstdAmt>
</Amt>
<CdtrAgt>
<FinInstnId>
<#if ebank.custrecord_2663_entity_bank_code?has_content && ebank.custrecord_2663_entity_branch_no?has_content>
<ClrSysMmbId><MmbId>${ebank.custrecord_2663_entity_bank_code}${ebank.custrecord_2663_entity_branch_no}</MmbId></ClrSysMmbId>
</#if>
<PstlAdr>
<Ctry>${ebank.custrecord_2663_entity_country_code}</Ctry>
</PstlAdr>
</FinInstnId>
</CdtrAgt>
<Cdtr>
<Nm>${setMaxLength(convertToLatinCharSet(buildEntityName(entity)),70)}</Nm>
<PstlAdr>
<Ctry>${ebank.custrecord_2663_entity_country_code}</Ctry>
</PstlAdr>
<Id>
<#if entity.isperson?string == "No">
<OrgId>
<Othr>
<Id>${removeTaxRegSymbols(entity.vatregnumber)}</Id>
<SchmeNm>
<Cd>TXID</Cd>
</SchmeNm>
</Othr>
</OrgId>
<#else>
<PrvtId>
<Othr>
<Id>${removeTaxRegSymbols(entity.vatregnumber)}</Id>
<SchmeNm>
<Cd>TXID</Cd>
</SchmeNm>
</Othr>
</PrvtId>
</#if>
</Id>
</Cdtr>
<CdtrAcct>
<Id>
<Othr>
<Id>${ebank.custrecord_2663_entity_acct_no}</Id>
</Othr>
</Id>
</CdtrAcct>
<#if InstrForDbtrAgt?has_content>
<InstrForDbtrAgt>${InstrForDbtrAgt}</InstrForDbtrAgt>
</#if>
<#if serviceLevel == 'URGP' || purpCd == 'P31'>
<Purp>
<Cd>${purpCd}</Cd>
</Purp>
</#if>
</CdtTrfTxInf>
</PmtInf>
</#list>
</CstmrCdtTrfInitn>
</Document><#rt>
#OUTPUT END#