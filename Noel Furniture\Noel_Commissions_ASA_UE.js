/**
 * Copyright (c) 1998-2019 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of NetSuite, Inc. ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with NetSuite.
 */
/**
 * Module Description
 * 
 * Version    Date				Author				Remarks
 * 1.00       07 18, 2019		hdossantos			Initial version.
 */
/**
 * @NApiVersion 2.x
 * @NScriptType UserEventScript
 */
define(['N/record', 'N/runtime', 'N/search'],
    function(record, runtime, search) { 
  
        function isEmpty(val) {
    		return (val == null || val == '' || val == undefined);
		}
      	      	
      	function afterSubmit(context)
      	{
			try {
				log.debug("afterSubmit","---------- SCRIPT START ----------");
				log.debug("afterSubmit","Context is: " + runtime.executionContext + " UE Type: " + context.type);
				if (context.type != context.UserEventType.DELETE && context.type != context.UserEventType.XEDIT)
				{
					// Get Script Parameters.  If Empty Exit Script.
					var tranSearch = runtime.getCurrentScript().getParameter("custscript_noel_comm_transrch");
					if (isEmpty(tranSearch)) { 
						log.debug("afterSubmit","Required script Parameters Empty.  Script Exiting...");
						return;
					}
					
					var rec = context.newRecord;
					var recId = rec.id;
					var recType = rec.type;
					
					// Load the transaction Record
					var recObj = record.load({
						type: recType,
						id: recId,
						isDynamic: false
					});
					
					// Execute a Saved Search to determine all Gross Profit details associated with the current transaction
					var tranSrchObj = search.load({
						id: tranSearch
					});
                  
                    // Add the filter to only search on the current transaction
                    var soFilter = search.createFilter({
                        name: 'internalid',
                        operator: search.Operator.IS,
                        values: [recId]
                    });
                  
                    tranSrchObj.filters.push(soFilter);
					
					var tranSrchResults = getAllSearchResults(tranSrchObj);
					
					// Determine all Commission Types used on the current transaction
					var commTypesArr = new Array();
					
					if (tranSrchResults) {
						for (var i=0;i<tranSrchResults.length;i++) {
							var currCommType = tranSrchResults[i].getValue({
								name: 'custitem_noel_comm_type',
								join: 'item'
							});
							
							if (!isEmpty(currCommType)) {
								commTypesArr.push(currCommType);
							}
						}
					}
					
					// Gather all Commission Information for the Commission Types found
					var commTypeSearch = search.create({
						type: 'customrecord_noel_commission_type',
						columns: [{
							name: 'internalid',
							sort: search.Sort.ASC
						},{
							name: 'custrecord_noel_ct_type'
						},{
							name: 'custrecord_noel_ct_fixpct'
						}, {
							name: 'custrecord_noel_ctr_com_pct',
							join: 'CUSTRECORD_NOEL_CTR_PARENT'
						}, {
							name: 'custrecord_noel_ctr_gm_pct',
							join: 'CUSTRECORD_NOEL_CTR_PARENT',
							sort: search.Sort.ASC
						}],
						filters: [{
							name: 'isinactive',
							operator: 'is',
							values: ['F']
						},{
							name: 'internalid',
							operator: 'anyof',
							values: commTypesArr
						},{
							name: 'custrecord_noel_ct_fixpct',
							operator: 'greaterthan',
							values: [0]
						}]					
					});
					
					var commSrchResults = getAllSearchResults(commTypeSearch);
					
					// Iterate through all Transaction Search results to determine correct commission.
					// Then update associated line item
					for (var i=0;i<tranSrchResults.length;i++) {
						var currItem = tranSrchResults[i].getValue({name: 'item'});
						var currCommType = tranSrchResults[i].getValue({name: 'custitem_noel_comm_type',join : 'item'});
						var currAmount = tranSrchResults[i].getValue({name: 'amount'});
						var currEstGP = tranSrchResults[i].getValue({name: 'estgrossprofit'});
						var currEstGPPct = parseFloat(tranSrchResults[i].getValue({name: 'estgrossprofitpct'}));
						var currLineID = tranSrchResults[i].getValue({name: 'line'});
						log.debug("findCommAmount","currItem: " + currItem + " currCommType: " + currCommType + " currAmount: " + currAmount + " currEstGP: "  + currEstGP + " currEstGPPct: " + currEstGPPct);
						
						var commAmt = findCommAmount(currCommType,currAmount,currEstGPPct,commSrchResults);
						//MADE CHANGES: BACKUP HERE: var commAmt = findCommAmount(currCommType,currEstGP,currEstGPPct,commSrchResults);
						
						updateTranASA(recObj,currItem,currAmount,commAmt,currLineID);
					}
					
					// Finally, Save the Updated Sales Order transaction
					recObj.save();
				}
				log.debug("afterSubmit","---------- SCRIPT END ----------");
			} catch(e) {
				log.error("afterSubmit","Error: " + e.message);
			}
        }
		
		function findCommAmount(commType, estGrossProfit, estGrossProfitPct, commissionData) {
			log.debug("findCommAmount","---------- START ----------");
			var commAmtResult = 0.00;
			
			// Iterate through Commission Type Data to find a match
			if (commType && parseFloat(estGrossProfit) > 0) {
				for (var i=0;commissionData && i<commissionData.length;i++) {
					var currCommType = commissionData[i].id;
					var currType = commissionData[i].getValue('custrecord_noel_ct_type');
					var currFixedPct = parseFloat(commissionData[i].getValue('custrecord_noel_ct_fixpct'));
					var currGMPct = parseFloat(commissionData[i].getValue({name: 'custrecord_noel_ctr_gm_pct', join: 'CUSTRECORD_NOEL_CTR_PARENT'}));
					var currCommPct = parseFloat(commissionData[i].getValue({name: 'custrecord_noel_ctr_com_pct', join: 'CUSTRECORD_NOEL_CTR_PARENT'}));
					//log.debug("findCommAmount","currCommType: " + currCommType + " currType: " + currType + " currFixedPct: " + currFixedPct + " currGMPct: "  + currGMPct + " currCommPct: " + currCommPct);
					
					if (currCommType == commType && currType == 1) { // Fixed Amount
						// The Commission Type is Fixed. Calculate the fixed Amount from 
						commAmtResult = estGrossProfit * (currFixedPct/100);
						break;
					} else if (currCommType == commType && currType == 2) { // Variable Amount
						log.debug("findCommAmount","estGrossProfitPct: " + estGrossProfitPct + " currGMPct: " + currGMPct + " i: " + i);
						if (estGrossProfitPct < currGMPct || (estGrossProfitPct > currGMPct && i == commissionData.length - 1)) {
							commAmtResult = estGrossProfit * (currCommPct/100);
							break;
						}
					}					
				}
			}
			log.debug("findCommAmount","commAmtResult: " + commAmtResult);
			return commAmtResult;
			
			log.debug("findCommAmount","---------- END ----------");
		}
		
		function updateTranASA(recObj,item,amount,commAmt,line) {
			
			log.debug("updateTranASA","---------- START ----------");
			var itemLineCount = recObj.getLineCount('item');
			
			for (var i=0;i<itemLineCount;i++) {
				var currItem = recObj.getSublistValue('item','item',i);
				var currAmount = recObj.getSublistValue('item','amount',i);
				var currLineID = recObj.getSublistValue('item','line',i);
				
				if (currItem == item && currAmount == amount && currLineID == line) {
					recObj.setSublistValue('item','altsalesamt',i,commAmt);				
				}
			}
			log.debug("updateTranASA","---------- END ----------");
		}
		
		function getAllSearchResults(objSavedSearch) {

                 var arrReturnSearchResults = new Array();
                 var objResultset = objSavedSearch.run();
                 var intSearchIndex = 0;
                 var arrResultSlice = null;

                 do
                 {

                   arrResultSlice = objResultset.getRange({
                     start: intSearchIndex,
                     end: intSearchIndex + 1000
                   });
				  if (arrResultSlice == null)
                  {
                     break;
                  }

                  arrReturnSearchResults = arrReturnSearchResults.concat(arrResultSlice);
                  intSearchIndex = arrReturnSearchResults.length;
                 }
				 while (arrResultSlice.length >= 1000);
               
                 return arrReturnSearchResults;
               
        }
		
		function isEmpty(val) {
    		return (val == null || val == '' || val == undefined);
		}
      	 

        return {
          	afterSubmit: afterSubmit
        }
    });