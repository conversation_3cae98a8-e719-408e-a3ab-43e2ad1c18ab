/**
 *
 */
 function customizeGlImpact(transactionRecord, standardLines, customLines, book) {
    
    try {
        var sublistId = 'custpage_deductions';
        var deductionLines = transactionRecord.getLineItemCount(sublistId);
        var amountTotal = 0.00;
        if (deductionLines == 0) {
            nlapiLogExecution('debug', 'debugging', 'no deduction lines');
            return;
        }
        var concatMemo = '';
        for (var i=1; i <= deductionLines; i++) {

            var amount =  transactionRecord.getLineItemValue(sublistId, 'deduction_amount', i);
            var account =  transactionRecord.getLineItemValue(sublistId, 'deduction_expense_account', i);
            var memo =  transactionRecord.getLineItemValue(sublistId, 'deduction_description', i);

            nlapiLogExecution('debug', 'debugging', "account id: " + account + " - memo: " + memo + " - amount : " + amount + " - line: " + i);
            
            var newCustomLine = customLines.addNewLine();
            newCustomLine.setAccountId(Number(account));
            newCustomLine.setMemo(memo);
            newCustomLine.setDebitAmount(Number(amount));
            amountTotal += Number(amount);

            concatMemo = concatMemo + ' ' + memo;

        }

        
        var newCustomLine = customLines.addNewLine();
        newCustomLine.setAccountId(Number(227));
        newCustomLine.setMemo(concatMemo);
        newCustomLine.setCreditAmount(amountTotal);
    } catch (e) {
        if ( e instanceof nlobjError ){
            nlapiLogExecution('ERROR', 'Unexpected Error: ' + e.getDetails());
        } else {
            nlapiLogExecution('ERROR', 'Unexpected Error: ' + e.toString());
        }
    }

}