function scheduled(type) {
    getSubscriptions();
}

function getSubscriptions() {

    var file = nlapiLoadFile(383126);

    var r = file.getValue();

   var filters = [],
       columns = [],
       searchResults,
       i = 0,
       listIds = [];
   
   filters[i++] = new nlobjSearchFilter('custrecord_in8_exported', null, 'is', 'F');
   filters[i++] = new nlobjSearchFilter('isinactive', null, 'is', 'F');
   
   i = 0;
   
   columns[i++] = new nlobjSearchColumn('internalid');
   
   searchResults = nlapiSearchRecord('customrecord_ps_subscription_pack', null, filters, columns);
   
   if (searchResults && searchResults.length) {
       //var r = '';
       var recmac = 'recmachcustrecord_ps_subscription_pack';

       for (i = 0; i < searchResults.length && i < 50; i++) {

            var internalId = searchResults[i].getValue('internalid');
        
            listIds.push(internalId);

            var record = nlapiLoadRecord('customrecord_ps_subscription_pack', internalId);
            
            r += internalId + ',';

            for (var i1 = 1; i1 <= record.getLineItemCount(recmac); i1++) {                

                var productsData = '';

                var quantity = record.getLineItemValue(recmac, 'custrecord_ps_item_quantity', i1);
                var item = record.getLineItemValue(recmac, 'custrecord_ps_subscription_pack_item', i1);

                var recordItem = nlapiLoadRecord(nlapiLookupField('item', item, 'recordtype'), item);

                if (recordItem) {
                    
                    productsData += getComponents(recordItem, quantity);
                }
                if (productsData) {
                    //productsData = productsData.substring(0, productsData.length - 1);

                    r+= productsData;
                }                
            }
            r += '\n';                               
       }
       nlapiLogExecution('DEBUG', 'data', r);
   }  

   var file = nlapiCreateFile('subscr.csv', 'CSV', r);
   file.setFolder(256287);
   nlapiSubmitFile(file);

   for (var i = 0; i < listIds.length; i++) {
        nlapiSubmitField('customrecord_ps_subscription_pack', listIds[i], 'custrecord_in8_exported', 'T');    
   }
}

function getComponents(record, quantity) {

    //product_id:840|name:Cellar Club|quantity:1|total:144.82|meta:|tax:14.48

    var products = '';

    for (var i = 1; i <= record.getLineItemCount('member'); i++) {

        var item = record.getLineItemValue('member', 'item', i);

        var itemRec = nlapiLookupField('item', item, ['itemid', 'custitem_in8_wc_product_id']);

        var quantityCurr = record.getLineItemValue('member', 'quantity', i);

        var price = nlapiLookupField('item', item, 'price3');

        var newQty = (quantity * quantityCurr);

        products = 'product_id:' + itemRec.custitem_in8_wc_product_id + '|name:' + itemRec.itemid + 
        '|quantity:' + (newQty) + '|total:' + nlapiFormatCurrency(newQty * price) + ';';
    }
    
    return products;
}