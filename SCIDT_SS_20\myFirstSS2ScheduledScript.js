/**
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
define(['N/search', 'N/record'], function(search, record) {

    function execute(context) {
        log.debug({
            title: 'Scheduled Script',
            details: 'Scheduled script Executed!'
        });

        var mySearch = search.load({ id: 'customsearch_sched_script_2_0' });

        var myResultSet = mySearch.run();
        myResultSet.each(function(result){
            record.submitFields({
                type: record.Type.SALES_ORDER,
                id: result.id,
                values: {
                    memo: 'Altered Via Scheduled Script'
                }
            });

            return true;
        });
    }

    return {
        execute: execute
    }
});
