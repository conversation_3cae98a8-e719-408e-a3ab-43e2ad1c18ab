<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%; font-size: 10pt;">
                    <tr>
                        <td style="width: 181px;">
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    style="float: left; margin: 7px; width: 130px; height: 100px;" /> </#if>
                        </td>
                        <td align="left" style="font-size: 35px; width: 623px;"><b>&nbsp; &nbsp; &nbsp;<br />&nbsp;
                                &nbsp; &nbsp; &nbsp; &nbsp;Shipping Manifest</b></td>
                    </tr>
                </table>

                <table border="1px" class="body" style="width: 100%; margin-top: 10px;">
                    <tr>
                        <td style="background-color: #000000; color: white;">Sales Order</td>
                        <td style="background-color: #000000; color: white;">Ship Date</td>
                        <td style="background-color: #000000; color: white;">Ship Via</td>
                        <td style="background-color: #000000; color: white;">Purchase Order</td>
                        <td style="background-color: #000000; color: white;">Item Fulfilment</td>
                        <td style="background-color: #000000; color: white;">NA#</td>
                    </tr>
                    <tr>
                        <td border-right="1px">${record.createdfrom.tranid}</td>
                        <td border-right="1px">${record.trandate}</td>
                        <td border-right="1px">${record.createdfrom.shipmethod}</td>
                        <td border-right="1px">${record.createdfrom.otherrefnum}</td>
                        <td border-right="1px">${record.tranid}</td>
                        <td border-right="1px">${record.memo}</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td align="center">${record.createdfrom.location}</td>
                    </tr>
                    <tr>
                        <td align="center">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            td.addressheader {
                font-size: 8pt;
                font-weight: bold;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            div.returnform {
                width: 100%;
                /* To ensure minimal height of return form */
                height: 200pt;
                page-break-inside: avoid;
                page-break-after: avoid;
            }

            hr {
                border-top: 1px dashed #d3d3d3;
                width: 100%;
                color: #ffffff;
                background-color: #ffffff;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="18%" footer="nlfooter" footer-height="10pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        &nbsp; &nbsp;
        <table style="width: 100%; margin-top:0px;">
            <tr>
                <td class="addressheader" style="height: 10px;font-size: large;">Ship-To/ Bill-To:</td>
            </tr>
            <tr>
                <td class="address" style="height: 10px;font-size: large;">${salesorder.shipaddress}</td>
            </tr>
        </table>
        <#if salesorder.item?has_content>
            <!-- Determind wheather or not to show the components of groups based on custom field-->
            <#assign showComponents=false>
                <#if record.custbody_show_components==true>
                    <#assign showComponents=true>
                    <!-- DO SHOW COMPONENTS (but do not show group headers)-->
                    <!-- GET list of classes in array-->
                    <#assign classArray=[]>
                    <#list salesorder.item as tranline>
                        <#if classArray?seq_index_of(tranline.class)==-1 && tranline.itemtype !='Group'>
                            <#assign classArray +=[tranline.class]>
                        </#if>
                    </#list>
                    <#list classArray as class>

                        <table style="margin-bottom:0;">
                            <tr>
                                <td style="font-weight:bold; font-size:18;">${class}</td>
                            </tr>
                        </table>

                        <table border-left="1px" border-top="1px" class="itemtable"
                            style="width: 100%; margin-bottom: 10px;">
                            <thead>
                                <tr border-bottom="1px">
                                    <td border-right="1px" colspan="6"><b>Customer Item</b></td>
                                    <td border-right="1px" colspan="3"><b>Qty</b></td>
                                    <td border-right="1px" colspan="8"><b>Code</b></td>
                                    <td align="right" border-right="1px" colspan="3"><b>BO Qty</b></td>
                                </tr>
                            </thead>
                            <#assign _seq=[]>
                                <#list salesorder.item as firstItem>
                                    <#assign _qty=0>
                                    <#assign _rem=0>
                                    <#if _seq?seq_index_of(firstItem.item)==-1>
                                        <#list record.item as secondItem>
                                            <#if firstItem.item==secondItem.item>
                                                <#assign _qty +=secondItem.quantity>
                                                <#--  <#assign _rem +=secondItem.quantityremaining>  -->
                                            </#if>
                                        </#list>
                                        <#if firstItem.quantityremaining gt _rem>
                                            <#assign _rem = firstItem.quantityremaining>
                                        </#if>
                                        <#assign _seq=_seq + [firstItem.item]>
                                            <#if firstItem.class==class && firstItem.itemtype !="Group">
                                                <tr border-bottom="1px">
                                                    <#if firstItem.custcol_scm_customerpartnumber?has_content>
                                                        <td border-right="1px" colspan="6">${firstItem.custcol_scm_customerpartnumber}</td>
                                                    <#else>
                                                        <td border-right="1px" colspan="6">${firstItem.item}</td>
                                                    </#if>
                                                    <td border-right="1px" colspan="3">${_qty}</td>
                                                    <td border-right="1px" colspan="8">${firstItem.custcol_stock_de}</td>
                                                    <td align="right" border-right="1px" colspan="3">${_rem}</td>
                                                </tr>
                                            </#if>
                                    </#if>
                                </#list>
                        </table>
                    </#list>
                <#else>
                    <!-- DO NOT SHOW COMPONENTS (SHOW ONLY GROUP HEADERS)-->
                    <!-- GET list of classes in array-->
                    <#assign classArray=[]>
                    <#list salesorder.item as tranline>
                        <#if classArray?seq_index_of(tranline.class)==-1 && tranline.itemtype!='NonInvtPart' && tranline.ingroup !='T'>
                            <#assign classArray +=[tranline.class]>
                        </#if>
                    </#list>
                    <#list classArray as class>

                        <table style="margin-bottom:0;">
                            <tr>
                                <td style="font-weight:bold; font-size:18;">${class}</td>
                            </tr>
                        </table>

                        <table border-left="1px" border-top="1px" class="itemtable"
                            style="width: 100%; margin-bottom: 10px;">
                            <thead>
                                <tr border-bottom="1px">
                                    <td border-right="1px" colspan="6"><b>Customer Item</b></td>
                                    <td border-right="1px" colspan="3"><b>Qty</b></td>
                                    <td border-right="1px" colspan="8"><b>Code</b></td>
                                    <td align="right" border-right="1px" colspan="3"><b>BO Qty</b></td>
                                </tr>
                            </thead>

                            <#assign _seq=[]>
                                <#list salesorder.item as firstItem>
                                    <#assign _qty=0>
                                    <#assign _rem=0>
                                        <#if _seq?seq_index_of(firstItem.item)==-1>
                                            <#list record.item as secondItem>
                                                <#if firstItem.item==secondItem.item>
                                                    <#assign _qty +=secondItem.quantity>
                                                    <#--  <#assign _rem +=secondItem.quantityremaining>  -->
                                                </#if>
                                                <#if firstItem.itemtype=="Group" && secondItem.ingroup=='T' && firstItem.item+' - NI'==secondItem.item>
                                                    <#assign _qty +=secondItem.quantity>
                                                </#if>
                                            </#list>
                                            <#if firstItem.quantityremaining gt _rem>
                                                <#assign _rem = firstItem.quantityremaining>
                                            </#if>
                                            <#assign _seq=_seq + [firstItem.item]>
                                                <#if firstItem.class==class && firstItem.itemtype !="NonInvtPart" && firstItem.ingroup !='T'>
                                                    <tr border-bottom="1px">
                                                    <#if firstItem.custcol_scm_customerpartnumber?has_content>
                                                        <td border-right="1px" colspan="6">
                                                            ${firstItem.custcol_scm_customerpartnumber}
                                                        </td>
                                                    <#else>
                                                        <td border-right="1px" colspan="6">${firstItem.item}</td>
                                                    </#if>
                                                    <#if _qty=0>
                                                        <td border-right="1px" colspan="3">${firstItem.quantityordered}</td>
                                                    <#else>
                                                        <td border-right="1px" colspan="3">${_qty}</td>
                                                    </#if>

                                                    <td border-right="1px" colspan="8">${firstItem.custcol_stock_de}</td>
                                                    <td align="right" border-right="1px" colspan="3">${_rem}</td>
                                                    </tr>
                                                </#if>
                                        </#if>
                                </#list>
                        </table>
                    </#list>
                </#if>
        </#if> <!-- closing if sales order has content--> &nbsp;


        <table class="comments" style="width: 100%;">
            <tr>
                <td>Received By: X______________________________________________Date:____________________</td>
            </tr>
        </table>
    </body>
</pdf>