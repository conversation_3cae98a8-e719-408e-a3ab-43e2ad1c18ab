<#-- format specific processing -->
    
    <#function computeTotalRecords recordCount>
        <#assign value = (recordCount / 10) >
        <#assign value = value?ceiling >    
        <#return value>
    </#function>

    <#function computeSequenceId>
        <#assign lastSeqId = getSequenceId(true)>
        
        <#-- store new sequence id to be returned later -->
        <#assign newSeqId = lastSeqId + 1>        

        <#-- do char code to character conversion -->
        <#assign seqId = (lastSeqId % 26) + 65>
        <#assign seqId = seqId?string?replace("65","A")>
        <#assign seqId = seqId?string?replace("66","B")>
        <#assign seqId = seqId?string?replace("67","C")>
        <#assign seqId = seqId?string?replace("68","D")>
        <#assign seqId = seqId?string?replace("69","E")>
        <#assign seqId = seqId?string?replace("70","F")>
        <#assign seqId = seqId?string?replace("71","G")>
        <#assign seqId = seqId?string?replace("72","H")>
        <#assign seqId = seqId?string?replace("73","I")>
        <#assign seqId = seqId?string?replace("74","J")>
        <#assign seqId = seqId?string?replace("75","K")>
        <#assign seqId = seqId?string?replace("76","L")>
        <#assign seqId = seqId?string?replace("77","M")>
        <#assign seqId = seqId?string?replace("78","N")>
        <#assign seqId = seqId?string?replace("79","O")>
        <#assign seqId = seqId?string?replace("80","P")>
        <#assign seqId = seqId?string?replace("81","Q")>
        <#assign seqId = seqId?string?replace("82","R")>
        <#assign seqId = seqId?string?replace("83","S")>
        <#assign seqId = seqId?string?replace("84","T")>
        <#assign seqId = seqId?string?replace("85","U")>
        <#assign seqId = seqId?string?replace("86","V")>
        <#assign seqId = seqId?string?replace("87","W")>
        <#assign seqId = seqId?string?replace("88","X")>
        <#assign seqId = seqId?string?replace("89","Y")>
        <#assign seqId = seqId?string?replace("90","Z")>
        <#return seqId>        
    </#function>
    
<#-- cached values -->
<#assign totalAmount = computeTotalAmount(payments)>    
    
<#-- template building -->
#OUTPUT START#
<#assign recordCount = 0>
<#assign batchCount = 0>
<#assign batchLineNum = 0>
<#assign ccdBankNumberHash = 0>
<#assign totalBankNumberHash = 0>
101 ${setLength(cbank.custpage_eft_custrecord_2663_bank_num, 9)}${setLength(cbank.custpage_eft_custrecord_2663_bank_comp_id, 10)}${setLength(pfa.custrecord_2663_file_creation_timestamp?date?string("yyMMdd"),6)}${setLength(pfa.custrecord_2663_file_creation_timestamp?time?string("HHmm"),4)}${setLength(computeSequenceId(),1)}094101${setLength(cbank.custpage_eft_custrecord_2663_bank_name, 23)}CIBC${setLength(cbank.custrecord_2663_legal_name, 19)}        
<#assign recordCount = recordCount + 1>
<#assign batchCount = batchCount + 1>
5200                FF3               US${setLength(cbank.custpage_eft_custrecord_2663_issuer_num,10)}IATPayment   USDUSD${pfa.custrecord_2663_process_date?string("yyMMdd")}   1********${setPadding(batchCount,"left","0",7)}
<#assign recordCount = recordCount + 1>
<#list payments as payment>
<#assign batchLineNum = batchLineNum + 1>
<#assign ebank = ebanks[payment_index]>
<#assign ccdBankNumberHash = ccdBankNumberHash + ebank.custrecord_2663_entity_bank_no?substring(0,8)?number>
627${setLength(ebank.custrecord_2663_entity_bank_no,8)}${setLength(ebank.custrecord_2663_entity_country_check,1)}0000             ${setPadding(formatAmount(getAmount(payment)),"left","0",10)}${setLength(ebank.custrecord_2663_entity_acct_no,17)}                      1********${setPadding(batchCount,"left","0",7)}
<#assign recordCount = recordCount + 1>
</#list>
<#assign totalBankNumberHash = totalBankNumberHash + ccdBankNumberHash>
8200${setPadding(batchLineNum,"left","0",6)}${setPadding(ccdBankNumberHash,"left","0",10)}${setPadding("0","left","0",12)}${setPadding(formatAmount(computeTotalAmount(payments)),"left","0",12)}${setLength(cbank.custpage_eft_custrecord_2663_issuer_num,10)}                         ********${setPadding(batchCount,"left","0",7)}
<#assign recordCount = recordCount + 1>
9${setPadding(batchCount,"left","0",6)}${setPadding(payments?size,"left","0",6)}${setPadding(recordCount,"left","0",8)}${setPadding(totalBankNumberHash,"left","0",10)}${setPadding("0","left","0",12)}${setPadding(formatAmount(totalAmount),"left","0",12)}                                       #OUTPUT END#