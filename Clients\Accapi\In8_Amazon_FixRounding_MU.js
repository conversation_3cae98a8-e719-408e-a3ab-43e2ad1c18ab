var ADJUSTMENT_ITEM = 5814;

function massUpdate(recType, recId) {

    try {

        if (recType == 'cashsale') {
            updateCashSale(recId);
            return;
        }
        if (recType == 'returnauthorization') {
            updateRMA(recType, recId);            
            return;
        }
        nlapiLogExecution('DEBUG', 'In8', 'Record Id ' + recId);

        var hasChanges = false;
        var difference = null;
        var amazonLineId;

        var record = nlapiLoadRecord(recType, recId);

        // Get the JSON data
        var amazonId = record.getFieldValue('custbody_in8_amzn_order_id');

        var jsonObj = getAmazonOrderLines(recId, amazonId);

        for (var i = 1; i <= record.getLineItemCount('item'); i++) {

            var item = record.getLineItemValue('item', 'item', i);
            var lineType = record.getLineItemValue('item', 'custcol_in8_amzn_linetype', i);
            var grossAmt = record.getLineItemValue('item', 'grossamt', i);

            if (!lineType) {
                amazonLineId = record.getLineItemValue('item', 'custcol_in8_amazon_id', i);

                // Item ASIN and SKU
                var s = nlapiSearchRecord('customrecord_in8_amzn_listing', null, [
                    new nlobjSearchFilter('custrecord_in8_amzn_list_item', null, 'is', item),
                    new nlobjSearchFilter('custrecord_in8_amzn_list_mktp', null, 'is', 7)
                ], [
                    new nlobjSearchColumn('internalid'),
                    new nlobjSearchColumn('custrecord_in8_amzn_list_asin'),
                    new nlobjSearchColumn('custrecord_in8_amzn_seller_sku'),
                ]) || [];

                if (s.length) {

                    for (var j = 0; j < s.length; j++) {

                        var asin = s[j].getValue('custrecord_in8_amzn_list_asin');
                        var sku = s[j].getValue('custrecord_in8_amzn_seller_sku');

                        nlapiLogExecution('DEBUG', 'In8', 'ASIN: ' + asin + ' - SKU: ' + sku);

                        if (asin && sku) {
                            // check the amount from payload
                            var amountPayload = getAmountPayload(jsonObj, asin, sku);

                            if (amountPayload) {
                                nlapiLogExecution('DEBUG', 'In8', 'Payload amount: ' + amountPayload.amount);
                            }

                            if (amountPayload && amountPayload.amount != 0 && amountPayload.amount != grossAmt) {

                                nlapiLogExecution('DEBUG', 'In8', 'Line ' + i + ' has difference');

                                record.setLineItemValue('item', 'amount', i, amountPayload.amount - amountPayload.tax);
                                record.setLineItemValue('item', 'tax1amt', i, amountPayload.tax);

                                // Calculate difference
                                //difference = amountPayload - grossAmt;

                                //if (difference < 1 && difference > -1) {
                                hasChanges = true;
                                //}
                            }
                        }
                    }
                }
            }
        }

        if (hasChanges) {
            record.setFieldValue('custbody_in8_rounding_adjusted', 'T');

            //nlapiLogExecution('DEBUG', 'In8', 'Difference: ' + difference);

            // // Add line item
            // record.selectNewLineItem('item');
            // record.setCurrentLineItemValue('item', 'item', ADJUSTMENT_ITEM);
            // record.setCurrentLineItemValue('item', 'price', -1);
            // record.setCurrentLineItemValue('item', 'taxcode', 5807);
            // record.setCurrentLineItemValue('item', 'grossamt', difference);
            // record.setCurrentLineItemValue('item', 'custcol_in8_amazon_id', amazonLineId);
            // record.commitLineItem('item');

            nlapiSubmitRecord(record, true, true);

            // Update cash sale
            // for (var i = 1; i <= record.getLineItemCount('links'); i++) {

            //     if (record.getLineItemValue('links', 'type', i) == 'Cash Sale') {

            //         record = nlapiLoadRecord('cashsale', record.getLineItemValue('links', 'id', i));

            //         // Add line item
            //         record.selectNewLineItem('item');
            //         record.setCurrentLineItemValue('item', 'item', ADJUSTMENT_ITEM);
            //         record.setCurrentLineItemValue('item', 'price', -1);
            //         record.setCurrentLineItemValue('item', 'taxcode', 5807);
            //         record.setCurrentLineItemValue('item', 'grossamt', difference);
            //         record.setCurrentLineItemValue('item', 'custcol_in8_amazon_id', amazonLineId);
            //         record.commitLineItem('item');

            //         nlapiSubmitRecord(record, true, true);
            //     }
            // }

            nlapiLogExecution('DEBUG', 'In8', 'Record Id ' + recId + ' UPDATED');
        }

    } catch (e) {
        nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e) + ' ' + e.stack);
    }
}

function getAmazonOrderLines(id, amazonId) {

    try {
        var s = nlapiSearchRecord('customrecord_in8_amzn_order_q', null, [
            new nlobjSearchFilter('custrecord_in8_amzn_order_id', null, 'is', amazonId),
            new nlobjSearchFilter('custrecord_in8_amzn_ns_trx', null, 'is', id)
        ], [
            new nlobjSearchColumn('internalid').setSort(true),
            new nlobjSearchColumn('custrecord_in8_amzn_order_lines')
        ]) || [];

        if (s.length) {
            return JSON.parse(s[0].getValue('custrecord_in8_amzn_order_lines'));
        }
        return {};
    } catch (e) {
        return {};
    }
}

function getAmountPayload(jsonObj, asin, sku) {

    if (jsonObj &&
        jsonObj.ListOrderItemsResponse &&
        jsonObj.ListOrderItemsResponse.ListOrderItemsResult &&
        jsonObj.ListOrderItemsResponse.ListOrderItemsResult.OrderItems) {

        for (a in jsonObj.ListOrderItemsResponse.ListOrderItemsResult.OrderItems) {
            var item = jsonObj.ListOrderItemsResponse.ListOrderItemsResult.OrderItems[a];

            if (item.ASIN == asin && item.SellerSKU == sku) {
                return {
                    amount: item.ItemPrice ? Number(item.ItemPrice.Amount) : 0,
                    tax: item.ItemTax ? Number(item.ItemTax.Amount) : 0
                }
            }
        }
        for (a in jsonObj.ListOrderItemsResponse.ListOrderItemsResult.OrderItems.OrderItem) {
            var item = jsonObj.ListOrderItemsResponse.ListOrderItemsResult.OrderItems.OrderItem[a];

            if (item.ASIN == asin && item.SellerSKU == sku) {
                return {
                    amount: item.ItemPrice ? Number(item.ItemPrice.Amount) : 0,
                    tax: item.ItemTax ? Number(item.ItemTax.Amount) : 0
                }
            }
        }
    }
    return null;
}

function updateCashSale(recId) {
    nlapiLogExecution('DEBUG', 'In8', 'Record Id ' + recId);

    var hasChanges = false;

    var record = nlapiLoadRecord('cashsale', recId);

    var recFee = nlapiLoadRecord('customrecord_in8_amzn_fees_q', record.getFieldValue('custbody_in8_amzn_feequeue'));

    var obj = JSON.parse(recFee.getFieldValue('custrecord_in8_amzn_fees_payload'));

    for (var i = 1; i <= record.getLineItemCount('item'); i++) {

        var lineType = record.getLineItemValue('item', 'custcol_in8_amzn_linetype', i);
        var amount = record.getLineItemValue('item', 'amount', i);
        var tax1amt = record.getLineItemValue('item', 'tax1amt', i);

        // Only for Non fee
        if (!lineType) {

            var amazonLineId = record.getLineItemValue('item', 'custcol_in8_amazon_id', i);

            // Get that item from payload
            var itemPayload = getItemPayload(obj, amazonLineId);

            if (itemPayload.tax) {
                if (itemPayload.amount) {
                    if (amount != itemPayload.amount) {
                        // Update amount
                        record.setLineItemValue('item', 'amount', i, itemPayload.amount);
                        hasChanges = true;
                    }
                }
                if (itemPayload.tax) {
                    if (tax1amt != itemPayload.tax) {
                        // Update amount
                        record.setLineItemValue('item', 'tax1amt', i, itemPayload.tax);
                        hasChanges = true;
                    }
                }
            }
        }
    }
    if (hasChanges) {
        record.setFieldValue('custbody_in8_rounding_adjusted', 'T');
        nlapiSubmitRecord(record, true, true);
        nlapiLogExecution('DEBUG', 'In8', 'Record Id ' + recId + ' UPDATED');
    }
}

function getItemPayload(obj, amazonLineId) {

    var r = {};

    if (obj.ShipmentItemList &&
        obj.ShipmentItemList.ShipmentItem &&
        obj.ShipmentItemList.ShipmentItem.OrderItemId) {
        // one item
        if (obj.ShipmentItemList.ShipmentItem.OrderItemId == amazonLineId) {

            if (obj.ShipmentItemList.ShipmentItem.ItemChargeList &&
                obj.ShipmentItemList.ShipmentItem.ItemChargeList.ChargeComponent &&
                obj.ShipmentItemList.ShipmentItem.ItemChargeList.ChargeComponent.length) {

                for (var j = 0; j < obj.ShipmentItemList.ShipmentItem.ItemChargeList.ChargeComponent.length; j++) {
                    var chargeComp = obj.ShipmentItemList.ShipmentItem.ItemChargeList.ChargeComponent[j];

                    if (chargeComp.ChargeType == 'Principal') {
                        r.amount = Number(chargeComp.ChargeAmount.CurrencyAmount);
                    } else if (chargeComp.ChargeType == 'Tax') {
                        r.tax = Number(chargeComp.ChargeAmount.CurrencyAmount);
                    }
                }
            }
        }
    } else {
        if (obj.ShipmentItemList &&
            obj.ShipmentItemList.ShipmentItem &&
            obj.ShipmentItemList.ShipmentItem.length) {

            for (var i = 0; i < obj.ShipmentItemList.ShipmentItem.length; i++) {

                if (obj.ShipmentItemList.ShipmentItem[i].OrderItemId == amazonLineId) {

                    if (obj.ShipmentItemList.ShipmentItem[i].ItemChargeList &&
                        obj.ShipmentItemList.ShipmentItem[i].ItemChargeList.ChargeComponent &&
                        obj.ShipmentItemList.ShipmentItem[i].ItemChargeList.ChargeComponent.length) {
        
                        for (var j = 0; j < obj.ShipmentItemList.ShipmentItem[i].ItemChargeList.ChargeComponent.length; j++) {
                            var chargeComp = obj.ShipmentItemList.ShipmentItem[i].ItemChargeList.ChargeComponent[j];
        
                            if (chargeComp.ChargeType == 'Principal') {
                                r.amount = Number(chargeComp.ChargeAmount.CurrencyAmount);
                            } else if (chargeComp.ChargeType == 'Tax') {
                                r.tax = Number(chargeComp.ChargeAmount.CurrencyAmount);
                            }
                        }
                    }
                }
            }
        }
    }
    return r;
}

function getItemPayloadRefund(obj, amazonLineId) {

    var r = {};

    if (obj.ShipmentItemAdjustmentList &&
        obj.ShipmentItemAdjustmentList.ShipmentItem &&
        obj.ShipmentItemAdjustmentList.ShipmentItem.OrderAdjustmentItemId) {
        // one item
        if (obj.ShipmentItemAdjustmentList.ShipmentItem.OrderAdjustmentItemId == amazonLineId) {

            if (obj.ShipmentItemAdjustmentList.ShipmentItem.ItemChargeAdjustmentList &&
                obj.ShipmentItemAdjustmentList.ShipmentItem.ItemChargeAdjustmentList.ChargeComponent &&
                obj.ShipmentItemAdjustmentList.ShipmentItem.ItemChargeAdjustmentList.ChargeComponent.length) {

                for (var j = 0; j < obj.ShipmentItemAdjustmentList.ShipmentItem.ItemChargeAdjustmentList.ChargeComponent.length; j++) {
                    var chargeComp = obj.ShipmentItemAdjustmentList.ShipmentItem.ItemChargeAdjustmentList.ChargeComponent[j];

                    if (chargeComp.ChargeType == 'Principal') {
                        r.amount = Math.abs(Number(chargeComp.ChargeAmount.CurrencyAmount));
                    } else if (chargeComp.ChargeType == 'Tax' && !r.tax) {
                        r.tax = Math.abs(Number(chargeComp.ChargeAmount.CurrencyAmount));
                    }
                }
            }
        }
    } else {
        if (obj.ShipmentItemAdjustmentList &&
            obj.ShipmentItemAdjustmentList.ShipmentItem &&
            obj.ShipmentItemAdjustmentList.ShipmentItem.length) {

            for (var i = 0; i < obj.ShipmentItemAdjustmentList.ShipmentItem.length; i++) {

                if (obj.ShipmentItemAdjustmentList.ShipmentItem[i].OrderAdjustmentItemId == amazonLineId) {

                    if (obj.ShipmentItemAdjustmentList.ShipmentItem[i].ItemChargeAdjustmentList &&
                        obj.ShipmentItemAdjustmentList.ShipmentItem[i].ItemChargeAdjustmentList.ChargeComponent &&
                        obj.ShipmentItemAdjustmentList.ShipmentItem[i].ItemChargeAdjustmentList.ChargeComponent.length) {
        
                        for (var j = 0; j < obj.ShipmentItemAdjustmentList.ShipmentItem[i].ItemChargeAdjustmentList.ChargeComponent.length; j++) {
                            var chargeComp = obj.ShipmentItemAdjustmentList.ShipmentItem[i].ItemChargeAdjustmentList.ChargeComponent[j];
        
                            if (chargeComp.ChargeType == 'Principal') {
                                r.amount = Math.abs(Number(chargeComp.ChargeAmount.CurrencyAmount));
                            } else if (chargeComp.ChargeType == 'Tax' && !r.tax) {
                                r.tax = Math.abs(Number(chargeComp.ChargeAmount.CurrencyAmount));
                            }
                        }
                    }
                }
            }
        }
    }
    return r;
}


function updateRMA(recType, recId) {
    
    nlapiLogExecution('DEBUG', 'In8', 'Record Id ' + recId);

    var hasChanges = false;

    var record = nlapiLoadRecord(recType, recId);

    var recFee = nlapiLoadRecord('customrecord_in8_amzn_fees_q', record.getFieldValue('custbody_in8_amzn_feequeue'));

    var obj = JSON.parse(recFee.getFieldValue('custrecord_in8_amzn_fees_payload'));

    for (var i = 1; i <= record.getLineItemCount('item'); i++) {

        var lineType = record.getLineItemValue('item', 'custcol_in8_amzn_linetype', i);
        var amount = record.getLineItemValue('item', 'amount', i);
        var tax1amt = record.getLineItemValue('item', 'tax1amt', i);

        // Only for Non fee
        if (!lineType) {

            var amazonLineId = record.getLineItemValue('item', 'custcol_in8_amazon_id', i);

            // Get that item from payload
            var itemPayload = getItemPayloadRefund(obj, amazonLineId);

            if (itemPayload.tax) {
                if (itemPayload.amount) {
                    if (amount != itemPayload.amount) {
                        // Update amount
                        record.setLineItemValue('item', 'amount', i, itemPayload.amount);
                        hasChanges = true;
                    }
                }
                if (itemPayload.tax) {
                    if (tax1amt != itemPayload.tax) {
                        // Update amount
                        record.setLineItemValue('item', 'tax1amt', i, itemPayload.tax);
                        hasChanges = true;
                    }
                }
            }
        }
    }
    if (hasChanges) {
        record.setFieldValue('custbody_in8_rounding_adjusted', 'T');
        nlapiSubmitRecord(record, true, true);
        nlapiLogExecution('DEBUG', 'In8', 'Record Id ' + recId + ' UPDATED');        

        // Update credit note
        for (var i = 1; i <= record.getLineItemCount('links'); i++) {

            if (record.getLineItemValue('links', 'type', i) == 'Credit Note') {
                deleteCreditMemoAndRelated(record.getLineItemValue('links', 'id', i));
                
                // regenerate credit memo
                var creditMemo = nlapiTransformRecord('returnauthorization', recId, 'creditmemo');

                creditMemo.setFieldValue('trandate', record.getFieldValue('trandate'));

                if (record.getFieldValue('taxamountoverride') != null) {
                    creditMemo.setFieldValue('taxamountoverride', record.getFieldValue('taxamountoverride'));
                }
                id = nlapiSubmitRecord(creditMemo, true, true);

                nlapiLogExecution('DEBUG', 'Vend', 'Credit memo id: ' + id);

                var customerRefund = nlapiCreateRecord('customerrefund', { recordmode: 'dynamic'});

                customerRefund.setFieldValue('customer', record.getFieldValue('entity'));
                customerRefund.setFieldValue('trandate', record.getFieldValue('trandate'));

                customerRefund.setFieldValue('custbody_in8_amzn_order_id', record.getFieldValue('custbody_in8_amzn_order_id'));
                customerRefund.setFieldValue('custbody_in8_amzn_eventdate', record.getFieldValue('custbody_in8_amzn_eventdate'));
                customerRefund.setFieldValue('custbody_in8_amzn_feequeue', record.getFieldValue('custbody_in8_amzn_feequeue'));
                customerRefund.setFieldValue('paymentmethod', 12);

                applyPaym(customerRefund, id);

                id = nlapiSubmitRecord(customerRefund, true, true);
            }
        }
    }    
}

function applyPaym(record, creditMemoId) {
    for (i = 1; i <= record.getLineItemCount('apply'); i++) {

        record.selectLineItem('apply', i);

        // Find the Invoice and apply the payment
        if (record.getCurrentLineItemValue('apply', 'internalid') == creditMemoId) {

            record.setCurrentLineItemValue('apply', 'apply', 'T');
            //uncommenting below line because amount is not applied properly
            //var total = record.getCurrentLineItemValue('apply', 'total');
            // record.setCurrentLineItemValue('apply', 'amount', total);
            //record.setCurrentLineItemValue('apply', 'amount', amountRefund * -1);                

            record.commitLineItem('apply');

            hasChanges = true;
            break;
        }
    }
}

function deleteCreditMemoAndRelated(internalId) {

    nlapiLogExecution('DEBUG', 'LS', 'Credit memo ' + internalId);

    var record = nlapiLoadRecord('creditmemo', internalId);
    var refunds = [];
    
    for (i = 1; i <= record.getLineItemCount('apply'); i++) {
        record.selectLineItem('apply', i);

        if (record.getCurrentLineItemValue('apply', 'apply') == 'T') {
            if (record.getCurrentLineItemValue('apply', 'type') == 'Customer Refund') {
                refunds.push(record.getCurrentLineItemValue('apply', 'internalid'));
            }            
            record.setCurrentLineItemValue('apply', 'apply', 'F');
            record.commitLineItem('apply');
        }
    }
    nlapiSubmitRecord(record, true, true);

    // Delete
    for (var i = 0; i < refunds.length; i++) {
        var id = refunds[i];
        nlapiLogExecution('DEBUG', 'LS', 'Delete refund ' + id);
        nlapiDeleteRecord('customerrefund', id);
    }    
    nlapiDeleteRecord('creditmemo', internalId);    
}