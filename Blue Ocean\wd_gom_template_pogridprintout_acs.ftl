<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
    {FTLUtil}
    <head>
        <style>
            {cssContent}
        </style>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale == "zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
        <#elseif .locale == "zh_TW">
            <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
        <#elseif .locale == "ja_JP">
            <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
        <#elseif .locale == "ko_KR">
            <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
        <#elseif .locale == "th_TH">
            <link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <#assign addtlData = record.custpage_po_grid_template_data?eval />
        <macrolist>
            <macro id="nlheader">
                <#assign blackLogo = addtlData.blackLogoURL />
                <#assign subsidiary = addtlData.subsidiary />
                <table width="100%" table-layout="fixed">
                    <tr>
                        <td width="60%" id="company_logo">
                            <#if companyInformation.logoUrl?length != 0>
                                <img float="left" width="120px" height="86.5px" padding-right="10px" src="${blackLogo}"/>
                            <#else>
                                <table width="120px" height="120px">
                                    <tr>
                                        <td></td>
                                    </tr>
                                </table>
                            </#if>
                        </td>
                        <td width="40%" align="center" vertical-align="middle" font-size="18pt" font-weight="bold" id="transaction_title" class="title">
                            ${record@title}
                        </td>
                    </tr>
                    <tr>
                        <td width="60%" id="company_logo">
                            &nbsp;
                        </td>
                    </tr>
                </table>
                <table width="100%" table-layout="fixed">
                    <tr>
                        <td rowspan="2" width="55%">
                            <table width="100%" align="left" id="company_info">
                                <tr>
                                    <td id="name">
                                        <#--  <@limitStringDisplay value=companyInformation.companyname?upper_case maxLength=50 lineFeed=""/>  -->
                                        <#--  ${companyInformation.companyname?upper_case}  -->
                                        <#--  <br />  -->
                                        <#--  <@updateAddressOverflowDisplay value=companyInformation.addresstext maxLength=35/>  -->
                                        <#if subsidiary??>
                                            <span class="companyName" id="gomp_inf_company_name">
                                                <@limitStringDisplay value=subsidiary.subsidiaryName maxLength=35 lineFeed="" /></span> <br />
                                            <#else>
                                                <span class="companyName" id="gomp_inf_company_name">
                                                    <@limitStringDisplay value=companyInformation.companyName maxLength=35 lineFeed="" /></span> <br />
                                        </#if>
                                        <#if subsidiary??>
                                            <span id="gomp_inf_mainaddress">
                                                ${subsidiary.address?eval}</span>
                                                
                                            <#else>
                                                <span id="gomp_inf_mainaddress">
                                                    <@updateAddressOverflowDisplay value=companyInformation.addressText maxLength=35 />
                                                    </span>
                                        </#if>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td width="45%">
                            <table width="100%" align="right" id="tran_details">
                                <tbody>
                                    <tr>
                                        <td class="header_label" id="trandate_lbl" font-weight="bold" width="120px" vertical-align="middle" align="right">${record.trandate@label}</td>
                                        <td class="header_info" id="trandate" font-weight="normal" align="left">${record.trandate}</td>
                                    </tr>
                                    <tr>
                                        <td class="header_label" id="tranid_lbl" font-weight="bold" width="120px" vertical-align="middle" align="right">${record.tranid@label}</td>
                                        <td class="header_info" id="tranid" font-weight="normal" align="left"><@limitStringDisplay value=record.tranid maxLength=20 lineFeed=""/></td>
                                    </tr>
                                    <tr>
                                        <td class="header_label" id="duedate_lbl" font-weight="bold" width="120px" vertical-align="middle" align="right">${record.duedate@label}</td>
                                        <td class="header_info" id="duedate" font-weight="normal" align="left">${record.duedate}</td>
                                    </tr>
                                </tbody>
                            </table>
                            <table width="100%">
                                <tbody>
                                    <tr>
                                        <td class="header_label" id="terms_lbl" font-weight="bold" width="120px" vertical-align="middle" align="right">${record.terms@label}</td>
                                        <td class="header_info" id="terms" font-weight="normal" align="left">${record.terms}</td>
                                    </tr>
                                    <tr>
                                        <td class="header_label" id="incoterm_lbl" font-weight="bold" width="120px" vertical-align="middle" align="right">${record.incoterm@label}</td>
                                        <td class="header_info" id="incoterm" font-weight="normal" align="left">${record.incoterm}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="custfooter" style="width: 100%;">
                    <tr>
                        <td>
                            <#if preferences.print_barcodes>
                                <barcode codetype="code128" showtext="true" value="${record.tranid}"/>
                            </#if>
                        </td>
                        <td align="right">
                            <pagenumber/> of <totalpages/>
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
    </head>
    <body header="nlheader" header-height="22%" size="A4" footer="nlfooter" footer-height="20pt">
        <table width="100%" id="other_info" border = "0.15" border-color = "#A0A0A0" table-layout="fixed">
            <tr>
                <#if !(record.billaddress == "")>
                    <td>
                        <table class="header" id="bill_to_info">
                            <tr>
                                <td class="addressheader" colspan="3" font-weight="bold" id="bill_addr_lbl" margin-bottom="5pt">${record.billaddress@label}:</td>
                            </tr>
                            <tr>
                                <td class="address" colspan="3" rowspan="2" id="bill_addr">${record.billaddress}</td>
                            </tr>
                        </table>
                    </td>
                </#if>
                <#if !(record.shipaddress == "null")>
                    <td border-left = "0.15" border-color = "#A0A0A0">
                        <table class="header" id="ship_to_info">
                            <tr>
                                <td class="addressheader" colspan="3" font-weight="bold" id="ship_addr_lbl" margin-bottom="5pt">${record.shipaddress@label}:</td>
                            </tr>
                            <tr>
                                <td class="address" colspan="3" rowspan="2" id="ship_addr">${record.shipaddress}</td>
                            </tr>
                        </table>
                    </td>
                </#if>
            </tr>
        </table>
        <br />
        <table width="100%" class="grid_table" table-layout="fixed" border="0.5">
            <thead class="grid_table_header" style="font-size: 8pt; text-align: left;">
                <tr id="grid_table_header">
                    <th rowspan="2" width="130px" id="grid_th_parent_name">${record.shipdate@label}</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">Sales Rep</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">${record.shipmethod@label}</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">Vendor Acct. No.</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">Vat Number</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">Customer PO</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">Deposit Req'd</th>
                </tr>
            </thead>
            <tbody class="grid_table_body">
                <tr style="font-size: 7pt;">
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px white-space: nowrap;" class="grid_row_attribute">${record.shipdate}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px white-space: nowrap;" class="grid_row_attribute">${record.employee}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px white-space: nowrap;" class="grid_row_attribute">${record.shipmethod}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px white-space: nowrap;" class="grid_row_attribute">${record.custpage_po_vendor_acct_no?keep_before(" VAT:")}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px white-space: nowrap;" class="grid_row_attribute">${record.custpage_po_vendor_acct_no?keep_after(" VAT:")}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px white-space: nowrap;" class="grid_row_attribute">${record.otherrefnum}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px white-space: nowrap;" class="grid_row_attribute"><@formatCurrencyDisplay value=record.custbody_blo_dep_req/></td>
                </tr>
            </tbody>
        </table>
        <br />
        <#assign tranTotalQty = 0/>
        <#assign tranTotalAmt = 0/>
        <#assign discountTotalAmt = 0/>
        <#assign markupTotalAmt = 0/>
        <#assign itemListTotalQty = 0/>
        <#assign itemListTotalAmt = 0/>
        <#assign itemListArr = []/>
        <#assign isItemGroupMember = "false"/>
        <#assign parentsData = addtlData.parentFields />
        <#if record.custbody_gridoe_productgrpdata?has_content>
        <#--- PREP ITEM DATA FOR GRID AND TAX SUMMARY-->
            <#assign parentItemName = {}>
            <#assign itemdetails = {}>
            <#list record.item as item>
                <#assign itemListMap = {}/>
                <#assign itemdetails = itemdetails + { "${item.custcol_gridoe_hidden_itemid}_${item.olditemid}" : {"rate": item.rate, "quantity": item.quantity, "amount": item.amount}}/>
                <#if !(parentItemName["${item.custcol_gridoe_hidden_itemid}"]?has_content)>
                    <#if item.custcol_gridoe_hidden_parentdispname?has_content>
                        <#assign parentItemName = parentItemName + {"${item.custcol_gridoe_hidden_itemid}" : item.custcol_gridoe_hidden_parentdispname}/>
                    <#elseif item.custcol_gridoe_hidden_parentname?has_content>
                        <#assign parentItemName = parentItemName + {"${item.custcol_gridoe_hidden_itemid}" : item.custcol_gridoe_hidden_parentname}/>
                    </#if>
                </#if>
                <#--  <#if item.custcol_gridoe_hidden_parentname?has_content>
                    <#list parentsData as parentData>
                        <#if item.custcol_gridoe_hidden_parentname?contains(parentData.itemid)>
                            <#assign parentDetail = parentData />
                        </#if>
                    </#list>
                </#if>  -->
                <#--- For Item List-->
                <#if !(item.custcol_gridoe_hidden_itemid?has_content)>
                    <#assign itemListMap = itemListMap + {"itemtype": item.itemtype}/>
                    <#if item.itemtype == 'Group'>
                        <#assign isItemGroupMember = "true">
                    <#elseif item.itemtype == 'EndGroup'>
                        <#assign isItemGroupMember = "false">
                    </#if>
                    <#if item.itemtype == 'Discount'>
                        <#assign discountTotalAmt = discountTotalAmt + item.amount/>
                    <#elseif item.itemtype == 'Markup'>
                        <#assign markupTotalAmt = markupTotalAmt + item.amount/>
                    <#elseif item.itemtype == 'Subtotal' || item.itemtype == 'Description' || item.itemtype == 'EndGroup'>
                    <#else>
                        <#if item.itemtype != 'Group'>
                            <#assign itemListMap = itemListMap + {"qty": item.quantity}/>
                            <#assign itemListMap = itemListMap + {"isitemgroupmember": isItemGroupMember}/>
                        </#if>
                        <#assign itemListMap = itemListMap + {"item": item.item}/>
                        <#--- Follows the display hierarchy for name: Vendor Name - Display Name - Item Name-->
                        <#if item.vendorname?has_content>
                            <#assign itemListMap = itemListMap + {"name": item.vendorname}/>
                        <#elseif item.custcol_gridoe_hidden_displayname?has_content>
                            <#assign itemListMap = itemListMap + {"name": item.custcol_gridoe_hidden_displayname}/>
                        <#else>
                            <#assign itemListMap = itemListMap + {"name": item.item}/>
                        </#if>
                        <#assign itemListMap = itemListMap + {"rate": item.rate}/>
                        <#assign itemListMap = itemListMap + {"amt": item.amount}/>
                        <#assign itemListArr = itemListArr + [itemListMap]/>
                        <#--- compute for total qty and amt-->
                        <#if item.itemtype != 'Group'>
                            <#assign itemListTotalQty = itemListTotalQty + item.quantity/>
                        </#if>
                        <#assign itemListTotalAmt = itemListTotalAmt + item.amount/>
                    </#if>
                </#if>
            </#list>
            <#--- START GRID---->
            <#assign gridlist = record.custbody_gridoe_productgrpdata>
            <#assign counter = 0>
            <#list gridlist?eval as gridData>
                <#assign counter++>
                <#if gridData?has_content>
                    <#--  <table id="grid_table_title_${counter}" class="grid_title_table">
                        <tr id="grid_template_name" width="100%">
                            <#if gridData.type == 'PG'>
                                <td class="gridtemplate_info">${gridData.name}</td>
                            <#else>
                                <#if parentItemName['${counter}']?has_content>
                                    <td class="gridtemplate_info">${parentItemName["${counter}"]}</td>
                                <#else>
                                    <td class="gridtemplate_info">${gridData.name}</td>
                                </#if>
                            </#if>
                        </tr>
                    </table>  -->
                    <#list parentsData as parentData>
                        <#if gridData.name?contains(parentData.itemid)>
                            <#assign parentDetail = parentData />
                        </#if>
                    </#list>
                    <#assign fontsize = "8pt">
                    <#if parentDetail.displayname?length gt 15 >
                        <#assign fontsize = "6pt">
                    </#if>
                    <#assign maxcol = 7>
                    <#assign totalcol = gridData.grid.header?size>
                    <#assign tablecount = ((totalcol)/maxcol)?ceiling>
                    <#assign gridTotalQty = 0/>
                    <#assign gridTotalAmt = 0/>
                    <#assign arrtable = []/>
                    <#list 1..tablecount as tablenum>
                        <#assign start = (tablenum-1) * (maxcol)>
                        <#assign end = min(start + maxcol-1, totalcol-1)>
                        <table width="100%" class="grid_table" table-layout="fixed" border="0.5">
                            <thead class="grid_table_header" style="font-size: 8pt; text-align: left;">
                                <tr id="grid_table_header">
                                    <th rowspan="2" width="120px" id="grid_th_parent_name">Season</th>
                                    <th rowspan="2" width="120px" id="grid_th_parent_name">Department</th>
                                    <th rowspan="2" width="125px" id="grid_th_parent_name">Theme</th>
                                    <th rowspan="2" width="125px" id="grid_th_parent_name">Category</th>
                                    <th rowspan="2" width="125px" id="grid_th_parent_name">MFG Style No.</th>
                                    <th rowspan="2" width="160px" id="grid_th_parent_name">Display Name</th>
                                    <th rowspan="2" width="135px" id="grid_th_parent_name">Article Code</th>
                                </tr>
                            </thead>
                            <tbody class="grid_table_body">
                                <tr style="font-size: 8pt;">
                                    <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.season_year}</td>
                                    <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.custitem_psgss_gender}</td>
                                    <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.custitem_bo_theme}</td>
                                    <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.custitem_psgss_category}</td>
                                    <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.vendorname}</td>
                                    <td style="font-size: ${fontsize}; align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.displayname}</td>
                                    <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px;" class="grid_row_attribute">${parentDetail.itemid}</td>
                                </tr>
                            </tbody>
                        </table>
                        <#--  <#if parentDetail.custitem_bhpc_prod_img?has_content>
                            <table width="100%" class="grid_table" table-layout="fixed" border="0.5">
                                <thead class="grid_table_header" style="font-size: 8pt; text-align: left;">
                                    <tr id="grid_table_header">
                                        <th rowspan="2" width="130px" id="grid_th_parent_name">Product Image</th>
                                    </tr>
                                </thead>
                                <tbody class="grid_table_body">
                                    <tr style="font-size: 8pt;">
                                        <td style="align:center; background-color:#FFFFFF; padding-left:10px; padding-right:10px white-space: nowrap;" class="grid_row_attribute">
                                            <img width="20%" height="25%" padding-right="10px" src="${parentDetail.custitem_bhpc_prod_img}"/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </#if>  -->
                            <table class="grid_table" id="grid_table_${counter}_${tablenum}" border="0.5" width="100%" table-layout="fixed">
                                <thead class="grid_table_header">
                                    <tr id="grid_table_header">
                                        <#if gridData.type == 'PG'>
                                            <th rowspan="2" width="115px" id="grid_th_parent_name">Color</th>
                                        <#else>
                                            <th rowspan="2" width="115px" id="grid_th_parent_name">Color</th>
                                        </#if>
                                        <#if start <= end>
                                            <#list gridData.grid.header[start..end] as column>      
                                                <th colspan="1" style="font-size: 7pt;" id="grid_th_col_attr${column.colAttr}">${column.name}</th>
                                            </#list>
                                        </#if>
                                        <th colspan="1" width="75px" id="grid_th_qty">${record.item[0].quantity@label}</th>
                                        <th colspan="1" width="80px" id="grid_th_rate">${record.item[0].rate@label}</th>
                                        <th colspan="1" width="100px" id="grid_th_amt">${record.item[0].amount@label}</th>
                                    </tr>
                                </thead>
                                <tbody class="grid_table_body">
                                    <#list gridData.grid.data as row>
                                        <#assign maptable = {}/>
                                        <#assign maptable = maptable + {"attr": row.rowattribute}/>
                                        <#if 0 <= totalcol-1>
                                            <#assign ctr = 0>
                                            <#list row.columnids[0..totalcol-1] as colid>
                                                <#assign tmpid = row[colid]['internalid']?c>
                                                <#if tmpid?has_content>
                                                    <#if itemdetails["${counter}_${tmpid}"]?has_content>
                                                        <#assign maptable = maptable + {"col" + ctr: itemdetails['${counter}_${tmpid}']['quantity']}/>
                                                        <#assign maptable = maptable + {"colnum" + ctr: itemdetails['${counter}_${tmpid}']['quantity']}/>
                                                        <#assign maptable = maptable + {"rate" + ctr: itemdetails['${counter}_${tmpid}']['rate']}/>
                                                        <#assign maptable = maptable + {"amt" + ctr: itemdetails['${counter}_${tmpid}']['amount']}/>
                                                    <#else>
                                                        <#assign maptable = maptable + {"col" + ctr: " "}/>
                                                        <#assign maptable = maptable + {"colnum" + ctr: 0}/>
                                                        <#assign maptable = maptable + {"amt" + ctr: 0}/>
                                                    </#if>
                                                </#if>
                                                <#assign ctr++>
                                            </#list>
                                            <#--- adds maps to an array-->
                                            <#assign arrtable = arrtable + [maptable]/>
                                        </#if>
                                    </#list>
                                    <#--- prints values to table per row-->
                                    <#assign rowCount = 0/>
                                    <#assign totalColTable = {}/>
                                    <#list arrtable as mapy>
                                        <#assign colTotal = 0/>
                                        <#assign amtTotal = 0/>
                                        <#assign rateTxt = "">
                                        <#assign dispRates = []/>
                                        <#if rowCount < gridData.grid.data?size>
                                            <#if start <= end>
                                                <#list start..end as i>
                                                    <#--- computes the total qty and amt per table-->
                                                    <#assign colTotal = colTotal + mapy["colnum" + i]/>
                                                    <#assign amtTotal = amtTotal + mapy["amt" + i]/>
                                                    <#if totalColTable["col" + i]?has_content>
                                                        <#assign totalColTable = totalColTable + {"col" + i: totalColTable["col" + i] + mapy["colnum" + i]}/>
                                                    <#else>
                                                        <#assign totalColTable = totalColTable + {"col" + i: mapy["colnum" + i]}/>
                                                    </#if>
                                                    <#if mapy["col" + i] gte 0>
                                                        <#assign rateFormat><@formatCurrencyDisplay value=mapy["rate" + i]/></#assign>
                                                        <#if !(dispRates?seq_contains(rateFormat))>
                                                            <#assign dispRates = dispRates + [rateFormat]/>
                                                        </#if>
                                                    </#if>
                                                </#list>
                                                <#list 0..dispRates?size-1 as k>
                                                    <#assign rateTxt = rateTxt + dispRates[k] + "<br/>">
                                                </#list>
                                                <#assign rateTxt = rateTxt?remove_ending("<br/>")>
                                                <#--- computes the total qty and amt per template-->
                                                <#assign gridTotalQty = gridTotalQty + colTotal/>
                                                <#assign gridTotalAmt = gridTotalAmt + amtTotal/>
                                            </#if>
                                            <tr id="grid_row_${rowCount+1}">
                                                <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px" class="grid_row_attribute" id="grid_row_attr">${mapy["attr"]}</td>
                                                <#if start <= end>
                                                    <#list start..end as i>
                                                        <#assign qtyTxt = "">
                                                        <#assign rateFormatted><@formatCurrencyDisplay value=mapy["rate" + i]/></#assign>
                                                        <#assign rowValueCtr = dispRates?seq_index_of(rateFormatted)>
                                                        <#if mapy["col" + i]?has_content> <#--- adds spaces for display of quantites with different rates-->
                                                            <#list 0..rowValueCtr-1 as x>
                                                                <#if rowValueCtr gt 0>
                                                                    <#assign qtyTxt = qtyTxt + "<br/>">
                                                                </#if>
                                                            </#list>
                                                        </#if>
                                                        <td align="center" width="120px" background-color = "#FFFFFF" id="grid_row_qty_${i}">${qtyTxt}<@formatNumberDisplay value=mapy["col" + i]/></td>
                                                    </#list>
                                                </#if>
                                                <td align="center" background-color = "#FFFFFF" id="grid_row_qty"><@formatNumberDisplay value=colTotal/></td>
                                                <td align="center" background-color = "#FFFFFF" id="grid_row_rate"><p align="center">${rateTxt}</p></td>
                                                <td align="right" background-color = "#FFFFFF" id="grid_row_amt" padding-right="10px"><@formatCurrencyDisplay value=amtTotal/></td>
                                            </tr>
                                        </#if>
                                        <#assign rowCount++/>
                                    </#list>
                                    <#if tablecount == tablenum> <#--- prints the total per template to the last table-->
                                        <tr id="grid_ttl">
                                            <td class="grid_row_attribute" background-color = "#e3e3e3"></td>
                                            <#if start <= end>
                                                <#list start..end as i>
                                                    <#if tablecount == 1>
                                                        <td align="center" width="100px" background-color = "#e3e3e3" id="grid_ttl_col_qty_${i}"><@formatNumberDisplay value=totalColTable["col" + i]/></td>
                                                    <#else>
                                                        <td background-color = "#e3e3e3"></td>
                                                    </#if>
                                                </#list>
                                            </#if>
                                            <td align="center" width="100px" background-color = "#e3e3e3" id="grid_ttl_qty"><@formatNumberDisplay value=gridTotalQty/></td>
                                            <td background-color = "#e3e3e3"></td>
                                            <td align="right" width="100px" background-color = "#e3e3e3" id="grid_ttl_amt" padding-right="10px"><@formatCurrencyDisplay value=gridTotalAmt/></td>
                                        </tr>
                                    </#if>
                                </tbody>
                            </table>
                        <#--  <#if tablecount gt tablenum>  -->
                            <br/>
                        <#--  </#if>  -->
                    </#list>
                    <#--- computes for the total qty and amt for the whole transaction-->
                    <#assign tranTotalQty = tranTotalQty + gridTotalQty/>
                    <#assign tranTotalAmt = tranTotalAmt + gridTotalAmt/>
                </#if>
            </#list>
        </#if>
        <#--- END GRID ---->
        <br/>
        <#-- START ITEMLIST -->
        <#if itemListArr?has_content>
            <table id="item_list" class="grid_itemlist" style="width: 100%; margin-top: 10px;" border = "0.5">
                <thead background-color="#A0A0A0" color="#FFFFFF" class="grid_table_header">
                    <tr id="item_list_header" font-weight= "bold">
                            <th width="90px" align="center" colspan="3" id="item_list_th_qty" border-right="0.5" border-color="#A0A0A0">${record.item[0].quantity@label}</th>
                            <th align="center" colspan="12" id="item_list_th_item" border-right="0.5" border-color="#A0A0A0">${record.item[0].item@label}</th>
                            <th align="center" colspan="10" id="item_list_th_name" border-right="0.5" border-color="#A0A0A0">Name</th>
                            <th width="90px" align="center" colspan="4" id="item_list_th_rate" border-right="0.5" border-color="#A0A0A0">${record.item[0].rate@label}</th>
                            <th width="90px" align="center" colspan="4" id="item_list_th_amt" border-color="#A0A0A0">${record.item[0].amount@label}</th>
                    </tr>
                </thead>
                <tbody>
                    <#assign ctr = 0>
                    <#list itemListArr as mapy>
                        <#assign ctr++>
                        <tr id="item_list_row_${ctr}">
                        <#assign indent = "10px"/>
                            <td align="right" colspan="3" line-height="150%" id="item_list_qty" border-right="0.5" border-color="#A0A0A0" padding-right="10px"><@formatNumberDisplay value=mapy.qty/></td>
                            <#if mapy.itemtype != 'Group' && mapy.isitemgroupmember == "true">
                                    <#assign indent = "20px"/>
                            </#if>
                            <td align="left" colspan="12" id="item_list_item" border-right="0.5" border-color="#A0A0A0" padding-left="${indent}" padding-right="10px">${mapy.item}</td>
                            <td align="left" colspan="10" id="item_list_name" border-right="0.5" border-color="#A0A0A0" padding-left="${indent}" padding-right="10px">${mapy.name}</td>
                            <td align="right" colspan="4" id="item_list_rate" border-right="0.5" border-color="#A0A0A0" padding-right="15px"><@formatCurrencyDisplay value=mapy.rate/></td>
                            <td align="right" colspan="4" id="item_list_amt" border-color="#A0A0A0" padding-right="10px"><@formatCurrencyDisplay value=mapy.amt/></td>
                        </tr>
                    </#list>
                    <tr id="item_list_ttl" background-color = "#e3e3e3">
                        <td align="right" colspan="3" line-height="150%" id="item_list_ttl_qty" border-right="0.5" border-color="#A0A0A0" padding-right="10px"><@formatNumberDisplay value=itemListTotalQty/></td>
                        <td colspan="12" border-right="0.5" border-color="#A0A0A0"></td>
                        <td colspan="10" border-right="0.5" border-color="#A0A0A0"></td>
                        <td colspan="4" border-right="0.5" border-color="#A0A0A0"></td>
                        <td align="right" colspan="4" id="item_list_ttl_amt" border-color="#A0A0A0" padding-right="10px"><@formatCurrencyDisplay value=itemListTotalAmt/></td>
                    </tr>
                </tbody>
            </table>

            <#--- computes for the total qty and amt for the whole transaction-->
            <#assign tranTotalQty = tranTotalQty + itemListTotalQty/>
            <#assign tranTotalAmt = tranTotalAmt + itemListTotalAmt/>
        </#if>
        <#-- END ITEMLIST -->
        <br/>
        <#--- displays the total qty and amt for the whole transaction-->
        <table align="right" page-break-inside="avoid" id="totals">
            <tr font-size = "13px">
                <td width="65%" colspan="2" align="right" padding-right="10px" id="subttl_lbl">Total ${record.item[0].quantity@label}</td>
                <td width="35%" align="right" padding-right="15px" id="subttl"><@formatNumberDisplay value=tranTotalQty/></td>
            </tr>
            <tr font-size = "13px">
                <td width="65%" colspan="2" align="right" padding-right="10px" id="subttl_lbl">Subtotal</td>
                <td width="35%" align="right" padding-right="15px" id="subttl"><@formatCurrencyDisplay value=tranTotalAmt/></td>
            </tr>
            <tr font-size="13px">
                <td width="65%" colspan="2" align="right" padding-right="10px" id="disc_ttl_lbl">Discounts</td>
                <td width="35%" align="right" padding-right="15px" id="disc_ttl"><@formatCurrencyDisplay value=discountTotalAmt/></td>
            </tr>
            <tr font-size="13px">
                <td width="36%" colspan="2" align="right" padding-right="10px" id="markup_ttl_lbl">Markups</td>
                <td width="35%" align="right" padding-right="15px" id="markup_ttl"><@formatCurrencyDisplay value=markupTotalAmt/></td>
            </tr>
            <tr font-size="13px" >
                <td width="65%" colspan="2" align="right" padding-right="10px" id="tax_ttl_lbl">${record.taxtotal@label}</td>
                <td width="35%" align="right" padding-right="15px" id="tax_ttl"><@formatCurrencyDisplay value=record.taxtotal/></td>
            </tr>
            <#if record.tax2total?has_content>
                <tr font-size="13px" >
                    <td width="65%" colspan="2" align="right" padding-right="10px" id="tax2_ttl_lbl">${record.tax2total@label}</td>
                    <td width="35%" align="right" padding-right="15px" id="tax2_ttl"><@formatCurrencyDisplay value=record.tax2total/></td>
                </tr>
            </#if>
            <#if addtlData.billsCtr != 0>
                <tr font-weight="bold" font-size="13px" >
                    <td width="65%" colspan="2" align="right" padding-right="10px" id="subttl_lbl">${record.total@label}</td>
                    <td width="35%" align="right" padding-right="15px" id="subttl"><@formatCurrencyDisplay value=record.total/></td>
                </tr>
                <tr font-size="13px" >
                    <td width="65%" colspan="2" align="right" padding-right="10px" id="subttl_lbl">Payment on Account</td>
                    <td width="35%" align="right" padding-right="15px" id="subttl"><@formatCurrencyDisplay value=addtlData.billsTotal/></td>
                </tr>
                <tr background-color="#e3e3e3" font-weight="bold" font-size="15px" >
                    <td width="65%" colspan="2" align="right" padding-right="10px" id="subttl_lbl" style="border-top:1;">Amount Due</td>
                    <td width="35%" align="right" padding-right="15px" id="subttl" style="border-top:1;"><@formatCurrencyDisplay value=addtlData.recTotal/></td>
                </tr>
            <#else>
                <tr background-color="#e3e3e3" font-weight="bold" font-size="15px" >
                    <td width="65%" colspan="2" align="right" padding-right="10px" id="subttl_lbl" style="border-top:1;">${record.total@label}</td>
                    <td width="35%" align="right" padding-right="15px" id="subttl" style="border-top:1;"><@formatCurrencyDisplay value=record.total/></td>
                </tr>
            </#if>
        </table>
    </body>
</pdf>