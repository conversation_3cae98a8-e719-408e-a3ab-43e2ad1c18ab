/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/format'], function(search, record, format) {

    function getInputData() {
        var itemSearch = search.load({
            id: 'customsearch_acs_items_with_receipt'
        });
        var resultArray = [];
        var exists = [];
        var caught = [];
        
        var pagedData = itemSearch.runPaged({ pageSize: 1000 });
        // iterate the pages
        for( var i=0; i < pagedData.pageRanges.length; i++ ) {
    
            // fetch the current page data
            var currentPage = pagedData.fetch(i);
    
            // and forEach() thru all results
            currentPage.data.forEach( function(result) {
                var itemId = result.getValue({ name: 'item', summary: 'GROUP' });
                var tranDate = result.getValue({ name: 'trandate', summary: 'GROUP' });
                if(exists.indexOf(itemId) === -1){
                    resultArray.push({
                        itemId: itemId,
                        tranDate: tranDate
                    });
                    exists.push(itemId);
                } else {
                    caught.push(itemId);
                }
            });
    
        }

        log.debug('Repeating Items', { caught: caught});

        return resultArray;
    }

    function reduce(context) {
        
        var reduceKey = context.key;
        var reduceValues = context.values;
        
        try {

            var parsedValue = JSON.parse(reduceValues[0]);
            var recObj = record.load({
                type: record.Type.INVENTORY_ITEM,
                id: parsedValue.itemId
            });

            var line = recObj.findSublistLineWithValue({
                sublistId: 'locations',
                fieldId: 'location',
                value: 1
            });            

            var nextCountDate = new Date(parsedValue.tranDate);
            nextCountDate.setDate(nextCountDate.getDate() + 30);

            recObj.setSublistValue({
                sublistId: 'locations',
                fieldId: 'nextinvtcountdate',
                line: line,
                value: nextCountDate
            });

            recObj.setSublistValue({
                sublistId: 'locations',
                fieldId: 'invtcountinterval',
                line: line,
                value: 30
            });

            recObj.setValue({
                fieldId: 'custitem_acs_mr_udpated',
                value: true,
                ignoreFieldChange: true
            });

            var recId = recObj.save({
                enableSourcing: true,
                ignoreMandatoryFields: true
            });

            if(recId) {
                log.debug('Successfuly saved', 'id: ' + recId);
            }

        } catch (e) {
            log.debug('error', e);
        }
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        reduce: reduce,
        summarize: summarize
    }
});
