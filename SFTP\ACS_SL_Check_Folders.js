/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/sftp'], function(sftp) {

    function onRequest(context) {
        var connection = sftp.createConnection({
            username: 'CorestreamZZZ',
            keyId: 'custkey6',
            url: 'securedata.regionstest.com',
            port: Number(22),
            hostKey: 'AAAAB3NzaC1yc2EAAAADAQABAAABAQCXJemYQMzXikIrpNQRHTZ85iSCZeK7DAFGFNmN3IKv89DBqZYEymqaJzKNxNqfhlI40k0fQ7nboX+gTIR4xz5do7aaawnDdKSi9NP+o2BWWHAAjp8t8rjEdXH8zvnt07QQtK3E95gGkHStR2Tg4vbSfyUPNb1/bO9bJrottd3mEyX8qFoO50L2LmyiciIMZ8qmpQnpOH8xICSMHKf+g3AKPsWbxbDHbGRN3MRv6m3KqBgskUyzO14ojYdI9A6WuQuiyGUZQu5IKQJea2sOCKhdE96drY55IsT4Ti+wuB0kpUnhtOVMfB5sPQcp5ahS7DlDIo64SDFM0mmJ6diDM1Pj'
        });

        log.debug("test", connection.list({
            path: '/BAI'
        }));
    }

    return {
        onRequest: onRequest
    }
});
