/**
 * @NApiVersion 2.x
 * @NScriptType Suitelet
 * @NModuleScope SameAccount
 */
 define(['N/runtime', 'N/ui/serverWidget'],
 /**
  * @param {runtime} runtime
  * @param {serverWidget} serverWidget
  */
 function(runtime, serverWidget) {
    
     /**
      * Definition of the Suitelet script trigger point.
      *
      * @param {Object} context
      * @param {ServerRequest} context.request - Encapsulation of the incoming request
      * @param {ServerResponse} context.response - Encapsulation of the Suitelet response
      * @Since 2015.2
      */
     function onRequest(context) {
         var myForm = serverWidget.createForm({
             title : 'Password Generator'
         });
         
         var scriptObj = runtime.getCurrentScript();
         
         var mydomain = scriptObj.getParameter({name: 'custscriptdomain'});
         var myscriptid= scriptObj.getParameter({name: 'custscriptscriptid'});
         
         var credField = myForm.addCredentialField({
             id : 'custpage_password',
             label : 'PassWord',
             restrictToDomains : mydomain,
             restrictToScriptIds : myscriptid,
             restrictToCurrentUser : false,
         });
         credField.maxLength = 64;
         if (context.request.method === 'POST') {
             /*
             var mypassword = myForm.getField({
                 id : 'custpage_password'
             });
             */
             var mypassword = context.request.parameters.custpage_password;
             var myfield = myForm.addField({
                 id : 'custpage_passwordout',
                 type : serverWidget.FieldType.TEXT,
                 label : 'Password Output'
             });
             myfield.defaultValue = mypassword;
             log.debug('my password',mypassword)
             
         }
         myForm.addSubmitButton({
             label : 'Submit'
         });
         
         context.response.writePage(myForm);
         
     }
 
     return {
         onRequest: onRequest
     };
     
 });
 