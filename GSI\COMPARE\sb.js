/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
 define(['N/record'], function(record) {

    function beforeLoad(context) {
        if(context.type == context.UserEventType.CREATE){
            var formObj = context.form;
            var shipMethodField = formObj.getField({
                id: 'shipmethod'
            });
            shipMethodField.defaultValue = 4779;
        }
    }

    function afterSubmit(context) {
        
        if(context.type == context.UserEventType.CREATE){
            var recObj = context.newRecord;

            var soId = recObj.getValue({ fieldId: 'createdfrom' });

            if(soId) {

                var soObj = record.load({
                    id: soId,
                    type: record.Type.SALES_ORDER,
                    isDynamic: true
                });

                // if IF count is blank, replace with 0 int
                var IFCount = soObj.getValue({ fieldId: 'custbody_ff_count' }) ? parseInt(soObj.getValue({ fieldId: 'custbody_ff_count' })) : 0;
              	log.debug('IF Count', IFCount);
              	IFCount = IFCount + 1;
              	log.debug('IF Count + 1', IFCount);
                soObj.setValue({ fieldId: 'custbody_ff_count', value: IFCount });

                soObj.save({
                    enableSourcing: false,
                    ignoreMandatoryFields: true
                });

            }
        }
    }

    return {
        beforeLoad: beforeLoad,
        afterSubmit: afterSubmit
    }
});
