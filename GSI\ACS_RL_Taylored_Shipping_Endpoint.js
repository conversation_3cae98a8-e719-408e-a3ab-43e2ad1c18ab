/**
 *@NApiVersion 2.x
 *@NScriptType Restlet
 */
define(['N/query', 'N/search', 'N/record'], function(query, search, record) {

    function getInboundShipmentList(dateCreated, inbShipId) {

        var returnArr = [];

        var inbShipSearch = search.load({
            id: 'customsearch_acs_inbound_shipment'
        });

        if(inbShipId) {
            var idFilter = search.createFilter({
                name: 'internalidnumber',
                operator: search.Operator.EQUALTO,
                values: inbShipId
            }); 

            inbShipSearch.filters.push(idFilter);
        }

        if(dateCreated) {
            var dateFilter = search.createFilter({
                name: 'formuladate',
                formula: ['TO_DATE({createddate})'],
                operator: search.Operator.ON,
                values: [dateCreated]
            });

            inbShipSearch.filters.push(dateFilter);
        }
        

        var inboundShipments = [];
        var inboundShipmentObj = null;

        inbShipSearch.run().each(function(result){

            var shipmentNo = result.getValue({ name: 'shipmentnumber' });
            var x = result.getValue({ name: "linesequencenumber", join: "purchaseOrder" });
            log.debug('linesequencenumber', { linesequencenumber : x});

            if(inboundShipments.indexOf(shipmentNo) !== -1) {

                var poNo = result.getText({ name: 'purchaseorder' });
                if(inboundShipmentObj.po_nos.indexOf(poNo) === -1){
                    inboundShipmentObj.po_nos.push(poNo);
                }
                var line_no = String(inboundShipmentObj.items.length + 1);
                inboundShipmentObj.items.push({
                    line_no: line_no,
                    po_no: result.getText({ name: 'purchaseorder' }),
                    sku_id: result.getValue({ name: 'item' }),
                    sku: result.getText({ name: 'item' }),
                    expected_qty: result.getValue({ name: 'quantityexpected' })
                })


            } else {

                if(inboundShipmentObj !== null) {
                    returnArr.push(inboundShipmentObj);
                }

                inboundShipments.push(shipmentNo);

                // this is to get the line number relative to the inbound shipment
                inboundShipmentObj = {
                    internalid: result.getValue({ name: 'internalid' }),
                    container_no: result.getValue({ name: 'externaldocumentnumber' }),
                    shipment_no: result.getValue({ name: 'shipmentnumber' }),
                    agent_ship_id: result.getValue({ name: 'billoflading' }),
                    arrival_date: result.getValue({ name: 'expecteddeliverydate' }),
                    date_created: result.getValue({ name: 'createddate' }),
                    po_nos: [result.getText({ name: 'purchaseorder' })],
                    items: [{
                        line_no: "1",
                        po_no: result.getText({ name: 'purchaseorder' }),
                        sku_id: result.getValue({ name: 'item' }),
                        sku: result.getText({ name: 'item' }),
                        expected_qty: result.getValue({ name: 'quantityexpected' })
                    }],                    
                };

                // this is to get the line number relative to the PO itself
                // inboundShipmentObj = {
                //     internalid: result.getValue({ name: 'internalid' }),
                //     container_no: result.getValue({ name: 'externaldocumentnumber' }),
                //     shipment_no: result.getValue({ name: 'vesselnumber' }),
                //     agent_ship_id: '',
                //     arrival_date: result.getValue({ name: 'expecteddeliverydate' }),
                //     po_nos: [result.getText({ name: 'purchaseorder' })],
                //     items: [{
                //         line_no: result.getValue({ name: "linesequencenumber", join: "purchaseOrder" }),
                //         po_no: result.getText({ name: 'purchaseorder' }),
                //         sku_id: result.getValue({ name: 'item' }),
                //         sku: result.getText({ name: 'item' }),
                //         expected_qty: result.getValue({ name: 'quantityexpected' })
                //     }],                    
                // };

            }
            
            return true;

        });
        
        if(inboundShipmentObj !== null) {
            returnArr.push(inboundShipmentObj);
        }
        return returnArr;

    }
    function _get(context) { 

        var tranDate = ((context.trandate) ? context.trandate : '');
        var inboundShipmentId =  ((context.inbound_shipment_id) ? context.inbound_shipment_id : '');
        var inbsList = [];

        try {

            inbsList = getInboundShipmentList(tranDate, inboundShipmentId)

            if(inbsList.length == 0) {
                inbsList = [];
            }

            
            return {
                success: 1,
                count: inbsList.length,
                result: inbsList
            }

        } catch (e) {
            log.error('Error', e);
            return {
                success: 0,
                message: e.message
            }
        }
    }

    function _post(context) {
        
    }

    function _put(context) {
        
    }

    function _delete(context) {
        
    }

    return {
        get: _get,
        post: _post,
        put: _put,
        delete: _delete
    }
});
