/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/record', 'N/https'], function(record, https) {

    function onRequest(context) {
        

        if(context.request.method == https.Method.GET){

            var contract_id = context.request.parameters.contract_id;

            var recObj = record.load({
                type: 'customrecord_contracts',
                id: contract_id,
                isDynamic: true
            });
    
            var origContractId = recObj.getValue({
                fieldId: 'custrecord_swe_original_contract'
            });

            var updated = recObj.getValue({
                fieldId: 'custrecord_acs_updated'
            });
            if(updated === 'T' || updated){
                if(origContractId) {
                    var searchObj = search.load({
                        id: 'customsearch_acs_contract_search'
                    });
        
                    var contractIdFilter = search.createFilter({
                        name: 'custrecord_swe_original_contract',
                        operator: search.Operator.ANYOF,
                        values: [origContractId]
                    });
                    searchObj.filters.push(contractIdFilter);
        
                    var resultSet = searchObj.run();
        
                    searchResult = resultSet.getRange({ start: 1, end: 2 });
                    searchResult.forEach(function(row) {
                        row.columns.forEach(function(column) {
                            if(column.name == 'internalid') {
                                log.audit('Original Contract ID', row.getValue(column));
                                return;
                            } 
                            log.debug('Test', { name: column.name, value: row.getValue(column) });
                            recObj.setValue({
                                fieldId: column.name,
                                value: row.getValue(column)
                            });
                        });
                    });

                    recObj.setValue({
                        fieldId: 'custrecord_acs_updated',
                        value: true
                    });

                    try{
                        recObj.save();
                    } catch (e) {
                        log.debug("Error", e);
                    }
                }
            }
        }
    }

    return {
        onRequest: onRequest
    }
});
