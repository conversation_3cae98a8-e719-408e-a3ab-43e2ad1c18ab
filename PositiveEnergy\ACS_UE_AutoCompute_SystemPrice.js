/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function sysPriceChanged(newRec, oldRec){

        if(oldRec){
            var newSysPrice = newRec.getValue({ fieldId: 'custbody_pes_system_price' });
            var oldSysPrice = oldRec.getValue({ fieldId: 'custbody_pes_system_price' });
            var sysPriceBool = ((oldSysPrice || newSysPrice) && (newSysPrice !== oldSysPrice));

            var newSubTotal = parseFloat(newRec.getValue({ fieldId: 'subtotal' }));
            var oldSubTotal = parseFloat(oldRec.getValue({ fieldId: 'subtotal' }));
            var subTotalBool = ((oldSubTotal || newSubTotal) && (newSubTotal !== oldSubTotal));

            var newCount = newRec.getLineCount({ sublistId: 'item' });
            var oldCount = oldRec.getLineCount({ sublistId: 'item' });
            var itemCountBool = (newCount != oldCount);
            
            return ((sysPriceBool || itemCountBool) || subTotalBool);
        } 

        // if new record, just return true
        return true;
    }

    function afterSubmit(context) {
        var newRecObj = context.newRecord;
        var oldRec = context.oldRecord;
        
        // check if system price has changed
        if(sysPriceChanged(newRecObj, oldRec)) {

            var recordObj = record.load({
                type: record.Type.SALES_ORDER,
                id: newRecObj.id
            });
            var systemPrice = parseFloat(recordObj.getValue({
                fieldId: 'custbody_pes_system_price'
            }));

           // check if added value already exist            
           var addedItem = recordObj.findSublistLineWithValue({
                sublistId: 'item',
                fieldId: 'item',
                value: 6528
            });

            log.debug({
                test: 'test',
                details: {
                    systemPrice: systemPrice
                }
            });
            if(systemPrice){
     

                if(addedItem != -1){

                    // get added item current value
                    var addedItemCurrentValue = recordObj.getSublistValue({
                        sublistId: 'item',
                        fieldId: 'amount',
                        line: addedItem
                    });
                    
                    // get subtotal
                    var subTotal = parseFloat(recordObj.getValue({
                        fieldId: 'subtotal'
                    }));
                    
                    // deduct current item value to subtotal
                    subTotal = subTotal - parseFloat(addedItemCurrentValue);

                    // recompute
                    var subsysDifference = systemPrice - subTotal;


                    // if item already exist, overwrite previous value
                    recordObj.setSublistValue({
                        sublistId: 'item',
                        fieldId: 'amount',
                        line: addedItem,
                        value: subsysDifference
                    });

                    recordObj.save({
                        enableSourcing: true,
                        ignoreMandatoryFields: true
                    });

                } else {

                    // reload recordObj as dynamic
                    var recordObj = record.load({
                        type: record.Type.SALES_ORDER,
                        id: newRecObj.id,
                        isDynamic: true
                    });

                    // get subtotal and system price difference
                    var subTotal = parseFloat(recordObj.getValue({
                        fieldId: 'subtotal'
                    }));

                    var subsysDifference = systemPrice - subTotal;

                    // insert new item with subtotal and system price difference
                    var lineCount = recordObj.getLineCount({
                        sublistId: 'item'
                    });

                    recordObj.insertLine({
                        sublistId: 'item',
                        line: lineCount,
                    });

                    recordObj.setCurrentSublistValue({
                        sublistId: 'item',
                        fieldId: 'item',
                        value: 6528
                    });

                    recordObj.setCurrentSublistValue({
                        sublistId: 'item',
                        fieldId: 'quantity',
                        value: 1
                    });

                    recordObj.setCurrentSublistValue({
                        sublistId: 'item',
                        fieldId: 'amount',
                        value: subsysDifference
                    });

                    recordObj.commitLine({
                        sublistId: 'item'
                    });

                    recordObj.save({
                        enableSourcing: true,
                        ignoreMandatoryFields: true
                    });
                }

            } else {
                
                if(addedItem != -1){
                    // if item already exist, overwrite previous value
                    recordObj.removeLine({
                        sublistId: 'item',
                        line: addedItem
                    });

                    recordObj.save({
                        enableSourcing: true,
                        ignoreMandatoryFields: true
                    });
                } 
            }
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
