<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
	{FTLUtil}
	<head>
		<style>
            table.custfooter {
                width: 100%;
                table-layout: fixed;
            }
            table.custfooter td {
                padding: 0px;
                font-size: 8pt;
            }
            /* Shared CSS */
            body {
                padding: 0.5in;
                size: letter;
                font-size: 9px;
                font-family: Tahoma, Geneva, sans-serif;
            }
            table {
                font-size: 9pt;
            }
            td p {
                align: left;
            }
            /* Report Grid Template Section */
            span.grid_templatename {
                font-weight: bold;
                line-height: 150%;
                font-size: 9pt;
            }
            table.grid_table {
                width: 100%;
                table-layout: fixed;
            }
            table.grid_table_title {
                margin-top: 20px;
                page-break-after: avoid;
            }
            td.gridtemplate_info {
                font-weight: bold;
            }
            thead.grid_table_header th {
                color: #FFFFFF;
                align: center;
                background-color: #A0A0A0;
                font-weight: bold;
                border: 0.1;
                border-color: #A0A0A0;
                height: 20px;
                vertical-align: middle;
                font-size: 7pt;
            }
            th p {
                align:center;
            }
            tbody.grid_table_body td {
                border: 0.1;
                border-color: #A0A0A0;
            }
            tbody.grid_table_body p {
                align: right;
            }
		</style>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale == "zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
        <#elseif .locale == "zh_TW">
            <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
        <#elseif .locale == "ja_JP">
            <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
        <#elseif .locale == "ko_KR">
            <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
        <#elseif .locale == "th_TH">
            <link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <#assign addtlData = record.custbody_acs_inv_grid_templt_data?eval />
		<macrolist>
			<macro id="nlheader">
                <#assign blackLogo = addtlData.blackLogoURL />
                <#assign subsidiary = addtlData.subsidiary />
				<table width="100%" table-layout="fixed">
                    <tr>
                        <td width="60%" id="company_logo">
                            <#if companyInformation.logoUrl?length != 0>
                                <img float="left" width="120px" height="86.5px" padding-right="10px" src="${blackLogo}"/>
                            <#else>
                                <table width="120px" height="120px">
                                    <tr>
                                        <td></td>
                                    </tr>
                                </table>
                            </#if>
                        </td>
                        <td width="40%" align="center" vertical-align="middle" font-size="18pt" font-weight="bold" id="transaction_title" class="title">
                            ${record@title}
                        </td>
                    </tr>
                    <tr>
                        <td width="60%" id="company_logo">
                            &nbsp;
                        </td>
                    </tr>
                </table>
				<table width="100%" table-layout="fixed">
					<tr>
						<td width="55%">
							<table width="100%" align="left" id="company_info">
                                <tr>
                                    <td id="name">
                                        <#--  <@limitStringDisplay value=companyInformation.companyname?upper_case maxLength=50 lineFeed=""/>  -->
                                        <#--  ${companyInformation.companyname?upper_case}  -->
                                        <#--  <br />  -->
                                        <#--  <@updateAddressOverflowDisplay value=companyInformation.addresstext maxLength=35/>  -->
                                        <#if subsidiary??>
                                            <span class="companyName" id="gomp_inf_company_name">
                                                <@limitStringDisplay value=subsidiary.subsidiaryName?upper_case maxLength=35 lineFeed="" /></span> <br />
                                            <#else>
                                                <span class="companyName" id="gomp_inf_company_name">
                                                    <@limitStringDisplay value=companyInformation.companyName maxLength=35 lineFeed="" /></span> <br />
                                        </#if>
                                        <#if subsidiary??>
                                            <span id="gomp_inf_mainaddress">
                                                ${record.custbody6}</span>
                                                
                                            <#else>
                                                <span id="gomp_inf_mainaddress">
                                                    <@updateAddressOverflowDisplay value=companyInformation.addressText maxLength=35 />
                                                    </span>
                                        </#if>
                                    </td>
                                </tr>
                            </table>
						</td>
						<td width="45%">
							<table width="100%" align="right" class="header" id="tran_details">
								<tbody>
									<tr>
										<td class="header_label" style="font-weight:bold; vertical-align:middle; width:120px; align:right" id="trandate_lbl" >${record.trandate@label}</td>
										<td class="header_info" style="font-weight:normal; align:left" id="trandate">${record.trandate}</td>
									</tr>
									<tr>
										<td class="header_label" style="font-weight:bold; vertical-align:middle; width:120px; align:right" id="tranid_lbl">${record.tranid@label}</td>
										<td class="header_info" style="font-weight:normal; align:left" id="tranid"><@limitStringDisplay value=record.tranid maxLength=20 lineFeed=""/></td>
									</tr>
									<tr>
										<td class="header_label" style="font-weight:bold; vertical-align:middle; width:120px; align:right" id="duedate_lbl">${record.duedate@label}</td>
										<td class="header_info" style="font-weight:normal; align:left" id="duedate">${record.duedate}</td>
									</tr>
								</tbody>
							</table>
							<table width="100%">
								<tbody>
									<tr>
										<td class="header_label" style="font-weight:bold; vertical-align:middle; width:120px; align:right" id="terms_lbl">${record.terms@label}</td>
										<td class="header_info" style="font-weight:normal; align:left" id="terms">${record.terms}</td>
									</tr>
                                    <tr>
										<td class="header_label" style="font-weight:bold; vertical-align:middle; width:120px; align:right" id="terms_lbl">Sales Rep.</td>
										<td class="header_info" style="font-weight:normal; align:left" id="terms">${record.salesrep}</td>
									</tr>
								</tbody>
							</table>
						</td>
					</tr>
				</table>
			</macro>
			<macro id="nlfooter">
				<table class="custfooter" style="width: 100%;">
					<tr>
						<td><#if preferences.print_barcodes><barcode codetype="code128" showtext="true" value="${record.tranid}"/></#if></td>
						<td align="right"><pagenumber/> of <totalpages/></td>
					</tr>
				</table>
			</macro>
		</macrolist>
	</head>
	<body header="nlheader" header-height="22%" footer="nlfooter" footer-height="17pt" size="A4">
		<#-- start prep data -->
		<#-- start get item data -->
		<#assign tranItems = {}>
        <#assign hasDiscount = false>
        <#assign hasMarkup = false>
        <#assign tranSubtotal = 0>
        <#assign itemTotalDiscount = 0>
        <#assign itemTotalMarkup = 0>
        <#assign itemParentNames = {}>
        <#assign parentsData = addtlData.parentFields />
        <#list record.item as item>
            <#if item.itemtype == 'Discount'>
                <#assign hasDiscount = true>
                <#assign itemTotalDiscount = itemTotalDiscount + item.amount>
            <#elseif item.itemtype == 'Markup'>
                <#assign hasMarkup = true>
                <#assign itemTotalMarkup = itemTotalMarkup + item.amount>
            <#elseif item.itemtype == 'Subtotal' || item.itemtype == 'Description'>
            <#else>
                <#if !itemParentNames["${item.olditemid}"]?has_content>
                    <#if item.custcol_gridoe_hidden_parentdispname?string != "">
                        <#assign itemParentNames = itemParentNames + {"${item.olditemid}" : item.custcol_gridoe_hidden_parentdispname}>
                    <#else>
                        <#assign itemParentNames = itemParentNames + {"${item.olditemid}" : item.custcol_gridoe_hidden_parentname}>
                    </#if>
                </#if>
                <#assign itemId = item.olditemid>
                <#assign itemQty = item.quantity>
				<#assign itemRate><@formatCurrencyDisplay value=item.rate/></#assign>
                <#assign itemAmt = item.amount?string['##0.00']?number>
                <#if !tranItems['${itemId}']??>
                    <#assign tranItems = tranItems + {'${itemId}': {'qty': [itemQty], 'rate': [itemRate], 'amt': itemAmt}}>
                <#else>
                    <#assign itemLine = tranItems['${itemId}']>
                    <#assign itemLineRates = itemLine.rate>
                    <#assign itemLineQtys = itemLine.qty>
                    <#assign itemLineAmt = itemLine.amt>
                    <#assign rateIndx = itemLineRates?seq_index_of(itemRate)>
                    <#if rateIndx gte 0>
                        <#assign qty = itemLineQtys[rateIndx] + itemQty>
                        <#assign itemLineQtys = replaceSeqVal(itemLineQtys, rateIndx, qty)>
                        <#assign amt = itemLineAmt?number + itemAmt?number>
                        <#assign tranItems = tranItems + {"${itemId}": {"qty": itemLineQtys, "rate": itemLineRates, "amt": amt}}>
                    <#else>
                        <#assign itemLineQtys = itemLineQtys + [itemQty]>
                        <#assign itemLineRates = itemLineRates + [itemRate]>
                        <#assign amt = itemLineAmt?number + itemAmt?number>
                        <#assign tranItems = tranItems + {"${itemId}": {"qty": itemLineQtys, "rate": itemLineRates, "amt": amt}}>
                    </#if>
                </#if>
            </#if>
		</#list>
		<#-- end get item data -->
		<#-- start get grid data -->
		<#assign gridlist = record.custbody_gridoe_trantemplatedata>
		<#assign gridItems = []>
		<#assign tranGrids = {}>
        <#assign gridParentNames = {}>
		<#assign maxcol = 7>
		<#list gridlist?eval as tmpltId, gridData>
			<#assign totalcol = gridData.grid.header?size>
			<#assign tablecount = ((totalcol)/maxcol)?ceiling>
			<#assign gridTables = []>
			<#list 1..tablecount as tablenum>
				<#assign start = (tablenum-1) * (maxcol)>
				<#assign end = min(start + maxcol-1, totalcol-1)>
				<#assign tableRows = []>
				<#list gridData.grid.data as row>
					<#assign totalQty = 0>
					<#assign totalAmt = 0>
					<#assign rates = []>
					<#assign rowItems = []>
					<#assign itemsQtyByRate = {}>
					<#assign i = start>
					<#assign colAttributes = []>
					<#list row.columnids[start..end] as columnid>
						<#assign itemid = row[columnid]["internalid"]?c>
						<#assign gridItems = gridItems + [itemid]>
						<#assign rowItems = rowItems + [itemid]>
						<#if tranItems['${itemid}']??>
							<#assign tranItem = tranItems['${itemid}']>
							<#assign itemRates = tranItem.rate>
							<#list itemRates as itemRate>
								<#if rates?seq_index_of(itemRate) lt 0>
									<#assign rates = rates + [itemRate]>
								</#if>
							</#list>
                            <#if !gridParentNames['${tmpltId}']?has_content && itemParentNames['${itemid}']?has_content>
                                <#assign gridParentNames = gridParentNames + {"${tmpltId}": itemParentNames["${itemid}"]}>
                            </#if>
						</#if>
						<#assign colAttributes = colAttributes + [gridData.grid.header[i].name]>
						<#assign i = i+1>
					</#list>
					<#assign nullitemidctr = 0>
					<#list rowItems as itemid>
						<#assign itemQtyByRate = []>
						<#if tranItems['${itemid}']??>
							<#assign tranItem = tranItems['${itemid}']>
							<#assign itemQtys = tranItem.qty>
							<#assign itemRates = tranItem.rate>
							<#assign totalAmt = totalAmt?number + tranItem.amt?string['##0.00']?number>
							<#list rates as rate>
								<#assign itemRateIndx = itemRates?seq_index_of(rate)>
								<#if itemRateIndx gte 0>
									<#assign itemQty = itemQtys[itemRateIndx]>
									<#assign totalQty = totalQty + itemQty>
									<#assign itemQtyByRate = itemQtyByRate + [itemQty]>
								<#else>
									<#assign itemQtyByRate = itemQtyByRate + [""]>
								</#if>
							</#list>
						<#else>
							<#list rates as rate>
								<#assign itemQtyByRate = itemQtyByRate + [""]>
							</#list>
						</#if>
						<#if itemid?number lte 0>
							<#assign nullitemidctr = nullitemidctr + 1>
							<#assign itemsQtyByRate = itemsQtyByRate + {"${itemid?string}+${nullitemidctr?string}": itemQtyByRate}>
						<#else>
							<#assign itemsQtyByRate = itemsQtyByRate + {"${itemid}": itemQtyByRate}>
						</#if>
					</#list>
					<#assign tableRows = tableRows + [{"qty": totalQty, "rates": rates, "amount": totalAmt, "colAttributes": colAttributes, "items": itemsQtyByRate}]>
				</#list>
				<#assign gridTables = gridTables + [tableRows]>
			</#list>
			<#assign tranGrids = tranGrids + {"${tmpltId}": gridTables}>
		</#list>
		<#-- end get grid data -->
		<#-- end prep data -->
        <table width="100%" border="0.15" border-color="#A0A0A0" table-layout="fixed" id="other_info">
			<tr>
				<#if !(record.billaddress == "")>
					<td>
						<table class="header" id="bill_to_info">
							<tr><td class="addressheader" colspan="3" font-weight="bold" id="bill_addr_lbl">${record.billaddress@label}:</td></tr>
							<tr><td class="address" colspan="3" rowspan="2" id="bill_addr">${record.billaddress}</td></tr>
						</table>
					</td>
				</#if>
				<#if !(record.shipaddress == "")>
					<td border-left = "0.15">
						<table class="header" id="ship_to_info">
							<tr><td class="addressheader" colspan="3" font-weight="bold" id="ship_addr_lbl">${record.shipaddress@label}:</td></tr>
							<tr><td class="address" colspan="3" rowspan="2" id="ship_addr">${record.shipaddress}</td></tr>
						</table>
					</td>
				</#if>
			</tr>
		</table>
        <br />
        <table width="100%" class="grid_table" table-layout="fixed" border="0.5">
            <thead class="grid_table_header" style="font-size: 8pt; text-align: left;">
                <tr id="grid_table_header">
                    <th rowspan="2" width="130px" id="grid_th_parent_name">${record.shipdate@label}</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">${record.shipmethod@label}</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">Vendor Acct. No.</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">Vat Number</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">Customer PO</th>
                    <th rowspan="2" width="130px" id="grid_th_parent_name">Deposit Req'd</th>
                </tr>
            </thead>
            <tbody class="grid_table_body">
                <tr style="font-size: 7pt;">
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px; white-space: nowrap;" class="grid_row_attribute">${record.shipdate}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px; white-space: nowrap;" class="grid_row_attribute">${record.shipmethod}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px;" class="grid_row_attribute">${record.custbody_bhpc_ven_no?keep_before(" VAT:")}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px; white-space: nowrap;" class="grid_row_attribute">${record.custbody_bhpc_ven_no?keep_after(" VAT:")}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px;" class="grid_row_attribute">${record.otherrefnum}</td>
                    <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px; white-space: nowrap;" class="grid_row_attribute"><@formatCurrencyDisplay value=record.custbody_blo_dep_req/></td>
                </tr>
            </tbody>
        </table>
        <br />
		<#-- GRIDS -->
		<#assign tranTotalQty = 0>
		<#assign tranGridList = gridlist?eval>
		<#assign gridCount = 0>
		<#list tranGrids as tmpltId, tables>
			<#-- GRID -->
			<#assign gridCount = gridCount + 1>
			<#assign gridData = tranGridList["${tmpltId}"]>
			<#assign gridTtlQty = 0>
			<#assign gridTtlAmt = 0>
			<#assign gridColAttrTotals = []>
			<#assign gridTableCount = tables?size>
			<#assign tablenum = 0>
            <#--  <table class="grid_table_title" id="grid_table_title_${gridCount}">
                <tr id="grid_template_name">
                    <td class="gridtemplate_info" id="name">${gridParentNames["${tmpltId}"]}${tmpltId}</td>
                </tr>
            </table>  -->
            <#list parentsData as parentData>
                <#if gridData["name"]?contains(parentData.itemid)>
                    <#assign parentDetail = parentData />
                </#if>
            </#list>
            <#assign fontsize = "8pt">
            <#if parentDetail.displayname?length gt 15 >
                <#assign fontsize = "6pt">
            </#if>
            <table width="100%" class="grid_table" table-layout="fixed" border="0.5">
            
                <thead class="grid_table_header" style="font-size: 8pt; text-align: left;">
                    <tr id="grid_table_header">
                        <th rowspan="2" width="120px" id="grid_th_parent_name">Season</th>
                        <th rowspan="2" width="120px" id="grid_th_parent_name">Department</th>
                        <th rowspan="2" width="125px" id="grid_th_parent_name">Theme</th>
                        <th rowspan="2" width="125px" id="grid_th_parent_name">Category</th>
                        <th rowspan="2" width="125px" id="grid_th_parent_name">MFG Style No.</th>
                        <th rowspan="2" width="160px" id="grid_th_parent_name">Display Name</th>
                        <th rowspan="2" width="135px" id="grid_th_parent_name">Article Code</th>
                    </tr>
                </thead>
                <tbody class="grid_table_body">
                    <tr style="font-size: 8pt;">
                        <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.season_year}</td>
                        <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.custitem_psgss_gender}</td>
                        <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.custitem_bo_theme}</td>
                        <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.custitem_psgss_category}</td>
                        <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.vendorname}</td>
                        <td style="font-size: ${fontsize}; align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px" class="grid_row_attribute">${parentDetail.displayname}</td>
                        <td style="align:center; background-color:#FFFFFF; padding-left:3px; padding-right:3px;" class="grid_row_attribute">${parentDetail.itemid}</td>
                    </tr>
                </tbody>
            </table>
            <#list tables as tableRows>
                <#assign tablenum = tablenum + 1>
                <table class="grid_table" border="0.5" id="grid_table_${gridCount}_${tablenum}">
                    <thead class="grid_table_header" id="grid_table_header">
                    <tr>
                        <th rowspan="2" width="90px" id="grid_th_parent_name">Item Attributes</th>
                        <#assign attrCount=0>
                        <#list tableRows[0].colAttributes as colAttr>
                            <#assign attrCount = attrCount + 1>
                            <th colspan="1" id="grid_th_col_attr${attrCount}">${colAttr}</th>
                        </#list>
                        <th colspan="1" width="80px" id="grid_th_qty">${record.item[0].quantity@label}</th>
                        <th colspan="1" width="90px" id="grid_th_rate">${record.item[0].rate@label}</th>
                        <th colspan="1" width="110px" id="grid_th_amt">${record.item[0].amount@label}</th>
                        </tr>
                    </thead>
                    <tbody class="grid_table_body">
                        <#assign rowIndx = 0>
                        <#list tableRows as tableRow>
                            <#assign rowIndx = rowIndx + 1>
                            <#assign gridTtlQty = gridTtlQty + tableRow.qty?number>
                            <tr id="grid_row_${rowIndx}">
                                <td style="align:left; background-color:#FFFFFF; padding-left:10px; padding-right:10px" id="grid_row_attr">${gridData.grid.data[rowIndx-1].rowattribute}</td>
                                <#assign attrCount = 0>
                                <#list tableRow.items as itemid, itemQtys>
                                    <#assign rowQty = ''>
                                    <#assign attrTtlQty = 0>
                                    <#list itemQtys as qty>
                                        <#assign qtyFormatted><@formatNumberDisplay value=qty/></#assign>
                                        <#assign rowQty = rowQty + qtyFormatted + "<br/>">
                                        <#if gridTableCount == 1><#assign attrTtlQty = attrTtlQty + qty></#if>
                                    </#list>
                                    <#if gridTableCount == 1>
                                        <#if !gridColAttrTotals[attrCount]??>
                                            <#assign gridColAttrTotals = gridColAttrTotals + [attrTtlQty]>
                                        <#else>
                                            <#assign newQty = attrTtlQty?number + gridColAttrTotals[attrCount]?number>
                                            <#assign gridColAttrTotals = replaceSeqVal(gridColAttrTotals, attrCount, newQty)>
                                        </#if>
                                    </#if>
                                    <#assign attrCount = attrCount + 1>
                                    <td style="align:center; background-color:#FFFFFF; width:120px" id="grid_row_qty_${attrCount}"><p align="center">${rowQty?remove_ending("<br/>")}</p></td>
                                </#list>
                                <#assign rowRate = ''>
                                <#list tableRow.rates as tableRowRate>
                                    <#assign rowRate = rowRate + tableRowRate + "<br/>">
                                </#list>
                                <td style="align:center; background-color:#FFFFFF" id="grid_row_qty"><@formatNumberDisplay value=tableRow.qty/></td>
                                <td style="align:center; background-color:#FFFFFF" id="grid_row_rate"><p align="center">${rowRate?remove_ending("<br/>")}</p></td>
                                <td style="align:right; background-color:#FFFFFF; padding-right:10px; padding-left:10px" id="grid_row_amt"><@formatCurrencyDisplay value=tableRow.amount/><#assign gridTtlAmt = gridTtlAmt + tableRow.amount?number></td>
                            </tr>
                        </#list>
                        <#if gridTableCount == tablenum>
                            <#assign tranTotalQty = tranTotalQty + gridTtlQty>
                            <tr id="grid_ttl">
                                <td style="align:center; background-color:#e3e3e3"></td>
                                <#if gridTableCount == 1>
                                    <#list gridColAttrTotals as colTtl>
                                        <td style="align:center; background-color:#e3e3e3"><@formatNumberDisplay value=colTtl/></td>
                                    </#list>
                                <#else>
                                    <#list 1..attrCount as col>
                                        <td style="align:center; background-color:#e3e3e3"></td>
                                    </#list>
                                </#if>
                                <td style="align:center; background-color:#e3e3e3" id="grid_ttl_qty"><@formatNumberDisplay value=gridTtlQty/></td>
                                <td style="align:center; background-color:#e3e3e3"></td>
                                <td style="align:right; background-color:#e3e3e3; padding-right:10px; padding-left:10px" id="grid_ttl_amt"><@formatCurrencyDisplay value=gridTtlAmt/><#assign tranSubtotal = tranSubtotal + gridTtlAmt></td>
                            </tr>
                        </#if>
                    </tbody>
                </table>
                <#if tablenum lt gridTableCount>
                    <br/>
                </#if>
            </#list>
            <br />
		</#list>
		<#-- END GRID -->
		<#-- START item list -->
		<#if record.item?has_content>
			<#assign hasHeader = "false">
			<#assign itemListTtlQty = 0>
			<#assign itemListTtlAmt = 0>
			<#assign itemCounter = 0>
			<#assign itemListItems = []>
            <#assign isItemGroupMember = "false">
            <#assign hasItemGroupMember = "false">
			<#list record.item as item>
                <#if item.itemtype == 'Group'>
                    <#assign isItemGroupMember = "true">
                <#elseif item.itemtype == 'EndGroup'>
                    <#assign isItemGroupMember = "false">
                </#if>
                <#if item.itemtype != 'Discount' && item.itemtype != 'Markup' && item.itemtype != 'Subtotal' && item.itemtype != 'Description' && item.itemtype != 'EndGroup'>
                    <#if gridItems?seq_index_of(item.olditemid) lt 0>
                        <#if item.custcol_gridoe_hidden_displayname?has_content>
                            <#assign name = item.custcol_gridoe_hidden_displayname>
                        <#else>
                            <#assign name = item.item>
                        </#if>
						<#if item.itemtype == 'Group'>
							<#assign itemDetails = {'item': item.item, 'type': item.itemtype, 'isitemgroupmember': 'false', 'quantity': '', 'name': name, 'rate':"", 'amount': ''}>
						<#else>
	                        <#assign itemDetails = {'item': item.item, 'type': item.itemtype, 'isitemgroupmember': isItemGroupMember, 'quantity': item.quantity, 'name': name, 'rate':item.rate, 'amount': item.amount}>
							<#assign itemListTtlQty = itemListTtlQty?number + item.quantity>
							<#assign itemListTtlAmt = itemListTtlAmt?number + item.amount>
                            <#if isItemGroupMember == "true">
                                <#assign hasItemGroupMember = "true">
                            </#if>
						</#if>
                        <#assign itemListItems = itemListItems + [itemDetails]>
                    </#if>
                </#if>
                <#if item.itemtype == 'EndGroup'>
                    <#if hasItemGroupMember == "false">
                        <#if itemListItems?size gt 1>
                            <#assign itemListItems = itemListItems[0..itemListItems?size-2]>
                        <#else>
                            <#assign itemListItems = []>
                        </#if>
                    </#if>
                    <#assign hasItemGroupMember = "false">
                </#if>
			</#list>
            <br/>
			<table id="item_list" style="width: 100%; margin-top: 10px;" border = "0">
				<#if itemListItems?has_content>
					<thead id="item_list_header" class="grid_table_header">
						<tr font-weight= "bold">
							<th width="80px" colspan="3" style="border-right: 0.5; border-color: #A0A0A0; padding-left:10px; padding-right:10px" id="item_list_th_qty">${record.item[0].quantity@label}</th>
							<th colspan="12" style="border-right: 0.5; border-color: #A0A0A0; padding-left:10px; padding-right:10px" id="item_list_th_item">${record.item[0].item@label}</th>
							<th colspan="3" style="border-right: 0.5; border-color: #A0A0A0; padding-left:10px; padding-right:10px" id="item_list_th_name">Name</th>
							<th width="90px" colspan="4" style="border-right: 0.5; border-color: #A0A0A0; padding-left:10px; padding-right:10px" id="item_list_th_rate">${record.item[0].rate@label}</th>
							<th width="110px" colspan="4" id="item_list_th_amt">${record.item[0].amount@label}</th>
						</tr>
					</thead>
					<tbody>
						<#list itemListItems as item>
							<#assign itemCounter = itemCounter + 1>
							<tr id="item_list_row_${itemCounter}">
								<td style="border-right: 0.5; border-color: #A0A0A0; align:right; padding-left:10px; padding-right:10px" colspan="3" line-height="150%" id="item_list_qty"><@formatNumberDisplay value=item.quantity/></td>
                                <#assign indent = "10px"/>
                                <#if item.isitemgroupmember == "true">
                                    <#assign indent = "20px"/>
                                </#if>
                                <td style="border-right: 0.5; border-color: #A0A0A0; align:left; padding-right:10px" padding-left="${indent}" colspan="12" id="item_list_item">${item.item}</td>
                                <td style="border-right: 0.5; border-color: #A0A0A0; align:left; padding-right:10px" padding-left="${indent}" colspan="3" id="item_list_name">${item.name}</td>
		                        <td style="border-right: 0.5; border-color: #A0A0A0; align:right; padding-left:10px; padding-right:10px" colspan="4" id="item_list_rate"><@formatCurrencyDisplay value=item.rate/></td>
								<td style="align:right; padding-left:10px; padding-right:10px" colspan="4" id="item_list_amt"><@formatCurrencyDisplay value=item.amount/></td>
							</tr>
						</#list>
						<tr background-color = "#e3e3e3" id="item_list_ttl">
							<#assign tranTotalQty = tranTotalQty + itemListTtlQty?number>
							<td style="border-right: 0.5; border-color: #A0A0A0; align:right; padding-left:10px; padding-right:10px" colspan="3" line-height="150%" id="item_list_ttl_qty"><@formatNumberDisplay value=itemListTtlQty/></td>
							<td colspan="12" style="border-right: 0.5; border-color: #A0A0A0;"></td>
                            <td colspan="3" style="border-right: 0.5; border-color: #A0A0A0;"></td>
                            <td colspan="4" style="border-right: 0.5; border-color: #A0A0A0;"></td>
							<td style="align:right; padding-left:10px; padding-right:10px" colspan="4" id="item_list_ttl_amt"><@formatCurrencyDisplay value=itemListTtlAmt/><#assign tranSubtotal = tranSubtotal + itemListTtlAmt></td>
						</tr>
					</tbody>
				</#if>
			</table>
		</#if>
		<#-- END item list -->
		<#-- START Total Summary -->
        <table width="100%">
            <tr>
                <td width="50%">
                    <table width="100%" class="grid_table" table-layout="fixed" border="0.5">
                        <thead class="grid_table_header" style="font-size: 8pt; text-align: left;">
                            <tr id="grid_table_header">
                                <th id="grid_th_parent_name">Remittance Bank Information</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    ${record.custbody7}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
                <td width="50%">
                    <table align="right" border="1" page-break-inside="avoid" id="totals">
                        <tr font-size = "13px" >
                            <td width="65%" style="align:right; padding-right:10px;" colspan="2" padding-left="15px" id="subttl_lbl">Total Quantity</td>
                            <td width="35%" style="align:right; padding-right:10px;" id="subttl"><@formatNumberDisplay value=tranTotalQty/></td>
                        </tr>
                        <#if record.subtotal?has_content>
                            <tr font-size = "13px" >
                                <td width="65%" style="align:right; padding-right:10px;" colspan="2" padding-left="15px" id="subttl_lbl">${record.subtotal@label}</td>
                                <td width="35%" style="align:right; padding-right:10px;" id="subttl"><@formatCurrencyDisplay value=tranSubtotal/></td>
                            </tr>
                        </#if>
                        <#assign discountTotal = 0>
                        <#if hasDiscount>
                            <#assign discountTotal = discountTotal?number + itemTotalDiscount?number>
                        </#if>
                        <#if record.discounttotal?has_content>
                            <#assign discountTotal = discountTotal?number + record.discounttotal>
                        </#if>
                        <tr font-size = "13px" >
                            <td style="align:right; padding-right:10px;" colspan="2" padding-left="15px" id="disc_ttl_lbl">Discounts</td>
                            <td style="align:right; padding-right:10px;" id="disc_ttl"><@formatCurrencyDisplay value=discountTotal/></td>
                        </tr>
                        <tr font-size = "13px" >
                            <td style="align:right; padding-right:10px;" colspan="2" padding-left="15px" id="markup_ttl_lbl">Markups</td>
                            <td style="align:right; padding-right:10px;" id="markup_ttl"><@formatCurrencyDisplay value=itemTotalMarkup/></td>
                        </tr>
                        <#if record.taxtotal?has_content>
                            <tr font-size = "13px" >
                                <td style="align:right; padding-right:10px;" colspan="2" padding-left="15px" id="tax_ttl_lbl">${record.taxtotal@label}</td>
                                <td style="align:right; padding-right:10px;" id="tax_ttl"><@formatCurrencyDisplay value=record.taxtotal/></td>
                            </tr>
                        </#if>
                        <#if record.tax2total?has_content>
                            <tr font-size = "13px" >
                                <td style="align:right; padding-right:10px;" colspan="2" padding-left="15px" id="tax2_ttl_lbl">${record.tax2total@label}</td>
                                <td style="align:right; padding-right:10px;" id="tax2_ttl"><@formatCurrencyDisplay value=record.tax2total/></td>
                            </tr>
                        </#if>
                        <#assign shippingTotal = 0>
                        <#assign hasShipping = false>
                        <#if record.altshippingcost?has_content>
                            <#assign shippingTotal = shippingTotal + record.altshippingcost>
                            <#assign hasShipping = true>
                        </#if>
                        <#if record.althandlingcost?has_content>
                            <#assign shippingTotal = shippingTotal + record.althandlingcost>
                            <#assign hasShipping = true>
                        </#if>
                        <#if hasShipping>
                            <tr font-size = "13px" >
                                <td style="align:right; padding-right:10px;" colspan="2" padding-left="15px" id="shipping_ttl_lbl">Shipping Total</td>
                                <td style="align:right; padding-right:10px;" id="shipping_ttl"><@formatCurrencyDisplay value=shippingTotal/></td>
                            </tr>
                        </#if>
                        <#if record.giftcertapplied?has_content>
                            <tr font-size = "13px">
                                <td style="align:right; padding-right:10px;" colspan="2" padding-left="15px" id="giftcert_ttl_lbl">${record.giftcertapplied@label}</td>
                                <td style="align:right; padding-right:10px;" id="giftcert_ttl"><@formatCurrencyDisplay value=record.giftcertapplied/></td>
                            </tr>
                        </#if>
                        <#if addtlData.billsCtr != 0>
                            <tr font-size = "13px">
                                <td style="align:right; padding-right:10px;" colspan="2" padding-left="15px" id="giftcert_ttl_lbl">${record.total@label}zzz</td>
                                <td style="align:right; padding-right:10px;" id="giftcert_ttl"><@formatCurrencyDisplay value=record.total/></td>
                            </tr>
                            <tr font-size = "13px">
                                <td style="align:right; padding-right:10px;" colspan="2" padding-left="15px" id="giftcert_ttl_lbl">Customer Payment</td>
                                <td style="align:right; padding-right:10px;" id="giftcert_ttl"><@formatCurrencyDisplay value=record.amountpaid/></td>
                            </tr>
                            <tr background-color = "#e3e3e3" font-weight= "bold" font-size="15px" >
                                <td width="45%" colspan="2" align="right" padding-right="10px" id="subttl_lbl" style="border-top:1;">Amount Due</td>
                                <td width="65%" style="border-top:1; align:center; font-size:16px; padding-right:10px" id="ttl_qty"><#--  <@formatNumberDisplay value=record.amountremaining/>  -->${record.amountremaining}</td>
                            </tr>
                        <#else>
                            <tr background-color = "#e3e3e3" font-weight= "bold" font-size="15px" >
                                <td width="65%" colspan="2" align="right" padding-right="10px" id="subttl_lbl" style="border-top:1;">${record.total@label}</td>
                                <td style="align:center; font-size:18px; padding-right:10px; border-top:1;" id="ttl_qty"><@formatCurrencyDisplay value=record.total/></td>
                            </tr>
                        </#if>
                    </table>
                </td>
            </tr>
        </table>
		<#-- END Total Summary -->
	</body>
</pdf>