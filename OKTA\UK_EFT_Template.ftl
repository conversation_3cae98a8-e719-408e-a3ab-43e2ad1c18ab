<#-- format specific processing -->
<#function buildEntityBillingAddress entity>
<#assign address="">
<#if entity.billaddress1?has_content>
<#assign address=entity.billaddress1>
<#elseif entity.shipaddress1?has_content>
<#assign address=entity.shipaddress1>
<#elseif entity.address1?has_content>
<#assign address=entity.address1>
</#if>
<#return address>
</#function>

<#function buildUnstructuredInfo payment>
<#assign name=cbank.custpage_eft_custrecord_2663_statement_name>
<#assign paymentTrans=transHash[payment.internalid]>
<#assign tranId="">
<#if paymentTrans?size==1>
<#assign transaction=paymentTrans[0]>
<#assign tranId=transaction.tranid>
</#if>
<#if tranId?has_content>
<#assign info="Payment " + tranId + " from " + name>
<#else>
<#assign info="Payment from " + name>
</#if>
<#return info>
</#function>

<#-- cached values -->
<#assign totalAmount=computeTotalAmount(payments)>

<#function getSvcLvlGt payments ebanks>
<#list payments as payment>
    <#if ebank.custrecord_wire_transfer>
        <#return 'URGP'>
    </#if>
    
    <#if getCurrencySymbol(payment.currency) != 'GBP'>
        <#return 'URGP'>
    </#if>
    
    <#assign ebank=ebanks[payment_index]>
    <#if ebank.custrecord_2663_entity_bic?has_content>
        <#return 'URGP'>
    </#if>
</#list>
<#assign totalAmount=computeTotalAmount(payments)>
<#if totalAmount gt 250000>
    <#return 'URGP'>
</#if>
<#return 'URNS'>
</#function>

<#function getSvcLvl payment ebank>
<#if ebank.custrecord_wire_transfer>
    <#return 'URGP'>
</#if>
<#if getCurrencySymbol(payment.currency) != 'GBP'>
    <#return 'URGP'>
</#if>
<#if ebank.custrecord_2663_entity_bic?has_content>
    <#return 'URGP'>
</#if>
<#assign totalAmount=computeTotalAmount(payments)>
<#if totalAmount gt 250000>
    <#return 'URGP'>
</#if>
<#return 'URNS'>
</#function>
<#-- NURG = BACS / URNS = FASTER PAYMENTS / URGP = CHAPS -->

<#-- template building -->
#OUTPUT START#
<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03">
    <CstmrCdtTrfInitn>
        <GrpHdr>
            <MsgId>${pfa.id}</MsgId>
            <CreDtTm>${pfa.custrecord_2663_file_creation_timestamp?date?string("yyyy-MM-dd")}T${pfa.custrecord_2663_file_creation_timestamp?time?string("HH:mm:ss")}</CreDtTm>
            <NbOfTxs>${payments?size?c}</NbOfTxs>
            <CtrlSum>${setMaxLength(formatAmount(totalAmount,"decLessThan1"),18)}</CtrlSum>
            <InitgPty>
                <Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}
                </Nm>
                <Id>
                    <OrgId>
                        <Othr>
                            <Id>OKTABOA</Id>
                        </Othr>
                    </OrgId>
                </Id>
            </InitgPty>
        </GrpHdr>
        <#assign countriesWithState = ['US', 'AU', 'CA']>
        <PmtInf>
            <PmtInfId>${setMaxLength(convertToLatinCharSet(pfa.custrecord_2663_ref_note),24)}</PmtInfId>
            <PmtMtd>TRF</PmtMtd>
            <BtchBookg>true</BtchBookg>
            <NbOfTxs>${setMaxLength(payments?size?c,15)}</NbOfTxs>
            <CtrlSum>${setMaxLength(formatAmount(totalAmount,"decLessThan1"),18)}</CtrlSum>
            <#if payments?size == 1>
            <PmtTpInf>
                <SvcLvl>
                    <Cd>${getSvcLvlGt(payments, ebanks)}</Cd>
                </SvcLvl>
            </PmtTpInf>
            </#if>
            <ReqdExctnDt>${pfa.custrecord_2663_process_date?string("yyyy-MM-dd")}</ReqdExctnDt>
            <Dbtr>
                <Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm>
                <PstlAdr>
                 	<PstCd>${cbank.custrecord_dbtr_zip}</PstCd>
                    <TwnNm>${cbank.custrecord_dbtr_city}</TwnNm>
                    <Ctry>GB</Ctry>
                    <AdrLine>${cbank.custrecord_dbtr_address_1}</AdrLine>
                </PstlAdr>
            </Dbtr>
            <DbtrAcct>
                <Id>
                    <Othr>
                        <Id>${cbank.custpage_eft_custrecord_2663_acct_num}</Id>
                    </Othr>
                </Id>
                <Ccy>GBP</Ccy>
            </DbtrAcct>
            <DbtrAgt>
                <FinInstnId>
                    <#if cbank.custpage_eft_custrecord_2663_bic?has_content>
                    <BIC>${cbank.custpage_eft_custrecord_2663_bic}</BIC>
                    </#if>
                    <PstlAdr>
                        <Ctry>GB</Ctry>
                    </PstlAdr>
                </FinInstnId>
                <BrnchId>
                    <Id>6008</Id>
                </BrnchId>
            </DbtrAgt>
            <#assign pmtSize = payments?size>
            <#list payments as payment>
            <#assign ebank=ebanks[payment_index]>
            <#assign entity=entities[payment_index]>
            <CdtTrfTxInf>
                <PmtId>
                    <EndToEndId>${setMaxLength(payment.memomain,12)}</EndToEndId>
                </PmtId>
                <#if pmtSize gt 1>
                <PmtTpInf>
                    <SvcLvl>
                        <Cd>${getSvcLvl(payment, ebank)}</Cd>
                    </SvcLvl>
                </PmtTpInf>
                </#if>
                <Amt>
                    <InstdAmt Ccy="${getCurrencySymbol(payment.currency)}">${setMaxLength(formatAmount(getAmount(payment),"decLessThan1"),16)}</InstdAmt>
                </Amt>
                <CdtrAgt>
                    <FinInstnId>
                        <#if ebank.custrecord_2663_entity_bic?has_content>
                        <BIC>${ebank.custrecord_2663_entity_bic}</BIC>
                        </#if>
                        <#if ebank.custrecord_2663_entity_bank_code?has_content>
                        <ClrSysMmbId>
                            <MmbId>${ebank.custrecord_2663_entity_bank_code}</MmbId>
                        </ClrSysMmbId>
                        </#if>
                        <Nm>${setMaxLength(ebank.custrecord_2663_entity_bank_name,140)}</Nm>
                        <PstlAdr>
                            <Ctry>${ebank.custrecord_2663_entity_country_code}</Ctry>
                        </PstlAdr>
                    </FinInstnId>
                </CdtrAgt>
                <Cdtr>
                    <Nm>${buildEntityName(entity,true)}</Nm>
                    <PstlAdr>
                        <#if entity.zipcode?has_content>
                        <PstCd>${entity.zipcode}</PstCd>
                        </#if>
                        <#if entity.city?has_content>
                        <TwnNm>${entity.city}</TwnNm>
                        </#if>
                        <#if entity.state?has_content>
                            <#if countriesWithState?seq_index_of(entity.state) != -1>
                        <CtrySubDvsn>${entity.state}</CtrySubDvsn>
                            </#if>
                        </#if>
                        <#if entity.billcountrycode?has_content>
                        <Ctry>${entity.billcountrycode}</Ctry>
                        </#if>
                        <#if entity.address1?has_content>
                        <AdrLine>${entity.address1}</AdrLine>
                        </#if>
                    </PstlAdr>
                </Cdtr>
                <CdtrAcct>
                    <#if ebank.custrecord_2663_entity_acct_no?has_content || ebank.custrecord_2663_entity_iban?has_content>
                    <Id>
                        <#if ebank.custrecord_2663_entity_iban?has_content>
                        <IBAN>${ebank.custrecord_2663_entity_iban}</IBAN>
                        </#if>
                        <#if ebank.custrecord_2663_entity_acct_no?has_content>
                        <Othr>
                            <Id>${ebank.custrecord_2663_entity_acct_no}</Id>
                        </Othr>
                        </#if>
                    </Id>
                    </#if>
                    <Ccy>${getCurrencySymbol(payment.currency)}</Ccy>
                </CdtrAcct>
                <RmtInf>
                    <Ustrd>${buildUnstructuredInfo(payment)}</Ustrd>
                </RmtInf>
            </CdtTrfTxInf>
            </#list>
        </PmtInf>
    </CstmrCdtTrfInitn>
</Document>
<#rt>
#OUTPUT END#