/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https'], function(https) {

    function onRequest(context) {
        log.debug({
            title: 'Suitelet Start',
            details: 'Suitelet Executed'
        });

        if(context.request.method == https.Method.GET){
        // logic for GET
            var getForm = serverWidget.createForm({
                title: 'Email for SS2.0'
            });

            getForm.clientScriptModulePath = './clientScriptSS2.js';

            getForm.addButton({
                id: 'custpage_mainbtn',
                label: 'How are you feeling today?',
                functionName: 'feelings()'
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
