function scheduled() {

    var MAX_LOAD_RECORDS = 1000;

    var searchResults,
        search,
        resultset,
        start = 0,
        end = MAX_LOAD_RECORDS,
        columns = [],
        filters = [];
    i = 0;

    var categories = [];
    
    filters.push(new nlobjSearchFilter('commercecategoryid', null, 'noneof', '@NONE@'));

    columns.push(new nlobjSearchColumn('internalid'));
    columns.push(new nlobjSearchColumn('displayname'));
    columns.push(new nlobjSearchColumn('commercecategoryid'));

    // Create a search and run
    search = nlapiCreateSearch('item', filters, columns);

    resultset = search.runSearch();

    // Gets the results from 0 to 1000 records
    searchResults = resultset.getResults(start, end);

    var page = 1;

    // 1000 records at time
    while (searchResults != null && searchResults.length > 0) {

        var csvdata = ''; //'Internal Id;Name;Commerce Category Id;Hierarchy';

        nlapiLogExecution('DEBUG', 'Debug', 'Reading page ' + page);

        for (var i = 0; i < searchResults.length; i++) {

            var itemId = searchResults[i].getValue('internalid');
            
            var commerceCategory = searchResults[i].getValue('commercecategoryid');

            if (!categories[commerceCategory]) {
                categories[commerceCategory] = getHierarchy(commerceCategory);
            }
            csvdata += itemId + ',"' + searchResults[i].getValue('displayname') + '",' + commerceCategory + ',"' + categories[commerceCategory] + '"\n';

            if (nlapiGetContext().getRemainingUsage() < 1000) {

                nlapiLogExecution('DEBUG', 'Debug', 'Yield script');

                nlapiYieldScript();
            }
        }

        var file = nlapiLoadFile(617766);
        var r = file.getValue();

        // Add new items to the CSV                
        var file = nlapiCreateFile('prodcategory.csv', 'CSV', r + csvdata);
        file.setFolder(440181);
        nlapiSubmitFile(file);
        
        page++;

        start = start + MAX_LOAD_RECORDS;
        end = end + MAX_LOAD_RECORDS;

        // Executes the search again to get the next range of records
        searchResults = resultset.getResults(start, end);
    }
}

function getHierarchy(commerceCategory) {

    var h = '';
    var i = 0;

    while (commerceCategory) {
        var rec = nlapiLoadRecord('commercecategory', commerceCategory);
        h = rec.getFieldValue('name') + ' : ' + h;

        commerceCategory = rec.getFieldValue('primaryparent');
        i++;

        if (i > 50) {
            return '';
        }
    }

    if (h) {
        h = h.substr(0, h.length - 3);
    }

    return h;
}