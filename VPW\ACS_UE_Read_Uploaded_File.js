/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/file'], function(record, file) {

    function afterSubmit(context) {
        if(context.type == context.UserEventType.EDIT || context.type == context.UserEventType.CREATE) {
            var readOnlyRec = context.newRecord;
            var fileId = readOnlyRec.getValue({ fieldId: 'custbody_upload_serial_info' });

            if(fileId != '' || fileId !== null) {

                var fileObj = file.load({
                    id: fileId
                });

                var iterator = fileObj.lines.iterator();

                //Skip the first line (CSV header)
                iterator.each(function () { return false; });
                iterator.each(function (line) {
                    // This function updates the total by
                    // adding the amount on each line to it
                    var lineValues = line.value.split(',');

                    var serialInfoRecObj = record.create({
                        type: 'customrecord_serial_info',
                        isDynamic: true
                    });

                    serialInfoRecObj.setValue({ fieldId: 'name', value: lineValues[0] });
                    serialInfoRecObj.setValue({ fieldId: 'custrecord_assembly_build', value: readOnlyRec.id });

                    try {
                        serialInfoRecObj.save();
                    } catch (e) {
                        log.debug("Error", e);
                    }

                    return true;                        
                });

                
            }
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
