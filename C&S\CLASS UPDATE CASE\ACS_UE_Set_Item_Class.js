/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search'], function(record, search) {

    function getItemType(itemId) {
        var itemSearchObj = search.create({
            type: "item",
            filters:
            [
               ["internalid","anyof",itemId]
            ],
            columns:
            [
               search.createColumn({ name: "itemid", label: "Name" }),
               search.createColumn({ name: "type", label: "Type" }),
            ]
         });

         var type = '';
         itemSearchObj.run().each(function(result){
            type = result.getText({ name: 'type' });
            return true;
         });

         switch (type) {
            case 'Non-inventory Item':
                return record.Type.NON_INVENTORY_ITEM;
                break;

            case 'Description':
                return record.Type.DESCRIPTION_ITEM
                break;

            case 'Other Charge':
                return record.Type.OTHER_CHARGE_ITEM
                break;

            case 'Discount':
                return record.Type.DISCOUNT_ITEM
                break;

            case 'Item Group':
                return record.Type.ITEM_GROUP
                break;

            case 'Kit/Package':
                return record.Type.KIT_ITEM
                break;

            case 'Service':
                return record.Type.SERVICE_ITEM
                break;

            case 'Gift Certificate':
                return record.Type.GIFT_CERTIFICATE_ITEM
                break;

             default:
                return record.Type.INVENTORY_ITEM
                break;
         }

    }

    function afterSubmit(context) {
        var recObj = context.newRecord;
        var sublistId = ((recObj.type == 'inventoryadjustment') ? 'inventory' : 'item');


        for(var i = 0; i < recObj.getLineCount({ sublistId: sublistId }); i++){
            var itemClass = recObj.getSublistValue({
                sublistId: sublistId,
                fieldId: 'class',
                line: i
            });
            

            // check if item has class
            if(itemClass){
                    
                var itemId = recObj.getSublistValue({
                    sublistId: sublistId,
                    fieldId: 'item',
                    line: i
                });
                
                try {
                    var itemObj = record.load({
                        type: getItemType(itemId),
                        id: itemId,
                        isDynamic: true
                    });
                    
                    itemObj.setValue({
                        fieldId: 'class',
                        value: itemClass
                    });

                    var savedId = itemObj.save();

                    log.debug('Successfully saved', 'id: ' + savedId);
                } catch (e) {
                    log.debug('Failed saving', e);
                }
            } 
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
