<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            .check table,
            .voucher1 table,
            .voucher2 table {
                position: relative;
                overflow: hidden;
                font-size: 8pt;
                padding: 0;
            }

            td p {
                align: left
            }

            p.uppercase {
                text-transform: uppercase;
            }

            p.lowercase {
                text-transform: lowercase;
            }

            p.capitalize {
                text-transform: capitalize;
            }
        </style>
    </head>

    <body padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
        <#list records as check>

            <div
                style="position: relative;font-family: Helvetica,sans-serif;top= -19pt;height: 250pt;width: 612pt;page-break-inside: avoid;font-size: 10pt;">

                <table style="position: absolute;overflow: hidden;left: 250pt;top: 80pt;height: 7pt;width: 85pt;">
                    <tr>
                        <td align="center"></td>
                    </tr>
                </table>
                <table style="position: absolute;overflow: hidden;left: 475pt;top: 60pt;height: 18pt;width: 111pt;">
                    <tr>
                        <td>***$<#if (check.usertotal?length> 0)>${check.usertotal?string?replace("$","")} <#else>
                                    ${check.total?string?replace("$","")}</#if>
                        </td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 58pt;top: 60pt;height: 18pt;width: 550pt;">
                    <tr>
                        <td>
                            <p>${check.entity?substring(check.entity?index_of(" ")+1)}</p>
                        </td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 3pt;top: 90pt;height: 18pt;width: 550pt;">
                    <tr>
                        <td>
                            <p class="capitalize">${check.totalwords} dollars***</p>
                        </td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 465pt;top: 30pt;height: 18pt;width: 108pt;">
                    <tr>
                        <td>${check.trandate}</td>
                    </tr>
                </table>
                
                <#assign sigUrl = "https://6758949.app.netsuite.com/core/media/media.nl?id=13400&c=6758949&h=xGer4RQfWA-jE_a6WVoHKpToYkBc2Vmc6tKcG9VNekpdo3Y2"?html />
                <table style="position: absolute;overflow: hidden;left: 400pt;top: 105pt;height: 18pt;width: 111pt;">
                    <tr>
                        <td><img src="${sigUrl}" width="119px" height="93px"/></td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 37pt;top: 120pt;height: 80pt;width: 537pt; font-size: 9pt;">
                    <tr>
                        <td>${check.address}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 32pt;top: 175pt;height: 18pt;width: 572pt;">
                    <tr>
                        <td>${check.memo}</td>
                    </tr>
                </table>
            </div>
            <div
                style="position: relative;font-family: Helvetica,sans-serif;height: 260pt;width: 612pt;page-break-before: avoid;font-size: 10pt;">
                <table
                    style="position: absolute;overflow: hidden;left: 320pt;top: -2pt;height: 17pt;width: 150pt;font-size: 10pt;">
                    <tr>
                        <td align="center"> </td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 175pt;top: -2pt;height: 13pt;width: 157pt;">
                    <tr>
                        <td>Payee : ${check.entity?substring(check.entity?index_of(" ")+1)}
                            ${check.customer?substring(check.customer?index_of(" ")+1)} </td>
                    </tr>
                </table>
                <#if check.item?has_content || check.expense?has_content>

                    <table style="position: absolute;overflow: hidden;left: 36pt;top: 80pt;width: 436pt;">
                        <#list check.expense as expense>
                            <tr>
                                <td>${expense.account}</td>
                                <td>${expense.date}</td>
                                <td>${expense.description}</td>
                                <td align="right">${expense.amount}</td>
                            </tr>
                        </#list>
                        <#list check.item as item>
                            <tr>
                                <td>&nbsp;</td>
                                <td>${item.date}</td>
                                <td>${item.item}, ${item.description}</td>
                                <td align="right">${item.amount}</td>
                            </tr>
                        </#list>
                    </table>
                </#if>
                <#if check.apply?has_content>
                    <table
                        style="position: absolute;overflow: hidden;left: 30pt;top: 30pt;width: 500pt;border-bottom .5pt">
                        <tr>
                            <th style="border-bottom: 1pt">Date</th>
                            <th style="border-bottom: 1pt">Reference</th>
                            <th style="border-bottom: 1pt">Orig Amount</th>
                            <th style="border-bottom: 1pt">Paid Amount</th>
                            <th style="border-bottom: 1pt">Discounts Taken</th>
                            <th style="border-bottom: 1pt">Credits Taken</th>
                            <th style="border-bottom: 1pt; align: left">Net Amount</th>
                        </tr>
                        <#list check.apply as apply>
                            <tr>
                                <td>${apply.applydate}</td>
                                <td>${apply.refnum}</td>
                                <td>${apply.total}</td>
                                <td>${apply.amount}</td>
                                <td>${apply.disc}</td>
                                <td></td>
                                <td align="left">${apply.amount}</td>
                            </tr>
                        </#list>
                    </table>
                </#if>
                <table style="position: absolute;overflow: hidden;left: 370pt;top: 190pt;height: 13pt;width: 150pt;">
                    <tr>
                        <td>Net Check Amt&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                                style="border-top: 1pt;">${check.total}</span></td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 148pt;top: 204pt;height: 13pt;width: 325pt;">
                    <tr>
                        <td></td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 9pt;top: 204pt;height: 13pt;width: 134pt;">
                    <tr>
                        <td></td>
                    </tr>
                </table>
            </div>

            <div
                style="position: relative;font-family: Helvetica,sans-serif;height: 250pt;width: 612pt;page-break-before: avoid;font-size: 10pt;">
                <table
                    style="position: absolute;overflow: hidden;left: 320pt;top: -2pt;height: 7pt;width: 150pt;font-size: 10pt;">
                    <tr>
                        <td align="center">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 175pt;top: -2pt;height: 13pt;width: 157pt;">
                    <tr>
                        <td>Payee : ${check.entity?substring(check.entity?index_of(" ")+1)}
                            ${check.customer?substring(check.customer?index_of(" ")+1)} </td>
                    </tr>
                </table>
                <#if check.item?has_content || check.expense?has_content>

                    <table style="position: absolute;overflow: hidden;left: 36pt;top: 80pt;width: 436pt;">
                        <#list check.expense as expense>
                            <tr>
                                <td>${expense.account}</td>
                                <td>${expense.date}</td>
                                <td>${expense.description}</td>
                                <td align="right">${expense.amount}</td>
                            </tr>
                        </#list>
                        <#list check.item as item>
                            <tr>
                                <td>&nbsp;</td>
                                <td>${item.date}</td>
                                <td>${item.item}, ${item.description}</td>
                                <td align="right">${item.amount}</td>
                            </tr>
                        </#list>
                    </table>
                </#if>
                <#if check.apply?has_content>
                    <table
                        style="position: absolute;overflow: hidden;left: 30pt;top: 30pt;width: 500pt;border-bottom .5pt">
                        <tr>
                            <th style="border-bottom: 1pt">Date</th>
                            <th style="border-bottom: 1pt">Reference</th>
                            <th style="border-bottom: 1pt">Orig Amount</th>
                            <th style="border-bottom: 1pt">Paid Amount</th>
                            <th style="border-bottom: 1pt">Discounts Taken</th>
                            <th style="border-bottom: 1pt">Credits Taken</th>
                            <th style="border-bottom: 1pt; align: left">Net Amount</th>
                        </tr>
                        <#list check.apply as apply>
                            <tr>
                                <td>${apply.applydate}</td>
                                <td>${apply.refnum}</td>
                                <td>${apply.total}</td>
                                <td>${apply.amount}</td>
                                <td>${apply.disc}</td>
                                <td></td>
                                <td align="left">${apply.amount}</td>
                            </tr>
                        </#list>
                    </table>
                </#if>
                <table style="position: absolute;overflow: hidden;left: 370pt;top: 190pt;height: 13pt;width: 150pt;">
                    <tr>
                        <td>Net Check Amt&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
                                style="border-top: 1pt;">${check.total}</span></td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 148pt;top: 204pt;height: 13pt;width: 325pt;">
                    <tr>
                        <td></td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 9pt;top: 204pt;height: 13pt;width: 134pt;">
                    <tr>
                        <td></td>
                    </tr>
                </table>
            </div>
        </#list>
    </body>
</pdf>