/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search', 'N/render', 'N/file'], function(record, search, render, file) {

    /**
     * 
     * @param {string} recId - id of the record created
     * @param {string} recType - type of the record
     * 
     * runs a search that checks the attachments of a record
     * 
     * @return {object} returnObj - returns an object that contain the boolean (recBool) if a file has been already generated and returns true if no file auto created yet
     *                             - if recBool is false, also return the fileId in case the record has been changed
     * 
     */
    function checkIfHasAnyAttachment(recId, recType) {

        // var recBool = true;
        var returnObj = {
            recBool: true,
            fileId: null
        }
        
        var fileSearch = search.create({
            type: "transaction",
            filters:
            [
                ["mainline","is","T"], 
                "AND", 
                ["internalid","anyof",recId]
            ],
            columns:
            [
                search.createColumn({
                    name: "internalid",
                    join: "file",
                    label: "Internal Id"
                }),
                search.createColumn({
                    name: "name",
                    join: "file",
                    label: "Name"
                })
            ]
        });
        var pagedData = fileSearch.runPaged({ pageSize: 5 });
        for( var i=0; i < pagedData.pageRanges.length; i++ ) {
    
            // fetch the current page data
            var currentPage = pagedData.fetch(i);
    
            // and forEach() thru all results
            currentPage.data.forEach( function(result) {
                var fileName = result.getValue({ name: 'name', join: 'file' });
                var fileId = result.getValue({ name: 'internalid', join: 'file' });

                if(fileName){
                    if(fileName.replace(".pdf", "") == recType + "_" + recId){
                        returnObj.recBool = false;
                        returnObj.fileId = fileId;
                    }
                }

            });
    
        }

        return returnObj;
    }

    /**
     * 
     * @param {object} record - object of the record being edited/created
     * 
     * Checks if the record has changed (if record is new, this will always return true)
     * 
     * @return {boolean} - returns true if checkbox custbody_record_changed is checked
     * 
     */
    function checkIfRecordChanged(record) {
        var recChanged = record.getValue({ fieldId: 'custbody_record_changed' });

        if(recChanged || recChanged == 'T'){
            return true;
        }

        return false;
    }

    function afterSubmit(context) {
        if(context.type == context.UserEventType.EDIT || context.type == context.UserEventType.CREATE){
            var recObj = context.newRecord;
            var recType = recObj.type;
            var recordAttachments = checkIfHasAnyAttachment(recObj.id, recType);

            // if the record has no attachments (recordAttachments.recBool = true) create a file 
            if(recordAttachments.recBool){

                var transactionFileObj = render.transaction({
                    entityId: recObj.id,
                    printMode: render.PrintMode.PDF,
                    inCustLocale: true
                });

                transactionFileObj.name = recType + "_" + recObj.id + ".pdf";
                transactionFileObj.folder = 379189;
                var fileId = transactionFileObj.save();

                // 379189 folder id of caffrey

                record.attach({
                    record: {
                        type: 'file',
                        id: fileId
                    },
                    to: recObj
                });

            // if the record has changed and record has attachments (recordAttachments.recBool = false), update the attached file
            // also removes the check on the custbody_record_changed
            } else if (checkIfRecordChanged(recObj)) {

                var fileObj = file.load({
                    id: recordAttachments.fileId
                });

                var transactionFileObj = render.transaction({
                    entityId: recObj.id,
                    printMode: render.PrintMode.PDF,
                    inCustLocale: true
                });

                transactionFileObj.name = fileObj.name;
                transactionFileObj.folder = fileObj.folder;

                var fileId = transactionFileObj.save();

                var recordObj = record.load({
                    type: recType,
                    id: recObj.id
                });

                recordObj.setValue({
                    fieldId: 'custbody_record_changed',
                    value: false
                });
                
                try {
                    recordObj.save();
                } catch (e) {
                    log.debug("Failed saving record", e);
                }

            }
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
