<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
	<link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
	<#if .locale == "zh_CN">
		<link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
	<#elseif .locale == "zh_TW">
		<link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
	<#elseif .locale == "ja_JP">
		<link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
	<#elseif .locale == "ko_KR">
		<link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
	<#elseif .locale == "th_TH">
		<link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
	</#if>
    <macrolist>
        <macro id="nlheader">
            <table class="header" style="width: 100%;"><tr>
	<td rowspan="3"><#if companyInformation.logoUrl?length != 0><img src="${companyInformation.logoUrl}" style="float: left; margin: 7px" width="150px" height="40px" /> </#if> <span class="nameandaddress"></span><br /><span class="nameandaddress"></span></td>
	<td align="right"><span class="title">${record@title}</span></td>
	</tr>
	<tr>
	<td align="right"><span class="number">#${record.tranid}</span></td>
	</tr>
	<tr>
	<td align="right">${record.trandate}</td>
	</tr></table>
        </macro>
        <macro id="nlfooter">
            <table class="footer" style="width: 100%;"><tr>
	<td></td>
	<td align="right"><pagenumber/> of <totalpages/></td>
	</tr></table>
        </macro>
    </macrolist>
    <style type="text/css">* {
		<#if .locale == "zh_CN">
			font-family: NotoSans, NotoSansCJKsc, sans-serif;
		<#elseif .locale == "zh_TW">
			font-family: NotoSans, NotoSansCJKtc, sans-serif;
		<#elseif .locale == "ja_JP">
			font-family: NotoSans, NotoSansCJKjp, sans-serif;
		<#elseif .locale == "ko_KR">
			font-family: NotoSans, NotoSansCJKkr, sans-serif;
		<#elseif .locale == "th_TH">
			font-family: NotoSans, NotoSansThai, sans-serif;
		<#else>
			font-family: NotoSans, sans-serif;
		</#if>
		}
		table {
			font-size: 9pt;
			table-layout: fixed;
		}
        th {
            font-weight: bold;
            font-size: 8pt;
            vertical-align: middle;
            padding: 5px 6px 3px;
            background-color: #e3e3e3;
            color: #333333;
        }
        td {
            padding: 4px 6px;
        }
		td p { align:left }
        b {
            font-weight: bold;
            color: #333333;
        }
        table.header td {
            padding: 0px;
            font-size: 10pt;
        }
        table.footer td {
            padding: 0px;
            font-size: 8pt;
        }
        table.itemtable th {
            padding-bottom: 10px;
            padding-top: 10px;
        }
        table.body td {
            padding-top: 2px;
        }
        table.total {
            page-break-inside: avoid;
        }
        tr.totalrow {
            background-color: #e3e3e3;
            line-height: 200%;
        }
        td.totalboxtop {
            font-size: 12pt;
            background-color: #e3e3e3;
        }
        td.addressheader {
            font-size: 8pt;
            padding-top: 6px;
            padding-bottom: 2px;
        }
        td.address {
            padding-top: 0px;
        }
        td.totalboxmid {
            font-size: 28pt;
            padding-top: 20px;
            background-color: #e3e3e3;
        }
        td.totalboxbot {
            background-color: #e3e3e3;
            font-weight: bold;
        }
        span.title {
            font-size: 28pt;
        }
        span.number {
            font-size: 16pt;
        }
        span.itemname {
            font-weight: bold;
            line-height: 150%;
        }
        hr {
            width: 100%;
            color: #d3d3d3;
            background-color: #d3d3d3;
            height: 1px;
        }
</style>
</head>
<body header="nlheader" header-height="10%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
    <table style="width: 100%; margin-top: 10px;"><tr>
	<td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
    <td class="addressheader" colspan="3"></td>
	<td class="addressheader" colspan="2"><b></b></td>
	</tr>
	<tr>
	<td class="address" colspan="3" rowspan="2">${record.billaddress}</td>
    <td class="address" colspan="3" rowspan="2"></td>
	<td align="right" colspan="2"></td>
	</tr>
	<tr>
	<td align="right" colspan="3"><b>Terms:</b> ${record.terms} <br/>
      <b>PO#:</b> ${record.otherrefnum}
      </td>
	</tr></table>
    
<#if record.item?has_content>
  
<#assign groups = []>
<#list record.item as item>
  <#if !groups?seq_contains(item.custcol_in8_group)>
    <#assign groups = groups + [ item.custcol_in8_group] >
  </#if>
</#list>
  
<!-- For each group -->
<#list groups as group>
  
  <#list record.item as item>
    <#if item.custcol_in8_group == group>
      <b>${item.custcol_in8_group_description}</b>
      <#break>
    </#if>
  </#list>
  <table class="itemtable" style="width: 100%; margin-top: 0px;"><!-- start items -->
  <thead>
      <tr>
      <th colspan="12">Item</th>
      <th colspan="12">Description</th>
      <th align="right" colspan="4">Amount</th>
      </tr>
  </thead>
  <#list record.item as item>
    <#if item.custcol_in8_group == group>
      <tr>
        <td colspan="12"><span class="itemname">${item.item}</span></td>
        <td colspan="12"><span class="itemname">${item.description}</span></td>
        <td align="right" colspan="4">${item.amount}</td>
      </tr>
    </#if>
  </#list><!-- end items --></table>
  <br/>
</#list>

<hr /></#if>
<table class="total" style="width: 100%; margin-top: 10px;">
  <tr>
	<td colspan="4">&nbsp;</td>
	<td align="right"><b>Total</b></td>
	<td align="right">${record.total}</td>
	</tr>
	  <tr>
	<td colspan="4">&nbsp;</td>
	<td align="right"><b>Amount Paid</b></td>
	<td align="right">${record.amountpaid}</td>
	</tr>
	
  </table>
  
<br/>
<br/>
<table>
<tr>
<td>
<b>Remit payments to:</b><br/>
<br/>
Backbone Group, LLC<br/>
65 North 4th Street, Suite 1<br/>
Carbondale, CO 81623<br/>
Phone: (************* ext 122<br/>
<br/>
ACH payment option: <br/>
Routing#: *********<br/>
Account#: **********	
  </td>  
</tr>    
</table> 
</body>
</pdf>