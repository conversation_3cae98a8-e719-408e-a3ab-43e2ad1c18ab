/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/query', 'N/url', 'N/ui/dialog'], function(query, url, dialog) {

    var fieldsOfInterest = [
        'entity',
        'location',
        'otherrefnum'
    ];
    var scheme;
    var host;


    function pageInit(context) {
        scheme = 'https://';
        host = url.resolveDomain({
            hostType: url.HostType.APPLICATION
        });

    }

    function fieldChanged(context) {


        // commented out for now as it wreaks havoc upon entry of a customer as customer source multiple fields which cascades to this field change multiple times.
        // check if entity, location, and otherrefnum has contents
        // if(fieldsOfInterest.indexOf(context.fieldId) === -1) {
        //     var recObj = context.currentRecord;
        //     var entity = recObj.getValue({ fieldId: fieldsOfInterest[0] });
        //     var location = recObj.getValue({ fieldId: fieldsOfInterest[1] });
        //     var otherrefnum = recObj.getValue({ fieldId: fieldsOfInterest[2] });

        //     if(!entity && !location && !otherrefnum) {
                
        //         dialog.alert({
        //             title: "Notice",
        //             message: "Please input Customer, Location and PO# first before adding anything else."
        //         });

        //         recObj.setValue({ fieldId: context.fieldId, value: '' });

        //     } else if (!location && !otherrefnum) {

        //         dialog.alert({
        //             title: "Notice",
        //             message: "Please input Location and PO# first before adding anything else."
        //         });

        //     } else if (!otherrefnum) {

        //         dialog.alert({
        //             title: "Notice",
        //             message: "Please input PO# first before adding anything else."
        //         });

        //     }

        // } else 
        
        if (fieldsOfInterest.indexOf(context.fieldId) == 2) {

            var recObj = context.currentRecord;
            var entity = recObj.getValue({ fieldId: fieldsOfInterest[0] });
            var otherrefnum = recObj.getValue({ fieldId: fieldsOfInterest[2] });
            var location = recObj.getValue({ fieldId: fieldsOfInterest[1] });

            if(entity && location) {

                validatePoNum(entity, otherrefnum, recObj, false);

            } else if (!location) {
                
                dialog.alert({
                    title: "Notice",
                    message: "Please select a location first before entering PO#"
                });
                recObj.setValue({ fieldId: fieldsOfInterest[2], value: '', ignoreFieldChange: true });

            } else {

                dialog.alert({
                    title: "Notice",
                    message: "Please select a customer first before entering PO#"
                });
                recObj.setValue({ fieldId: fieldsOfInterest[2], value: '', ignoreFieldChange: true });

            }
        }
    }

    function saveRecord(context) {
        var recObj = context.currentRecord;
        var entity = recObj.getValue({ fieldId: fieldsOfInterest[0] });
        var otherrefnum = recObj.getValue({ fieldId: fieldsOfInterest[2] });
        if(entity) {
            if(validatePoNum(entity, otherrefnum, recObj, true)){
                return false;
            }

            return true;
        }
        
    }

    function validatePoNum(entity, poNum, recObj, saveRecord){
        var sqlQueryData = "SELECT id, tranid FROM transaction WHERE entity = ? AND otherrefnum = ?";
        var resultSet = query.runSuiteQL({
            query: sqlQueryData,
            params: [entity, poNum]
        });

        var results = resultSet.results;
        if(results.length) {
            if(results[0].values[0] != recObj.id) {
                var originalSOId = results[0].values[0];
                var originalSOTranId = results[0].values[1];


                var output = url.resolveRecord({
                    recordType: 'salesorder',
                    recordId: Number(originalSOId),
                    isEditMode: true
                });

                var urlToRecord = scheme + host + output;

                var buttons = [
                    { label: 'Yes', value: 0 },
                    { label: 'No', value: 1 },
                ];

                var saveReturn = true;

                dialog.create({
                    title: 'Notice',
                    message: 'PO#'+poNum+' has been used on Sales Order #'+originalSOTranId+'. Do you want to view and modify the PO#?',
                    buttons: buttons
                }).then(function(result){
                    if(result == 0){
                        window.open(urlToRecord, '_blank');
                    } else if(result == 1) {
                        recObj.setValue({ fieldId: fieldsOfInterest[2], value: '' });
                    }

                    saveReturn = false;
                }).catch(function(reason){
                    log.error('Something went wrong', reason);
                    dialog.alert({
                        title: 'Something went wrong',
                        message: 'Something unexpected happened. Please Contact your administrator.'
                    });
                });

                if(saveRecord){
                    return saveReturn;
                }
            }
        } else {
            // proceed
        }
    }

    return {
        pageInit: pageInit,
        fieldChanged: fieldChanged,
        saveRecord: saveRecord
    }
});
