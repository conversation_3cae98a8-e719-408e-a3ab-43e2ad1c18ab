/**
 * Module Description
 *    - This script provides user functionality and validation on the "Invoice Insertion Orders" page.
 * Version    Date            Author           Remarks
 * 1.00       30 Jan 2019     Brandon Fuller    TVG
 *
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord'], function(currentRecord) {

    function pageInit() {
        
        return;

        setTimeout(function(){         
            window.document.getElementById('custpage_sublisttxt').click();
    
            setTimeout(function(){ 
                window.document.getElementById('custpage_sublist_grouptxt').click();
    
                setTimeout(function(){ 
                    
                    // window.document.getElementById('custpage_sublist_inv_div').style = 'margin-top:-20px';
                    // window.document.getElementById('custpage_sublist_layer').style = 'min-width:60%;float:left;margin-top:20px';
                    // window.document.getElementById('custpage_sublist_group_layer').style = 'min-width:40%;float:right;margin-top:20px';    
    
                    window.document.getElementsByClassName('bgsubtabbar')[1].style.display = 'none';
    //                window.document.getElementById('custpage_sublist_inv_layer').style.display = 'block';
                    
                    nlapiCancelLineItem('custpage_sublist');
                }, 500);
            },500);
        },500);
    }

    // Recalculate invoice total field when boxes are checked manually (NOT MARK ALL)
    function sublistChanged(context) {
        try {
            var form = currentRecord.get();
            var currentTotal = 0.00;
            var lineCount = form.getLineCount('custpage_sublist');
            for (var i = 0; i < lineCount; i++) {
                if (form.getSublistValue('custpage_sublist', 'custpage_bill', i) == true) {
                    currentTotal += form.getSublistValue('custpage_sublist', 'custpage_amt_field', i);
                }
            }
            form.setValue('custpage_total_field', parseFloat(currentTotal).toFixed(2));
        } catch(e) {
            window.console.log('error', e.toString());
        }
    }
    // Custom button that sets the submitLines step with validation against users submitting invoices with negative totals or no lines selected.
    function submitButton() {
        try {
            var form = currentRecord.get();
            var invoiceTotal = parseFloat(form.getValue('custpage_total_field') || 0.00);
            require(['N/ui/dialog'], function(dialog) {
                function submitDialog() {
                    dialog.confirm({
                        title : 'Invoice submission!',
                        message : 'You are about to create a $' + invoiceTotal.toLocaleString() + ' invoice for ' +
                            form.getSublistValue({sublistId : 'custpage_sublist', fieldId : 'custpage_cust_text', line : 0}) + '. Press OK to continue.'
                    }).then(function(submit) {
                        if (!!submit) {
                            form.setValue('custpage_step', 'submitLines');
                            jQuery("#submitter").click();
                        }
                    });
                }
                if (invoiceTotal < 0.00) {
                    dialog.alert({
                        title: 'Negative invoice total!',
                        message: 'Invoices cannot be created with negative totals.\nPlease make changes.'
                    });
                } else if (invoiceTotal == 0.00) {
                    var lineCount = form.getLineCount('custpage_sublist');
                    for (var i = 0; i < lineCount; i++) {
                        form.selectLine('custpage_sublist', i);
                        if (form.getCurrentSublistValue('custpage_sublist', 'custpage_bill') == true) {
                            submitDialog();
                        }
                    }
                    dialog.alert({
                        title: 'No lines selected!',
                        message: 'Please select insertion orders to invoice'
                    });
                } else {
                    submitDialog()
                }
            });
        } catch(e) {
            window.console.log('error', e.toString());
        }
    }
    // markAll custom button that recalculates invoice total field
    function markAll() {
        try {
            var form = currentRecord.get();
            var lineCount = form.getLineCount('custpage_sublist');
            var currentTotal = 0.00;
            for (var i = 0; i < lineCount; i++) {
                form.selectLine('custpage_sublist', i);
                form.setCurrentSublistValue('custpage_sublist', 'custpage_bill', true, false);
                var lineAmount = form.getCurrentSublistValue({sublistId : 'custpage_sublist', fieldId : 'custpage_amt_field_2'});
                currentTotal += lineAmount;
            }
            form.setValue('custpage_total_field', parseFloat(currentTotal).toFixed(2));
        } catch(e) {
            window.console.log('error', e.toString());
        }
    }
    // unmarkAll custom button that resets invoice total field
    function unmarkAll() {
        try {
            var form = currentRecord.get();
            var lineCount = form.getLineCount('custpage_sublist');
            for (var i = 0; i < lineCount; i++) {
                form.selectLine('custpage_sublist', i);
                form.setCurrentSublistValue('custpage_sublist', 'custpage_bill', false, false);
            }
            form.setValue('custpage_total_field', 0.00);
        } catch(e) {
            window.console.log('error', e.toString());
        }
    }
    // CSV export custom button
    function exportButton() {
        require(["N/ui/dialog"], function(dialog) {
            // Provide user with selectable export options (PDF/CSV)
            dialog.create({
                title : 'Export Invoicing Lines!',
                message : 'Would you like to export this as a CSV document?', //PDF or
                buttons : [
                    //{ label : 'PDF', value : 'pdf' },
                    { label : 'CSV', value : 'csv' },
                    { label : 'Cancel', value : 'cancel'}
                ]
            }).then(function(selection) {
                require(['N/url'], function(url) {
                    var form = currentRecord.get();
                    if (selection == 'cancel') {
                        return false;
                    } else {
                        window.open(url.resolveScript({
                            scriptId : 'customscript_bulk_invoice_export_sl',
                            deploymentId : 'customdeploy_bulk_invoice_export_sl',
                            returnExternalUrl : false,
                            params : {
                                cust : form.getValue("custpage_cust_filt"),
                                start : form.getValue("custpage_start_filt"),
                                end : form.getValue("custpage_end_filt"),
                                linesToExclude : form.getValue("custpage_lines_to_exclude")
                            }
                        }), '_new');
                        return false;
                    }
                });
            }).catch(function(error) {
                require(["N/dialog"], function(dialog) {
                    dialog.alert({
                        title : 'Uh oh!',
                        message : 'There was a problem exporting... ' + error.message
                    });
                });
            });
        });                
    }

    function fieldChanged(context) {
        
        if (context.fieldId == 'custpage_num_groups') {

            submitter.click();

            // var form = currentRecord.get();
            
            // var lineCount = form.getLineCount('custpage_sublist');            

            // //debugger;
            // // remove the groups
            // // for (var i = 50; i >= 1; i--) {
            // //     try {
            // //         nlapiRemoveLineItemOption('custpage_sublist', 'custpage_group', i);
            // //     } catch(e) {}
            // // }
    
            // if (form.getLineCount('custpage_sublist_group') > 0) {
            //     for (var i = form.getLineCount('custpage_sublist_group') - 1; i >= 0; i--) {
            //         form.removeLine({ sublistId: 'custpage_sublist_group', line: i, ignoreRecalc: true});                
            //     }
            // }            
            
            // var n = form.getValue("custpage_num_groups");
    
            // for (var i = 0; i < n; i++) {
            //     form.selectLine('custpage_sublist_group', i);
            //     form.setCurrentSublistValue('custpage_sublist_group', 'custpage_group', i + 1, true);
            //     form.commitLine('custpage_sublist_group');                                
            // }
            
            // var lineCount = form.getLineCount('custpage_sublist');

            // for (var i = 0; i < lineCount; i++) {
            //     for (var j = 0; j < n; j++) {
            //         var field = form.getSublistField({ sublistId: 'custpage_sublist', fieldId: 'custpage_group', line: i })                                    

            //         // remove select options
            //         // for (var k = 50; k >= 0; k--) {
            //         //     try {
            //         //         field.removeSelectOption(k);
            //         //     } catch(e) {}
            //         // }

            //         field.insertSelectOption({
            //             value: j + 1,
            //             text: 'Group ' + (j + 1)
            //         });
            //     }
            // }
        }
    }

    return {
        sublistChanged : sublistChanged,
        submitButton : submitButton,
        markAll : markAll,
        unmarkAll : unmarkAll,
        exportButton : exportButton,
        pageInit: pageInit, 
        fieldChanged: fieldChanged
    };
});