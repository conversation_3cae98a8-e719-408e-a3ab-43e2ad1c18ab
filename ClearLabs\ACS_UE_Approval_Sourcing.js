/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function beforeLoad(context) {
        var recordObj = context.newRecord;
        if(recordObj.id){
            var nextApprover = recordObj.getValue({ fieldId: 'custpage_sas_next_approver' });
            var acsApprover = recordObj.getValue({ fieldId: 'custbody_acs_next_approver' });
            var approvalStatus = recordObj.getValue({ fieldId: 'custpage_sas_approval_status' });
            
            if((nextApprover != acsApprover) && (approvalStatus == 1)){
                var approvalStatus = recordObj.getValue({ fieldId: 'custpage_sas_approval_status' });
                var nextApprover = recordObj.getValue({ fieldId: 'custpage_sas_next_approver' });

                var recordObj = record.load({
                    id: recordObj.id,
                    type: record.Type.SALES_ORDER,
                    isDynamic: true
                });

                recordObj.setValue({ fieldId: 'custbody_acs_approval_status', value: approvalStatus });
                recordObj.setValue({ fieldId: 'custbody_acs_next_approver', value: nextApprover });
                recordObj.save();
                log.debug({
                    title: 'test',
                    details: {
                        approval_status: approvalStatus,
                        next_approver: nextApprover
                    }
                });
            }
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
