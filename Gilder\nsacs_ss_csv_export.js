/**
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
define(['N/task', 'N/format', 'N/file', 'N/log', 'N/runtime'], function (task, format, file, log, runtime) {

    function execute(context) {
        var searchTask = task.create({
            taskType: task.TaskType.SEARCH
        });

        searchTask.savedSearchId = 'customsearch_ns_acs_wire_transactions_2';

        var dateTime = getFormattedTime();
        var filePath = 'Celigo Payment Files/GGH_' + dateTime + '.csv';
        searchTask.filePath = filePath;
        var searchTaskId = searchTask.submit();
        log.debug('searchTaskId', searchTaskId);

        var getFileID = file.load({
            id: filePath
        });
        var fileId = getFileID.id;
        log.debug('fileId', fileId);

        var mrScript = runtime.getCurrentScript().getParameter({
            name: 'custscript_nsacs_mr_script'
        });
        var mrTask = task.create({
            taskType: task.TaskType.MAP_REDUCE,
            scriptId: mrScript,
            deploymentId: 1,
            params: {
                'custscript_nsacs_csv_file': fileId
            }
        });
        var mrTaskID = mrTask.submit();
        log.debug('mrTaskID', mrTaskID);
    }

    function getFormattedTime() {
        var today = new Date();
        var y = today.getFullYear();
        // JavaScript months are 0-based.
        var m = today.getMonth() + 1;
        var d = today.getDate();
        var h = today.getHours();
        var mi = today.getMinutes();
        var s = today.getSeconds();
        return m + "-" + d + "-" + y + "_" + h + "-" + mi + "-" + s;
    }

    return {
        execute: execute
    }
});