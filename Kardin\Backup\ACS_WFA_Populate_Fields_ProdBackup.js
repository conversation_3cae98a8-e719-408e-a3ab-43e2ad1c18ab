/**
 *@NApiVersion 2.x
 *@NScriptType WorkflowActionScript
 */
 define(['N/record', 'N/search', 'N/format', 'N/url'], function(record, search, format, url) {

    function getOriginalContractID(contractId) {

        var origContractSearchValues = search.lookupFields({
            type: 'customrecord_contracts',
            id: contractId,
            columns: ['custrecord_swe_original_contract']
        });
        
        if(origContractSearchValues.custrecord_swe_original_contract) {
            return origContractSearchValues.custrecord_swe_original_contract[0].value;
        }

        return false;

    }

    function onAction(scriptContext) {

        var recObj = record.load({
            type: 'customrecord_contract_item',
            id: scriptContext.newRecord.id,
            isDynamic: true
        });

        var date = recObj.getValue({
            fieldId: 'created'
        });

        formattedDate = format.format({
            value: date,
            type: format.Type.DATE
        });

        dateNow = format.format({
            value: new Date(),
            type: format.Type.DATE
        });

        // if contract item has been created today
        if((formattedDate == dateNow)) {

            var contractId = recObj.getValue({
                fieldId: 'custrecord_ci_contract_id'
            });

            var lineNo = recObj.getValue({
                fieldId: 'custrecord_ci_original_so_lineno'
            });           

            var origContractId = getOriginalContractID(contractId);
            if(origContractId) {
                var searchObj = search.load({
                    id: 'customsearch_acs_contract_item_search'
                });

                var contractIdFilter = search.createFilter({
                    name: 'custrecord_ci_contract_id',
                    operator: search.Operator.ANYOF,
                    values: [origContractId]
                });
                searchObj.filters.push(contractIdFilter);
    
                var resultSet = searchObj.run();

                searchResult = resultSet.getRange({ start: (parseInt(lineNo) - 1), end: parseInt(lineNo) });
                searchResult.forEach(function(row) {
                    row.columns.forEach(function(column) {
                        if(column.name == 'internalid') {
                            log.audit('Original Contract Item ID', row.getValue(column));
                            return;
                        }
                        if(column.name == 'custrecord_kardin_unlock_date_item') {
                            recObj.setValue({
                                fieldId: column.name,
                                value: new Date(row.getValue(column))
                            });
                        } else {
                            recObj.setValue({
                                fieldId: column.name,
                                value: row.getValue(column)
                            });
                        }
                    });
                });
            }

            try{
                recObj.save();

                
                var outputUrl = url.resolveScript({
                    scriptId: 'customscript_acs_render_label',
                    deploymentId: 'customdeploy_acs_render_lbl_suitelet_depl',
                    params: {
                        custpage_POId: rec.id
                    }
                });

            } catch (e) {
                log.debug("Error", e);
            }
        }
    }
    return {
        onAction: onAction
    }
});
