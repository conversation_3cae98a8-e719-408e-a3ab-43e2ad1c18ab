/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/runtime'], function(search, record, runtime) {

    function getInputData() {
        var scriptObj = runtime.getCurrentScript();
        var parentId = JSON.parse(scriptObj.getParameter('custscript_parent_id'));

        try {
            var subItemSearch = search.load({
                id: 'customsearch_si_search_su'
            });

            var itemFilter = search.createFilter({
                name: 'parent',
                operator: search.Operator.ANYOF,
                values: [parentId]
            });
            subItemSearch.filters.push(itemFilter);

            var subItemSearchCount = subItemSearch.runPaged().count;
            log.audit('getInput - subItemSearchCount', subItemSearchCount);
            if(subItemSearchCount < 300) {
                return subItemSearch;
            } else {
                log.error('Too Many Sub Items!', 'This parent has exceeded 300 sub item limit. Please Contact Your Administrator.');
                return [];                
            }
        } catch (e) {
            log.error('error', e);
        }

    }

    function map(context) {
        try {

            var scriptObj = runtime.getCurrentScript();
            var bodyFieldChanges = JSON.parse(scriptObj.getParameter('custscript_body_field_changes'));
            var priceLevelChanges = JSON.parse(scriptObj.getParameter('custscript_price_level_changes'));
            var locationChanges = JSON.parse(scriptObj.getParameter('custscript_location_changes'));
            
            var keyFromInput = context.key;

            var subItemObj = record.load({
                type: record.Type.INVENTORY_ITEM,
                id: keyFromInput
            });

            // apply body field changes
            for(var field in bodyFieldChanges) {
                subItemObj.setValue({ fieldId: field, value: bodyFieldChanges[field] });
            }

            // apply price level header changes
            if(priceLevelChanges.header.length) {
                var priceLevelHeader = priceLevelChanges.header;
                for(var i = 0; i < priceLevelHeader.length; i++) {
                    subItemObj.setMatrixHeaderValue({
                        sublistId: 'price',
                        fieldId: 'price',
                        column: priceLevelHeader[i].column,
                        value: priceLevelHeader[i].newValue,
                    });
                }
            }

            // apply price level sublist changes
            if(priceLevelChanges.sublist.length) {
                var priceLevelSublist = priceLevelChanges.sublist;
                for(var i = 0; i < priceLevelSublist.length; i++) {
                    subItemObj.setMatrixSublistValue({
                        sublistId: 'price',
                        fieldId: 'price',
                        column: priceLevelSublist[i].column,
                        line: priceLevelSublist[i].row,
                        value: priceLevelSublist[i].newValue
                    });
                }
            }

            // apply location sublist changes
            if(locationChanges.length) {
                for(var i = 0; i < locationChanges.length; i++) {
                    subItemObj.setSublistValue({
                        sublistId: 'locations',
                        fieldId: locationChanges[i].fieldId,
                        line: locationChanges[i].line,
                        value: locationChanges[i].newValue
                    });
                }
            }

            var subItemID = subItemObj.save({
                enableSourcing: false,
                ignoreMandatoryFields: true
            });

            log.audit('Successfully saved subitem', { subItemID: subItemID });
        } catch (e){
            log.error("error", e);
        }

    }

    function reduce(context) {
        
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
