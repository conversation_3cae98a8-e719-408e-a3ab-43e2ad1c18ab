/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search'], function(record, search) {

    function beforeLoad(context) {

        if(context.type == context.UserEventType.EDIT || context.type == context.UserEventType.CREATE) {
            var form = context.form;

            var objSublist = form.getSublist({ id: 'item' });
            var taxCodeField = objSublist.getField({ id: 'taxcode' });
            taxCodeField.isMandatory = false;

            log.debug('test', taxCodeField.isMandatory);
        }
        
    }

    function afterSubmit(context) {
        var recObj = record.load({
            type: record.Type.SALES_ORDER,
            id: context.newRecord.id,
            isDynamic: true
        });

        for(var i = 0; i < recObj.getLineCount({ sublistId: 'item' }); i++){
            var itemId = recObj.getSublistValue({
                sublistId: 'item',
                fieldId: 'item',
                line: i
            });

            var itemLookUp = search.lookupFields({
                type: search.Type.ITEM,
                id: itemId,
                columns: [
                    'taxschedule'
                ]
            });

            var taxCode = "";
            if(itemLookUp.taxschedule[0].value){

                switch(itemLookUp.taxschedule[0].value) {
                    case 1:
                        taxCode = 11;
                        break;
                    case 2:
                        taxCode = 15;
                        break;
                    default:
                        break;
                }

                recObj.setSublistValue({
                    sublistId: 'item',
                    fieldId: 'taxcode',
                    line: i,
                    value: taxCode
                });
            }
        }
        
        try {
            var recordId = recObj.save();
            log.debug('Success', 'Successfully saved sales order (ID: ' + recordId + ')');
        } catch (error) {
            log.debug('Error', error);
        }
    }

    return {
        beforeLoad: beforeLoad,
        afterSubmit: afterSubmit
    }
});
