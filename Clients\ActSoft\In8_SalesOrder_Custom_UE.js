/**
* Sales Order Custom
*
* Version		Date			Author				Remarks
* 1.0			25 Sep 2019		<PERSON> (In8Sync)	Initial Version
*
*/

/**
* @appliedtorecord recordtype
* 
* @param {String} type Operation types: create, edit, delete, xedit
*
* @returns {Void}
*/
function beforeSubmit(type) {

    try {
        if (type == 'create' || type == 'edit') {
            if (nlapiGetFieldValue('custbody_in8_wc_order_id')) {                    
                if (nlapiGetFieldValue('custbody_in8_startdate')) {
                    nlapiSetFieldValue('custbody_rr_startdate', nlapiGetFieldValue('custbody_in8_startdate')); 
                    nlapiSetFieldValue('custbody_in8_startdate', '');
                }
                if (nlapiGetFieldValue('custbody_in8_enddate')) {
                    nlapiSetFieldValue('custbody_rr_enddate', nlapiGetFieldValue('custbody_in8_enddate'));          
                    nlapiSetFieldValue('custbody_in8_enddate', '');
                }
            }
        }
    } catch(e) {
        nlapiLogExecution('ERROR', 'Error', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
    }
}
