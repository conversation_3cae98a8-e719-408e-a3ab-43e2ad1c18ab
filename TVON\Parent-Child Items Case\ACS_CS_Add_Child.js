/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord', 'N/url'], function(currentRecord, url) {

    function pageInit(context) {
        
    }

    function createChild(){
        rec = currentRecord.get();
        var outputUrl = url.resolveScript({
            scriptId: 'customscript_acs_sl_create_child',
            deploymentId: 'customdeploy_acs_sl_create_child',
            params: {
                parent_item: rec.id
            }
        });
        window.open(outputUrl);
    }

    return {
        pageInit : pageInit,
        createChild : createChild
    }
});
