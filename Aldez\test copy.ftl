<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table border-bottom="2px" border-top="2px" class="header" style="width: 100%;">
                    <tr>
                        <td align="center" style="font-size: 27px; height: 74px;"><br /><b>Work Order</b><br />&nbsp;
                        </td>
                        <td align="right" style="font-size: 27px; height: 74px;"><br /><b>${record.tranid}</b></td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td>
                            <barcode codetype="code128" showtext="true" value="${record.tranid}" />
                        </td>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #E3E3E3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #D3D3D3;
                background-color: #D3D3D3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="10%" footer="nlfooter" footer-height="10pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <td align="left" style="height: 27px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Customer:&nbsp; &nbsp;
                    &nbsp; &nbsp;${record.entity}</td>
                <td align="right" style="height: 27px;">Due Date: &nbsp; &nbsp; &nbsp; ${record.enddate}</td>
            </tr>
            <tr>
                <td style="height: 31px;">Production Date:&nbsp; &nbsp; &nbsp; &nbsp; ${record.custbody_p_start_date}
                </td>
            </tr>
            <tr>
                <td style="height: 31px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                    &nbsp;Item:&nbsp; &nbsp; &nbsp; &nbsp; ${record.assemblyitem}</td>
            </tr>
            <tr>
                <td style="height: 31px;">Item Description:&nbsp; &nbsp; &nbsp; &nbsp;
                    ${record.assemblyitem.purchasedescription}</td>
            </tr>
            <tr>
                <td style="height: 31px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                    &nbsp;Size:&nbsp; &nbsp; &nbsp; &nbsp; ${record.units}</td>
            </tr>
            <tr>
                <td style="height: 31px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                    &nbsp; Qty:&nbsp; &nbsp; &nbsp; &nbsp; ${record.quantity}</td>
            </tr>
        </table>
        <table class="comments" style="width: 100%; margin-top: 10px;">
            <tr>
                <td><b>Comments</b></td>
            </tr>
            <tr>
                <td>${record.memo}; ${record.assemblyitem.custitem_bom_memo}</td>
            </tr>
        </table>
        <table class="comments" style="width: 100%; margin-top: 10px;">
            <tr>
                <td style="font-size: 22px;"><b>Bill Of Material</b></td>
            </tr>
        </table>
        <#if record.item?has_content>
            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th align="center" colspan="3"><b>Scrap</b></th>
                                <th colspan="4"><b>Item</b></th>
                                <th align="left" colspan="4"><b>Description</b></th>
                                <th align="center" colspan="4"><b>Total Qty per WO</b></th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td align="center" colspan="3" line-height="150%">___________</td>
                        <td colspan="4">${item.item}</td>
                        <td align="left" colspan="4">${item.description}</td>
                        <td align="center" colspan="4">${item.quantity}</td>
                    </tr>
                    <!-- end items -->
                </#list>
            </table>
            <hr />
        </#if>
        <#if record.assemblydetail?has_content>
            <table class="comments" style="width: 100%; margin-top: 10px;">
                <tr>
                    <td style="font-size: 22px;"><b>Assembly Hierarchy</b></td>
                </tr>
            </table>
            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.assemblydetail as assembly>
                    <#if assembly_index==0>
                        <thead>
                            <tr>
                                <th colspan="4"><b>Item</b></th>
                                <th align="center" colspan="2">${assembly.quantity@label}</th>
                            </tr>
                        </thead>
                        <tr>
                            <td colspan="4"><span class="itemname">${assembly.item}</span><br />${assembly.description}
                            </td>
                            <td align="center" colspan="2">&nbsp;</td>
                        </tr>
                        <#else>
                            <#list record.item as recordItem>
                                <#if recordItem.item==assembly.item>
                                    <#assign _space=recordItem.assemblylevel - 1>
                                </#if>
                            </#list>
                            <tr>
                                <td colspan="4"><span
                                        class="itemname">${assembly.item}</span><br />${assembly.description}</td>
                                <td colspan="4"><span class="itemname">
                                        <@repeat input="&emsp;" times=_space />${assembly.item}</span><br />
                                    <@repeat input="&emsp;" times=_space />${assembly.description}</td>
                                <td align="center" colspan="2">${assembly.quantity}</td>
                            </tr>
                    </#if>
                </#list><!-- end items -->
            </table>
        </#if>
        <table class="comments" style="width: 100%; margin-top: 10px;">
            <tr>
                <td>Packed By:______________________________________</td>
            </tr>
            <tr>
                <td>Packed Date:_____________________________________</td>
            </tr>
            <tr>
                <td>Start Time:______________________________</td>
                <td>Finish Time:______________________________</td>
            </tr>
        </table>
    </body>
</pdf>