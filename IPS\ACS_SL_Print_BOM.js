/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
 define(['N/https', 'N/render', 'N/record', 'N/search', 'N/file', 'N/runtime', 'N/query'], function(https, render, record, search, file, runtime, query) {

    function getItemAssoc(itemId, type) {
        var sqlQueryData = "SELECT COUNT(DISTINCT transaction.id) FROM transaction INNER JOIN transactionline ON transactionline.transaction = transaction.id WHERE transactionline.item = ? AND transaction.type = ?";
        var resultSet = query.runSuiteQL({
            query: sqlQueryData,
            params: [itemId, type]
        });
        var results = resultSet.results;
    
        return results[0].values[0] ? results[0].values[0] : 0;
    }

    function sortComponents (a, b) {
        var itemNameA = a.item_name.toUpperCase();
        var itemNameB = b.item_name.toUpperCase();
        if (itemNameA < itemNameB) {
            return -1;
        }
        if (itemNameA > itemNameB) {
            return 1;
        }
    
        // names must be equal
        return 0;
    }

    function onRequest(context) {
        if(context.request.method == https.Method.GET){
            requestParams = context.request.parameters;
            if(requestParams.hasOwnProperty('item_id') && requestParams.hasOwnProperty('is_simple')) {
                itemId = parseInt(requestParams.item_id);
                isSimple = requestParams.is_simple == 'true';
                var scriptObj = runtime.getCurrentScript();

                // CREATE TEMPLATE RENDERER OBJECT
                var renderer = render.create();

                // GET TEMPLATE BASED ON PARAMETERS ON SCRIPT
                var templateFileID = scriptObj.getParameter({ name: 'custscript_template_to_use' });

                var templateFileObj = file.load({
                    id: templateFileID
                });
            
                renderer.templateContent = templateFileObj.getContents();

                var assmbItemObj = record.load({
                    type: record.Type.ASSEMBLY_ITEM,
                    id: itemId,
                    isDynamic: true
                });

                // ADD THE RECORD TO BE USED BASED ON PARAMETERS GIVEN
                renderer.addRecord({
                    templateName: 'record',
                    record: assmbItemObj
                });


                // BUILD THE ASSEMBLY ITEM'S COMPONENT DETAILS
                var assemblyItemComponentArr = buildAssemblyItemComponentDetails(assmbItemObj, isSimple, 0);

                assemblyItemComponentArr.sort(sortComponents);

                log.debug("assemblyitems", {assemblyItemComponentArr: assemblyItemComponentArr});

                renderer.addCustomDataSource({
                    format: render.DataSource.JSON,
                    alias: "assm_components",
                    data: JSON.stringify({ components: assemblyItemComponentArr })
                });

                // GET THE TOTAL AMOUNT OF ALL THE ASSEMBLY COMPONENTS
                var componentsTotal = 0.00;
                var qtyTotal = 0;
                for(var i = 0; i < assemblyItemComponentArr.length; i++) {
                    componentsTotal += assemblyItemComponentArr[i].item_total;
                    qtyTotal += assemblyItemComponentArr[i].item_qty;
                }

                renderer.addCustomDataSource({
                    format: render.DataSource.JSON,
                    alias: "assm_total",
                    data: JSON.stringify({ total: componentsTotal, qty_total: qtyTotal })
                });

                // GET THE TOTAL ITEM ASSOCIATED IN SO
                var totalCountSO = getItemAssoc(itemId, 'SalesOrd');

                // GET THE TOTAL ITEM ASSOCIATED IN PO
                var totalCountPO = getItemAssoc(itemId, 'PurchOrd');

                renderer.addCustomDataSource({
                    format: render.DataSource.JSON,
                    alias: "assm_tran_total",
                    data: JSON.stringify({ total_count_so: totalCountSO, total_count_po: totalCountPO })
                });

                // GET THE SALES PRICE
                var salesPrice = assmbItemObj.getSublistValue({
                    sublistId: 'price1',
                    fieldId: 'price_1_',
                    line: 0
                });

                renderer.addCustomDataSource({
                    format: render.DataSource.JSON,
                    alias: "salesPrice",
                    data: JSON.stringify({ price: salesPrice })
                });                

                // RENDER AS PDF
                var BOMXml = renderer.renderAsString();

                // SEND RESPONSE XML TO RENDER AS PDF
                context.response.renderPdf(BOMXml);

            } else {
                context.response.write({
                    output: 'No item_id'
                });
            }
        }
    }

    function buildAssemblyItemComponentDetails(assmbItemObj, isSimple, level) {
        var componentLength = assmbItemObj.getLineCount({ sublistId: 'member' });
        var componentsArr = [];
        level += 1;

        // loop through all the components
        for(var i = 0; i < componentLength; i++) {
            
            var componentItemType = assmbItemObj.getSublistValue({ sublistId: 'member', fieldId: 'sitemtype', line: i });
            var componentItemID = assmbItemObj.getSublistValue({ sublistId: 'member', fieldId: 'item', line: i });
            var componentItemDesc = assmbItemObj.getSublistValue({ sublistId: 'member', fieldId: 'memberdescr', line: i });
            var componentItemQty = assmbItemObj.getSublistValue({ sublistId: 'member', fieldId: 'quantity', line: i });
                                
            var compLookUp = search.lookupFields({
                type: search.Type.ITEM,
                id: componentItemID,
                columns: ['cost', 'itemid', 'purchasedescription', 'displayname']
            });

            // if description is blank, get puchase description OR display name
            // if(!componentItemDesc){
            // var componentItemDesc = ((compLookUp.purchasedescription) ? compLookUp.purchasedescription : compLookUp.displayname );
            // }
            
            var componentItemDesc = compLookUp.purchasedescription;

            // truncate description if too long
            // var length = 43;
            // var componentItemDesc = componentItemDesc.length > length ? 
            //                         componentItemDesc.substring(0, length - 3) + "..." : 
            //                         componentItemDesc;

            var componentObj = {
                item_name: compLookUp.itemid,
                item_level: level,
                item_desc: componentItemDesc,
                item_type: componentItemType,
                item_cost: 0.00,
                item_qty: Number(componentItemQty),
                item_total: 0.00
            }
            
            // if component is an assembly item, call self
            if(componentItemType == 'Assembly') {

                var componentAssmbItemObj = record.load({
                    type: record.Type.ASSEMBLY_ITEM,
                    id: componentItemID,
                    isDynamic: true
                });

                var assemblyComponents = buildAssemblyItemComponentDetails(componentAssmbItemObj, isSimple, level);
                assemblyComponents.sort(sortComponents);
                var totalCostAllComponent = 0.00;
                assemblyComponents.forEach(function(element) {
                    totalCostAllComponent += element.item_total;
                });

                componentObj.item_cost = totalCostAllComponent;
                componentObj.item_total = componentObj.item_qty * totalCostAllComponent;

                if(isSimple) {
                    componentObj["item_components"] = [];
                } else {
                    componentObj["item_components"] = assemblyComponents;
                }

            } else {
                
                componentObj.item_cost = Number(compLookUp.cost);
                componentObj.item_total = componentObj.item_qty * componentObj.item_cost;
                componentObj["item_components"] = [];

            }

            componentsArr.push(componentObj);

        }

        return componentsArr;

    }

    return {
        onRequest: onRequest
    }
});
