/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
 define(['N/currentRecord', 'N/url'], function(currentRecord, url) {

    function pageInit(context){
    }

    function redirectToPDF(str_type){
        rec = currentRecord.get();
        var outputUrl = url.resolveScript({
            scriptId: 'customscript_noel_sl_inv_worksheet',
            deploymentId: 'customdeploy_noel_sl_inv_worksheet',
            params: {
                custpage_invoice: rec.id
            }
        });
        window.open(outputUrl);
    }    

    return {
        pageInit : pageInit,
        redirectToPDF : redirectToPDF
    }
});
