/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/workflow', 'N/record'], function(workflow, record) {

    function afterSubmit(context) {
        workflow.trigger({
            recordType: record.Type.VENDOR_PAYMENT,
            recordId: context.newRecord.id,
            workflowId: 'customworkflow10',
            actionId: 'workflowaction13257'
        });
    }

    return {
        afterSubmit: afterSubmit
    }
});
