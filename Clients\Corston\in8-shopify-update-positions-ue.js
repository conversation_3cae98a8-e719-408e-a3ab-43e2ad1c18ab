/**
 * @NApiVersion 2.x
 * @NScriptType UserEventScript
 */

define(['N/search'], function(search) {
    function getProperties(lineId, properties) {
        return properties.reduce(function(propertyMap, property) {
            var propName = property.name;
            var propValue = property.value;
            propertyMap.lineId = lineId;

            if (propName == '_type') {
                propertyMap.type = propValue;
            } else if (propName == '_identifier') {
                propertyMap.identifier = propValue;
            } else if (propName == '_group-sku') {
                propertyMap.groupSku = propValue;
            } else if(propName == '_position') {
                propertyMap.position = propValue;
            } else if(propName == '_parent') {
                propertyMap.parentSku = propValue;
            } else if(propName.indexOf('_position') == 0) {
                propertyMap.positions = propertyMap.positions || [];
                propertyMap.positions.push({
                    position: propName,
                    value: propValue
                });
            }

            return propertyMap;
        }, {});
    }
    function getLineItemMap(lineItems) {
        var itemMap = lineItems.reduce(function(itemMap, lineItem) {
            if (lineItem.properties.length == 0) return itemMap;
            var lineId = lineItem.id;
            var properties = lineItem.properties;

            var propertyMap = getProperties(lineId, properties);
            var mapType = propertyMap.type;

            itemMap[mapType][lineId] = itemMap[mapType][lineId] || propertyMap;

            return itemMap;
        }, { parent: {}, child: {}});
        return itemMap;
    }
    function getShopDomain(settingsId) {
        var shopDomain = search.create({
            type: 'customrecord_in8_shop_settings',
            filters: [
                [ 'internalid' , search.Operator.ANYOF, settingsId ]
            ],
            columns: [ 'custrecord_in8_shopify_domain_ref' ]
        })
        .run()
        .getRange({ start: 0, end: 1 })
        .reduce(function(res, result) {
            return result.getValue({ name: 'custrecord_in8_shopify_domain_ref' });
        }, '');
        return shopDomain;
    }
    function getOrderPayload(shopDomain, shopOrderId) {
        var payload = search.create({
            type: 'customrecord_in8_shopify_requests',
            filters: [
                [ 'custrecord_in8_shopify_domain' , search.Operator.IS, shopDomain ],
                'AND',
                [ 'custrecord_in8_shopify_req_id' , search.Operator.IS, shopOrderId ],
                'AND',
                [ 'custrecord_in8_shop_req_topic', search.Operator.IS, 'orders/paid' ]
            ],
            columns: [ 'custrecord_in8_shop_req_body' ]
        })
        .run()
        .getRange({ start: 0, end: 1 })
        .reduce(function(res, result) {
            return result.getValue({ name: 'custrecord_in8_shop_req_body' });
        }, '');
        return payload;
    }
    function getPositionText(positionList) {
        return positionList.map(function(position) {
            return [position.position, position.value].join('<br>');
        },'')
        .join('<br><br>');
    }
    function updateLines(newRecord, parentLines, childrenLines) {
        Object.keys(parentLines).forEach(function (pLineId) {
            var parentItem = parentLines[pLineId];
            log.debug({ title: 'parentItem', details: parentItem });
            var index = newRecord.findSublistLineWithValue({ sublistId: 'item', fieldId: 'custcol_in8_shopify_line_id', value: parentItem.lineId });
            if (index !== -1) {
                var positionText = getPositionText(parentItem.positions);
                newRecord.setSublistValue({ sublistId: 'item', fieldId: 'custcol_identifier', value: parentItem.identifier, line: index });
                newRecord.setSublistValue({ sublistId: 'item', fieldId: 'custcol_group_sku', value: parentItem.groupSku, line: index });
                newRecord.setSublistValue({ sublistId: 'item', fieldId: 'custcol_type', value: parentItem.type, line: index });
                newRecord.setSublistValue({ sublistId: 'item', fieldId: 'custcol_position', value: positionText, line: index });
            }
        });
        Object.keys(childrenLines).forEach(function (cLineId) {
            var childItem = childrenLines[cLineId];
            log.debug({ title: 'childItem', details: childItem });
            var index = newRecord.findSublistLineWithValue({ sublistId: 'item', fieldId: 'custcol_in8_shopify_line_id', value: childItem.lineId });
            if (index !== -1) {
                newRecord.setSublistValue({ sublistId: 'item', fieldId: 'custcol_identifier', value: childItem.identifier, line: index });
                newRecord.setSublistValue({ sublistId: 'item', fieldId: 'custcol_type', value: childItem.type, line: index });
                newRecord.setSublistValue({ sublistId: 'item', fieldId: 'custcol_tran_position', value: childItem.position, line: index });
                newRecord.setSublistValue({ sublistId: 'item', fieldId: 'custcol_tran_parentitem', value: childItem.parentSku, line: index });
            }
        });
    }
    /**
     *
     * Child Items will have:
     *   Properties
     *       _type: child
     *       _parent: <parent SKU>
     *       _identifier: <key> (matches Parent and Child items together)
     *   NetSuite Order
     *   Parent line need to have the following Set:
     *       _identifier -> Identifier
     *       _type -> Relationship
     *       _group-sku -> Group SKU
     *       Positions
     *           all the Properties for _positions. that indicate the which child items is in what position.
     *   Child lines need to have the following set:
     *       _parent -> Parent
     *       _identifier -> Identifier
     *       _type -> Relationship
     *       Position:
     *           The position listing from the Parent Positions with the following Match, where the Child SKU is found in the Position Line.(format: title, sku, price)
     *           _parent
     *           _identifier
     */
    function beforeSubmit(context) {
        if (context.type != 'create') return;

        var newRecord = context.newRecord;

        var settingsId = newRecord.getValue({ fieldId: 'custbody_in8_shop_setting' });
        log.debug({ title: 'settingsId', details: settingsId });
        if (!settingsId) return;

        var shopOrderId = newRecord.getValue({ fieldId: 'custbody_in8_shop_id' });
        log.debug({ title: 'shopOrderId', details: shopOrderId });
        if (!shopOrderId) return;

        var shopDomain = getShopDomain(settingsId);
        log.debug({ title: 'shopDomain', details: shopDomain });
        if (!shopDomain) return;

        var payload = getOrderPayload(shopDomain, shopOrderId);
        log.debug({ title: 'payload', details: payload });
        if (!payload) return;

        var body = JSON.parse(payload);
        var lineItemMap = getLineItemMap(body.line_items || []);
        log.debug({ title: 'lineItemMap', details: lineItemMap });

        updateLines(newRecord, lineItemMap.parent, lineItemMap.child);
    }
    return {
        beforeSubmit: beforeSubmit
    };
});