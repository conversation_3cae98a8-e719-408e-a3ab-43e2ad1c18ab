var secretKey = 'ac8420dd-5069-4877-8d19-c017c12706b2';
var recordId = null;
var accesslevel = 1;
var customform = null;
var id = null;
var hash = '';
var subtabs = [];
var sourceid = null;
var sourceform = null;
var touchedFields = [];
var defaultOpen = [];

function suitelet(request, response)
{
    try {
        if(request.getMethod() == "GET")
        {
            customform = request.getParameter('customform');

            if(customform == "null")
                throw nlapiCreateError('Custom Form', "You have insufficient privileges to access to this form");

            recordId = request.getParameter('recordid');

            if(request.getParameter('accesslevel'))
                accesslevel = request.getParameter('accesslevel');

            id = request.getParameter('internalid');
            hash = request.getParameter('hash');

            sourceid = request.getParameter('sourceid');
            sourceform = request.getParameter('sourceform');

            var hashCompare = String(CryptoJS.SHA256(secretKey + id));

            if (!id || hash != hashCompare) {

                var form = nlapiCreateForm("Error");

                var field = form.addField('custpage_message', 'inlinehtml', 'Message');
                field.setDefaultValue("<h2>Invalid parameters: " + hash + "</h2>");

                response.writePage(form);
                return;
            }

            var form = nlapiCreateForm('');

            form.setScript('customscript_in8_externalform_cs');

            createBodyFields(form, customform);

            if(request.getParameter('dash') == "T")
                createQuickSearch(form);

            createSublists(form, customform);

            // var menuField = form.addField('custpage_menu', 'inlinehtml', '');
            // menuField.setDefaultValue('<div style="top: 5px; right: 5px; position: fixed; width: 150px; height: 100px; z-index:100">'+createSublistMenu()+'</div>');
            // menuField.setDisplaySize(200, 200);

            form.addButton('custpage_homebutton', 'Home', 'gotoDashboard()');

            if(accesslevel == 2)
            {
                form.addSubmitButton('Save');
            }
            else
            {
                if(checkAccess(id) == "2" && !request.getParameter('dash'))
                    form.addButton('custpage_editbutton', 'Edit', 'editCustomer()');
            }

            if (!request.getParameter('dash')) {
                form.addButton('custpage_mainrecordbutton', 'Back to Main Record', 'gotoMainRecord()');
            }

            response.writePage(form);
        }
        else
        {
            customform = request.getParameter('custpage_customform');
            recordId = request.getParameter('custpage_recordid');

            id = request.getParameter('custpage_employee');
            hash = request.getParameter('custpage_hash');

            sourceid = request.getParameter('custpage_sourceid');
            sourceform = request.getParameter('custpage_sourceform');

            recordId = request.getParameter('custpage_recordid');

            var record = getSampleRecord();

            try
            {
                var fields = JSON.parse(request.getParameter('custpage_touchedfields'));

                for (var i = 0; i < fields.length; i++) {
                    record.setFieldValue(fields[i].name.replace('custpage_', ''), fields[i].value);
                }

                recordId = nlapiSubmitRecord(record);

                var formUrl = nlapiResolveURL('SUITELET',
                    'customscript_in8_externalcustomer_sl',
                    'customdeploy_in8_externalcustomer_sl',
                    parseInt(nlapiGetContext().getUser()) < 0);

                formUrl += '&customform='+request.getParameter('custpage_customform')
                    +'&internalid='+request.getParameter('custpage_employee')
                    +'&hash='+request.getParameter('custpage_hash')
                    +'&recordid='+recordId
                    +'&sourceid='+sourceid
                    +'&sourceform='+sourceform;


                response.write('<script>window.location = "'+formUrl+'"</script>');

            }
            catch (e1) {
                throw nlapiCreateError('Submit record', 'Error saving record: ' + e1.message);
            }
        }
    } catch(e) {
        nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e) + ' ' + e.stack);

        var form = nlapiCreateForm("Error");
        form.setScript('customscript_in8_externalform_cs');
        var code = "UNEXPECTED_ERROR";
        var message = "";

        if(e instanceof nlobjError)
        {
            code = e.getCode();
            message = e.getDetails();
        }
        else
        {
            message = e.message;
        }

        var field = form.addField('custpage_message', 'inlinehtml', 'Message');
        field.setDefaultValue("<h2>Code: " + code +" <br/>Message: " + message + "</h2>");

        form.addButton('custpage_mainrecordbutton', 'Back to Main Record', 'gotoMainRecord()');

        response.writePage(form);

	}
}

function createFormField(form, fieldObj, value, tab, mandatory, readonly, text)
{
    if(fieldObj)
    {
        var label = fieldObj.label;

        try
        {
            if (readonly)
            {
                var newField = form.addField('custpage_' + fieldObj.name, 'longtext', label,null, tab);
                newField.setDisplayType('inline');
            }
            else {

                if (mandatory)
                    label += " (*)";

                if (fieldObj.type == 'poscurrency')
                    var newField = form.addField('custpage_' + fieldObj.name, 'currency', label, null, tab);
                if (fieldObj.type == 'datetime')
                    var newField = form.addField('custpage_' + fieldObj.name, 'datetimetz', label, null, tab);
                if (fieldObj.type == 'identifieranycase')
                    var newField = form.addField('custpage_' + fieldObj.name, 'text', label, null, tab);
                else {
                    try {
                        var newField = form.addField('custpage_' + fieldObj.name, fieldObj.getType(), label, null, tab);
                    } catch (e) {
                        var newField = form.addField('custpage_' + fieldObj.name, 'text', label, null, tab);
                    }
                }

                if (mandatory)
                    newField.setMandatory(true);

                if (fieldObj.type == "select" || fieldObj.type == "multiselect") {
                    var options = fieldObj.getSelectOptions();

                    if (options)
                    {
                        newField.addSelectOption('', '');

                        if((options.length > 100 || options.length == 0) &&
                            fieldObj.getName() != 'custevent3' &&
                            fieldObj.getName() != 'custevent5' &&
                            fieldObj.getName() != 'assigned' &&
                            fieldObj.getName() != 'custevent35' &&
                            fieldObj.getName() != 'custentity96' &&
                            fieldObj.getName() != 'custrecord38')
                        {
                            newField.addSelectOption('opensearch='+fieldObj.getName(), '******* Search *******');
                            newField.addSelectOption('showAll=T&opensearch='+fieldObj.getName(), '******* Show All *******');

                            if(value && text)
                            {
                                newField.addSelectOption(value,text);
                            }
                        }
                        else
                        {
                            for (var i = 0; i < options.length; i++)
                            {
                                newField.addSelectOption(options[i].getId(), options[i].getText());
                            }
                        }
                    }
                }
            }

            //nlapiLogExecution('DEBUG', 'Value', value);

            if (value)
                newField.setDefaultValue(value)

        }
        catch(e)
        {
            nlapiLogExecution('ERROR', 'Creating field', e.message);
        }
    }

    return form;
}

function getSampleRecord()
{
    var customForm = nlapiLoadRecord('customrecord_in8_customexternalform', customform);

    if(recordId == "new")
    {
        var record = nlapiCreateRecord(customForm.getFieldValue('custrecord_in8_cef_recordtype'));

        if(request.getParameter('sourcetype') && request.getParameter('sourceid'))
        {
            var linkField = nlapiLookupField('customrecord_in8_cef_sublists', request.getParameter('sourcetype'), 'custrecord_in8_cefs_parentfield');

            if(linkField)
            {
                if(customForm.getFieldValue('custrecord_in8_cef_recordtype') == "projectTask")
                {
                    var record = nlapiCreateRecord('projectTask', {company : request.getParameter('sourceid')});

                    record.setFieldValue('custevent5', id);

                }
                else
                {
                    record.setFieldValue(linkField, request.getParameter('sourceid'));

                    var field = {};
                    field.name = linkField;
                    field.value = request.getParameter('sourceid');

                    touchedFields.push(field);
                }
            }
        }

        if(sourceid && customForm.getFieldValue('custrecord_in8_cef_recordtype') == "projectTask")
        {
            //nlapiLogExecution('DEBUG', 'Create task', "Task created");
            var record = nlapiCreateRecord('projectTask', {company : sourceid});
            record.setFieldValue('custevent5', id);
        }

        return record;
    }
    else
        return nlapiLoadRecord(customForm.getFieldValue('custrecord_in8_cef_recordtype'), recordId);
}

function createBodyFields(form, customform)
{
    var record = getSampleRecord(customform);

    var userForms = getUserForms();

    form = createParameterField(form, 'custpage_touchedfields', JSON.stringify(touchedFields));
    form = createParameterField(form, 'custpage_recordid', recordId);
    form = createParameterField(form, 'custpage_customform', customform);
    form = createParameterField(form, 'custpage_employee', id);
    form = createParameterField(form, 'custpage_hash', hash);
    form = createParameterField(form, 'custpage_sourceid', sourceid);
    form = createParameterField(form, 'custpage_sourceform', sourceform);
    form = createParameterField(form, 'custpage_defaultopened', '');
    form = createParameterField(form, 'custpage_filters', request.getParameter('filters') ? JSON.parse(request.getParameter('filters')) : JSON.stringify(getStoredFilters()));
    form = createParameterField(form, 'custpage_history', nlapiGetContext().getSessionObject('random'));

    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_ceff_fieldid'));
    columns.push(new nlobjSearchColumn('custrecord_in8_ceff_readonly'));
    columns.push(new nlobjSearchColumn('custrecord_in8_ceff_mandatory'));
    columns.push(new nlobjSearchColumn('custrecord_in8_ceff_relatedrecord'));
    columns.push(new nlobjSearchColumn('custrecord_in8_ceff_tab').setSort(true));
    columns.push(new nlobjSearchColumn('custrecord_in8_ceff_order').setSort(false));

    var search = nlapiSearchRecord('customrecord_in8_cef_fields', null, new nlobjSearchFilter('custrecord_in8_ceff_externalform', null, 'anyof', customform), columns);

    var currentTab = '';
    var currentGroup = '';

    var tab = null;

    for(var i = 0; search != null && i < search.length; i++)
    {
        if(buildObjectName(currentTab) != buildObjectName(search[i].getValue('custrecord_in8_ceff_tab')) && search[i].getValue('custrecord_in8_ceff_tab'))
        {
            tab = form.addTab(buildObjectName(search[i].getValue('custrecord_in8_ceff_tab')), search[i].getValue('custrecord_in8_ceff_tab'));
            currentTab = search[i].getValue('custrecord_in8_ceff_tab');

            subtabs.push({ id: buildObjectName(search[i].getValue('custrecord_in8_ceff_tab')) , label: search[i].getValue('custrecord_in8_ceff_tab')});
        }

        // if(buildObjectName(currentGroup) != buildObjectName(search[i].getValue('custrecord_in8_ceff_group') && search[i].getValue('custrecord_in8_ceff_group')))
        // {
        //     form.addFieldGroup(buildObjectName(search[i].getValue('custrecord_in8_ceff_group')),search[i].getValue('custrecord_in8_ceff_group'), currentTab ? buildObjectName(currentTab) : null);
        //     currentGroup = search[i].getValue('custrecord_in8_ceff_group')
        // }

        var field = record.getField(search[i].getValue('custrecord_in8_ceff_fieldid'));

        var readOnly = true;
        var value = null;

        var touchField = touchedFields.filter(function(value){
            return value.name == search[i].getValue('custrecord_in8_ceff_fieldid');
        });

        //nlapiLogExecution('DEBUG', 'Touch', JSON.stringify(touchField));

        if(accesslevel == 2 &&
            search[i].getValue('custrecord_in8_ceff_readonly') == "F" &&
            touchField.length == 0)
        {
            readOnly = false;
            value = record.getFieldValue(search[i].getValue('custrecord_in8_ceff_fieldid'));
        }
        else
        {
            value = record.getFieldText(search[i].getValue('custrecord_in8_ceff_fieldid')) ? record.getFieldText(search[i].getValue('custrecord_in8_ceff_fieldid')) : record.getFieldValue(search[i].getValue('custrecord_in8_ceff_fieldid'));

            if(search[i].getValue('custrecord_in8_ceff_relatedrecord'))
            {
                var userForm = userForms.filter(function (result){
                    if(result.getValue('custrecord_in8_cef_recordtype', 'custrecord_in8_cfa_form') == search[i].getValue('custrecord_in8_ceff_relatedrecord'))
                        return result;
                })

                nlapiLogExecution('DEBUG', 'UserForm', JSON.stringify(userForm));

                if(userForm.length > 0)
                {
                    var formUrl = nlapiResolveURL('SUITELET',
                        'customscript_in8_externalcustomer_sl',
                        'customdeploy_in8_externalcustomer_sl',
                        parseInt(nlapiGetContext().getUser()) < 0) +
                        '&hash='+hash+
                        '&internalid='+id+
                        '&customform='+userForm[0].getValue('custrecord_in8_cfa_form')+
                        '&recordid='+record.getFieldValue(search[i].getValue('custrecord_in8_ceff_fieldid'));

                    value = '<a href=\''+formUrl+'\'>'+value+'</a>';
                }
            }
        }

        form = createFormField(form,
            field,
            value,
            search[i].getValue('custrecord_in8_ceff_tab') ? buildObjectName(search[i].getValue('custrecord_in8_ceff_tab')) : null,
            search[i].getValue('custrecord_in8_ceff_mandatory') == "T",
            readOnly,
            record.getFieldText(search[i].getValue('custrecord_in8_ceff_fieldid')));
    }

    if(search == null)
        form.addField('custpage_nofields', 'inlinehtml', '').setDefaultValue('There are no fields setup for this form.');

    return form;
}

function buildObjectName(label)
{
    var objectName = "custpage_" + label.replace(/[^a-zA-Z0-9_]+/g, '');

    objectName = objectName.toLowerCase();

    return objectName;
}

function createSublists(form, customform)
{
    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_cefs_savedsearch'));
    columns.push(new nlobjSearchColumn('custrecord_in8_cefs_parentfield'));
    columns.push(new nlobjSearchColumn('name'));
    columns.push(new nlobjSearchColumn('custrecord_in8_cefs_edit'));
    columns.push(new nlobjSearchColumn('custrecord_in8_cefs_create'));
    columns.push(new nlobjSearchColumn('custrecord_in8_cefs_opened'));

    var search = nlapiSearchRecord('customrecord_in8_cef_sublists', null, new nlobjSearchFilter('custrecord_in8_cefs_customform', null, 'anyof', customform), columns);

    var formUrl = nlapiResolveURL('SUITELET',
        'customscript_in8_externalcustomer_sl',
        'customdeploy_in8_externalcustomer_sl',
        parseInt(nlapiGetContext().getUser()) < 0);

    for(var i = 0; search && i < search.length; i++)
    {
        //nlapiLogExecution('DEBUG', 'Create Sublists', 'name: ' + search[i].getValue('name') );

        var newTab = form.addTab(buildObjectName('tab_'+search[i].getValue('name')),search[i].getValue('name'));

        subtabs.push({ id: buildObjectName('tab_'+search[i].getValue('name')) , label: search[i].getValue('name')});

        var sublistSearch = nlapiLoadSearch(null, search[i].getValue('custrecord_in8_cefs_savedsearch'));

        var sublistForm = getCustomForm(sublistSearch.getSearchType());

        if(sublistSearch.getSearchType() == "supportcase" && search[i].getValue('custrecord_in8_cefs_parentfield') == "company")
        {
            sublistSearch.addFilter(new nlobjSearchFilter('internalid', 'customer', 'anyof', recordId));
        }
        else if(sublistSearch.getSearchType() == "message")
        {
            sublistSearch.addFilter(new nlobjSearchFilter('internalid', search[i].getValue('custrecord_in8_cefs_parentfield'), 'anyof', recordId));
        }
        else if(sublistSearch.getSearchType() == "timebill" && search[i].getValue('custrecord_in8_cefs_parentfield') == "custevent_project_task")
        {
            var projectTaskId = nlapiLookupField('supportcase', recordId, 'custevent_project_task');

            if(projectTaskId)
                sublistSearch.addFilter(new nlobjSearchFilter('casetaskevent', null, 'anyof', projectTaskId));

            sublistSearch.addFilter(new nlobjSearchFilter('custcoltime_case_id', null, 'anyof', recordId));
        }
        else {
            if (search[i].getValue('custrecord_in8_cefs_parentfield')) {
                sublistSearch.addFilter(new nlobjSearchFilter(search[i].getValue('custrecord_in8_cefs_parentfield'), null, 'anyof', recordId));
            }
        }

        var sublist = form.addSubList(buildObjectName(search[i].getValue('name')), 'list', search[i].getValue('name'), buildObjectName('tab_'+search[i].getValue('name')));

        [form, sublist, sublistSearch] = addSublistFilters(form, search[i].getId(), sublist, sublistSearch, search[i].getValue('name'));

        if(search[i].getValue('custrecord_in8_cefs_parentfield') == "case" && sublistSearch.getSearchType() == "message")
            sublist.addButton('custpage_newmessage', 'New Message', 'newMessageForm()');

        //var field = form.addField('custpage_filter', 'inlinehtml', '', null, buildObjectName('tab_'+search[i].getValue('name')));
        //field.setDefaultValue('<div id="divFilter">Filter  Name: <input type="text" id="filterItemName" onblur="filterName()" value="' + (request.getParameter('filterItemName') ? request.getParameter('filterItemName') : '') + '"></div>');

        var pagesField = form.addField(buildObjectName('page_'+search[i].getValue('name')), 'select', '', null, buildObjectName('tab_'+search[i].getValue('name')));

        var pagesValues = getSearchPages(sublistSearch);

        for(var opt = 0; opt < pagesValues.length; opt++)
        {
            pagesField.addSelectOption(pagesValues[opt].id, pagesValues[opt].label);
        }

        var sublistURL = formUrl + '&hash='+hash+'&internalid='+id+'&sourceid='+recordId+'&sourceform='+customform+'&customform='+sublistForm+'&recordid=';

        var columns = sublistSearch.getColumns();

        sublist = createSublistFields(sublist, columns);

        if(search[i].getValue('custrecord_in8_cefs_create') == "T")
        {
            sublist.addButton('custpage_addrecord', 'Create', "createRecord('"+sublistURL+"new&accesslevel=2&sourceid="+recordId+"&sourcetype="+search[i].getId()+"');");
        }

        var line = 1;

        if(request.getParameter('machine') == buildObjectName(search[i].getValue('name')) && request.getParameter('page'))
            var page = request.getParameter('page');
        else
            var page = 1;

        pagesField.setDefaultValue(page);

        var summedValues = [];
        var hasSumFields = false;

        if((sublistSearch.getSearchType() != "customer" || sublistSearch.getFilters().length > 2) && recordId != "new")
        {
            var start = 50*(page-1);
            var end = 50*page;

            sublistSearch.runSearch().getResults(start, end).map(function (result) {

                var url = sublistURL + result.getId();
                var link = "<a href='#' onclick=\"window.location.href = '" + url + "'\">View</a>";

                if (search[i].getValue('custrecord_in8_cefs_edit') == "T") {
                    var url = sublistURL + result.getId() + '&accesslevel=2';
                    link += "| <a href='#' onclick=\"window.location.href = '" + url + "'\">Edit</a>";
                }

                if (sublistForm) {
                    sublist.setLineItemValue("custpage_link", line, link);
                }

                //nlapiLogExecution('DEBUG', 'Record Type', result.getRecordType());

                for (var c = 0; c < columns.length; c++) {
                    var value = "";

                    if(columns[c].getName() == "name" && columns[c].getJoin() == "file")
                    {
                        var link = "<a href='#' onclick=\"viewFile('" + result.getValue('internalid', 'file') + "')\">View</a>";
                        sublist.setLineItemValue("custpage_link", line, link);
                    }

                    if(result.getRecordType() == "projecttask" && columns[c].getName() == "title")
                    {
                        var titleArray = String(result.getValue(columns[c])).split(":");

                        if(titleArray.length > 1)
                            value = "&nbsp;&nbsp;&nbsp;&nbsp;" + titleArray[titleArray.length-1];
                        else
                            value = '<b><h1>' + titleArray[titleArray.length-1] + '</h1></b>';

                        //nlapiLogExecution('DEBUG', 'Title', 'Tiel');
                    }
                    else
                    {
                        if (result.getText(columns[c]))
                            value = String(result.getText(columns[c])).length > 300 ? String(stripHtml(result.getText(columns[c]))).substr(0, 300) : stripHtml(result.getText(columns[c]));
                        else
                            value = String(result.getValue(columns[c])).length > 300 ? String(stripHtml(result.getValue(columns[c]))).substr(0, 300) : stripHtml(result.getValue(columns[c]));
                    }
                    sublist.setLineItemValue("custpage_" + columns[c].getName(), line, value);

                    if (columns[c].getLabel().toLowerCase() == 'id' || columns[c].getName().toLowerCase() == 'internalid') {
                        try {
                            sublist.setLineItemValue("custpage_" + columns[c].getName() + "_n", line, value);
                        } catch(e) {
                            nlapiLogExecution('ERROR', 'Err', e);
                        }
                    }

                    if(columns[c].type == "currency" || columns[c].type == "float" || columns[c].type == "integer" || columns[c].name == "hours")
                    {
                        if(columns[c].name == "hours")
                            value = hoursToInt(value);

                        if(!isNaN(Number(value)))
                        {
                            if (summedValues["custpage_" + columns[c].getName()])
                                summedValues["custpage_" + columns[c].getName()] = parseFloat(summedValues["custpage_" + columns[c].getName()] || 0) + parseFloat(value || 0);
                            else
                                summedValues["custpage_" + columns[c].getName()] = parseFloat(value || 0);

                            hasSumFields = true;
                        }
                    }
                }

                if (sublistSearch.getId() == "1269") {
                    //nlapiLogExecution('DEBUG', 'Value', parseFloat(result.getValue('custentity145')));
                    var color = setPaceReportColor(parseFloat(result.getValue('custentity145')));

                    if (color)
                        sublist.setLineItemValue("custpage_linecolor", line, color);
                }

                line++;

                // if (line > maxResults) {
                //     return false;
                // }
                return true;
            });

            if(hasSumFields)
            {
                sublist.setLineItemValue('custpage_link', line, '<b>Totals</b>');
                for (var keys in summedValues)
                {
                    try {
                        if(keys == 'custpage_hours')
                            sublist.setLineItemValue(keys, line, intToHours(summedValues[keys]));
                        else
                            sublist.setLineItemValue(keys, line, summedValues[keys]);
                    } catch(e) {
                        // Added to resolve an error with Totals due to formatting
                    }
                }
            }
        }

        if(search[i].getValue('custrecord_in8_cefs_opened') == "T" ||
            (request.getParameter('companyname') && sublistSearch.getSearchType() == "customer"))
        {
            defaultOpen.push(buildObjectName('tab_' + search[i].getValue('name')));
        }

    }

    form.getField('custpage_defaultopened').setDefaultValue(JSON.stringify(defaultOpen));

    return form;
}

function createSublistFields(sublist, columns)
{
    sublist.addField('custpage_link', 'textarea', '');

    var hasAmountField = false;

    for(var i = 0; i < columns.length; i++)
    {
        try {

            if(columns[i].type == "date" || columns[i].type == "float" || columns[i].type == "currency") {
                var field = sublist.addField("custpage_"+columns[i].getName(), columns[i].type, columns[i].getLabel());
            } else if (columns[i].getLabel().toLowerCase() == 'id' || columns[i].getName().toLowerCase() == 'internalid') {
                var field = sublist.addField("custpage_"+columns[i].getName(), "text", columns[i].getLabel());
                field = sublist.addField("custpage_"+columns[i].getName()+ "_n", "integer", columns[i].getLabel() + ' (Number)');
            }
            //else if(columns[i].type == "datetime")
            //    var field = sublist.addField("custpage_"+columns[i].getName(), 'datetimetz', columns[i].getLabel());
            else {
                var field = sublist.addField("custpage_"+columns[i].getName(), "text", columns[i].getLabel());
            }

            if(columns[i].getName() == "title")
                field.setLabel("Name ______________________________");

                //nlapiLogExecution('DEBUG', 'Creating field ' + columns[i].getName(), columns[i].getLabel() + ' type: ' + columns[i].type);
        } catch(e) {
            nlapiLogExecution('ERROR', 'Creating field ' + columns[i].getName(), e);
        }
    }

    sublist.addField('custpage_linecolor', 'text', '').setDisplayType('hidden');

    return sublist;
}


function checkAccess(id) {

    var search = nlapiSearchRecord('employee', null, new nlobjSearchFilter('internalid', null, 'anyof', id), new nlobjSearchColumn('custentity_in8_employeeslaccess'));

    if(search)
    {
        return parseInt(search[0].getValue('custentity_in8_employeeslaccess'));
    }

    return 3;
}

function getCustomForm(recordType)
{
    var filters = [];
    filters.push(new nlobjSearchFilter('custrecord_in8_cfa_employee', null, 'anyof', id));
    filters.push(new nlobjSearchFilter('custrecord_in8_cef_recordtype', 'custrecord_in8_cfa_form', 'is', recordType));

    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_cfa_level').setSort(false));
    columns.push(new nlobjSearchColumn('custrecord_in8_cfa_form'));

    var search = nlapiSearchRecord('customrecord_in8_externalformaccess', null, filters, columns);

    if(search)
        return search[0].getValue('custrecord_in8_cfa_form');
    else
        return null;
}

function getUserForms()
{
    var filters = [];
    filters.push(new nlobjSearchFilter('custrecord_in8_cfa_employee', null, 'anyof', id));

    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_cfa_level').setSort(false));
    columns.push(new nlobjSearchColumn('custrecord_in8_cfa_form'));
    columns.push(new nlobjSearchColumn('custrecord_in8_cef_recordtype', 'custrecord_in8_cfa_form'));

    var search = nlapiSearchRecord('customrecord_in8_externalformaccess', null, filters, columns);

    if(search)
        return search;
    else
        return [];
}

function createSublistMenu()
{
    var html = "<table>";

    html += "<tr><td style=\"border: 1px solid;background-color: white; font-size: 12px;\"><a href=\"#\" onclick=\"window.scrollTo(0, 0)\">General</a></a></td></tr>";

    for(var i = 0; i < subtabs.length; i++)
    {
        html += "<tr><td style=\"border: 1px solid;background-color: white;font-size: 12px;\" onclick=\"jumpToTab('"+subtabs[i].id+"')\"><a href=\"#"+subtabs[i].id+"_pane_hd\">"+subtabs[i].label+"</a></td></tr>";
    }

    html += "</table>";

    return html;
}

function createParameterField(form, id, value)
{
    var hiddenField = form.addField(id, 'longtext', '');
    hiddenField.setDefaultValue(value);
    hiddenField.setDisplayType('hidden');

    return form;
}

function setPaceReportColor(value)
{
    if(value < -75)
    {
        return "#ffffff";
    } else if(value >= -75 && value < -50)
    {
        return "#ddedf5";
    } else if(value >= -50 && value < -25)
    {
        return "#9acee8";
    } else if(value >= -25 && value < 0)
    {
        return "#35ace7";
    } else if(value >= 0 && value < 10)
    {
        return "#b0d674";
    } else if(value >= 10 && value < 20)
    {
        return "#fde201";
    } else if(value >= 20)
    {
        return "#fd6001";
    }

    return null;
}

function addSublistFilters(form, sublistId, sublist, search, sublistName)
{
    var columns = [];
    columns.push(new nlobjSearchColumn('custrecord_in8_cefsf_type'));
    columns.push(new nlobjSearchColumn('custrecord_in8_cefsf_fieldid'));
    columns.push(new nlobjSearchColumn('name'));

    if(request.getParameter('filters'))
    {
        var filterData = JSON.parse(JSON.parse(request.getParameter('filters')));
        updateStoredFilters(filterData);
    }
    else
        var filterData = getStoredFilters();

    var searchFilters = nlapiSearchRecord('customrecord_in8_cefs_filters', null, new nlobjSearchFilter('custrecord_in8_cefsf_sublist', null, 'anyof', sublistId), columns);

    if(searchFilters)
    {
        nlapiLogExecution('DEBUG', 'Sublist name', sublistName);

        var baseRecord = nlapiCreateRecord(search.getSearchType());

        sublist.addButton(sublistName+"btn", 'Search', 'searchCustomer(\''+buildObjectName(sublistName)+'\')');

        for (var f = 0; searchFilters && f < searchFilters.length; f++)
        {
            var filterFieldId = buildObjectName('filter_'+sublistName+'_'+searchFilters[f].getValue('custrecord_in8_cefsf_fieldid'));

            if (searchFilters[f].getValue('custrecord_in8_cefsf_type') == "select")
            {
                var fieldObj = baseRecord.getField(searchFilters[f].getValue('custrecord_in8_cefsf_fieldid'));

                if (fieldObj && fieldObj.getType() == "select")
                {
                    var sublistFilter = form.addField(filterFieldId, "select", searchFilters[f].getValue('name'), null, buildObjectName('tab_'+sublistName));

                    var options = fieldObj.getSelectOptions();

                    if (options)
                    {
                        sublistFilter.addSelectOption('', '');

                        for (var i = 0; i < options.length; i++)
                        {
                            sublistFilter.addSelectOption(options[i].getId(), options[i].getText());
                        }
                    }

                    var value = filterData[filterFieldId];

                    if(value)
                    {
                        if(search.getSearchType() == "supportcase" && searchFilters[f].getValue('custrecord_in8_cefsf_fieldid') == "company")
                            search.addFilter(new nlobjSearchFilter('internalid', 'customer', 'anyof', value));
                        else
                            search.addFilter(new nlobjSearchFilter(searchFilters[f].getValue('custrecord_in8_cefsf_fieldid'), null, 'anyof', value));

                        sublistFilter.setDefaultValue(value);
                    }
                }
            }
            else if(searchFilters[f].getValue('custrecord_in8_cefsf_type') == "date")
            {
                var sublistFilter = form.addField(filterFieldId+"_start", "date", searchFilters[f].getValue('name') + " From", null, buildObjectName('tab_'+sublistName));
                var startDate = filterData[filterFieldId+"_start"];

                if(startDate)
                    sublistFilter.setDefaultValue(startDate);

                var sublistFilter = form.addField(filterFieldId+"_end", "date", searchFilters[f].getValue('name') + " To", null, buildObjectName('tab_'+sublistName));
                var endDate = filterData[filterFieldId+"_end"];

                if(endDate)
                    sublistFilter.setDefaultValue(endDate);

                if(startDate && endDate)
                {
                    search.addFilter(new nlobjSearchFilter(searchFilters[f].getValue('custrecord_in8_cefsf_fieldid'), null, 'within', startDate, endDate));
                }
                else if(startDate)
                {
                    search.addFilter(new nlobjSearchFilter(searchFilters[f].getValue('custrecord_in8_cefsf_fieldid'), null, 'onorafter', startDate));
                }
                else if(endDate)
                {
                    search.addFilter(new nlobjSearchFilter(searchFilters[f].getValue('custrecord_in8_cefsf_fieldid'), null, 'onorbefore', endDate));
                }
            }
            else
            {
                nlapiLogExecution('DEBUG', 'filterFieldId', filterFieldId);

                var sublistFilter = form.addField(filterFieldId, "text", searchFilters[f].getValue('name'), null, buildObjectName('tab_'+sublistName));

                var value = filterData[filterFieldId];

                if(value)
                {
                    search.addFilter(new nlobjSearchFilter(searchFilters[f].getValue('custrecord_in8_cefsf_fieldid'), null, 'contains', value));
                    sublistFilter.setDefaultValue(value);
                }
            }
        }
    }

    return [form, sublist, search];
}

function stripHtml (html)
{
    var ret = html.replace(/&gt;/g, '>');
    ret = ret.replace(/&lt;/g, '<');
    ret = ret.replace(/&quot;/g, '"');
    ret = ret.replace(/&apos;/g, "'");
    ret = ret.replace(/&amp;/g, '&');

    return ret.replace(/<(?:.|\n)*?>/gm, '');
}

function createQuickSearch(form)
{
    form.addTab("custpage_tabquichsearck", "Quick Search");

    var typeField = form.addField("custpage_quicksearchtype", 'select', 'Search for', null, "custpage_tabquichsearck");
    typeField.addSelectOption('1', 'Case Number');
    typeField.addSelectOption('2', 'Project Task (Internal ID)');
    typeField.addSelectOption('3', 'Customer ID');
    typeField.addSelectOption('4', 'Project ID');

    form.addField('custpage_quicksearchbtnfield','inlinehtml','',null, "custpage_tabquichsearck").setDefaultValue("<tr class=\"tabBnt\"> <td ><img src=\"/images/nav/ns_x.gif\" class=\"bntLT\" border=\"0\" height=\"50%\" width=\"10\" alt=\"\"> <img src=\"/images/nav/ns_x.gif\" class=\"bntLB\" border=\"0\" height=\"50%\" width=\"10\" alt=\"\"> </td> <td height=\"20\" valign=\"top\" nowrap=\"\" class=\"bntBgB\"> <input type=\"button\" style=\"box-sizing: border-box;font-family: Arial,Helvetica,sans-serif;background: none !important;margin: 0;border: 0;cursor: pointer;display: block;width: 100%;font-size: 14px !important;font-weight: 600 !important;height: 100% !important;padding: 0 12px !important;color: inherit !important;\" class=\"rndbuttoninpt bntBgT\" value=\"Search\" id=\"Quick Search Btn\" name=\"Quick Search Btn\" onclick=\"try{ if (!!window) { var origScriptIdForLogging = window.NLScriptIdForLogging; var origDeploymentIdForLogging = window.NLDeploymentIdForLogging; window.NLScriptIdForLogging = 'customscript_in8_externalcustomer_sl'; window.NLDeploymentIdForLogging = 'customdeploy_in8_externalcustomer_sl'; }quickSearch()}finally{ if (!!window) { window.NLScriptIdForLogging = origScriptIdForLogging; window.NLDeploymentIdForLogging = origDeploymentIdForLogging; }}; return false;\" onmousedown=\"this.setAttribute('_mousedown','T'); setButtonDown(true, true, this);\" onmouseup=\"this.setAttribute('_mousedown','F'); setButtonDown(false, true, this);\" onmouseout=\"if(this.getAttribute('_mousedown')=='T') setButtonDown(false, true, this);\" onmouseover=\"if(this.getAttribute('_mousedown')=='T') setButtonDown(true, true, this);\" _mousedown=\"F\"></td> <td id=\"tdrightcap_Quick Searchbtn\"> <img src=\"/images/nav/ns_x.gif\" height=\"50%\" class=\"bntRT\" border=\"1\" width=\"10\" alt=\"\"> <img src=\"/images/nav/ns_x.gif\" height=\"50%\" class=\"bntRB\" border=\"0\" width=\"10\" alt=\"\"> </td> </tr>");
    form.addField("custpage_quicksearchid", 'text', 'Search', null, "custpage_tabquichsearck");

}

function getSearchPages(search)
{

    var ret = [];
    var resultsQty = 0;
    var resultSet = search.runSearch();

    var start = 0;
    var end = 1000;

    do
    {
        var results = resultSet.getResults(start,end);

        resultsQty += parseInt(results.length);

        start += 1000;
        end += 1000;
    }
    while(results.length > 0 && end < 4000)

    var pages = parseInt(Math.floor(resultsQty / 50)) + 1;

    for(var i = 1; i <= pages; i++)
    {
        ret.push({
           id: i,
           label: "Page "+((i*50)-49)+" to " + (i*50)
        });
    }

    return ret
}
function updateStoredFilters(filters)
{
    var searchFilter = [];
    searchFilter.push(new nlobjSearchFilter('custrecord_in8_eff_employee', null, 'anyof', id));
    searchFilter.push(new nlobjSearchFilter('custrecord_in8_eff_form', null, 'anyof', customform));

    var searchFilters = nlapiSearchRecord('customrecord_in8_cef_employeefilters', null, searchFilter, null);

    if(searchFilters)
    {
        nlapiSubmitField('customrecord_in8_cef_employeefilters', searchFilters[0].getId(), 'custrecord_in8_eff_filters', JSON.stringify(filters));
    }
    else
    {
        var employeeFilter = nlapiCreateRecord('customrecord_in8_cef_employeefilters');
        employeeFilter.setFieldValue('custrecord_in8_eff_employee', id);
        employeeFilter.setFieldValue('custrecord_in8_eff_form', customform);
        employeeFilter.setFieldValue('custrecord_in8_eff_filters', JSON.stringify(filters));
        nlapiSubmitRecord(employeeFilter);
    }
}

function getStoredFilters()
{
    var searchFilter = [];
    searchFilter.push(new nlobjSearchFilter('custrecord_in8_eff_employee', null, 'anyof', id));
    searchFilter.push(new nlobjSearchFilter('custrecord_in8_eff_form', null, 'anyof', customform));

    var searchFilters = nlapiSearchRecord('customrecord_in8_cef_employeefilters', null, searchFilter, new nlobjSearchColumn('custrecord_in8_eff_filters'));

    if(searchFilters)
    {
        return JSON.parse(searchFilters[0].getValue('custrecord_in8_eff_filters'));
    }
    else
    {
        return {};
    }
}

function hoursToInt(hours)
{
    var time = hours.split(':');

    var ret = parseFloat(time[0] * 60) + parseFloat(time[1]);

    return ret;
}

function intToHours(minutes)
{
    var hours = parseInt(minutes / 60);

    minutes = Number(minutes % 60);

    return hours+":"+(minutes < 10 ? "0"+minutes : minutes);
}