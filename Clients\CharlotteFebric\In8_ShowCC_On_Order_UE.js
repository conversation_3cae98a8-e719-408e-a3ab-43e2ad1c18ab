var beforeSubmit = function() {
	try {
		if (['3', '4', '5', '6'].indexOf(nlapiGetFieldValue('paymentmethod')) < 0) {
			nlapiLogExecution('DEBUG', 'Payment method is ' + nlapiGetFieldValue('paymentmethod') + ' returning.');
			return;
		}

		if (!nlapiGetFieldValue('custbody_in8_wc_order_id')) {
			nlapiLogExecution('DEBUG', 'Not a WC order. Returning.');
			return;
		}
		
		var customerRec = nlapiLoadRecord('customer', nlapiGetFieldValue('entity'));
		var defaultCCId;
		var defaultCCNum;

		for (var i = 1; i <= customerRec.getLineItemCount('creditcards'); i++) {

			nlapiLogExecution('DEBUG', 'ccDefaualt', customerRec.getLineItemValue('creditcards', 'ccdefault', i));

			if (customerRec.getLineItemValue('creditcards', 'ccdefault', i) == 'T') {
				defaultCCId = customerRec.getLineItemValue('creditcards', 'internalid', i);
				defaultCCNum = customerRec.getLineItemValue('creditcards', 'ccnumber', i);
				break;
			}
		}

		nlapiLogExecution('DEBUG', 'defaultCCId', defaultCCId);
		nlapiLogExecution('DEBUG', 'defaultCCNum', defaultCCNum);

		if (defaultCCId)
			nlapiSetFieldValue('custbody_in8_cc_internalid', defaultCCId);
		
		if (defaultCCNum)
			nlapiSetFieldValue('custbody_in8_cc_digits', defaultCCNum);
	}
	catch (ex) {
		nlapiLogExecution('DEBUG', 'Exception', ex);
	}
}