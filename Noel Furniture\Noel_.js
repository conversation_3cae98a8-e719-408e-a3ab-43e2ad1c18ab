/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
 define(['N/currentRecord', 'N/url'], function(currentRecord, url) {

    function pageInit(context){
    }

    function redirectToPDF(str_type){
        rec = currentRecord.get();
        var outputUrl = url.resolveScript({
            scriptId: 'customscript_noel_render_label',
            deploymentId: 'customdeploy_noel_render_lbl_suitelet_de',
            params: {
                custpage_item_id: rec.id,
                custpage_print_type: str_type
            }
        });
        window.open(outputUrl);
    }    

    return {
        pageInit : pageInit,
        redirectToPDF : redirectToPDF
    }
});
