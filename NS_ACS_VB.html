<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

  <head>
    <#if .locale=="ru_RU">
      <link name="verdana" type="font" subtype="opentype" src="${nsfont.verdana}" src-bold="${nsfont.verdana_bold}" bytes="2" />
    </#if>
    <macrolist>
      <macro id="nlheader">
        <table class="header" style="width: 100%;">
          <tr>
            <td rowspan="3">
              <img src="https://926137-sb1.app.netsuite.com/core/media/media.nl?id=2398&amp;c=926137_SB1&amp;h=45a53b61751eca2fef90" width="117" height="70" style="float: left; margin: 7px;" />
            </td>
            <td align="right"><span class="title">${record@title}</span></td>
          </tr>
          <tr>
            <td align="right"><span class="number">#${record.tranid}</span></td>
          </tr>
          <tr>
            <td align="right">${record.trandate}</td>
          </tr>
        </table>
      </macro>
    </macrolist>
    <style type="text/css">
      table {
        <#if .locale=="zh_CN">font-family: stsong, sans-serif;
        <#elseif .locale=="zh_TW">font-family: msung, sans-serif;
        <#elseif .locale=="ja_JP">font-family: heiseimin, sans-serif;
        <#elseif .locale=="ko_KR">font-family: hygothic, sans-serif;
        <#elseif .locale=="ru_RU">font-family: verdana;
        <#else>font-family: sans-serif;
        </#if>font-size: 9pt;
        table-layout: fixed;
      }

      th {
        font-weight: bold;
        font-size: 8pt;
        vertical-align: middle;
        padding: 5px 6px 3px;
        background-color: #e3e3e3;
        color: #333333;
      }

      td {
        padding: 4px 6px;
      }

      b {
        font-weight: bold;
        color: #333333;
      }

      table.header td {
        padding: 0;
        font-size: 10pt;
      }

      table.footer td {
        padding: 0;
        font-size: 8pt;
      }

      table.itemtable th {
        padding-bottom: 10px;
        padding-top: 10px;
      }

      table.itemtable td {
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      table.total {
        page-break-inside: avoid;
      }

      tr.totalrow {
        background-color: #e3e3e3;
        line-height: 200%;
      }

      td.totalboxtop {
        font-size: 12pt;
        background-color: #e3e3e3;
      }

      td.addressheader {
        font-size: 8pt;
        padding-top: 6px;
        padding-bottom: 2px;
      }

      td.address {
        padding-top: 0;
      }

      td.totalboxmid {
        font-size: 28pt;
        padding-top: 20px;
        background-color: #e3e3e3;
      }

      td.totalboxbot {
        background-color: #e3e3e3;
        font-weight: bold;
      }

      span.title {
        font-size: 28pt;
      }

      span.number {
        font-size: 16pt;
      }

      span.itemname {
        font-weight: bold;
        line-height: 150%;
        text-align:left;
      }

      hr {
        width: 100%;
        color: #d3d3d3;
        background-color: #d3d3d3;
        height: 1px;
      }
    </style>
  </head>

  <body header="nlheader" header-height="10%" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
    <table style="width: 100%; margin-top: 10px;">
      <tr>
          <td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
          <td class="addressheader" colspan="3"></td>
      </tr>
      <tr>
          <td class="address" colspan="3" rowspan="2">${record.billaddress}</td>
          <td class="address" colspan="3" rowspan="2"></td>
      </tr>
    </table>
    <hr />
    <#if record.item?has_content>

      <table class="itemtable" style="width: 100%;">
        <!-- start items -->
        <#list record.item as item>
          <#if item_index==0>
            <thead>
              <tr>
                <th align="center" colspan="4"><b>ITEM</b></th>
              </tr>
              <tr>
                <th align="center" colspan="3">${item.quantity@label}</th>
                <th align="center" colspan="10">${item.item@label}</th>
                <th align="center" colspan="3">${item.rate@label}</th>
                <th align="center" colspan="3">${item.amount@label}</th>
                <th align="center" colspan="5">${item.department@label}</th>
                <th align="center" colspan="5">${item.class@label}</th>
              </tr>
            </thead>
          </#if>
          <tr>
            <td valign="middle" align="center" colspan="3" line-height="150%">${item.quantity}</td>
            <td valign="middle" align="left" colspan="10"><p style="text-align: left;"><span class="itemname">${item.item}</span><br />${item.description}</p></td>
            <td valign="middle" align="center" colspan="3">${item.rate}</td>
            <td valign="middle" align="center" colspan="3">${item.amount}</td>
            <td valign="middle" align="center" colspan="5">${item.department}</td>
            <td valign="middle" align="center" colspan="5">${item.class}</td>
          </tr>
        </#list><!-- end items -->
      </table>
    </#if>

    <#if record.expense?has_content>
      <table class="itemtable" style="width: 100%; table-layout: fixed;">
        <!-- start expenses -->
        <#list record.expense as expense>
          <#if expense_index==0>
            <thead>
              <tr>
                <th align="center" colspan="4"><b>EXPENSE</b></th>
              </tr>
              <tr>
                <th align="center" colspan="8">${expense.account@label}</th>
                <th align="center" colspan="3">${expense.amount@label}</th>
                <th align="center" colspan="4">${expense.department@label}</th>
                <th align="center" colspan="4">${expense.class@label}</th>
                <th align="center" colspan="6">${expense.memo@label}</th>
              </tr>
            </thead>
          </#if>
          <tr>
            <td valign="middle" align="left" colspan="8"><p style="text-align: left;"><span class="itemname">${expense.account}</span></p></td>
            <td valign="middle" align="center" colspan="3">${expense.amount}</td>
            <td valign="middle" align="center" colspan="4">${expense.department}</td>
            <td valign="middle" align="center" colspan="4">${expense.class}</td>
            <td valign="middle" align="left" colspan="6">${expense.memo}</td>
          </tr>
        </#list><!-- end expenses -->
      </table>
    </#if>

    <table style="width: 100%;">
      <tr>
        <td width="60%">

        </td>
        <td width="40%">
          <table class="itemtable" style="width: 100%;">    
            <thead>
              <tr>
                <th align="center" colspan="4"><b>TOTAL AMOUNT</b></th>
                <th align="center" colspan="4"><b>${record.total}</b></th>
              </tr>
            </thead>
          </table>
        </td>
      </tr>
    </table>

    <#if record["line"]?has_content>
      <table class="itemtable" style="width: 100%;">
        <!-- start line -->
        <#list record["line"] as linedata>
          <#if linedata_index==0>
            <thead>
              <tr>
                <th align="center" colspan="4"><b>LINE</b></th>
              </tr>
              <tr>
                <th colspan="8">Account</th>
                <th align="right">Debit</th>
                <th align="right">Credit</th>
              </tr>
            </thead>
          </#if>
          <tr>
            <td colspan="8">${linedata.account}</td>
            <td align="right">${linedata.debit}</td>
            <td align="right">${linedata.credit}</td>
          </tr>
        </#list><!-- end expenses -->
      </table>
    </#if>

    <hr />

  </body>
</pdf>
