/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define([], function() {

    function beforeLoad(context) {
        var getForm = context.form;
        getForm.clientScriptModulePath = 'SuiteScripts/ACS_PackingSlip_Redirect.js';
        getForm.addButton({
            id: 'custpage_print_packing_slip',
            label: 'Print as Packing Slip',
            functionName: 'redirectToPDF()'
        });
    }

    return {
        beforeLoad: beforeLoad
    }
});
