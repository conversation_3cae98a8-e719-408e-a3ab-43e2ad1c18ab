/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/render', 'N/record', 'N/format', 'N/search', 'N/file'], function(https, render, record, format, search, file) {

    function onRequest(context) {
        if(context.request.method == https.Method.GET){
            requestParams = context.request.parameters;
            var purchaseOrderID = parseInt(requestParams.custpage_POId);
            
            // retrieve the xml template file from file cabinet
            var xmlTemplateFile = file.load('SuiteScripts/ACS_PO_Receiving_label.xml');
            var renderer = render.create();

            // insert the template file content to the renderer
            renderer.templateContent = xmlTemplateFile.getContents();

            // ADD THE RECORD TO BE USED BASED ON PARAMETERS GIVEN
            var recObj = record.load({
                type: record.Type.PURCHASE_ORDER,
                id: purchaseOrderID,
                isDynamic: true
            });

            renderer.addRecord({
                templateName: 'record',
                record: recObj
            });
            
            renderer.addCustomDataSource({
                format: render.DataSource.OBJECT,
                alias: "date",
                data: { datenow: format.format({ value: new Date(), type: format.Type.DATE })}
            });

            
            var itemCount = recObj.getLineCount({
                sublistId: 'item'
            });

            var items = [];
            for(var x = 0; x < itemCount; x++) {

                var itemIntID = recObj.getSublistValue({
                    sublistId: 'item',
                    fieldId: 'item',
                    line: x
                });
                
                var customerText = recObj.getSublistText({
                    sublistId: 'item',
                    fieldId: 'customer',
                    line: x
                });

                var itemLookUp = search.lookupFields({
                    type: search.Type.ITEM,
                    id: itemIntID,
                    columns: [
                        'displayname',
                        'itemid',
                        'salesdescription'
                    ]
                });

                var item = {
                    displayname: itemLookUp.displayname,
                    itemid: itemLookUp.itemid,
                    salesdescription: itemLookUp.salesdescription.replace('&', '&#38;'),
                    customer: customerText.replace('&', '&#38;')
                }

                items.push(item);

            }
            
            renderer.addCustomDataSource({
                format: render.DataSource.OBJECT,
                alias: "JSON",
                data: { items: items }
            });


            // RENDER AS STRING
            var packingSlipXML = renderer.renderAsString();

            log.debug(packingSlipXML);

            // SEND RESPONSE XML TO RENDER AS PDF
            context.response.renderPdf({
                xmlString: packingSlipXML
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
