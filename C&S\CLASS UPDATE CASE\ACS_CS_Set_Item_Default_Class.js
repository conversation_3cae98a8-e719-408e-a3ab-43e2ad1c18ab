/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define([], function() {

    function pageInit(context) {
        
    }

    function postSourcing(context) {
        if(context.fieldId == 'item') {
            
            var poObj = context.currentRecord;

            var itemClass = poObj.getCurrentSublistValue({
                sublistId: context.sublistId,
                fieldId: 'class'
            });

            if(itemClass == ''){
                poObj.setCurrentSublistValue({
                    sublistId: context.sublistId,
                    fieldId: 'class',
                    value: 367,
                    ignoreFieldChange: true
                });
            }
        }
    }

    return {
        pageInit: pageInit,
        postSourcing: postSourcing
    }
});
