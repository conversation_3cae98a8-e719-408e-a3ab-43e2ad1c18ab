/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/record', 'N/redirect'], function(record, redirect) {

    function onRequest(context) {
        var parentRecordId = context.request.parameters.parent_item;

        redirect.toRecord({
            type: record.Type.INVENTORY_ITEM,
            id: parentRecordId,
            isEditMode: true,
            parameters: {
                'cp' : 'T',
                'parentRecord': parentRecordId
            }
        });

    }

    return {
        onRequest: onRequest
    }
});
