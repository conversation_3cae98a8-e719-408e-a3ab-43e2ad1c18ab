/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', './crypto/crypto-js.js', 'N/workflow', 'N/record', 'N/file', 'N/url', 'N/runtime', 'N/format'], 
        function(https, crypto, workflow, record, file, url, runtime, format) {

    /**
     *
     * loads the content the pass phrase file.
     * 
     */
    function getPassphrase() {
        var pFile = file.load('SuiteScripts/pp.txt')
        return pFile.getContents();
    }

    /**
     * 
     * @param {string} email - decrypted email of the approver
     * @param {object} poObj - object of the bill
     * 
     * confirms the identity of the approver.
     * current approver in purchase order must be the same as the approver email attached to the URL of the received email
     * 
     */
    function confirmApprover(email) {
        

        var approverFile = file.load('SuiteScripts/approver.txt')

        var approverEmail = approverFile.getContents();
        return approverEmail == email;
    }

    /**
     * 
     * @param {int} poId - id of the bill
     * @param {string} actionId - id of the workflow action to be triggered
     * 
     * triggers the action based on the attached action id on URL of the received email
     * 
     */
    function triggerAction(poId, actionId) {

        workflow.trigger({
            recordType: record.Type.PURCHASE_ORDER,
            recordId: poId,
            workflowId: 'customworkflow14',
            actionId: actionId
        });
        return true;
    }

    /**
     * 
     * @param {object} errorObj - error object from a catch
     * @param {object} customError - custom error object
     * 
     */
    function renderError(context, errorObj, customError) {

        if(customError !== null)
            errorObj = customError;

        log.error('Error: ' + errorObj.name , errorObj.message);

        var htmlFile = file.load('SuiteScripts/ACS_Error_HTML.html')

        var html = htmlFile.getContents();

        html = html.replace("{{ERROR_NAME}}", errorObj.name);
        html = html.replace("{{ERROR_MESSAGE}}", errorObj.message);

        context.response.write(html);

    }

    function renderPage(context, poObj, action, type, email) {

        var htmlFile = file.load('SuiteScripts/ACS_APPROVAL_PROCESS.html')

        var html = htmlFile.getContents();

        var appProcessSuitelet = url.resolveScript({
            scriptId: "customscript_acs_sl_po_approval",
            deploymentId: 'customdeploy_acs_sl_po_approval',
            returnExternalUrl: true
        });

        var tranNo = poObj.getValue({ fieldId: "transactionnumber"});

        html = html.replace("{{SUITELET_URL}}", appProcessSuitelet);
        html = html.replace("{{PO_ID}}", poObj.id);
        html = html.replace("{{TRANSACTION_NO}}", tranNo);
        html = html.replace("{{ACTION_ID}}", action);
        html = html.replace("{{EMAIL}}", email);
        html = html.replace("{{ACTION_TYPE}}", type);
        html = html.replace("{{ACTION_TYPE}}", type);
        html = html.replace("{{ACTION_TYPE}}", type);

        context.response.write(html);
    }

    function renderSuccess(context, poObj, status) {

        var htmlFile = file.load('SuiteScripts/ACS_Success_HTML.html')

        var html = htmlFile.getContents();

        var tranNo = poObj.getValue({ fieldId: "transactionnumber"});

        html = html.replace("{{TRANSACTION_NO}}", tranNo);
        html = html.replace("{{STATUS}}", status);
        html = html.replace("{{STATUS}}", status);

        context.response.write(html);

    }

    function onRequest(context) {

        if(context.request.method == https.Method.GET){

            var PP = getPassphrase();

            var encEmail = decodeURIComponent(context.request.parameters.em); // encoded email
            var encRec = decodeURIComponent(context.request.parameters.re); // encoded record id
            var encApprovalType = decodeURIComponent(context.request.parameters.apt); // encoded approval type
            var encAction = decodeURIComponent(context.request.parameters.ac); // encoded workflow action

            var email = crypto.AES.decrypt(encEmail, PP).toString(crypto.enc.Utf8);
            var recId = crypto.AES.decrypt(encRec, PP).toString(crypto.enc.Utf8);
            var approvalType = crypto.AES.decrypt(encApprovalType, PP).toString(crypto.enc.Utf8).toUpperCase();
            var action = crypto.AES.decrypt(encAction, PP).toString(crypto.enc.Utf8);
            var poObj = record.load({
                type: record.Type.PURCHASE_ORDER,
                id: recId
            });
            var appStatus = poObj.getValue({ fieldId: 'approvalstatus' });
            log.debug('test', runtime.getCurrentUser().role);

            var approverConfirmed = false;

            approverConfirmed = confirmApprover(email);
            
            if(appStatus == 1){
                if(approverConfirmed) {

                    if(approvalType == 'APPROVE' || approvalType == 'REJECT') {

                        try {
                            renderPage(context, poObj, action, approvalType, email);
                        } catch (err) {
                            renderError(context, err, null);
                        }

                    } else { 

                        renderError(context, null, {
                            name: "Wrong approval type",
                            message: "Wrong approval type. Please contact your administrator!"
                        });
                        
                    }

                } else {

                    renderError(context, null, {
                        name: "Wrong approver!",
                        message: "You are not the approver of this purchase order."
                    });
                    
                } // else of if(confirmApprover(email, poObj)) {
            } else if (appStatus == 2) {
                renderError(context, null, {
                    name: "Purchase order already approved",
                    message: "This purchase order has already been approved."
                });
            } else {
                renderError(context, null, {
                    name: "Purchase order already rejected",
                    message: "This purchase order has already been rejected."
                });
            }

        } else {
            
            var requestParams = context.request.parameters;
            var recId = requestParams.po_id;
            var actionId = requestParams.action_id;
            var actionType = requestParams.action_type;
            var email = requestParams.email;
            var note = requestParams.note;

            try {

                var poObj = record.load({
                    type: record.Type.PURCHASE_ORDER,
                    id: recId
                });

                // var memo = poObj.getValue({ fieldId: 'memo' });

                // var datetime = format.format({
                //     value: new Date(),
                //     type: format.Type.DATETIME
                // });

                // var nnote = ' : ' + note;

                // memo += '\r\n (' + actionType + ' @ ' + datetime + ' by ' + email + ')' + nnote;

                // poObj.setValue({ fieldId: 'memo', value: memo });
                // poObj.save();
                
                triggerAction(poObj.id, actionId);
                renderSuccess(context, poObj, ((actionType == 'APPROVE') ? 'approved' : 'rejected'));

            } catch (err) {
                renderError(context, err, null);
                log.debug('error', err);
            }
        }
    }

    return {
        onRequest: onRequest
    }
});
