/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', './crypto/crypto-js.js', 'N/file', 'N/format'], function(search, crypto, file, format) {

    function getInputData() {
        var searchObj = search.create({
            type: "vendorbill",
            filters:
            [
               ["type","anyof","VendBill"], 
               "AND", 
               ["mainline","is","T"], 
               "AND", 
               ["trandate","within","thismonth"]
            ],
            columns:
            [
               search.createColumn({name: "transactionnumber", label: "Transaction Number"})
            ]
         });

        var pagedData = searchObj.runPaged({ pageSize: 1 });
        var resultArray = [];
        // iterate the pages
        pagedData.pageRanges.forEach(function(pageRange){

    
            // fetch the current page data
            var currentPage = pagedData.fetch({index: pageRange.index});
    
            // and forEach() thru all results
            currentPage.data.forEach( function(result) {
    
                resultArray.push({
                    tranid: result.getValue('transactionnumber')
                });
    
            });
    
        });
        

        return resultArray;
    }

    function map(context) {
        // accept data from previous stage
        var valueFromInput = context.value;

        var vbTranObj = JSON.parse(valueFromInput);
        try {

            context.write({
                key: 1,
                value: crypto.AES.encrypt(vbTranObj.tranid.toString(), "z1x2c3v q7w8e9r1t").toString()
            })

        } catch (errorObj) {
            log.debug({
                title: 'error 1',
                details: errorObj
            });
        }
    }

    function reduce(context) {
        var reduceValues = context.values;

        var concatString = "";
        try {
            for(var y = 0; y < reduceValues.length; y++){
                concatString = concatString + reduceValues[y] + " ";
            }

            // concatString.trim();
            var name = format.format({
                value: new Date(),
                type: format.Type.DATE
            });
            name = name + '.txt';
            var fileObj = file.create({
                name: name,
                fileType: file.Type.PLAINTEXT,
                contents: concatString.trim(),
                description: 'phrase',
                encoding: file.Encoding.UTF8,
                folder: 1117,
                isOnline: false
            });

            var fileId = fileObj.save();

            if(fileId) {
                log.debug("Success", "Successfully created file!");
            } else {
                log.error("Error", "File was not created");
            }

        } catch (e) {
            log.error("Error", e);
        }

    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
