/**
 * @NApiVersion 2.x
 * @NScriptType Suitelet
 */
define(['N/ui/serverWidget', 'N/runtime', 'N/crypto', 'N/encode', 'N/sftp', 'N/file'], function(ui, runtime, crypto, encode, sftp, file) {
    function onRequest(option) {
        if (option.request.method === 'GET') {
            var form = ui.createForm({
                title: 'SFTP Uploader'
            });
			var uploadFile = form.addField({
				id: 'custpage_file',
				type: ui.FieldType.FILE,
				label: "File Upload"
			});
           /* var skField = form.addSecretKeyField({
                id: 'mycredential',
                label: 'Credential',
                restrictToScriptIds: [runtime.getCurrentScript().id],
                restrictToCurrentUser: false
            })
			
            skField.maxLength = 200;
			skField.defaultValue = "$378#ZRlWg"*/
			
            form.addSubmitButton();

            option.response.writePage(form);
        } else {
            var form = ui.createForm({
                title: 'SFTP Uploader'
            });

            var inputString = "YWJjZGVmZwo=";
            var myGuid = option.request.parameters.mycredential;

            // Create the key
           /* var sKey = crypto.createSecretKey({
                guid: myGuid,
                encoding: encode.Encoding.UTF_8
            });

                var hmacSha512 = crypto.createHmac({
                    algorithm: 'SHA512',
                    key: sKey
                });
                hmacSha512.update({
                    input: inputString,
                    inputEncoding: encode.Encoding.BASE_64
                });
                var digestSha512 = hmacSha512.digest({
                    outputEncoding: encode.Encoding.HEX
                });
           

            var digestField = form.addField({
                id: 'result',
                label: 'Your digested hash value',
                type: 'textarea'
            }).defaultValue = digestSha512;
			log.debug("digest Field:", JSON.stringify(digestField))
            */
           /*var base64EncodedString = encode.convert({
                string: "f3:b1:cd:13:00:e5:08:c6:f8:32:da:d3:72:2f:56:9d",
                inputEncoding: encode.Encoding.UTF_8,
                outputEncoding: encode.Encoding.BASE_64
            });
          var Base64 = {

              // private property
              _keyStr : "f3:b1:cd:13:00:e5:08:c6:f8:32:da:d3:72:2f:56:9d",

              // public method for encoding
              encode : function (input) {
                  var output = "";
                  var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
                  var i = 0;

                  input = Base64._utf8_encode(input);

                  while (i < input.length) {

                      chr1 = input.charCodeAt(i++);
                      chr2 = input.charCodeAt(i++);
                      chr3 = input.charCodeAt(i++);

                      enc1 = chr1 >> 2;
                      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
                      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
                      enc4 = chr3 & 63;

                      if (isNaN(chr2)) {
                          enc3 = enc4 = 64;
                      } else if (isNaN(chr3)) {
                          enc4 = 64;
                      }

                      output = output +
                      this._keyStr.charAt(enc1) + this._keyStr.charAt(enc2) +
                      this._keyStr.charAt(enc3) + this._keyStr.charAt(enc4);
                  }
                  return output;
              },

              // public method for decoding
              decode : function (input) {
                  var output = "";
                  var chr1, chr2, chr3;
                  var enc1, enc2, enc3, enc4;
                  var i = 0;

                  input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");

                  while (i < input.length) {

                      enc1 = this._keyStr.indexOf(input.charAt(i++));
                      enc2 = this._keyStr.indexOf(input.charAt(i++));
                      enc3 = this._keyStr.indexOf(input.charAt(i++));
                      enc4 = this._keyStr.indexOf(input.charAt(i++));

                      chr1 = (enc1 << 2) | (enc2 >> 4);
                      chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
                      chr3 = ((enc3 & 3) << 6) | enc4;

                      output = output + String.fromCharCode(chr1);

                      if (enc3 != 64) {
                          output = output + String.fromCharCode(chr2);
                      }
                      if (enc4 != 64) {
                          output = output + String.fromCharCode(chr3);
                      }
                  }

                  output = Base64._utf8_decode(output);

                  return output;
              },

              // private method for UTF-8 encoding
              _utf8_encode : function (string) {
                  string = string.replace(/\r\n/g,"\n");
                  var utftext = "";

                  for (var n = 0; n < string.length; n++) {

                      var c = string.charCodeAt(n);

                      if (c < 128) {
                          utftext += String.fromCharCode(c);
                      }
                      else if((c > 127) && (c < 2048)) {
                          utftext += String.fromCharCode((c >> 6) | 192);
                          utftext += String.fromCharCode((c & 63) | 128);
                      }
                      else {
                          utftext += String.fromCharCode((c >> 12) | 224);
                          utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                          utftext += String.fromCharCode((c & 63) | 128);
                      }
                  }
                  return utftext;
              },

              // private method for UTF-8 decoding
              _utf8_decode : function (utftext) {
                  var string = "";
                  var i = 0;
                  var c = c1 = c2 = 0;

                  while ( i < utftext.length ) {

                      c = utftext.charCodeAt(i);

                      if (c < 128) {
                          string += String.fromCharCode(c);
                          i++;
                      }
                      else if((c > 191) && (c < 224)) {
                          c2 = utftext.charCodeAt(i+1);
                          string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                          i += 2;
                      }
                      else {
                          c2 = utftext.charCodeAt(i+1);
                          c3 = utftext.charCodeAt(i+2);
                          string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                          i += 3;
                      }
                  }
                  return string;
              }
          }*/
			var fil = option.request.files.custpage_file;
			var fileObj = file.create({
				name: fil.name,
				fileType: fil.fileType,
				contents: fil.getContents()
			});
			fileObj.folder = 100781;
			var fileId = fileObj.save();
          	
			var sftUrl = "ftp.theroi.com";
          
          
			var connection = sftp.createConnection({
                username : '<EMAIL>',
                //passwordGuid : digestField,
                keyId: "custkey_theroi_test_key",
                url : sftUrl,
                port : 22,
                //directory : "/",
				hostKey: "AAAAB3NzaC1yc2EAAAADAQABAAACAQC2Tftx5KMephIFfFr3443/4Yv8BqygdzI+4GSIAhG0bZki12Y8a8slGSwdUTxemMVqxGBo/OkrJSN7YLkksl5zFVxJvv+auClP+L7GcnoNj5pGLB9Qzy6zJVNvD9Jre26lVRIxpSyEALGTdEDbNcmYSmDRBHkAfRZaxmOcsfljlnyQeWXUcFIPQAVGBFOOO+tMOOHcOHi+zRZ+b42ZKL9i3NhEGY30vkxeUD8Ki61sfsCVOzzKt/eq2MPcdJAYZGxPTOXTlPWQxxSLBSK5FM4lwMPI8O7BPwMpFMhjyUAsFHXP3JRlL6wnikdAEkokvpL+DTJI0CdzlZbcG+4/faOPtIA/oTRydfURl8tei5/FroGmVGcb5PsdF/4FyE1Zxlwwp/VHUjuwlQAH/D8OxYcJ39/ZYxAVk16dx1W4Ie5s/3BhJJf8jylErdqa9L024sRIGbIKWIfE6WYoUBO3Lb3IVTC/7bjjpX6Lu911p/lPSnuR+gfgfWVbfjLJZC9sEeVD+53LXVUS3lwrT5WbCw3jK4FowNIqXyNjByY8m96QDQsOn62UdXYJq8h35aL3PIaDkZYcLgEe9pWxd54vDtkh4bvxG2akniqw49AnLhzfnTKPXjsP1I8LfFF1xSmnyqjU+ykiiPWrRv1xq65Eo3kVVYWl5eEQR1yND1fk2q2HfQ=="
			});
			connection.upload({
				directory : '/',
				filename : fil.name,
				file : fil,
				replaceExisting : true
			});
            option.response.writePage(form);
        }
    }
    return {
        onRequest: onRequest
    };
});