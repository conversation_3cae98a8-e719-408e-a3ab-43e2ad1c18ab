{
    header: {
        ERPTransactionID: "ERPTransactionID",
        DocumentType: "DocumentType",
        DateTimeIssued: "DateTimeIssued",
        CustomerType: "CustomerType",
        CustomerName: "CustomerName",
        CustomerTaxId: "CustomerTaxId",
        CustomerAddress: "CustomerAddress",
        CountryName: "CountryName",
        TransactionDescription: "TransactionDescription",
        ERPCurrencyCode: "ERPCurrencyCode",
        ExchangeRate: "ExchangeRate"
    },
    lines: [{
            ERPTransactionLineID: "",
            ERPItemName: "ERPItemName",
            ItemCode: "ItemCode",
            ERPItemDesc: "ERPItemDesc",
            Quantity: "Quantity",
            Rate: "Rate",
            Amount: "Amount",
            VATAmount: "",
            GrossAmount: "",
            WHTAmount: ""
        },
        {
            ERPTransactionLineID: "",
            ERPItemName: "ERPItemName",
            ItemCode: "ItemCode",
            ERPItemDesc: "ERPItemDesc",
            Quantity: "Quantity",
            Rate: "Rate",
            Amount: "Amount",
            VATAmount: "",
            GrossAmount: "",
            WHTAmount: ""
        }
    ]
}