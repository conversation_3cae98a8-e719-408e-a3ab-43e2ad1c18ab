/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search'], function(record, search) {

    function getPid() {
        var searchObj = search.load({
            id: 'customsearch_acs_document_search'
        });
        var searchResult = searchObj.run().getRange({
            start: 0,
            end: 1
        });
        for (var i = 0; i < 1; i++) {
            return searchResult[i].getValue({ name: 'internalid' });
        }

        return false;
    }

    function beforeLoad(context) {
        if(context.type == context.UserEventType.VIEW) {
            var recObj = record.load({
                type: record.Type.VENDOR_BILL,
                id: context.newRecord.id
            });

            var pid = getPid();

            if(pid){
                recObj.setValue({ fieldId: 'custbody_pid', value: pid });
                var id = recObj.save();
                if(id) {
                    log.debug("Success", "Successfully saved pid");
                }
            } else {
                log.debug('Error', "No pid found");
            }
        }
    }

    function afterSubmit(context) {
        if(context.type == context.UserEventType.CREATE || ) {
            var recObj = record.load({
                type: record.Type.VENDOR_BILL,
                id: context.newRecord.id
            });

            var pid = getPid();

            if(pid){
                recObj.setValue({ fieldId: 'custbody_pid', value: pid });
                var id = recObj.save();
                if(id) {
                    log.debug("Success", "Successfully saved pid");
                }
            } else {
                log.debug('Error', "No pid found");
            }
        }
    }

    return {
        beforeLoad: beforeLoad,
        afterSubmit: afterSubmit
    }
});
