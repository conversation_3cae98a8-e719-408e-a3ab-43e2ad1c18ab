/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord'], function(currentRecord) {

    function pageInit(context) {
        
    }
    
    function unpopulatePickTicketQuantity () {

        var recObj = currentRecord.get();

        var lineCount = recObj.getLineCount({
            sublistId: 'item'
        });
            
        for(var i = 0; i < lineCount; i++) {
                
            recObj.selectLine({
                sublistId: 'item',
                line: i
            });

            recObj.setCurrentSublistValue({
                sublistId: 'item',
                fieldId: 'custcol_pick_ticket_qty',
                value: '',
                ignoreFieldChange: true
            });

            if(i != (lineCount - 1))
                recObj.commitLine({ sublistId: 'item' });
        }
    }

    return {
        pageInit: pageInit,
        unpopulatePickTicketQuantity : unpopulatePickTicketQuantity
    }
});
