/**
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
define(['N/sftp', 'N/file', 'N/runtime'], function (sftp, file, runtime) {

    function execute(context) {
        try {
            //Username: miesftp
            //Password: KtVCE&k5S3!p58Q
            var pwGuid = runtime.getCurrentScript().getParameter('custscript_pw_guid');
            var hostKey = runtime.getCurrentScript().getParameter('custscript_host_key');
            var connection = sftp.createConnection({
                username: 'miesftp',
                passwordGuid: pwGuid, // references var myPwdGuid
                url: 'sftp://sftp.intelioum.systems',
                hostKey: hostKey // references var myHostKey
            });

            var downloadedFile = connection.download({
                filename: runtime.getCurrentScript().getParameter('custscript_filename')
            });

            var scriptTask = task.create({
                taskType: task.TaskType.CSV_IMPORT
            });
            scriptTask.mappingId = runtime.getCurrentScript().getParameter('custscript_csv_mapping');
            scriptTask.importFile = downloadedFile;
            var csvImportTaskId = scriptTask.submit();
            log.audit('Successful CSV Import', 'csvImportTaskId: ' + csvImportTaskId);
        } catch (error) {
            log.error('Error on Scheduled Script', error.name + ': ' + error.message);
        }
    }

    return {
        execute: execute
    }
});