/**
 * Copyright (c) 2017, 2020, Oracle and/or its affiliates.
 *
 * @NApiVersion 2.x
 * @NScriptType usereventscript
 */
define(['../../dao/WD_SAP_DAO_MirrorApproval',
        '../../dao/WD_SAP_DAO_ApprovalHistory',
        '../../dao/WD_SAP_DAO_Employee',
        '../../lib/WD_SAP_WorkflowWrapper',
        '../../lib/WD_SAP_TaskWrapper',
        '../../lib/WD_SAP_RuntimeWrapper',
        '../../lib/WD_SAP_ServerWidgetWrapper',
        '../../lib/WD_SAP_URLWrapper',
        '../../lib/WD_SAP_Constants',
        '../../lib/WD_SAP_ScriptHelper',
        '../../lib/WD_SAP_PreferencesLib',
        '../../localization/WD_SAP_Dictionary'],
function(MirrorApprovalDAO,
         ApprovalHistoryDAO,
         EmployeeDAO,
         workflow,
         task,
         runtime,
         serverWidget,
         url,
         Constants,
         Helper,
         PreferencesLib,
         Dictionary) {
    var dictionary;
    var approvalPrefStatus;
    var mirrorApproval;
    var mappingSetup;
    var newRecord;

    const formComps = Constants.ReferenceRecordComponents;
    const approvalStatusOptions = formComps.ApprovalStatus;
    const approvalGroup = formComps.ApprovalGroup;
    const approvalFields = formComps.Fields;
    const approvalButtons = formComps.Buttons;
    const scripts = formComps.Scripts;

    const ADMIN_ROLE = 3;
    const FULL_ACCESS_ROLE = 18;

    const APPROVAL_WORKFLOW_ID = 'customworkflow_sas_approval_workflow';
    const GET_APPROVAL_RULE_BTN = 'workflowaction_sas_get_rule';
    const PENDING_APPROVAL_CANCEL_BTN = 'workflowaction_sas_pendingapp_cancelbtn';
    const REJECTED_CANCEL_BTN = 'workflowaction_sas_rejected_cancelbtn';

    const MIRROR_APPROVAL_ID = 'customrecord_sas_mirror_approval';
    const APPROVAL_HISTORY_ID = 'customrecord_sas_approval_history';
    const EMAIL_APPROVAL_LOG_ID = 'customrecord_sas_approval_logs';

    const SALES_ORDER_ID = 'salesorder';
    const ORDER_STATUS_FIELD = 'orderstatus';
    const APPROVE_ORDER_BUTTON = 'approve';
    const CANCEL_ORDER_BUTTON = 'cancelorder';

    function beforeLoad(scriptContext) {
        if (!initialize(scriptContext)) {
            return;
        }

        const form = scriptContext.form;
        const isViewMode = scriptContext.type == scriptContext.UserEventType.VIEW;
        const isCreateMode = scriptContext.type == scriptContext.UserEventType.CREATE;
        const isCopyMode = scriptContext.type == scriptContext.UserEventType.COPY;

        if (runtime.getExecutionContext() == runtime.getContextType().USER_INTERFACE) {
            if (scriptContext.type == scriptContext.UserEventType.EDIT) {
                validateRecordIsEditable();
            }

            form.addFieldGroup({
                id: approvalGroup.id,
                label: dictionary.get(approvalGroup.label)
            });

            addApprovalFields(form, newRecord, isViewMode);
            showNextApprovers(scriptContext);
            disableParentRecordApprovalFields(newRecord.type, form, isCreateMode, isCopyMode);
            initializeSalesOrderForm(newRecord.type, form);
            addApprovalButtons(form, newRecord, isViewMode);
            addHistoryTab(form, newRecord, isViewMode);
            addVBExceptionButton(form, isViewMode);
            form.clientScriptModulePath = scripts.CS_SupportedRecords;
        }
    }

    function beforeSubmit(scriptContext) {
        if (!initialize(scriptContext)) {
            return;
        }

        if (scriptContext.type != scriptContext.UserEventType.CANCEL) {
            validateRecordIsEditable();
        }

        // check for concurrency. record's last modified date should be same as previous
        if (scriptContext.oldRecord && scriptContext.newRecord &&
            scriptContext.oldRecord['lastmodifieddate'] !== scriptContext.newRecord['lastmodifieddate']) {
            log.error({
                title: 'Concurrency Error',
                details: 'Record has been changed last ' + scriptContext.oldRecord['lastmodifieddate'] +
                         ', expected: ' + scriptContext.newRecord['lastmodifieddate']
            });
            return;
        }
    }

    function afterSubmit(scriptContext) {
        if (!initialize(scriptContext)) {
            return;
        }

        const record = scriptContext.newRecord;
        const recType = record.type;
        const recId = record.id;

        if (scriptContext.type === scriptContext.UserEventType.DELETE) {
            deleteMirrorApproval(recType, recId);
        } else if ([scriptContext.UserEventType.CREATE, scriptContext.UserEventType.EDIT, scriptContext.UserEventType.COPY].indexOf(scriptContext.type) > -1) {
            const cUser = runtime.getCurrentUser();
            if (!mirrorApproval && (cUser.id !== Constants.ReservedUserId.SYSTEM || recType === 'customrecord_sas_approval_logs')) {
                const mirrorApprovalId = createMirrorApproval(recType, recId, cUser, record);
                initiateWorkflow(mirrorApprovalId);
                initiateApprovalRuleSearch(mirrorApprovalId);
            }
        } else if (scriptContext.type === scriptContext.UserEventType.CANCEL) {
            cancelWorkflow();
        }
    }

    function initialize(scriptContext) {
        newRecord = scriptContext.newRecord;

        if (isApprovalRoutingRequired(newRecord) === false) {
            return;
        }

        const recType = newRecord.type;

        mappingSetup = Helper.getMappingSetup(recType);

        if (!mappingSetup) {
            log.error({
                title: 'Supported Records',
                details: 'Record does not have a Mapping Setup. Please contact your administrator.'
            });
            return;
        }

        const recId = parseInt(newRecord.id, 10);
        approvalPrefStatus = PreferencesLib.getApprovalPreferenceStatus(recType);

        mirrorApproval = Helper.getMirrorApproval(recType, recId);

        dictionary = new Dictionary();

        return true;
    }

    function validateRecordIsEditable() {
        const SUPPORTED_SCRIPT_TYPES = [
            runtime.getContextType().MAP_REDUCE,
            runtime.getContextType().ACTION,
            runtime.getContextType().WORKFLOW
        ];
        if (SUPPORTED_SCRIPT_TYPES.indexOf(runtime.getExecutionContext()) === -1
                && isValidMirrorApproval()) {
            if (!isRoleAllowed()) {
                throw (dictionary.get('record.locked_for_edit'));
            }
        }
    }

    function isValidMirrorApproval() {
        return mirrorApproval && mirrorApproval.approvalRule && !mirrorApproval.pendingRuleEvaluation &&
            parseInt(mirrorApproval.approvalStatus, 10) !== Constants.TransactionState.REJECTED;
    }

    function isRoleAllowed() {
        const role = runtime.getCurrentUser().role;
        return (role === ADMIN_ROLE) || (role === FULL_ACCESS_ROLE);
    }

    function addApprovalFields(form, record, isViewMode) {
        var hasMultipleApprover = false;
        if (mirrorApproval && mirrorApproval.nextApproversId) {
            hasMultipleApprover = true;
        }

        util.each(approvalFields, function(fld) {
            var label = dictionary.get(fld.label) || ' ';
            var options = {
                id: fld.id,
                label: label,
                type: serverWidget.getFieldType()[fld.type],
                container: approvalGroup.id
            };

            if (fld.source) {
                options.source = fld.source;
            }

            var tmpField = form.addField(options);

            if (fld.help) {
                var help = dictionary.get(fld.help) || '';
                tmpField.setHelpText({
                    help: help
                });
            }

            if (fld.id === approvalFields.APPROVAL_STATUS.id) {
                if (!mirrorApproval) {
                    approvalStatusOptions.PENDING_APPROVAL['isSelected'] = true;
                }
                addApprovalStatusOptions(tmpField);
            }

            if (mirrorApproval && fld.recordmap) {
                tmpField.defaultValue = mirrorApproval[fld.recordmap];
            }

            switch (fld.id) {
                case approvalFields.APPROVAL_STATUS.id:
                    if (approvalPrefStatus && mappingSetup && mappingSetup.approvalStatusFld) {
                        fld.displayType = serverWidget.getFieldDisplayType().HIDDEN;
                    }

                    break;
                case approvalFields.NEXT_APPROVER.id:
                    if (approvalPrefStatus && ((mappingSetup && mappingSetup.nextApproverFld) || hasMultipleApprover)) {
                        fld.displayType = serverWidget.getFieldDisplayType().HIDDEN;
                    }

                    break;
                case approvalFields.REJECT_REASON.id:
                    if (!isViewMode) {
                        fld.displayType = serverWidget.getFieldDisplayType().HIDDEN;
                    }

                    break;
                case approvalFields.WAITING_INDICATOR.id:
                    if (isViewMode && mirrorApproval && mirrorApproval.pendingRuleEvaluation) {
                        var styles = [
                            'display: inline-block;',
                            'font-size: 12px;',
                            'font-weight: bold;',
                            'padding: 3px;',
                            'background-color: #d5e0ec;',
                            'position: relative;',
                            'top: -4px;',
                            'margin-top: 10px;',
                            'text-transform: uppercase;'
                        ];
                        tmpField.defaultValue = '<div style="' + styles.join(' ') + '">' +
                            dictionary.get(fld.defaultValue) + '</div>';
                    } else {
                        fld.displayType = serverWidget.getFieldDisplayType().HIDDEN;
                    }

                    break;
                default:
                    break;
            }

            if (fld.displayType) {
                tmpField.updateDisplayType({
                    displayType: serverWidget.getFieldDisplayType()[fld.displayType]
                });
            }
        });
    }

    function showNextApprovers(scriptContext) {
        const form = scriptContext.form;

        const nextApproversTxtFieldId = approvalFields.NEXT_APPROVERS_ID.id;
        const nextApproversMSFieldId = approvalFields.NEXT_APPROVERS.id;
        const delegateApproversMSFieldId = approvalFields.DELEGATED_APPROVERS.id;
        const nextApproversMSField = form.getField({
            id: nextApproversMSFieldId
        });
        const nextApproversTxtField = form.getField({
            id: nextApproversTxtFieldId
        });
        const delegateApproversMsField = form.getField({
            id: delegateApproversMSFieldId
        });

        const newRecord = scriptContext.newRecord;

        hideField(nextApproversTxtField);

        if (scriptContext.type === scriptContext.UserEventType.VIEW) {
            // Set field value. For display only.
            var nxtApprover;
            var nxtApprovers;
            if (mirrorApproval) {
                nxtApprover = mirrorApproval.nextApprover;
                nxtApprovers = mirrorApproval.nextApproversId;
            }

            if (nxtApprovers) {
                const selNxtApps = nxtApprovers.split(',');
                newRecord.setValue({
                    fieldId: nextApproversMSFieldId,
                    value: selNxtApps
                });

                // Hide records next approver field
                if (mappingSetup && mappingSetup.nextApproverFld) {
                    hideField(form.getField(mappingSetup.nextApproverFld));
                }
            } else {
                hideField(nextApproversMSField);
            }

            const delegateApprovers = Helper.getDelegates(nxtApprover, nxtApprovers);
            if (delegateApprovers.length > 0) {
                newRecord.setValue({
                    fieldId: delegateApproversMSFieldId,
                    value: delegateApprovers
                });
            } else {
                hideField(delegateApproversMsField);
            }
        } else {
            // Hide MS field on non-view mode
            hideField(nextApproversMSField);
            hideField(delegateApproversMsField);
        }
    }

    function hideField(field) {
        if (field) {
            field.updateDisplayType({
                displayType: serverWidget.getFieldDisplayType().HIDDEN
            });
        }
    }

    function initializeSalesOrderForm(type, form) {
        try {
            const orderStatusFld = form.getField(ORDER_STATUS_FIELD);
            if (orderStatusFld) {
                orderStatusFld.updateDisplayType({
                    displayType: serverWidget.getFieldDisplayType().DISABLED
                });
            }

            const approveSalesOrderBtn = form.getButton(APPROVE_ORDER_BUTTON);
            if (approveSalesOrderBtn) {
                approveSalesOrderBtn.isHidden = true;
            }
            const cancelSalesOrderBtn = form.getButton(CANCEL_ORDER_BUTTON);
            if (cancelSalesOrderBtn && mirrorApproval && mirrorApproval.pendingRuleEvaluation) {
                cancelSalesOrderBtn.isHidden = true;
            }
        } catch (e) {
            log.error({
                title: 'Error! Unable to initialize sales order form. ',
                details: e.toString()
            });
        }
    }

    function addApprovalStatusOptions(fld) {
        util.each(approvalStatusOptions, function(option) {
            var text = dictionary.get(option.text) || ' ';
            var newOption = {
                text: text,
                value: option.value,
                isSelected: option.isSelected
            };
            fld.addSelectOption(newOption);
        });
    }

    function disableParentRecordApprovalFields(recType, form, isCreateMode, isCopyMode) {
        try {
            if ((mappingSetup && (isCreateMode || isCopyMode)) || (mirrorApproval && mirrorApproval.approvalRule)) {
                approvalStatusFldId = mappingSetup.approvalStatusFld;
                nextApproverFldId = mappingSetup.nextApproverFld;

                if (approvalStatusFldId && nextApproverFldId) {
                    var tmpField = form.getField(approvalStatusFldId);
                    tmpField.updateDisplayType({
                        displayType: serverWidget.getFieldDisplayType().INLINE
                    });

                    tmpField = form.getField(nextApproverFldId);
                    tmpField.updateDisplayType({
                        displayType: serverWidget.getFieldDisplayType().INLINE
                    });
                }
            }
        } catch (e) {
            // TODO: handle exception
            const err = e.message || e.toString();
            log.error({
                title: 'ERROR disabling Parent Record Approval fields',
                details: err
            });
        }
    }

    function addApprovalButtons(form, newRecord, isViewMode) {
        if (!isViewMode) {
            return;
        }

        if (mirrorApproval) {
            const userId = parseInt(runtime.getCurrentUser().id, 10);
            // Note: delegatedApprovers values are from EmployeeDAO, hence id are integer type.
            const delegatedApprovers = newRecord.getValue(approvalFields.DELEGATED_APPROVERS.id);
            var isDelegatedApprover = false;
            if (delegatedApprovers && util.isArray(delegatedApprovers) && delegatedApprovers.length > 0) {
                if (delegatedApprovers.indexOf(userId) > -1) {
                    isDelegatedApprover = true;
                }
            }

            // Note: nextApprovers values are from mirrorRecord.nextApproversId which is a string separated by comma.
            // All id are string type.
            var nextApprovers = newRecord.getValue(approvalFields.NEXT_APPROVERS.id);
            var isAnApprover = false;
            if (nextApprovers && util.isArray(nextApprovers) && nextApprovers.length > 0) {
                if (nextApprovers.indexOf(userId.toString()) > -1) {
                    isAnApprover = true;
                }
            }

            if (parseInt(mirrorApproval.approvalStatus, 10) === approvalStatusOptions.PENDING_APPROVAL.value) {
                var hasRejectButton = false;
                const employee = new EmployeeDAO().getList({
                    internalId: userId
                }).first();

                if ((employee.superApproverRecords || '').split(',').indexOf(mirrorApproval.approvalRuleRecordName) > -1
                    && parseInt(mirrorApproval.submitter, 10) !== userId) {
                    hasRejectButton = true;
                    const superApproveButton = approvalButtons.SUPER_APPROVE;
                    superApproveButton.label = dictionary.get(superApproveButton.label);
                    form.addButton(superApproveButton);
                }

                if (parseInt(mirrorApproval.nextApprover, 10) === userId || isDelegatedApprover || isAnApprover) {
                    hasRejectButton = true;
                    const approveButton = approvalButtons.APPROVE;
                    approveButton.label = dictionary.get(approveButton.label);
                    form.addButton(approveButton);
                }

                if (hasRejectButton) {
                    const rejectButton = approvalButtons.REJECT;
                    rejectButton.label = dictionary.get(rejectButton.label);
                    form.addButton(rejectButton);
                }
            } else if (parseInt(mirrorApproval.approvalStatus, 10) === approvalStatusOptions.REJECTED.value
                && mirrorApproval.recordScriptId !== EMAIL_APPROVAL_LOG_ID) {
                const resubmitButton = approvalButtons.RESUBMIT;
                resubmitButton.label = dictionary.get(resubmitButton.label);
                form.addButton(resubmitButton);
            }
        }
    }

    function addHistoryTab(form, record, isViewMode) {
        if (mirrorApproval && isViewMode) {
            const items = formComps.ApprovalHistory;

            form.addTab({
                id: items.TAB.id,
                label: dictionary.get(items.TAB.label)
            });

            const sublist = form.addSublist({
                id: items.SUBLIST.id,
                label: dictionary.get(items.SUBLIST.label),
                tab: items.TAB.id,
                type: serverWidget.getSublistType()[items.SUBLIST.type]
            });

            util.each(items.COLUMNS, function(column) {
                sublist.addField({
                    id: column.id,
                    label: dictionary.get(column.label),
                    type: serverWidget.getFieldType()[column.type],
                    source: column.source
                });
            });

            try {
                new ApprovalHistoryDAO().getList({
                    recordType: record.type,
                    recordId: record.id,
                    sortByDateDesc: true
                }).each(function(row, index) {
                    var line = parseInt(index, 10);

                    sublist.setSublistValue({
                        id: items.COLUMNS.ACTION_DATE.id,
                        line: line,
                        value: row.actionDate
                    });

                    if (row.action) {
                        sublist.setSublistValue({
                            id: items.COLUMNS.ACTION.id,
                            line: line,
                            value: row.action
                        });
                    }

                    sublist.setSublistValue({
                        id: items.COLUMNS.ACTION_OWNER.id,
                        line: line,
                        value: row.actionOwnerText
                    });

                    if (row.approvalStatusText) {
                        sublist.setSublistValue({
                            id: items.COLUMNS.STATUS.id,
                            line: line,
                            value: row.approvalStatusText
                        });
                    }

                    if (row.approverText) {
                        sublist.setSublistValue({
                            id: items.COLUMNS.APPROVER_TYPE.id,
                            line: line,
                            value: row.approverTypeText || dictionary.get('history.approver_type.employee_hierarchy')
                        });

                        sublist.setSublistValue({
                            id: items.COLUMNS.APPROVER.id,
                            line: line,
                            value: row.approverText.split(',').join(', ')
                        });
                    }

                    if (row.remarks) {
                        var remarks = (row.remarks.length > 220) ? row.remarks.substring(0, 220) +
                            getApprovalHistoryLink(row.id) : row.remarks;

                        sublist.setSublistValue({
                            id: items.COLUMNS.REMARKS.id,
                            line: line,
                            value: remarks
                        });
                    }
                });
            } catch (e) {
                log.error({
                    title: 'APPROVAL_HISTORY_LOADING_ERROR',
                    details: e
                });
            }
        }
    }

    function deleteMirrorApproval(recType, recId) {
        try {
            if (recType && recId) {
                const mirrorApproval = Helper.getMirrorApproval(recType, recId);
                if (mirrorApproval && mirrorApproval.id) {
                    new MirrorApprovalDAO().deleteRecord(mirrorApproval.id);
                }
            }
        } catch (e) {
            log.error({
                title: 'Cannot delete Mirror Approval record',
                details: e
            });
        }
    }

    function isCustomApproval(record) {
        var status;
        if (record.type === 'journalentry') {
            status = record.getValue('iscustomapproval');
        } else if (record.type === SALES_ORDER_ID) {
            status = PreferencesLib.isSalesOrderPreferenceEnabled();
        }
        return status;
    }

    function isApprovalRoutingRequired(record) {
        // Check custom approval for Journal Entry only
        // Value should be strictly false to exit
        if (isCustomApproval(record) === false) {
            log.audit({
                title: 'Custom Approval setting is off',
                details: 'Record does not require approval routing.'
            });
            return false;
        }

        // Check Approval Routing Preference
        // Value should be strictly false to exit
        if (PreferencesLib.getApprovalPreferenceStatus(record.type) === false) {
            log.audit({
                title: 'Approval Routing Preference is off',
                details: 'Record does not require approval routing.'
            });
            return false;
        }

        return true;
    }

    function createMirrorApproval(type, id, currUser, record) {
        if (type && id) {
            try {
                const newApprovalRecord = new MirrorApprovalDAO().createRecord();
                newApprovalRecord.setValue({
                    fieldId: MirrorApprovalDAO.Fields.REF_RECORD_SCRIPT_ID,
                    value: type
                });

                newApprovalRecord.setValue({
                    fieldId: MirrorApprovalDAO.Fields.REF_RECORD_INTERNAL_ID,
                    value: id
                });

                try {
                    const submitterId = type === 'customrecord_sas_approval_logs' ?
                        record.getValue('custrecord_sas_app_log_approver') : getSubmitter(record, currUser);

                    newApprovalRecord.setValue({
                        fieldId: MirrorApprovalDAO.Fields.SUBMITTER,
                        value: submitterId
                    });
                } catch (e) {
                    log.error({
                        title: 'ERROR: Cannot set the record submitter!',
                        details: e.message || e.toString()
                    });
                    throw e;
                }

                // retain record submitter's preference since we cannot load an employee's preference (for PO cases)
                newApprovalRecord.setValue({
                    fieldId: MirrorApprovalDAO.Fields.PREFERENCES,
                    value: JSON.stringify({
                        timezone: currUser.getPreference({
                            name: 'TIMEZONE'
                        }),
                        language: currUser.getPreference({
                            name: 'LANGUAGE'
                        })
                    })
                });

                const mirrorId = newApprovalRecord.save({
                    enableSourcing: true,
                    ignoreMandatoryFields: false
                });

                return mirrorId;
            } catch (e) {
                log.error({
                    title: 'ERROR: Cannot create Mirror Approval record!',
                    details: e.message || e.toString()
                });
                throw e;
            }
        }
    }

    function getSubmitter(record, currUser) {
        var employee = '';
        const employeeFld = mappingSetup.employeeFld;
        if (employeeFld) {
            employee = record.getValue(employeeFld);
        }
        return employee || currUser.id;
    }

    function initiateWorkflow(mirrorRecId) {
        try {
            workflow.initiate({
                recordType: MIRROR_APPROVAL_ID,
                recordId: mirrorRecId,
                workflowId: APPROVAL_WORKFLOW_ID
            });
            workflow.trigger({
                recordType: MIRROR_APPROVAL_ID,
                recordId: mirrorRecId,
                workflowId: APPROVAL_WORKFLOW_ID,
                actionId: GET_APPROVAL_RULE_BTN
            });
        } catch (e) {
            log.error({
                title: 'ERROR: Cannot initiate Approval Workflow!',
                details: e.message || e.toString()
            });
            throw e;
        }
    }

    function cancelWorkflow() {
        try {
            if (mirrorApproval && mirrorApproval.approvalRule && !mirrorApproval.pendingRuleEvaluation &&
                parseInt(mirrorApproval.approvalStatus, 10) !== Constants.TransactionState.APPROVED) {
                var action = PENDING_APPROVAL_CANCEL_BTN;
                if (parseInt(mirrorApproval.approvalStatus, 10) === Constants.TransactionState.REJECTED) {
                    action = REJECTED_CANCEL_BTN;
                }
                workflow.trigger({
                    recordType: MIRROR_APPROVAL_ID,
                    recordId: mirrorApproval.id,
                    workflowId: APPROVAL_WORKFLOW_ID,
                    actionId: action
                });
            }
        } catch (e) {
            log.error({
                title: 'ERROR: Cannot cancel Approval Workflow!',
                details: e.message || e.toString()
            });
            throw e;
        }
    }

    function initiateApprovalRuleSearch(mirrorRecId) {
        try {
            task.create({
                taskType: task.getTaskType().MAP_REDUCE,
                scriptId: 'customscript_sas_approval_rule_eval',
                deploymentId: 'customdeploy_sas_approval_rule_eval',
                params: {
                    // eslint-disable-next-line camelcase
                    custscript_sas_appruleeval_mirror_rec: mirrorRecId
                }
            }).submit();
        } catch (e) {
            log.error({
                title: 'ERROR! Cannot initiate Approval Rule search!',
                details: e.message || e.toString()
            });
            if (e.name !== 'MAP_REDUCE_ALREADY_RUNNING') {
                throw e;
            }
        }
    }

    function getApprovalHistoryLink(id) {
        var historyLink = url.resolveRecord({
            recordType: APPROVAL_HISTORY_ID,
            recordId: id,
            isEditMode: false
        });

        if (historyLink) {
            historyLink = ' <a href="' + historyLink + '" target="_blank">(more...)</a>';
        }

        return historyLink;
    }

    function addVBExceptionButton(form, isViewMode) {
        if (!isViewMode) {
            return;
        }

        if (mirrorApproval && mirrorApproval.recordScriptId === 'vendorbill'
            && mirrorApproval.exceptions.length > 0) {
            var exceptions = mirrorApproval.exceptions;

            if (exceptions !== 'F') {
                form.addButton({
                    id: 'custpage_sas_vb_btn_show_exceptions',
                    label: dictionary.get('vb.button_show_exceptions'),
                    functionName: 'btnShowExceptions("'+ exceptions + '")'
                });
            }
        }
    }

    return {
        beforeLoad: beforeLoad,
        beforeSubmit: beforeSubmit,
        afterSubmit: afterSubmit
    };
});