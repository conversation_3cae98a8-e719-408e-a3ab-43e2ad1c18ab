revised

CASE WHEN {custbody_contract_name.custrecord_kardin_billing_method}='Company' THEN (CASE WHEN {customermain.custentity3} IS NULL THEN {customermain.email} ELSE {customermain.custentity3} END) ELSE (CASE WHEN {custbody_contract_name.custrecord_kardin_billing_method}='Individual' THEN (CASE WHEN {custbody_user_billto} IS NULL THEN (CASE WHEN {customermain.custentity3} IS NULL THEN {customermain.email} ELSE {customermain.custentity3} END) ELSE {custbody_user_billto} END) ELSE '' END) END

old 
CASE WHEN {custbody_contract_name.custrecord_kardin_billing_method}='2' THEN {customermain.email} ELSE (CASE WHEN {customermain.custentity_electronic_billing}='T' OR {custbody_contract_name.custrecord_kardin_billing_method}='' THEN {customermain.custentity3} ELSE {custbody_user_billto} END) END


CASE WHEN {custbody_contract_name.custrecord_kardin_billing_method}='2' THEN {customermain.email} ELSE (CASE WHEN {customermain.custentity_electronic_billing}='T' OR {custbody_contract_name.custrecord_kardin_billing_method}='' THEN {customermain.custentity3} ELSE {custbody_user_billto} END) END