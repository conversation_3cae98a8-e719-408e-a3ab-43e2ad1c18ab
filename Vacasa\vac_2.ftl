<#-- format specific processing -->
<#function isBalanceLine>
<#return cbank.custpage_eft_custrecord_2663_balance_line >
</#function>
<#function getBankServiceClassCode>
<#if isBalanceLine() >
<#assign value = "200">
<#else>
<#assign value = "220">
</#if>
<#return value>
</#function>
<#function getEntityBankAccountType bankAccount>
<#if bankAccount == "Savings" >
<#assign value = "32">
<#else>
<#assign value = "22">
</#if>
<#return value>
</#function>
<#function computeTotalDebitAmt batchPayments>
<#if isBalanceLine() >
<#assign value = 0>
<#list batchPayments as payment>
<#assign value = value + getAmount(payment)>
</#list>
<#else>
<#assign value = 0>
</#if>
<#return value>
</#function>
<#function computeTotalRecords recordCount>
<#assign value = (recordCount / 10) >
<#assign value = value?ceiling >
<#return value>
</#function>
<#function getBalanceLineTransactionCode ebanks>
<#assign value = "">
<#if isBalanceLine() >
<#assign cbankAcctType = cbank.custpage_eft_custrecord_2663_bank_acct_type>
<#if cbankAcctType == "Savings">
<#assign value = "32">
<#else>
<#assign value = "22">
</#if>
<#list ebanks as ebank>
<#assign bankAccount = ebank.custrecord_2663_entity_bank_acct_type >
<#assign ebankAcctType = getEntityBankAccountType(bankAccount) >
<#if ebankAcctType == "22" || ebankAcctType == "32" >
<#if cbankAcctType == "Savings">
<#assign value = "37">
<#else>
<#assign value = "27">
</#if>
<#break>
</#if>
</#list>
</#if>
<#return value>
</#function>
<#-- Divide Payments, Entities, and Entity Banks into two groups (CCD, PPD) -->
<#assign ccdPaymentsStr = "">
<#assign ccdEbanksStr = "">
<#assign ccdEntitiesStr = "">
<#assign ppdPaymentsStr = "">
<#assign ppdEbanksStr = "">
<#assign ppdEntitiesStr = "">
<#-- avoid sequence concatenation, use sequence strings instead -->
<#list payments as payment>
<#assign ebank = ebanks[payment_index]>
<#assign entity = entities[payment_index]>
<#if ebank.custrecord_2663_parent_vendor == payment.entity || ebank.custrecord_2663_parent_cust_ref == payment.entity>
<#assign ccdPaymentsStr = ccdPaymentsStr + "payments[" + payment_index?c?string + "],">
<#assign ccdEbanksStr = ccdEbanksStr + "ebanks[" + payment_index?c?string + "],">
<#assign ccdEntitiesStr = ccdEntitiesStr + "entities[" + payment_index?c?string + "],">
</#if>
<#if ebank.custrecord_2663_parent_employee == payment.entity>
<#assign ppdPaymentsStr = ppdPaymentsStr + "payments[" + payment_index?c?string + "],">
<#assign ppdEbanksStr = ppdEbanksStr + "ebanks[" + payment_index?c?string + "],">
<#assign ppdEntitiesStr = ppdEntitiesStr + "entities[" + payment_index?c?string + "],">
</#if>
</#list>
<#-- convert from strings to sequences -->
<#assign ccdPayments = ("[" + removeEnding(ccdPaymentsStr, ",") + "]")?eval>
<#assign ccdEbanks = ("[" + removeEnding(ccdEbanksStr, ",") + "]")?eval>
<#assign ccdEntities = ("[" + removeEnding(ccdEntitiesStr, ",") + "]")?eval>
<#assign ppdPayments = ("[" + removeEnding(ppdPaymentsStr, ",") + "]")?eval>
<#assign ppdEbanks = ("[" + removeEnding(ppdEbanksStr, ",") + "]")?eval>
<#assign ppdEntities = ("[" + removeEnding(ppdEntitiesStr, ",") + "]")?eval>
<#function computeSequenceId>
<#assign lastSeqId = getSequenceId(true)>
<#-- store new sequence id to be returned later -->
<#assign newSeqId = lastSeqId + 1>
<#-- do char code to character conversion -->
<#assign seqId = (lastSeqId % 26) + 65>
<#assign seqId = seqId?string?replace("65","A")>
<#assign seqId = seqId?string?replace("66","B")>
<#assign seqId = seqId?string?replace("67","C")>
<#assign seqId = seqId?string?replace("68","D")>
<#assign seqId = seqId?string?replace("69","E")>
<#assign seqId = seqId?string?replace("70","F")>
<#assign seqId = seqId?string?replace("71","G")>
<#assign seqId = seqId?string?replace("72","H")>
<#assign seqId = seqId?string?replace("73","I")>
<#assign seqId = seqId?string?replace("74","J")>
<#assign seqId = seqId?string?replace("75","K")>
<#assign seqId = seqId?string?replace("76","L")>
<#assign seqId = seqId?string?replace("77","M")>
<#assign seqId = seqId?string?replace("78","N")>
<#assign seqId = seqId?string?replace("79","O")>
<#assign seqId = seqId?string?replace("80","P")>
<#assign seqId = seqId?string?replace("81","Q")>
<#assign seqId = seqId?string?replace("82","R")>
<#assign seqId = seqId?string?replace("83","S")>
<#assign seqId = seqId?string?replace("84","T")>
<#assign seqId = seqId?string?replace("85","U")>
<#assign seqId = seqId?string?replace("86","V")>
<#assign seqId = seqId?string?replace("87","W")>
<#assign seqId = seqId?string?replace("88","X")>
<#assign seqId = seqId?string?replace("89","Y")>
<#assign seqId = seqId?string?replace("90","Z")>
<#return seqId>
</#function>
<#-- cached values -->
<#assign totalAmount = computeTotalAmount(payments)>
<#-- template building -->
#OUTPUT START#
<#assign recordCount = 0>
<#assign batchCount = 0>
<#assign lineCount = 0>
<#assign batchLineNum = 0>
<#assign padBlocksString = "">
<#assign ccdBankNumberHash = 0>
<#assign ppdBankNumberHash = 0>
<#assign totalBankNumberHash = 0>
<#assign begitem = 0>
<#if (ccdPayments?size > 0) >
<#assign begitem = ccdPayments[0]!>
</#if>
<#if (ppdPayments?size > 0) >
<#assign begitem = ppdPayments[0]!>
</#if>
${setPadding(batchCount,"left","0",6)}290122234DRFT5${setLength(pfa.custrecord_jul_date,3)} *********${setLength(begitem.tranid,11)} 01EPL${setPadding(batchLineNum,"left"," ",149)}
<#assign recordCount = recordCount + 1>
<#if (ccdPayments?size > 0) >
<#assign batchCount = batchCount + 1>
<#assign recordCount = recordCount + 1>
<#list ccdPayments as payment>
<#assign batchLineNum = batchLineNum + 1>
<#assign ebank = ccdEbanks[payment_index]>
<#assign entity = ccdEntities[payment_index]>
<#assign ccdBankNumberHash = ccdBankNumberHash + ebank.custrecord_2663_entity_bank_no?substring(0,8)?number>
<#assign padBlocksString = "\n">
<#assign paidTransactions=transHash[payment.internalid]>
<#assign clientPaymentRef=paidTransactions[0]>
${setPadding(batchCount,"left","0",6)}30108${setLength(ebank.custrecord_2663_entity_acct_no,16)}${setLength(payment.custbody_trxdate,6)} ${setPadding(formatAmount(getAmount(payment)),"left","0",9)}PAYMENT ${setLength(clientPaymentRef.tranid,17)}${setLength("",16)}Y${setLength(recordCount,11)}${setLength("",19)}0000${setLength("",9)}${setLength(payment.custbody_ent_id,60)}${setLength("",11)}
<#assign recordCount = recordCount + 1>
</#list>
<#assign lineCount = lineCount + batchLineNum>
<#assign totalBankNumberHash = totalBankNumberHash + ccdBankNumberHash>
<#assign recordCount = recordCount + 1>
</#if>
<#assign batchLineNum = 0>
<#if (ppdPayments?size > 0) >
<#assign batchCount = batchCount + 1>
<#assign recordCount = recordCount + 1>
<#list ppdPayments as payment>
<#assign batchLineNum = batchLineNum + 1>
<#assign ebank = ppdEbanks[payment_index]>
<#assign entity = ppdEntities[payment_index]>
<#assign ppdBankNumberHash = ppdBankNumberHash + ebank.custrecord_2663_entity_bank_no?substring(0,8)?number>
<#assign padBlocksString = "\n">
${setPadding(batchCount,"left","0",6)}30108${setLength(ebank.custrecord_2663_entity_acct_no,16)}${setLength(payment.custbody_trxdate,6)} ${setPadding(formatAmount(getAmount(payment)),"left","0",9)}PAYMENT ${setLength(clientPaymentRef.tranid,17)}${setLength("",16)}Y${setLength(recordCount,11)}${setLength("",19)}0000${setLength("",9)}${setLength(payment.custbody_ent_id,60)}${setLength("",11)}
<#assign recordCount = recordCount + 1>
</#list>
<#assign lineCount = lineCount + batchLineNum>
<#assign totalBankNumberHash = totalBankNumberHash + ppdBankNumberHash>
<#assign recordCount = recordCount + 1>
</#if>
<#assign recordCount = recordCount + 1>
<#if cbank.custpage_eft_custrecord_2663_pad_blocks && (recordCount % 10 > 0)>
<#assign padBlocksString = "\n">
<#assign numBlocks = 10 - (recordCount % 10) >
<#assign padding = "9999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999">
<#list 1..numBlocks as i>
<#assign padBlocksString = padBlocksString + padding + "\n">
</#list>
</#if>
${setPadding(batchCount,"left","0",6)}590132234DRFT5${setLength(pfa.custrecord_jul_date,3)}************ TO DO: ADD NET AMOUNT, COUNT OF DEBITS ${setPadding(batchCount,"left","0",6)}${setPadding(formatAmount(totalAmount),"left","0",10)}${setLength("",110)}
<#rt>
#OUTPUT END#
#RETURN START#
sequenceId:${newSeqId}
#RETURN END#