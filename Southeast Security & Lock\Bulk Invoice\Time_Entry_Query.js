require(["N/currentRecord", "N/query", "N/search"], function(record, query, search) {
        
    var recObj = record.get();
    var sqlQueryData = "SELECT tb.id, tsk.id, tb.item, tb.hours FROM timebill tb INNER JOIN task tsk ON tb.casetaskevent = tsk.id AND tsk.supportcase = ?";
    var resultSet = query.runSuiteQL({
        query: sqlQueryData,
        params: [287528]
    });

    var results = resultSet.results;

    for(var i = 0; i < results.length; i++ ){
        console.log(results[i].values);
    }
});