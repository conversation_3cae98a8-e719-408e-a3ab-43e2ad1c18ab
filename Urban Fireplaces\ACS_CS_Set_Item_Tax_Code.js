/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/search'], function(search) {

    function pageInit(context) {
        
    }

    function postSourcing(context) {
        var soObj = context.currentRecord;

        if(context.sublistId == 'item' && context.fieldId == 'item'){

            var itemId = soObj.getCurrentSublistValue({
                sublistId: context.sublistId,
                fieldId: context.fieldId
            });        

            var itemLookUp = search.lookupFields({
                type: search.Type.ITEM,
                id: itemId,
                columns: [
                    'taxschedule'
                ]
            });

            var taxCode = "";

            log.debug('test', itemLookUp);

            if(itemLookUp.hasOwnProperty('taxschedule')){
                if(itemLookUp.taxschedule.length > 0) {
                    switch(itemLookUp.taxschedule[0].value) {
                        case "1":
                            soObj.setCurrentSublistValue({
                                sublistId: 'item',
                                fieldId: 'taxcode',
                                value: 11
                            });
                            break;
                        case "2":
                            soObj.setCurrentSublistValue({
                                sublistId: 'item',
                                fieldId: 'taxcode',
                                value: 15
                            });
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    return {
        pageInit: pageInit,
        postSourcing: postSourcing
    }
});
