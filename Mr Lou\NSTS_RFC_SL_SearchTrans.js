/**
 * Copyright (c) 1998-2017 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * NetSuite, Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with NetSuite.
 * 
 * Search Source Transactions (test)
 * 
 * Version    Date            	Author           	Remarks
 * 1.00       20 May 2017      	rilagan		   		Initial version.
 * 1.20		  13 April 2021		jdgonzal			Added Shipping Method
 * 1.21		  26 April 2021		jdgonzal			Added Shipping Addresses
 */

/**
* @NApiVersion 2.0
* @NScriptType suitelet
*/
define(['N/record','N/search','N/url','N/format', 'N/ui/serverWidget','N/file','../library/NSTS_RFC_Lib_ObjectsAndFunctions.js','../library/NSTS_RFC_Lib_CreateTransaction.js','../library/NSTS_RFC_Lib_Constants.js'],
    function (record,search,url, format,serverWidget,file,lib,libCreate) {

		var stSubmittedCase	= '';	
		var stPassedCase = '';
		var stCustomer = '';
		var stTransFrom = '';
		var stTransTo = '';
		var stSearchType = '';
		var stDateCreate = '';
		var stCreateTrans ='';
		var stLines ='';
		var stSelTrans ='';
		var stFieldMemo = '';
		var stTransNo = '';
		var stActionType = '';
		var intCurrentPage = null;
		var stRowResults = '';
		var stCustomerId = null;
		var stShipMethod = '';
		var intMaxSearchResults= 75000;
		function searchTransSuitelet(context) {		
			stSubmittedCase = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.CASE.ID];
			stActionType = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.ACTION_TYPE.ID];
			intCurrentPage = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.CURR_PAGE.ID];
			stPassedCase = context.request.parameters['case'];
			stLines = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID];
			stSelTrans = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TRANS.ID];
			stCustomer = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.CUSTOMER.ID];
			stTransFrom = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_FROM.ID];
			stTransTo = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_TO.ID];
			stSearchType = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SEARCH_TYPE.ID];
			stDateCreate = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID];
			stCreateTrans = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID];
			stFieldMemo = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID];
			stTransNo = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_NO.ID];
			stCustomerId = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.CUSTOMER_ID.ID];
			stCaseSelType = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.CASE_SEL_TYPE.ID];
			stShipMethod = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID];
			stShipAddress = context.request.parameters[HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_ADDRESS.ID];
			
			var accountFeatures	= lib.getFeatures();
			if(lib.isEmpty(stCaseSelType))
				stCaseSelType = '';
			else
				stCaseSelType = stCaseSelType.toLowerCase();
			
			var form = serverWidget.createForm({
	                title: HC_RFC_SUITELET_SHOW_TRANS.Title
	            });

            var intScriptInternalId = lib.getScriptInternalId('customscript_nsts_rfc_suitelet_valid_cs');
            form.clientScriptFileId = intScriptInternalId;
            
    		var objPrefRec = lib.getPreferenceRecord();    
    		stPrefRecordId = objPrefRec.id;
    		if(lib.isEmpty(objPrefRec)){
    			form = showError(form,'Please set up preference record.');
    			context.response.writePage(form);
    			return true;
    		}else{
        		var bEnableScript =  objPrefRec.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_ENABLE_CREATE);
        		stRowResults = objPrefRec.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_MAX_ROW);
        		if(!bEnableScript){
        			form = showError(form,'Please enable creation on preference record.');
        			context.response.writePage(form);
        			return true;
        		}
        		//Get Default Fields
        		if(lib.isEmpty(stDateCreate))
        			stDateCreate = objPrefRec.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DATE_CREATE_TRANS);
        		if(lib.isEmpty(stFieldMemo))
        			stFieldMemo = objPrefRec.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_MEMO);
    		}

    		defaultFields();

        		if(lib.isEmpty(stPassedCase)){
        			form = showError(form,'No case record.');
        			context.response.writePage(form);
        			return true;
        		}
    			if(!lib.isEmpty(stLines)){
					log.audit('janel test params', context.request.parameters );
    				var stError = libCreate.newTrans(context,null,objPrefRec,accountFeatures);
    				if(stError){

            			form = createForm(context,objPrefRec,form);
            			var fldError = form.addField({
	    	                id: 'custpage_nsts_rfc_flderror',
	    	                type: serverWidget.FieldType.INLINEHTML,
	    	                label: 'Error',
	    	                container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
	    	            });
	    				fldError.defaultValue = stError;
    				}
    			}
    			else
        			form = createForm(context,objPrefRec,form,accountFeatures);
    			
    		//}
			context.response.writePage(form);
        }
		
		/********************Default fields upon initialize of suitelet **********************/
		function defaultFields(){
			var bInit = true;
			if(!lib.isEmpty(stSubmittedCase)){
				stPassedCase = stSubmittedCase;
				bInit = false;
			}
			if(lib.isEmpty(stCustomer) && bInit){
				var objRecord = record.load({
				    type: 'supportcase', 
				    id: stPassedCase
				});
				try{
					var objRecord = search.lookupFields({
					    type: 'supportcase',
					    id: stPassedCase,
					    columns: [HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_TRANS_FROM,
					              HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_TRANS_TO, 
					              HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_SEARCH_TRANS_TYPE,
					              HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_CREATE_TRANS,
					              HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_SEARCH_TRANS_NO,
					              'casenumber','company']
					});
					if(lib.isEmpty(stTransFrom))
						stTransFrom = objRecord[HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_TRANS_FROM];
					if(lib.isEmpty(stTransTo))
						stTransTo = objRecord[HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_TRANS_TO];
					if(lib.isEmpty(stSearchType))
						stSearchType = objRecord[HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_SEARCH_TRANS_TYPE];
					if(!lib.isEmpty(stSearchType))
						stSearchType = stSearchType[0]['value'];
					if(lib.isEmpty(stCreateTrans))
						stCreateTrans = objRecord[ HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_CREATE_TRANS];
					if(!lib.isEmpty(stCreateTrans))
						stCreateTrans = stCreateTrans[0]['value'];
					if(lib.isEmpty(stCustomer))
						stCustomer = objRecord['company'];

					if(!lib.isEmpty(stCustomer)){
						stCustomerId = stCustomer[0]['value'];
						stCustomer = stCustomer[0]['text'];
					}
					var stCaseNo = objRecord['casenumber'];
					
					if(!lib.isEmpty(stFieldMemo)){
						stFieldMemo = stFieldMemo.replace('{casenumber}',' # '+stCaseNo);
					}
					var stOrigTransNo = objRecord[HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_SEARCH_TRANS_NO];
					if(!lib.isEmpty(stOrigTransNo))
						stOrigTransNo = stOrigTransNo[0]['value'];
					if(bInit && !lib.isEmpty(stOrigTransNo)){
						try{

							var stTranId =  HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_SEARCH_TRANS_NO +'.tranid';
							var stTranType =  HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_SEARCH_TRANS_NO +'.type';
							var objLookup = search.lookupFields({
							    type: 'supportcase',
							    id: stPassedCase,
							    columns: [stTranId,stTranType]
							});
							stTransNo	 	= objLookup[stTranId];
							stType	 		= objLookup[stTranType][0]['value'];
							if(!lib.isEmpty(stType)){
								stType = stType.toLowerCase();
								if (stType.indexOf("inv") !=-1) {
									stSearchType = HC_RFC_LIST_TRANSACTION_TYPE.INVOICE;
								}else
									stSearchType = HC_RFC_LIST_TRANSACTION_TYPE.SALESORDER;
							}
								
						}catch(error){

							log.error('trans', JSON.stringify(error));	
						}
					}
					if(!lib.isEmpty(stTransNo))
						stTransNo = stTransNo.trim();				

				}catch(error){
					log.error('ERROR ON DEFAULT FIELDS', error.toString());
				}
			}
		}
		
		/*******************************Show error *******************************/
		function showError(form,stMsg){
			var objFld = form.addField({
                id: '_nsts_rfc_showerr',
                type: serverWidget.FieldType.INLINEHTML,
                label: 'err',
            });
            objFld.defaultValue = stMsg;
			return form;
		}

		/*********************************Get Customer Addresses *****************************/
		function getCustomrAddresses() {

			var addresses = [];

			var custObj = record.load({
				type: record.Type.CUSTOMER,
				id: stCustomerId,
				isDynamic: false
			});
			var count = custObj.getLineCount({
				sublistId: 'addressbook'
			});
			for (var i = 0; i < count; i++){
				var addressId = custObj.getSublistValue({
					sublistId: 'addressbook',
					fieldId: 'internalid',
					line: i 
				});

				var addressLabel = custObj.getSublistValue({
					sublistId: 'addressbook',
					fieldId: 'label',
					line: i 
				});

				var addressDefaultShipping = custObj.getSublistValue({
					sublistId: 'addressbook',
					fieldId: 'defaultshipping',
					line: i 
				});

				addresses.push({
					address_id: addressId,
					address_label: addressLabel,
					default_shipping: addressDefaultShipping
				})
			}

			return addresses;

		}
		
		
		/*********************************Create form *****************************/
		function createForm(context,objPref,form,accountFeatures){


			var fieldgroup = form.addTab({
			    id : HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
			    label : HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.LABEL,
			});
			var objCase = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.CASE.ID,
                type: serverWidget.FieldType.SELECT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.CASE.LABEL,
                source: 'supportcase'
            });
			objCase.updateDisplayType({displayType: serverWidget.FieldDisplayType.INLINE});
			objCase.defaultValue = stPassedCase;
			objCase.setHelpText({
			    help : HC_RFC_SUITELET_SHOW_TRANS.Fields.CASE.HELP
			});
			var objCustomer = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.CUSTOMER.ID,
                type: serverWidget.FieldType.TEXT,
                label:HC_RFC_SUITELET_SHOW_TRANS.Fields.CUSTOMER.LABEL
            });
			objCustomer.defaultValue = stCustomer;
			objCustomer.setHelpText({
			    help : HC_RFC_SUITELET_SHOW_TRANS.Fields.CUSTOMER.HELP
			});
			objCustomer.updateDisplayType({displayType: serverWidget.FieldDisplayType.INLINE});

			var objType = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.SEARCH_TYPE.ID,
                type: serverWidget.FieldType.SELECT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.SEARCH_TYPE.LABEL
            });
			objType.defaultValue = stSearchType;
			objType.setHelpText({
			    help : HC_RFC_SUITELET_SHOW_TRANS.Fields.SEARCH_TYPE.HELP
			});
			objType.addSelectOption({value: HC_RFC_LIST_TRANSACTION_TYPE.INVOICE, text: 'Invoice'});
			objType.addSelectOption({value: HC_RFC_LIST_TRANSACTION_TYPE.SALESORDER, text: 'Sales Order'});
			objType.isMandatory = true;
			var objTransFrom = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_FROM.ID,
                type: serverWidget.FieldType.DATE,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_FROM.LABEL
            });
			objTransFrom.defaultValue = stTransFrom;
			objTransFrom.isMandatory = true;
			objTransFrom.setHelpText({
			    help : HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_FROM.HELP
			});

			var objTransTo = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_TO.ID,
                type: serverWidget.FieldType.DATE,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_TO.LABEL
            });	
			objTransTo.defaultValue = stTransTo;
			objTransTo.isMandatory = true;
			objTransTo.setHelpText({
			    help : HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_TO.HELP
			});
			
			var objTransNo = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_NO.ID,
                type: serverWidget.FieldType.TEXT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_NO.LABEL
            });	
			objTransNo.setHelpText({
			    help : HC_RFC_SUITELET_SHOW_TRANS.Fields.TRANSACTION_NO.HELP
			});
			if(!lib.isEmpty(stTransNo))
				objTransNo.defaultValue = stTransNo;
			else
				objTransNo.defaultValue  = '';
			
			//Results
			var objSeparate = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.SEPARATOR.ID,
                type: serverWidget.FieldType.INLINEHTML,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.SEPARATOR.LABEL,
                container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
            });
			objSeparate.defaultValue = '<hr></hr>';
			objSeparate.defaultValue = HC_OTHERS.SEPARATOR_CSS +'<div id="ns_navigation" style="height: 30px;">  </div>';
			objSeparate.updateBreakType({breakType: serverWidget.FieldBreakType.STARTCOL});
            var objFldBuildPage = form.addField({
                id:HC_RFC_SUITELET_SHOW_TRANS.Fields.TABLE_PAGE.ID,
                type: serverWidget.FieldType.INLINEHTML,
                label:HC_RFC_SUITELET_SHOW_TRANS.Fields.TABLE_PAGE.LABEL,
                container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
            });
			var objFldPages = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.PAGE_BUTTONS.ID,
                type: serverWidget.FieldType.INLINEHTML,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.PAGE_BUTTONS.LABEL,
                container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
            });
			var stDisplay = HC_OTHERS.BTN_CSS +'<p style="text-align: left">';

			
			var objResults = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.TREE_RESULTS.ID,
                type: serverWidget.FieldType.INLINEHTML,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.TREE_RESULTS.LABEL,
                container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
            });
			objResults.defaultValue = '';
			var objDateOnCreate = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID,
                type: serverWidget.FieldType.SELECT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.LABEL,
                container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
            });	 

			objDateOnCreate.defaultValue = stDateCreate;
			objDateOnCreate.setHelpText({
			    help : HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.HELP
			});

			// JLG - EDITED 01/27/21
			objDateOnCreate.addSelectOption({value: HC_RFC_LIST_DATE_TYPE.SYSTEM, text: 'System Date'});
			objDateOnCreate.addSelectOption({value: HC_RFC_LIST_DATE_TYPE.TRANSACTION, text: 'Transaction Date'});
			objDateOnCreate.addSelectOption({value: HC_RFC_LIST_DATE_TYPE.CASE, text: 'Case Date'});
			objDateOnCreate.defaultValue = HC_RFC_LIST_DATE_TYPE.SYSTEM;

			objDateOnCreate.isMandatory = true;

            var objCreateTrans = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID,
                type: serverWidget.FieldType.SELECT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.LABEL,
                container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
            });	 

            if(stCaseSelType != 'returnauthorization')
            	objCreateTrans.addSelectOption({value: HC_RFC_LIST_CREATE_TRANS.CREATE_RMA, text: 'Create Return Authorization'});
            if(stCaseSelType != 'salesorder')
            	objCreateTrans.addSelectOption({value: HC_RFC_LIST_CREATE_TRANS.CREATE_SO, text: 'Create Replacement Sales Order'});

			// JLG - ADDED 04/13/2021
			var objShippingMethod = form.addField({
				id: HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID,
				type: serverWidget.FieldType.SELECT,
				label: HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.LABEL,
				container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
			});	 

			objShippingMethod.setHelpText({
			    help : HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.HELP
			});

			// JLG - EDITED 04/13/21
			objShippingMethod.addSelectOption({value: '', text: '' });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.CURBSIDE.ID, text: HC_RFC_LIST_SHIP_METHOD.CURBSIDE.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.CUSTOMER_ACOUNT.ID, text: HC_RFC_LIST_SHIP_METHOD.CUSTOMER_ACOUNT.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.FDXINTECDDP.ID, text: HC_RFC_LIST_SHIP_METHOD.FDXINTECDDP.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.FEDEXIP.ID, text: HC_RFC_LIST_SHIP_METHOD.FEDEXIP.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.FEDEXS.ID, text: HC_RFC_LIST_SHIP_METHOD.FEDEXS.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.FEDGRD.ID, text: HC_RFC_LIST_SHIP_METHOD.FEDGRD.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.FEDPON.ID, text: HC_RFC_LIST_SHIP_METHOD.FEDPON.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.FEDSEC.ID, text: HC_RFC_LIST_SHIP_METHOD.FEDSEC.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.FEDSON.ID, text: HC_RFC_LIST_SHIP_METHOD.FEDSON.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.FXINTE.ID, text: HC_RFC_LIST_SHIP_METHOD.FXINTE.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.INSTORE.ID, text: HC_RFC_LIST_SHIP_METHOD.INSTORE.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.PRIME.ID, text: HC_RFC_LIST_SHIP_METHOD.PRIME.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.SEE_BRANDON.ID, text: HC_RFC_LIST_SHIP_METHOD.SEE_BRANDON.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.UPS.ID, text: HC_RFC_LIST_SHIP_METHOD.UPS.TEXT });
			objShippingMethod.addSelectOption({value: HC_RFC_LIST_SHIP_METHOD.USPS.ID, text: HC_RFC_LIST_SHIP_METHOD.USPS.TEXT });

			objShippingMethod.isMandatory = false;

			// JLG - ADDED 04/25/2021
			var objCustomerAddress = form.addField({
				id: HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_ADDRESS.ID,
				type: serverWidget.FieldType.SELECT,
				label: HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_ADDRESS.LABEL,
				container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
			});	 

			objCustomerAddress.setHelpText({
				help : "Customer's address to be used"
			});
			var addresses = getCustomrAddresses();

			for(var i = 0; i < addresses.length; i++) {
				var label = ((addresses[i].default_shipping) ? '[DEFAULT SHIPPING] ' : '' ) + addresses[i].address_label;
				objCustomerAddress.addSelectOption({value: addresses[i].address_id, text: label });
			}

			objCustomerAddress.isMandatory = true;

			
			if(!lib.isEmpty(stCreateTrans))
				objCreateTrans.defaultValue = stCreateTrans;
			objCreateTrans.isMandatory = true;
			objCreateTrans.setHelpText({
			    help : HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.HELP
			});

			var fldMemo = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID,
                type: serverWidget.FieldType.TEXT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.LABEL,
                container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
            });
			fldMemo.defaultValue = stFieldMemo;
			fldMemo.setHelpText({
			    help : HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.HELP
			});
			

			var fldPages = form.addField({
                id: 'custparam_nsts_rfc_submit',
                type: serverWidget.FieldType.INLINEHTML,
                label: 'Buttons',
                container: HC_RFC_SUITELET_SHOW_TRANS.Field_Group.RESULTS.ID,
            });
			var fieldgroup = form.addTab({
			    id : '_nsts_rfc_hidden',
			    label : 'To Be hidden fields',
			});
			var fldLines = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_LINES.ID,
                type: serverWidget.FieldType.TEXT,
                label: 'Lines',
                container: '_nsts_rfc_hidden',
            });

			fldLines.updateDisplayType({displayType: serverWidget.FieldDisplayType.HIDDEN});
			var fldTrans = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TRANS.ID,
                type: serverWidget.FieldType.TEXT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TRANS.LABEL,
                container: '_nsts_rfc_hidden',
            });

			fldTrans.updateDisplayType({displayType: serverWidget.FieldDisplayType.HIDDEN});
            //current page
            var objFldBuildCurrPage = form.addField({
                id:HC_RFC_SUITELET_SHOW_TRANS.Fields.CURR_PAGE.ID,
                type: serverWidget.FieldType.INTEGER,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.CURR_PAGE.LABEL,
                container: '_nsts_rfc_hidden',
            });

            if(lib.isEmpty(intCurrentPage)) {
                intCurrentPage = 0;
            }
            objFldBuildCurrPage.defaultValue = intCurrentPage;

            objFldBuildCurrPage.updateDisplayType({displayType: serverWidget.FieldDisplayType.HIDDEN});
            //last page
            var objFldBuildLastPage = form.addField({
                id:HC_RFC_SUITELET_SHOW_TRANS.Fields.LAST_PAGE.ID,
                type: serverWidget.FieldType.INTEGER,
                label:HC_RFC_SUITELET_SHOW_TRANS.Fields.LAST_PAGE.LABEL,
                container: '_nsts_rfc_hidden',
            });

            if(!lib.isEmpty(intLastPage)) {

                objFldBuildCurrPage.defaultValue = intLastPage;
            }
            objFldBuildLastPage.updateDisplayType({displayType: serverWidget.FieldDisplayType.HIDDEN});
			var fldType = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TYPE.ID,
                type: serverWidget.FieldType.TEXT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.SELECTED_TYPE.LABEL,
                container: '_nsts_rfc_hidden',
            });

			fldType.updateDisplayType({displayType: serverWidget.FieldDisplayType.HIDDEN});
			
			var fldSelCaseType = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.CASE_SEL_TYPE.ID,
                type: serverWidget.FieldType.TEXT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.CASE_SEL_TYPE.LABEL,
                container: '_nsts_rfc_hidden',
            });
			fldSelCaseType.defaultValue = stCaseSelType;
			fldSelCaseType.updateDisplayType({displayType: serverWidget.FieldDisplayType.HIDDEN});
			var fldActionType = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.ACTION_TYPE.ID,
                type: serverWidget.FieldType.TEXT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.ACTION_TYPE.LABEL,
                container: '_nsts_rfc_hidden',
            });

			fldActionType.defaultValue = stActionType;
			fldActionType.updateDisplayType({displayType: serverWidget.FieldDisplayType.HIDDEN});
			var fldCustId = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.CUSTOMER_ID.ID,
                type: serverWidget.FieldType.TEXT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.CUSTOMER_ID.LABEL,
                container: '_nsts_rfc_hidden',
            });
			if(!lib.isEmpty(stCustomerId))
				fldCustId.defaultValue = stCustomerId;
			fldCustId.updateDisplayType({displayType: serverWidget.FieldDisplayType.HIDDEN});
			
			//MultiCurrency
			var flMultCurr = form.addField({
                id: HC_RFC_SUITELET_SHOW_TRANS.Fields.MULT_CURR.ID,
                type: serverWidget.FieldType.TEXT,
                label: HC_RFC_SUITELET_SHOW_TRANS.Fields.MULT_CURR.LABEL,
                container: '_nsts_rfc_hidden',
            });
			if(!lib.isEmpty(accountFeatures))
				flMultCurr.defaultValue = accountFeatures.bMulticurrency;
			flMultCurr.updateDisplayType({displayType: serverWidget.FieldDisplayType.HIDDEN});
			
			
			var stDisplay = HC_OTHERS.BTN_CSS +'<p style="text-align: left">';
			
			if(lib.isEmpty(stCaseSelType))
	        form.addButton({
	        	id: 'custpage_submit',
	            label: 'Search',
	            functionName: 'submitForm()'
	        });
			
	        if(lib.isEmpty(stCaseSelType))
		        form.addButton({
		        	id: 'custpage_edit',
		            label: 'Edit',
		            functionName: 'editForm()'
		        });
	        form.addButton({
	        	id: 'custpage_reset',
	            label: 'Cancel',
	            functionName: 'goBack()'
	        });
	        
	        form.addButton({
	        	id: 'custpage_createTrans',
	            label: 'Save',
	            functionName: 'createTrans()'
	        });
	        var stTableTree = lib.replaceAll('{tableid}','lot2',(HC_OTHERS.TABLE_TREE));
            if(((!lib.isEmpty(stTransNo) && !lib.isEmpty(stSearchType)) || (!lib.isEmpty(stTransFrom) && !lib.isEmpty(stTransTo) && !lib.isEmpty(stSearchType)))
	        		&& stActionType != 'edit'){

		        try{		        	
			        
			        var objSearch = loadAndFilterSearch(context,objPref,stTransFrom,stTransTo,stSearchType,stTransNo,accountFeatures);
			        var tableHeaders = '';
		            var arrColumns = [];
		            for (var i = 0; i < objSearch.columns.length; i++) {
		                var label = objSearch.columns[i].label;
		                var name = objSearch.columns[i].name;
		                var join = objSearch.columns[i].join;
		                if(name != 'lineuniquekey' && name != 'item')
		                tableHeaders += ('<th>' + (!lib.isEmpty(label) ? label : 'Column ' + (i+1)) + '</th>');
		            }
		            tableHeaders = '<th>SELECT</th>' + tableHeaders;
		            var intLineNo = 0;
		            var intPageNo = 0;
		            var intPageStart = 0;
		            if(!intCurrentPage)
		                intCurrentPage = 0;
		            log.error('tr');
					var objSearchTest = searchLoad({
		                id: objPref.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_SS),
		                filters: objSearch.filters,
		                columns: [search.createColumn({name: 'internalid', summary: search.Summary.COUNT})],
		                resultStart: 0,
		                resultLimit: 1
		            });
					var intRelTotal = 0;
					
					if(!lib.isEmpty(objSearchTest)){

						var objRelResult = objSearchTest.result;
						if(!lib.isEmpty(objRelResult)){
							var intRelTotal  = objRelResult[0].getValue({name: 'internalid', summary: search.Summary.COUNT});
						}
					}
					if(intRelTotal > 0 && intRelTotal <= intMaxSearchResults){

						var intMaxResults = parseInt(stRowResults);
						var intCntPages = Math.ceil(intRelTotal / intMaxResults);
						var objPageStartEnd = paginateArray(intCurrentPage, intMaxResults, intRelTotal);

						var objSearch = searchLoad({
							id: objPref.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_SS),
			                filters: objSearch.filters,
			                columns: objSearch.columns,
			                resultStart: objPageStartEnd.start,
			                resultLimit: objPageStartEnd.end
			            });
						var objRelResult = objSearch.result;
						var objSearch1 = objSearch.search;
						stTableTree +=  '<table id="lot2" width="100%">' + tableHeaders;
						var intLastPage = intCntPages - 1;
			            log.error('intLastPage',intLastPage);
			            log.error('intCurrentPage',intCurrentPage);
			            log.error('objSearch.columns.length',objSearch1.columns.length);
						objFldBuildPage.defaultValue = '<br><span style="font-size: 120% !important; font-weight: bold; ">Page ' + (parseInt(intCurrentPage)+1) + ' of ' + (parseInt(intLastPage)+1) + '</span>';
						var intLineNo = 1;
						log.error('tst1',objRelResult.length);
						for(cnt=0;cnt<objRelResult.length;cnt++){
							var result = objRelResult[cnt];
							var intId = result.id;
							log.error('intId',intId);
		            		var stLabelId = 'label-'+intId;
		            		var stResultType = result.recordType;
							log.error('stResultType',stResultType);
		            		if(!lib.isEmpty(stResultType))
		            			stResultType = stResultType.toLowerCase();
		            		stTableTree += '<tr id="rfc_tr-'+intId+'-'+stResultType+'">';
		            		stTableTree += ('<td " style="white-space: nowrap;"><input type="checkbox" id="myCheckbox"  /><label id="'+stLabelId+'" for="checkbox"></label></td>');                               

		            		for (var i = 0; i < objSearch1.columns.length; i++) {
		            			var fieldId = '_nsts_rfc_ss' + i;
		                        var stName = objSearch1.columns[i].name;
		                        var stJoin = objSearch1.columns[i].join;
								log.error('stName',stName);
								log.error('stJoin',stJoin);
								var stValue = '';
								try{

									if(stName == 'tranid'){									
										var output = url.resolveRecord({
														recordType: result.recordType,
														recordId: result.id,
														isEditMode: false
													});
										var stResRecType = (result.recordType).toUpperCase();
										stValue = "<a href='"+output+"' target='_blank'>"+ ' '+result.getValue({name: stName, join: lib.isEmpty(stJoin) ? '' : stJoin})+'</a>';
									} else {
		                                stValue = result.getText({name: stName, join: lib.isEmpty(stJoin) ? '' : stJoin});  
		                            }
		                            if(lib.isEmpty(stValue))
		                                stValue = result.getValue({name: stName, join: lib.isEmpty(stJoin) ? '' : stJoin});

		                            if(lib.isEmpty(stValue))
		                                stValue = ' ';
		                            if(!lib.isEmpty(stName)){
			                            if(!lib.isEmpty(stValue) && stName.indexOf('formulacurrency') == 0){
			                            	stValue = lib.getAmountWithCommas(stValue);
			                            }
		                            }
		                            if(stName != 'lineuniquekey' && stName != 'item')
		                            	stTableTree +=  '<td>' + stValue + '</td>';
		                            
								}catch(error){
									log.error('err',error.toString())
								}
		            		}
		                    stTableTree += '</tr>';
						}
						stTableTree += '</table>';
	                    if(intCntPages > 0) {
	                        objFldBuildPage.defaultValue = '<br><span style="font-size: 120% !important; font-weight: bold; ">Page ' + (parseInt(intCurrentPage)+1) + ' of ' + (parseInt(intLastPage)+1) + '</span>';
	                        
	                        var bFDisabled = (intCurrentPage <= 0);
	                        var bPDisabled = (intCurrentPage <= 0);
	                        var bNDisabled = (intCurrentPage >= intLastPage);
	                        var bLDisabled = (intCurrentPage >= intLastPage);
	                        var stRetFields =  'javascript:document.forms[0].'+HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID+".value =nlapiGetFieldValue('"+HC_RFC_SUITELET_SHOW_TRANS.Fields.DATE_CREATE.ID+"')"+';' +
	                        'javascript:document.forms[0].'+HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID+".value =nlapiGetFieldValue('"+HC_RFC_SUITELET_SHOW_TRANS.Fields.CREATION_ACTION.ID+"')"+';'+
	                        'javascript:document.forms[0].'+HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID+".value =nlapiGetFieldValue('"+HC_RFC_SUITELET_SHOW_TRANS.Fields.DEFAULT_MEMO.ID+"')"+';'+
	                        'javascript:document.forms[0].'+HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID+".value =nlapiGetFieldValue('"+HC_RFC_SUITELET_SHOW_TRANS.Fields.SHIP_METHOD.ID+"')"+';'+
	                        'javascript:window.ischanged = false;';
	                        objFldPages.defaultValue ='<a id="'+( bFDisabled ? 'btnDisabled' : 'btnEnabled') +'" href="' + (bFDisabled ? 'javascript:void(0)': 'javascript:document.forms[0].custpage_nsts_rfc_curr_page.value = 0; '+stRetFields+ 'javascript:document.forms[0].submit();') + '"><< First Page</a>' +
	                        '<a id="'+( bPDisabled ? 'btnDisabled' : 'btnEnabled') +'" href="' + (bPDisabled ? 'javascript:void(0)': 'javascript:document.forms[0].custpage_nsts_rfc_curr_page.value = '+(parseInt(intCurrentPage)-1)+'; '+stRetFields+' javascript:document.forms[0].submit();') + '">< Previous Page </a>' +
	                        '<a id="'+( bNDisabled ? 'btnDisabled' : 'btnEnabled') +'" href="' + (bNDisabled ? 'javascript:void(0)': 'javascript:document.forms[0].custpage_nsts_rfc_curr_page.value = '+(parseInt(intCurrentPage)+1)+';'+stRetFields+'javascript:document.forms[0].submit();') + '">Next Page ></a>' +
	                        '<a id="'+( bLDisabled ? 'btnDisabled' : 'btnEnabled') +'" href="' + (bLDisabled ? 'javascript:void(0)': 'javascript:document.forms[0].custpage_nsts_rfc_curr_page.value = '+intLastPage+';'+stRetFields+' javascript:document.forms[0].submit();') + '">Last Page >></a>' ;
	                    } 
					}else if(intRelTotal > intMaxSearchResults){


		                stTableTree += '<table  id="lot2" width="100%"><tr><th colspan=7>Status</th></tr><tr><td colspan=7>Max number of results (75,000) is reached. Please adjust filters to trim down number of results.</td></tr>';
		                objFldBuildPage.defaultValue = '<span style="font-size: 120% !important; font-weight: bold;">Page 0 of 0</span>';
		            	stTableTree += '</table>';
			        	objResults.defaultValue = stTableTree;
			        	objFldPages.defaultValue = stDisplay+'<a id="btnDisabled" href="javascript:void(0)"><< First Page</a>' +
		                '<a id="btnDisabled" href="javascript:void(0)">< Previous Page </a>' +
		                '<a id="btnDisabled" href="javascript:void(0)">Next Page ></a>' +
		                '<a id="btnDisabled" href="javascript:void(0)">Last Page >></a>';
		                objFldBuildPage.defaultValue = '<span style="font-size: 120% !important; font-weight: bold;">Page 0 of 0</span>';
			            stActionType = 'edit';
					}else{


		                stTableTree += '<table  id="lot2" width="100%"><tr><th colspan=7>Status</th></tr><tr><td colspan=7>No records to show.</td></tr>';
		                objFldBuildPage.defaultValue = '<span style="font-size: 120% !important; font-weight: bold;">Page 0 of 0</span>';
		            	stTableTree += '</table>';
			        	objResults.defaultValue = stTableTree;
			        	objFldPages.defaultValue = stDisplay+'<a id="btnDisabled" href="javascript:void(0)"><< First Page</a>' +
		                '<a id="btnDisabled" href="javascript:void(0)">< Previous Page </a>' +
		                '<a id="btnDisabled" href="javascript:void(0)">Next Page ></a>' +
		                '<a id="btnDisabled" href="javascript:void(0)">Last Page >></a>';
		                objFldBuildPage.defaultValue = '<span style="font-size: 120% !important; font-weight: bold;">Page 0 of 0</span>';
			            stActionType = 'edit';
		            }
		            objResults.defaultValue = stTableTree;

		        }catch(error){

		        	objResults.defaultValue = 'There was an error encountered upon generating the results. Please check your saved search settings.';
		        	objFldPages.defaultValue = stDisplay+'<a id="btnDisabled" href="javascript:void(0)"><< First Page</a>' +
	                '<a id="btnDisabled" href="javascript:void(0)">< Previous Page </a>' +
	                '<a id="btnDisabled" href="javascript:void(0)">Next Page ></a>' +
	                '<a id="btnDisabled" href="javascript:void(0)">Last Page >></a>';
	                objFldBuildPage.defaultValue = '<span style="font-size: 120% !important; font-weight: bold;">Page 0 of 0</span>';
		            stActionType = 'edit';
					//fldPages.defaultValue = stDisplay+'<a id="btnDisabled" href="javascript:void(0);"> Save </a>';
		            log.error('err',error.toString());
		        }		

	        }else{
                stTableTree += '<table  id="lot2" width="100%"><tr><th colspan=7>Status</th></tr><tr><td colspan=7>No records to show.</td></tr>';
                objFldBuildPage.defaultValue = '<span style="font-size: 120% !important; font-weight: bold;">Page 0 of 0</span>';
            	stTableTree += '</table>';

            	objFldPages.defaultValue = stDisplay+'<a id="btnDisabled" href="javascript:void(0)"><< First Page</a>' +
                '<a id="btnDisabled" href="javascript:void(0)">< Previous Page </a>' +
                '<a id="btnDisabled" href="javascript:void(0)">Next Page ></a>' +
                '<a id="btnDisabled" href="javascript:void(0)">Last Page >></a>';
	            objResults.defaultValue = stTableTree;
				//fldPages.defaultValue = stDisplay+'<a id="btnDisabled" href="javascript:void(0);"> Save </a>';
	            stActionType = 'edit';
	        }

	        if(stActionType != 'edit'){				
				
	        	objTransTo.updateDisplayType({displayType: serverWidget.FieldDisplayType.INLINE});
	        	objTransFrom.updateDisplayType({displayType: serverWidget.FieldDisplayType.INLINE});
	        	objTransNo.updateDisplayType({displayType: serverWidget.FieldDisplayType.INLINE});
	        	objType.updateDisplayType({displayType: serverWidget.FieldDisplayType.INLINE});

	        }

			return form;
	    }
		
		/************************* Load search and add filters ******************************************************/
		function loadAndFilterSearch(context,objPref,stTransFrom,stTransTo,stSearchType,stTransNo,accountFeatures){
			
			
			if(!lib.isEmpty(stTransNo)){
				stTransNo = stTransNo.trim();
			}
			//Load Original Saved Search
			var objOrigSearch = search.load({
		        id: objPref.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_SS)
		    });
			
			//Add Filters to the Original Saved Search
			var sfilters = objOrigSearch.filters;
			
			if(!lib.isEmpty(stTransNo)){				
				sfilters.push(  search.createFilter({
					name: 'numbertext',
					operator: search.Operator.IS,
					values:[stTransNo]
				}));
			}
			if(!lib.isEmpty(stTransFrom) && !lib.isEmpty(stTransTo) && lib.isEmpty(stTransNo)){
				stTransFrom = new Date(stTransFrom);
				stTransTo = new Date(stTransTo);
				stTransFrom = format.format({
	                value: stTransFrom,
	                type: format.Type.DATE
	            });
				stTransTo = format.format({
	                value: stTransTo,
	                type: format.Type.DATE
	            });
				
				sfilters.push(  search.createFilter({
					name: 'trandate',
					operator: search.Operator.WITHIN,
					values:[stTransFrom,stTransTo]
				}));
			}
			if(!lib.isEmpty(stSearchType)){
				if(stSearchType == HC_RFC_LIST_TRANSACTION_TYPE.INVOICE){
					sfilters.push(  search.createFilter({
						name: 'recordtype',
						operator: search.Operator.IS,
						values: ['invoice']
					}));
				}else{
					sfilters.push(  search.createFilter({
						name: 'recordtype',
						operator: search.Operator.IS,
						values: ['salesorder']
					}));
				}
			}
			
			//Add Customer Filter
			if(!lib.isEmpty(stCustomerId))
			sfilters.push(  search.createFilter({
				name: 'internalidnumber',
				operator: search.Operator.EQUALTO,
				join: 'customer',
				values:parseInt(stCustomerId)
			}));
			var objCustColumns = objOrigSearch.columns;
			var objCustColumns = [];
			var objSearchColumns = objOrigSearch.columns;
			for(var i=0;i<objSearchColumns.length;i++){
				var stName = objSearchColumns[i].name;
				if(stName.indexOf('formula') > -1  && (objSearchColumns[i].formula)){
					stName = stName+'_'+i;
					
					if(accountFeatures.bMulticurrency){

						objCustColumns.push( search.createColumn({
							 name: stName,
							 formula: objSearchColumns[i].formula,
							 join: objSearchColumns[i].join,
							 label: objSearchColumns[i].label,
							 summary: objSearchColumns[i].summary,
							 sort: objSearchColumns[i].sort,
							 function: objSearchColumns[i].function						 
							 })
						 );
					}else{

						
						var stFormula = objSearchColumns[i].formula;
						if(!lib.isEmpty(stFormula)){

							stFormula = lib.replaceAll('fxamount','amount',stFormula);
							objCustColumns.push( search.createColumn({
								 name: stName,
								 formula: stFormula,
								 join: objSearchColumns[i].join,
								 label: objSearchColumns[i].label,
								 summary: objSearchColumns[i].summary,
								 sort: objSearchColumns[i].sort,
								 function: objSearchColumns[i].function						 
								 })
							 );
						}
					}
					
				}else{
					objCustColumns.push(objSearchColumns[i]);
				}
			}

		    var objCustSearch = search.create({
		        type: objOrigSearch.searchType,
				filters: sfilters,
				columns: objCustColumns
		    });
		    return objCustSearch;
		    
		}

		function searchLoad(option){
			if(lib.isEmpty(option)){
				return null;
			}
			
			var objSearch = search.load({
				id: option.id,
			});
			
			if(!lib.isEmpty(option.filters)){
				objSearch.filters = option.filters;
			}
			if(!lib.isEmpty(option.columns)){
				objSearch.columns = option.columns;
						   
			}
			if(!lib.isEmpty(objSearch.columns)){
				for(var i = 0; i < objSearch.columns.length; i++){

					
					if(!lib.isEmpty(objSearch.columns[i].formula)){
						//log.debug('SEARCHLOAD',JSON.stringify(objSearch.columns[i]));
						var stName = objSearch.columns[i].name;
						var stSummary = objSearch.columns[i].summary;
						var stFormula = objSearch.columns[i].formula;
						var stJoin = objSearch.columns[i].join;
						var stFunction = objSearch.columns[i]['function'];
						var stSort = objSearch.columns[i].sort;
						var stSortdir = objSearch.columns[i].sortdir;
						var stLabel = objSearch.columns[i].label;
						var stLabelId = isEmptyReplaceWith(stLabel, '');
							stLabelId = stLabelId.replace(/\s/g,'');
							stLabelId = stLabelId.toLowerCase();

						objSearch.columns[i] = search.createColumn({
							name: stName + "_" + stLabelId + (i+1),
							summary: stSummary,
							join: stJoin,
							label: stLabel,
							'function': stFunction,
							formula: stFormula,
							sort: stSort,
							sortdir: stSortdir
						});
							
						//log.debug('SEARCHLOAD #2',JSON.stringify(objSearch.columns[i]));
					}
				}
			}

			
			if(!lib.isEmpty(option.addFilters)){
				var arrFil = objSearch.filters;
				arrFil = lib.isEmpty(arrFil)? []: arrFil;
				
				objSearch.filters = arrFil.concat(option.addFilters);
			}
			
			if(!lib.isEmpty(option.addColumns)){
				var arrCol = objSearch.columns;
				arrCol = lib.isEmpty(arrCol)? []: arrCol;
				
				objSearch.columns = arrCol.concat(option.addColumns);
			}
			
			log.debug('fddcol', JSON.stringify(objSearch.columns));
			var arrResult = [];
			var intStart  = 0;
			if(!lib.isEmpty(option.resultStart))
				intStart = option.resultStart;
		  
			if(!lib.isEmpty(option.resultLimit)){

				option.resultLimit = parseInt(option.resultLimit);
				option.resultLimit = (option.resultLimit <= 0 )? 1000: option.resultLimit;
				arrResult = objSearch.run().getRange(intStart,option.resultLimit);
			}else{
				arrResult = searchGetAllResultSrchObj(objSearch, option);
			}
			
			log.debug("SEARCHLOAD filters", JSON.stringify(objSearch.filters));
			log.debug("SEARCHLOAD arrResult", JSON.stringify(arrResult));
			
			return {
				search: objSearch,
				result: arrResult,
			}
		}

		function paginateArray(page, displayItemCount, resultSet){
			displayItemCount = parseInt(displayItemCount);
			var len = resultSet;
			var cntPages = Math.ceil(len / displayItemCount);
			if (page > cntPages) return null;

			var pageResults = null;
			var start = 0;
			var end = displayItemCount;
			
			if(page > 0)
			{
				start = (page * displayItemCount);
				end = start + displayItemCount;

			}

			return {
				start: start,
				end  : end
			};
		}
		function isEmptyReplaceWith(object,defaultValue){
			if(lib.isEmpty(object)){
				return defaultValue;
			}
			return object;
		}
        return {
            onRequest: searchTransSuitelet
        };

    });