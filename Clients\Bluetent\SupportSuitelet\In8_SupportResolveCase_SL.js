function suitelet(request, response)
{

    try
    {
        if(request.getMethod() == "GET")
        {
            var caseId = request.getParameter('caseid');

            if(!caseId)
            {
                throw nlapiCreateError("INVALID","Invalid parameters");
            }

            nlapiSubmitField('supportcase', caseId, 'status', 5);

        }

        response.write(JSON.stringify({status: true, message: "Case resolved!"}));
    }
    catch(e)
    {
        response.write(JSON.stringify({status: false, message: e.getDetails()}));
    }
}