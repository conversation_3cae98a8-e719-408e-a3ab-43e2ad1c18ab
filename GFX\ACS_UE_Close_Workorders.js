/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search'], function(record, search) {
    
    function checkIfNotClosed(woId) {
        var workOrderStatus = search.lookupFields({
            type: search.Type.WORK_ORDER,
            id: woId,
            columns: [
                'status'
            ]
        });


        if(workOrderStatus.status[0].text == "Closed"){
            return true;
        } 

        return false;

    }

    function closeWorkOrder(woId) {

        // loop work order items and close individually
        try {

            if(woId != ""){
                var isClosed = checkIfNotClosed(woId);
                if(!isClosed){
                    
                    var woObj = record.load({
                        type: record.Type.WORK_ORDER,
                        id: woId
                    });
                    
                    var woLineCount = woObj.getLineCount({ sublistId: 'item' });

                    // loop through work order items and close individually
                    for(var j = 0; j < woLineCount; j++){
                        woObj.setSublistValue({
                            sublistId: 'item',
                            fieldId: 'isclosed',
                            line: j,
                            value: true
                        });
                    }
                    
                    woObj.save();
                }
            }
            
        } catch (e) {
            log.error('Error', e);
        }
    }

    function beforeLoad(context) {

        var recObj = context.newRecord;
        var recordStatus = recObj.getValue({ fieldId: 'status' });
        if(context.type == context.UserEventType.VIEW && recordStatus == 'Closed'){

            // get item sublist work orders
            var lineCount = recObj.getLineCount({ sublistId: 'item' });

            for(var i = 0; i < lineCount; i++){

                // load work order
                var woId = recObj.getSublistValue({
                    sublistId: 'item',
                    fieldId: 'woid',
                    line: i
                });

                closeWorkOrder(woId);
            }

        } 
    }

    function afterSubmit(context) {
        log.debug('afterSubmit', 'Event Type - EDIT');
        if (context.type == context.UserEventType.EDIT) {

            var recObj = context.newRecord;

            // get closed item sublists work orders
            var lineCount = recObj.getLineCount({ sublistId: 'item' });

            for(var i = 0; i < lineCount; i++){

                // check if line is closed
                var isClosed = recObj.getSublistValue({
                    sublistId: 'item',
                    fieldId: 'isclosed',
                    line: i
                });

                if(isClosed || isClosed === 'T'){

                    // load work order
                    var woId = recObj.getSublistValue({
                        sublistId: 'item',
                        fieldId: 'woid',
                        line: i
                    });

                    log.debug('afterSubmit', { isClosed: isClosed });


                    closeWorkOrder(woId);
                }
            }
        }
    }

    return {
        beforeLoad: beforeLoad,
        afterSubmit: afterSubmit
    }
});