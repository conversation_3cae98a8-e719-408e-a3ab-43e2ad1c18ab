<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="3"><span style="font-size:28px;"><strong>STATEMENT</strong></span><br /><br /><span
                                class="nameandaddress">${companyInformation.companyName}</span><br /><span
                                class="nameandaddress">${companyInformation.addressText}</span><br />www.room.com<br />+1-646-791-3726
                        </td>
                        <td align="right"><img src="https://5664336-sb1.app.netsuite.com/core/media/media.nl?id=4291&amp;c=5664336_SB1&amp;h=1303b1121ee969cd11a1" style="float: left; margin: 7px; width: 100px; height: 90px;" /></td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0px;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 5px;
                padding-top: 5px;
                font-size: 7pt;
            }
            
            table.itemtable td {
                font-size: 6pt;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0px;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 20px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="12%" footer="nlfooter" footer-height="1pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        &nbsp;
        <#setting number_format=",##0.00">
        <#assign aDateTime = .now>
        <#assign date = aDateTime?date>
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td align="right">Statement Date: ${date?string.long}</td>
            </tr>
        </table>
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader" colspan="3"><b>Customer</b></td>
                <td class="addressheader" colspan="3"></td>
                <td class="totalboxtop" colspan="5"><b>TOTAL</b></td>
            </tr>
            <tr>
                <td class="address" colspan="3" rowspan="2">
                    ${cust_invoices.entity} <br /> ${cust_invoices.cust_address}</td>
                <td class="address" colspan="3" rowspan="2"></td>
                <td align="right" class="totalboxmid" colspan="5">${cust_invoices.total_amt}</td>
            </tr>
            <tr>
                <td align="right" class="totalboxbot" colspan="5"></td>
            </tr>
        </table>
        <table class="itemtable" style="width: 100%; margin-top: 10px;">
            <!-- start items -->
            <#list cust_invoices.invoices as invoice>
                <#if invoice_index==0>
                    <thead>
                        <tr>
                            <th colspan="2">TRANSACTION</th>
                            <th>DATE</th>
                            <th>CREATED FROM</th>
                            <th>PO #</th>
                            <th>DUE DATE</th>
                            <th align="right">INV. AMT</th>
                            <th align="right">BALANCE</th>
                            <th align="right">TOTAL</th>
                        </tr>
                    </thead>
                </#if>
                <tr>
                    <td>${invoice.type}</td>
                    <td>${invoice.tranid}</td>
                    <td>${invoice.trandate}</td>
                    <td>${invoice.createdfrom}</td>
                    <td>${invoice.otherrefnum}</td>
                    <td>${invoice.duedate}</td>
                    <td align="right">${invoice.amount}</td>
                    <td align="right">${invoice.amountremaining}</td>
                    <td align="right">${invoice.runningbal}</td>
                </tr>
            </#list><!-- end items -->
        </table>
        <table class="itemtable" style="width: 100%; margin-top: 10px;">
            <thead>
                <tr>
                    <th align="right">Current</th>
                    <th align="right">1-30 Days</th>
                    <th align="right">31-60 Days</th>
                    <th align="right">61-90 Days</th>
                    <th align="right">Over 90 Days</th>
                    <th align="right">Amount Due</th>
                </tr>
            </thead>
            <tr>
                <td align="right">${cust_invoices.aging.one}</td>
                <td align="right">${cust_invoices.aging.two}</td>
                <td align="right">${cust_invoices.aging.three}</td>
                <td align="right">${cust_invoices.aging.four}</td>
                <td align="right">${cust_invoices.aging.five}</td>
                <td align="right">${cust_invoices.total_amt}</td>
            </tr>
        </table>
        <table class="total" style="width: 100%; margin-top: 10px;">
            <tr>
                <td rowspan="7" style="width: 430px; background-color: rgb(255, 255, 255);">Wire/ACH
                    instruction:<br />Account Name: Phonebooths Inc.<br />Account Address: 599 Broadway, Floor 9, New
                    York, NY 10012<br />Account No.: **********<br />Bank Name: Silicon Valley Bank<br />Bank Address:
                    3003 Tasman Drive, Santa Clara, CA 95054<br />Routing No. (Domestic): *********<br />SWIFT Code
                    (International): SVBKUS6S</td>
                <td align="right" style="width: 179px;"></td>
                <td align="right" style="width: 155px;"></td>
            </tr>
            <tr>
                <td align="right" style="width: 179px;"></td>
                <td align="right" style="width: 155px;"></td>
            </tr>
            <tr>
                <td align="right" style="width: 179px;"></td>
                <td align="right" style="width: 155px;"></td>
            </tr>
            <tr>
                <td align="right" style="width: 179px;"></td>
                <td align="right" style="width: 155px;"></td>
            </tr>
        </table>
        &nbsp;
    </body>
</pdf>