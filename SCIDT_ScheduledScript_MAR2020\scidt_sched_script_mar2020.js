/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       23 May 2020     jdgonzal
 *
 */

/**
 * @param {String} type Context Types: scheduled, ondemand, userinterface, aborted, skipped
 * @returns {Void}
 */
function scheduled(type) {
    var search = nlapiSearchRecord('employee');
    for(var i = 0; i < search.length; i++){
        nlapiLoadRecord('employee', 2);
        var remainingUsage = nlapiGetContext().getRemainingUsage();
        nlapiLogExecution('DEBUG', 'REMAINING USAGE', remainingUsage);
        if(remainingUsage < 9980){
            nlapiLogExecution('DEBUG', 'Yielding', remainingUsage);
            var status = nlapiYieldScript().status;
            nlapiLogExecution('DEBUG', 'Yield Status', status);
        }
    }
}
