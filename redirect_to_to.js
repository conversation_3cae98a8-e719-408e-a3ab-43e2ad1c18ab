/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord', 'N/url'], function(currentRecord, url) {

    function pageInit(context){
    }

    function redirectToTO(){
        rec = currentRecord.get();
        var outputURL = url.resolveDomain({
            hostType: url.HostType.APPLICATION
        });
        outputURL += '/app/accounting/transactions/trnfrord.nl?whence=&item_receipt_id=' + rec.id;
        // console.log('https://' + outputURL);
        window.open('https://' + outputURL);
    }    

    return {
        pageInit : pageInit,
        redirectToTO : redirectToTO
    }
});
