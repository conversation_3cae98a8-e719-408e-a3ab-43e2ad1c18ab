/**
 *@NApiVersion 2.1
 *@NScriptType Suitelet
 */
define(['N/record', 'N/search'], function (record, search) {

    function onRequest(context) {

        let emp_name = context.request.parameters.emp_name;

        log.debug('test', emp_name);

        let employeeSearchObj = search.create({
            type: "employee",
            filters: [
                ["entityid", "is", emp_name]
            ],
            columns: [
                search.createColumn({
                    name: "entityid",
                    label: "Name"
                }),
                search.createColumn({
                    name: "mobilephone",
                    label: "Mobile Phone"
                })
            ]
        });

        let emp_phone_number;
            employeeSearchObj.run().each(function (result) {
                emp_phone_number = result.getValue({ name: "mobilephone" });
                return true;
            });

        let returnStr = "<#assign sales_rep_number =" + emp_phone_number + " />";

        context.response.write({
            output: returnStr
        });

    }

    return {
        onRequest: onRequest
    }
});
