/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define([], function() {

    function getInputData() {

        var x = {
            test1_prop: 'test1 contet',
            test2_prop: 'test2 contet',
            test3_prop: 'test3 contet',
        }

        for (var prop in x) {
            if (x.hasOwnProperty(prop)) {
                log.debug(prop, x.prop);
            }
        }
        
    }

    function map(context) {
        
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        summarize: summarize
    }
});
