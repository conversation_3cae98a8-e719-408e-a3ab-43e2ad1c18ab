/**
 * @NLApiVersion 2.x
 * @NScriptType ClientScript
 */
require(['N/runtime'], function(runtime) {

    // alert("Your account ID is: " + runtime.accountId);
    // alert("Exec context is: " + runtime.executionContext);
    // alert("Environment Type: " + runtime.envType);
    // alert("Version: " + runtime.version);
    alert("Your name is " + runtime.getCurrentUser().name);

});



if(true){
    alert('hahaha');
} else {
    alert('hehehe');
}

if(true)
{
    if(false)
    {
        alert('hehehe');
    }
}
else 
{
    alert('hahaha');
}