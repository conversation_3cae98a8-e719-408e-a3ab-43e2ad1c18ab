/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function afterSubmit(context) {
        // var recObj = record.load({
        //     id: context.newRecord.id,
        //     type: record.Type.SALES_ORDER,
        //     isDynamic: true
        // });

        var cashSalesObj = record.transform({
            fromType: record.Type.SALES_ORDER,
            fromId: context.newRecord.id,
            toType: record.Type.CASH_SALE,
            isDynamic: true 
        });
        cashSalesObj.save({ enableSourcing: true, ignoreMandatoryFields: true });
    }

    return {
        afterSubmit: afterSubmit
    }
});
