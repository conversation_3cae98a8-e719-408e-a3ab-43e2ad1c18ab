<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td style="width: 189px;">
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    style="float: left; margin: 12px; width: 130px; height: 100px;" /> </#if>
                        </td>
                        <td align="center" style="font-size: 40px; width: 623px;">
                            <br /><b>Picking Ticket</b></td>
                    </tr>
                </table>
                <table class="body" style="width: 100%; margin-top: 0px; margin-bottom:10px; font-size: large;">
                    <tr>
                        <td style="background-color: rgb(0, 0, 0); color: white; width: 240px;">Sales Order</td>
                        <td style="background-color: rgb(0, 0, 0); color: white; width: 189px;">Purchase Order</td>
                        <td style="background-color: rgb(0, 0, 0); color: white; width: 222px;">Ship Date</td>
                        <td style="width: 97px;">&nbsp;</td>
                    </tr>
                    <tr>
                        <td border-bottom="1px" border-left="1px" border-right="1px"
                            style="width: 240px; height: 19px;">${record.tranid}</td>
                        <td border-bottom="1px" border-right="1px" style="width: 189px; height: 19px;">
                            ${record.otherrefnum}</td>
                        <td border-bottom="1px" border-right="1px" style="width: 222px; height: 19px;">
                            ${record.shipdate}</td>
                        <td style="width: 97px; height: 19px;">&nbsp;</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td align="center"><b>${record.createdfrom.location}</b></td>
                    </tr>
                    <tr>
                        <td align="center">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="20%" footer="nlfooter" footer-height="10pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader" style="height: 25px; font-size: large;"><b>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                        &nbsp; &nbsp; &nbsp; &nbsp;Bill To:</b></td>
                <td class="addressheader" style="height: 25px; font-size: large;"><b>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                        &nbsp; &nbsp; &nbsp; &nbsp;Ship To:</b></td>
            </tr>
            <tr>
                <td align="center" class="address" style="height: 35px; font-size: large;">${record.billaddress}</td>
                <td align="center" class="address" style="height: 35px; font-size: large;">${record.shipaddress}</td>
            </tr>
        </table>
        <#assign classArray=[]>
        <#list record.item as tranline>
            <#if classArray?seq_index_of(tranline.class)==-1 && tranline.itemtype !="Group" && tranline.itemtype !="NonInvtPart">
                <#assign classArray +=[tranline.class]>
            </#if>
        </#list>

        <#list classArray as class>

            <table style="margin-bottom:0;">
                <tr>
                    <td style="font-weight:bold; font-size:18;">${class}</td>
                </tr>
            </table>
            <table class="itemtable" border="2px" style="width: 100%; margin-bottom: 10px; font-size: 10;">
                <#assign _seq=[]>
                <#assign _kitPickTicketQty=0>
                <#assign _kitActualQty=0>
                <#assign _isKit=false>
                <#list record.item as firstItem>
                    <#assign _qty=0>
                        <#if _seq?seq_index_of(firstItem.item)==-1>
                            <#list record.item as secondItem>
                                <#if firstItem.item==secondItem.item>
                                    <#if _isKit>
                                        <#assign _qty += secondItem.quantity>
                                    <#else>
                                        <#assign _qty += secondItem.custcol_pick_ticket_qty>
                                    </#if>
                                </#if>
                            </#list>
                            <#assign _seq=_seq + [firstItem.item]>
                            <#if firstItem_index==0>
                                <thead>
                                    <tr>
                                        <td colspan="2" border-bottom="1px" border-right="1px"><b>Item</b></td>
                                        <td colspan="3" border-bottom="1px" border-right="1px"><b>Description</b></td>
                                        <td border-bottom="1px" border-right="1px"><b>Type</b></td>
                                        <td align="center" border-bottom="1px"><b>Qty</b></td>
                                    </tr>
                                </thead>
                            </#if>
                            <#if firstItem.itemtype !="Kit" && firstItem.class==class && firstItem.itemtype !="Group" && firstItem.itemtype !="NonInvtPart">
                                <#if _isKit>
                                    <#assign _qty = (_qty / _kitActualQty) * _kitPickTicketQty>
                                </#if>
                                <tr>
                                    <td colspan="2" border-bottom="1px" border-right="1px"> ${firstItem.item}</td>
                                    <td colspan="3" border-bottom="1px" border-right="1px"><span class="itemdescription">${firstItem.custcol_stock_de}</span></td>
                                    <td border-bottom="1px" border-right="1px"><span class="itemdescription">${firstItem.itemtype}</span></td>
                                    <td align="center" border-bottom="1px">${_qty}</td>
                                </tr>
                            <#elseif firstItem.itemtype=="Kit">
                                <#assign _kitActualQty = firstItem.quantity>
                                <#assign _kitPickTicketQty = _qty>
                                <#assign _isKit=true>
                            <#else>
                                <#assign _isKit=false>
                            </#if>
                        </#if>
                </#list>
            </table>
        </#list>
    </body>
</pdf>