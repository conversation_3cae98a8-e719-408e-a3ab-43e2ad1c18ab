/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search','N/file', 'N/runtime', 'N/log', 'N/record'], function(search, file, runtime, log, record) {

    function getInputData() {
        var stLogTitle = 'getInputData';
        try {
            log.debug(stLogTitle, '** START **');

            var scriptObj = runtime.getCurrentScript();
            var fileID = scriptObj.getParameter('custscript_nsacs_csv_file');
            log.debug(stLogTitle, 'fileID: ' + fileID);

            var fileObj = file.load({
                id: fileID
            });
            return fileObj;

        } catch (error) {
            log.error(stLogTitle, 'error: ' + error);
        }
    }

    function map(context) {
        try{
            var stLogTitle = 'map';
            log.debug('TEST', context);
            var csvLines = context.value;
            log.debug(stLogTitle, 'csvLines: ' + csvLines);
            var csvArray = csvLines.toString().split(',');
            log.debug(stLogTitle, 'Key: ' + context.key);
            log.debug(stLogTitle, 'csvArray: ' + JSON.stringify(csvArray));
            log.debug(stLogTitle, 'Document #: ' + csvArray[1]);

            if (csvLines.indexOf('Total') > -1) {
                log.debug(stLogTitle, 'LAST LINE');
                return;
            } else {
                processFlag(csvArray[1]);
                context.write({
                    key: context.key,
                    value: csvLines
                });
            }
        } catch (e) {
            log.debug("Error", e);
        }
    }

    function processFlag(docNo){
        try{
            var transactionSearchObj = search.create({
                type: "transaction",
                filters:
                [
                ["numbertext","is",docNo]
                ],
                columns:
                [
                "internalid"
                ]
            });
            var searchResultCount = transactionSearchObj.runPaged().count;
            log.debug("transactionSearchObj result count",searchResultCount);

            var internalID;
            transactionSearchObj.run().each(function(result){
                internalID = result.id;
                return true;
            });

            // record.submitFields({
            //     type: record.Type.VENDOR_PAYMENT,  
            //     id: internalID,
            //     values: {
            //         custbody_nsacs_csv_export_status: 2
            //     },
            //     options: {
            //         enableSourcing: false,
            //         ignoreMandatoryFields : true
            //     }
            // });
        } catch (e) {
            log.debug("Error", e);
        }
    }

    function reduce(context) {
        
    }

    function summarize(summary) {
        log.debug('summarize');
        // var stLogTitle = 'summarize';
    

        // var newContent = '';
        // summary.output.iterator().each(function (key, value) {
        //     log.debug(stLogTitle, 'value: ' + value);
        //     newContent += value + '\n';
        //     return true;
        // });

        // log.debug('newContent', newContent);

        // var dateTime = getFormattedTime();

        // successFile = file.create({
        //     name: 'GGH_' + dateTime + '.csv',
        //     contents: newContent,
        //     folder: 643,
        //     fileType: 'CSV'
        // });
        // var fileID = successFile.save();
        // log.debug(stLogTitle, 'fileID: ' + fileID);
        // file.delete({
        //     id: runtime.getCurrentScript().getParameter('custscript_nsacs_csv_file')
        // });
    }

    function getFormattedTime() {
        var today = new Date();
        var y = today.getFullYear();
        // JavaScript months are 0-based.
        var m = today.getMonth() + 1;
        var d = today.getDate();
        var h = today.getHours();
        var mi = today.getMinutes();
        var s = today.getSeconds();
        return m + "-" + d + "-" + y + "_" + h + "-" + mi + "-" + s;
    }

    return {
        getInputData: getInputData,
        map: map,
        // reduce: reduce,
        summarize: summarize
    }
});
