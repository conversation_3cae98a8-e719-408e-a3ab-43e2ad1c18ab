/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       23 May 2020     jdgonzal
 *
 */

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response){
    if(request.getMethod() == 'GET'){
        var soId = request.getParameter('salesorder');

        var r = nlapiPrintRecord('PICKINGTICKET', soId, 'DEFAULT');

        response.setContentType(r.getType(), 'transaction_' + soId + '.pdf', 'inline');
        response.write(r.getValue());
    }

}
