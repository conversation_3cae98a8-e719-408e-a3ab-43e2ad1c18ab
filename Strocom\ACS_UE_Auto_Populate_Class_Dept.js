/**
 *@NApiVersion 2.1
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search', 'N/runtime'], function(record, search, runtime) {

    function beforeLoad(context) {
        
        log.debug('execContext', runtime.executionContext);
        log.debug('contextType', context.type);
        
        var formObj = context.form;

        var revElementSublist = formObj.getSublist({ id: 'revenueelement'});
        var classField = revElementSublist.getField({ id: 'class' });
        var departmentField = revElementSublist.getField({ id: 'department' });
        classField.isMandatory = false;
        departmentField.isMandatory = false;
    }

    function beforeSubmit(context) {
        log.debug('execContext', runtime.executionContext);
        log.debug('contextType', context.type);
        try {
            if(context.type == context.UserEventType.CREATE || context.type == context.UserEventType.EDIT){
                var recObj = context.newRecord;
                var lineCount = recObj.getLineCount({ sublistId: 'revenueelement' });
                log.debug('Debugging', {
                    recObj: recObj.id,
                    lineCount: lineCount
                });
                for(var i = 0; i < lineCount; i++) {
                    var jobId = recObj.getSublistValue({ fieldId: 'customer', sublistId: 'revenueelement', line: i});
                    var searchObj = search.lookupFields({
                        type: search.Type.JOB,
                        id: jobId,
                        columns: ['custentity_class', 'custentity_department']
                    });

                    var _class = ((searchObj.hasOwnProperty('custentity_class')) ? searchObj.custentity_class[0].value : '');
                    var _dept = ((searchObj.hasOwnProperty('custentity_department')) ? searchObj.custentity_department[0].value : '');

                    recObj.setSublistValue({ fieldId: 'class', sublistId: 'revenueelement', line: i, value: _class });
                    recObj.setSublistValue({ fieldId: 'department', sublistId: 'revenueelement', line: i, value: _dept });

                    recObj.setValue({ fieldId: 'class', value: _class });
                    recObj.setValue({ fieldId: 'department', value: _dept });

                    log.debug('TEST', {
                        jobId: jobId,
                        _class: recObj.getValue({ fieldId: 'class' }),
                        _dept: recObj.getValue({ fieldId: 'department' })
                    });
                }

            }
        } catch (e) {
            log.error('Error', e);
        }
    }

    return {
        beforeLoad: beforeLoad,
        beforeSubmit: beforeSubmit
    }
});
