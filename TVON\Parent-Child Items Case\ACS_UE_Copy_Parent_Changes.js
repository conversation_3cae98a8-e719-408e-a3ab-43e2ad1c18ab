/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search', 'N/task', 'N/runtime'], function(record, search, task, runtime) {

    var STATIC_EXCLUDED_FIELDS = [
        "_eml_nkey_",
        "_multibtnstate_",
        "selectedtab",
        "nsapiPI",
        "nsapiSR",
        "nsapiVF",
        "nsapiFC",
        "nsapiPS",
        "nsapiVI",
        "nsapiVD",
        "nlapiVL",
        "nsapiPD",
        "nsapiVL",
        "nsapiRC",
        "nsapiLI",
        "nsapiLC",
        "nsapiCT",
        "nlapiVD",
        "nlapiVI",
        "nlapiVF",
        "recmachcustrecord_scm_cpn_itemflags",
        "inpt_customform",
        "transactionsdotted",
        "systemnotesdotted",
        "itemvendorfieldsets",
        "quantitypricingleveltypes",
        "nsbrowserenv",
        "wfPI",
        "wfSR",
        "wfVF",
        "wfFC",
        "wfPS",
        "type",
        "id",
        "externalid",
        "whence",
        "customwhence",
        "entryformquerystring",
        "_csrf",
        "wfinstances",
        "createddate",
        "lastmodifieddate",
        "itemtype",
        "itemtypename",
        "accchange",
        "vendormapchange",
        "vendormapcodechange",
        "binmapchange",
        "keywordschange",
        "pricingchange",
        "bassemblychild",
        "itemid",
        "upccode",
        "includechildren",
        "itemoptions",
        "origbinactive",
        "haschildren",
        "hasparent",
        "nluser",
        "nlrole",
        "nldept",
        "nlloc",
        "nlsub",
        "baserecordtype",
        "baseprice",
        "onlineprice",
        "pricelevel1",
        "pricelevel2",
        "pricelevel5",
        "pricelevel6",
        "pricelevel7",
        "pricelevel8",
        "pricelevel9",
        "custpage_scm_cpn_hdnpreferenceflag",
        "custpage_scm_lc_elcenabled",
        "customsublist6dotted",
        "priceflags",
        "transactionsloaded",
        "systemnotesloaded",
        "activitiesdotted",
        "quantitypricinglevelvalid",
        "itemvendortypes",
        "customsublist44loaded",
        "usernotesdotted",
        "translationslabels"
    ];
    function applyChangesToSubitems(subItemSearch, bodyFieldChanges, priceLevelChanges, locationChanges, parentId, recordType) {
        try {                
            
            
            var subItemSearchCount = subItemSearch.runPaged().count;

            // if the subItem search count is greater than 2, run a map reduce script instead
            // if(subItemSearchCount <= 10) {
            if(subItemSearchCount) {
                subItemSearch.run().each(function(result){

                    var internalid = result.getValue({ name: 'internalid' });

                    var subItemObj = record.load({
                        type: recordType,
                        id: internalid
                    });

                    // apply body field changes
                    for(var field in bodyFieldChanges) {
                        subItemObj.setValue({ fieldId: field, value: bodyFieldChanges[field] });
                    }

                    // apply price level header changes
                    if(priceLevelChanges.header.length) {
                        var priceLevelHeader = priceLevelChanges.header;
                        for(var i = 0; i < priceLevelHeader.length; i++) {
                            subItemObj.setMatrixHeaderValue({
                                sublistId: 'price',
                                fieldId: 'price',
                                column: priceLevelHeader[i].column,
                                value: priceLevelHeader[i].newValue,
                            });
                        }
                    }

                    // apply price level sublist changes
                    if(priceLevelChanges.sublist.length) {
                        var priceLevelSublist = priceLevelChanges.sublist;
                        for(var i = 0; i < priceLevelSublist.length; i++) {
                            subItemObj.setMatrixSublistValue({
                                sublistId: 'price',
                                fieldId: 'price',
                                column: priceLevelSublist[i].column,
                                line: priceLevelSublist[i].row,
                                value: priceLevelSublist[i].newValue
                            });
                        }
                    }

                    // apply location sublist changes
                    if(locationChanges.length) {
                        for(var i = 0; i < locationChanges.length; i++) {
                            subItemObj.setSublistValue({
                                sublistId: 'locations',
                                fieldId: locationChanges[i].fieldId,
                                line: locationChanges[i].line,
                                value: locationChanges[i].newValue
                            });
                        }
                    }

                    var subItemID = subItemObj.save({
                        enableSourcing: false,
                        ignoreMandatoryFields: true
                    });

                    log.audit('Successfully saved subitem', { subItemID: subItemID });
                    return true

                });

            } else {
                // var mrTask = task.create({
                //     taskType: task.TaskType.MAP_REDUCE,
                //     scriptId: 'customscript_acs_mr_copy_parent_changes',
                //     deploymentId: 'customdeploy_acs_mr_copy_parent_changes',
                //     params: {
                //         'custscript_body_field_changes': JSON.stringify(bodyFieldChanges),
                //         'custscript_price_level_changes': JSON.stringify(priceLevelChanges),
                //         'custscript_location_changes': JSON.stringify(locationChanges),
                //         'custscript_parent_id': parentId
                //     }
                // });
                
                // var mrTaskID = mrTask.submit();
                // log.debug('mrTaskID', mrTaskID);
            }

        } catch (e) {
            log.debug('Error', e);
        }

    }

    function afterSubmit(context) {
        
        var newRecObj = context.newRecord;
        var oldRecObj = context.oldRecord;

        if(context.type == context.UserEventType.EDIT){

            
            var scriptObj = runtime.getCurrentScript();
            
            // get the excluded fields from the parameters given by the user
            var excludedFields = scriptObj.getParameter('custscript_excluded_fields').split(",");

            var subItemSearch = search.load({
                id: 'customsearch_si_search_su'
            });

            var itemFilter = search.createFilter({
                name: 'parent',
                operator: search.Operator.ANYOF,
                values: [newRecObj.id]
            });
            subItemSearch.filters.push(itemFilter);

            var subItemSearchCount = subItemSearch.runPaged().count;

            if(subItemSearchCount) {

                // GET BODY FIELD CHANGES //

                    var changedBodyFields = {};
                        
                    var fieldsArr = newRecObj.getFields();

                    // loop through all the body fields and check if changed
                    for(var i = 0; i < fieldsArr.length; i++) {

                        // don't do work if any of the fields are excluded
                        if(STATIC_EXCLUDED_FIELDS.indexOf(fieldsArr[i]) !== -1) continue;
                        if(excludedFields.indexOf(fieldsArr[i]) !== -1) continue;

                        var oldValue = oldRecObj.getValue({ fieldId: fieldsArr[i] });
                        var newValue = newRecObj.getValue({ fieldId: fieldsArr[i] });

                        // if old value is not the same as the new value, insert date to custom modified date field
                        if(oldValue != newValue){
                            changedBodyFields[fieldsArr[i]] = newValue;
                        }
                    }

                // END -- GET BODY FIELD CHANGES //
                
                // GET PRICE LEVEL HEADER CHANGES //

                    var priceHeaderCount = newRecObj.getMatrixHeaderCount({
                        sublistId: 'price',
                        fieldId: 'price'
                    });

                    var changedHeaders = [];

                    for(var i = 1; i < priceHeaderCount; i++) {
                        var oldHeadValue = oldRecObj.getMatrixHeaderValue({ sublistId: 'price', fieldId: 'price', column: i  });
                        var newHeadValue = newRecObj.getMatrixHeaderValue({ sublistId: 'price', fieldId: 'price', column: i  });

                        if(oldHeadValue != newHeadValue) {
                            var changedHeaderValue = {
                                column: i,
                                newValue: newHeadValue
                            }
                            changedHeaders.push(changedHeaderValue);
                        }
                    }

                // END -- GET PRICE LEVEL HEADER CHANGES //

                // GET PRICE LEVEL SUBLIST CHANGES //

                    var priceLevelsCount = newRecObj.getLineCount({
                        sublistId: 'price'
                    });

                    changedPrice = [];

                    for(var i = 0; i < priceLevelsCount; i++) {
                        for(var j = 0; j < priceHeaderCount; j++) {
                            
                            var oldItemPrice = oldRecObj.getMatrixSublistValue({
                                sublistId: 'price',
                                fieldId: 'price',
                                column: j,
                                line: i
                            });
                            var newItemPrice = newRecObj.getMatrixSublistValue({
                                sublistId: 'price',
                                fieldId: 'price',
                                column: j,
                                line: i
                            });
                            if(oldItemPrice != newItemPrice) {
                                var changedItemPriceValue = {
                                    column: j,
                                    row: i,
                                    newValue: newItemPrice
                                }
                                changedPrice.push(changedItemPriceValue);
                            }
                        }
                    }

                    var changedPriceLevels = {
                        header: changedHeaders,
                        sublist: changedPrice
                    }

                // END -- GET PRICE LEVEL SUBLIST CHANGES //

                // GET LOCATION SUBLIST CHANGES //
                    var changedLocations = [];

                    var locationFields = newRecObj.getSublistFields({ sublistId: 'locations' });
                    var locationLineCount = newRecObj.getLineCount({ sublistId: 'locations' });

                    for(var i = 0; i < locationLineCount; i++) {
                        for(var j = 0; j < locationFields.length; j++) {

                            var oldValue = oldRecObj.getSublistValue({ sublistId: 'locations', fieldId: locationFields[j], line: i });
                            var newValue = newRecObj.getSublistValue({ sublistId: 'locations', fieldId: locationFields[j], line: i });

                            if(oldValue != newValue) {
                                var changedLocationField = {
                                    fieldId: locationFields[j],
                                    line: i,
                                    newValue: newValue
                                }
                                changedLocations.push(changedLocationField);
                            }
                        }
                    }


                // END -- GET LOCATION SUBLIST CHANGES //

                // apply changes to subitems
                applyChangesToSubitems(subItemSearch, changedBodyFields, changedPriceLevels, changedLocations, newRecObj.id, newRecObj.type);

            }

        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
