<#-- format specific processing -->
<#function buildUnstructuredInfo payment>
<#assign paidTransactions=transHash[payment.internalid]>
<#assign referenceNote="">
<#assign paidTransactionsCount=paidTransactions?size>
<#if (paidTransactionsCount>= 1)>
    <#assign referenceNote="Payment for Invoice/s Nos. ">
    <#list paidTransactions as transaction>
        <#if transaction.tranid?has_content>
            <#assign referenceNote=referenceNote + transaction.tranid>
            <#if (paidTransactionsCount> 1 && transaction_index != paidTransactionsCount - 1)>
                <#assign referenceNote=referenceNote + ",">
            </#if>
        </#if>
    </#list>
</#if>
<#return referenceNote>
</#function>

<#function convertSEPACharSet text>
<#assign value=text>
<#assign value=value?replace('&','+')>
<#assign value=value?replace('*','.')>
<#assign value=value?replace('$','.')>
<#assign value=value?replace('%','.')>
<#assign value=convertToLatinCharSet(value)>
<#return value>
</#function>
<#-- template building -->
#OUTPUT START#
<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03">
    <CstmrCdtTrfInitn>
        <GrpHdr>
            <MsgId>${setMaxLength(pfa.id,35)}</MsgId>
            <CreDtTm>${pfa.custrecord_2663_file_creation_timestamp?date?string("yyyy-MM-dd")}T${pfa.custrecord_2663_file_creation_timestamp?time?string("HH:mm:ss")}</CreDtTm>
            <NbOfTxs>${setMaxLength(payments?size?c,15)}</NbOfTxs>
            <CtrlSum>${formatAmount(computeTotalAmount(payments),"decLessThan1")}</CtrlSum>
            <InitgPty>
                <Nm>Amphastar Pharmaceuticals Inc</Nm>
                <Id>
                    <OrgId>
                        <Othr>
                            <Id>ABCB5264001</Id>
                        </Othr>
                    </OrgId>
                </Id>
            </InitgPty>
        </GrpHdr>
        <PmtInf>
            <PmtInfId>${setMaxLength(convertSEPACharSet(pfa.custrecord_2663_ref_note),35)}</PmtInfId>
            <PmtMtd>TRF</PmtMtd>
            <PmtTpInf>
                <SvcLvl>
                    <Cd>URGP</Cd>
                </SvcLvl>
            </PmtTpInf>
            <ReqdExctnDt>${pfa.custrecord_2663_file_creation_timestamp?date?string("yyyy-MM-dd")}</ReqdExctnDt>
            <Dbtr>
                <Nm>${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm>
                <PstlAdr>
                    <#if cbank.custrecord_dbtr_strt_nm?has_content>
                    <StrtNm>${cbank.custrecord_dbtr_strt_nm}</StrtNm>
                    </#if>
                    <#if cbank.custrecord_dbtr_zip?has_content>
                    <PstCd>${cbank.custrecord_dbtr_zip}</PstCd>
                    </#if>
                    <#if cbank.custrecord_dbtr_city?has_content>
                    <TwnNm>${cbank.custrecord_dbtr_city}</TwnNm>
                    </#if>
                    <#if cbank.custrecord_dbtr_state?has_content>
                    <CtrySubDvsn>${cbank.custrecord_dbtr_state}</CtrySubDvsn>
                    </#if>
                    <Ctry>GB</Ctry>
                </PstlAdr>
            </Dbtr>
            <DbtrAcct>
                <Id>
                    <IBAN>${setMaxLength(cbank.custpage_eft_custrecord_2663_iban,34)}</IBAN>
                </Id>
            </DbtrAcct>
            <DbtrAgt>
                <FinInstnId>
                    <BIC>${cbank.custpage_eft_custrecord_2663_bic}</BIC>
					<PstlAdr>
						<Ctry>GB</Ctry>
					</PstlAdr>
                </FinInstnId>
            </DbtrAgt>
            <ChrgBr>DEBT</ChrgBr>
            <#assign countriesWithState = ['US', 'AU', 'CA']>
            <#list payments as payment>
            <#assign ebank=ebanks[payment_index]>
            <#assign entity=entities[payment_index]>
            <CdtTrfTxInf>
                <PmtId>
                    <EndToEndId>${setMaxLength(payment.tranid,12)}</EndToEndId>
                </PmtId>
                <Amt>
                    <InstdAmt Ccy="${getCurrencySymbol(payment.currency)}">${formatAmount(getAmount(payment),"decLessThan1")}</InstdAmt>
                </Amt>
                <CdtrAgt>
                    <FinInstnId>
                        <#if ebank.custrecord_2663_entity_bic?has_content>
                        <BIC>${ebank.custrecord_2663_entity_bic}</BIC>
                        </#if>
                        <#if ebank.custrecord_2663_entity_bank_code?has_content>
                        <ClrSysMmbId>
                            <MmbId>${ebank.custrecord_2663_entity_bank_code}</MmbId>
                        </ClrSysMmbId>
                        </#if>
                        <Nm>${setMaxLength(ebank.custrecord_2663_entity_bank_name,140)}</Nm>
                        <PstlAdr>
                            <Ctry>${ebank.custrecord_2663_entity_country_code}</Ctry>
                        </PstlAdr>
                    </FinInstnId>
                </CdtrAgt>
                <Cdtr>
                    <Nm>${setMaxLength(convertSEPACharSet(buildEntityName(entity)?trim),20)}</Nm>
                    <PstlAdr>
                        <#if entity.address1?has_content>
                            <#assign address = entity.address1>
                            <#if entity.address2?has_content>
                                <#assign address = address + ' ' + entity.address2>
                            </#if>
                        <StrtNm>${address}</StrtNm>
                        </#if>
                        <#if entity.zipcode?has_content>
                        <PstCd>${entity.zipcode}</PstCd>
                        </#if>
                        <#if entity.city?has_content>
                        <TwnNm>${entity.city}</TwnNm>
                        </#if>
                        <#if entity.state?has_content>
                            <#if countriesWithState?seq_index_of(entity.state) != -1>
                        <CtrySubDvsn>${entity.state}</CtrySubDvsn>
                            </#if>
                        </#if>
                        <#if entity.billcountrycode?has_content>
                        <Ctry>${entity.billcountrycode}</Ctry>
                        </#if>
                    </PstlAdr>
                </Cdtr>
                <CdtrAcct>
                    <#if ebank.custrecord_2663_entity_acct_no?has_content || ebank.custrecord_2663_entity_iban?has_content>
                    <Id>
                        <#if ebank.custrecord_2663_entity_iban?has_content && ebank.custrecord_2663_entity_country_code != 'AU'>
                        <IBAN>${ebank.custrecord_2663_entity_iban}</IBAN>
                        <#elseif ebank.custrecord_2663_entity_acct_no?has_content>
                        <Othr>
                            <Id>${ebank.custrecord_2663_entity_acct_no}</Id>
                        </Othr>
                        </#if>
                    </Id>
                    </#if>
                </CdtrAcct>
                <RmtInf>
                    <Ustrd>${buildUnstructuredInfo(payment)}</Ustrd>
                </RmtInf>
            </CdtTrfTxInf>
            </#list>
        </PmtInf>
    </CstmrCdtTrfInitn>
</Document>
<#rt>
#OUTPUT END#