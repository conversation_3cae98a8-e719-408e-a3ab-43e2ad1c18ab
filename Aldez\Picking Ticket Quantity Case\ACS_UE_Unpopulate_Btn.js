/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define([], function() {

    function beforeLoad(context) {
        if(context.type == context.UserEventType.EDIT || context.type == context.UserEventType.CREATE) {
            context.form.clientScriptModulePath = 'SuiteScripts/ACS_CS_Unpopulate_Func.js';
            context.form.addButton({
                id: 'custpage_clear_pick_tick',
                label: 'Clear Pick Ticket Qty',
                functionName: 'unpopulatePickTicketQuantity()'
            });
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
