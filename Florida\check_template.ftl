<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            .check table,
            .voucher1 table,
            .voucher2 table {
                position: relative;
                overflow: hidden;
                font-size: 8pt;
                padding: 0;
            }

            td p {
                align: left
            }
            
        </style>
    </head>

    <body padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
        <#list records as check>
            <div
                style="position: relative;font-family: Helvetica,sans-serif;top= -11pt;height: 250pt;width: 612pt;page-break-inside: avoid;font-size: 8pt;">
                <table style="position: absolute;overflow: hidden;left: 466pt;top: 25pt;height: 18pt;width: 108pt;">
                    <tr>
                        <td>${check.trandate}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 42pt;top: 58pt;height: 18pt;width: 393pt;">
                    <tr>
                        <td>${check.entity}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 458pt;top: 58pt;height: 18pt;width: 111pt;">
                    <tr>
                        <td>**<#if (check.usertotal?length> 0)>${check.usertotal}<#else>${check.total}</#if>
                        </td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 2pt;top: 83pt;height: 18pt;width: 572pt;">
                    <tr>
                        <td>${check.totalwords}*********************************************************************
                        </td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 37pt;top: 110pt;height: 80pt;width: 537pt;">
                    <tr>
                        <td>${check.address}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 408pt;top: 115pt;height: 18pt;width: 572pt;">
                    <tr>
                        <td><img src="https://6232470.app.netsuite.com/core/media/media.nl?id=2280&amp;c=6232470&amp;h=89fb2cda72f78864c465"
                                style="width: 160px; height: 49px;" /></td>
                    </tr>
                </table>
            </div>

            <div
                style="position: relative;font-family: Helvetica,sans-serif;height: 250pt;width: 612pt;page-break-before: avoid;font-size: 8pt;">
                <table style="position: absolute;overflow: hidden;left: 412pt;top: -2pt;height: 13pt;width: 70pt;">
                    <tr>
                        <td>${check.trandate}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 36pt;top: -2pt;height: 13pt;width: 157pt;">
                    <tr>
                        <td>${check.entity}</td>
                    </tr>
                </table>
                <#if check.apply?has_content>
                    <#assign x=0>
                    
                        <#if check.apply?has_content>
                            <#list check.apply as apply>
                                <#assign x=x + 1>
                            </#list>
                        </#if>
                        <#if check.credit?has_content>
                            <#list check.credit as credit>
                                <#assign x=x + 1>
                            </#list>
                        </#if>

                        <table style="position: absolute;overflow: hidden;left: 36pt;top: 20pt;width: 436pt;">
                            <#if x lte 12>
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Bill #</th>
                                        <th>Bill Amount</th>
                                        <th>Amount</th>
                                        <th>Memo</th>
                                    </tr>
                                </thead>
                                <#list check.credit as credit>
                                    <tr>
                                        <td style="padding-top: 2px;">${credit.creditdate}</td>
                                        <td style="padding-top: 2px;">${credit.refnum}</td>
                                        <td style="padding-top: 2px;">-${credit.amount?string.currency}</td>
                                        <td style="padding-top: 2px;">-${credit.amount}</td>
                                        <td style="padding-top: 2px;">${credit.memo}</td>
                                    </tr>
                                </#list>
                                <#list check.apply as apply>
                                    <tr>
                                        <td style="padding-top: 2px;">${apply.applydate}</td>
                                        <td style="padding-top: 2px;">${apply.refnum}</td>
                                        <td style="padding-top: 2px;">${apply.total}</td>
                                        <td style="padding-top: 2px;">${apply.amount}</td>
                                        <td style="padding-top: 2px;">${apply.memo}</td>
                                    </tr>
                                </#list>
                            <#elseif x gt 5>
                                <tr>
                                    <td>Please refer to separate voucher for remittance information.</td>
                                </tr>
                            </#if>
                        </table>
                </#if>

                <table style="position: absolute;overflow: hidden;left: 473pt;top: 204pt;height: 13pt;width: 67pt;">
                    <tr>
                        <td>&nbsp;</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 295pt;top: 204pt;height: 13pt;width: 134pt;">
                    <tr>
                        <td>${check.total}</td>
                    </tr>
                </table>
            </div>

            <div
                style="position: relative;font-family: Helvetica,sans-serif;height: 250pt;width: 612pt;page-break-before: avoid;font-size: 8pt;">
                <table style="position: absolute;overflow: hidden;left: 412pt;top: -2pt;height: 13pt;width: 70pt;">
                    <tr>
                        <td>${check.trandate}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 36pt;top: -2pt;height: 13pt;width: 157pt;">
                    <tr>
                        <td>${check.entity}</td>
                    </tr>
                </table>
                <#if check.apply?has_content || check.credit?has_content>
                    <#assign x=0>
                        <table style="position: absolute;overflow: hidden;left: 36pt;top: 20pt;width: 436pt;">
                            <#if x lte 12>
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Bill #</th>
                                        <th>Bill Amount</th>
                                        <th>Amount</th>
                                        <th>Memo</th>
                                    </tr>
                                </thead>
                                <#if check.credit?has_content>
                                    <#list check.credit as credit>
                                        <tr>
                                            <td style="padding-top: 2px;">${credit.creditdate}</td>
                                            <td style="padding-top: 2px;">${credit.refnum}</td>
                                            <td style="padding-top: 2px;">-${credit.amount?string.currency}</td>
                                            <td style="padding-top: 2px;">-${credit.amount}</td>
                                            <td style="padding-top: 2px;">${credit.memo}</td>
                                        </tr>
                                    </#list>
                                </#if>
                                <#if check.apply?has_content>
                                    <#list check.apply as apply>
                                        <tr>
                                            <td style="padding-top: 2px;">${apply.applydate}</td>
                                            <td style="padding-top: 2px;">${apply.refnum}</td>
                                            <td style="padding-top: 2px;">${apply.total}</td>
                                            <td style="padding-top: 2px;">${apply.amount}</td>
                                            <td style="padding-top: 2px;">${apply.memo}</td>
                                        </tr>
                                    </#list>
                                </#if>
                            <#elseif x gt 5>
                                <tr>
                                    <td>Please refer to separate voucher for remittance information.</td>
                                </tr>
                            </#if>
                        </table>
                </#if>

                <table style="position: absolute;overflow: hidden;left: 473pt;top: 204pt;height: 13pt;width: 67pt;">
                    <tr>
                        <td>&nbsp;</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 295pt;top: 204pt;height: 13pt;width: 134pt;">
                    <tr>
                        <td>${check.total}</td>
                    </tr>
                </table>
            </div>
        </#list>
    </body>
</pdf>