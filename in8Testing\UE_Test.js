/**
 *@NApiVersion 2.1
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function afterSubmit(context) {
        let recId = context.newRecord.id;

        let recObj = record.load({
            id: recId,
            type: record.Type.TASK,
            isDynamic: false
        });

        log.debug('afterSubmit', `SO ID: ${recId}`);

        recObj.save();
    }

    return {
        afterSubmit: afterSubmit
    }
});
