/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/format', 'N/runtime'], function(search, record, format, runtime) {

    const DEPARTMENT = runtime.getCurrentScript().getParameter('custscript_department').toString();
    const LOCATION = runtime.getCurrentScript().getParameter('custscript_location').toString();
    const PROGRAM = runtime.getCurrentScript().getParameter('custscript_program').toString();
    const CLASS = runtime.getCurrentScript().getParameter('custscript_fund').toString();
    const MEMO = runtime.getCurrentScript().getParameter('custscript_memo');
    const DATE = runtime.getCurrentScript().getParameter('custscript_date');
    const DUE_DATE = runtime.getCurrentScript().getParameter('custscript_due_date');
    const POSTING_PERIOD = runtime.getCurrentScript().getParameter('custscript_posting_period');
    const RANGE_LIMIT = runtime.getCurrentScript().getParameter('custscript_range_limit');

    function getInputData() {
        var homeownerSearch = search.load({
            id: 'customsearch_acs_homeowner_customers'
        });

        if(RANGE_LIMIT){

            var resultArray = [];            
            var searchResult = homeownerSearch.run().getRange({ start: 0, end: RANGE_LIMIT });

            // iterate each result
            for( var i=0; i < searchResult.length; i++ ) {
                resultArray.push({
                    id: searchResult[i].getValue({ name: 'internalid' })
                });    
            }
            
            return resultArray;
        }

        return homeownerSearch;
    }

    function map(context) {
        var keyFromInput = context.key;
        var valueFromInput = context.value;

        var recordObj = JSON.parse(valueFromInput);

        try {
            var newRecObj = record.transform({
                fromType: record.Type.CUSTOMER,
                fromId: recordObj.id,
                toType: record.Type.INVOICE,
                isDynamic: true,
            });
            
            var firstDayOfMonth = new Date(DATE);

            var lastDayOfMonth = new Date(DUE_DATE);

            // var date = new Date();

            // var firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);

            // var trandate = format.format({
            //     value: firstDayOfMonth,
            //     type: format.Type.DATE
            // });

            // var lastDayOfMonth = new Date(date.getFullYear(), date.getMonth()+1, 0);

            // var duedate = format.format({
            //     value: lastDayOfMonth,
            //     type: format.Type.DATE
            // });

            newRecObj.setValue({ fieldId: 'trandate', value: firstDayOfMonth });
            newRecObj.setValue({ fieldId: 'duedate', value: lastDayOfMonth });
            newRecObj.setValue({ fieldId: 'department', value: DEPARTMENT });
            newRecObj.setValue({ fieldId: 'location', value: LOCATION });
            newRecObj.setValue({ fieldId: 'custbody_cseg2', value: PROGRAM });
            newRecObj.setValue({ fieldId: 'class', value: CLASS });
            newRecObj.setValue({ fieldId: 'memo', value: MEMO });
            newRecObj.setValue({ fieldId: 'postingperiod', value: POSTING_PERIOD });

            var items = [6, 5];

            for(var i = 0; i < items.length; i++) {
                
                newRecObj.selectNewLine({
                    sublistId: 'item'
                });

                newRecObj.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'item',
                    value: items[i],
                    line: i
                });

                newRecObj.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'department',
                    value: DEPARTMENT,
                    line: i
                });

                newRecObj.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'class',
                    value: CLASS,
                    line: i
                });

                newRecObj.setCurrentSublistValue({
                    sublistId: 'item',
                    fieldId: 'location',
                    value: LOCATION,
                    line: i
                });

                newRecObj.commitLine({ sublistId: 'item' });
            }

            var savedId =  newRecObj.save();
            log.audit("Success", "Successfully saved invoice with ID: " + savedId);
        } catch (e) {
            log.error("Error", e);
        }
    }

    function summarize(summary) {
        if (summary.isRestarted)
        {
          log.audit('SUMMARY isRestarted', 'YES');
        }
        else
        {
          log.audit('SUMMARY isRestarted', 'NO');
        }
        
        log.audit('summarize', summary);
    }

    return {
        getInputData: getInputData,
        map: map,
        summarize: summarize
    }
});
