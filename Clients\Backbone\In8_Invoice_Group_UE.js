/**
* 
*
* Version		Date			Author				Remarks
* 1.0			01 Mar 2018		<PERSON> (In8Sync)	Initial Version
*
*/

function beforeLoad(type, form, request){
 
	// try {        
    //     if (type == 'edit') {
    //         var customForm = nlapiGetContext().getCompany() == '789815_SB1' ? 121 : 108;
            
    //         if (nlapiGetFieldValue('customform') == customForm) {

    //             var itemsGroups = getItemsGroups(nlapiGetRecordId());
                
    //             for (var i = 1; i <= nlapiGetLineItemCount('itemcost'); i++) {
    //                 var group = itemsGroups[nlapiGetLineItemValue('itemcost', 'doc', i)];
    //                 if (group) {
    //                     nlapiLogExecution('DEBUG', 'in8', 'set group ' + i + ' - group ' + group);
    //                     nlapiSetLineItemValue('itemcost', 'custcol_in8_group', i, group);
    //                 }
    //             }
    //             for (var i = 1; i <= nlapiGetLineItemCount('expcost'); i++) {
    //                 var group = itemsGroups[nlapiGetLineItemValue('expcost', 'doc', i)];
    //                 if (group) {
    //                     nlapiSetLineItemValue('expcost', 'custcol_in8_group', i, group);
    //                 }
    //             }
    //         }
    //      }
	// } catch(e) {
	// 	nlapiLogExecution('ERROR', 'Error', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
	// }	
}

/**
* @appliedtorecord recordtype
* 
* @param {String} type Operation types: create, edit, delete, xedit
*
* @returns {Void}
*/
function beforeSubmit(type) {

    if (type == 'edit' || type == 'create') {        
        setGroupDescription('item');
        setGroupDescription('itemcost');
        setGroupDescription('expcost');
    }    
}

function setGroupDescription(list) {
    for (var i = 1; i <= nlapiGetLineItemCount(list); i++) {
        var group = nlapiGetLineItemValue(list, 'custcol_in8_group', i);
        if (group) {
            // try to find the group description
            for (var j = 1; j <= nlapiGetLineItemCount('recmachcustrecord_in8_tran_group_tran'); j++) {
                if (nlapiGetLineItemValue('recmachcustrecord_in8_tran_group_tran', 'custrecord_in8_tran_group', j) == group) {
                    nlapiSetLineItemValue(list, 'custcol_in8_group_description', i, nlapiGetLineItemValue('recmachcustrecord_in8_tran_group_tran', 'custrecord_in8_tran_group_description', j));
                }                    
            }
        }
    }
}

function getItemsGroups(internalId) {

    var s = nlapiSearchRecord("transaction", null,
        [
            ["internalidnumber", "equalto", internalId],
            "AND",
            ["mainline", "is", "F"]
        ],
        [
            new nlobjSearchColumn("item"),
            new nlobjSearchColumn("custcol_in8_group"),
            new nlobjSearchColumn("appliedtotransaction"),
        ]
    ) || [];

    var itemsGroup = [];

    for (var i = 0; i < s.length; i++) {
        itemsGroup[s[i].getValue('appliedtotransaction')] = s[i].getValue('custcol_in8_group');
    }
    return itemsGroup;
}
