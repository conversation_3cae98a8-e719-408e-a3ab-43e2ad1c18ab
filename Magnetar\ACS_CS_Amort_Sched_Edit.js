/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/ui/dialog'], function(dialog) {

    function pageInit(context) {
        
    }

    function fieldChanged(context) {
        if(context.sublistId == 'expense' && context.fieldId == 'custcol_mag_vb_line_isprepaid') {
            var recObj = context.currentRecord;

            var pillar = recObj.getCurrentSublistValue({ sublistId: 'expense', fieldId: 'cseg6', line: context.line });
            var isPrepaid = recObj.getCurrentSublistValue({ sublistId: 'expense', fieldId: 'custcol_mag_vb_line_isprepaid' });
            var amortSched = recObj.getCurrentSublistValue({ sublistId: 'expense', fieldId: 'amortizationsched' });

            if(pillar && pillar == 8){
                if(!isPrepaid) {
                    if(amortSched){
                        dialog.alert({
                            title: 'Warning',
                            message: 'Please remember to remove the Amortization Journal Entries for this line.'
                        });
                    }
                }
            }

        }
    }

    return {
        pageInit: pageInit,
        fieldChanged: fieldChanged,
    }
});
