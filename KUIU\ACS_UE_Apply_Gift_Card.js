/**
 *@NApiVersion 2.1
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function afterSubmit(context) {
        var recObj = context.newRecord;
        var gateway = recObj.getValue({ fieldId: 'custrecord_celigo_etail_trans_gateway' });

        if(gateway == 'gift_card') {
            var soId = recObj.getValue({ fieldId: 'custrecord_celigo_etail_orderid' });
            var amount = recObj.getValue({ fieldId: 'custrecord_celigo_etail_trans_amount' });

            var soObj = record.load({
                id: soId,
                type: record.Type.SALES_ORDER
            });

            var line = soObj.findSublistLineWithValue({
                sublistId: 'item',
                fieldId: 'item',
                value: 57406
            });

            soObj.setSublistValue({
                sublistId: 'item',
                fieldId: 'amount',
                line: line,
                value: (amount * -1)
            });

            soObj.save({
                enableSourcing: false,
                ignoreMandatoryFields: true
            });

        }

    }

    return {
        afterSubmit: afterSubmit
    }
});
