/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/email', 'N/render', 'N/search'], function(email, render, search) {

    function afterSubmit(context) {
        var currRecord = context.newRecord;
        if(context.type == context.UserEventType.CREATE){
            var soId = currRecord.getValue({ fieldId: 'createdfrom'});
            var soText = currRecord.getText({ fieldId: 'createdfrom'});
            var customerText = currRecord.getText({ fieldId: 'entity' });
            var salesOrderLUF = search.lookupFields({
                type: search.Type.SALES_ORDER,
                id: soId,
                columns: [
                    'salesrep.email',
                    'custbodyso_accountmanager.email',
                    'salesrep',
                    'custbodyso_accountmanager',
                ]
            });

            if(salesOrderLUF["salesrep.email"] != "" || salesOrderLUF["custbodyso_accountmanager.email"] != ""){
                var mergeResult = render.mergeEmail({
                    templateId: 120
                });

                var emailSubject = mergeResult.subject;
                var emailBody = mergeResult.body;
                emailSubject = emailSubject.replace('{CREATED_FROM}', soText);
                emailBody = emailBody.replace('{CUSTOMER}', customerText);
                emailBody = emailBody.replace('{CREATED_FROM}', soText);
                
                var transactionFile = render.transaction({
                    entityId: parseInt(currRecord.id),
                    printMode: render.PrintMode.PDF,
                    inCustLocale: true
                });

                if(salesOrderLUF["salesrep.email"] != ""){
                    email.send({
                        author: 22,
                        recipients: salesOrderLUF["salesrep.email"],
                        subject: emailSubject,
                        body: emailBody,
                        attachments: [transactionFile],
                        relatedRecords: {
                            entityId: salesOrderLUF.salesrep[0].value,
                            transactionId: soId
                        }
                    });

                    log.debug("sent to " + salesOrderLUF["salesrep.email"]);
                }

                if(salesOrderLUF["custbodyso_accountmanager.email"] != ""){
                    email.send({
                        author: 22,
                        recipients: salesOrderLUF["custbodyso_accountmanager.email"],
                        subject: emailSubject,
                        body: emailBody,
                        attachments: [transactionFile],
                        relatedRecords: {
                            entityId: salesOrderLUF.custbodyso_accountmanager[0].value,
                            transactionId: soId
                        }
                    });
                    log.debug("sent to " + salesOrderLUF["custbodyso_accountmanager.email"]);

                }
            }
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
