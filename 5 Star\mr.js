/**
 * Copyright (c) 1998-2017 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * NetSuite, Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with NetSuite.
 * 
 * * Version    Date            Author                  Remarks
 *   1.00       Oct 30, 2019  Virginia Bertolini     Initial Version
 *
 * 
 * @NApiVersion 2.x
 * @NScriptType MapReduceScript
 */

 define(['N/record', 'N/search', 'N/runtime', 'N/task'],
 function(NS_Record, NS_Search, NS_Runtime, NS_Task) {


   /**
    **    GET INPUT DATA
    **/
   function getInputData() {

     var logTitle = 'Get Input Data';

     try {

       log.debug(logTitle, '**** START *****');

       var searchId = NS_Runtime.getCurrentScript().getParameter({
         name: "custscript_cm_ss_id"
       });

       var res = NS_Search.load({
         id: searchId
       });

       return res;


     } catch (e) {
       log.error(logTitle, e.message);
     }
   }

   function map(context) {
     var logTitle = 'map';
     try {

       var cmObj = JSON.parse(context.value);
       var objToProcess = {};

       var objToProcess = {};
       if (!isEmpty(cmObj.values)) {
         objToProcess.cmID = cmObj.id;
         objToProcess.subsidiary = cmObj.values.subsidiary.value;
         objToProcess.item = cmObj.values.item.value;
         objToProcess.qty = cmObj.values.quantity;
         objToProcess.location = cmObj.values.location.value;
         objToProcess.customer = cmObj.values["internalid.customer"].value;
       }

       context.write({
         key: cmObj.id,
         value: objToProcess
       });
     } catch (e) {
       log.error(logTitle, e.message);
     }
   }

   /**
    **    REDUCE
    **/
   function reduce(context) {
     var logTitle = 'Reduce';
     try {
       var cmID = context.key;
       var arrContext = context.values;

       log.debug("arrContext[0]", JSON.stringify(arrContext[0]));

       if (!isEmpty(arrContext) && arrContext.length > 0) {

         var customerID = JSON.parse(arrContext[0]).customer;
         var invAdjRecord = NS_Record.create({
           type: "inventoryadjustment",
           isDynamic: false,
         });

         invAdjRecord.setValue({
           fieldId: 'entity',
           value: customerID,
           ignoreFieldChange: true
         });

         invAdjRecord.setValue({
           fieldId: 'adjlocation',
           value: JSON.parse(arrContext[0]).location,
           ignoreFieldChange: true
         });

         var account = NS_Runtime.getCurrentScript().getParameter({
           name: "custscript_account"
         });

         invAdjRecord.setValue({
           fieldId: 'account',
           value: account,
           ignoreFieldChange: true
         });

         log.debug("l", arrContext.length);
         for (var j = 0; j < arrContext.length; j++) {

           invAdjRecord.setSublistValue({
             sublistId: 'inventory',
             fieldId: 'item',
             line: j,
             value: JSON.parse(arrContext[j]).item
           });
           var newQty = parseInt(JSON.parse(arrContext[j]).qty);

           invAdjRecord.setSublistValue({
             sublistId: 'inventory',
             fieldId: 'adjustqtyby',
             line: j,
             value: newQty
           });

           invAdjRecord.setSublistValue({
             sublistId: 'inventory',
             fieldId: 'location',
             line: j,
             value: JSON.parse(arrContext[j]).location
           });

         }


       }
       var newInv = invAdjRecord.save();
       log.debug("newInv", newInv);

     } catch (e) {
       log.error(logTitle, e);
     }
   }



   /**
    **    SUMMARIZE
    **/

   function summarize(summary) {

     var stLogTitle = 'Summarize';

     try {



     } catch (e) {

       log.error(stLogTitle, e.message);

     }



   }

   // This function returns true if the stValue is empty
   function isEmpty(stValue) {
     if ((stValue == '') || (stValue == null) || (stValue == undefined)) {
       return true;
     }
     return false;
   }


   return {
     getInputData: getInputData,
     map: map,
     reduce: reduce,
     summarize: summarize
   };

 });