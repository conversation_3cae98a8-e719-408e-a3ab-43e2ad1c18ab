/**
 *@NApiVersion 2.0
 *@NScriptType UserEventScript
 */
 define(['N/search', 'N/runtime'],
    function(search, runtime) {
        function beforeSubmit(context) {
            log.debug('EXECUTION CONTEXT BEFORE SUBMIT', runtime.executionContext);
            log.debug('context type', context.type);
            if (context.type == context.UserEventType.CREATE) {
                log.debug('EXECUTION CONTEXT', runtime.executionContext);
                log.debug('context', context);
                if (runtime.executionContext == runtime.ContextType.USER_INTERFACE) {
                    var billPayment = context.newRecord;
                    var params = billPayment.getValue({
                        fieldId: 'entryformquerystring'
                    });
                    log.debug('params', params);
                    if (params) {
                        var billIndex = params.indexOf('bill=') + 5;

                        if (billIndex) {
                            var billString = params.substring(billIndex);
                            log.debug('billString', billString);

                            var billId = billString.substring(0, billString.indexOf('&'));
                            log.debug('billId', billId);

                            var billEmail = billEmailAddress(billId);
                            if (billEmail) {
                                billPayment.setValue({
                                    fieldId: 'custbody_bill_email_address',
                                    value: billEmail
                                });
                            }
                        }
                    } else {
                        scanApplyList(billPayment);
                    }
                } else if (runtime.executionContext == runtime.ContextType.SCHEDULED) {
                    var billPayment = context.newRecord;
                    scanApplyList(billPayment);
                } else if (runtime.executionContext == runtime.ContextType.CSV_IMPORT) {
                    var billPayment = context.newRecord;
                    scanApplyList(billPayment);
                }
            }
            if (context.type == context.UserEventType.EDIT) {
                var billPayment = context.newRecord;
                scanApplyList(billPayment);
            }
        }

        function billEmailAddress(billId) {
            var billFields = search.lookupFields({
                type: search.Type.VENDOR_BILL,
                id: billId,
                columns: ['custbody_bill_email_address']
            });
            log.debug('billFields', billFields);

            if (billFields) {
                return billFields.custbody_bill_email_address;
            } else {
                return null;
            }
        }

        function scanApplyList(billPayment) {
            var applyListSize = billPayment.getLineCount({
                sublistId: 'apply'
            });
            log.debug('applyListSize', applyListSize);

            for (var x = 0; x < applyListSize; x++) {
                var apply = billPayment.getSublistValue({
                    sublistId: 'apply',
                    fieldId: 'apply',
                    line: x
                });
                if (apply) {
                    var billId = billPayment.getSublistValue({
                        sublistId: 'apply',
                        fieldId: 'internalid',
                        line: x
                    });
                    log.debug('billId', billId);

                    var billEmail = billEmailAddress(billId);
                    if (billEmail) {
                        billPayment.setValue({
                            fieldId: 'custbody_bill_email_address',
                            value: billEmail
                        });
                    }
                    break;
                }
            }
        }

        return {
            beforeSubmit: beforeSubmit
        };
    });