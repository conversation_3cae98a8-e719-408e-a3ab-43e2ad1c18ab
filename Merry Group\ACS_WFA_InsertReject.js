/**
 *@NApiVersion 2.x
 *@NScriptType WorkflowActionScript
 */
define(['N/record', 'N/runtime', 'N/format', 'N/email'], function(record, runtime, format, email) {

    function onAction(scriptContext) {
        try {

            var contextRecord = scriptContext.newRecord;
            var approver = runtime.getCurrentScript().getParameter('custscript_user_name_id');
            var action = runtime.getCurrentScript().getParameter('custscript_action');
            var datetime = format.format({
                value: new Date(),
                type: format.Type.DATE
            });

            var approvalLogs = contextRecord.getValue({ fieldId: 'custbody_approval_log' });
            approvalLogs += action + ' by ' + approver + '(' + datetime + ')\r\n';
            log.debug(contextRecord.id);
            // var recObj = record.load({
            //     type: record.Type.ESTIMATE,
            //     id: contextRecord.id,
            //     isDynamic: true
            // });

            record.submitFields({
               type: record.Type.ESTIMATE,
               id: contextRecord.id,
               values: { 'custbody_approval_log': approvalLogs }
            });

            log.debug('Success', 'Successfully updated approval log (id: '+ contextRecord.id +')');

        } catch (e) {

            log.debug('Error', e);

        }
    }

    return {
        onAction: onAction
    }
});
