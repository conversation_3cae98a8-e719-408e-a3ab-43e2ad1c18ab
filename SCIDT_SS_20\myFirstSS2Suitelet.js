/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/ui/serverWidget', 'N/email'], function(https, serverWidget, email) {

    function onRequest(context) {
        log.debug({
            title: 'Suitelet Start',
            details: 'Suitelet Executed'
        });

        if(context.request.method == https.Method.GET){
        // logic for GET
            
            var getForm = serverWidget.createForm({
                title: 'Email Sender 2.0'
            });

            // add custom button
            getForm.clientScriptModulePath = './clientScriptSS2.js';

            getForm.addButton({
                id: 'custpage_mainbtn',
                label: 'How are you feeling today?',
                functionName: 'feelings()'
            });

            // recipient field
            var recipientField = getForm.addField({
                id: 'custpage_recipient',
                label: 'Recipient',
                type: serverWidget.FieldType.EMAIL
            });
            recipientField.isMandatory = true;

            // subject field
            var subjectField = getForm.addField({
                id: 'custpage_subject',
                label: 'Subject',
                type: serverWidget.FieldType.TEXT
            });
            subjectField.isMandatory = true;

            // body field
            var bodyField = getForm.addField({
                id: 'custpage_body',
                label: 'Body',
                type: serverWidget.FieldType.RICHTEXT
            });
            bodyField.isMandatory = true;

            // submit button
            getForm.addSubmitButton({
                label: 'Send'
            });

            context.response.writePage({ pageObject: getForm });

        } else {
        // logic for POST
            var requestParams = context.request.parameters;
            var recipient = requestParams.custpage_recipient;
            var subject = requestParams.custpage_subject;
            var body = requestParams.custpage_body;

            email.send({
                author: 2,
                recipients: recipient,
                subject: subject,
                body: body
            });

            var postForm = serverWidget.createForm({
                title: 'Email Successfully Sent to' + recipient
            });

            context.response.writePage({
                pageObject: postForm
            });

        }
    }

    return {
        onRequest: onRequest
    }
});
