/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define([], function() {

    function beforeLoad(context) {
        var recObj = context.newRecord;
        if(recObj.type == 'assemblyitem') {
            var getForm = context.form;
            getForm.clientScriptModulePath = 'SuiteScripts/ACS_CS_Resolve_Suitelet.js';
            getForm.addButton({
                id: 'custpage_print_pdf',
                label: 'Print Full BOM',
                functionName: 'renderPdf(false)'
            });

            getForm.addButton({
                id: 'custpage_print_pdf_simple',
                label: 'Print Simple BOM',
                functionName: 'renderPdf(true)'
            });

            getForm.addButton({
                id: 'custpage_dl_csv',
                label: 'Download CSV',
                functionName: 'renderCSV(false)'
            });

            
            getForm.addButton({
                id: 'custpage_dl_csv_simple',
                label: 'Download CSV Simple',
                functionName: 'renderCSV(true)'
            });
        }
    }

    return {
        beforeLoad: beforeLoad,
    }
});
