/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
 define(['N/runtime'], function(runtime) {

    const INCLUDED_FIELDS = runtime.getCurrentScript().getParameter('custscript_included_fields').split(",");
    const MODIFIED_DATE_FIELD_ID = runtime.getCurrentScript().getParameter('custscript_excluded_fields');

    function pageInit(context) {
        
    }

    function fieldChanged(context) {
        if(INCLUDED_FIELDS.indexOf(context.fieldId) !== -1 && context.fieldId != MODIFIED_DATE_FIELD_ID) {
            var recObj = context.currentRecord;
            var date = new Date;

            recObj.setValue({ fieldId: 'custrecord_acs_last_modified', value: date });
            console.log(context.fieldId);
        }
    }

    return {
        pageInit: pageInit,
        fieldChanged: fieldChanged,
    }
});
