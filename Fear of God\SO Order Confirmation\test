<#-- format specific processing -->
<#function buildUnstructuredInfo payment>
<#assign paidTransactions=transHash[payment.internalid]>
<#assign referenceNote="">
<#assign paidTransactionsCount=paidTransactions?size>
<#if (paidTransactionsCount>= 1)>
<#list paidTransactions as transaction>
<#if transaction.tranid?has_content>
<#assign referenceNote=referenceNote + transaction.tranid>
<#if (paidTransactionsCount> 1 && transaction_index != paidTransactionsCount - 1)>
<#assign referenceNote=referenceNote + ",">
</#if>
</#if>
</#list>
</#if>
<#return referenceNote>
</#function>

<#function convertSEPACharSet text>
<#assign value=text>
<#assign value=value?replace('&','+')>
<#assign value=value?replace('*','.')>
<#assign value=value?replace('$','.')>
<#assign value=value?replace('%','.')>
<#assign value=convertToLatinCharSet(value)>
<#return value>
</#function>
<#-- template building -->
#OUTPUT START#
<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03">
<CstmrCdtTrfInitn>
<GrpHdr>
<MsgId>${setMaxLength(pfa.id,35)}</MsgId>
<CreDtTm>
${pfa.custrecord_2663_file_creation_timestamp?date?string("yyyy-MM-dd")}T${pfa.custrecord_2663_file_creation_timestamp?time?string("HH:mm:ss")}
</CreDtTm>
<NbOfTxs>${setMaxLength(payments?size?c,15)}</NbOfTxs>
<CtrlSum>${formatAmount(computeTotalAmount(payments),"decLessThan1")}</CtrlSum>
<InitgPty>
<Id>
<OrgId>
<Othr>
<Id>${setMaxLength(convertSEPACharSet(cbank.custpage_eft_custrecord_2663_issuer_num),35)}</Id>
</Othr>
</OrgId>
</Id>
</InitgPty>
</GrpHdr>
<PmtInf>
<PmtInfId>${setMaxLength(convertSEPACharSet(pfa.custrecord_2663_ref_note),35)}</PmtInfId>
<PmtMtd>TRF</PmtMtd>
<PmtTpInf>
<SvcLvl>
<Cd>SEPA</Cd>
</SvcLvl>
</PmtTpInf>
<ReqdExctnDt>${pfa.custrecord_2663_process_date?string("yyyy-MM-dd")}</ReqdExctnDt>
<Dbtr>
<Nm>${setMaxLength(convertSEPACharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}</Nm>
</Dbtr>
<DbtrAcct>
<Id>
<IBAN>${setMaxLength(cbank.custpage_eft_custrecord_2663_iban,34)}</IBAN>
</Id>
</DbtrAcct>
<DbtrAgt>
<FinInstnId>
<BIC>${cbank.custpage_eft_custrecord_2663_bic}</BIC>
</FinInstnId>
</DbtrAgt>
<ChrgBr>SLEV</ChrgBr>
<#list payments as payment>
<#assign ebank=ebanks[payment_index]>
<#assign entity=entities[payment_index]>
<CdtTrfTxInf>
<PmtId>
<EndToEndId>${setMaxLength(payment.tranid,12)}</EndToEndId>
</PmtId>
<Amt>
<InstdAmt Ccy="EUR">${formatAmount(getAmount(payment),"decLessThan1")}
</InstdAmt>
</Amt>
<CdtrAgt>
<FinInstnId>
<BIC>${ebank.custrecord_2663_entity_bic}</BIC>
</FinInstnId>
</CdtrAgt>
<Cdtr>
<Nm>${setMaxLength(convertSEPACharSet(buildEntityName(entity)?trim),20)}</Nm>
</Cdtr>
<CdtrAcct>
<Id>
<IBAN>${ebank.custrecord_2663_entity_iban}</IBAN>
</Id>
</CdtrAcct>
<RmtInf>
<Ustrd>${setMaxLength(convertSEPACharSet(buildUnstructuredInfo(payment)),140)}</Ustrd>
</RmtInf>
</CdtTrfTxInf>
</#list>
</PmtInf>
</CstmrCdtTrfInitn>
</Document>
<#rt>
#OUTPUT END#