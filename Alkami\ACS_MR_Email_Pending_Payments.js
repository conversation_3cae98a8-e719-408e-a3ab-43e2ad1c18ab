/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/record', 'N/search', 'N/render', 'N/email', 'N/url'], function(record, search, render, email, url) {

    function getInputData() {
        var mySearch = search.load({
            id: 'customsearch_acs_pending_bill_payment_ap'
        });
        return mySearch;
    }

    function map(context) {
        // accept data from previous stage
        var keyFromInput = context.key;
        var valueFromInput = context.value;

        var recordObj = JSON.parse(valueFromInput);
        try {
            context.write({
                key: 'ids',
                value: recordObj.id
            });
    
        } catch (errorObj) {
            log.debug({
                title: 'error 1',
                details: errorObj
            });
        }
    }

    function reduce(context) {
        var reduceKey = context.key;
        var reduceValues = context.values;
        
        try {
            var listoflinks = "";
            for(var i = 0; i < reduceValues.length; i++){
                var scheme = 'https://';
                var host = url.resolveDomain({
                    hostType: url.HostType.APPLICATION
                });
                var relativePath = url.resolveRecord({
                    recordType: record.Type.VENDOR_PAYMENT,
                    recordId: reduceValues[i],
                    isEditMode: false
                });
                var myURL = scheme + host + relativePath;
                listoflinks += '<a href="'+myURL+'">'+reduceValues[i]+'</a><br>';
            }

            var mergeResult = render.mergeEmail({
                templateId: 25
            });

            var emailSubject = mergeResult.subject;
            var emailBody = mergeResult.body;
            emailBody = emailBody.replace('{SCRIPTDATA}', listoflinks);

            email.send({
                author: 5528,
                recipients: '<EMAIL>',
                subject: emailSubject,
                body: emailBody
            });

        } catch (errorObj) {
            log.debug({
                title: 'error 2',
                details: errorObj
            });
        }
        
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
