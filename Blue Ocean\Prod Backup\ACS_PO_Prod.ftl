<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        
        <macrolist>
            <macro id="nlheader">
                <#assign blackLogo = 'https://5046151.app.netsuite.com/core/media/media.nl?id=8308&c=5046151&h=dddc713b94b459f6412b' />
                <table class="header" style="width: 100%;">
                    <tr>
                        <td width="50%" rowspan="3"><img src="${blackLogo?html}" style="width: 70%; height: 70%; float: left; margin-right: 5px;" />
                            <span><b>${record.subsidiary}</b></span><br /><span>${subsidiary.mainaddress_text}</span></td>
                        <td width="50%" align="right"><span class="title">${record@title}</span></td>
                    </tr>
                    <tr>
                        <td width="50%" align="right"><span class="number">${record.tranid}</span></td>
                    </tr>
                    <tr>
                        <td width="50%" align="right">${record.trandate}</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td>
                            <barcode codetype="code128" showtext="true" value="${record.tranid}" />
                        </td>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0px;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0px;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 15px;
                background-color: #e3e3e3;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="15%" footer="nlfooter" footer-height="30pt" padding="0.4in 0.4in 0.4in 0.4in"
        size="Letter">

        <#assign _suiteletURL=("https://5046151.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1069&deploy=1&compid=5046151&h=6a035dd877b62bce3048&recid=" + record.id + "&rectype=" + record.type) /><#include _suiteletURL />
        <#assign items = item_data.items />
        

        <table style="width: 100%; margin-top: 15px;">
            <tr>
                <td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
                <td class="addressheader" colspan="3"><b>${record.shipaddress@label}</b>;</td>
                <td class="totalboxtop" colspan="5"><b>${record.total@label?upper_case}</b></td>
            </tr>
            <tr>
                <td class="address" colspan="3" rowspan="2">${record.billaddress}</td>
                <td class="address" colspan="3" rowspan="2">${record.shipaddress}</td>
                <td align="right" class="totalboxmid" colspan="5">${item_data.total}</td>
            </tr>
        </table>
        &nbsp;

        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th>${record.terms@label}</th>
                <th>${record.shipdate@label}</th>
                <th>Customer PO#</th>
                <th>Sales Rep</th>
            </tr>
            <tr>
                <td>${record.terms}</td>
                <td>${record.shipdate}</td>
                <td>${record.otherrefnum}<br />${record.custbody_bhpc_po_venpo}</td>
                <td>${record.employee}</td>
            </tr>
        </table>
        <span style="display: none;">&nbsp;</span>
        <#if items?has_content>
            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list items as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th width="16.6%" align="left">Article Code</th>
                                <th width="16.6%" align="left">MFG Style #</th>
                                <th width="16.6%" align="left">Display Name/Code</th>
                                <th width="10%" align="left">Color</th>
                                <th width="7%" align="left">Size</th>
                                <th width="7%" align="center">QTY</th>
                                <th width="12%" align="right">Rate</th>
                                <th width="12%" align="right">Amount</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td font-size="7pt" align="left" line-height="150%">${item.itemid}</td>
                        <td font-size="7pt" align="left" line-height="150%">${item.vendorname}</td>
                        <td font-size="7pt" align="left" line-height="150%">${item.displayname}</td>
                        <td font-size="7pt" align="left" line-height="150%">${item.custitem_psgss_product_color}</td>
                        <td font-size="7pt" align="left" line-height="150%">${item.custitem_psgss_product_size}</td>
                        <td font-size="7pt" align="center" line-height="150%">${item.quantity}</td>
                        <td font-size="7pt" align="right">${item.rate}</td>
                        <td font-size="7pt" align="right">${item.amount}</td>
                    </tr>
                </#list><!-- end items -->
            </table>
            <hr />
        </#if>
        
        <table width="100%">
            <tr>
                <td width="50%">
                    <#--  <table class="body" style="width: 100%; margin-top: 10px;">
                        <tr>
                            <th>Remittance Bank Information</th>
                        </tr>
                        <tr>
                            <td>${record.custbody7}</td>
                        </tr>
                    </table>  -->
                </td>
                <td width="50%">                
                    <table class="total" style="width: 100%; margin-top: 10px;">
                        <tbody>
                            <tr>
                                <td align="right" width="60%"><b>Total Quantity</b></td>
                                <td align="right" width="40%">${item_data.totalQty}</td>
                            </tr>
                            <tr>
                                <td align="right" width="60%"><b><span class="nscke-label" data-code="record.subtotal@label" title="record.subtotal@label">Subtotal</span></b></td>
                                <td align="right" width="40%"><span class="nscke-field" data-code="record.subtotal" title="record.subtotal">${item_data.subtotal}</span></td>
                            </tr>
                            <tr>
                                <td align="right" width="60%"><b><span class="nscke-label">Discounts</span></b></td>
                                <td align="right" width="40%"><span class="nscke-field">${item_data.discount}</span></td>
                            </tr>
                            <tr>
                                <td align="right" width="60%"><b><span class="nscke-label">Markups</span></b></td>
                                <td align="right" width="40%"><span class="nscke-field">${item_data.markup}</span></td>
                            </tr>
                            <tr>
                                <td align="right" width="60%"><b><span class="nscke-label" data-code="record.taxtotal@label" title="record.taxtotal@label">Tax Total</span> (${record.taxrate}%)</b></td>
                                <td align="right" width="40%"><span class="nscke-field" data-code="record.taxtotal" title="record.taxtotal">${item_data.taxtotal}</span></td>
                            </tr>
                            <tr class="totalrow">
                                <td align="right" width="60%"><strong>Total</strong></td>
                                <td align="right" width="40%">${item_data.total}</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </table>

        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th>Shipping Information</th>
            </tr>
            <tr>
                <td>${record.custbody_bos_shipping_information}</td>
            </tr>
        </table>
    </body>
</pdf>