<!DOCTYPE html>
<!-- saved from url=(0107)https://1115163-sb2.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=683&id=70&whence=&print=T -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">

<title>Payment File Format</title>
<script language="JavaScript" type="text/javascript">window.status='';</script><script type="text/javascript" src="./Payment File Format_files/NLMapping.nl__t=ZL1B&amp;NS_VER=2021.2&amp;minver=88&amp;locale=en_US.nlqs"></script>
<script type="text/javascript" src="./Payment File Format_files/undefine.js.download"></script>
<link rel="stylesheet" href="./Payment File Format_files/pagestyles.nl">

<script type="application/json" id="data_1145492348" "="">{"translations":{"NLFormlabelContext.INLINE_EDITING":"Inline Editing","NLButtonContext.SEARCH_SERIALIZATION":"Export - Serialization","NLButtonContext.ADD":"Add","NLHeadingContext.NO_RESULTS_FOUND":"No results found","NLButtonContext.EXPORT__MICROSOFT_WORD":"Export - Microsoft \u0026#174; Word","NLButtonContext.EXPORT__MICROSOFT_EXCEL":"Export - Microsoft \u0026#174; Excel","NLPagemessageContext.ADD_ROW":"Add Row","NLFormlabelContext.EDIT":"Edit","NLButtonContext.EXPANDCOLLAPSE_FILTERS":"Expand/Collapse filters","NLButtonContext.EXPORT__TABLEAU_WORKBOOK":"Export - Tableau\u0026#174; Workbook","NLFormlabelContext.FILTERS":"Filters","NLButtonContext.EXPORT__EXCEL_WEB_QUERY":"Export - Excel Web Query","NLButtonContext.EXPORT__CSV":"Export - CSV","NLButtonContext.PRINT":"Print","NLFormlabelContext.EXPAND_ALL":"Expand All","NLButtonContext.EXPORT__PDF":"Export - PDF"},"horizontalLabelEnabled":false}</script>
<script type="application/javascript">
(function (data, preferences) {
NS = window.NS || {};

NS.Translations = NS.Translations || {};
NS.Translations.dictionary = NS.Translations.dictionary || {};
for(var key in data.translations)
{
    if (data.translations.hasOwnProperty(key))
    {
        NS.Translations.dictionary[key] = data.translations[key];
    }
}
NS.Translations.getTranslatedString = function (key, params) {
	if (NS.Translations.dictionary && NS.Translations.dictionary[key]) {
		var translated = NS.Translations.dictionary[key];
		if (params) {
			var index = 1;
			for (var property in params) {
				translated = translated.replace("{" + index + ":" + property + "}", params[property]);
				index++;
			}
		}

		return translated;
	}
	return key;
}

NS.UI = NS.UI || {};
NS.UI.Preferences = NS.UI.Preferences || {};
NS.UI.Preferences.horizontalLabelsEnabled = data.horizontalLabelEnabled;

})(JSON.parse(document.getElementById('data_1145492348').textContent),{});
</script>
</head>
<body bgcolor="#FFFFFF" class="body_2010 nl-pagetint " link="#000000" vlink="#000000" alink="#000000" text="#000000" style="background-image:none;margin: 5px 0px 5px 0px;">
<div id="outerwrapper" style="width:100%">
<div id="innerwrapper">
<div id="div__header" class="noprint" leftmargin="0" topmargin="0" marginwidth="1">
<table border="0" cellspacing="0" cellpadding="0" width="100%">

</table></div>
<div id="div__title" style="margin: 0px;"></div>
<div id="div__label" class="scrollarea" style="margin: 0px; overflow:hidden;"></div>
<div id="div__body" style="margin: 0px; ">
<form name="main_form" id="main_form" style="margin: 0; font-size: 0px; padding-bottom: 10px; ">
<table width="100%" border="0" cellspacing="0" cellpadding="0" role="presentation">
<tbody><tr>
    <td>
</td></tr><tr><td><div id="div__pt_title" class="pt_container">
    <div class="pt_head"></div>
    <div class="pt_body">
    <div class="pt_buttons"></div>
    <div class="pt_title">Payment File Format: BofA - NURG (NORAM - CA - CAD)</div>





</div>
    <div class="pt_end"></div>
</div>

</td></tr>
    


<tr>
<td>
<table border="0" cellspacing="0" cellpadding="0" width="100%">
<tbody><tr><td valign="top" width="33%"><table border="0" class="table_fields" cellspacing="0" cellpadding="0" data-colnumber="3">

	   <tbody><tr>

		<td>

<div class="uir-field-wrapper" data-field-type="text"><span id="name_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="name_fs_lbl" class="smallgraytextnolink" style="">

Name
</span></span><span class="uir-field inputreadonly">
        BofA - NURG (NORAM - CA - CAD)
</span>
</div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-onoff" data-field-type="checkbox"><span class="uir-field inputreadonly">
        &nbsp;
</span>
<span id="isinactive_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="isinactive_fs_lbl" class="checkboxSpan smallgraytextnolink" style="">

Inactive
</span></span></div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper" data-field-type="select"><span id="custrecord_2663_payment_file_type_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_payment_file_type_fs_lbl" class="smallgraytextnolink" style="">

Payment File Type
</span></span><span class="uir-field inputreadonly">
        <span class="inputreadonly">EFT</span>
</span>
</div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper" data-field-type="select"><span id="custrecord_2663_format_country_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_format_country_fs_lbl" class="smallgraytextnolink" style="">

Country
</span></span><span class="uir-field inputreadonly">
        <span class="inputreadonly">Canada, United States</span>
</span>
</div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-onoff" data-field-type="checkbox"><span class="uir-field inputreadonly">
        No
</span>
<span id="custrecord_2663_include_all_currencies_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_include_all_currencies_fs_lbl" class="checkboxSpan smallgraytextnolink" style="">

Include All Currencies
</span></span></div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-long-text" data-field-type="textarea"><span id="custrecord_2663_ref_fields_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_ref_fields_fs_lbl" class="smallgraytextnolink" style="">

Reference Fields
</span></span><span class="uir-field inputreadonly uir-resizable">
        &lt;refFields type="SEPA Credit Transfer (Netherlands)"&gt;<br>	&lt;refField id="custrecord_2663_bank_comp_id" label="Company Id" mandatory="true" helptext="Enter the code the bank uses to identify your company. This is assigned by the bank."/&gt;<br>	&lt;refField id="custrecord_2663_branch_num" label="Branch Code" mandatory="true" helptext="Enter the code the bank uses to identify fund transfers from your company. This is assigned by the bank."/&gt;<br>   	&lt;refField id="custrecord_2663_iban" label="IBAN" mandatory="true" helptext="Enter your company's International Bank Account Number (IBAN)."/&gt;<br>   	&lt;refField id="custrecord_2663_bank_address1" label="Address Line 1" mandatory="true" helptext="Enter the street address of your company's bank."/&gt;<br>   	&lt;refField id="custrecord_2663_bank_address2" label="Address Line 2" mandatory="true" helptext="Enter the building, city, or town address of your company's bank."/&gt;<br>   	&lt;refField id="custrecord_2663_country_code" label="Country" helptext="Select the country where your company's bank is located."/&gt;<br>	&lt;refField id="custrecord_2663_bank_state" label="State" mandatory="true"  helptext="Enter the State of your company's bank."/&gt; <br>   	&lt;refField id="custrecord_2663_statement_name" label="Company Name" mandatory="true" helptext="Enter your company's name."/&gt;<br>   	&lt;refField id="custrecord_2663_bic" label="BIC" mandatory="true" helptext="Enter the Business Identifier Code (BIC), also called SWIFT code, of your company's bank (eight or 11 characters). "/&gt;<br>        &lt;refField id="custrecord_2663_acct_num" label="Bank Account Number" helptext="Enter the bank account number. "/&gt;<br>        &lt;refField id="custrecord_2663_bank_code" label="Bank Code" helptext="Enter the bank code or sort code. "/&gt;<br>        &lt;refField id="custrecord_2663_bank_name" label="Bank Name" helptext="Enter the bank name. "/&gt;<br>&lt;/refFields&gt;
</span>
</div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-long-text" data-field-type="textarea"><span id="custrecord_2663_entity_ref_fields_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_entity_ref_fields_fs_lbl" class="smallgraytextnolink" style="">

Entity Reference Fields
</span></span><span class="uir-field inputreadonly uir-resizable">
        &lt;refFields type="NON SEPA Credit Transfer"&gt;<br>   &lt;refField id="custrecord_2663_entity_bic" label="BIC" /&gt;<br>   &lt;refField id='custrecord_2663_entity_iban' label='IBAN' /&gt; <br>   &lt;refField id='custrecord_2663_entity_acct_no' label='Bank Account Number' /&gt; <br>   &lt;refField id='custrecord_2663_entity_bank_code' label='Bank Code or sort code' /&gt; <br>   &lt;refField id="custrecord_2663_entity_state" label="Province" /&gt; <br>   &lt;refField id='custrecord_2663_entity_country_code' label='Country Code' /&gt;<br>&lt;/refFields&gt;
</span>
</div>
		</td>

	   </tr>


    </tbody></table>
  </td><td valign="top" width="33%">
    <table class="table_fields" border="0" cellspacing="0" cellpadding="0">

	   <tbody><tr>

		<td>

<div class="uir-field-wrapper uir-long-text" data-field-type="textarea"><span id="custrecord_2663_field_validator_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_field_validator_fs_lbl" class="smallgraytextnolink" style="">

Field Validator
</span></span><span class="uir-field inputreadonly uir-resizable">
        &lt;fieldValidatorList&gt;<br><br>&lt;fieldValidator&gt;<br>&lt;fieldName&gt;custrecord_2663_bic&lt;/fieldName&gt;<br>&lt;validatorList&gt;<br>&lt;validator type="len"&gt;<br>&lt;param name="validLength"&gt;8|11&lt;/param&gt;<br>&lt;/validator&gt;<br>&lt;validator type='custom' /&gt;<br>&lt;/validatorList&gt;<br>&lt;/fieldValidator&gt;<br><br>&lt;fieldValidator&gt;<br>&lt;fieldName&gt;custrecord_2663_iban&lt;/fieldName&gt;<br>&lt;validatorList&gt;<br>&lt;validator type='custom' /&gt;<br>&lt;/validatorList&gt;<br>&lt;/fieldValidator&gt;<br><br>&lt;fieldValidator&gt;<br>&lt;fieldName&gt;custrecord_2663_statement_name&lt;/fieldName&gt;<br>&lt;validatorList&gt;<br>&lt;validator type="len"&gt;<br>&lt;param name="maxLength"&gt;70&lt;/param&gt;<br>&lt;/validator&gt;<br>&lt;/validatorList&gt;<br>&lt;/fieldValidator&gt;<br><br>&lt;fieldValidator&gt;<br>&lt;fieldName&gt;custrecord_2663_bank_address1&lt;/fieldName&gt;<br>&lt;validatorList&gt;<br>&lt;validator type="len"&gt;<br>&lt;param name="maxLength"&gt;70&lt;/param&gt;<br>&lt;/validator&gt;<br>&lt;/validatorList&gt;<br>&lt;/fieldValidator&gt;<br><br>&lt;fieldValidator&gt;<br>&lt;fieldName&gt;custrecord_2663_bank_address2&lt;/fieldName&gt;<br>&lt;validatorList&gt;<br>&lt;validator type="len"&gt;<br>&lt;param name="maxLength"&gt;70&lt;/param&gt;<br>&lt;/validator&gt;<br>&lt;/validatorList&gt;<br>&lt;/fieldValidator&gt;<br><br>&lt;fieldValidator&gt;<br>&lt;fieldName&gt;custrecord_2663_entity_bic&lt;/fieldName&gt;<br>&lt;validatorList&gt;<br>&lt;validator type="len"&gt;<br>&lt;param name="validLength"&gt;8|11&lt;/param&gt;<br>&lt;/validator&gt;<br>&lt;validator type='custom' /&gt;<br>&lt;/validatorList&gt;<br>&lt;/fieldValidator&gt;<br><br>&lt;fieldValidator&gt;<br>&lt;fieldName&gt;custrecord_2663_entity_iban&lt;/fieldName&gt;<br>&lt;validatorList&gt;<br>&lt;validator type='custom' /&gt;<br>&lt;/validatorList&gt;<br>&lt;/fieldValidator&gt;<br><br>&lt;fieldValidator&gt;<br>&lt;fieldName&gt;custrecord_2663_entity_acct_no&lt;/fieldName&gt;<br>&lt;validatorList&gt;<br>&lt;validator type='custom' /&gt;<br>&lt;/validatorList&gt;<br>&lt;/fieldValidator&gt;<br><br>&lt;fieldValidator&gt;<br>&lt;fieldName&gt;custrecord_2663_entity_bank_code&lt;/fieldName&gt;<br>&lt;validatorList&gt;<br>&lt;validator type='custom' /&gt;<br>&lt;/validatorList&gt;<br>&lt;/fieldValidator&gt;<br><br>&lt;/fieldValidatorList&gt;
</span>
</div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-onoff" data-field-type="checkbox"><span class="uir-field inputreadonly">
        No
</span>
<span id="custrecord_2663_native_format_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_native_format_fs_lbl" class="checkboxSpan smallgraytextnolink" style="">

Native Format?
</span></span></div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper" data-field-type="integer"><span id="custrecord_2663_max_lines_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_max_lines_fs_lbl" class="smallgraytextnolink" style="">

Maximum Lines
</span></span><span class="uir-field inputreadonly">
        2,000
</span>
</div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-onoff" data-field-type="checkbox"><span class="uir-field inputreadonly">
        No
</span>
<span id="custrecord_13272_sorting_sepadd_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_13272_sorting_sepadd_fs_lbl" class="checkboxSpan smallgraytextnolink" style="">

Use Advanced SEPA DD Sorting
</span></span></div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-onoff" data-field-type="checkbox"><span class="uir-field inputreadonly">
        No
</span>
<span id="custrecord_2663_update_entity_details_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_update_entity_details_fs_lbl" class="checkboxSpan smallgraytextnolink" style="">

Update Entity Bank Details
</span></span></div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-long-text" data-field-type="textarea"><span id="custrecord_12144_free_marker_body_st_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_12144_free_marker_body_st_fs_lbl" class="smallgraytextnolink" style="">

SuiteTax Bank File Template
</span></span><span class="uir-field inputreadonly uir-resizable">
        &nbsp;
</span>
</div>
		</td>

	   </tr>


    </tbody></table>
  </td><td valign="top" width="33%">
    <table class="table_fields" border="0" cellspacing="0" cellpadding="0">

	   <tbody><tr>

		<td>

<div class="uir-field-wrapper uir-long-text" data-field-type="textarea"><span id="custrecord_2663_free_marker_body_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_free_marker_body_fs_lbl" class="smallgraytextnolink" style="">

Bank File Template
</span></span><span class="uir-field inputreadonly uir-resizable">
        &lt;#-- format specific processing --&gt;<br>&lt;#function getReferenceNote payment&gt;<br>    &lt;#assign paidTransactions = transHash[payment.internalid]&gt;<br>    &lt;#assign referenceNote = ""&gt;<br>    &lt;#assign paidTransactionsCount = paidTransactions?size&gt;<br>    &lt;#if (paidTransactionsCount &gt;= 1)&gt;<br>    	&lt;#list paidTransactions as transaction&gt;<br>    		&lt;#if transaction.tranid?has_content&gt;<br>    			&lt;#if referenceNote?has_content&gt;<br>    				&lt;#assign referenceNote = referenceNote + ", " + transaction.tranid&gt;<br>    			&lt;#else&gt;<br>    				&lt;#assign referenceNote = transaction.tranid&gt;<br>    			&lt;/#if&gt;<br>		    &lt;/#if&gt;<br>		&lt;/#list&gt;<br>    &lt;/#if&gt;<br>	&lt;#return referenceNote&gt;<br>&lt;/#function&gt;<br>&lt;#-- cached values --&gt;<br>&lt;#assign totalAmount = computeTotalAmount(payments)&gt;<br>&lt;#-- template building --&gt;<br>#OUTPUT START#<br>&lt;?xml version="1.0" encoding="UTF-8"?&gt;<br>&lt;Document xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;<br>&lt;CstmrCdtTrfInitn&gt;<br>&lt;GrpHdr&gt;<br>&lt;MsgId&gt;${cbank.custrecord_2663_file_name_prefix}${pfa.name}&lt;/MsgId&gt;<br>&lt;CreDtTm&gt;${pfa.custrecord_2663_file_creation_timestamp?date?string("yyyy-MM-dd")}T${pfa.custrecord_2663_file_creation_timestamp?time?string("HH:mm:ss")}&lt;/CreDtTm&gt;<br>&lt;NbOfTxs&gt;${payments?size?c}&lt;/NbOfTxs&gt;<br>&lt;CtrlSum&gt;${formatAmount(totalAmount,"decLessThan1")}&lt;/CtrlSum&gt;<br>&lt;InitgPty&gt;<br>&lt;Nm&gt;${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}&lt;/Nm&gt;<br>&lt;Id&gt;<br>&lt;OrgId&gt;<br>&lt;Othr&gt;<br>&lt;Id&gt;${cbank.custpage_eft_custrecord_2663_bank_comp_id}&lt;/Id&gt;<br>&lt;SchmeNm&gt;<br>&lt;Cd&gt;CUST&lt;/Cd&gt;   <br>&lt;/SchmeNm&gt;  <br>&lt;/Othr&gt;<br>&lt;/OrgId&gt;<br>&lt;/Id&gt;<br>&lt;/InitgPty&gt;<br>&lt;/GrpHdr&gt;<br>&lt;PmtInf&gt;<br>&lt;PmtInfId&gt;${pfa.id}-1&lt;/PmtInfId&gt;<br>&lt;PmtMtd&gt;TRF&lt;/PmtMtd&gt;<br>&lt;BtchBookg&gt;false&lt;/BtchBookg&gt;<br>&lt;NbOfTxs&gt;${payments?size?c}&lt;/NbOfTxs&gt;<br>&lt;CtrlSum&gt;${formatAmount(totalAmount,"decLessThan1")}&lt;/CtrlSum&gt;<br>&lt;PmtTpInf&gt;<br>&lt;SvcLvl&gt;<br>&lt;Cd&gt;NURG&lt;/Cd&gt;<br>&lt;/SvcLvl&gt;<br>&lt;/PmtTpInf&gt;<br>&lt;ReqdExctnDt&gt;${pfa.custrecord_2663_process_date?string("yyyy-MM-dd")}&lt;/ReqdExctnDt&gt;<br>&lt;Dbtr&gt;<br>&lt;Nm&gt;${setMaxLength(convertToLatinCharSet(cbank.custpage_eft_custrecord_2663_statement_name),70)}&lt;/Nm&gt;<br>&lt;PstlAdr&gt;<br>&lt;StrtNm&gt;${cbank.custpage_eft_custrecord_2663_bank_address1} ${cbank.custpage_eft_custrecord_2663_bank_address2}&lt;/StrtNm&gt;<br>&lt;CtrySubDvsn&gt;${cbank.custpage_eft_custrecord_2663_bank_state}&lt;/CtrySubDvsn&gt; <br>&lt;Ctry&gt;${cbank.custpage_eft_custrecord_2663_country_code}&lt;/Ctry&gt;<br>&lt;/PstlAdr&gt;<br>&lt;/Dbtr&gt;<br>&lt;DbtrAcct&gt;<br>&lt;Id&gt;<br>&lt;Othr&gt;<br>&lt;Id&gt;${cbank.custpage_eft_custrecord_2663_acct_num}&lt;/Id&gt;<br>&lt;/Othr&gt;<br>&lt;/Id&gt;<br>&lt;Tp&gt;<br>&lt;Cd&gt;CACC&lt;/Cd&gt;<br>&lt;/Tp&gt;<br>&lt;Ccy&gt;CAD&lt;/Ccy&gt;<br>&lt;/DbtrAcct&gt;<br>&lt;DbtrAgt&gt;<br>&lt;FinInstnId&gt;<br>&lt;BIC&gt;${cbank.custpage_eft_custrecord_2663_bic}&lt;/BIC&gt;<br>&lt;Nm&gt;${cbank.custpage_eft_custrecord_2663_bank_name}&lt;/Nm&gt;<br>&lt;PstlAdr&gt;<br>&lt;Ctry&gt;${cbank.custpage_eft_custrecord_2663_country_code}&lt;/Ctry&gt;<br>&lt;/PstlAdr&gt;<br>&lt;/FinInstnId&gt;<br>&lt;/DbtrAgt&gt;<br>&lt;ChrgBr&gt;SHAR&lt;/ChrgBr&gt;<br>&lt;#list payments as payment&gt;<br>    &lt;#assign ebank = ebanks[payment_index]&gt;<br>    &lt;#assign entity = entities[payment_index]&gt;<br>&lt;CdtTrfTxInf&gt;<br>&lt;PmtId&gt;<br>&lt;InstrId&gt;${pfa.id}-${payment.tranid}&lt;/InstrId&gt;<br>&lt;EndToEndId&gt;${pfa.id}-${payment.tranid}&lt;/EndToEndId&gt;<br>&lt;/PmtId&gt;<br>&lt;Amt&gt;<br>&lt;InstdAmt Ccy="${getCurrencySymbol(cbank.custrecord_2663_currency)}"&gt;${formatAmount(getAmount(payment),"decLessThan1")}&lt;/InstdAmt&gt;<br>&lt;/Amt&gt;<br>&lt;CdtrAgt&gt;<br>&lt;FinInstnId&gt;<br>&lt;ClrSysMmbId&gt;&lt;MmbId&gt;${ebank.custrecord_2663_entity_bank_code}&lt;/MmbId&gt;&lt;/ClrSysMmbId&gt;<br>&lt;PstlAdr&gt;<br>&lt;CtrySubDvsn&gt;${ebank.custrecord_2663_entity_state}&lt;/CtrySubDvsn&gt;<br>&lt;Ctry&gt;${ebank.custrecord_2663_entity_country_code}&lt;/Ctry&gt;<br>&lt;/PstlAdr&gt;<br>&lt;/FinInstnId&gt;<br>&lt;/CdtrAgt&gt;<br>&lt;Cdtr&gt;<br>&lt;Nm&gt;${setMaxLength(convertToLatinCharSet(buildEntityName(entity)),70)}&lt;/Nm&gt;<br>&lt;PstlAdr&gt;<br>&lt;StrtNm&gt;${entity.billaddress1}&lt;/StrtNm&gt;<br>&lt;TwnNm&gt;${entity.billcity}&lt;/TwnNm&gt;<br>&lt;CtrySubDvsn&gt;${entity.billstate}&lt;/CtrySubDvsn&gt;<br>&lt;Ctry&gt;${ebank.custrecord_2663_entity_country_code}&lt;/Ctry&gt;<br>&lt;/PstlAdr&gt;<br>&lt;/Cdtr&gt;<br>&lt;CdtrAcct&gt;<br>&lt;Id&gt;<br>&lt;Othr&gt;<br>&lt;Id&gt;${ebank.custrecord_2663_entity_acct_no}&lt;/Id&gt;<br>&lt;/Othr&gt;<br>&lt;/Id&gt;<br>&lt;/CdtrAcct&gt;<br>&lt;/CdtTrfTxInf&gt;<br>&lt;/#list&gt;<br>&lt;/PmtInf&gt;<br>&lt;/CstmrCdtTrfInitn&gt;<br>&lt;/Document&gt;&lt;#rt&gt;<br>#OUTPUT END#
</span>
</div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper" data-field-type="text"><span id="custrecord_2663_output_file_extension_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_output_file_extension_fs_lbl" class="smallgraytextnolink" style="">

Output File Extension
</span></span><span class="uir-field inputreadonly">
        XML
</span>
</div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper" data-field-type="select"><span id="custrecord_2663_output_file_encoding_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_2663_output_file_encoding_fs_lbl" class="smallgraytextnolink" style="">

Output File Encoding
</span></span><span class="uir-field inputreadonly">
        <span class="inputreadonly">UTF-8</span>
</span>
</div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-onoff" data-field-type="checkbox"><span class="uir-field inputreadonly">
        No
</span>
<span id="custrecord_12194_localized_format_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_12194_localized_format_fs_lbl" class="checkboxSpan smallgraytextnolink" style="">

From Localization SuiteApp
</span></span></div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-onoff" data-field-type="checkbox"><span class="uir-field inputreadonly">
        No
</span>
<span id="custrecord_15152_hide_acc_num_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_15152_hide_acc_num_fs_lbl" class="checkboxSpan smallgraytextnolink" style="">

Hide Account Number
</span></span></div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper uir-onoff" data-field-type="checkbox"><span class="uir-field inputreadonly">
        No
</span>
<span id="custrecord_15152_encrypt_acc_num_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custrecord_15152_encrypt_acc_num_fs_lbl" class="checkboxSpan smallgraytextnolink" style="">

Encrypt Account Number
</span></span></div>
		</td>

	   </tr>



	   <tr>

		<td>

<div class="uir-field-wrapper" data-field-type="select"><span id="custpage_2663_format_currency_fs_lbl_uir_label" class="smallgraytextnolink uir-label "><span id="custpage_2663_format_currency_fs_lbl" class="smallgraytextnolink" style="">

Currency
</span></span><span class="uir-field inputreadonly">
        <span class="inputreadonly">&nbsp;</span>
</span>
</div>
		</td>

	   </tr>


    </tbody></table></td>
  </tr>
  </tbody></table>
</td>
</tr>

</tbody></table>
<input type="hidden" name="_eml_nkey_">
<input type="hidden" name="_multibtnstate_">
<input type="hidden" name="selectedtab">
<input type="hidden" name="nsapiPI">
<input type="hidden" name="nsapiSR">
<input type="hidden" name="nsapiVF">
<input type="hidden" name="nsapiFC">
<input type="hidden" name="nsapiPS">
<input type="hidden" name="nsapiVI">
<input type="hidden" name="nsapiVD">
<input type="hidden" name="nsapiPD">
<input type="hidden" name="nsapiVL">
<input type="hidden" name="nsapiRC">
<input type="hidden" name="nsapiLI">
<input type="hidden" name="nsapiLC">
<input type="hidden" name="nsapiCT">
<input type="hidden" name="nsbrowserenv">
<input type="hidden" name="wfPI">
<input type="hidden" name="wfSR">
<input type="hidden" name="wfVF">
<input type="hidden" name="wfFC">
<input type="hidden" name="wfPS">
<input type="hidden" name="type">
<input type="hidden" name="id">
<input type="hidden" name="externalid">
<input type="hidden" name="whence">
<input type="hidden" name="customwhence">
<input type="hidden" name="entryformquerystring">
<input type="hidden" name="_csrf">
<input type="hidden" name="wfinstances">
<input type="hidden" name="owner">
<input type="hidden" name="custrecord_2663_format_currency">
<input type="hidden" name="nameorig">
<input type="hidden" name="scriptid">
<input type="hidden" name="ownerid">
<input type="hidden" name="custrecord_2663_file_fields">
<input type="hidden" name="custrecord_2663_use_free_marker">
<input type="hidden" name="custrecord_12194_app_id">
<input type="hidden" name="rectype">
<input type="hidden" name="linenumber">
<input type="hidden" name="version">
<input type="hidden" name="submitnext_t">
<input type="hidden" name="submitnext_y">
<input type="hidden" name="nluser">
<input type="hidden" name="nlrole">
<input type="hidden" name="nldept">
<input type="hidden" name="nlloc">
<input type="hidden" name="nlsub">
<input type="hidden" name="baserecordtype">
<input type="hidden" name="custpage_translation_strings">
<input type="hidden" name="formdisplayview">
<input name="_button" id="_button" type="hidden" value="">

</form>




</div>
<div id="div__footer" class="noprint" leftmargin="0" topmargin="0" marginwidth="1" marginheight="1">
</div>
</div></div>

<!-- 2,883 s: 53% #59 cache: 1% #19 -->
<!-- Host [ a2.prod.sv ] App Version [ 2021.2.0.88 ] -->
<!-- COMPID [ 1115163_SB2 ]  EMAIL [ <EMAIL> ] URL [ /app/common/custom/custrecordentry.nl ] Time [ Tue Nov 23 12:03:36 PST 2021 ] -->
<!-- Not logging slowest SQL -->
</body></html>