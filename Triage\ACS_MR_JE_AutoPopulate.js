/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/format'], function(search, record, format) {

    function getInputData() {
        var mySearch = search.load({
            id: 474
        });

        return mySearch;
    }

    function map(context) {
        // accept data from previous stage
        var keyFromInput = context.key;
        var valueFromInput = context.value;
        
        var recordObj = JSON.parse(valueFromInput);
        try {
            var values = {
                custentity_client_manager: recordObj.values["custentity_client_manager.job"].value,
                custentity_recruiter: recordObj.values["custentity_recruiter_sourced.job"].value,
                custentity_profession: recordObj.values["custentity_profession.job"].value,
                cseg_specialty_: recordObj.values["cseg_specialty_.job"].value,
                startdate: recordObj.values["startdate.job"],
                enddate: recordObj.values["projectedenddate.job"],
                line: recordObj.values.linesequencenumber
            }
            context.write({
                key: recordObj.id,
                value: values
            });
    
        } catch (errorObj) {
            log.debug({
                title: 'error',
                details: errorObj
            })
        }
        // pass data to the next stage
    }

    function reduce(context) {
        // accept the data here
        var reduceKey = context.key;
        var reduceValues = context.values;
        try {
            var recObj = record.load({
                type: record.Type.JOURNAL_ENTRY,
                id: reduceKey
            });


            for(x = 0; x < reduceValues.length; x++){
                var values = JSON.parse(reduceValues[x]);
                var line = parseInt(values.line) - 1;
                try {                 
                    if(values.cseg_specialty_){
                        recObj.setSublistValue({
                            sublistId: 'line',
                            fieldId: 'cseg_specialty_',
                            line: line,
                            value: values.cseg_specialty_
                        });
                    }

                    if(values.custentity_profession){
                        recObj.setSublistValue({
                            sublistId: 'line',
                            fieldId: 'department',
                            line: line,
                            value: values.custentity_profession
                        });
                    }
                    if(values.custentity_client_manager){
                        recObj.setSublistValue({
                            sublistId: 'line',
                            fieldId: 'custcolclient_manager',
                            line: line,
                            value: values.custentity_client_manager
                        });
                    }
                    if(values.custentity_recruiter){
                        recObj.setSublistValue({
                            sublistId: 'line',
                            fieldId: 'custcolrecruiter',
                            line: line,
                            value: values.custentity_recruiter
                        });
                    }
                    if(values.startdate){

                        var startdate = format.parse({
                            value: new Date(values.startdate),
                            type: format.Type.DATE
                        });

                        recObj.setSublistValue({
                            sublistId: 'line',
                            fieldId: 'custcol_project_start_date',
                            line: line,
                            value: startdate
                        });
                    }
                    if(values.enddate){

                        var enddate = format.parse({
                            value: new Date(values.enddate),
                            type: format.Type.DATE
                        });

                        recObj.setSublistValue({
                            sublistId: 'line',
                            fieldId: 'custcol_project_end_date',
                            line: line,
                            value: enddate
                        });
                    }
                            
                } catch (errorObj) {

                    log.debug({
                        title: 'error',
                        details: errorObj
                    });

                }
            }    

            recObj.save();

        } catch (errorObj) {
            log.debug({
                title: "error",
                details: errorObj
            })
        }
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});

