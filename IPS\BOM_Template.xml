<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
    <#macro renderRow components ctr>
        <#list components as component>
        
            <#if ctr != 1>
                <#assign nbsp = "">
                <#list 1..ctr as index>
                    <#assign nbsp = nbsp + "&nbsp;&nbsp;&nbsp;">
                </#list>                
                <#assign value = nbsp + component.item_name>
            <#else>
                <#assign value = component.item_name>
            </#if>
            <tr>
                <td align="left">${value}</td>
                <td align="center">${component.item_level}</td>
                <td align="left">${component.item_desc?html}</td>
                <td align="left">${component.item_type}</td>
                <td align="right">${component.item_cost?number?string(",##0.00")}</td>
                <td align="right">${component.item_qty}</td>
                <td align="right">${component.item_total?number?string(",##0.00")}</td>
            </tr>
            <#if component.item_components?has_content>
            <@renderRow components=component.item_components ctr=ctr+1 />
            </#if>
        </#list>
    </#macro>

    <head>
        <macrolist>
            <macro id="nlheader">
                <table style="width: 100%;">
                    <tr>
                        <td align="center" style="font-size: 14pt">Inventory Assembly/Bill of Materials</td>
                    </tr>
                    <tr>
                        <td align="center" style="font-size: 11.5pt">Item Name/Number ${record.itemid}</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%; font-size: 8pt">
                    <tr>
                        <td>
                            ${.now?string.short}
                        </td>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.headertable td {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="10%" footer="nlfooter" footer-height="15pt" padding="0.3in 0.3in 0.3in 0.3in"
        size="Letter">
            <table class="headertable" style="width: 100%; margin-top: 10px; font-size: 10.5pt;">
                <tr>
                    <td colspan="6"><b>Purchase Description:</b> ${record.purchasedescription?html}</td>
                </tr>
                <tr>
                    <td colspan="6"><b>Sales Description:</b> ${record.displayname?html}</td>
                </tr>
                <tr>
                    <td colspan="2"><b>Sales Price:</b> $${salesPrice.price?number?string(",##0.00")}</td>
                    <td colspan="2"><b>Cost:</b> $${assm_total.total?number?string(",##0.00")}</td>
                    <td colspan="2"><b>Total Value:</b> ${record.totalvalue}</td>
                </tr>
                <tr>
                    <td colspan="2"><b>Qty on Hand:</b> ${record.totalquantityonhand}</td>
                    <td colspan="4"><b></td>
                </tr>
                <tr>
                    <td colspan="2"><b>Income Account:</b> ${record.incomeaccount}</td>
                    <td colspan="2"><b>COGS Account:</b> ${record.cogsaccount}</td>
                    <td colspan="2"><b>Asset Account:</b> ${record.assetaccount}</td>
                </tr>
                <tr>
                    <td colspan="2"><b>On Sales Order:</b> ${assm_tran_total.total_count_so}</td>
                    <td colspan="4"></td>
                </tr>
            </table>
            <br />
            <table class="itemtable" style="width: 100%; margin-top: 5px;">
                <thead>
                    <tr>
                        <th align="left" width="20%">Item Name/Num</th>
                        <th align="center" width="8%">Level</th>
                        <th align="left" width="35%">Description</th>
                        <th align="left" width="12%">Type</th>
                        <th align="right" width="8.33%">Cost</th>
                        <th align="right" width="8.33%">Qty</th>
                        <th align="right" width="8.33%">Total</th>
                    </tr>
                </thead>
                <@renderRow components=assm_components.components ctr=1 />
                <tr style="border-top: 1px;">
                    <td></td>
                    <td></td>
                    <td colspan="3" align="left"><b>Total Bill of Materials</b></td>
                    <td align="right"><b>${assm_total.qty_total}</b></td>
                    <td align="right"><b>${assm_total.total?number?string(",##0.00")}</b></td>
                </tr>
            </table>
    </body>
</pdf>