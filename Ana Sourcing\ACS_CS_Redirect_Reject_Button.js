/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord', 'N/url'], function(currentRecord, url) {

    function pageInit(context){
        
    }

    function redirectToReject(){
        var rec = currentRecord.get();
        var outputUrl = url.resolveScript({
            scriptId: 'customscript_acs_sl_reject_reason',
            deploymentId: 'customdeploy_acs_sl_reject_reason',
            params: {
                recid: rec.id
            }
        });
        window.location.replace(outputUrl);
    }    

    return {
        pageInit : pageInit,
        redirectToReject : redirectToReject
    }
});
