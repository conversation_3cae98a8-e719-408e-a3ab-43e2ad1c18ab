
function createInvoiceBasedInApprove(event, form) {

    try {
    	if (event == 'approve' || (event == 'create' && nlapiGetContext().getExecutionContext() == 'userinterface')) {
    		nlapiSetFieldValue('paymentmethod', '');
    	}
    } catch (e) {
        nlapiLogExecution('ERROR', 'ERROR', e);
    }
}

function createPayment(type) {

    try {
    	var invoiceNumber = null;
    	
    	nlapiLogExecution('DEBUG', 'type', type);
    	
    	if (type == 'approve') {
    		
    		invoiceNumber = createInvoice();
    		
    		nlapiLogExecution('DEBUG', 'Returned Id', invoiceNumber);    		    	
    	}    	       
    	createInvoicePayment(invoiceNumber);

        //unsetCreditCard(salesorder.getFieldValue('entity'));
    } catch (e) {
        nlapiLogExecution('ERROR', 'ERROR', e);
    }
}

function createInvoice() {
	
	var so = nlapiGetNewRecord();
    var source = so.getFieldValue('source');
    var cod_type = so.getFieldValue('custbody_vendortermspo') || null;
    
    if (so.getFieldValue('custbody_order_processed') == 'T') return;

    // If order is created from WooCommerce, should process it
    if (source != 'Web (Charlotte Fabrics)') {
        if (!so.getFieldValue('custbody_in8_wc_order_id')) {
            return;
        }
    }

    //so.setFieldValue('custbody_order_processed', 'T');
    var entity = so.getFieldValue('entity');
    var codFee = so.getFieldValue('handlingcost') || null;
    var lineItems = so.getLineItemCount('item');
    
    var invoice = nlapiTransformRecord('salesorder', nlapiGetRecordId(), 'invoice');

    //invoice.setFieldValue('createdfrom',so.getId());

    invoice.setFieldValue('custbody_test_order', so.getId());
    
    var shippingFee = so.getFieldValue('shippingcost') || null;
    if (shippingFee) shippingFee = parseInt(shippingFee);
    invoice.setFieldValue('shippingcost', shippingFee);
    invoice.setFieldValue('shipcarrier', so.getFieldValue('shipcarrier'));
    invoice.setFieldValue('shipmethod', so.getFieldValue('shipmethod'));
    invoice.setFieldValue('shippingcost', so.getFieldValue('shippingcost'));
    invoice.setFieldValue('discountitem', so.getFieldValue('discountitem'));
    invoice.setFieldValue('discountrate', so.getFieldValue('discountrate'));

    var arrayFields = [
        "custbody_bf_other_pymt_type", "custbody_bf_terms_amt", "custbody_bf_credit_type", "custbody_bf_credit_amt", "custbody_bf_cod_type", "custbody_order_cod_amt", "shipaddress", "billaddress"
    ]
    for (var i = 0; i < arrayFields.length; i++) {
        invoice.setFieldValue(arrayFields[i], so.getFieldValue(arrayFields[i]));
    }

    var id = nlapiSubmitRecord(invoice, true, true);
    
    nlapiLogExecution('DEBUG', 'Invoice Id', id);
    
    so.setFieldValue('custbody_invoice_number', id);
    var net30 = so.getFieldValue('custbody_vendortermspo') || null;
    var linesPayment = so.getLineItemCount('paymentevent');

    if (cod_type != null && codFee && linesPayment <= 0) {

        var t = nlapiLoadRecord('invoice', id);
        if (cod_type != "Net 30") {
            codFee = parseInt(codFee);
            nlapiLogExecution('ERROR', 'codFee', codFee);
            t.setFieldValue('handlingcost', codFee);
            t.setFieldValue('althandlingcost', codFee);
            nlapiSubmitRecord(t);

            so.setFieldValue('custbody_order_cod_amt', so.getFieldValue('total'));
        }
    } 	
    return id;
}

function createInvoicePayment(invoiceNumber) {
	
	var salesorder = nlapiLoadRecord('salesorder', nlapiGetRecordId());
    var source = salesorder.getFieldValue('source');
    var net30 = salesorder.getFieldValue('custbody_vendortermspo') || null;
    
    if (net30) {
        if (net30 == "Net 30") return;
    }

    // If order is created from WooCommerce, should process it
    if (source != 'Web (Charlotte Fabrics)') {
        if (!salesorder.getFieldValue('custbody_in8_wc_order_id')) {
            return;
        }
        //var paymentMethod = salesorder.getFieldValue('paymentmethod');
    
        //nlapiLogExecution('DEBUG', 'Payment Method', paymentMethod);
        
        // Do not create payment        
        //if (!paymentMethod || (paymentMethod && nlapiLookupField('paymentmethod', paymentMethod, 'creditcard') == 'F')) {
        
        if (salesorder.getFieldText('custbody_bf_credit_type') != 'Credit Card') {
        	
        	nlapiLogExecution('DEBUG', 'Not CC', 'Not CC transaction. Do not create payment.');
        	
        	return;
        }
    }

    if (salesorder.getFieldValue('custbody_payment_process') == 'T') return;

    salesorder.setFieldValue('custbody_payment_process', 'T');

    if (!invoiceNumber) {
    	invoiceNumber = salesorder.getFieldValue('custbody_invoice_number');	
    }        

    nlapiLogExecution('DEBUG', 'invoiceNumber', invoiceNumber);

    if (!invoiceNumber) return;

    var payment = nlapiTransformRecord('invoice', invoiceNumber, 'customerpayment'); //nlapiCreateRecord('customerpayment');

    //payment.setFieldValue('customer', salesorder.getFieldValue('entity'));
    payment.setFieldValue('payment', salesorder.getFieldValue('total'));

    var lines = payment.getLineItemCount('apply');

    for (var x = 1; x <= lines; x++) {

        if (payment.getLineItemValue('apply', 'internalid', x) == invoiceNumber) {
            payment.selectLineItem('apply', x);
            payment.setCurrentLineItemValue('apply', 'apply', 'T');
            payment.setCurrentLineItemValue('apply', 'total', salesorder.getFieldValue('total'));
            payment.commitLineItem('apply');
        }

    }
    payment.setFieldValue('custbody_test_order', salesorder.getId());

    var id = nlapiSubmitRecord(payment, true, true);

    nlapiSubmitRecord(salesorder, true, true);

    nlapiLogExecution('DEBUG', 'payment--', id);
}

function unsetCreditCard(customerId) {
    
	try {
        var customer = nlapiLoadRecord('customer', customerId);
        var countCreditCrds = customer.getLineItemCount('creditcards');
        if (countCreditCrds >= 1) {
            try {
                for (var xi = 1; xi <= countCreditCrds; xi++) {
                    customer.selectLineItem('creditcards', xi);
                    customer.setCurrentLineItemValue('creditcards', 'ccdefault', 'F');
                    customer.commitLineItem('creditcards');
                };
            } catch (e) {
                nlapiLogExecution('ERROR', 'ERROR', e);
            }

        } //end if
        nlapiSubmitRecord(customer, false, true);

    } catch (e) {
        nlapiLogExecution('ERROR', 'ERROR', e);
    }
}

function getAddress() {

	try {
        var record = nlapiGetNewRecord();
        var address = record.getFieldValue('custbody_default_address') || null;
        var billing_address = record.getFieldValue('custbody_default_billing_address') || null;

        if (!address) return;
        var customer = nlapiLoadRecord('customer', record.getFieldValue('entity'));

        var addressbook = customer.getLineItemCount('addressbook');

        for (var i = 1; i <= addressbook; i++) {
            var addressCompare = customer.getLineItemValue('addressbook', 'internalid', i);
            if (addressCompare == address) {
                customer.selectLineItem('addressbook', i);
                customer.setCurrentLineItemValue('addressbook', 'defaultshipping', "T");
                customer.commitLineItem('addressbook');
            }

            if (addressCompare == billing_address) {
                customer.selectLineItem('addressbook', i);
                customer.setCurrentLineItemValue('addressbook', 'defaultbilling', "T");
                customer.commitLineItem('addressbook');
            }
        }
        nlapiSubmitRecord(customer, false, true);
    } catch (e) {
        nlapiLogExecution('ERROR', 'ERROR', e);
    }
}