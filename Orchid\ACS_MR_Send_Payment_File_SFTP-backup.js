/**
 * @NApiVersion 2.x
 * @NScriptType MapReduceScript
 * @NModuleScope SameAccount
 */
 define(['N/file', "N/sftp", 'N/runtime', 'N/search', 'N/record', 'N/email'],
 function (file, sftp, runtime, search, record, email) {

     var filesSent = [];

         
     function pad(pad, str, padLeft) {
         try {
             if (typeof str === 'undefined')
                 return pad;
             if (padLeft) {
                 return (pad + str).slice(-pad.length);
             } else {
                 return (str + pad).substring(0, pad.length);
             }
         } catch (e) {
             log.error('pad error', e);
         }
     }

     /**
      * Marks the beginning of the Map/Reduce process and generates input data.
      *
      * @typedef {Object} ObjectRef
      * @property {number} id - Internal ID of the record instance
      * @property {string} type - Record type id
      *
      * @return {Array|Object|Search|RecordRef} inputSummary
      * @since 2015.1
      */
     function getInputData() {

         try {
             var scriptObj = runtime.getCurrentScript();
             var searchId = scriptObj.getParameter({ name: 'custscript_search_id' });

             // retrieve files created today
             var searchObj = search.load({
                 id: searchId
             });
     
             return searchObj;
         } catch (e) {
             log.error('getInputData error', e);
         }

     }

     /**
      * Executes when the map entry point is triggered and applies to each key/value pair.
      *
      * @param {MapSummary} context - Data collection containing the key/value pairs to process through the map stage
      * @since 2015.1
      */
     function map(context) {

         try {

             var scriptObj = runtime.getCurrentScript();

             // get PFA and File ID
             var pfaID = context.key;
             var contextValues = JSON.parse(context.value);
             var fileId = contextValues.values.custrecord_2663_file_ref.value;
             var type = contextValues.values.custrecord_2663_payment_type.text;
             var fileSent = contextValues.values.custrecord_acs_file_sent;

             // get SFTP Credentials here using script parameters
             var sftpHostKey = scriptObj.getParameter({ name: 'custscript_sftp_host_key' });
             var sftpUsername = scriptObj.getParameter({ name: 'custscript_sftp_username' });
             var sftpPort = scriptObj.getParameter({ name: 'custscript_sftp_port' });
             var sftpKeyId = scriptObj.getParameter({ name: 'custscript_sftp_key_id' });
             var sftpServerUrl = scriptObj.getParameter({ name: 'custscript_sftp_server_url' });
             var archiveId = scriptObj.getParameter({ name: 'custscript_archive_id' });

             // load file object
             var origFileObj = file.load({
                 id: fileId
             });

             var proceedWithTransmission = false
             
             var date = new Date;

             var dateTime = pad("00", (date.getMonth() + 1), true) + pad("00", date.getDate(), true) + date.getFullYear() + '_' + pad("00", date.getHours(), true) + pad("00", date.getMinutes(), true);

             if(type == 'EFT') {
                 var fileName = 'efm2899r.' + dateTime + '_' + pad("0000000", pfaID, true) + '.txt';
                 proceedWithTransmission = true;
             } else if (type == 'Positive Pay') {
                 proceedWithTransmission = true;
                 var fileName = 'crt0802r.' + dateTime + '_' + pad("0000000", pfaID, true) + '.txt';
             } else {
                 log.error('Wrong Payment Types!', 'Only payment types EFT and Positive Pay is currently supported. Please contact admin!');
             }
             

             if(proceedWithTransmission && (fileSent !== 'T')) {

                 // set the name based on bank's requirements and move to 'sent folder'

                 var fileObj = file.create({
                     name: fileName,
                     fileType: file.Type.PLAINTEXT,
                     contents: origFileObj.getContents(),
                     folder: archiveId
                 });

                 var newFileID = fileObj.save();

                 // reload file
                 var fileToBeSentObj = file.load({
                     id: newFileID
                 });
                 
                 
                 log.audit("Start SFTP Transmission", "-------- start SFTP transmission --------");


                 // connect to sftp server using script parameters
                 var sftpConnection = sftp.createConnection({
                     username: sftpUsername,
                     keyId: sftpKeyId,
                     url: sftpServerUrl,
                     port: Number(sftpPort),
                     hostKey: sftpHostKey
                 });

                 // send file to server
                 sftpConnection.upload({
                     file: fileToBeSentObj,
                     replaceExisting: true
                 });
                 
                 log.debug('File has been transmitted successfully!', { fileId: fileToBeSentObj.id, fileName: fileToBeSentObj.name });
                         
                 record.submitFields({
                     type: 'customrecord_2663_file_admin',
                     id: pfaID,
                     values: {
                         custrecord_acs_file_sent: true
                     }
                 });

             }
         } catch(e) {
             log.error('Map - Error during sftp upload', e);
         }
     }

     /**
      * Executes when the reduce entry point is triggered and applies to each group.
      *
      * @param {ReduceSummary} context - Data collection containing the groups to process through the reduce stage
      * @since 2015.1
      */
     function reduce(context) {

     }


     /**
      * Executes when the summarize entry point is triggered and applies to the result set.
      *
      * @param {Summary} summary - Holds statistics regarding the execution of a map/reduce script
      * @since 2015.1
      */
     function summarize(summary) {
             
         var scriptObj = runtime.getCurrentScript();
         var emailSuccess = scriptObj.getParameter({ name: 'custscript_email_success_transmit' });

         var emailBody = "Good Day!<br><br>Payment has been successfully transmitted. Transmitted files can be seen in the File Cabinet under Sent Payment Files.<br>"
         emailBody += '<ul>';
         for(var i = 0; i < filesSent.length; i++) {
             emailBody += '<li>' + filesSent[i] + '</li>';
         }
         emailBody += '</ul>';
         
         try {
             email.send({
                 author: 1341,
                 recipients: emailSuccess,
                 subject: 'Payment Transmission Successful',
                 body: emailBody
             });
         } catch (e) {
             log.error('Summarize - Error while trying to send email', e);
         }

     }

     return {
         getInputData: getInputData,
         map: map,
         reduce: reduce,
         summarize: summarize
     };

 });