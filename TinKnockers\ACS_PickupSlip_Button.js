/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define([], function() {

    function beforeLoad(context) {
        var getForm = context.form;
        getForm.clientScriptModulePath = 'SuiteScripts/ACS_PickupSlip_Redirect.js';
        getForm.addButton({
            id: 'custpage_print_packing_slip',
            label: 'Print as Pick Up Slip',
            functionName: 'redirectToPDF()'
        });
    }

    return {
        beforeLoad: beforeLoad
    }
});
