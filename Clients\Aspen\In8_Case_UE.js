/**
* User event for Cases
*
* Version		Date			Author				Remarks
* 1.0			14 Aug 2018		<PERSON> (In8Sync)	Initial Version
*
*/

/**
* @appliedtorecord recordtype
* 
* @param {String} type Operation types: create, edit, delete, xedit
*
* @returns {Void}
*/
function beforeSubmit(type) {

    try {
        if (type == 'create') {
            var entityId = nlapiGetFieldValue('custevent_in8_case_entity_id');

            if (entityId) {
                nlapiSetFieldValue('company', entityId);
            }
        }
    } catch(e) {
        nlapiLogExecution('ERROR', 'Error', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
    }    
}
