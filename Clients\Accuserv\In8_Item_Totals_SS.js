/**
 * In8 - Item Totals
 * 
 * Version    Date            Author           Remarks
 * 1.00       10 Oct 2016     <PERSON> (In8)   Initial Version
 *
 */

/**
 * @param {String} type Context Types: scheduled, ondemand, userinterface, aborted, skipped
 * @returns {Void}
 */
function scheduled(type) {
	
	try {
		var dateSync = getDateSync();
		
		dateSync = dateSync.substring(0, dateSync.length - 6);
		
		nlapiLogExecution('DEBUG', 'In8', 'Last Sync date: ' + dateSync);
				
		if (!dateSync) {
			firstTimeUpdate();	
		} else {					
			// Sync only differences
			syncFromDate(dateSync);
			
			// Update last sync date
			setDateSync();			
		}			
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
	}
}

function getDateSync() {
	
	var filters = [],
	    columns = [],
	    searchResults
	    i = 0;
	
	i = 0;
	
	columns[i++] = new nlobjSearchColumn('internalid');
	columns[i++] = new nlobjSearchColumn('custrecord_in8_item_sync_date');
	
	searchResults = nlapiSearchRecord('customrecord_in8_item_sync', null, filters, columns);
	
	if (searchResults && searchResults.length) {
		return searchResults[0].getValue('custrecord_in8_item_sync_date');
	}
	return null;
}

function setDateSync() {
	
	var filters = [],
	    columns = [],
	    searchResults
	    i = 0;
	
	i = 0;
	
	columns[i++] = new nlobjSearchColumn('internalid');
	columns[i++] = new nlobjSearchColumn('custrecord_in8_item_sync_date');
	
	searchResults = nlapiSearchRecord('customrecord_in8_item_sync', null, filters, columns);
	
	var dt = new Date();
	
	if (searchResults && searchResults.length) {
		nlapiSubmitField('customrecord_in8_item_sync', searchResults[0].getValue('internalid'), 'custrecord_in8_item_sync_date', dt);		
	} else {
		var record = nlapiCreateRecord('customrecord_in8_item_sync');
		record.setFieldValue('custrecord_date', dt);
		nlapiSubmitRecord(record, true, true);
	}	
}

function syncFromDate(dateSync) {
	
	var arrayItems = [];
	
	var filters = [],
	    columns = [],
	    searchResults,
	    i = 0;
	
	nlapiLogExecution('DEBUG', 'In8', 'Search updated estimates.');
	
	// Get List of estimates to be synced
	var filterExpression = [
		['lastmodifieddate', 'onorafter', dateSync ], 'AND',
		['item.lastmodifieddate', 'before', dateSync ]];
	
	i = 0;
	
	//columns[i++] = new nlobjSearchColumn('internalid', null, 'group');
	columns[i++] = new nlobjSearchColumn('item', null, 'group');
	
	var search = nlapiCreateSearch('estimate', filterExpression, columns);     
	
	var resultSet = search.runSearch();
	
	var start = 0;
	var end = 1000;
	
	var searchResults = resultSet.getResults(start, end);
	
	while (searchResults != null && searchResults.length > 0) {
		//searchResults = nlapiSearchRecord('estimate', null, filterExpression, columns);
		
		for (var i = 0; searchResults && i < searchResults.length; i++) {
			if (arrayItems.indexOf(searchResults[i].getValue('item', null, 'group')) == -1) {
				arrayItems.push(searchResults[i].getValue('item', null, 'group'));	
			}		
		}
		
		start = start + 1000;
		end = end + 1000;
	
		searchResults = resultSet.getResults(start, end);
	}
	
	nlapiLogExecution('DEBUG', 'In8', 'Search updated orders.');
	
	// Get list of Sales Orders or Fulfillments after the last synced date
	filterExpression = [
		['lastmodifieddate', 'onorafter', dateSync], 'AND',
		['type', 'anyof', ['SalesOrd', 'ItemShip']], 'AND',
		['item.lastmodifieddate', 'before', dateSync]];
	
	i = 0;
	
	//columns[i++] = new nlobjSearchColumn('internalid', null, 'group');
	columns[i++] = new nlobjSearchColumn('item', null, 'group');
	
	//searchResults = nlapiSearchRecord('transaction', null, filterExpression, columns);
	search = nlapiCreateSearch('transaction', filters, columns);     
	
	var resultSet = search.runSearch();
	
	var start = 0;
	var end = 1000;
	
	var searchResults = resultSet.getResults(start, end);
	
	while (searchResults != null && searchResults.length > 0) {
		for (var i = 0; searchResults && i < searchResults.length; i++) {
			if (arrayItems.indexOf(searchResults[i].getValue('item', null, 'group')) == -1) {
				arrayItems.push(searchResults[i].getValue('item', null, 'group'));	
			}		
		}
		start = start + 1000;
		end = end + 1000;
	
		searchResults = resultSet.getResults(start, end);
	}
	
	syncItems(arrayItems);
}

function syncItems(arrayItems) {
	
	var count = 0;
	
	arrayItems.sort();
	
	if (arrayItems.length) {
		nlapiLogExecution('DEBUG', 'In8', 'Items to be updated: ' + arrayItems.length);
		
		for (var i = 0; i < arrayItems.length; i++) {
			
			var itemId = arrayItems[i];
			
			if (itemId) {
				nlapiLogExecution('DEBUG', 'In8', 'Updating item Id ' + itemId);
				
				var recordType = nlapiLookupField('item', itemId, 'recordtype');
				
				if (recordType) {
					var record = nlapiLoadRecord(recordType, itemId);
					
					// Update unexpired estimates
					getUnexpiredEstimates(record, itemId);
					
					// Update last sales order date
					getLastSaleDate(record, itemId);
					
					try {
						nlapiSubmitRecord(record, true, true);
						
						count++;
					} catch(e) {
						nlapiLogExecution('DEBUG', 'In8', 'Error on item ' + itemId + ': ' + e);
					}						
				}	
			}
			
			var usage = nlapiGetContext().getRemainingUsage();
			
	        if (usage < 100) {
	        	nlapiLogExecution('DEBUG', 'In8', 'Items updated: ' + count);	        	
	        	return;		
	        }
		}		
		nlapiLogExecution('DEBUG', 'In8', 'Items updated: ' + count);
	} else {
		nlapiLogExecution('DEBUG', 'In8', 'Items updated: 0');
	}
}

function firstTimeUpdate() {
	
	var filters = [],
	    columns = [],
	    searchResults,
	    numberOfEstimates = 0,
	    i = 0;
	
	filters[i++] = new nlobjSearchFilter('custitem_in8_unexpired_estimates', null, 'isempty');
	
	i = 0;
	
	columns[i++] = new nlobjSearchColumn('internalid');
	
	var search = nlapiCreateSearch('item', filters, columns);     
	
	var resultSet = search.runSearch();
	
	var start = 0;
	var end = 1000;
	
	var searchResults = resultSet.getResults(start, end);
	
	while (searchResults != null && searchResults.length > 0)
	{
		for (var i = 0; i < searchResults.length; i++)
		{			
			var itemId = searchResults[i].getValue('internalid');
			
			if (itemId) {
				nlapiLogExecution('DEBUG', 'In8', 'Updating item Id ' + itemId);
				
				var record = nlapiLoadRecord(searchResults[i].recordType, itemId);
				
				// Update unexpired estimates
				getUnexpiredEstimates(record, itemId);
				
				// Update last sales order date
				getLastSaleDate(record, itemId);
				
				nlapiSubmitRecord(record, true, true);
			}
						
			// Test purposes
			if (i == 100) return;
		}
		start = start + 1000;
		end = end + 1000;
	
		searchResults = resultSet.getResults(start, end);
	}
}

/**
 * Get Unexpired Estimates
 * 
 * @returns {Void}
 */
function getUnexpiredEstimates(record, itemId) {

	var filters = [],
	    columns = [],
	    searchResults,
	    numberOfEstimates = 0,
	    i = 0;
	
	filters[i++] = new nlobjSearchFilter('item', null, 'is', itemId);
	filters[i++] = new nlobjSearchFilter('expectedclosedate', null, 'onorafter', 'today');
	
	i = 0;
	
	columns[i++] = new nlobjSearchColumn('internalid', null, 'group');
	columns[i++] = new nlobjSearchColumn('item', null, 'group');
	
	searchResults = nlapiSearchRecord('estimate', null, filters, columns);
	
	if (searchResults && searchResults.length) numberOfEstimates = searchResults.length
	
	// Set the number of unexpired estimates
	record.setFieldValue('custitem_in8_unexpired_estimates', numberOfEstimates);
}

/**
 * Get Last Sale Date
 * 
 * @returns {Void}
 */
function getLastSaleDate(record, itemId) {
	
	var filters = [],
	    columns = [],
	    searchResults,
	    lastSaleDate = '',
	    i = 0;
	
	filters[i++] = new nlobjSearchFilter('type', null, 'anyof', ['SalesOrd', 'ItemShip']);
	filters[i++] = new nlobjSearchFilter('item', null, 'is', itemId);
	
	i = 0;
	
	var column = new nlobjSearchColumn('trandate');
	column.setSort(true);
	
	columns[i++] = column;
	
	searchResults = nlapiSearchRecord('transaction', null, filters, columns);
	
	if (searchResults && searchResults.length) {
		lastSaleDate = searchResults[0].getValue('trandate');
	}
	
	// Set the last sale date
	record.setFieldValue('custitem_in8_last_sale_date', lastSaleDate);
}
