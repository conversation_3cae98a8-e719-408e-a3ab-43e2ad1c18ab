/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/record', 'N/search'], function(record, search) {

    function onRequest(context) {
         
        if(context.request.method == 'POST'){

            try {
                requestParams = context.request.parameters;
                salesOrderId = requestParams.sales_order_ids.split(',');

                if(salesOrderId.length) {
                    for(var i = 0; i < salesOrderId.length; i++) {
                        var timeStamp = search.lookupFields({
                            type: search.Type.TRANSACTION,
                            id: salesOrderId[i],
                            columns: ['custbody_rcvd_by_ty_timestamp']
                        });
                        
                        if(timeStamp.custbody_rcvd_by_ty_timestamp == '') {
                            record.submitFields({
                                type: record.Type.SALES_ORDER,  
                                id: salesOrderId[i],
                                values: {
                                    custbody_received_by_taylored: true,
                                    custbody_rcvd_by_ty_timestamp: new Date()
                                },
                                options: {
                                    enableSourcing: false,
                                    ignoreMandatoryFields : true
                                }
                            });
                        }
                    }

                    return {
                        success: 1,
                        message: "Successfully tagged orders"
                    };

                } else {
                    
                    return {
                        success: 1,
                        message: "No sales order id given"
                    }

                }

            } catch (e) {

                log.error('Error', e);

                return {
                    success: 0,
                    message: e.message
                }

            }
            
        }

    }

    return {
        onRequest: onRequest
    }
});
