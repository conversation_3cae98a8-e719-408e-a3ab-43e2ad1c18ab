/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search', 'N/format'], function(record, search, format) {

    function getOriginalContractID(contractId) {

        var origContractSearchValues = search.lookupFields({
            type: 'customrecord_contracts',
            id: contractId,
            columns: ['custrecord_swe_original_contract']
        });

        if(origContractSearchValues.custrecord_swe_original_contract) {
            return false;
        }

        return origContractSearchValues.custrecord_swe_original_contract[0].value;

    }

    function afterSubmit(context) {
        // if(context.type == context.UserEventType.CREATE) {
            log.debug('test', context.type);
            var recObj = record.load({
                type: 'customrecord_contract_item',
                id: context.newRecord.id,
                isDynamic: true
            });

            // get status of the contract item
            var status = contextRecObj.getValue({
                fieldId: 'custrecord_ci_status'
            });

            var date = contextRecObj.getValue({
                fieldId: 'created'
            });

            formattedDate = format.format({
                value: date,
                type: format.Type.DATE
            });

            dateNow = format.format({
                value: new Date(),
                type: format.Type.DATE
            });

            // if contract item is active and it has been created today
            if(status == 'Active' && (formattedDate == dateNow)) {

                var contractId = contextRecObj.getValue({
                    fieldId: 'custrecord_ci_contract_id'
                });

                var origContractId = getOriginalContractID(contractId);

                if(origContractId) {
                    var searchObj = search.load({
                        id: 'customsearch_acs_contract_item_search'
                    });

                    var contractIdFilter = search.createFilter({
                        name: 'custrecord_ci_contract_id',
                        operator: search.Operator.ANYOF,
                        values: [startdate]
                    });
                    glSearch.filters.push(contractIdFilter);    
        
                    var resultSet = searchObj.run();

                    searchResult = resultSet.getRange({ start: 0, end: 1 });
                    searchResult.forEach(function(row) {
                        row.columns.forEach(function(column) {
                            if(column.name != 'internalid'){
                                recObj.setValue({
                                    fieldId: column.name,
                                    value: row.getValue(column)
                                });
                            }
                        });
                    });
                }

                try{
                    recObj.save();
                } catch (e) {
                    log.debug("Error", e);
                }
            }
        // }
    }

    return {
        afterSubmit: afterSubmit
    }
});
