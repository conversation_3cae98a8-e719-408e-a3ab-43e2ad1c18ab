/**
 *@NApiVersion 2.1
 *@NScriptType ScheduledScript
 */
define(['N/sftp', 'N/file', 'N/runtime', 'N/task'], function(sftp, file, runtime, task) {
    
    function pad(pad, str, padLeft) {
        if (typeof str === 'undefined')
            return pad;
        if (padLeft) {
            return (pad + str).slice(-pad.length);
        } else {
            return (str + pad).substring(0, pad.length);
        }
    }

    function execute(context) {
        
        var scriptObj = runtime.getCurrentScript();
        
        var sftpHostKey = scriptObj.getParameter({ name: 'custscript_sftp_host_key' });
        var sftpUsername = scriptObj.getParameter({ name: 'custscript_sftp_username' });
        var sftpPort = scriptObj.getParameter({ name: 'custscript_sftp_port' });
        var sftpKeyId = scriptObj.getParameter({ name: 'custscript_sftp_key_id' });
        var sftpServerUrl = scriptObj.getParameter({ name: 'custscript_sftp_server_url' });
        var archiveId = scriptObj.getParameter({ name: 'custscript_archive_id' });
        var mappingId = scriptObj.getParameter({ name: 'custscript_mapping_id' });
        
        var date = new Date;
        var year = ("0"+date.getFullYear()).slice(-2);
        log.debug('year', year);
        var fileDate = pad("00", (date.getMonth() + 1), true) + '.' + pad("00", date.getDate(), true) + '.' + year;

        // connect to sftp server using script parameters
        var sftpConnection = sftp.createConnection({
            username: sftpUsername,
            keyId: sftpKeyId,
            url: sftpServerUrl,
            port: Number(sftpPort),
            hostKey: sftpHostKey
        });

        var filesinDirectory = sftpConnection.list({
            path: '/',
            sort: sftp.Sort.DATE_DESC
        });

        log.debug('test', filesinDirectory);

        var fileName = filesinDirectory[0].name;
        
        var downloadedFile = sftpConnection.download({
            filename: fileName
        });
        
        downloadedFile.folder = Number(archiveId);
        var csvID = downloadedFile.save();

        sftpConnection.removeFile({
            path: fileName
        });

        var scriptTask = task.create({
            taskType: task.TaskType.CSV_IMPORT
        });

        scriptTask.mappingId = mappingId;

        var fileObj = file.load({
            id: csvID
        });

        scriptTask.importFile = fileObj;
        var csvImportTaskId = scriptTask.submit();

        log.debug('Importing', csvImportTaskId);

    }

    return {
        execute: execute
    }
});
