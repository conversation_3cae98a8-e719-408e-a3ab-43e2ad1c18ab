/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', 'N/render', 'N/record', 'N/file'], function(https, render, record, file) {

    function onRequest(context) {
        if(context.request.method == https.Method.GET){
            requestParams = context.request.parameters;
            vendBillID = parseInt(requestParams.vbid);

            var xmlTemplateFile = file.load('SuiteScripts/acs_vendor_bill_template.xml');
            var renderer = render.create();

            // insert the template file content to the renderer
            renderer.templateContent = xmlTemplateFile.getContents();

            // ADD THE RECORD TO BE USED BASED ON PARAMETERS GIVEN
            renderer.addRecord({
                templateName: 'record',
                record: record.load({
                    type: record.Type.VENDOR_BILL,
                    id: vendBillID
                })
            });

            // RENDER AS STRING
            var packingSlipXML = renderer.renderAsString();

            // SEND RESPONSE XML TO RENDER AS PDF
            context.response.renderPdf({
                xmlString: packingSlipXML
            });
        }
    }

    return {
        onRequest: onRequest
    }
});
