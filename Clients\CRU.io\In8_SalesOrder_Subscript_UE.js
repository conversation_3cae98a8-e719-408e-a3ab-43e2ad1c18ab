/**
 * Subscription Items
 * 
 * Version    Date            Author           Remarks
 * 1.00       02 May 2017     Marcel P		   Initial Version
 *
 */

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord Sales Order
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      
 * @returns {Void}
 */
function beforeSubmit(type) {
 
	var subscriptionId = nlapiGetFieldValue('custbody_in8_subscription_id'),
		wcOrderId = nlapiGetFieldValue('custbody_in8_wc_order_id'),
		status = nlapiGetFieldValue('status'),
		recmac = 'recmachcustrecord_ps_subscription_pack',
		record,
		item,
		quantity,
		price,
		i = 0,
		i1 = 0;
	
	try {
      nlapiLogExecution('DEBUG', 'status ' + status);
      
		if ((type == 'edit' || type == 'create') && wcOrderId && subscriptionId) {
			
			//var rec = nlapiLoadRecord('salesorder', nlapiGetRecordId());
			
			nlapiLogExecution('DEBUG', 'Subscription Item ' + subscriptionId);
			
			record = nlapiLoadRecord('customrecord_ps_subscription_pack', subscriptionId);
			
			if (record) {
			
				// Clear the items
				for (i = nlapiGetLineItemCount('item'); i > 0; i--) {
					nlapiRemoveLineItem('item', i);
				}
				
				for (i1 = 1; i1 <= record.getLineItemCount(recmac); i1++) {
					
					item = record.getLineItemValue(recmac, 'custrecord_ps_subscription_pack_item', i1);
					quantity = record.getLineItemValue(recmac, 'custrecord_ps_item_quantity', i1);
				
					// Load the Item group
					var recordItemGroup = nlapiLoadRecord(nlapiLookupField('item', record.getLineItemValue(recmac, 'custrecord_ps_subscription_pack_item', i1), 'recordtype'), record.getLineItemValue(recmac, 'custrecord_ps_subscription_pack_item', i1));
					
					if (recordItemGroup && recordItemGroup.getLineItemCount('member') > 0) {
						
						// Load each member
						for (i = 1; i <= recordItemGroup.getLineItemCount('member'); i++) {
							
							nlapiLogExecution('DEBUG', 'In8Sync', 'Loading data from record: ' +  recordItemGroup.getLineItemValue('member', 'item', i));
							
							var recordItem = nlapiLoadRecord(nlapiLookupField('item', recordItemGroup.getLineItemValue('member', 'item', i), 'recordtype'), recordItemGroup.getLineItemValue('member', 'item', i));
							
							// Gets the price
							var currentPrice = In8Lib.getPricing(3, recordItem, null, 1) ? parseFloat(In8Lib.getPricing(3, recordItem, null, 1)) : 0;
														
							quantity = recordItemGroup.getLineItemValue('member', 'quantity', i);
							
							price += currentPrice;
							
							nlapiLogExecution('DEBUG', 'In8Sync', 'Adding item: ' +  item + ' - price: ' + (quantity * currentPrice));
							
							nlapiSelectNewLineItem('item');	
							nlapiSetCurrentLineItemValue('item', 'item', recordItemGroup.getLineItemValue('member', 'item', i));
							nlapiSetCurrentLineItemValue('item', 'price', 3);
							nlapiSetCurrentLineItemValue('item', 'quantity', quantity);
							nlapiSetCurrentLineItemValue('item', 'grossamt', quantity * currentPrice);
							//nlapiSetCurrentLineItemValue('item', 'rate', quantity * currentPrice);				
							nlapiCommitLineItem('item');
						}										
					}							
				}
				
				// Clear the field to not execute all the times
				nlapiSetFieldValue('custbody_in8_subscription_id', '');
				nlapiSetFieldValue('shippingcost', 0);
				//nlapiSubmitRecord(rec, true, true);
			}				
		}	
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error ', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));		
	}	
}




/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord Sales Order
 * 
 * @param {String} type Operation types: create, edit, delete, xedit
 *                      
 * @returns {Void}
 */
/*function afterSubmit(type) {
 
	var subscriptionId = nlapiGetFieldValue('custbody_in8_subscription_id'),
		wcOrderId = nlapiGetFieldValue('custbody_in8_wc_order_id'),
		status = nlapiGetFieldValue('status'),
		recmac = 'recmachcustrecord_ps_subscription_pack',
		record,
		item,
		quantity,
		price,
		i = 0,
		i1 = 0;
	
	try {
		if ((type == 'edit' || type == 'create') && wcOrderId && subscriptionId && (status == null || status == '' || status == 'Pending Fulfillment' || status == 'pending')) {
			
			var rec = nlapiLoadRecord('salesorder', nlapiGetRecordId());
			
			nlapiLogExecution('DEBUG', 'Subscription Item ' + subscriptionId);
			
			record = nlapiLoadRecord('customrecord_ps_subscription_pack', subscriptionId);
			
			if (record) {
			
				// Clear the items
				for (i = rec.getLineItemCount('item'); i > 0; i--) {
					rec.removeLineItem('item', i);
				}
				
				for (i1 = 1; i1 <= record.getLineItemCount(recmac); i1++) {
					
					item = record.getLineItemValue(recmac, 'custrecord_ps_subscription_pack_item', i1);
					quantity = record.getLineItemValue(recmac, 'custrecord_ps_item_quantity', i1);
				
					// Load the Item group
					var recordItemGroup = nlapiLoadRecord(nlapiLookupField('item', record.getLineItemValue(recmac, 'custrecord_ps_subscription_pack_item', i1), 'recordtype'), record.getLineItemValue(recmac, 'custrecord_ps_subscription_pack_item', i1));
					
					if (recordItemGroup && recordItemGroup.getLineItemCount('member') > 0) {
						
						// Load each member
						for (i = 1; i <= recordItemGroup.getLineItemCount('member'); i++) {
							nlapiLogExecution('DEBUG', 'In8Sync', 'Loading data from record: ' +  recordItemGroup.getLineItemValue('member', 'item', i));
							
							var recordItem = nlapiLoadRecord(nlapiLookupField('item', recordItemGroup.getLineItemValue('member', 'item', i), 'recordtype'), recordItemGroup.getLineItemValue('member', 'item', i));
							
							// Gets the price
							var currentPrice = In8Lib.getPricing(3, recordItem, null, 1) ? parseFloat(In8Lib.getPricing(3, recordItem, null, 1)) : 0;
							
							price += currentPrice;
							
							nlapiLogExecution('DEBUG', 'In8Sync', 'Adding item: ' +  item + ' - price: ' + (quantity * currentPrice));
							
							rec.selectNewLineItem('item');	
							rec.setCurrentLineItemValue('item', 'item', recordItemGroup.getLineItemValue('member', 'item', i));
							rec.setCurrentLineItemValue('item', 'price', 3);
							rec.setCurrentLineItemValue('item', 'quantity', quantity);
							rec.setCurrentLineItemValue('item', 'amount', quantity * currentPrice);
							rec.setCurrentLineItemValue('item', 'rate', quantity * currentPrice);				
							rec.commitLineItem('item');
						}										
					}							
				}
				
				nlapiSubmitRecord(rec, true, true);
			}				
		}	
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error ', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));		
	}	
}
*/
