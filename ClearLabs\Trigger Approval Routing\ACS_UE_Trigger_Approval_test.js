/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['../SuiteBundles/Bundle 20305/com.netsuite.suiteapprovals/src/dao/WD_SAP_DAO_MirrorApproval',
        '../SuiteBundles/Bundle 20305/com.netsuite.suiteapprovals/src/lib/WD_SAP_WorkflowWrapper',
        '../SuiteBundles/Bundle 20305/com.netsuite.suiteapprovals/src/lib/WD_SAP_TaskWrapper',
        '../SuiteBundles/Bundle 20305/com.netsuite.suiteapprovals/src/lib/WD_SAP_RuntimeWrapper',
        '../SuiteBundles/Bundle 20305/com.netsuite.suiteapprovals/src/lib/WD_SAP_Constants',
        '../SuiteBundles/Bundle 20305/com.netsuite.suiteapprovals/src/lib/WD_SAP_ScriptHelper',
        '../SuiteBundles/Bundle 20305/com.netsuite.suiteapprovals/src/lib/WD_SAP_PreferencesLib',
        '../SuiteBundles/Bundle 20305/com.netsuite.suiteapprovals/src/localization/WD_SAP_Dictionary'],
function(rec,
         workflow,
         task,
         runtime,
         Constants,
         Helper,
         PreferencesLib,
         Dictionary) {
    var dictionary;
    var approvalPrefStatus;
    var mirrorApproval;
    var mappingSetup;
    var newRecord;


    const APPROVAL_WORKFLOW_ID = 'customworkflow_sas_approval_workflow';
    const GET_APPROVAL_RULE_BTN = 'workflowaction_sas_get_rule';
    const MIRROR_APPROVAL_ID = 'customrecord_sas_mirror_approval';

    function afterSubmit(scriptContext) {
        if(context.Type == 'specialorder'){
                
            if (!initialize(scriptContext)) {
                return;
            }

            const record = scriptContext.newRecord;
            const recType = record.type;
            const recId = record.id;
            const cUser = runtime.getCurrentUser();

            if (!mirrorApproval && (cUser.id !== Constants.ReservedUserId.SYSTEM || recType === 'customrecord_sas_approval_logs')) {
                const mirrorApprovalId = createMirrorApproval(recType, recId, cUser, record);
                initiateWorkflow(mirrorApprovalId);
                initiateApprovalRuleSearch(mirrorApprovalId);
            }
        }
    }

    function initialize(scriptContext) {
        newRecord = scriptContext.newRecord;

        if (isApprovalRoutingRequired(newRecord) === false) {
            return;
        }

        const recType = newRecord.type;

        mappingSetup = Helper.getMappingSetup(recType);

        if (!mappingSetup) {
            log.error({
                title: 'Supported Records',
                details: 'Record does not have a Mapping Setup. Please contact your administrator.'
            });
            return;
        }

        const recId = parseInt(newRecord.id, 10);
        approvalPrefStatus = PreferencesLib.getApprovalPreferenceStatus(recType);

        mirrorApproval = Helper.getMirrorApproval(recType, recId);

        dictionary = new Dictionary();

        return true;
    }

    function createMirrorApproval(type, id, currUser, record) {
        if (type && id) {
            try {
                const newApprovalRecord = record.create({
                    type: 'customrecord_sas_mirror_approval',
                    isDynamic: true
                });

                newApprovalRecord.setValue({
                    fieldId: 'custrecord_sas_mar_ref_record_scriptid',
                    value: type
                });

                newApprovalRecord.setValue({
                    fieldId: 'custrecord_sas_mar_ref_record_internalid',
                    value: id
                });

                try {
                    const submitterId = currUser.id;

                    newApprovalRecord.setValue({
                        fieldId: 'custrecord_sas_mar_submitter',
                        value: submitterId
                    });
                } catch (e) {
                    log.error({
                        title: 'ERROR: Cannot set the record submitter!',
                        details: e.message || e.toString()
                    });
                    throw e;
                }

                // retain record submitter's preference since we cannot load an employee's preference (for PO cases)
                newApprovalRecord.setValue({
                    fieldId: 'custrecord_sas_mar_preferences',
                    value: JSON.stringify({
                        timezone: currUser.getPreference({
                            name: 'TIMEZONE'
                        }),
                        language: currUser.getPreference({
                            name: 'LANGUAGE'
                        })
                    })
                });

                const mirrorId = newApprovalRecord.save({
                    enableSourcing: true,
                    ignoreMandatoryFields: false
                });

                return mirrorId;
            } catch (e) {
                log.error({
                    title: 'ERROR: Cannot create Mirror Approval record!',
                    details: e.message || e.toString()
                });
                throw e;
            }
        }
    }

    function getSubmitter(record, currUser) {
        var employee = '';
        const employeeFld = mappingSetup.employeeFld;
        if (employeeFld) {
            employee = record.getValue(employeeFld);
        }
        return employee || currUser.id;
    }

    
    function initiateWorkflow(mirrorRecId) {
        try {
            workflow.initiate({
                recordType: MIRROR_APPROVAL_ID,
                recordId: mirrorRecId,
                workflowId: APPROVAL_WORKFLOW_ID
            });
            workflow.trigger({
                recordType: MIRROR_APPROVAL_ID,
                recordId: mirrorRecId,
                workflowId: APPROVAL_WORKFLOW_ID,
                actionId: GET_APPROVAL_RULE_BTN
            });
        } catch (e) {
            log.error({
                title: 'ERROR: Cannot initiate Approval Workflow!',
                details: e.message || e.toString()
            });
            throw e;
        }
    }


    function initiateApprovalRuleSearch(mirrorRecId) {
        try {
            task.create({
                taskType: task.TaskType.MAP_REDUCE,
                scriptId: 'customscript_sas_approval_rule_eval',
                deploymentId: 'customdeploy_sas_approval_rule_eval',
                params: {
                    // eslint-disable-next-line camelcase
                    custscript_sas_appruleeval_mirror_rec: mirrorRecId
                }
            }).submit();
        } catch (e) {
            log.error({
                title: 'ERROR! Cannot initiate Approval Rule search!',
                details: e.message || e.toString()
            });
            if (e.name !== 'MAP_REDUCE_ALREADY_RUNNING') {
                throw e;
            }
        }
    }
    return {
        afterSubmit: afterSubmit
    }
});
