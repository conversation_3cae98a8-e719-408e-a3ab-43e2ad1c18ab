function suitelet(request, response)
{
    if(request.getMethod() == "GET" && request.getParameter('copyrecord'))
    {

        try
        {

            var newRecord = nlapiCopyRecord('customrecord_in8_customsupportform', request.getParameter('copyrecord'));
            newRecord.setFieldValue('name', 'New Support Form Copy');

            var newRecordId = nlapiSubmitRecord(newRecord);

            var searchFields = nlapiSearchRecord('customrecord_in8_csf_fields', null, nlobjSearchFilter('custrecord_in8_csff_externalform', null, 'anyof', request.getParameter('copyrecord')));

            for(var i = 0; searchFields && i < searchFields.length; i++)
            {
                var newField = nlapiCopyRecord('customrecord_in8_csf_fields', searchFields[i].getId());
                newField.setFieldValue('custrecord_in8_csff_externalform', newRecordId);
                nlapiSubmitRecord(newField);
            }

            response.sendRedirect('RECORD', 'customrecord_in8_customsupportform', newRecordId, true);

        }
        catch(e)
        {
            response.writePage(e.getDetails());

        }

    }

}