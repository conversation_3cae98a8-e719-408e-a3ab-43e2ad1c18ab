<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="3">
                            <#if companyInformation.logoUrl?length !=0><img src="${companyInformation.logoUrl}"
                                    style="height: 70px; width: 350px; float: left; margin: 7px;" /> </#if>
                        </td>
                        <td align="right"><b style="font-size: 20px;">${record@title}</b></td>
                    </tr>
                    <tr>
                        <td align="right">Order Number: ${salesorder.tranid}</td>z
                    </tr>
                    <tr>
                        <td align="right">Order Date: ${salesorder.trandate}</td>
                    </tr>
                    <tr>
                        <td align="center"><span style="font-size:10px;">103 Pope&#39;s Island ~ New Bedford, MA
                                02740-7252</span></td>
                    </tr>
                    <tr>
                        <td align="center"><span style="font-size:10px;">www.ramitchell.com ~ <EMAIL></span>
                        </td>
                    </tr>
                    <tr>
                        <td align="center"><span style="font-size:10px;">Phone: ************ Fax: ************</span>
                        </td>
                    </tr>
                    <tr>
                        <td align="center"><span style="font-size:10px;">Generator Systems, Engines, Pumps, Parts and
                                Service</span></td>
                    </tr>
                    <tr>
                        <td align="center"><span style="font-size:10px;">WBE/DBE Certified</span></td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer">
                    <tr>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            td.addressheader {
                font-size: 8pt;
                font-weight: bold;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            div.returnform {
                width: 100%;
                /* To ensure minimal height of return form */
                height: 200pt;
                page-break-inside: avoid;
                page-break-after: avoid;
            }

            hr {
                border-top: 1px dashed #d3d3d3;
                width: 100%;
                color: #ffffff;
                background-color: #ffffff;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="15%" footer="nlfooter" footer-height="20pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader" style="font-size: 13px">${salesorder.shipaddress@label}</td>
            </tr>
            <tr>
                <td class="address" style="font-size: 15px">${salesorder.shipaddress}</td>
            </tr>
        </table>

        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th>${salesorder.linkedtrackingnumbers@label}</th>
                <th>${record.shipmethod@label}</th>
                <th>${record.shipphone@label}</th>
            </tr>
            <tr>
                <td>${salesorder.linkedtrackingnumbers}</td>
                <td>${record.shipmethod}</td>
                <td>${record.shipphone}</td>
            </tr>
        </table>
        <#if salesorder.item?has_content>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <thead>
                    <tr>
                        <th style="width: 35%; white-space: nowrap;">${salesorder.item[0].item@label}</th>
                        <th align="right" style="width: 11.6%; white-space: nowrap;">${salesorder.item[0].quantityordered@label}</th>
                        <th align="right" style="width: 11.6%; white-space: nowrap;">${salesorder.item[0].quantity@label}</th>
                        <th align="right" style="width: 11.6%; white-space: nowrap;">${salesorder.item[0].quantityremaining@label}</th>
                        <th align="left" style="width: 30%; white-space: nowrap;">${salesorder.item[0].inventorydetail@label}</th>
                    </tr>
                </thead>
                <#list salesorder.item as tranline>
                    <tr>
                        <td><span class="itemname">${tranline.item}</span><br />${tranline.description}</td>
                        <td align="right">${tranline.quantityordered}</td>
                        <td align="right">${tranline.quantity}</td>
                        <td align="right">${tranline.quantityremaining}</td>
                        <td align="left">${tranline.inventorydetail}</td>
                    </tr>
                </#list>
            </table>
        </#if>
        <br />
        <table width="100%">
            <tr>
                <th>Note Field</th>
            </tr>
            <tr>
                <td>${record.custbody_note_field}</td>
            </tr>
        </table>

        <#if preferences.RETURNFORM && returnForm??>

            <hr />
            <div class="returnform">
                <table style="width: 100%; margin-top: 10px;">
                    <tr>
                        <td><span class="nameandaddress">${companyInformation.companyName}</span></td>
                        <td align="right"><span class="number">${returnForm@title}</span></td>
                    </tr>
                </table>

                <table style="width: 100%; margin-top: 10px;">
                    <tr>
                        <td class="addressheader" colspan="4">${returnForm.returnAddress}</td>
                        <th>${returnForm.rmaNum}</th>
                        <th colspan="2">${returnForm.customerName}</th>
                        <th>${salesorder.tranid@label}</th>
                    </tr>
                    <tr>
                        <td class="address" colspan="4" rowspan="2">${companyInformation.returnaddress_text}</td>
                        <td>&nbsp;</td>
                        <td colspan="2">${salesorder.entity}</td>
                        <td>${salesorder.tranid}</td>
                    </tr>
                    <tr>
                        <td colspan="4">&nbsp;</td>
                    </tr>
                </table>

                <table class="itemtable" style="width: 100%; margin-top: 10px;">
                    <thead>
                        <tr>
                            <th colspan="2">${returnForm.item}</th>
                            <th colspan="1">${returnForm.quantity}</th>
                            <th colspan="5">${returnForm.reason}</th>
                        </tr>
                    </thead>
                    <tr>
                        <td colspan="8">&nbsp;</td>
                    </tr>
                </table>
            </div>
        </#if>
    </body>
</pdf>