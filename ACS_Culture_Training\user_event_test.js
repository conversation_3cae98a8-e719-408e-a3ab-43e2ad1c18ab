/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(["N/currentRecord"], function(currentRecord) {

    function beforeLoad(scriptContext) {

        // Only executes if in EDIT mode

          if(scriptContext.type == scriptContext.UserEventType.EDIT)
          {

            var recordObj = currentRecord.get();
            recordObj.setValue({
                fieldId: 'comments',
                value: 'test'
            });

            recordObj.save();

        //           // Gets the current record

        //           var recordObj = scriptContext.newRecord;

        //           // Get the type for validation

        //           var recordType = recordObj.type;

        //           // Internal ID of the record

        //           var recordId = recordObj.id;
        //           log.debug({
        //                 title: 'recordId',
        //                 details: recordId + " " + recordType
        //             });

                          

        //           recordObj.setValue({

        //                   fieldId:'comments',
        //                   value:'Test'
        //           });

        //    recordObj.save();

            }

      }

    function beforeSubmit(context) {
        
    }

    function afterSubmit(context) {
        
    }

    return {
        beforeLoad: beforeLoad,
        beforeSubmit: beforeSubmit,
        afterSubmit: afterSubmit
    }
});