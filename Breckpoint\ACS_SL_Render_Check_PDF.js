/**
 *@NApiVersion 2.1
 *@NScriptType Suitelet
 */
define(['N/render', 'N/search', 'N/format/i18n', 'N/record'], function(render, search, format, record) {

    var recTypes = {
        "Check": "check",
        "CustRfnd": "customerrefund",
        "VendPymt": "vendorpayment",
    }

    // System for American Numbering 
    var th_val = ['', 'thousand', 'million', 'billion', 'trillion'];
    
    var dg_val = ['zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
    var tn_val = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
    var tw_val = ['twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
    function toWordsconver(s) {
        s = s.toFixed(2);
        s = s.replace(/[\, ]/g, '');
        if (s != parseFloat(s))
            return 'not a number ';
        var x_val = s.indexOf('.');
        if (x_val == -1)
            x_val = s.length;
        if (x_val > 15)
            return 'too big';
        var n_val = s.split('');
        var str_val = '';
        var sk_val = 0;
        for (var i = 0; i < x_val; i++) {
            if ((x_val - i) % 3 == 2) {
                if (n_val[i] == '1') {
                    str_val += tn_val[Number(n_val[i + 1])] + ' ';
                    i++;
                    sk_val = 1;
                } else if (n_val[i] != 0) {
                    str_val += tw_val[n_val[i] - 2] + ' ';
                    sk_val = 1;
                }
            } else if (n_val[i] != 0) {
                str_val += dg_val[n_val[i]] + ' ';
                if ((x_val - i) % 3 == 0)
                    str_val += 'hundred ';
                sk_val = 1;
            }
            if ((x_val - i) % 3 == 1) {
                if (sk_val)
                    str_val += th_val[(x_val - i - 1) / 3] + ' ';
                sk_val = 0;
            }
        }
        // if (x_val != s.length) {
            var y_val = s.length;
            str_val += 'and ';

            var ctr = 0;
            for (var i = x_val + 1; i < y_val; i++){
                str_val += n_val[i];
                ctr++;
            }

            if(ctr != 0) { 
                str_val += '/100';
            } else {
                str_val += '00/100';
            }
        // }
        return str_val.replace(/\s+/g, ' ');
    }

    function onRequest(context) {
        
        if(context.request.method == 'GET'){
            
            var paymentIds = context.request.parameters.payments;
            paymentIds = paymentIds.split(',');

            // create PDF
            var renderer = render.create();

            renderer.setTemplateByScriptId({
                scriptId: "CUSTTMPL_ACS_CUSTOM_CHECK"
            });

            var checks = [];
                
            var billPaymentsSearch = search.load({
                id: 'customsearch_acs_search_bill_payments'
            });

			var idFilter = search.createFilter({
				name: 'internalid',
				operator: search.Operator.ANYOF,
				values: paymentIds
			});

            billPaymentsSearch.filters.push(idFilter);
            
            billPaymentsSearch.run().each(function(result){

                var curFormatter = format.getCurrencyFormatter({ currency: result.getText({ name: 'currency' }) });
                var amount = result.getValue({ name: "amount" });
                var formattedAmt = curFormatter.format({ number: Number(amount) });

                var type = recTypes[result.getValue({ name: "type" })]

                var paymentRec = record.load({
                    id: result.getValue({ name: "internalid" }),
                    type: type,
                    isDynamic: true
                });

                var address = paymentRec.getText({ fieldId: 'address' });
                address = address.split("\r\n").join("<br />");

                var check = {
                    checknumber: result.getValue({ name: "tranid" }),
                    trandate: result.getValue({ name: "trandate" }),
                    entity: result.getText({ name: "entity" }),
                    address: address,
                    totalinwords: toWordsconver(Number(amount)),
                    total: formattedAmt,
                    memo: result.getValue({ name: "memo" })
                }

                checks.push(check);

                return true
            });

            renderer.addCustomDataSource({

                format: render.DataSource.OBJECT,
                alias: "records",
                data:{
                    checks: checks
                }
            });

            var xmlString = renderer.renderAsString();

            context.response.renderPdf(xmlString);
            
        }
    }

    return {
        onRequest: onRequest
    }
});
