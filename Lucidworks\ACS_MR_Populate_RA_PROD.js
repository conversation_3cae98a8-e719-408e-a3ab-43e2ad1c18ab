/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/record', 'N/search'], function (record, search) {

    function getRevenueArrangement(revPlanID) {

        // get rev element object from rev plan
        // Revenue Plan Obj -> Revenue Element Obj -> Revenue Arrangement ID
        var revenueplanSearchObj = search.create({
            type: "revenueplan",
            filters: [
                ["recordnumber", "startswith", revPlanID]
            ],
            columns: [
                search.createColumn({
                    name: "revenuearrangement",
                    join: "revenueElement",
                    label: "Revenue Arrangement"
                })
            ]
        });

        var resultSet = revenueplanSearchObj.run(); 
        var result = resultSet.getRange({
                start : 0,
                end : 1
        })[0];

        return result.getValue({ name: 'revenuearrangement', join: 'revenueElement' });

    }

    function getInputData() {
        var journalentrySearchObj = search.create({
            type: "journalentry",
            filters: [
                ["type", "anyof", "Journal"],
                "AND",
                ["memo", "startswith", "Rev Rec"],
                "AND",
                ["mainline", "is", "T"],
                "AND", 
                ["custcol_acs_ra","isempty",""]
            ],
            columns: [
                search.createColumn({
                    name: "internalid",
                    label: "Internal ID"
                }),
                search.createColumn({
                    name: "linesequencenumber",
                    label: "Line Sequence Number"
                })
            ]
        });

        var pagedData = journalentrySearchObj.runPaged({ pageSize: 10 });
        var resultArray = [];
        // iterate the pages
        // for( var i=0; i < 20; i++ ) {
        pagedData.pageRanges.forEach(function(pageRange){

    
            // fetch the current page data
            var currentPage = pagedData.fetch({index: pageRange.index});
            // var currentPage = pagedData.fetch(i);
    
            // and forEach() thru all results
            currentPage.data.forEach( function(result) {
    
                resultArray.push({
                    transId: result.id,
                    lineNo: result.getValue('linesequencenumber')
                });
    
            });
    
        });
        // }

        return resultArray;

    }

    function map(context) {
        // accept data from previous stage
        var keyFromInput = context.key;
        var valueFromInput = context.value;

        var recordObj = JSON.parse(valueFromInput);
        
        try {
            // var dynamicJournalObj = record.load({
            //     type: 'journalentry',
            //     id: recordObj.transId,
            //     isDynamic: true
            // });
            
            var journalObj = record.load({
                type: 'journalentry',
                id: recordObj.transId
            });

            var revPlanID = journalObj.getSublistValue({
                sublistId: 'line',
                fieldId: 'sourcerevenueplan',
                line: recordObj.lineNo
            });
            
            journalObj.setSublistValue({
                sublistId: 'line',
                fieldId: 'custcol_acs_ra',
                line:  recordObj.lineNo,
                value: getRevenueArrangement(revPlanID)
            });

            var savedId = journalObj.save();
            log.debug('Map - Saved', "Successfully saved " + savedId);


        } catch (e) {
            log.debug('error', e);
        }

    }

    function reduce(context) {
    }

    function summarize(summary) {

    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});
