/**
 * Copyright (c) 1998-2017 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * NetSuite, Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with NetSuite.
 * 
 * RMA From Case - constants used on scripts
 * 
 * Version    Date            Author            Remarks
 * 1.00       14 Apr 2017     rilagan	       Initial version.
 * 
 */


 var TRANS_LINES_THRESHOLD = 100;


 /**
  * @Section: CUSTOM RECORD
  */
 var HC_RFC_RECORDS = {         
     RFC_PREF : {
         ID     :  'customrecord_nsts_rfc_pref_record',
         FIELDS : {
              CUSTRECORD_NSTS_RFC_ENABLE_CREATE		: 'custrecord_nsts_rfc_enable_create',			
              CUSTRECORD_NSTS_RFC_DEF_SO_FORM			: 'custrecord_nsts_rfc_def_so_form',					
              CUSTRECORD_NSTS_RFC_DEF_INV_FORM		: 'custrecord_nsts_rfc_def_inv_form',				
              CUSTRECORD_NSTS_RFC_DEF_RMA_FORM		: 'custrecord_nsts_rfc_def_rma_form',				
              CUSTRECORD_NSTS_RFC_DEF_SS				: 'custrecord_nsts_rfc_def_ss',						
              CUSTRECORD_NSTS_RFC_SET_CASE_STAT		: 'custrecord_nsts_rfc_set_case_stat',				
              CUSTRECORD_NSTS_RFC_DEF_MEMO			: 'custrecord_nsts_rfc_def_memo',					
              CUSTRECORD_NSTS_RFC_DEF_SEARCH_TRANS_TYP: 'custrecord_nsts_rfc_def_search_trans_typ',		
              CUSTRECORD_NSTS_RFC_DAYS_DIFF			: 'custrecord_nsts_rfc_days_diff',					
              CUSTRECORD_NSTS_RFC_DATE_CREATE_TRANS	: 'custrecord_nsts_rfc_date_create_trans',			
              CUSTRECORD_NSTS_RFC_MAX_ROW				: 'custrecord_nsts_rfc_max_row'
         }
     }       
 }
 var HC_RFC_CASE = {
         FIELDS : {
             CUSTEVENT_NSTS_RFC_SEARCH_TRANS_NO		: 'custevent_nsts_rfc_search_trans_no',			
              CUSTEVENT_NSTS_RFC_CREATE_TRANS			: 'custevent_nsts_rfc_create_trans',					
              CUSTEVENT_NSTS_RFC_SEARCH_TRANS_TYPE	: 'custevent_nsts_rfc_search_trans_type',				
              CUSTEVENT_NSTS_RFC_TRANS_TO				: 'custevent_nsts_rfc_trans_to',				
              CUSTEVENT_NSTS_RFC_TRANS_FROM			: 'custevent_nsts_rfc_trans_from'
         }
 }
 
 var HC_RFC_RELATED_RECORDS = {
         ID     :  'customrecord_nsts_rfc_case_related_rec',
         FIELDS : {
             ID											: 'internalid',
             CUSTRECORD_NSTS_RFC_CASE					: 'custrecord_nsts_rfc_case',			
             CUSTRECORD_NSTS_RFC_RELATED_RECORDS			: 'custrecord_nsts_rfc_related_records',					
             CUSTRECORD_NSTS_RFC_CREATED_TRANS			: 'custrecord_nsts_rfc_created_trans'
         }
 }
 
 /**
  * @Section: SAVED SEARCHES
  */
 var HC_RFC_SAVED_SEARCHES = {  
     SS_RFC_SOURCE_TRANS 	: 'customsearch_nsts_rfc_source_trans_rpt'
 }
 
 /**
  * @Section: SCRIPT
  */
 var HC_RFC_SCRIPT = {  
     SEARCH_TRANS_SL : 'customscript_nsts_rfc_searchtrans_sl',
     CREATE_TRANS_SC : 'customscript_nsts_rfc_create_trans_sc'
 }
 
 /**
  * @Section: SCRIPT PARAMETERS
  */
 var HC_RFC_SCRIPT_PARAMS = {  
     RECORD_TYPE 		: 'custscript_nsts_rfc_record_type',
     SELECTED_TRANS 		: 'custscript_nsts_rfc_selected_trans',
     SUBMITTED_CASE		: 'custscript_nsts_rfc_submitted_case',
     SELECTED_LINES		: 'custscript_nsts_rfc_selected_lines',
     TRAN_DATE_REF		: 'custscript_nsts_rfc_trans_date_ref',
     MEMO				: 'custscript_nsts_rfc_memo',
     CREATION_ACTION		: 'custscript_nsts_rfc_creation_action',
     DEFAULT_TRANS_FORM	: 'custscript_nsts_rfc_default_trans_form'
 }
 
 /**
  * @Section: SCRIPT DEPLOYMENT
  */
 var HC_RFC_SCRIPT_DEPLOYMENT = {  
     UE_PREFERENCE 	: 'customdeploy_nsts_lt_filter_lot_trace_sl',
     UE_CASE 		: 'customdeploy_nsts_rfc_case_ue',
     CS_CASE			: 'customdeploy_nsts_rfc_validate_case_cs',
     SEARCH_TRANS_SL : 'customdeploy_nsts_rfc_searchtrans_sl',
     CREATE_TRANS_SC : 'CUSTOMDEPLOY_NSTS_RFC_CREATE_TRANS_SC'
 }
 
 /**
  * @Section: LIST TRANSACTION TYPE
  */
 var HC_RFC_LIST_TRANSACTION_TYPE = {  
     INVOICE : '1',
     SALESORDER : '2' 
 }
 /**
  * @Section: LIST TRANSACTION TYPE
  */
 var HC_RFC_LIST_CREATE_TRANS = {  
     CREATE_RMA : '1',
     CREATE_SO : '2' 
 }
 
 /**
  * @Section: LIST DATE TYPE
  */
 var HC_RFC_LIST_DATE_TYPE = {  
     TRANSACTION : '1',
     SYSTEM : '2',
     CASE: '3'
 }
 
 /**
  * @Section: SUITELET FIELDS
  */
 var HC_RFC_SUITELET_SHOW_TRANS = {
         Title: 'Sales Order / Invoice Search',
         Fields: {
             CASE: {
                 ID      : 'custpage_nsts_rfc_case',
                 LABEL   : 'Case',
                 HELP	: 'The case number, based on which the transaction would be created'
             },
             CUSTOMER: {
                 ID      : 'custpage_nsts_rfc_customer',
                 LABEL   : 'Customer',
                 HELP	: 'The customer linked to the case and the transactions'
             },
             TRANSACTION_FROM: {
                 ID      : 'custpage_nsts_rfc_trans_from',
                 LABEL   : 'Transaction  From',
                 HELP	: 'The oldest date of the transaction that would be included in the search. This date is calculated with reference to the to date and the days differential'
             },
             TRANSACTION_TO: {
                 ID      : 'custpage_nsts_rfc_trans_to',
                 LABEL   : 'Transaction To',
                 HELP	: 'The latest date of the transaction that would be included in the search'
             },
             SEARCH_TYPE: {
                 ID      : 'custpage_nsts_rfc_search_type',
                 LABEL   : 'Search Transaction Type',
                 HELP	: 'Set the transaction type that would be retrieved. The options are Invoices or Sales Orders'
             },
             DATE_CREATE: {
                 ID      : 'custpage_nsts_rfc_date_trans',
                 LABEL   : 'Date on Create Transaction',
                 HELP	: 'Set the Date on the Transaction created from the Case record. The options are System Date, Source Transaction Date or the Case Date'
             },
             CREATION_ACTION: {
                 ID      : 'custpage_nsts_rfc_create_trans',
                 LABEL   : 'Create Transaction',
                 HELP	: 'Set the transaction type that should be created. The options are Return Material Authorization or Sales Order'
             },
             PAGE_BUTTONS: {
                 ID      : 'custpage_nsts_rfc_page_btns',
                 LABEL   : 'Table Buttons'
             },
             TREE_RESULTS: {
                 ID      : 'custpage_nsts_rfc_tree_results',
                 LABEL   : 'Tree Results'
             },
             TRANSACTION_NO: {
                 ID      : 'custpage_nsts_rfc_trans_no',
                 LABEL   : 'Transaction No',
                 HELP	: 'Enter the transaction/document number (fieldid: tranid) that is reported in this case.'
             },
             SELECTED_TRANS: {
                 ID      : 'custpage_nsts_rfc_sel_trans',
                 LABEL   : 'Selected Trans Internal ID'
             },
             DEFAULT_MEMO: {
                 ID      : 'custpage_nsts_rfc_def_memo',
                 LABEL   : 'Default Memo Text',
                 HELP	: 'Set the Default Memo Text in the transaction created.'
             },
             SELECTED_LINES: {
                 ID      : 'custpage_nsts_rfc_sel_lines',
                 LABEL   : 'Lines'
             },
             SELECTED_TYPE: {
                 ID      : 'custpage_nsts_rfc_sel_rectype',
                 LABEL   : 'Selected Record Type'
             },
             CURR_PAGE: {
                 ID      : 'custpage_nsts_rfc_curr_page',
                 LABEL   : 'Curr Page'
             },
             LAST_PAGE: {
                 ID      : 'custpage_nsts_rfc_last_page',
                 LABEL   : 'Last Page'
             },
             TABLE_PAGE: {
                 ID      : 'custpage_nsts_rfc_t_page',
                 LABEL   : 'Pages'
             },
             ACTION_TYPE: {
                 ID      : 'custpage_nsts_rfc_act_type',
                 LABEL   : 'Action Type'
             },
             PLUS_URL: {
                 ID      : 'custpage_nsts_rfc_plus',
                 LABEL   : 'PLUS'
             },
             MINUS_URL: {
                 ID      : 'custpage_nsts_rfc_minus',
                 LABEL   : 'MINUS'
             },
             LOADER_URL: {
                 ID      : 'custpage_nsts_rfc_loader',
                 LABEL   : 'LOADER'
             },
             SEPARATOR: {
                 ID      : 'custpage_nsts_rfc_separator',
                 LABEL   : 'SEPARATOR'
             },
             CUSTOMER_ID: {
                 ID      : 'custpage_nsts_cust_id',
                 LABEL   : 'customer id'
             },
             CASE_SEL_TYPE: {
                 ID      : 'custpage_nsts_casesel_type',
                 LABEL   : 'case selected  type'
             },
             MULT_CURR: {
                 ID      : 'custpage_nsts_mult_curr',
                 LABEL   : 'mult curr'
             }
         },
         Field_Group:{
             CASE: {
                 ID      : '_nsts_rfc_case',
                 LABEL   : 'Case Details'
             },
             FILTER: {
                 ID      : '_nsts_rfc_filter',
                 LABEL   : 'Search Filter'
             },
             RESULTS: {
                 ID      : '_nsts_rfc_results',
                 LABEL   : 'Results'
             },
         }
 }
 
 /**
  * @Section: OTHER HARDCODED VARS
  */
 var HC_OTHERS = {  
         BTN_CSS 	: '' +
                     '<style type="text/css">' +
                         '#btnEnabled:link, #btnEnabled:visited, #btnEnabled:active {font-size: 14px !important; font-weight: 600; padding: 3px 12px !important; background-color: #f5f5f5; margin-right: 15px; margin-bottom: 40px; border-radius: 3px; border: 1px solid #999999 !important; text-decoration: none; line-height: 50px !important;}' +
                         '#btnEnabled:hover {background-color: #e4e4e4}' + 
                         '#btnDisabled:link, #btnDisabled:visited, #btnDisabled:active {color: #5d5d5d; font-size: 14px !important; font-weight: 600; padding: 3px 12px !important; background-color: #e4e4e4; margin-right: 15px; margin-bottom: 40px; border-radius: 3px; border: 1px solid #999999 !important; text-decoration: none; line-height: 50px !important;}' +
                         '#btnDisabled:hover {cursor: default;}' + 
                     '</style>',
         SEPARATOR_CSS 	: '' +
                     '<style type="text/css">' +
                         '#ns_navigation { padding: 0 20px 0 20px !important; background-color: #607799 !important; min-height:38px;min-width:300px;}' +						
                     '</style>',
 
         TABLE_TREE 	: '' +
                     '<style type="text/css">' +
                     'table#{tableid} { font-size: 120% !important; border-collapse: collapse; border: #E5E5E5 1px solid;}' +
                     'table#{tableid} th { background-color: #E5E5E5 !important; text-transform: uppercase; padding: 5px; padding-left: 8px}' +
                     'table#{tableid} td { border: none !important; border-bottom: 1px solid #EBEBEB !important; padding: 5px;}' +
                     'table#{tableid} tr:hover { background-color: #fffff2; }' +
                     '#btnEnabled:link, #btnEnabled:visited, #btnEnabled:active {font-size: 14px !important; font-weight: 600; padding: 3px 12px !important; background-color: #f5f5f5; margin-right: 15px; margin-bottom: 40px; border-radius: 3px; border: 1px solid #999999 !important; text-decoration: none; line-height: 50px !important;}' +
                     '#btnEnabled:hover {background-color: #e4e4e4}' + 
                     '#btnDisabled:link, #btnDisabled:visited, #btnDisabled:active {color: #5d5d5d; font-size: 14px !important; font-weight: 600; padding: 3px 12px !important; background-color: #e4e4e4; margin-right: 15px; margin-bottom: 40px; border-radius: 3px; border: 1px solid #999999 !important; text-decoration: none; line-height: 50px !important;}' +
                     '#btnDisabled:hover {cursor: default;}' + 
                 '</style>'
                     
     };
 
 /**
  * @Section: RECORD
  * 
  * JLG - ADDED NEW FIELDS 01/27/2021
  */
 var HC_NS_RECORDS = {
         COMMON: {
             RECORDS: {
                 INVENTORY_NUMBER: 'inventorynumber',
                 LOCATION        : 'location',
                 ITEM            : 'item',
                 SUBSIDIARY      : 'subsidiary'
             },
             FIELDS: {
                 INTERNALID  : 'internalid',
                 ITEM        : 'item',
                 LOCATION    : 'location',
                 SUBSIDIARY  : 'subsidiary',
                 NAME        : 'name',
                 ISLOTITEM   : 'islotitem',
                 ITEMID      : 'itemid',
                 TRAN_DATE   : 'trandate',
                 MEMO   		: 'memo',
                 FORM   		: 'customform',
                 ENTITY 		: 'entity',
                 SALES_REP	: 'salesrep',
                 PARTNER		: 'partner',
                 LEAD_SOURCE	: 'leadsource',
                 DEPARTMENT	: 'department',
                 CLASS		: 'class',
                 LOCATION	: 'location',
                 CURRENCY	: 'currency',
                 STATUS		: 'orderstatus',
                 MARKETPLACE : 'custbody_ns_market_place_order_number',
                 CHNNELADVSR : 'custbody_ns_channeladviso_order_number'
             },
             SUBLIST_ID: {
                 ITEM: 'item'
             },
             SUBLIST_FIELD: {
                 ITEM			: 'item',
                 DESCRIPTION		: 'description',
                 QUANTITY		: 'quantity',
                 RATE			: 'rate',
                 AMOUNT			: 'amount',
                 LINE_UNIQUE_KEY	: 'lineuniquekey',
                 TAX_CODE		: 'taxcode',
                 UNITS			: 'units'
             }
         },
         INVENTORY_NUMBER: {
             ID      : 'inventorynumber',
             FIELDS  : {
                 ITEM_SUBSIDIARY : 'item.subsidiary',
                 ITEM            : 'item',
                 LOCATION        : 'location',
                 ITEM_ISLOTITEM  : 'item.islotitem',
                 INVENTORYNUMBER : 'inventorynumber',
                 QUANTITYONHAND  : 'quantityonhand'
             }
         },
         CASE: {
             ID      : 'supportcase',
             FIELDS  : {
                 CREATED_DATE : 'createddate',
                 COMPANY : 'company',
                 SUBSIDIARY : 'subsidiary',
                 CASE_NUMBER : 'casenumber'
             }
         },
         RETURN_AUTHORIZATION: {
             ID      : 'returnauthorization',
             FIELDS  : {
                 CUSTOMER : 'entity',
                 FORM : 'customform',
                 TRAN_DATE : 'trandate',
                 MEMO : 'memo'
             }
         },
         SALES_ORDER: {
             ID      : 'salesorder',
             FIELDS  : {
                 FORM : 'customform',
                 TRAN_DATE : 'trandate',
                 MEMO : 'memo'
             }
         },
         ITEM_RECEIPT: {
             ID      : 'itemreceipt',
             FIELDS  : {
                 CREATED_FROM : 'createdfrom',
                 UPDATE_CASE_STATUS : 'custbody_nsts_rfc_update_case_status'
             }
         }
     };
 