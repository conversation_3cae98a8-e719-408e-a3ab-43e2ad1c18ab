/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/record', 'N/currentRecord'], function(record, currentRecord) {

    function fieldChanged(context){
        var curRecord = context.currentRecord;
        var sublistId = context.sublistId;
        var fieldId = context.fieldId;
        if(fieldId == 'custcoljob_listing' || fieldId == 'customer'){
            var custJobId = curRecord.getCurrentSublistValue({
                sublistId: sublistId,
                fieldId: fieldId
            });
            
            if(custJobId){
                var custJob = record.load({
                    type: record.Type.JOB,
                    id: custJobId,
                    isDynamic: true,
                });
                
                var specialty = custJob.getValue({ fieldId: 'cseg_specialty_' });
                var profession = custJob.getValue({ fieldId: 'custentity_profession' });
                var clientManager = custJob.getValue({ fieldId: 'custentity_client_manager' });
                var recruiter = custJob.getValue({ fieldId: 'custentity_recruiter_sourced' });
                var projStart = custJob.getValue({ fieldId: 'startdate' });
                var projEnd = custJob.getValue({ fieldId: 'enddate' });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'cseg_specialty_',
                    value: specialty,
                    ignoreFieldChange: true
                });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'department',
                    value: profession,
                    ignoreFieldChange: true
                });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'custcol_client_manager',
                    value: clientManager,
                    ignoreFieldChange: true
                });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'custcol_recruiter',
                    value: recruiter,
                    ignoreFieldChange: true
                });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'custcol_project_start_date',
                    value: projStart,
                    ignoreFieldChange: true
                });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'custcol_project_end_date',
                    value: projEnd,
                    ignoreFieldChange: true
                });
            } else {
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'cseg_specialty_',
                    value: "",
                    ignoreFieldChange: true
                });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'department',
                    value: "",
                    ignoreFieldChange: true
                });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'custcol_client_manager',
                    value: "",
                    ignoreFieldChange: true
                });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'custcol_recruiter',
                    value: "",
                    ignoreFieldChange: true
                });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'custcol_project_start_date',
                    value: "",
                    ignoreFieldChange: true
                });
                curRecord.setCurrentSublistValue({
                    sublistId: sublistId,
                    fieldId: 'custcol_project_end_date',
                    value: "",
                    ignoreFieldChange: true
                });
            }
        }
    }

    return {
        fieldChanged: fieldChanged
    }
});
