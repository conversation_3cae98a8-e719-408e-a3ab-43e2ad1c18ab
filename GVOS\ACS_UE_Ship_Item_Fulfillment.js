/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search', 'N/workflow'], function(record, search, workflow) {

    /**
     * 
     * @param {string} address - shipaddress of the item fulfillment
     * 
     * loads a saved search that returns all the internal addresses of the company
     * if determined to be an internal address, return true else, return false.
     * 
     */
    function addressChecker(address) {
        var locationSearch = search.load({
            id: 'customsearch_acs_internal_locations'
        });
        var isInternal = false;
        locationSearch.run().each(function(result){
            var address1 = result.getValue({ name: 'address1' });
            if(address.indexOf(address1.toLowerCase()) !== -1){
                isInternal = true;
                return false;
            }
            return true;
         });

        return isInternal;
    }

    function afterSubmit(context) {
        log.debug("context.type", context.type);
        if(context.type == context.UserEventType.CREATE){
            var recId = context.newRecord.id;
            var recordObj = record.load({
                type: record.Type.ITEM_FULFILLMENT,
                id: recId,
                isDynamic: true
            });
            var address = recordObj.getText({ fieldId: 'shipaddress' });
            var createdFrom = recordObj.getValue({ fieldId: 'createdfrom' });
            if(address != "" && addressChecker(address.toLowerCase())){
                try {
                    recordObj.setValue({ fieldId: 'shipstatus', value: 'C' });
                    recordObj.save();
                    record.submitFields({
                        type: record.Type.SALES_ORDER,
                        id: createdFrom,
                        values: {
                            custbody_acs_auto_invoice: true
                        },
                        options: {
                            enableSourcing: false,
                            ignoreMandatoryFields : true
                        }
                    });
                } catch (e) {
                    log.debug("Error", e);
                }
            }
        }        
    }

    return {
        afterSubmit: afterSubmit
    }
});
