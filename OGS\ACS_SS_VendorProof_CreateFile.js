/**
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
 define(['N/file', 'N/runtime', 'N/task', 'N/search'], function (file, runtime, task, search) {

    function pad(pad, str, padLeft) {
        if (typeof str === 'undefined')
            return pad;
        if (padLeft) {
            return (pad + str).slice(-pad.length);
        } else {
            return (str + pad).substring(0, pad.length);
        }
    }

    function execute(context) {
        
        var scriptObj = runtime.getCurrentScript();

        // file cabinet queue folder id
        var queueFolderID = scriptObj.getParameter({ name: 'custscript_queue_folder_id' });

        // search id
        var searchId = scriptObj.getParameter({ name: 'custscript_search_id' });

        var date = new Date;
        var dateTime = pad("00", (date.getMonth() + 1), true) + pad("00", date.getDate(), true) + date.getFullYear() + '_' + pad("00", date.getHours(), true) + pad("00", date.getMinutes(), true);
        var fileName = "VendorProof_" + dateTime + ".csv";

        var fileObj = file.create({
            name: fileName,
            fileType: file.Type.CSV,
            folder: queueFolderID
        });
        var fileId = fileObj.save();

        var searchTask = task.create({
            taskType: task.TaskType.SEARCH
        });
        searchTask.savedSearchId = searchId;
        searchTask.fileId = fileId;
        var searchTaskId = searchTask.submit();

        log.audit('Search Task Running', 'Search Task Running - ID: ' + searchTaskId);

    }

    return {
        execute: execute
    }
});
