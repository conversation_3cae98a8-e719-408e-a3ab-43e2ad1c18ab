function suitelet(request, response)
{

    try
    {
        var form = nlapiCreateForm('New message');

        if(request.getMethod() == "GET")
        {
            var caseId = request.getParameter('caseid');

            if(!caseId)
                throw nlapiCreateError('NOT_FOUND', "Invalid parameters");


            form.addFieldGroup('custpage_groupmessage', 'Message');
            // case
            var caseField = form.addField("custpage_caseid", 'text', '', null, 'custpage_groupmessage');
            caseField.setDefaultValue(caseId);
            caseField.setDisplayType('hidden');


            //reply
            var replyField = form.addField('custpage_reply', 'richtext', 'Message', null, 'custpage_groupmessage');
            replyField.setMandatory(true);

            form.addSubmitButton('Send');
        }
        else
        {
            var supportCase = nlapiLoadRecord('supportcase', request.getParameter('custpage_caseid'));

            supportCase.setFieldValue('incomingmessage', request.getParameter('custpage_reply'));
            supportCase.setFieldValue('messagenew', "T");

            if(supportCase.getFieldValue('status') == nlapiGetContext().getSetting('SCRIPT', 'SUPPORT_DEFAULT_CLOSEDSTATUS') &&
                nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_csf_reopenonnewmessage') == "T")
                supportCase.setFieldValue('status', nlapiGetContext().getSetting('SCRIPT', 'SUPPORT_DEFAULT_REOPENEDSTATUS'));


            nlapiSubmitRecord(supportCase);

            var field = form.addField('custpage_message', 'inlinehtml', 'Message');
            field.setDefaultValue("<h2>Message sent!</h2>");

        }

        response.writePage(form);
    }
    catch(e)
    {
        nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e) + ' ' + e.stack);

        var form = nlapiCreateForm("Error");

        var code = "UNEXPECTED_ERROR";
        var message = "";

        if(e instanceof nlobjError)
        {
            code = e.getCode();
            message = e.getDetails();
        }
        else
        {
            message = e.message;
        }

        var field = form.addField('custpage_message', 'inlinehtml', 'Message');
        field.setDefaultValue("<h2>Code: " + code +" <br/>Message: " + message + "</h2>");

        response.writePage(form);
    }
}