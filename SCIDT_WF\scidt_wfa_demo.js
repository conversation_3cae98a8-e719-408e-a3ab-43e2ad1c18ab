/**
 * Module Description
 * 
 * Version    Date            Author           Remarks
 * 1.00       26 May 2020     jdgonzal
 *
 */

/**
 * @returns {Void} Any or no return value
 */
function workflowAction() {

    var userId = nlapiGetUser();
    var user = nlapiLoadRecord('employee', userId).getFieldValue('altname');
    var poObj = nlapiLoadRecord('purchaseorder', nlapiGetRecordId());

    var itemCount = poObj.getLineItemCount('item');

    for(var i = 0; i < itemCount; i++){
        var itemId = poObj.getLineItemValue('item', 'item', i+1);
        var itemObj = nlapiLoadRecord('inventoryitem', itemId);
        var log = "";
        if(itemObj.getFieldValue('custitem3')){
            log = itemObj.getFieldValue('custitem3');
        }
        log = log + "\nPurchased by " + user + " on " + nlapiDateToString(new Date(), 'datetime');
        itemObj.setFieldValue('custitem3', log);
        nlapiSubmitRecord(itemObj);
    }

}
