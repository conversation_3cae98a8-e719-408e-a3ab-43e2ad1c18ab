/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/https', './crypto/crypto-js.js', 'N/workflow', 'N/record', 'N/file', 'N/url', 'N/runtime', 'N/format'], 
        function(https, crypto, workflow, record, file, url, runtime, format) {

    /**
     * 
     * @param {string} pid - ID of the pass phrase file
     * 
     * loads the content the pass phrase file.
     * 
     */
    function getPassphrase(pid) {
        var pFile = file.load({
            id: pid
        })
        return pFile.getContents();
    }

    /**
     * 
     * @param {string} email - decrypted email of the approver
     * @param {object} billObj - object of the bill
     * 
     * confirms the identity of the approver.
     * current approver in vendor bill must be the same as the approver email attached to the URL of the received email
     * 
     */
    function confirmApprover(email, billObj) {
        
        var currentApprover = billObj.getValue({ fieldId: 'custbody_current_approver' });
        // var vendorId = billObj.getValue({ fieldId: 'entity' });
        // var vendorObj = record.load({
        //     type: record.Type.VENDOR,
        //     id: vendorId
        // });

        // var vendorApprover = vendorObj.getValue({ fieldId: 'custentity_vendor_approver' });

        // return ((vendorApprover == email) && (currentApprover == email));
        return currentApprover == email;
    }

    /**
     * 
     * @param {string} email - decrypted email of the approver
     * @param {object} billObj - object of the bill
     * 
     * different process for the confirmApprover process
     * this is just here for future use (not to be used now)
     * 
     */
    function confirmMaintenanceApprover(email, deptId) {

        var approvers = [];

        var deptObj = record.load({
            type: record.Type.DEPARTMENT,
            id: deptId
        });

        approvers.push(deptObj.getValue({ fieldId: 'custrecord_department_approver_1' }));
        approvers.push(deptObj.getValue({ fieldId: 'custrecord_department_approver_2' }));
        approvers.push(deptObj.getValue({ fieldId: 'custrecord_department_approver_3' }));

        for(var x = 0; x < approvers.length; x++) {
            if(email == approvers[x]){
                return true;
            }
        }

        return false;
        
    }

    /**
     * 
     * @param {int} billId - id of the bill
     * @param {string} actionId - id of the workflow action to be triggered
     * 
     * triggers the action based on the attached action id on URL of the received email
     * 
     */
    function triggerAction(billId, actionId) {

        workflow.trigger({
            recordType: record.Type.VENDOR_BILL,
            recordId: billId,
            workflowId: 'customworkflow_acs_vendor_bill_appr',
            actionId: actionId
        });
        return true;
    }

    /**
     * 
     * @param {object} errorObj - error object from a catch
     * @param {object} customError - custom error object
     * 
     */
    function renderError(context, errorObj, customError) {

        if(customError !== null)
            errorObj = customError;

        log.error('Error: ' + errorObj.name , errorObj.message);

        var htmlFile = file.load('SuiteScripts/ACS_Error_HTML.html')

        var html = htmlFile.getContents();

        html = html.replace("{{ERROR_NAME}}", errorObj.name);
        html = html.replace("{{ERROR_MESSAGE}}", errorObj.message);

        context.response.write(html);

    }

    function renderPage(context, billObj, action, type, email) {

        var htmlFile = file.load('SuiteScripts/ACS_APPROVAL_PROCESS.html')

        var html = htmlFile.getContents();

        var appProcessSuitelet = url.resolveScript({
            scriptId: "customscript_acs_sl_vendor_bill_approval",
            deploymentId: 'customdeploy_acs_sl_vendor_bill_approval',
            returnExternalUrl: true
        });

        var tranNo = billObj.getValue({ fieldId: "transactionnumber"});

        html = html.replace("{{SUITELET_URL}}", appProcessSuitelet);
        html = html.replace("{{VENDOR_BILL_ID}}", billObj.id);
        html = html.replace("{{TRANSACTION_NO}}", tranNo);
        html = html.replace("{{ACTION_ID}}", action);
        html = html.replace("{{EMAIL}}", email);
        html = html.replace("{{ACTION_TYPE}}", type);
        html = html.replace("{{ACTION_TYPE}}", type);
        html = html.replace("{{ACTION_TYPE}}", type);

        context.response.write(html);
    }

    function renderSuccess(context, billObj, status) {

        var htmlFile = file.load('SuiteScripts/ACS_Success_HTML.html')

        var html = htmlFile.getContents();

        var tranNo = billObj.getValue({ fieldId: "transactionnumber"});

        html = html.replace("{{TRANSACTION_NO}}", tranNo);
        html = html.replace("{{STATUS}}", status);
        html = html.replace("{{STATUS}}", status);

        context.response.write(html);

    }

    function onRequest(context) {

        if(context.request.method == https.Method.GET){

            var pid = decodeURIComponent(context.request.parameters.pi); // pid

            var PP = getPassphrase(pid);

            var encEmail = decodeURIComponent(context.request.parameters.em); // encoded email
            var encRec = decodeURIComponent(context.request.parameters.re); // encoded record id
            var encApprovalType = decodeURIComponent(context.request.parameters.apt); // encoded approval type
            var encAction = decodeURIComponent(context.request.parameters.ac); // encoded workflow action

            var email = crypto.AES.decrypt(encEmail, PP).toString(crypto.enc.Utf8);
            var recId = crypto.AES.decrypt(encRec, PP).toString(crypto.enc.Utf8);
            var approvalType = crypto.AES.decrypt(encApprovalType, PP).toString(crypto.enc.Utf8).toUpperCase();
            var action = crypto.AES.decrypt(encAction, PP).toString(crypto.enc.Utf8);

            var billObj = record.load({
                type: record.Type.VENDOR_BILL,
                id: recId
            });
            var appStatus = billObj.getValue({ fieldId: 'approvalstatus' });
            var deptId = billObj.getValue({ fieldId: 'department' });

            var approverConfirmed = false;
            approverConfirmed = confirmMaintenanceApprover(email, deptId);

            if(!approverConfirmed){
                approverConfirmed = confirmApprover(email, billObj);
            }
            
            if(appStatus == 1){
                if(approverConfirmed) {

                    if(approvalType == 'APPROVE' || approvalType == 'REJECT') {

                        try {
                            renderPage(context, billObj, action, approvalType, email);
                        } catch (err) {
                            renderError(context, err, null);
                        }

                    } else { 

                        renderError(context, null, {
                            name: "Wrong approval type",
                            message: "Wrong approval type. Please contact your administrator!"
                        });
                        
                    }

                } else {

                    renderError(context, null, {
                        name: "Wrong approver!",
                        message: "You are not the approver of this vendor bill."
                    });
                    
                } // else of if(confirmApprover(email, billObj)) {
            } else if (appStatus == 2) {
                renderError(context, null, {
                    name: "Vendor bill already approved",
                    message: "This vendor bill has already been approved."
                });
            } else {
                renderError(context, null, {
                    name: "Vendor bill already rejected",
                    message: "This vendor bill has already been rejected."
                });
            }

        } else {
            
            var requestParams = context.request.parameters;
            var recId = requestParams.vendor_bill_id;
            var actionId = requestParams.action_id;
            var actionType = requestParams.action_type;
            var email = requestParams.email;
            var note = requestParams.note;

            try {

                var billObj = record.load({
                    type: record.Type.VENDOR_BILL,
                    id: recId
                });

                var approvalProcessLogs = billObj.getValue({ fieldId: 'custbody_approval_process_logs' });

                var datetime = format.format({
                    value: new Date(),
                    type: format.Type.DATETIME
                });

                approvalProcessLogs += '\r\n (' + actionType + ' @ ' + datetime + ' by ' + email + ') : ' + note;

                billObj.setValue({ fieldId: 'custbody_approval_process_logs', value: approvalProcessLogs });
                billObj.save();
                
                triggerAction(billObj.id, actionId);
                renderSuccess(context, billObj, ((actionType == 'APPROVE') ? 'approved' : 'rejected'));

            } catch (err) {
                renderError(context, err, null);
            }
        }
    }

    return {
        onRequest: onRequest
    }
});
