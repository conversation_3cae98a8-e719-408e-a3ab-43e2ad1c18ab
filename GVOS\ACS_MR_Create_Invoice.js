/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/format', 'N/workflow'], function(search, record, format, workflow) {

    // function getCustomerDeposit(salesOrderID) {

    //     var arrFilters = [
    //         search.createFilter({name : 'salesorder', operator : search.Operator.ANYOF, values : salesOrderID}),
    //     ];
        
    //     var arrColumns = [
    //         search.createColumn({name: "internalid", label: "Internal ID"})
    //     ]

    //     var depositSearch = search.create({
    //         type: search.Type.TRANSACTION,
    //         filters: arrFilters,
    //         columns: arrColumns
    //     });

    //     var depositSearchResultObj = depositSearch.run();
    //     var depositIds = [];
    //     depositSearchResultObj.each(function(result){
    //         depositIds.push(result.getValue({ name: 'internalid' }));
    //         return true;
    //     });

    //     return ((depositIds.length > 0) ? depositIds : false );
    // }

    function lockSalesOrder(soId){

        try {
            workflow.trigger({
                recordType: record.Type.SALES_ORDER,
                recordId: soId,
                workflowId: 'customworkflow_acs_auto_close_so',
                actionId: 'workflowaction_acs_lock_action'
            });
        } catch (e) {
            log.debug('lockSalesOrder - Error', e);
        }

    }

    function invoiceViaWorkflow(soId){
        try {
            workflow.trigger({
                recordType: record.Type.SALES_ORDER,
                recordId: soId,
                workflowId: 'customworkflow_acs_auto_close_so',
                actionId: 'workflowaction_acs_inv_action'
            });
        } catch (e) {
            log.debug('invoiceViaWorkflow - Error', e);
        }
    }

    function getInputData() {
        var itemFulfillmentSearch = search.load({
            id: 'customsearch_acs_item_fulfill_created__2'
        });
        return itemFulfillmentSearch;
    }

    function map(context) {
        var keyFromInput = context.key;
        var valueFromInput = context.value;
        
        var salesOrder = JSON.parse(valueFromInput);
        log.debug('map', salesOrder);
        var salesOrderID = JSON.parse(salesOrder.values.internalid.value);

        try {

            var invObject = record.transform({
                fromType: record.Type.SALES_ORDER,
                fromId: salesOrderID,
                toType: record.Type.INVOICE
            });

            // REMOVE THIS BEFORE DEPLOYING TO PROD
            // invObject.setValue({ fieldId: 'trandate', value: new Date('12/15/2020') });

            var invId = invObject.save();
            log.debug("Logs", "Successfully created invoice. ID: " + invId);

            lockSalesOrder(salesOrderID);
            
        } catch (e) {
            log.debug('test', {
                error_name: e.name,
                soId: salesOrderID
            });
            if(e.name == 'UNEXPECTED_ERROR') {
                // this is a workaround for the unexpected error. currently cannot locate the exact bug.
                invoiceViaWorkflow(salesOrderID);
                log.debug("Logs", "Workaround triggered!");

            } else {
                log.debug('Map - Error', e);
            }
        }

    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        map: map,
        summarize: summarize
    }
});
