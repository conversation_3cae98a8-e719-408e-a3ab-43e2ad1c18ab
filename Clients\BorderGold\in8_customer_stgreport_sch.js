/**
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
define(['N/search', './In8_StorageReportHelper'],
    function (search, stgReportHelper) {

        function execute(context) {
            try {
                var filters = [
                    search.createFilter({
                        name: 'custentity_default_storage_location',
                        operator: search.Operator.NONEOF,
                        values: '@NONE@'
                    }),
                    search.createFilter({
                        name: 'custentity_monthlystoragereport',
                        operator: search.Operator.IS,
                        values: true
                    }),
                    search.createFilter({
                        name: 'email',
                        operator: search.Operator.ISNOTEMPTY,
                        values: null
                    }),
                ];

                search.create({
                    type: search.Type.CUSTOMER,
                    filters: filters,
                }).run().each(function (result) {
                    stgReportHelper.sendReportEmail(result.id)
                    return true;
                });
            } catch (e) {
                log.debug({
                    title: 'Error in Storage Report SCH',
                    details: e.message
                });
            }
        }

        return {
            execute: execute
        }
    });