/**
 * In8 - Item Totals - User Event
 * 
 * Version    Date            Author           Remarks
 * 1.00       19 Sep 2016     <PERSON> (In8)   Initial Version
 *
 */

/**
 * @appliedtorecord Sales Order
 *   
 * @param {String} type Operation types: create, edit, view, copy, print, email
 * @param {nlobjForm} form Current form
 * @param {nlobjRequest} request Request object
 * @returns {Void}
 */
function beforeLoad(type, form, request) {
 
	try {
		if (type == 'view' || type == 'edit') {
			getUnexpiredEstimates();
			
			getLastSaleDate();	
		}				
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
	}
}

/**
 * Get Unexpired Estimates
 * 
 * @returns {Void}
 */
function getUnexpiredEstimates() {

	var filters = [],
	    columns = [],
	    searchResults,
	    numberOfEstimates = 0,
	    i = 0;
	
	filters[i++] = new nlobjSearchFilter('item', null, 'is', nlapiGetRecordId());
	filters[i++] = new nlobjSearchFilter('expectedclosedate', null, 'onorafter', 'today');
	
	i = 0;
	
	columns[i++] = new nlobjSearchColumn('internalid', null, 'group');
	columns[i++] = new nlobjSearchColumn('item', null, 'group');
	
	searchResults = nlapiSearchRecord('estimate', null, filters, columns);
	
	if (searchResults && searchResults.length) numberOfEstimates = searchResults.length
	
	// Set the number of unexpired estimates
	nlapiSetFieldValue('custitem_in8_unexpired_estimates', numberOfEstimates);
}

/**
 * Get Last Sale Date
 * 
 * @returns {Void}
 */
function getLastSaleDate() {
	
	var filters = [],
	    columns = [],
	    searchResults,
	    lastSaleDate = '',
	    i = 0;
	
	filters[i++] = new nlobjSearchFilter('item', null, 'is', nlapiGetRecordId());
	
	i = 0;
	
	var column = new nlobjSearchColumn('trandate');
	column.setSort(true);
	
	columns[i++] = column;
	
	searchResults = nlapiSearchRecord('salesorder', null, filters, columns);
	
	if (searchResults && searchResults.length) {
		lastSaleDate = searchResults[0].getValue('trandate');
	}
	
	// Set the last sale date
	nlapiSetFieldValue('custitem_in8_last_sale_date', lastSaleDate);
}
