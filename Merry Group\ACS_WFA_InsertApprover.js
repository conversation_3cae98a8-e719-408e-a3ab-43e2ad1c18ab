/**
 *@NApiVersion 2.x
 *@NScriptType WorkflowActionScript
 */
define(['N/record', 'N/runtime', 'N/format', 'N/email'], function(record, runtime, format, email) {

    function onAction(scriptContext) {
        try {

            var contextRecord = scriptContext.newRecord;
            var approver = runtime.getCurrentScript().getParameter('custscript_approver_name');
            var datetime = format.format({
                value: new Date(),
                type: format.Type.DATE
            });

            var approvalLogs = contextRecord.getValue({ fieldId: 'custbody_approval_log' });
            approvalLogs += 'Approved by ' + approver + '(' + datetime + ')\r\n';

            var recObj = record.load({
                type: record.Type.ESTIMATE,
                id: contextRecord.id
            });

            recObj.setFieldValue({
                fieldId: 'custbody_approval_log',
                value: approvalLogs
            });

            var recId = recObj.save();

            log.debug('Success', 'Successfully updated approval log (id: '+ recId +')');

        } catch (e) {

            log.debug('Error', e);

        }
    }

    return {
        onAction: onAction
    }
});
