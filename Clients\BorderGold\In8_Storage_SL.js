/**
 * Storage Brinks
 *
 * Version    Date            Author           Remarks
 * 1.00       27 Jan 2020     <PERSON>   Initial Version
 *
 */

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
 function suitelet(request, response) {

    if (request.getMethod() == 'GET') {
        app.handleGet();
    } else {
        app.handlePost();
    }
}

var app = (function () {

    var FORM_NAME = 'Storage Brinks-Billed Accounts', // Form Name
        LIST_NAME = 'List', // List Name
        MAX_RECORDS = null, // Maximum number of records to display on the sublist
        MARKALL_ENABLED = false; // Mark all option enabled

    /**
     * Handles Suitelet GET Method
     *
     * @returns {Void}
     */
    var handleGet = function () {

        var form,
            subList;

        try {
            form = nlapiCreateForm(FORM_NAME, false);

            form.setScript('customscript_in8_storage_cs');

            var params = nlapiLoadRecord('customrecord_in8_storage_parameters', 1);

            if (request.getParameter('search') == 'T') {
                // Update parameters
                updateParameters(params);
            }

            addFilters(form, params);

            addButtons(form);

            // Creates and inserts the sublist on the form
            subList = getSubList(form);

            // Populates the sublist based on the search results
            populateSubList(subList, params);

            // Displays the page
            response.writePage(form);
        } catch (e) {
            displayMessage('An error occurred. ' + (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
        }
    };

    /**
     * Handles Suitelet POST method
     *
     * @returns {Void}
     */
    var handlePost = function () {

        var invoices = [];

        for (var i = 1; i <= request.getLineItemCount('custpage_sublist'); i++) {

            if (request.getLineItemValue('custpage_sublist', 'custpage_selected', i) == 'T') {

                var entityId = request.getLineItemValue('custpage_sublist', 'custpage_internalid', i);
                var amountUSD = request.getLineItemValue('custpage_sublist', 'custpage_usd', i);
                var amountCAD = request.getLineItemValue('custpage_sublist', 'custpage_cad', i);

                //var currency = nlapiLookupField('customer', entityId, 'currency');

                var record = nlapiCreateRecord('customrecord_in8_storage');

                record.setFieldValue('custrecord_in8_storage_customer', entityId);
                record.setFieldValue('custrecord_in8_storage_amount', amountUSD);
                record.setFieldValue('custrecord_in8_storage_amount_cad', amountCAD);
                record.setFieldValue('custrecord_in8_storage_status', 1);
                record.setFieldValue('custrecord_in8_storage_ref_date', request.getParameter('custpage_start_date'));

                var date_time_value = nlapiDateToString(new Date(), 'datetimetz');

                record.setFieldValue('custrecord_in8_storage_date', date_time_value, false);

                nlapiSubmitRecord(record, true, true);
            }
        }

        var myParams = [];
        nlapiScheduleScript('customscript_in8_storage_ss', 'customdeploy_in8_storage_ss', myParams);

        var url = '/app/common/custom/custrecordentrylist.nl?rectype=' + getRecordId('customrecord_in8_storage');

        response.write('<script>location.href="' + url + '"</script>');
    };

    /**
     * Add Buttons
     *
     * @param {nlobjForm} form Object containing the form

     * @returns {Void}
     */
    var addButtons = function (form) {

        form.addSubmitButton('Generate Invoices');

        form.addButton('custom_recalculate', 'Search', 'search()');
    };

    /**
     * Add Filter fields to the Form
     *
     * @param {nlobjForm} form Object containing the form

     * @returns {Void}
     */
    var addFilters = function (form, params) {

        form.addFieldGroup('custpage_filters', 'Filters');

        var fld = form.addField('custpage_start_date', 'date', 'Start Date', null, 'custpage_filters');
        fld.setMandatory(true);
        fld.setDefaultValue(params.getFieldValue('custrecord_in8_storage_start_date'));

        var fld = form.addField('custpage_end_date', 'date', 'End Date', null, 'custpage_filters');
        fld.setMandatory(true);
        fld.setDefaultValue(params.getFieldValue('custrecord_in8_storage_end_date'));

        form.addFieldGroup('custpage_params', 'Parameters');

        var fld = form.addField('custpage_au', 'float', 'Au', null, 'custpage_params');
        fld.setMandatory(true);
        fld.setDefaultValue(params.getFieldValue('custrecord_in8_storage_au'));

        fld = form.addField('custpage_ag', 'float', 'Ag', null, 'custpage_params');
        fld.setMandatory(true);
        fld.setDefaultValue(params.getFieldValue('custrecord_in8_storage_ag'));

        fld = form.addField('custpage_pt', 'float', 'Pt', null, 'custpage_params');
        fld.setMandatory(true);
        fld.setDefaultValue(params.getFieldValue('custrecord_in8_storage_pt'));

        fld = form.addField('custpage_pd', 'float', 'Pd', null, 'custpage_params');
        fld.setMandatory(true);
        fld.setDefaultValue(params.getFieldValue('custrecord_in8_storage_pd'));

        fld = form.addField('custpage_cad', 'float', 'CAD', null, 'custpage_params');
        fld.setMandatory(true);

        fld.setDefaultValue(params.getFieldValue('custrecord_in8_storage_cad'));
    };

    /**
     * Add a sublist to the Form
     *
     * @param {nlobjForm} form Object containing the form

     * @returns {Void}
     */
    var getSubList = function (form) {

        var subList = form.addSubList('custpage_sublist', 'list', LIST_NAME, 'general');

        //if (MARKALL_ENABLED)
        subList.addField('custpage_selected', 'checkbox', 'Selected');

        fld = subList.addField('custpage_internalid', 'text', 'InternalId');
        fld.setDisplayType('hidden');
        subList.addField('custpage_entityid', 'text', 'Entity');
        subList.addField('custpage_trandate', 'text', 'Date');
        subList.addField('custpage_item', 'text', 'Item');
        subList.addField('custpage_quantity', 'float', 'Quantity');
        subList.addField('custpage_rate', 'float', 'Rate');
        subList.addField('custpage_fee', 'text', 'Initial Fee');
        subList.addField('custpage_add_fee', 'text', 'Additional Fee');
        subList.addField('custpage_amount', 'text', 'Amount');
        fld = subList.addField('custpage_usd', 'text', 'USD');
        fld.setDisplayType('entry');
        fld = subList.addField('custpage_cad', 'text', 'CAD');
        fld.setDisplayType('entry');

        // Add an option to mark all items
        if (MARKALL_ENABLED) subList.addMarkAllButtons();

        return subList;
    };

    /**
     * Populate the SubList
     *
     * @param {nlobjSublist} list Object sublist
     * @param {nlobjSearchResults} searchResults Object search results
     *
     * @returns {Void}
     */
    var populateSubList = function (list, params) {

        var customers = [];
        var i = 0,
            line = 1;

        var CAD = params.getFieldValue('custrecord_in8_storage_cad');
        var accumulated = 0;

        // List of customers
        var customersSearch = nlapiSearchRecord("customer", null,
            ["custentity_bill_storage", "is", "T"],
            [
                new nlobjSearchColumn("internalid"),
                new nlobjSearchColumn("entityid"),
                new nlobjSearchColumn("altname"),
                new nlobjSearchColumn("custentity_negostorage_rate")
            ]
        ) || [];

        for (var i = 0; i < customersSearch.length; i++) {
            var internalId = customersSearch[i].getValue('internalid');

            customers[internalId] = {};
            customers[internalId].internalId = customersSearch[i].getValue('internalid');
            customers[internalId].name = customersSearch[i].getValue('entityid') + ' ' + customersSearch[i].getValue('altname');
            customers[internalId].negotiatedRate = parseFloat(customersSearch[i].getValue('custentity_negostorage_rate'));
        }

        //nlapiLogExecution('DEBUG', 'customers', JSON.stringify(customers));

        // Get As Of report
        var transactionSearch = nlapiSearchRecord("transaction", null,
            [
                ["trandate", "before", params.getFieldValue('custrecord_in8_storage_start_date')],
                "AND",
                ["type", "anyof", ["InvAdjst"]],
                "AND",
                ["posting", "is", "T"],
                "AND",
                ["accounttype", "anyof", "OthCurrAsset"],
                "AND",
                ["location", "anyof", ["27","35","32","34","25"]],
                "AND",
                ["customer.custentity_bill_storage", "is", "T"]
            ],
            [
                new nlobjSearchColumn("internalid", "customer", "GROUP"),
                new nlobjSearchColumn("item", null, "GROUP"),
                new nlobjSearchColumn("quantity", null, "SUM"),
                new nlobjSearchColumn("unitabbreviation", null, "GROUP")
            ]
        ) || [];

        for (var i = 0; i < transactionSearch.length; i++) {
            var internalId = transactionSearch[i].getValue("internalid", "customer", "GROUP");

            if (customers[internalId]) {
                if (!customers[internalId].items) {
                    customers[internalId].items = [];
                }
                var item = {};
                item.item = transactionSearch[i].getValue("item", null, "GROUP");
                item.itemText = transactionSearch[i].getText("item", null, "GROUP");
                item.quantity = transactionSearch[i].getValue("quantity", null, "SUM");

                item.calc = calculateFee(params, item.itemText, item.quantity, customers[internalId].negotiatedRate);

                customers[internalId].items.push(item);
            }
        }

        // Get the inventory adjustments for the period
        var customerSearch = nlapiSearchRecord("customer", null,
            [
                ["transaction.type", "anyof", ["InvAdjst"]],
                "AND",
                ["transaction.mainline", "is", "F"],
                "AND",
                ["custentity_negostorage_rate", "isnotempty", ""],
                "AND",
                ["custentity_bill_storage", "is", "T"],
                "AND",
                ["formulanumeric: case WHEN {transaction.item} LIKE 'Au%' OR {transaction.item} LIKE 'Ag%' OR {transaction.item} LIKE 'Pt%' OR {transaction.item} LIKE 'Pd%' THEN 1 ELSE 0 END", "equalto", "1"],
                "AND",
                ["transaction.trandate", "onorafter", params.getFieldValue('custrecord_in8_storage_start_date')],
                "AND",
                ["transaction.trandate", "onorbefore", params.getFieldValue('custrecord_in8_storage_end_date')],
                //["transaction.location","anyof","21"]
            ],
            [
                new nlobjSearchColumn("internalid", null, "GROUP"),
                new nlobjSearchColumn("entityid", null, "GROUP").setSort(false),
                new nlobjSearchColumn("custentity_negostorage_rate", null, "GROUP"),
                new nlobjSearchColumn("altname", null, "GROUP"),
                new nlobjSearchColumn("quantity", "transaction", "SUM"),
                new nlobjSearchColumn("type", "transaction", "GROUP"),
                new nlobjSearchColumn("item", "transaction", "GROUP"),
                new nlobjSearchColumn("trandate", "transaction", "GROUP")
            ]
        ) || [];

        for (var i = 0; i < customerSearch.length; i++) {
            var internalId = customerSearch[i].getValue("internalid", null, "GROUP");

            var customer = customers[internalId];

            var item = customerSearch[i].getValue("item", "transaction", "GROUP");

            var calc = calculateFee(params, customerSearch[i].getText("item", "transaction", "GROUP"),
                customerSearch[i].getValue("quantity", "transaction", "SUM"),
                customer.negotiatedRate);

            var tranDate = customerSearch[i].getValue('trandate', 'transaction', 'GROUP');

            // Date difference between transacton date and End Date
            var dtDiff = datediff(nlapiStringToDate(tranDate), nlapiStringToDate(params.getFieldValue('custrecord_in8_storage_end_date')));

            // Date difference between the filter dates
            var dtFilterDiff = datediff(nlapiStringToDate(params.getFieldValue('custrecord_in8_storage_start_date')), nlapiStringToDate(params.getFieldValue('custrecord_in8_storage_end_date')));

            var d = dtFilterDiff == 0 ? 0 : dtDiff / dtFilterDiff;

            var addFee = calc ? calc * d : 0;

            var found = false;

            if (!found) {
                var newItem = {};
                newItem.item = item;
                newItem.tranDate = customerSearch[i].getValue('trandate', 'transaction', 'GROUP');
                newItem.itemText = customerSearch[i].getText("item", "transaction", "GROUP");
                newItem.quantity = customerSearch[i].getValue("quantity", "transaction", "SUM");
                newItem.addFee = addFee;
                if (!customer.items) {
                    customer.items = [];
                }
                customer.items.push(newItem);
            }
        }

        var line = 1;

        for (var c in customers) {

            var customer = customers[c];

            list.setLineItemValue('custpage_internalid', line, customer.internalId);
            list.setLineItemValue('custpage_entityid', line, customer.name);

            var accumulated = 0;

            if (customer.items) {
                for (var i = 0; i < customer.items.length; i++) {
                    var item = customer.items[i];
                    if (item.calc) {
                        accumulated += Number(item.calc);
                    }
                    if (item.addFee) {
                        accumulated += Number(item.addFee);
                    }
                }
            }
            list.setLineItemValue('custpage_amount', line, nlapiFormatCurrency(accumulated));
            list.setLineItemValue('custpage_usd', line, nlapiFormatCurrency(accumulated < 45 ? 45 : accumulated));
            list.setLineItemValue('custpage_cad', line, nlapiFormatCurrency(accumulated * CAD < 45 ? 45 : accumulated * CAD));

            line++;

            if (customer.items) {
                for (var i = 0; i < customer.items.length; i++) {
                    var item = customer.items[i];

                    //if (Number(item.quantity) > 0) {
                        list.setLineItemValue('custpage_item', line, item.itemText);

                        if (item.tranDate) {
                            list.setLineItemValue('custpage_trandate', line, item.tranDate);
                        }
                        list.setLineItemValue('custpage_quantity', line, item.quantity);
                        list.setLineItemValue('custpage_rate', line, customer.negotiatedRate);
                        list.setLineItemValue('custpage_fee', line, item.calc ? item.calc.toFixed(4) : '');
                        list.setLineItemValue('custpage_add_fee', line, item.addFee ? item.addFee.toFixed(4) : '');
                        line++;
                    //}
                }
            }
        }
    };

    function calculateFee(params, itemText, quantity, rate) {

        var calc = null;

        switch (itemText.substr(0, 2)) {
            case 'Au':
                var au = params.getFieldValue('custrecord_in8_storage_au');
                calc = au * quantity * rate / 4;
                break;
            case 'Pt':
                var pt = params.getFieldValue('custrecord_in8_storage_pt');
                calc = pt * quantity * rate / 4;
                break;
            case 'Pd':
                var pd = params.getFieldValue('custrecord_in8_storage_pd');
                calc = pd * quantity * rate / 4;
                break;
            case 'Ag':
                var ag = params.getFieldValue('custrecord_in8_storage_ag');
                calc = ag * quantity * rate / 4;
                break;
        }
        return calc;
    }

    function updateParameters(params) {

        if (request.getParameter('custpage_au')) {
            params.setFieldValue('custrecord_in8_storage_au', request.getParameter('custpage_au'));
        }
        if (request.getParameter('custpage_ag')) {
            params.setFieldValue('custrecord_in8_storage_ag', request.getParameter('custpage_ag'));
        }
        if (request.getParameter('custpage_pt')) {
            params.setFieldValue('custrecord_in8_storage_pt', request.getParameter('custpage_pt'));
        }
        if (request.getParameter('custpage_pd')) {
            params.setFieldValue('custrecord_in8_storage_pd', request.getParameter('custpage_pd'));
        }
        if (request.getParameter('custpage_cad')) {
            params.setFieldValue('custrecord_in8_storage_cad', request.getParameter('custpage_cad'));
        }
        if (request.getParameter('custpage_start_date')) {
            params.setFieldValue('custrecord_in8_storage_start_date', request.getParameter('custpage_start_date'));
        }
        if (request.getParameter('custpage_end_date')) {
            params.setFieldValue('custrecord_in8_storage_end_date', request.getParameter('custpage_end_date'));
        }

        nlapiSubmitRecord(params, true, true);
    }

    function datediff(first, second) {
        return Math.round((second - first) / (1000 * 60 * 60 * 24));
    }

    function getRecordId(scriptId) {

        var filters = [],
            columns = [],
            searchResults,
            i = 0;

        filters[i++] = new nlobjSearchFilter('scriptid', null, 'is', scriptId); // 'customrecord_in8_vend_mappings_type');

        i = 0;

        columns[i++] = new nlobjSearchColumn('internalid');
        columns[i++] = new nlobjSearchColumn('name');
        columns[i++] = new nlobjSearchColumn('scriptid');

        searchResults = nlapiSearchRecord('customrecordtype', null, filters, columns);

        if (searchResults && searchResults.length) {
            return searchResults[0].getValue('internalid');
        }
        return null;
    }

    /**
     * Displays a message
     *
     * @param {String} message Message
     * @returns {Void}
     */
    var displayMessage = function (message) {

        // Create a NetSuite form
        var form = nlapiCreateForm(FORM_NAME, false),
            html = message;

        // Add a new HTML field to display the HTML contents
        field = form.addField('file', 'inlinehtml', 'label');
        field.setLayoutType('outsidebelow');
        field.setDefaultValue('<font size="2pt">' + html + '</font>');

        form.addButton('custombutton_back', 'Back', 'window.history.back()');

        response.writePage(form);
    };

    return {
        handleGet: handleGet,
        handlePost: handlePost
    };
})();