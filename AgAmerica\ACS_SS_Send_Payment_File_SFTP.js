/**
 *@NApiVersion 2.x
 *@NScriptType ScheduledScript
 */
 define(['N/file', "N/sftp", 'N/runtime', 'N/search'], function (file, sftp, runtime, search) {

            
    var scriptObj = runtime.getCurrentScript();

    // get SFTP Credentials here using script parameters
    var sftpHostKey = scriptObj.getParameter({ name: 'custscript_sftp_host_key' });
    var sftpUsername = scriptObj.getParameter({ name: 'custscript_sftp_username' });
    var sftpPassword = scriptObj.getParameter({ name: 'custscript_sftp_password' });
    var sftpPort = scriptObj.getParameter({ name: 'custscript_sftp_port' });
    var sftpKeyId = scriptObj.getParameter({ name: 'custscript_sftp_key_id' });
    var sftpServerUrl = scriptObj.getParameter({ name: 'custscript_sftp_server_url' });
    var sftpDirectory = scriptObj.getParameter({ name: 'custscript_sftp_directory' });

    // saved search
    var queuedSearchID = scriptObj.getParameter({ name: 'custscript_sftp_queued_files_search_id' });
    var archiveFolderID = scriptObj.getParameter({ name: 'custscript_archive_folder_id' });
    var manualFileID = scriptObj.getParameter({ name: 'custscript_manual_file_id' });

    function fileTransmissionProper(fileId) {

        try {

            log.audit("Start SFTP Server connection", "-------- Initialize SFTP Server Connection --------");

            // connect to sftp server using script parameters
            var sftpConnection = sftp.createConnection({
                username: sftpUsername,
                keyId: sftpKeyId,
                passwordGuid: sftpPassword,
                url: sftpServerUrl,
                port: Number(sftpPort),
                hostKey: sftpHostKey,
                hostKeyType: 'rsa'
            });
            log.audit("Connected successfully to SFTP Server", "------- Connected Successfully ------");
            
            log.audit("Start SFTP Transmission", "-------- start SFTP transmission --------");

            var fileToBeSentObj = file.load({
                id: fileId
            });

            // send file to server
            sftpConnection.upload({
                file: fileToBeSentObj,
                directory: sftpDirectory,
                replaceExisting: true
            });
            log.audit("Successful File Transmission", "-------- Successfully transmitted file. File ID: " + fileId + " --------");

            log.audit("Archiving file..", "-------- Archiving file. File ID: " + fileId + " ------");

            fileToBeSentObj.folder = archiveFolderID;
            var fileId = fileToBeSentObj.save();
            
            log.audit("Successfully moved file..", "-------- File Archived. File ID: " + fileId + " ------");
            
        } catch (e) {
            log.error("SFTP Server Connection Error", e)
        }

    }

    function testConnection() {
        
        log.audit("Start SFTP Server connection", "-------- Initialize SFTP Server Connection --------");

        // connect to sftp server using script parameters
        var sftpConnection = sftp.createConnection({
            username: sftpUsername,
            keyId: sftpKeyId,
            passwordGuid: sftpPassword,
            url: sftpServerUrl,
            port: Number(sftpPort),
            hostKey: sftpHostKey,
            hostKeyType: 'rsa'
        });
        log.audit("Connected successfully to SFTP Server", "------- Connected Successfully ------");
    }

    function execute(context) {

        // for connection debugging purposes
        // testConnection();

        // this part is when the user checks the "Send After File Creation" parameter on the Create PP File script
        if(manualFileID) {

            fileTransmissionProper(manualFileID);

        // if the script is not triggered/called manually by the Create PP File Script, script should check SFTP Queued Files folder for files to send
        } else if(queuedSearchID) {

            var queueSearch = search.load({
                id: queuedSearchID
            });

            queueSearch.run().each(function(result){

                var fileId = result.getValue({ name: 'internalid' });
                fileTransmissionProper(fileId);
              	return true;

            });

        } else {

            log.error('Parameter missing!', 'No File ID or Search Queue ID found');

        }

    }

    return {
        execute: execute
    }
});
