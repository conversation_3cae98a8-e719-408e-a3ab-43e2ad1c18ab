/**
 *@NApiVersion 2.x
 *@NScriptType WorkflowActionScript
 */
define(['N/record', 'N/search', 'N/format'], function (record, search, format) {


    function onAction(scriptContext) {

        var contractId = scriptContext.newRecord.getValue({
            fieldId: 'custrecord_ci_contract_id'
        });

        var customrecord_contract_itemSearchObj = search.create({
            type: "customrecord_contract_item",
            filters: [
                ["custrecord_ci_contract_id", "anyof", contractId]
            ],
            columns: [
                search.createColumn({
                    name: "internalid",
                    label: "Internal ID"
                })
            ]
        });
        var searchResultCount = customrecord_contract_itemSearchObj.runPaged().count;

        if(searchResultCount <= 1) {
            var recObj = record.load({
                type: 'customrecord_contracts',
                id: contractId,
                isDynamic: true
            });

            var origContractId = recObj.getValue({
                fieldId: 'custrecord_swe_original_contract'
            });

            if (origContractId) {
                var searchObj = search.load({
                    id: 'customsearch_acs_contract_search'
                });

                var contractIdFilter = search.createFilter({
                    name: 'custrecord_swe_original_contract',
                    operator: search.Operator.ANYOF,
                    values: [origContractId]
                });
                searchObj.filters.push(contractIdFilter);

                var resultSet = searchObj.run();

                searchResult = resultSet.getRange({
                    start: 1,
                    end: 2
                });
                searchResult.forEach(function (row) {
                    row.columns.forEach(function (column) {
                        if (column.name == 'internalid') {
                            log.audit('Original Contract ID', row.getValue(column));
                            return;
                        }
                        log.debug('Test', {
                            name: column.name,
                            value: row.getValue(column)
                        });
                        // if (column.name == 'custrecord_imc') {
                        //     var imc = ((row.getValue(column)) ? 'T' : 'F');
                        //     recObj.setValue({
                        //         fieldId: column.name,
                        //         value: imc
                        //     });
                        // } else {
                        recObj.setValue({
                            fieldId: column.name,
                            value: row.getValue(column)
                        });
                        // }
                    });
                });
                try {
                    recObj.save();
                } catch (e) {
                    log.debug("Error", e);
                }
            }
        }
    }
    return {
        onAction: onAction
    }
});
