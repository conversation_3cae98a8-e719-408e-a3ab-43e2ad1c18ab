/**
* 
*
* Version		Date			Author				Remarks
* 1.0			01 Mar 2018		<PERSON> (In8Sync)	Initial Version
*
*/

function pageInit(type){
 
	try {        
        if (type == 'edit') {
            var customForm = nlapiGetContext().getCompany() == '789815_SB1' ? 121 : 108;
            
            if (nlapiGetFieldValue('customform') == customForm) {

                var itemsGroups = getItemsGroups(nlapiGetRecordId());
                
                for (var i = 1; i <= nlapiGetLineItemCount('itemcost'); i++) {
                    var group = itemsGroups[nlapiGetLineItemValue('itemcost', 'doc', i)];
                    if (group) {
                        nlapiSetLineItemValue('itemcost', 'custcol_in8_group', i, group);
                    }
                }
                for (var i = 1; i <= nlapiGetLineItemCount('expcost'); i++) {
                    var group = itemsGroups[nlapiGetLineItemValue('expcost', 'doc', i)];
                    if (group) {
                        nlapiSetLineItemValue('expcost', 'custcol_in8_group', i, group);
                    }
                }
            }
         }
	} catch(e) {
		console.log('Error: ' + (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
	}	
}

function getItemsGroups(internalId) {

    var s = nlapiSearchRecord("transaction", null,
        [
            ["internalidnumber", "equalto", internalId],
            "AND",
            ["mainline", "is", "F"]
        ],
        [
            new nlobjSearchColumn("item"),
            new nlobjSearchColumn("custcol_in8_group"),
            new nlobjSearchColumn("appliedtotransaction"),
        ]
    ) || [];

    var itemsGroup = [];

    for (var i = 0; i < s.length; i++) {
        itemsGroup[s[i].getValue('appliedtotransaction')] = s[i].getValue('custcol_in8_group');
    }
    return itemsGroup;
}
