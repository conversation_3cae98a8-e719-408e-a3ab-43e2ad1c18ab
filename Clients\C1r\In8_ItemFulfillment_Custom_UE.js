/**
 * Item fulfillment
 * 
 * Version    Date            Author           Remarks
 * 1.00       23 Nov 2018     Marcel P
 *
 */

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit,
 *                      
 * @returns {Void}
 */
function beforeSubmit(type){
  
	try {
        if (nlapiGetFieldValue('custbody_in8_shop_id')) {
            
            var shipMethod = nlapiGetFieldValue('shipmethod');

            if (shipMethod) {
                var search = nlapiSearchRecord('customrecord_in8_shop_shipping', null, [
                    new nlobjSearchFilter('custrecord_in8_shop_ship_method', null, 'is', shipMethod)
                ], [
                    new nlobjSearchColumn('custrecord_in8_shop_ship_method'),
                    new nlobjSearchColumn('custrecord_in8_shop_ship_method_fulfill')
                ]) || [];
                
                if (search.length) {
                    var newShipMethod = search[0].getValue('custrecord_in8_shop_ship_method_fulfill');

                    if (newShipMethod) {
                        nlapiSetFieldValue('shipmethod', newShipMethod);
                    }                    
                }
            }
        }		
	} catch(e) {
		nlapiLogExecution('ERROR', 'Error ', ( e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e ));
	}		
}
