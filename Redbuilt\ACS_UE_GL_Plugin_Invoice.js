/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function afterSubmit(context) {

        // FOR RE-TRIGGERING GL PLUG-IN
        if(context.type == context.UserEventType.EDIT || context.type == context.UserEventType.CREATE || context.type == context.UserEventType.COPY){
            var recObj = record.load({
                type: context.newRecord.type,
                id: context.newRecord.id,
                isDynamic: true
            });

            recObj.save();
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
