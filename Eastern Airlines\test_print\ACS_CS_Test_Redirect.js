/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 */
define(['N/currentRecord', 'N/url'], function(currentRecord, url) {

    function pageInit(context){
    }

    function redirectToPDF(){
        rec = currentRecord.get();
        var outputUrl = url.resolveScript({
            scriptId: 'customscript_acs_sl_print_pdf',
            deploymentId: 'customdeploy_acs_sl_print_pdf',
            params: {
                vbid: rec.id
            }
        });
        window.open(outputUrl);
    }    

    return {
        pageInit : pageInit,
        redirectToPDF : redirectToPDF
    }
});
