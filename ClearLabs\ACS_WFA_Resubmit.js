/**
 *@NApiVersion 2.x
 *@NScriptType WorkflowActionScript
 */
define(['N/record'], function(record) {

    function onAction(scriptContext) {
        var recObj = scriptContext.newRecord;
        try {
            var reloadRecObj = record.load({
                type: record.Type.PURCHASE_ORDER,
                id: recObj.id,
                isDynamic: true
            });

            var emp = reloadRecObj.getText({ fieldId: "employee" })
            if(emp != "") {
                reloadRecObj.save();
            }

        } catch (e) {
            log.error('Error', e);
        }

    }

    return {
        onAction: onAction
    }
});
