/**
 * Module Description
 *    - Bulk customer invoicing of Insertion Orders
 * Version    Date            Author           Remarks
 * 1.00       29 Jan 2019     <PERSON>    TVG
 *
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
var modules = ['N/record', 'N/search', 'N/runtime', 'N/ui/serverWidget', 'N/task', 'N/redirect', 'N/url', '../TVG 2.0 Utils/utils'];
define(modules, function(record, search, runtime, ui, task, redirect, url, u) {
    var arInvoiceField = 'custcol_ar_invoice_tvg';
    function onRequest(context) {
        try {
            var request = context.request, // Gettin'em para-meters
                step = request.parameters.custpage_step,
                cust = request.parameters.custpage_cust_filt,
                start = request.parameters.custpage_start_filt,
                end = request.parameters.custpage_end_filt,
                linesToExclude = !!request.parameters.custpage_lines_to_exclude ? request.parameters.custpage_lines_to_exclude.split(',') : [];
            log.debug('Step:', step);
            log.debug('Cust:', cust);
            log.debug('Lines to exclude:', linesToExclude);
            if (step == 'submitLines') {
                // submitLines creates the invoice based on the marked sublist lines and stamps the relevant IO lines' invoice fields
                submitLines(request, linesToExclude);
            }
            var form = ui.createForm('Invoice Insertion Orders'); // Initializing the form object
            if (!!linesToExclude) {
                form.addField({id : 'custpage_lines_to_exclude', type : 'longtext', label : 'Lines to Exclude'}).updateDisplayType({displayType : 'hidden'}).defaultValue = linesToExclude.toString();
            }
            form.clientScriptModulePath = './bulkInvoicingCL.js'; // Setting the ClientScript file path
            // Adding filter fields and search button
            form.addSubmitButton('Search');
            var stepField = form.addField({id : 'custpage_step', type : 'text', label : 'Step'}).updateDisplayType({displayType : 'hidden'});
            stepField.defaultValue = !!step ? step : 'getLines';
            var custFiltField = form.addField({id : 'custpage_cust_filt', type : 'select', label : 'Customer', source : 'customer'}).updateLayoutType({layoutType : 'startrow'});
            custFiltField.isMandatory = true;
            custFiltField.defaultValue = !!cust ? cust : '';
            var startFiltField = form.addField({id : 'custpage_start_filt', type : 'date', label : ' From'}).updateLayoutType({layoutType : 'midrow'});
            //startFiltField.isMandatory = true;
            startFiltField.defaultValue = !!start ? start : '';
            var endFiltField = form.addField({id : 'custpage_end_filt', type : 'date', label : 'To'}).updateLayoutType({layoutType : 'endrow'});
            //endFiltField.isMandatory = true;
            endFiltField.defaultValue = !!end ? end : '';

            // Number of groups
            var fld = form.addField({id: 'custpage_num_groups', type: 'select', label: 'Number of groups'});                        
            fld.addSelectOption({ value : '', text : '' });                        
            for (var i = 1; i <= 50; i++) {
                fld.addSelectOption({ value : i, text : i });
            }
            if (request.parameters.custpage_num_groups) {
                fld.defaultValue = request.parameters.custpage_num_groups;
            }

            var totalField = form.addField({id : 'custpage_total_field', type : 'currency', label : 'Invoice Total'}).updateDisplayType({displayType : 'inline'}).updateBreakType({breakType : 'startcol'});
            totalField.defaultValue = 0.00;
            var listLinkField = form.addField({id : 'custpage_list', type : 'text', label : ' '}).updateDisplayType({displayType : 'inline'}).updateBreakType({breakType : 'startcol'});
            listLinkField.defaultValue = '<a href=' + url.resolveTaskLink({id : 'LIST_TRAN_CUSTINVC'}) + '>List</a>'; // Adding link to list view of invoices
            // Adding sublist fields and un/mark all buttons
            var sublist = form.addSublist({id : 'custpage_sublist', type : 'list', label : 'Insertion Orders'});
            sublist.addButton({id : 'custpage_mark_all', label : 'Mark All', functionName : 'markAll'}); // Defined in ClientScript
            sublist.addButton({id : 'custpage_unmark_all', label : 'Unmark All', functionName : 'unmarkAll'}); // Defined in ClientScript
            sublist.addField({id : 'custpage_bill', type : 'checkbox', label : ' '});
            sublist.addField({id : 'custpage_vend_field', type : 'select', label : 'Vendor', source : 'vendor'}).updateDisplayType({displayType : 'inline'});
            sublist.addField({id : 'custpage_io_field', type : 'select', label : 'IO#', source : 'purchaseorder'}).updateDisplayType({displayType : 'inline'});
            sublist.addField({id : 'custpage_camp_field', type : 'text', label : 'Campaign'});
            sublist.addField({id : 'custpage_camp_field_2', type : 'text', label : 'Campaign ID'}).updateDisplayType({displayType : 'hidden'}); // Storing campaign internal id in this field
            //sublist.addField({id : 'custpage_sale_date_field', type : 'date', label : 'On Sale Date'});
            sublist.addField({id : 'custpage_start_field', type : 'date', label : 'Start / On Sale Date'});
            sublist.addField({id : 'custpage_end_field', type : 'date', label : 'End Date'});
            sublist.addField({id : 'custpage_item_field', type : 'select', label : 'Item', source : 'item'}).updateDisplayType({displayType : 'hidden'});
            sublist.addField({id : 'custpage_placement_field', type : 'textarea', label : 'Placement'}).updateDisplayType({displayType : 'inline'});
            sublist.addField({id : 'custpage_notes_field', type : 'text', label : 'Notes'}).updateDisplayType({displayType : 'inline'});
            sublist.addField({id : 'custpage_desc_field', type : 'text', label : 'Description'}).updateDisplayType({displayType : 'entry'}).updateDisplaySize({width : 15, height : 10});
            sublist.addField({id : 'custpage_amt_field', type : 'currency', label : 'Amount'});
            var fldGroup = sublist.addField({id : 'custpage_group', type: 'select', label : 'Group'});
            sublist.addField({id : 'custpage_amt_field_2', type : 'currency', label : 'Amount (Retrievable)'}).updateDisplayType({displayType : 'hidden'}); // Above amount field not retrievable from ClientScript (list type?)
            sublist.addField({id : 'custpage_io_line', type : 'text', label : 'IO Line'}).updateDisplayType({displayType : 'hidden'});
            sublist.addField({id : 'custpage_unique_id', type : 'text', label : 'Unique ID'}).updateDisplayType({displayType : 'hidden'});
            sublist.addField({id : 'custpage_cust_field', type : 'select', label : 'Customer', source : 'customer'}).updateDisplayType({displayType : 'hidden'});
            sublist.addField({id : 'custpage_cust_text', type : 'text', label : 'Customer (Text)'}).updateDisplayType({displayType : 'hidden'});
            if (step == 'getLines') {
                // getLines displays the search results on the sublist based on the chosen filters
                getLines(form, sublist, cust, start, end, linesToExclude);
            }
                        
            fldGroup.addSelectOption({ value: '', text: '' });

            if (request.parameters.custpage_num_groups) {
                for (var i = 0; i < request.parameters.custpage_num_groups; i++) {
                    fldGroup.addSelectOption({ value: i + 1, text: 'Group ' + (i + 1) });
                }
            }
            
            var sublist = form.addSublist({id : 'custpage_sublist_group', type : 'inlineeditor', label : 'Groups'});        
            sublist.addField({id : 'custpage_group', type: 'select', label : 'Group', source: 'customlist_in8_group_list'});
            sublist.addField({id : 'custpage_group_description', type: 'textarea', label : 'Description'});             

            if (request.parameters.custpage_num_groups) {
                for (var i = 0; i < request.parameters.custpage_num_groups; i++) {
                    sublist.setSublistValue({id : 'custpage_group', line : i, value : i + 1});
                }
            }

            context.response.writePage(form); // writePage dat form yo
        } catch(err) {
            if (err.toString() === 'error.SuiteScriptError') {
                log.error('BULK_INVOICING_ERROR', 'MESSAGE: ' + err.message + ' STACK: ' + err.stack);
                throw 'BULK_INVOICING_ERROR\n' + err.name + '\n\n' + err.message + '\n\n' + err.id + '\n\n' + err.stack;
            } else {
                log.error('Bulk Invoicing Error:', err.toString());
            }
        }
    }

    function searchLines(cust, start, end, linesToExclude) {
        log.audit('start : end', start + " : " + end);
        log.audit('!!end', !!end);
        var lineFilt = [['lineuniquekey', 'isnotempty', ''], 'AND'];
        // building filter to exclude lines that were submitted in the previous page load that may not have been updated by the ScheduledScript yet
        linesToExclude.forEach(function(line) {
            lineFilt.push(['lineuniquekey', 'notequalto', line], 'AND');
        });
        if (!!start && !!end) {
            lineFilt.push([['custcol_start_date', 'within', start, end], 'OR', ['custcol_on_sale_date', 'within', start, end]]);
        } else if (!!start) {
            lineFilt.push([['custcol_start_date', 'onorafter', start], 'OR', ['custcol_on_sale_date', 'onorafter', start]]);
        } else if (!!end) {
            lineFilt.push([['custcol_start_date', 'onorbefore', end], 'OR', ['custcol_on_sale_date', 'onorbefore', end]]);
        } else {
            lineFilt.pop();
        }
        var ioSearch = search.create({
            type : 'transaction',
            filters : [
                ["type","anyof","VendBill","VendCred","CardRfnd","Check","CardChrg","PurchOrd"], 'AND',
                ['mainline', 'is', 'F'], 'AND', [arInvoiceField, 'anyof', '@NONE@'], 'AND',
                ['taxline', 'is', 'F'], 'AND', ['item', 'anyof', 211], 'AND', lineFilt, 'AND', ['billable', 'is', 'T'], 'AND',
                [['custbody_client', 'anyof', cust], 'OR', ['customer.internalid', 'anyof', cust]], 'AND',
                ["formulatext: CASE WHEN {type} != 'Purchase Order' THEN 1 ELSE CASE WHEN {closed} != 'T' THEN 1 ELSE 0 END END","is","1"]
            ],
            columns : [
                search.createColumn({name : 'internalid', join : 'vendor'}), search.createColumn('item'), search.createColumn({name : 'tranid', sort : search.Sort.ASC}), search.createColumn('type'),
                search.createColumn('amount'), search.createColumn('custbody_campaign_name_field_tvg'), search.createColumn('custbody_client'), search.createColumn('custcol_placement'),
                search.createColumn('custcol_on_sale_date'), search.createColumn({name : 'custcol_start_date', sort : search.Sort.ASC}), search.createColumn('custcol_end_date'),
                search.createColumn('linesequencenumber'), search.createColumn('lineuniquekey'), search.createColumn('custcol_note')
            ]
        });
        return ioSearch;
    }

    function getLines(form, sublist, cust, start, end, linesToExclude) {

        var ioSearch = searchLines(cust, start, end, linesToExclude);
        var ioSearchResults = u.getAllSearchResults(ioSearch);
        log.debug('ioSearchResults', JSON.stringify(ioSearchResults));
        if (!u.isNullOrEmpty(ioSearchResults)) {
            // Adding the submitButton only if search results are found, prevents submitting an empty form
            form.addButton({id : 'custpage_submit_button', label : 'Submit', functionName : 'submitButton'});
            form.addButton({id : 'custpage_export_button', label : 'Export', functionName : 'exportButton'});
            // Looping through search results and set sublist lines for each column value that exists
            var lineCounter = 0;
            ioSearchResults.forEach(function(result) {
                var vendor = result.getValue({name : 'internalid', join : 'vendor'}), cStart = result.getValue('custcol_start_date'), note = result.getValue('custcol_note'),
                    cEnd = result.getValue('custcol_end_date'), item = result.getValue('item'), place = result.getValue('custcol_placement'), onSaleDate = result.getValue('custcol_on_sale_date'),
                    amount = result.getValue('amount'), line = result.getValue('linesequencenumber'), unique = result.getValue('lineuniquekey'), customer = result.getValue('custbody_client'),
                    campaign = result.getText('custbody_campaign_name_field_tvg'), campaignID = result.getValue('custbody_campaign_name_field_tvg'), customerText = result.getText('custbody_client');
                if (!!vendor)       sublist.setSublistValue({id : 'custpage_vend_field', line : lineCounter, value : vendor});
                if (!!result.id)    sublist.setSublistValue({id : 'custpage_io_field', line : lineCounter, value : result.id});
                if (!!campaign)     sublist.setSublistValue({id : 'custpage_camp_field', line : lineCounter, value : campaign});
                if (!!campaignID)   sublist.setSublistValue({id : 'custpage_camp_field_2', line : lineCounter, value : campaignID}); // Storing campaign internal id here
                if (!!cStart)       sublist.setSublistValue({id : 'custpage_start_field', line : lineCounter, value : cStart});
                if (!!cEnd)         sublist.setSublistValue({id : 'custpage_end_field', line : lineCounter, value : cEnd});
                if (!!onSaleDate)   sublist.setSublistValue({id : 'custpage_start_field', line : lineCounter, value : onSaleDate});
                if (!!item)         sublist.setSublistValue({id : 'custpage_item_field', line : lineCounter, value : item});
                if (!!place)        sublist.setSublistValue({id : 'custpage_placement_field', line : lineCounter, value : place});
                if (!!note)         sublist.setSublistValue({id : 'custpage_notes_field', line : lineCounter, value : note});
                if (!u.isNullOrEmpty(amount)) {     sublist.setSublistValue({id : 'custpage_amt_field', line : lineCounter, value : parseFloat(-1*amount)}); // Amount field is not gettable from ClientScript
                                    sublist.setSublistValue({id : 'custpage_amt_field_2', line : lineCounter, value : parseFloat(-1*amount)}); } // Amount field 2 is gettable
                if (!!line)         sublist.setSublistValue({id : 'custpage_io_line', line : lineCounter, value : line});
                if (!!unique)       sublist.setSublistValue({id : 'custpage_unique_id', line : lineCounter, value : unique});
                if (!!customer)     sublist.setSublistValue({id : 'custpage_cust_field', line : lineCounter, value : customer});
                if (!!customerText) sublist.setSublistValue({id : 'custpage_cust_text', line : lineCounter, value : customerText});
                lineCounter++;
            });
        }
    }

    function submitLines(request, linesToExclude) {

        var lineCount = request.getLineCount('custpage_sublist');
        // Building linesToInclude object to be fed into ScheduledScript
        // Updating linesToExclude array for the next page load
        var linesToInclude = {};
        var groups = {};

        for (var i = 0; i < lineCount; i++) {
            var bill = request.getSublistValue('custpage_sublist', 'custpage_bill', i);
            if (bill == 'T') {
                var uniqueKey = request.getSublistValue('custpage_sublist', 'custpage_unique_id', i);
                var itemDesc = request.getSublistValue('custpage_sublist', 'custpage_desc_field', i);                                
                linesToInclude[uniqueKey] = itemDesc;
                groups[uniqueKey] = request.getSublistValue('custpage_sublist', 'custpage_group', i);
                linesToExclude.push(uniqueKey);
            }
        }

        // Groups descriptions
        var lineCount = request.getLineCount('custpage_sublist_group');
        var groupsDesc = [];

        for (var i = 0; i < lineCount; i++) {
            groupsDesc.push({ group: Number(request.getSublistValue('custpage_sublist_group', 'custpage_group', i)),
                            description: request.getSublistValue('custpage_sublist_group', 'custpage_group_description', i) });
        }
        log.debug('groups descs', groupsDesc);

        var params = { // SS parameters
            custscript_data : {
                linesToInclude : linesToInclude,
                groups: groups,
                groupsDesc : groupsDesc,
                userId : runtime.getCurrentUser().id
            }
        };
        params.custscript_data = JSON.stringify(params.custscript_data);
        var createInvoices = task.create({
            taskType : task.TaskType.SCHEDULED_SCRIPT,
            scriptId : 'customscript_bulk_invoice_ss',
            params : params
        });
        var taskID = createInvoices.submit();
        log.debug('taskStatus', task.checkStatus(taskID).status);
        // Checking that task was successfully submitted
        if (task.checkStatus(taskID).status == 'FAILED') {
            log.error('Failed to create invoices!');
        }
        // Redirecting to the current Suitelet with the proper step, ensuring multiple invoices aren't created because of users pressing the browser's refresh button
        var script = runtime.getCurrentScript();
        redirect.toSuitelet({
            scriptId : script.id,
            deploymentId : script.deploymentId,
            parameters : {
                custpage_step : 'getLines',
                custpage_cust_filt : request.parameters.custpage_cust_filt,
                custpage_start_filt : request.parameters.custpage_start_filt,
                custpage_end_filt : request.parameters.custpage_end_filt,
                custpage_lines_to_exclude : linesToExclude.length > 0 ? linesToExclude.toString() : ''
            }
        });
    }

    return {
        onRequest : onRequest
    };
});