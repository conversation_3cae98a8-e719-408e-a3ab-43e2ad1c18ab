/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/runtime'], function(runtime) {

    function beforeLoad(context) {
        var recObj = context.newRecord;
        if(recObj.getValue({ fieldId: 'custbody15' }) == 2){
            if(runtime.getCurrentUser().id == recObj.getValue({ fieldId: 'nextapprover' })){
                context.form.clientScriptModulePath = 'SuiteScripts/ACS_CS_Redirect_Reject_Button.js';
                context.form.addButton({
                    id: 'custpage_reject_vendor_bill',
                    label: 'Reject',
                    functionName: 'redirectToReject()'
                });
            }
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
