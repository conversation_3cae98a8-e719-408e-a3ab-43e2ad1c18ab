/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/record', 'N/format'], function(search, record, format) {

    function getInputData() {
        var itemSearch = search.load({
            id: 'customsearch_acs_items_with_with_recept2'
        });
        var resultArray = [];
        
        var pagedData = itemSearch.runPaged({ pageSize: 1000 });
        // iterate the pages
        for( var i=0; i < pagedData.pageRanges.length; i++ ) {
    
            // fetch the current page data
            var currentPage = pagedData.fetch(i);
    
            // and forEach() thru all results
            currentPage.data.forEach( function(result) {
                resultArray.push({
                    itemId: result.getValue({ name: 'item', summary: 'GROUP' })
                });
            });
    
        }
        
        return resultArray;
    }

    function reduce(context) {
        var reduceKey = context.key;
        var reduceValues = context.values;

        try {

            var parsedValue = JSON.parse(reduceValues[0]);
            var recObj = record.load({
                type: record.Type.INVENTORY_ITEM,
                id: parsedValue.itemId
            });

            var line = recObj.findSublistLineWithValue({
                sublistId: 'locations',
                fieldId: 'location',
                value: 1
            });     

            var itemSearch = search.load({
                id: 'customsearch_acs_inventory_activity_sear'
            });
            var itemFilter = search.createFilter({ name: 'internalidnumber', join: 'item', operator: search.Operator.EQUALTO, values: recObj.id });
            itemSearch.filters.push(itemFilter);
            var searchResult = itemSearch.run().getRange({ start: 0, end: 1 });
            if(searchResult.length == 0){        
                recObj.setSublistValue({
                    sublistId: 'locations',
                    fieldId: 'nextinvtcountdate',
                    line: line,
                    value: ''
                });

                recObj.setSublistValue({
                    sublistId: 'locations',
                    fieldId: 'invtcountinterval',
                    line: line,
                    value: ''
                });

                var recId = recObj.save({
                    enableSourcing: true,
                    ignoreMandatoryFields: true
                });

                if(recId) {
                    log.debug('Successfuly saved', 'id: ' + recId);
                }
            }

        } catch (e) {
            log.debug("Error", e);
        }
    }

    function summarize(summary) {
        
    }

    return {
        getInputData: getInputData,
        reduce: reduce,
        summarize: summarize
    }
});
