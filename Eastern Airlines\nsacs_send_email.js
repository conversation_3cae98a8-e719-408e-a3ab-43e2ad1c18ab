/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NModuleScope SameAccount
 */
define(['N/email', 'N/search', 'N/runtime', 'N/url', 'N/record'],

function(email, search, runtime, url, record) {

    /**
     * Function definition to be triggered before record is loaded.
     *
     * @param {Object} scriptContext
     * @param {Record} scriptContext.newRecord - New record
     * @param {Record} scriptContext.oldRecord - Old record
     * @param {string} scriptContext.type - Trigger type
     * @Since 2015.2
     */
    function afterSubmit(scriptContext) {
        if(scriptContext.type == "create" || scriptContext.type == "approve" || scriptContext.type == "edit"){
            //Getters
            var currentRecord = scriptContext.newRecord;
            var billID = currentRecord.id;
            var tranNo = currentRecord.getValue('transactionnumber');
            var vendor = currentRecord.getText('entity');

            //Split fullname to First Name and Last Name
            var approverObject = searchForEmpEmails(vendor);
            log.debug('approverObject', 'approverObject: ' + JSON.stringify(approverObject));
            var approver = "Test Approver";
            var emailBody = generateEmailBody(billID, approver, vendor, tranNo);
            var recipients = "<EMAIL>"
            log.debug('emailBody', 'emailBody: ' + emailBody);
            try {
                email.send({
                    author: runtime.getCurrentScript().getParameter({name: 'custscript_nsacs_sender'}),
                    recipients: recipients,
                    subject: 'Vendor Bill: ' + tranNo + ' Requires Your Approval',
                    body: emailBody,
                    relatedRecords: {
                        transactionId: billID
                    }
                });
                log.audit({
                    title:"Success",
                    details: "Email Successfully Sent"
                })
            }
            catch(err) {
                log.audit({
                    title:"Failed",
                    details: err.message
                })
            }
        }
        
    }
    function searchForEmpEmails(vemdor) {
        // var employeeSearchObj = search.create({
        //     type: "employee",
        //     filters: [
        //         ["formulatext: {firstname}", "contains", firstName],
        //         "AND",
        //         ["formulatext: {lastname}", "contains", lastName]
        //     ],
        //     columns: [
        //         "email"
        //     ]
        // });
        // var approverEmail, approverID;
        // employeeSearchObj.run().each(function (result) {
        //     approverEmail = result.getValue('email');
        //     approverID = result.id;
        //     return true;
        // });
        // var approverObj = {
        //     approveremail: approverEmail,
        //     approverid: approverID,
        //     firstname: 
        // }
        // return approverObj;
    }
    function generateEmailBody(billID, approver, vendor, tranNo) {
        var arrayGL = generateGLImpact(billID);
        var body = '<!DOCTYPE html>'
        body +='<head>';
        body +='<meta charset="utf-8">';
        body +='</head>';
        body +='<body>';
        body += '<p>Hi ' + approver + ',<br> <br></p>';
        body += '<p>Vendor Bill (' + tranNo + ') from ' + vendor + ' has been submitted for approval.</p>';

        var suiteletURL = generateSuiteletUrl(billID);
        //Buttons lines.
        body += '<form action="'+suiteletURL+'" method="POST" target="_blank">'
        body += '<input type="hidden" name="bill_id" value="'+billID+'">'        
        body += '<table width="500px;" cellspacing="0" cellpadding="0">'
        body += '<tr>'
        body += '<td><label>Rejection Note:</label><br><input type="text" name="rejection_note" /></td>'
        body += '</tr>'
        body += '<tr>'
        body += '<td>'
        body += '<button type="submit" name="approval_status" value="2">'
        body += 'Approve'
        body += '</button>'
         body += '<button type="submit" name="approval_status" value="3">'
        body += 'Reject'
        body += '</button>'
        body += '</td>'
        body += '</tr>'
        body += '</table>'
        body += '</form>'

        //Start of GL Impact lines.
        body += '<u><br>Below is the GL Impact:</u>';
        body += '<table style="border-collapse: collapse;width: 100%">';
        body += '<tr style="background-color:#404040;color: white;border: 1px solid black">';
        body += ' <th style="border: 1px solid black"> Account </th>';
        body += ' <th style="border: 1px solid black"> Amount (Debit) </th>';
        body += ' <th style="border: 1px solid black"> Amount (Credit) </th>';
        body += ' <th style="border: 1px solid black"> Posting </th>';
        body += ' <th style="border: 1px solid black"> Memo </th>';
        body += ' <th style="border: 1px solid black"> Name </th>';
        body += ' <th style="border: 1px solid black"> Department </th>';
        body += ' <th style="border: 1px solid black"> Line of Business </th>';
        body += ' <th style="border: 1px solid black"> Location </th>';
        body += '</tr>';

        for (x = 0; x < arrayGL.length; x++) {
            body += '<tr>';
            body += ' <td style="border: 1px solid black">' + arrayGL[x].account + '</td>';
            body += ' <td style="border: 1px solid black">' + arrayGL[x].debitamount + '</td>';
            body += ' <td style="border: 1px solid black">' + arrayGL[x].creditamount + '</td>';
            body += ' <td style="border: 1px solid black">' + arrayGL[x].posting + '</td>';
            body += ' <td style="border: 1px solid black">' + arrayGL[x].memo + '</td>';
            body += ' <td style="border: 1px solid black">' + arrayGL[x].entity + '</td>';
            body += ' <td style="border: 1px solid black">' + arrayGL[x].department + '</td>';
            body += ' <td style="border: 1px solid black">' + arrayGL[x].class + '</td>';
            body += ' <td style="border: 1px solid black">' + arrayGL[x].location + '</td>';
            body += '</tr>';
        }
        body += '</table>';
        body += '</body></html>';

        return body;
    }

    function generateGLImpact(billID) {
        var vendorbillSearchObj = search.create({
            type: "vendorbill",
            filters: [
                ["type", "anyof", "VendBill"],
                "AND",
                ["internalid", "anyof", billID]
            ],
            columns: [
                "account",
                "debitamount",
                "creditamount",
                "posting",
                "memo",
                "entity",
                "department",
                "class",
                "location"
            ]
        });
        var arrayGL = [];
        vendorbillSearchObj.run().each(function (result) {
            var glObj = {
                account: result.getText('account'),
                debitamount: result.getValue('debitamount'),
                creditamount: result.getValue('creditamount'),
                posting: result.getValue('posting'),
                memo: result.getValue('memo'),
                entity: result.getText('entity'),
                department: result.getText('department'),
                class: result.getText('class'),
                location: result.getText('location')
            }
            arrayGL.push(glObj);
            return true;
        });
        return arrayGL;
    }

    function generateSuiteletUrl(billID) {
        var approveSuitelet = url.resolveScript({
            scriptId: "customscript_nsacs_vendor_bill_approval",
            deploymentId: 1,
            returnExternalUrl: true
        });
        // var rejectSuitelet = url.resolveScript({
        //     scriptId: runtime.getCurrentScript().getParameter({
        //         name: 'custscript_nsacs_vend_bill_suitelet'
        //     }),
        //     deploymentId: 1,
        //     returnExternalUrl: true,
        //     params: {
        //         'custparam_billid': billID,
        //         'custparam_status': 3
        //     }
        // });
        // var urlObj = {
        //     approve: approveSuitelet,
        //     reject: rejectSuitelet
        // }
        return approveSuitelet;
    }
    return {
        afterSubmit: afterSubmit
    };
    
});
