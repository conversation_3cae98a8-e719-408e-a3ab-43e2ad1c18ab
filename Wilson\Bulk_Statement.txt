<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<#assign print_attachments=false />
<#assign uniq=.now?iso_utc_ms />
<#assign key="ac288b08-3fb2-4e30-b96f-a03adc0ca4cf" />
<#if record??>
    <pdfset>
        <pdf>
            <head>
                <macrolist>
                    <macro id="nlheader">
                        <table class="header" style="width: 100%;">
                            <tr>
                                <td rowspan="3">
                                    <#if subsidiary.logo@url?length !=0><img src="${subsidiary.logo@url}"
                                            style="float: left; margin: 7px; width: 151px; height: 94px;" /> </#if>
                                </td>
                                <td rowspan="3" align="right">
                                    <span>${subsidiary.mainaddress_text}</span>
                                </td>
                                <td align="right">
                                    <span class="title">${record@title}</span>
                                </td>
                            </tr>
                            <tr>
                                <td align="right">as of ${record.trandate} </td>
                            </tr>
                        </table>
                    </macro>
                    <macro id="nlfooter">
                        <table class="footer" style="width: 100%;">
                            <tr>
                                <td class="note" style="text-align: center ">
                                    <p align="center"></p>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: center; ">
                                    <p align="left"></p>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align: center">
                                    <p align="center"></p>
                                </td>
                            </tr>
                            <tr>
                                <td border="0.1" align="center">
                                    Page
                                    <pagenumber /> of
                                    <totalpages /> Pages
                                </td>
                            </tr>
                        </table>
                    </macro>
                </macrolist>
                <style type="text/css">
                    table {
                        font-family: sans-serif;
                        font-size: 9pt;
                        table-layout: fixed;
                    }
                    p{
                        text-align: left;
                    }

                    th {
                        font-weight: bold;
                        font-size: 8pt;
                        vertical-align: middle;
                        padding: 5px 6px 3px;
                        background-color: #e3e3e3;
                        color: #333333;
                    }

                    td {
                        padding: 4px 6px;
                    }

                    b {
                        font-weight: bold;
                        color: #333333;
                    }

                    table.header td {
                        padding: 0;
                        font-size: 10pt;
                    }

                    table.footer td {
                        padding: 2pt;
                        font-size: 8pt;
                    }

                    table.itemtable th {
                        padding-bottom: 10px;
                        padding-top: 10px;
                    }

                    table.body td {
                        padding-top: 2px;
                    }

                    .remittanceTable {
                        page-break-inside: avoid;
                    }

                    td.addressheader {
                        font-weight: bold;
                        font-size: 8pt;
                        padding-top: 6px;
                        padding-bottom: 2px;
                    }

                    td.address {
                        padding-top: 0;
                    }

                    span.title {
                        font-size: 28pt;
                    }

                    span.number {
                        font-size: 16pt;
                    }

                    div.remittanceSlip {
                        width: 100%;
                        /* To ensure minimal height of remittance slip */
                        height: 200pt;
                        page-break-inside: avoid;
                        page-break-after: avoid;
                    }

                    hr {
                        border-top: 1px dashed #d3d3d3;
                        width: 100%;
                        color: #ffffff;
                        background-color: #ffffff;
                        height: 1px;
                    }

                    .signature {
                        border: 0;
                        border-bottom: 1px solid #000;
                    }
                </style>
            </head>

            <body header="nlheader" header-height="15%" footer="nlfooter" footer-height="10%" padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
                <#assign curl=companyInformation.loginURL?replace("<[^>]+>","","r")?replace("app","extforms")/>
                <#assign npos=curl?index_of("/",8)-1 />
                <#assign baseURL=curl[0..npos] />
                <#assign _startdate=(record.lines[0].datecol)!'' />
              	<#assign pdfdata={"suiteletURL":"","pdfs":[]} />

                    <table style="width: 100%; margin-top: 10px;">
                        <tr>
                            <td class="addressheader" colspan="3"><b>${record.billaddress@label}</b></td>
                        </tr>
                        <tr>
                            <td class="address" colspan="3">${record.billaddress}</td>
                        </tr>
                    </table>
                    <table class="body" style="width: 100%;">
                        <tr>
                            <td align="right"><b>Customer ID:</b> ${customer.entityid} <#if subsidiary.id=='7'><b>RIMMS
                                        ID:</b> ${customer.custentity_ovs_rimss_field}</#if>
                            </td>
                        </tr>
                    </table>

                    <table class="itemtable" style="width: 100%; margin-top: 10px;">
                        <thead>
                        <tr>
                            <th colspan="11" style="border:.1px solid #000000;">Date</th>
                            <th colspan="10" style="border:.1px solid #000000;">Type</th>
                            <th align="left" colspan="22" style="border:.1px solid #000000;">Invoice / Doc. #</th>
                            <th align="center" colspan="11" style="border:.1px solid #000000;">Due Date</th>
                            <th align="center" colspan="7" style="border:.1px solid #000000;">
                                <p style="text-align:center">Days Past Due</p>
                            </th>
                            <th align="right" colspan="13" style="border:.1px solid #000000;">Amount</th>
                            <th align="right" colspan="13" style="border:.1px solid #000000;">Open</th>
                            <th align="right" colspan="13" style="border:.1px solid #000000;">Balance</th>
                        </tr>
                        </thead>

                        <!--setting number_format="currency">-->
                        <#assign runningBalance=0 />
                        <#assign paymentDup=[] />
                        <#list record.lines as line>
                            <#if line_index==0>
                                <#assign _suiteletURL=(baseURL+"/app/site/hosting/scriptlet.nl?script=1375&deploy=1&compid="+companyInformation.companyid+"&h=5b31e9b2de238d25d5cb&openonly=F&customerid="+customer.id+"+&statementdate="+record.trandate+"&startdate="+line.datecol+"&subsidiary="+subsidiary.id+"&uniq="+uniq+"&key="+key+"&mode=DATA") />
                                <#include _suiteletURL />
                              	<#if !paymentData??>
                                  <!--Try again, the query or request may have timed out-->
                                  <#include _suiteletURL />
                                </#if>
                                <#if line.balance?string?has_content>
                                    <#assign runningBalance=line.balance />
                                </#if>
                            </#if>

                            <!--if ((line.charge?length &gt; 0) || (line.charge?length==0 && line.amountremaining?length
                                &gt; 0))>-->
                                <#if paymentData['${line.description?replace("Invoice #", "_" )}']?has_content>
                                    <tr>
                                        <#else />
                                    <tr style="border-bottom: 1px;">
                                </#if>

                                <td colspan="11">${line.datecol}</td>
                                <td colspan="10">${line.description?split("#")?first}</td>
                                <td align="left" colspan="22">
                                    <#if line.description!='Balance Forward'><p>${line.description?split("#")?last}</p></#if>
                                </td>
                                <td align="center" colspan="11">${line.duedate}</td>
                                <td align="center" colspan="7">
                                    <#if line.duedate?string?has_content &&
                                        paymentData['${line.description?replace("Invoice #", "_" )}']?has_content>
                                        <#assign d1=record.trandate?long>
                                            <#assign d2=line.duedate?long>
                                                <#assign daysoverdue=((d1 - d2) / (24*60*60*1000))?floor>
                                                    <#if daysoverdue &gt; 0>${daysoverdue}</#if>
                                    </#if>
                                </td>
                                <td align="right" colspan="13"><#if line.description!='Balance Forward'><#if line.charge?length &gt; 0>${line.charge}<#else />${(line.payment*-1)?string.currency}</#if></#if>
                                </td>
                                <td align="right" colspan="13">
                                  	<#if line.description!='Balance Forward'>
                                  		<#if line.charge?length &gt; 0>
                                          	${line.amountremaining}
                                        <#else />
                                          	<#if line.amountremaining?length &gt; 0 >
                                              	(${line.amountremaining})
                                            </#if>
                                        </#if>
                                    </#if>
                                </td>
                                <td align="right" colspan="13"><!--{runningBalance?string.currency}--> ${line.balance}</td>
                                </tr>

                                <#assign invoiceText='${line.description?replace("Invoice #", "_")}' />
                                <#if paymentData['${invoiceText}']?has_content>
                                    <#list paymentData['${invoiceText}'] as payment>
                                        <#if payment?is_last>
                                            <tr style="border-bottom: 1px;">
                                                <#else>
                                            <tr>
                                        </#if>

                                        <td colspan="11">${payment.date}</td>
                                        <td align="right" colspan="10">Applied</td>
                                        <td colspan="22" style="padding-left:12pt">${payment.num?split("#")?last}</td>
                                        <td align="center" colspan="11"></td>
                                        <td align="center" colspan="7"></td>
                                        <td align="right" colspan="13">
                                            <#if payment.amount?length &gt; 0>(${payment.amount?number?string.currency})
                                            </#if>
                                        </td>
                                        <td align="right" colspan="13"></td>
                                        <td align="right" colspan="13"></td>
                                        </tr>
                                    </#list>
                                </#if>
                                <#else>

                            <!--/#if-->
                        </#list>

                        <!-- End of Table -->

                    </table>
                    <table class="aging" style="width: 100%; margin-top: 10px;">
                        <tr>
                            <th style="border:.1px solid #000000;">${record.aging1@label}</th>
                            <th style="border:.1px solid #000000;">${record.aging2@label}</th>
                            <th style="border:.1px solid #000000;">${record.aging3@label}</th>
                            <th style="border:.1px solid #000000;">${record.aging4@label}</th>
                            <th style="border:.1px solid #000000;">${record.aging5@label}</th>
                            <th style="border:.1px solid #000000;">${record.agingbal@label}</th>
                        </tr>
                        <tr>
                            <td style="border:.1px solid #000000;">${record.aging1}</td>
                            <td style="border:.1px solid #000000;">${record.aging2}</td>
                            <td style="border:.1px solid #000000;">${record.aging3}</td>
                            <td style="border:.1px solid #000000;">${record.aging4}</td>
                            <td style="border:.1px solid #000000;">${record.aging5}</td>
                            <td style="border:.1px solid #000000;">${record.agingbal}</td>
                        </tr>
                        <tr>
                            <!-- <td colspan="6" align="center"><B>Interested in going green? Email <EMAIL>
                                    to
                                    request
                                    statements by email!</B></td> -->
                        </tr>
                    </table>
                    <!-- Remittance Slip -->
                    <hr style="border-top: dotted 4px;" />
                    <table class="remittanceTable" style="width: 100%; margin-top: 10px;page-break-inside: avoid;">
                        <tr>
                            <td colspan="5" align="center" style="font-size:12pt"><strong>Remittance Slip</strong>
                            </td>
                        </tr>
                       <tr>
                                <td colspan="5" align="center" style="font-size:8pt"><strong>${record.trandate}</strong>
                            </td>
                            </tr>
                        <tr style="margin-top:10px">
                            <td style="width:25%" colspan="2">
                                <strong>Make Check Payable
                                    To</strong><br /><span>${subsidiary.mainaddress_text}</span>
                            </td>
                            <td style="width:35%"></td>
                            <td class="address" colspan="2" style="margin-left:60px">
                                <b>Customer: </b><br></br>${customer.entityid}<br></br><br></br>
                                <strong>Amount
                                    Due</strong>&nbsp;<b>${record.agingbal}</b><br></br><br></br>
                                <strong>Amount Paid</strong>&nbsp;<span>_____________</span>
                            </td>
                        </tr>
                    </table>
            </body>
        </pdf>
		<#if print_attachments>
            <#assign _suiteletURL=(baseURL+"/app/site/hosting/scriptlet.nl?script=1375&deploy=1&compid="+companyInformation.companyid+"&h=5b31e9b2de238d25d5cb&openonly=F&customerid="+customer.id+"+&statementdate="+record.trandate+"&startdate="+_startdate+"&subsidiary="+subsidiary.id+"&uniq="+uniq+"&key="+key+"&mode=PDF_ATTACHMENTS&ftl_var=pdfdata&pdfs=") />
            <#include _suiteletURL />
            <#assign pdfURL=pdfdata.suiteletURL />
	        <#assign aFiles=pdfdata['pdfs'] />
        	<#list aFiles as _file>
                <#assign renderPDF=false />
                <#if _file?has_content>
                    <#assign pdffile = pdfURL+"&"+_file />
                    <#assign urlcomp=[] />
                    <#list pdffile?split("?") as x>
                        <#assign renderPDF=true />
                        <#if x?starts_with("https")==false>
                            <#list x?split('&') as var>
                                <#assign urlcomp=urlcomp + [var?split('=')[1]] />
                            </#list>
                        <#else />
                            <#assign urlcomp=[x] />
                        </#if>
                    </#list>
                    <#if renderPDF>
                        <pdf src="${urlcomp[0]}?script=${urlcomp[1]}&amp;deploy=${urlcomp[2]}&amp;compid=${urlcomp[3]}&amp;h=${urlcomp[4]}&amp;recid=${urlcomp[5]}&amp;uniq=${uniq}&amp;key=${key}" />
                        <#flush>
                    </#if>
                </#if>
            </#list>
        <#else />
            <#assign _suiteletURL=(baseURL+"/app/site/hosting/scriptlet.nl?script=1375&deploy=1&compid="+companyInformation.companyid+"&h=5b31e9b2de238d25d5cb&openonly=F&customerid="+customer.id+"+&statementdate="+record.trandate+"&startdate="+_startdate+"&subsidiary="+subsidiary.id+"&uniq="+uniq+"&key="+key+"&mode=PDF_FTL") />
            <#include _suiteletURL />
        </#if>
	</pdfset>
<#else>
	<pdf>
		<head></head>
		<body>
			<p>Multi currency customer setting was detected. Please use Multi Currency Advanced Printing Template</p>
		</body>
	</pdf>
</#if>