<#-- format specific processing -->
<#function computeExpirationDate>
    <#assign processDate = pfa.custrecord_2663_process_date?string("yyDDD")>
    <#assign expireDate = processDate?number + 1>        
    <#return expireDate?c>
</#function>

<#function getReferenceNote payment>
    <#assign paidTransactions = transHash[payment.internalid]>
    <#assign referenceNote = "">
    <#assign paidTransactionsCount = paidTransactions?size>
    <#if (paidTransactionsCount >= 1)>
    	<#list paidTransactions as transaction>
    		<#if transaction.tranid?has_content>
    			<#if referenceNote?has_content>
    				<#assign referenceNote = referenceNote + " " + transaction.tranid>
    			<#else>
    				<#assign referenceNote = transaction.tranid>
    			</#if>
		    </#if>
		</#list>
    </#if>
	<#return referenceNote>
</#function>

<#function getAccountType ebank>
	<#assign accountType = "">
	<#assign acctType = "">
    <#assign accountType = ebank.custrecord_2663_entity_bank_acct_type>
	<#if accountType == "Checking">
		<#assign acctType = "D">
	</#if>
	<#if accountType == "Savings">
		<#assign acctType = "S">
	</#if>
<#return acctType>
</#function>

<#function buildEntityBillingAddress entity payment>
    <#assign address = "">
    <#if payment.custbody_eft_address_1?has_content >
        <#assign address = payment.custbody_eft_address_1 >
    <#elseif entity.billaddress1?has_content >
        <#assign address = entity.billaddress1 >
    <#elseif entity.shipaddress1?has_content >
        <#assign address = entity.shipaddress1 >
    <#elseif entity.address1?has_content >
        <#assign address = entity.address1 >
    </#if>
    <#return address>
</#function>

<#function buildEntityBillingAddress2 entity payment>
    <#assign address = "">
    <#if payment.custbody_eft_address_2?has_content && payment.custbody_eft_city?has_content>
        <#assign address = payment.custbody_eft_address_2 >
    <#elseif entity.billaddress2?has_content >
        <#assign address = entity.billaddress2 >
    <#elseif entity.shipaddress1?has_content >
        <#assign address = entity.shipaddress2 >
    <#elseif entity.address1?has_content >
        <#assign address = entity.address2 >
    </#if>
    <#return address>
</#function>

<#function buildEntityBillingCity entity payment>
	<#assign address = "">
    <#if payment.custbody_eft_city?has_content >
        <#assign address = payment.custbody_eft_city >
	<#elseif entity.billcity?has_content >
		<#assign address = entity.billcity >
	<#elseif entity.shipcity?has_content >
		<#assign address = entity.shipcity >
	<#elseif entity.city?has_content >
		<#assign address = entity.city >
	</#if>
	<#return address>
</#function>

<#function buildEntityBillingState entity payment>
	<#assign address = "">
	<#if payment.custbody_eft_state?has_content >
        <#assign address = payment.custbody_eft_state >
	<#elseif entity.billstate?has_content >
		<#assign address = entity.billstate >
	<#elseif entity.shipstate?has_content >
		<#assign address = entity.shipstate >
	<#elseif entity.state?has_content >
		<#assign address = entity.state>
	</#if>
	<#return address>
</#function>

<#function buildEntityBillingZip entity payment>
	<#assign address = "" > 
	<#if payment.custbody_eft_zip?has_content >
        <#assign address = payment.custbody_eft_zip >
	<#elseif entity.billzipcode?has_content >
		<#assign address = entity.billzipcode?replace("-","") >
	<#elseif entity.shipzip?has_content >
		<#assign address = entity.shipzip?replace("-","") >
	<#elseif entity.zipcode?has_content >
		<#assign address = entity.zipcode?replace("-","") >
	</#if>
		<#return address>
</#function>

<#function buildEntityBillingName entity payment>
	<#assign address = buildEntityName(entity, false)> 
	<#if payment.custbody_eft_addressee?has_content >
        <#assign address = payment.custbody_eft_addressee >
	</#if>
	<#if payment.custbody_eft_name?has_content >
        <#assign address = payment.custbody_eft_name >
	</#if>
   <#return address>
</#function>

<#function getReferenceAmount payment accountType transaction> 
<#assign tranAmount = 0 > 
<#if payment.currency?has_content>
<#if accountType == "Bank">
<#assign tranAmount = transaction.appliedtoforeignamount>
<#else>
<#assign tranAmount = transaction.applyingforeignamount>
</#if>
<#else>
<#if accountType == "Bank">
<#assign tranAmount = transaction.appliedtolinkamount>
<#else>
<#assign tranAmount = transaction.applyinglinkamount>
</#if>
</#if> 
<#if (tranAmount < 0)>
<#assign tranAmount = tranAmount * -1>
</#if>
<#return tranAmount>
</#function>



<#function buildEntityBillingCountry entity>
	<#assign address = "USA" > 
	<#if entity.billcountry?has_content >
		<#assign address = getCountryCode(entity.billcountry) >
	<#elseif entity.shipcountry?has_content >
		<#assign address = getCountryCode(entity.shipcountry) >
	<#elseif entity.country?has_content >
		<#assign address = getCountryCode(entity.country) >
	</#if>
		<#return address>
</#function>

<#function getAggregationLineCount payment>
<#assign subTrans = transHash[payment.internalid]>
<#return subTrans?size>
</#function>

<#function getAggregationLineCountCheck payment>
<#assign subTrans = transHash[payment.internalid]>
<#if payment.custbody_capture_bills_and_credits?has_content>
<#assign lineCount = 0>
<#assign transCredit = payment.custbody_capture_bills_and_credits?eval />
<#list transCredit as transCreditLine>
<#assign lineCount = lineCount + 1>
</#list>
<#return lineCount>
<#else>
<#return subTrans?size>
</#if>
</#function>



<#-- cached values -->
<#assign totalACHCount = 0>
<#assign totalPaymentCount = 0>
<#assign totalCheckCount = 0>
<#assign totalWireCount = 0>
<#assign totalACHAmount = 0>
<#assign totalCheckAmount = 0>
<#assign totalWireAmount = 0>
<#assign totalPaymentAmount = 0>

${setPadding(customrecord_line.col3,"left","0",18)}
${setLength(" ",70)}

<#-- template building -->
#OUTPUT START#
010${setPadding(cbank.custpage_eft_custrecord_2663_bank_code,"right"," ",15)}AP0${setLength(cbank.custrecord_2663_legal_name,16)}${setPadding(pfa.custrecord_2663_file_creation_timestamp?string("yyyyMMdd"),"left"," ",8)}${setPadding(pfa.custrecord_2663_file_creation_timestamp?string("HHMM"),"left"," ",4)}${setLength(" ",10)}${setLength(" ",291)}
<#assign counter = 0>
<#list payments as payment>
<#assign ebank = ebanks[payment_index]>
<#assign entity = entities[payment_index]>
<#if ebank.custrecord_payment_method == "ACH - CTX">
<#assign totalACHCount = totalACHCount + 1>
<#assign totalPaymentCount = totalPaymentCount + 1>
<#assign totalACHAmount = totalACHAmount + getAmount(payment)>
<#assign totalPaymentAmount = totalPaymentAmount + getAmount(payment)>
060ACH${setLength(" ",1)}${setLength(entity.entityid,10)}${setLength(" ",4)}USD${setPadding(payment.tranid?replace("/",""),"left","0",10)}${pfa.custrecord_2663_process_date?string("yyyyMMdd")}000${setPadding(formatAmount(getAmount(payment),"noDec"),"left","0",10)}${setLength(buildEntityName(entity, false)?replace(","," "),22)}${setLength(" ",13)}${setLength(" ",15)}${setLength(" ",20)}${setLength(entity.billaddress1,35)}${setLength(entity.billaddress2,35)}${setLength(" ",35)}${setLength(entity.billcity,27)}${setLength(entity.billstate,2)}${setLength(entity.billzipcode,9)}${setLength(" ",4)}${setPadding(getAggregationLineCountCheck(payment),"left","0",5)}${setLength(" ",10)}${setPadding(ebank.custrecord_2663_entity_bank_no,"left","0",9)}${setLength(" ",3)}${setLength(ebank.custrecord_2663_entity_acct_no,17)}22CTX${setLength(" ",20)}${setLength(" ",9)}
07001${setLength(entity.entityid,10)}${setLength(" ",30)}${setLength(" ",1)}${setLength(" ",20)}${setLength(" ",284)}
<#if payment.custbody_capture_bills_and_credits?has_content>
<#assign transCredit = payment.custbody_capture_bills_and_credits?eval />
<#list transCredit as transCreditLine>
<#if transCreditLine.col0 == "CINV">
07006${setLength(transCreditLine.col2?replace("-",""),8)}${setLength(transCreditLine.col1,20)}${setLength(" ",30)}${setPadding(transCreditLine.col3?replace(".",""),"left","0",13)}*************${setPadding(transCreditLine.col3?replace(".",""),"left","0",13)}${setLength(" ",248)}
<#else>
07006${setLength(transCreditLine.col2?replace("-",""),8)}${setLength(transCreditLine.col1,20)}${setLength(" ",30)}-${setPadding(transCreditLine.col3?replace(".","")?replace("-",""),"left","0",12)}*************-${setPadding(transCreditLine.col3?replace(".","")?replace("-",""),"left","0",12)}${setLength(" ",248)}
</#if>
</#list>
</#if>
07009${setLength("0",26)}${setLength(" ",319)}
<#elseif ebank.custrecord_payment_method == "E-Check">
<#assign totalCheckCount = totalCheckCount + 1>
<#assign totalPaymentCount = totalPaymentCount + 1>
<#assign totalCheckAmount = totalCheckAmount + getAmount(payment)>
<#assign totalPaymentAmount = totalPaymentAmount + getAmount(payment)>
060CHK${setLength("X",1)}${setLength(entity.entityid,10)}${setLength(cbank.custpage_eft_custrecord_mail_method,2)}${cbank.custpage_eft_custrecord_pnc_address_code}USD${setPadding(payment.tranid?replace("/",""),"left","0",10)}${pfa.custrecord_2663_process_date?string("yyyyMMdd")}${setPadding(formatAmount(getAmount(payment),"noDec"),"left","0",13)}${setLength(buildEntityBillingName(entity, payment)?replace(","," "),70)}${setLength(buildEntityBillingAddress(entity payment),35)}${setLength(buildEntityBillingAddress2(entity payment),35)}${setLength(" ",35)}${setLength(buildEntityBillingCity(entity payment),27)}${setLength(buildEntityBillingState(entity payment),2)}${setLength(buildEntityBillingZip(entity payment),9)}${setLength(" ",1)}${setLength(buildEntityBillingCountry(entity),3)}${setPadding(getAggregationLineCountCheck(payment),"left","0",5)}${setLength(" ",10)}${setLength(" ",50)}${setLength(" ",13)}
07001${setLength(entity.entityid,10)}${setLength(" ",30)}${setLength(" ",1)}${setLength(" ",20)}${setLength(" ",284)}
<#if payment.custbody_capture_bills_and_credits?has_content>
<#assign transCredit = payment.custbody_capture_bills_and_credits?eval />
<#list transCredit as transCreditLine>
<#if transCreditLine.col0 == "CINV">
07006${setLength(transCreditLine.col2?replace("-",""),8)}${setLength(transCreditLine.col1,20)}${setLength(" ",30)}${setPadding(transCreditLine.col3?replace(".",""),"left","0",13)}*************${setPadding(transCreditLine.col3?replace(".",""),"left","0",13)}${setLength(" ",248)}
<#else>
07006${setLength(transCreditLine.col2?replace("-",""),8)}${setLength(transCreditLine.col1,20)}${setLength(" ",30)}-${setPadding(transCreditLine.col3?replace(".","")?replace("-",""),"left","0",12)}*************-${setPadding(transCreditLine.col3?replace(".","")?replace("-",""),"left","0",12)}${setLength(" ",248)}
</#if>
</#list>
</#if>
07009${setLength("0",26)}${setLength(" ",319)}
<#elseif ebank.custrecord_payment_method == "Wire">
<#assign totalWireCount = totalWireCount + 1>
<#assign totalPaymentCount = totalPaymentCount + 1>
<#assign totalWireAmount = totalWireAmount + getAmount(payment)>
<#assign totalPaymentAmount = totalPaymentAmount + getAmount(payment)>
060FWT${setLength(" ",15)}USD${setLength(" ",10)}${pfa.custrecord_2663_process_date?string("yyyyMMdd")}${setPadding(formatAmount(getAmount(payment),"noDec"),"left","0",13)}${setLength(buildEntityName(entity, false)?replace(","," "),35)}${setLength(" ",35)}${setLength(entity.billaddress1,35)}${setLength(entity.billaddress2,35)}${setLength(" ",35)}${setLength(" ",1)}${setLength(" ",20)}${setLength(" ",21)}00001${setLength(ebank.custrecord_2663_entity_bank_no,12)}${setLength(ebank.custrecord_2663_entity_acct_no,17)}A${setLength(" ",3)}D${setLength(" ",8)}${setPadding(payment.tranid?replace("/",""),"left","0",30)}${setLength(" ",1)}
080${setLength(" ",35)}${setLength(" ",35)}${setLength(" ",35)}${setLength(" ",35)}${setLength(" ",16)}${setLength(" ",3)}${setLength(" ",35)}${setLength(" ",153)}
085${setLength(" ",35)}${setLength(" ",35)}${setLength(" ",35)}${setLength(" ",35)}${setLength(" ",3)}${setLength(" ",12)}${setLength(ebank.custrecord_2663_entity_bank_name,35)}${setLength(" ",35)}${setLength(" ",35)}${setLength(" ",35)}${setLength(" ",52)}
</#if>
</#list>
090${setPadding(formatAmount(totalCheckAmount,"noDec"),"left","0",13)}${setPadding(totalCheckCount,"left","0",7)}${setPadding(formatAmount(totalACHAmount,"noDec"),"left","0",13)}${setPadding(totalACHCount,"left","0",7)}${setPadding(formatAmount(totalWireAmount,"noDec"),"left","0",13)}${setPadding(totalWireCount,"left","0",7)}*************0000000${setPadding(formatAmount(totalPaymentAmount,"noDec"),"left","0",13)}${setPadding(totalPaymentCount,"left","0",7)}${setPadding(formatAmount(totalPaymentAmount,"noDec"),"left","0",13)}${setPadding(totalPaymentCount,"left","0",7)}${setLength(" ",227)}
#OUTPUT END#