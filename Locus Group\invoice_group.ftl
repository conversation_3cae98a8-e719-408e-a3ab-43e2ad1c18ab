<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>

        <#assign addtl_data=record.custpage_addtl_data?eval />

        <macrolist>
            <macro id="nlheader">

                <table class="header" style="width:100%;">
                    <tr>
                        <td style="width:50%">
                            <table align="left" border="0" cellpadding="1" cellspacing="1" style="width:300px;">
                                <tr>
                                    <td colspan="5" style="text-align: left; display: block;">
                                        <div style="width:100%;text-align:left;">
                                            <#if addtl_data.subsidiary.pagelogo.value?has_content>
                                                <img src="${addtl_data.subsidiary.pagelogo.value}" />
                                            </#if>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td style="width:30%">
                            <table align="center" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td style="text-align: left;"><span class="comp-font"><#if addtl_data.subsidiary.addressee.text?has_content && addtl_data.subsidiary.addrtext.text?has_content>${addtl_data.subsidiary.addressee.value}</span><br /><span>${addtl_data.subsidiary.addrtext.text}</span></#if></td>
                                </tr>
                                <tr>
                                    <td height="5"> </td>
                                </tr>
                                <tr>
                                    <td style="font-size:11px;">Tax ID: <#if
                                            addtl_data.subsidiary.federalidnumber.text?has_content>
                                            ${addtl_data.subsidiary.federalidnumber.text}</#if>
                                    </td>
                                </tr>
                            </table>
                        </td>

                    </tr>
                    <tr>
                        <td>
                            <hr />
                        </td>
                        <td>
                            <hr />
                        </td>
                    </tr>
                </table>
            </macro>

            <macro id="nlfooter">
                <hr />

                <table class="footer" style="width: 100%;">
                    <tr>
                        <td>Email: <#if addtl_data.subsidiary.email.value?has_content>
                                <u>${addtl_data.subsidiary.email.value}</u> </#if>
                        </td>
                        <td>Tel: <#if addtl_data.subsidiary.phone.value?has_content>
                                <u>${addtl_data.subsidiary.url.value}</u> </#if>
                        </td>
                        <td align="right">
                            <#if addtl_data.subsidiary.url.value?has_content><u>${addtl_data.subsidiary.url.value}</u>
                            </#if>
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>

        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            span.comp-font {
                color: #183ead;
                font-size: 16px;
            }
            table {
                font-size: 10pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #e3e3e3;
                color: #333333;
            }

            td {
                padding: 4px 6px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0px;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #e3e3e3;
                line-height: 200%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #e3e3e3;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0px;
            }

            td.totalbox {
                border: 1px;
            }

            td.items th.items {
                border: 1px;
            }

            td.totalboxbot {
                background-color: #e3e3e3;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" footer="nlfooter" header-height="13%" footer-height="27pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 55%;vertical-align: top;">
                        <table style="width: 100%;" cellpadding="0" cellspacing="2">
                            <tr>
                                <td><b>${record.billaddress@label}:</b></td>
                            </tr>
                            <tr>
                                <td style="line-height:12">${record.billaddress}</td>
                            </tr>
                            <#if record.vatregnum?has_content && (subsidiary.country!='Israel' ||
                                record.billcountry=='IL' )>
                                <tr>
                                    <td>${record.vatregnum}</td>
                                </tr>
                            </#if>
                        </table>
                    </td>
                    <td style="width: 45%;">
                        <table style="break-inside: avoid; margin-top: 10px; width: 100%;">
                            <tr style="">
                                <td align="left"
                                    style="background-color: #d3d3d3; font-weight: bold; color: rgb(51, 51, 51); border-width: 1px; border-style: solid; border-color: initial;">
                                    ${record.amountpaid@label}</td>
                                <td align="right"
                                    style="border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid; width: 161px;">
                                    ${record.amountpaid}</td>
                            </tr>
                            <tr style="">
                                <td align="left"
                                    style="background-color: #d3d3d3; font-weight: bold; color: rgb(51, 51, 51); border-left: 1px solid; border-bottom: 1px solid; border-right: 1px solid;">
                                    ${record.amountdue@label}</td>
                                <td align="right"
                                    style="border-bottom: 1px solid; border-right: 1px solid; width: 161px;">
                                    ${record.amountdue}</td>
                            </tr>
                            <tr style=" ">
                                <td align="left"
                                    style="background-color: #d3d3d3; font-weight: bold; color: rgb(51, 51, 51); border-left: 1px solid; border-bottom: 1px solid; border-right: 1px solid;">
                                    ${record.total@label}</td>
                                <td align="right"
                                    style="border-bottom: 1px solid; border-right: 1px solid; width: 161px;">
                                    ${record.total}</td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            &nbsp;

            <table style="break-inside: avoid; margin-top: 10px; width: 677px;">
                <tr>
                    <td align="left"><strong>Primary Information</strong></td>
                </tr>
            </table>

            <table class="body" style="width: 100%; margin-top: 10px;">
                <tr style="background-color: #d3d3d3; ">
                    <th align="center" style="font-weight: bold; border-left: 1px solid; border-top: 1px solid;border-bottom: 1px solid; border-right: 1px solid" width="150pt">Account #</th>
                    <th align="center" style="font-weight: bold; border-top: 1px solid;border-bottom: 1px solid; border-right: 1px solid" width="150pt">${record.trandate@label}</th>
                    <th align="center" style="font-weight: bold; border-top: 1px solid;border-bottom: 1px solid; border-right: 1px solid" width="150pt">${record.terms@label}</th>
                    <th align="center" style="font-weight: bold; border-top: 1px solid;border-bottom: 1px solid; border-right: 1px solid" width="150pt">${record.duedate@label}</th>
                </tr>
                <#assign customer=record.customer?split(" ") />
                <tr>
                    <td align="center" style="border-left: 1px solid;border-bottom: 1px solid; border-right: 1px solid">${customer[0]}</td>
                    <td align="center" style="border-bottom: 1px solid; border-right: 1px solid">${record.trandate}</td>
                    <td align="center" style="border-bottom: 1px solid; border-right: 1px solid">${record.terms}</td>
                    <td align="center" style="border-bottom: 1px solid; border-right: 1px solid">${record.duedate}</td>
                </tr>
            </table>

            <#if groupedinvoices_summary?has_content>

                <table style=" break-inside: avoid; margin-top: 10px; width: 677px;">
                    <tr>
                        <td align="left"><strong>Invoice Group Summary</strong></td>
                    </tr>
                </table>

                <table style="width: 100%; margin-top: 10px;">
                    <#list groupedinvoices_summary as invoice_summary>
                        <#assign invoice_num=invoice_summary.invoicenum?replace("Invoice #", "")>
                        <#list addtl_data.invoices as invoice>
                            <#if invoice.invoice_number==invoice_num>
                                <#assign due_date=invoice.due_date>
                            </#if>
                        </#list>
                        <#if invoice_summary_index==0>
                            <thead>
                                <tr style="background-color: #d3d3d3;">
                                    <td align="center" colspan="4" style="font-weight: bold;border-left: 1px solid; border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid ">${invoice_summary.invoicenum@label}</td>
                                    <td align="center" colspan="3" style="font-weight: bold;border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid">${invoice_summary.trandate@label}</td>
                                    <td align="center" colspan="3" style="font-weight: bold;border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid">Due Date</td>
                                    <td align="center" colspan="3" style="font-weight: bold;border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid">${invoice_summary.fxamount@label}</td>
                                </tr>
                            </thead>
                        </#if>
                        <tr>
                            <td align="center" colspan="4" style="color: #333333;border-left: 1px solid; border-bottom: 1px solid; border-right: 1px solid">${invoice_num}</td>
                            <td align="center" colspan="3" style="border-bottom: 1px solid; border-right: 1px solid">${invoice_summary.trandate}</td>
                            <td align="center" colspan="3" style="border-bottom: 1px solid; border-right: 1px solid">${due_date}</td>
                            <td align="right" colspan="3" style="border-bottom: 1px solid; border-right: 1px solid">${invoice_summary.fxamount}</td>
                        </tr>
                    </#list>
                </table>
            </#if>

            <pbr header="nlheader" header-height="13%" footer="nlfooter" footer-height="27pt"
                padding="0.5in 0.5in 0.5in 0.5in" size="letter" />

            <#if groupedinvoices_detailed?has_content>

                <table style="break-inside: avoid; margin-top: 10px; width: 647px;">
                    <tr>
                        <td align="left"><strong>Invoice Group Detail</strong></td>
                    </tr>
                </table>

                <table style="width=100%; margin-top: 10px;margin-bottom: 0px; ">
                    <#list groupedinvoices_detailed as invoice_details>
                        <#if invoice_details_index==0>
                            <thead>
                                <tr style="background-color: #d3d3d3; ">
                                    <th border="1" align="center" style="font-weight: bold; border-left: 1px solid; border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid" width="105pt">${invoice_details.invoicenum@label}</th>
                                    <th border="1" align="center" style=" font-weight: bold; border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid" width="70pt">Seq Num</th>
                                    <th border="1" align="center" style=" font-weight: bold; border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid" width="175pt">${invoice_details.item@label}</th>
                                    <th border="1" align="center" style=" font-weight: bold; border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid" width="75pt">${invoice_details.fxrate@label}</th>
                                    <th border="1" align="center" style=" font-weight: bold; border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid" width="60pt">Qty</th>
                                    <th border="1" align="center" style=" font-weight: bold; border-top: 1px solid; border-bottom: 1px solid; border-right: 1px solid" width="80pt">${invoice_details.fxgrossamount@label}</th>
                                </tr>
                            </thead>
                        </#if>
                        <#if invoice_details.linesequencenumber!=0>
                            <#assign istax=(invoice_details.itemtype=="TaxItem" )||(invoice_details.itemtype=="TaxGroup")>
                            <#assign ispercent=(invoice_details.itemtype=="Discount" )||istax>
                            <#assign noquantity=(invoice_details.itemtype=="ShipItem" )||istax>
                            <#assign notsubdesc=(invoice_details.itemtype!="ShipItem" ) && (invoice_details.itemtype!="Description")&&(invoice_details.itemtype!="Subtotal" )>
                            <#assign due_date="">
                            <#if invoice_details.linesequencenumber==1>
                                <#assign invoice_num=invoice_details.invoicenum?replace("Invoice #", "")>
                            <#else>
                                <#assign invoice_num="">
                            </#if>
                            <tr>
                                <td style="border-left: 1px solid; border-bottom: 1px solid; border-right: 1px solid" align="center">${invoice_num}</td>
                                <td style="border-left: 1px solid; border-bottom: 1px solid; border-right: 1px solid" align="center">${invoice_details.linesequencenumber}</td>
                                <td style="border-left: 1px solid; border-bottom: 1px solid; border-right: 1px solid" align="left">${invoice_details.item}</td>
                                <td style="border-left: 1px solid; border-bottom: 1px solid; border-right: 1px solid" align="right">${invoice_details.fxrate}</td>
                                <td style="border-left: 1px solid; border-bottom: 1px solid; border-right: 1px solid" align="center"><#if !noquantity>${invoice_details.quantity}</#if></td>
                                <td style="border-left: 1px solid; border-bottom: 1px solid; border-right: 1px solid" align="right">${invoice_details.fxgrossamount}</td>
                            </tr>
                        </#if>
                    </#list>
                </table>
            </#if>
            <br />
            <table width="100%">
                <tr>
                    <td width="120pt">&nbsp;</td>
                    <td width="70pt">&nbsp;</td>
                    <td width="130pt">&nbsp;</td>
                    <td width="75pt">&nbsp;</td>
                    <td class="totalbox" align="left" style="background-color: #d3d3d3; font-weight: bold; color: rgb(51, 51, 51);border-bottom: 1px solid; border-right: 1px solid;" width="90pt">
                        ${record.taxtotal@label}</td>
                    <td class="totalbox" align="right" style="border-bottom: 1px solid; border-right: 1px solid;" width="80pt">
                        <b>${record.taxtotal}</b></td>
                </tr>
                <#if record.shippingcost?has_content>
                    <tr>
                    <td width="120pt">&nbsp;</td>
                    <td width="70pt">&nbsp;</td>
                    <td width="130pt">&nbsp;</td>
                    <td width="75pt">&nbsp;</td>
                        <td class="totalbox" align="left" style="background-color: #d3d3d3; font-weight: bold; color: rgb(51, 51, 51);border-bottom: 1px solid; border-right: 1px solid;" width="90pt">
                            ${record.shippingcost@label}</td>
                        <td class="totalbox" align="right" style="border-bottom: 1px solid; border-right: 1px solid;" width="80pt">
                            <b>${record.shippingcost}</b></td>
                    </tr>
                </#if>
                <#if record.handlingcost?has_content>
                    <tr>
                    <td width="120pt">&nbsp;</td>
                    <td width="70pt">&nbsp;</td>
                    <td width="130pt">&nbsp;</td>
                    <td width="75pt">&nbsp;</td>
                        <td class="totalbox" align="left"
                            style="background-color: #d3d3d3; font-weight: bold; color: rgb(51, 51, 51);border-bottom: 1px solid; border-right: 1px solid;" width="90pt">
                            ${record.handlingcost@label}</td>
                        <td class="totalbox" align="right" style="border-bottom: 1px solid; border-right: 1px solid;" width="80pt">
                            <b>${record.handlingcost}</b></td>
                    </tr>
                </#if>
                <tr>
                    <td width="120pt">&nbsp;</td>
                    <td width="70pt">&nbsp;</td>
                    <td width="130pt">&nbsp;</td>
                    <td width="75pt">&nbsp;</td>
                    <td class="totalbox" align="left"
                        style="background-color: #d3d3d3; font-weight: bold; color: rgb(51, 51, 51);border-bottom: 1px solid; border-right: 1px solid;" width="90pt">
                        ${record.total@label}</td>
                    <td class="totalbox" align="right" style="border-bottom: 1px solid; border-right: 1px solid;" width="80pt">
                        <b>${record.total}</b></td>
                </tr>
            </table>

            <br />
            <br />
            <table width="65%" style="font-size: 9pt">
                <tr>
                    <td>Invoice payment terms: ${record.terms}. Interest accrued at 1.5% per month there after.
                    <br />
                    <br />
                    <b>Pay by Check:</b><br />
                    LocusView Solutions Inc.; P.O. Box ********; Chicago, IL 60674-8871
                    <br />
                    <br />
                    <b>Pay by ACH:</b><br />
                    Bank of America, N.A.: 135 S. LaSalle St.; Chicago, IL, 60603
                    ACH Routing#: *********; Acct# ************
                    <br />
                    <br />
                    <b>Pay by Wire Transfer</b><br />
                    Bank of America, N.A.: 135 S. LaSalle St.; Chicago, IL, 60603
                    Wire Routing#: *********; Acct# ************</td>
                </tr>
            </table>
    </body>
</pdf>