/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function afterSubmit(context) {
        if(context.Type == 'specialorder'){
            
            var reloadRecObj = record.load({
                type: record.Type.PURCHASE_ORDER,
                id: context.newRecord.id,
                isDynamic: true
            });

            reloadRecObj.setValue({ fieldId: 'memo', value: 'test1' });
            log.debug(reloadRecObj.getValue({ fieldId: 'memo' }));

            reloadRecObj.save();
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
