/**
 *@NApiVersion 2.x
 *@NScriptType MapReduceScript
 */
define(['N/search', 'N/render', 'N/record', 'N/email', 'N/format'], function(search, render, record, email, format) {

    function getInputData() {
        var mySearch = search.load({
            id: 'customsearch_cust_overdue_inv'
        });

        return mySearch;
    }
    function map(context) {
        
        // accept data from previous stage
        var keyFromInput = context.key;
        var valueFromInput = context.value;
        var recordObj = JSON.parse(valueFromInput);
        try {
            context.write({
                key: recordObj.values.entity.value,
                value: {
                    type: recordObj.values.type.text,
                    trandate: recordObj.values.trandate,
                    tranid: recordObj.values.tranid,
                    entity: recordObj.values.entity.text,
                    duedate: recordObj.values.duedate,
                    otherrefnum: recordObj.values.otherrefnum,
                    createdfrom: recordObj.values.createdfrom.text,
                    amount: parseFloat(recordObj.values.amount),
                    amountpaid: parseFloat(recordObj.values.amountpaid),
                    amountremaining: parseFloat(recordObj.values.amountremaining)
                }
            });                    
        } catch (errorObj) {
            log.debug({
                title: 'map 1',
                details: errorObj
            });
        }
    }
    
    function reduce(context) {

        try {
            var reduceKey = context.key;
            var reduceValues = context.values;
            
            var customerObj = record.load({
                type: record.Type.CUSTOMER,
                id: reduceKey
            });

            var customerAddress = customerObj.getValue({ fieldId: 'defaultaddress' });              

            // create PDF
            var renderer = render.create();
            
            // select template to be used via script ID
            renderer.setTemplateByScriptId({
                scriptId: "CUSTTMPL_ACS_STATEMENT_PDF"
            });

            // prepare invoice data to be included in PDF
            var invoices = [];
            var totalInvoicesAmt = 0.00;
            var aging = {
                one: 0.00,
                two: 0.00,
                three: 0.00,
                four: 0.00,
                five: 0.00
            };
            var running_bal = 0.00;
            for(x = 0; x < reduceValues.length; x++){
                var parsedValue = JSON.parse(reduceValues[x]);
                var parsedAmountRemaining = parseFloat(parsedValue.amountremaining);
                totalInvoicesAmt = totalInvoicesAmt + parsedAmountRemaining;
                parsedValue.runningbal = totalInvoicesAmt;
                invoices.push(parsedValue);
                
                var tranDateObj = new Date(parsedValue.trandate);
                var currDate = new Date();
                var one_day=1000*60*60*24;
                var dateDiff = currDate - tranDateObj;
                var age = Math.floor(dateDiff/one_day);
                if(age == 0) {
                    aging.one = aging.one + parsedAmountRemaining;
                } else if (age >= 1 && age <= 30) {
                    aging.two = aging.two + parsedAmountRemaining;
                } else if (age >= 31 && age <= 60) {
                    aging.three = aging.three + parsedAmountRemaining;
                } else if (age >= 61 && age <= 90) {
                    aging.four = aging.four + parsedAmountRemaining;
                } else if (age >= 91) {
                    aging.five = aging.five + parsedAmountRemaining;
                }
            }

            var formattedDateNow = format.format({ value: currDate, type: format.Type.MMYYDATE });
            formattedDateNow = formattedDateNow.replace('/', '_');

            var custObj = {
                invoices: invoices,
                aging: aging,
                total_amt: totalInvoicesAmt,
                cust_address: customerAddress
            };
            
            renderer.addCustomDataSource({
                format: render.DataSource.OBJECT,
                alias: "cust_invoices",
                data: custObj
            });

            renderer.addRecord({
                templateName: 'record',
                record: customerObj
            });

            var statementPDF = renderer.renderAsPdf();

            statementPDF.name = formattedDateNow + '_statement.pdf';

            // invoke email template to be used
            var mergeResult = render.mergeEmail({
                templateId: 6,
                entity: {
                    type: 'customer',
                    id: customerObj.id
                },
                recipient: null,
                supportCaseId: null,
                transactionId: null,
                customRecord: null
            });

            var emailSubject = mergeResult.subject;
            var emailBody = mergeResult.body;
            // CHANGE BASED ON WHAT CUSTOMER WILL SAY
            email.send({
                author: 536810,
                recipients: '<EMAIL>',
                subject: emailSubject,
                body: emailBody,
                attachments: [statementPDF]
            });
        } catch (errorObj) {
            log.debug({
                title: 'reduce 1',
                details: errorObj
            });
        }
        
    }
    
    function summarize(context){

    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    }
});

require(['N/record'], function(record) {

    var x = record.load({
        type: record.Type.CUSTOMER,
        id: 9240,
        isDynamic: true
    });

    var aging = x.getField({ fieldId: 'aging' });
    
});
