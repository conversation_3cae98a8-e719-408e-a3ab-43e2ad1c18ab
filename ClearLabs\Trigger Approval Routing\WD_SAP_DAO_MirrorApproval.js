/**
 * Copyright (c) 2017, 2020, Oracle and/or its affiliates.
 *
 * Version    Date            Author           Remarks
 * 1.00       10 July 2017    mbalmeo
 *
 * @NApiVersion 2.x
 */

define(['../lib/WD_SAP_SearchWrapper', './WD_SAP_DAO_Base'],

function(sapSearch, BaseDAO) {
    function MirrorApprovalDAO() {
        BaseDAO.call(this);
        this.name = 'MirrorApprovalDAO';
        this.recordType = 'customrecord_sas_mirror_approval';
        this.filters = [];
        this.columns = [];
        this.searchObject = null;
    }

    util.extend(MirrorApprovalDAO.prototype, BaseDAO.prototype);

    MirrorApprovalDAO.Fields = {
        INTERNAL_ID: 'internalid',
        REF_RECORD_SCRIPT_ID: 'custrecord_sas_mar_ref_record_scriptid',
        REF_RECORD_INTERNAL_ID: 'custrecord_sas_mar_ref_record_internalid',
        APPROVAL_STATUS: 'custrecord_sas_mar_approval_status',
        NEXT_APPROVER: 'custrecord_sas_mar_next_approver',
        NEXT_APPROVERS_ID: 'custrecord_sas_mar_next_approvers_id',
        REJECT_REASON: 'custrecord_sas_mar_reject_reason',
        REJECT_REASON_CODE: 'custrecord_sas_mar_reject_reason_code',
        APPROVAL_RULE: 'custrecord_sas_mar_approval_rule',
        APPROVAL_RULE_RECORD_NAME: {
            name: 'custrecord_sas_ar_record_name',
            join: 'custrecord_sas_mar_approval_rule'
        },
        CURRENT_APPROVAL_MATRIX: 'custrecord_sas_mar_curr_approval_matrix',
        PENDING_RULE_EVALUATION: 'custrecord_sas_mar_pending_rule_eval',
        RECORD_DATE: 'custrecord_sas_mar_record_date',
        DATE_CREATED: 'created',
        SUBMITTER: 'custrecord_sas_mar_submitter',
        PREFERENCES: 'custrecord_sas_mar_preferences',
        EXCEPTIONS: 'custrecord_sas_mar_rec_exceptions',
        EMAIL_APPROVAL_ID: 'custrecord_sas_mar_email_app_id',
        ACTION_OWNER: 'custrecord_sas_mar_action_owner',
        TARGET_STATE: 'custrecord_sas_mar_target_approval_state'
    };

    MirrorApprovalDAO.prototype.initialize = function(options) {
        const columns = [];

        util.each(MirrorApprovalDAO.Fields, function(fld, key) {
            if (key == 'REF_RECORD_INTERNAL_ID') {
                fld = sapSearch.createColumn({
                    name: fld,
                    sort: sapSearch.getSort().ASC
                });
            }
            columns.push(fld);
        });

        this.columns = columns;

        if (options && options.internalId) {
            this.filters.push({
                name: MirrorApprovalDAO.Fields.INTERNAL_ID,
                operator: sapSearch.getOperator().ANYOF,
                values: options.internalId
            });
        }

        if (options && options.excludeId && options.excludeId.length > 0) {
            this.filters.push({
                name: MirrorApprovalDAO.Fields.INTERNAL_ID,
                operator: sapSearch.getOperator().NONEOF,
                values: options.excludeId
            });
        }

        if (options && options.recordScriptId) {
            this.filters.push({
                name: MirrorApprovalDAO.Fields.REF_RECORD_SCRIPT_ID,
                operator: sapSearch.getOperator().IS,
                values: options.recordScriptId
            });
        }

        if (options && options.recordIntId) {
            this.filters.push({
                name: MirrorApprovalDAO.Fields.REF_RECORD_INTERNAL_ID,
                operator: sapSearch.getOperator().EQUALTO,
                values: options.recordIntId
            });
        }

        if (options && options.approvalRuleId) {
            this.filters.push({
                name: MirrorApprovalDAO.Fields.APPROVAL_RULE,
                operator: sapSearch.getOperator().ANYOF,
                values: options.approvalRuleId
            });
        }

        if (options && options.currrentApprovalMatrix) {
            this.filters.push({
                name: MirrorApprovalDAO.Fields.CURRENT_APPROVAL_MATRIX,
                operator: sapSearch.getOperator().ANYOF,
                values: options.currrentApprovalMatrix
            });
        }

        if (options && options.approvalStatus) {
            this.filters.push({
                name: MirrorApprovalDAO.Fields.APPROVAL_STATUS,
                operator: sapSearch.getOperator().ANYOF,
                values: options.approvalStatus
            });
        }

        if (options && options.mappingSetupIds && options.mappingSetupIds.length > 0) {
            this.filters.push({
                name: MirrorApprovalDAO.Fields.APPROVAL_RULE_RECORD_NAME.name,
                join: MirrorApprovalDAO.Fields.APPROVAL_RULE_RECORD_NAME.join,
                operator: sapSearch.getOperator().ANYOF,
                values: options.mappingSetupIds
            });
        }

        if (options && options.hasOwnProperty('isPendingRuleEvaluation')) {
            this.filters.push({
                name: MirrorApprovalDAO.Fields.PENDING_RULE_EVALUATION,
                operator: sapSearch.getOperator().IS,
                values: options.isPendingRuleEvaluation
            });
        }

        if (options && options.emailApprovalId) {
            this.filters.push({
                name: MirrorApprovalDAO.Fields.EMAIL_APPROVAL_ID,
                operator: sapSearch.getOperator().IS,
                values: options.emailApprovalId
            });
        }

        if (options && options.actionOwner) {
            this.filters.push({
                    name: MirrorApprovalDAO.Fields.ACTION_OWNER,
                    operator: sapSearch.getOperator().IS,
                    values: options.actionOwner
            });
        }
    };

    MirrorApprovalDAO.prototype.search = function() {
        this.searchObject = sapSearch.create({
            type: this.recordType,
            columns: this.columns,
            filters: this.filters
        });

        return this.searchObject.run();
    };

    MirrorApprovalDAO.prototype.rowToObject = function(row) {
        return {
            recordType: this.recordType,
            id: row.id,
            recordScriptId: row.getValue(MirrorApprovalDAO.Fields.REF_RECORD_SCRIPT_ID),
            recordIntId: row.getValue(MirrorApprovalDAO.Fields.REF_RECORD_INTERNAL_ID),
            approvalStatus: row.getValue(MirrorApprovalDAO.Fields.APPROVAL_STATUS),
            approvalStatusText: row.getText(MirrorApprovalDAO.Fields.APPROVAL_STATUS),
            nextApprover: row.getValue(MirrorApprovalDAO.Fields.NEXT_APPROVER),
            nextApproverText: row.getText(MirrorApprovalDAO.Fields.NEXT_APPROVER),
            nextApproversId: row.getValue(MirrorApprovalDAO.Fields.NEXT_APPROVERS_ID),
            rejectReason: row.getValue(MirrorApprovalDAO.Fields.REJECT_REASON),
            approvalRule: row.getValue(MirrorApprovalDAO.Fields.APPROVAL_RULE),
            approvalRuleRecordName: row.getValue(MirrorApprovalDAO.Fields.APPROVAL_RULE_RECORD_NAME),
            currentAppMatrix: row.getValue(MirrorApprovalDAO.Fields.CURRENT_APPROVAL_MATRIX),
            pendingRuleEvaluation: row.getValue(MirrorApprovalDAO.Fields.PENDING_RULE_EVALUATION),
            recordDate: row.getValue(MirrorApprovalDAO.Fields.RECORD_DATE),
            dateCreated: row.getValue(MirrorApprovalDAO.Fields.DATE_CREATED),
            submitter: row.getValue(MirrorApprovalDAO.Fields.SUBMITTER),
            submitterText: row.getText(MirrorApprovalDAO.Fields.SUBMITTER),
            preferences: row.getValue(MirrorApprovalDAO.Fields.PREFERENCES),
            exceptions: row.getValue(MirrorApprovalDAO.Fields.EXCEPTIONS),
            emailApprovalId: row.getValue(MirrorApprovalDAO.Fields.EMAIL_APPROVAL_ID),
            actionOwner: row.getValue(MirrorApprovalDAO.Fields.ACTION_OWNER)
        };
    };

    return MirrorApprovalDAO;
});
