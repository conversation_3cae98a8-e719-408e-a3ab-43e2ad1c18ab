/**
 * Order History Suitelet
 * 
 * Version    Date            Author           Remarks
 * 1.00       05 Sep 2019     <PERSON>   Initial Version
 *
 */

// var secretKey = 'ac8420dd-5069-4877-8d19-c017c12706b2';
var secretKey = 'c42bbaaf-d96a-4d0d-a30b-5704dae8986e';

/**
 * @param {nlobjRequest} request Request object
 * @param {nlobjResponse} response Response object
 * @returns {Void} Any output is written via response object
 */
function suitelet(request, response) {

    if (request.getMethod() == 'GET') {
        app.handleGet();
    } else {
        app.handlePost();
    }
}

var app = (function () {

    var FORM_NAME = 'Orders History', // Form Name
        LIST_NAME = 'Orders History', // List Name
        SUBMIT_BUTTON = 'Submit', // Submit button caption
        MAX_RECORDS = 1000, // Maximum number of records to display on the sublist
        MARKALL_ENABLED = false, // Mark all option enabled
        tranType;

    //var isAdmin = false;
    var hasPermissions;
    /**
     * Handles Suitelet GET Method
     * 
     * @returns {Void}
     */
    var handleGet = function () {

        var form,
            subList,
            searchResults;

        try {
            var userId = request.getParameter('userid');
            var hash = request.getParameter('hash');
            var customer = request.getParameter('custpage_customer');
            tranType = request.getParameter('custpage_type') ? request.getParameter('custpage_type') : 'SalesOrd';

            var hashCompare = String(CryptoJS.SHA256(secretKey + userId + customer));

            if (!userId || hash != hashCompare) {
                displayMessage('Invalid access credentials.');
                return;
            }
            if (!customer) {
                displayMessage('Invalid parameters.');
                return;
            }

            //hasPermissions = nlapiLookupField('contact', userId, 'custentity_files_slas') == 'T';

            form = nlapiCreateForm('', false);

            form.setScript('customscript_in8_orderhistory_cs');

            addFilters(form);

            addButtons(form);

            // Creates and inserts the sublist on the form
            subList = getSubList(form);

            // Gets the search results to be displayed
            searchResults = getSearchResults();

            // Populates the sublist based on the search results
            populateSubList(subList, searchResults);
            
            // Displays the page
            response.writePage(form);
        } catch (e) {
            displayMessage('An error occurred. ' + (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
        }
    };

    /**
     * Handles Suitelet POST method
     * 
     * @returns {Void} 
     */
    var handlePost = function () {

        for (var i = 1; i <= request.getLineItemCount('custpage_sublist'); i++) {
            if (request.getLineItemValue('custpage_sublist', 'custpage_selected', i) == 'T') {
                // TODO: Process the line
            }
        }

        // Reloads window
        handleGet();
    };

    /**
     * Add Buttons
     * 
     * @param {nlobjForm} form Object containing the form
     * @returns {Void} 
     */
    var addButtons = function (form) {

        if (nlapiGetContext().getCompany() != '4856065' && nlapiGetContext().getCompany() != '5077347') { // hygiene
            form.addButton('custombutton_search', 'Search', 'search()');
        }

        //form.addSubmitButton(SUBMIT_BUTTON);

        // if (isAdmin) {
        //     form.addButton('custombutton', 'Add Client Site', 'addClientSite(' + request.getParameter('custpage_customer') + ',\'' + request.getParameter('userid') + '\', \'' + request.getParameter('hash') + '\')');
        // }	    
    };

    /**
     * Add Filter fields to the Form
     * 
     * @param {nlobjForm} form Object containing the form
     * @returns {Void} 
     */
    var addFilters = function (form) {

        // if (!hasPermissions) {
        //     var field = form.addField('file', 'inlinehtml', 'label');
        //     field.setDefaultValue('<span style="color:red;font-size:12pt">You do not have the necessary permissions to View/Download. If you need Permissions, please contact the listed "Contact Administrator" for the Project <a href="https://in8sync.com/account/contact-administration/">HERE</a></span><br/><br/>');
        // }       

        // var field = form.addField('file1', 'inlinehtml', 'label');
        // field.setDefaultValue("<span style='font-size:12pt'>Credentials saved in this form are not saved on the website, they are directly saved in In8Syncs' NetSuite account only,  for security reasons.</span><br/>");
        // field.setLayoutType('outsidebelow','startrow');

        if (nlapiGetContext().getCompany() != '4856065' && nlapiGetContext().getCompany() != '5077347') { // hygiene
            var internalId = form.addField('custpage_tranid', 'text', 'Transaction Name', null);
            if (request.getParameter('custpage_tranid')) internalId.setDefaultValue(request.getParameter('custpage_tranid'));
        }
    };

    /**
     * Add a sublist to the Form
     * 
     * @param {nlobjForm} form Object containing the form
     * @returns {Void} 
     */
    var getSubList = function (form) {

        var deliveryStatusField = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_delivery_status_field');
        var deliveryConfirmField = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_delivery_confirm_field');

        //form.addFieldGroup('custpage_grp', 'Contacts');

        switch (tranType) {
            case 'SalesOrd':
                LIST_NAME = 'Sales Orders';
                break;
            case 'CustInvc':
                LIST_NAME = 'Invoices';
                break;
            case 'CustCred':
                LIST_NAME = 'Credit Memos';
                break;
            case 'Estimate':
                LIST_NAME = 'Estimates';
                break;
        }
        var subList = form.addSubList('custpage_sublist', 'list', LIST_NAME);
    
        subList.addField('custpage_trandate', 'text', 'Date');
        //subList.addField('custpage_edit', 'text', 'Edit');
        subList.addField('custpage_name', 'text', 'Transaction Name');
        //subList.addField('custpage_number', 'text', 'Transaction Number');
        //subList.addField('custpage_customer', 'text', 'Customer');
        if (!deliveryStatusField || tranType != 'SalesOrd') subList.addField('custpage_status', 'text', 'Status');

        if (tranType == 'SalesOrd' || tranType == 'CustInvc') {
            subList.addField('custpage_po_num', 'text', 'PO#');
        }

        subList.addField('custpage_amount', 'currency', 'Amount');

        if (tranType == 'CustInvc') {
            subList.addField('custpage_amount_remaining', 'currency', 'Amount Remaining');
        }

        subList.addField('custpage_view', 'textarea', 'View/Download');

        if (tranType == 'SalesOrd' && deliveryStatusField) {
            subList.addField('custpage_status', 'text', 'Delivery Status');
        }
        if (tranType == 'SalesOrd' && deliveryConfirmField) {
            subList.addField('custpage_delivery', 'text', 'Delivery Confirmation');
        }

        subList.addField('custpage_pay', 'textarea', '');

        if (tranType == 'SalesOrd') {
            subList.addField('custpage_packing', 'textarea', '');
        }
        //subList.addField('custpage_download', 'text', 'Download');        

        return subList;
    };

    /**
     * Populate the SubList
     * 
     * @param {nlobjSublist} list Object sublist
     * @param {nlobjSearchResults} searchResults Object search results
     * 
     * @returns {Void} 
     */
    var populateSubList = function (list, searchResults) {

        var deliveryConfirmField = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_delivery_confirm_field');
        var deliveryStatusField = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_delivery_status_field');
        var enablePrintPacking = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_enable_print_packing');
        var enableInvoicePayments = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_enable_invoice_payments');

        var searchLength = (searchResults ? searchResults.length : 0),
            i = 0;

        // Checks if needs to display only a number of records
        if (MAX_RECORDS) searchLength = searchLength > MAX_RECORDS ? MAX_RECORDS : searchLength;

        var url = nlapiResolveURL('SUITELET', 'customscript_in8_orderprint_sl', 'customdeploy_in8_orderprint_sl_dep', 'external');

        for (i = 0; i < searchLength; i++) {

            //list.setLineItemValue('custpage_edit', i + 1, '<a href="https://4130581.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=212&deploy=1&compid=4130581&h=93ef2e6458a64f56b163&custpage_case_id=' + searchResults[i].getValue('internalid') + '&custpage_company=' + searchResults[i].getValue('company') + '&userid=' + request.getParameter('userid') + '&hash=' + request.getParameter('hash') + '">Edit</a>');
            list.setLineItemValue('custpage_trandate', i + 1, searchResults[i].getValue('trandate'));
            list.setLineItemValue('custpage_name', i + 1, searchResults[i].getValue('transactionname'));
            //list.setLineItemValue('custpage_number', i + 1, searchResults[i].getValue('transactionnumber'));

            var fulfillData = [];

            if (tranType == 'SalesOrd' && (deliveryStatusField || deliveryConfirmField)) {
                fulfillData = getTrackingStatus(deliveryStatusField, deliveryConfirmField, searchResults[i].getValue('internalid'));
            }
            if (tranType == 'SalesOrd' && deliveryStatusField) {
                var trackingData = '';
                var statuses = [];
                for (var j = 0; j < fulfillData.length; j++) {
                    if (fulfillData[j].getValue(deliveryStatusField) && statuses.indexOf(fulfillData[j].getValue(deliveryStatusField)) == -1) {
                        statuses.push(fulfillData[j].getValue(deliveryStatusField));
                    }
                }
                trackingData = statuses.join('<br/>').substr(0, 300);

                list.setLineItemValue('custpage_status', i + 1, trackingData);
            } else {
                list.setLineItemValue('custpage_status', i + 1, searchResults[i].getText('status'));
            }

            list.setLineItemValue('custpage_amount', i + 1, searchResults[i].getValue('amount'));

            if (tranType == 'CustInvc') {
                list.setLineItemValue('custpage_amount_remaining', i + 1, searchResults[i].getValue('amountremaining'));
            }
            if (tranType == 'CustInvc' || tranType == 'SalesOrd') {
                list.setLineItemValue('custpage_po_num', i + 1, searchResults[i].getValue('otherrefnum'));
            }

            var hash = String(CryptoJS.SHA256(secretKey + searchResults[i].getValue('internalid')));

            var html = '<a href="' + url + '&custpage_internalid=' + searchResults[i].getValue('internalid') + '&userid=' + request.getParameter('userid') + '&hash=' + hash + '" target="_blank">View</a>' +
                '&nbsp;&nbsp;<a href="' + url + '&custpage_internalid=' + searchResults[i].getValue('internalid') + '&userid=' + request.getParameter('userid') + '&hash=' + hash + '&download=T" target="_blank">Download</a>';

            list.setLineItemValue('custpage_view', i + 1, html);

            html = '';

            hash = String(CryptoJS.SHA256(secretKey + request.getParameter('userid') + request.getParameter('custpage_customer')));

            //var baseurl = nlapiResolveURL('SUITELET', 'customscript_in8_creditcards_sl', 'customdeploy_in8_creditcards_sl', true);

            if (enableInvoicePayments && tranType == 'CustInvc' && searchResults[i].getText('status') == 'Open') {
                html += '&nbsp;&nbsp;<a href="javascript:payInvoice(' + searchResults[i].getValue('internalid') + ',\'' + searchResults[i].getValue('transactionname') + '\',' + request.getParameter('userid') + ',' + request.getParameter('custpage_customer') + ',\'' + hash + '\',' + searchResults[i].getValue('amountremaining') + ');' + '' + '">Pay Invoice</a>';
            }

            hash = String(CryptoJS.SHA256(secretKey + searchResults[i].getValue('internalid')));

            list.setLineItemValue('custpage_pay', i + 1, html);

            html = '';
            if (tranType == 'SalesOrd' && enablePrintPacking == 'T' && (searchResults[i].getText('status') == 'Billed' || searchResults[i].getText('status') == 'Pending Billing')) {
                //hash = String(CryptoJS.SHA256(secretKey + searchResults[i].getValue('internalid')));

                html += '&nbsp;&nbsp;<a href="' + url + '&custpage_internalid=' + searchResults[i].getValue('internalid') + '&userid=' + request.getParameter('userid') + '&hash=' + hash + '&packingSlip=T" target="_blank">Packing Slip</a>';
                list.setLineItemValue('custpage_packing', i + 1, html);
            }

            html = '';
            if (deliveryConfirmField) {
                if (searchResults[i].getValue(deliveryConfirmField)) {
                    html += '&nbsp;&nbsp;<a href="' + nlapiLookupField('file', searchResults[i].getValue(deliveryConfirmField), 'url') + '" target="_blank">Delivery Confirmation</a>';
                } else {
                    var statuses = [];
                    for (var j = 0; j < fulfillData.length; j++) {
                        if (fulfillData[j].getValue(deliveryConfirmField) && statuses.indexOf(fulfillData[j].getValue(deliveryConfirmField)) == -1) {
                            statuses.push(fulfillData[j].getValue(deliveryConfirmField));
                            html += '&nbsp;&nbsp;<a href="' + nlapiLookupField('file', fulfillData[j].getValue(deliveryConfirmField), 'url') + '" target="_blank">Delivery Confirmation</a><br/>';
                        }
                    }
                }
                list.setLineItemValue('custpage_delivery', i + 1, html);
            }

            //list.setLineItemValue('custpage_download', i + 1, '<a href="' + url + '&custpage_internalid=' + searchResults[i].getValue('internalid') + '&hash=' + hash + '" download>Download</a>');
        }
    };

    function getTrackingStatus(fieldName, deliveryConfirmField, internalId) {

        var statuses = [];
        var fields = [];
        if (fieldName) {
            fields.push(new nlobjSearchColumn(fieldName));
        }
        if (deliveryConfirmField) {
            fields.push(new nlobjSearchColumn(deliveryConfirmField));
        }

        var search = nlapiSearchRecord('itemfulfillment', null, [
            new nlobjSearchFilter('createdfrom', null, 'anyof', internalId)
        ], fields) || [];

        return search;
    }

    /**
     * Get the Searh Results
     * 
     * @returns {Void} 
     */
    var getSearchResults = function () {

        var filters = [],
            columns = [],
            i = 0;

        var deliveryConfirmField = nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_delivery_confirm_field');

        if (request.getParameter('custpage_customer')) filters[i++] = new nlobjSearchFilter('entity', null, 'anyof', request.getParameter('custpage_customer'));

        if (request.getParameter('custpage_tranid')) filters[i++] = new nlobjSearchFilter('tranid', null, 'contains', request.getParameter('custpage_tranid'));

        filters[i++] = new nlobjSearchFilter('mainline', null, 'is', 'T');
        filters[i++] = new nlobjSearchFilter('type', null, 'anyof', tranType);

        i = 0;

        columns[i++] = new nlobjSearchColumn('transactionname');
        columns[i++] = new nlobjSearchColumn('transactionnumber');
        var column = new nlobjSearchColumn('trandate');
        column.setSort(true);
        columns[i++] = column;
        column = new nlobjSearchColumn('internalid');
        column.setSort(true);
        columns[i++] = column;
        columns[i++] = new nlobjSearchColumn('status');
        columns[i++] = new nlobjSearchColumn('entity');
        columns[i++] = new nlobjSearchColumn('amount');
        columns[i++] = new nlobjSearchColumn('otherrefnum');

        if (deliveryConfirmField) {
            columns[i++] = new nlobjSearchColumn(deliveryConfirmField);
        }
        if (tranType == 'CustInvc') {
            columns[i++] = new nlobjSearchColumn('amountremaining');
        }
        return nlapiSearchRecord('transaction', null, filters, columns);
    };

    /**
     * Displays a message
     * 
     * @param {String} message Message
     * @returns {Void}
     */
    var displayMessage = function (message) {

        // Create a NetSuite form
        var form = nlapiCreateForm(FORM_NAME, false),
            html = message;

        // Add a new HTML field to display the HTML contents
        field = form.addField('file', 'inlinehtml', 'label');
        field.setLayoutType('outsidebelow');
        field.setDefaultValue('<font size="2pt">' + html + '</font>');

        form.addButton('custombutton_back', 'Back', 'window.history.back()');

        response.writePage(form);
    };

    return {
        handleGet: handleGet,
        handlePost: handlePost
    };
})();