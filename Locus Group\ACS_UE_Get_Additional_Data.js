/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/ui/serverWidget', 'N/search', 'N/query', 'N/file', 'N/url'], function(record, serverWidget, search, query, file, url) {

    var host;

    function isEmpty(stValue) {
        return ((stValue === '' || stValue === null || stValue === undefined) || (stValue.constructor === Array && stValue.length === 0) || (stValue.constructor === Object && (function (v) {
            for (var k in v)
                return false;
            return true;
        })(stValue)));
    }

    function createSubsidiaryObj(recObj) {
        var subsidiary = {};

        var subsidiaryId = recObj.getValue({ fieldId: 'subsidiary' });
        var subObj = record.load({
            type: record.Type.SUBSIDIARY,
            id: subsidiaryId,
            isDynamic: true
        });

        // var includedFields = subObj.getFields();
        var includedFields = ["defaultaddressee", "federalidnumber", "email", "phone", "fax", "url", "currency", "pagelogo"];
        
        for(var i = 0; i < includedFields.length; i++) {
            if(!subsidiary.hasOwnProperty(includedFields[i])){
                var value = subObj.getValue({ fieldId: includedFields[i] });
                var text = subObj.getValue({ fieldId: includedFields[i] });

                if(includedFields[i] == 'pagelogo'){

                    var fileObj = file.load({
                        id: value
                    });

                    var url = 'https://' + host + fileObj.url.replace('&', '&amp;');

                    subsidiary[includedFields[i]] = {
                        value: url
                    };
                    continue;
                }

                subsidiary[includedFields[i]] = {
                    value: value,
                    text: text
                };
            }
        }
        
        var addressFields = ["addressee","addrtext"];
        var addressObj = subObj.getSubrecord({
            fieldId: 'mainaddress'
        });
        for(var i = 0; i < addressFields.length; i++) {
            if(!subsidiary.hasOwnProperty(addressFields[i])){
                var value = addressObj.getValue({ fieldId: addressFields[i] });
                var text = addressObj.getValue({ fieldId: addressFields[i] });
                subsidiary[addressFields[i]] = {
                    value: value,
                    text: text
                };
            }
        }

        subsidiary.addrtext.value = subsidiary.addrtext.value.replace(subsidiary.addressee.text , "");
        subsidiary.addrtext.value = subsidiary.addrtext.value.replace(/\r\n/g, "<br>");
        subsidiary.addrtext.text = subsidiary.addrtext.value;
        return subsidiary;

    }

    function createInvoicesObj(recObj) {
        
        var invoices = [];

        var invoiceGroupId = recObj.id;

        var invoicegroupSearchObj = search.load({
            id: 'customsearch_acs_invoice_group_search'
        });

        var internalIdFilter = search.createFilter({
            name: 'internalidnumber',
            operator: search.Operator.EQUALTO,
            values: invoiceGroupId
        });

        invoicegroupSearchObj.filters.push(internalIdFilter);

        invoicegroupSearchObj.run().each(function(result){
            
            var sqlQueryData = "SELECT inv.duedate, inv.tranid FROM transaction inv WHERE inv.id = ?";
		
            var resultSet = query.runSuiteQL({
                query: sqlQueryData,
                params: [result.getValue({
                    name: "internalid",
                    join: "transaction",
                    summary: "GROUP"
                })]
            });
         
            var results = resultSet.results;
            for(var i = 0; i < results.length; i++ ){
                invoices.push({
                    invoice_number: results[i].values[1], 
                    due_date: results[i].values[0]
                });
                
            }
            return true;
        });

        log.audit('invoices', invoices);
        return invoices;
    }

    function beforeLoad(context) {
        if(context.type == context.UserEventType.PRINT) {

            try {

                var recordForm = context.form;
                var recObj = context.newRecord;
                host = url.resolveDomain({
                    hostType: url.HostType.APPLICATION
                });


                var subsidiaryData = createSubsidiaryObj(recObj);
                var invoiceData = createInvoicesObj(recObj);

                var additionalData = {
                    subsidiary: subsidiaryData,
                    invoices: invoiceData
                }

                var additionalDataField = recordForm.addField({
                    id : 'custpage_addtl_data',
                    type : serverWidget.FieldType.LONGTEXT,
                    label : 'additional data'
                });

                log.audit('additionalData',additionalData);
                
                additionalDataField.defaultValue = JSON.stringify(additionalData);

            } catch (e) {

                var additionalDataField = recordForm.addField({
                    id : 'custpage_addtl_data',
                    type : serverWidget.FieldType.LONGTEXT,
                    label : 'additional data'
                });
                
                additionalDataField.defaultValue = '';

                log.error("Something went wrong while getting subsidiary fields", e);
            }
        }
    }

    return {
        beforeLoad: beforeLoad
    }
});
