<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            .check table,
            .voucher1 table,
            .voucher2 table {
                position: relative;
                overflow: hidden;
                font-size: 8pt;
                padding: 0;
            }

            td p {
                align: left
            }
        </style>
    </head>

    <body padding="0.5in 0.5in 0.5in 0.5in" size="Letter">
        <#list records as check>
            <div
                style="position: relative;font-family: Helvetica,sans-serif;top= -11pt;height: 250pt;width: 612pt;page-break-inside: avoid;font-size: 8pt;">
                <table
                    style="position: absolute;overflow: hidden;left: 489pt;top: 8pt;height: 7pt;width: 85pt;font-size: 5pt;">
                    <tr>
                        <td align="center">${check.checknumber}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 466pt;top: 34pt;height: 18pt;width: 108pt;">
                    <tr>
                        <td>${check.trandate}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 50pt;top: 69pt;height: 18pt;width: 393pt;">
                    <tr>
                        <td>${check.entity}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 463pt;top: 69pt;height: 18pt;width: 111pt;">
                    <tr>
                        <td>**<#if (check.usertotal?length> 0)>${check.usertotal}<#else>${check.total}</#if>
                        </td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 2pt;top: 93pt;height: 18pt;width: 572pt;">
                    <tr>
                        <td>${check.totalwords}*********************************************************************
                        </td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 37pt;top: 122pt;height: 80pt;width: 537pt;">
                    <tr>
                        <td>${check.address}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 2pt;top: 178pt;height: 18pt;width: 572pt;">
                    <tr>
                        <td>${check.memo}</td>
                    </tr>
                </table>
            </div>

            <div
                style="position: relative;font-family: Helvetica,sans-serif;height: 250pt;width: 612pt;page-break-before: avoid;font-size: 8pt;">
                <table
                    style="position: absolute;overflow: hidden;left: 403pt;top: -16pt;height: 7pt;width: 40pt;font-size: 5pt;">
                    <tr>
                        <td align="center">${check.checknumber}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 412pt;top: -2pt;height: 13pt;width: 70pt;">
                    <tr>
                        <td>${check.trandate}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 36pt;top: -2pt;height: 13pt;width: 157pt;">
                    <tr>
                        <td>${check.entity}<br /><br /><br /><br /><br /><br /><br />${check.memo}</td>
                    </tr>
                </table>
                <#if check.item?has_content || check.expense?has_content || check.apply?has_content>

                    <table style="position: absolute;overflow: hidden;left: 36pt;top: 90pt;width: 436pt;">
                        <#list check.expense as expense>
                            <tr>
                                <td>${expense.account}</td>
                                <td>${expense.date}</td>
                                <td>${expense.description}</td>
                                <td align="right">${expense.amount}</td>
                            </tr>
                        </#list>
                        <#list check.item as item>
                            <tr>
                                <td>&nbsp;</td>
                                <td>${item.date}</td>
                                <td>${item.item}, ${item.description}</td>
                                <td align="right">${item.amount}</td>
                            </tr>
                        </#list>
                        <#if check.apply?has_content>
                            <tr>
                                <td>&nbsp;</td>
                                <td>Date Due</td>
                                <td>Type</td>
                                <td>Ref No.</td>
                                <td>Orig. Amt.</td>
                                <td>Amt. Due</td>
                                <td>Amt. Paid</td>
                                <td>Balance</td>
                                <td>Currency</td>
                            </tr>
                            <#assign totalbal = 0.00>
                            <#list check.apply as apply>
                                <#assign due = apply.due>
                                <#if check.credit?has_content>
                                    <#list check.credit as credit>
                                        <#assign appliedto = credit.appliedto?keep_after(" #")>
                                        <#if apply.refnum == appliedto> 
                                            <#assign due = apply.due + credit.amount>
                                        </#if>
                                    </#list>
                                </#if>

                                <#assign balance = due - apply.amount>
                                <#assign totalbal = totalbal + balance>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>${apply.applydate}</td>
                                    <td>${apply.type}</td>
                                    <td>${apply.refnum}</td>
                                    <td>${apply.total}</td>
                                    <td>${due?string.currency}</td>
                                    <td>${apply.amount}</td>
                                    <td>${balance?string.currency}</td>
                                    <td>${apply.currency}</td>
                                </tr>
                            </#list>
                            <#if check.credit?has_content>
                                <#list check.credit as credit>
                                    <#assign totalbal = totalbal - credit.amount>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td>${credit.creditdate}</td>
                                        <td>*${credit.type}</td>
                                        <td>${credit.refnum}</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>(${credit.amount})</td>
                                        <td></td>
                                    </tr>
                                </#list>
                            </#if>
                            <tr style="border-top: 1px;">
                                <td colspan="2"><b>Total Balance</b></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td><b>${totalbal?string.currency}</b></td>
                                <td></td>
                            </tr>
                        </#if>
                    </table>
                </#if>

                <table style="position: absolute;overflow: hidden;left: 473pt;top: 204pt;height: 13pt;width: 67pt;">
                    <tr>
                        <td></td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 148pt;top: 204pt;height: 13pt;width: 325pt;">
                    <tr>
                        <td>${check.memo}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 9pt;top: 204pt;height: 13pt;width: 134pt;">
                    <tr>
                        <td>${check.total}</td>
                    </tr>
                </table>
            </div>

            <div
                style="position: relative;font-family: Helvetica,sans-serif;height: 250pt;width: 612pt;page-break-before: avoid;font-size: 8pt;">
                <table
                    style="position: absolute;overflow: hidden;left: 403pt;top: -16pt;height: 7pt;width: 40pt;font-size: 5pt;">
                    <tr>
                        <td align="center">${check.checknumber}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 412pt;top: -2pt;height: 13pt;width: 70pt;">
                    <tr>
                        <td>${check.trandate}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 36pt;top: -2pt;height: 13pt;width: 157pt;">
                    <tr>
                        <td>${check.entity}<br /><br /><br /><br /><br /><br />${check.memo}</td>
                    </tr>
                </table>
                <#if check.item?has_content || check.expense?has_content || check.apply?has_content>

                    <table style="position: absolute;overflow: hidden;left: 36pt;top: 90pt;width: 436pt;">
                        <#list check.expense as expense>
                            <tr>
                                <td>${expense.account}</td>
                                <td>${expense.date}</td>
                                <td>${expense.description}</td>
                                <td align="right">${expense.amount}</td>
                            </tr>
                        </#list>
                        <#list check.item as item>
                            <tr>
                                <td>&nbsp;</td>
                                <td>${item.date}</td>
                                <td>${item.item}, ${item.description}</td>
                                <td align="right">${item.amount}</td>
                            </tr>
                        </#list>
                        <#if check.apply?has_content>
                            <tr>
                                <td>&nbsp;</td>
                                <td>Date Due</td>
                                <td>Type</td>
                                <td>Ref No.</td>
                                <td>Orig. Amt.</td>
                                <td>Amt. Due</td>
                                <td>Amt. Paid</td>
                                <td>Balance</td>
                                <td>Currency</td>
                            </tr>
                            <#assign totalbal = 0.00>
                            <#list check.apply as apply>
                                <#assign due = apply.due>
                                <#if check.credit?has_content>
                                    <#list check.credit as credit>
                                        <#assign appliedto = credit.appliedto?keep_after(" #")>
                                        <#if apply.refnum == appliedto> 
                                            <#assign due = apply.due + credit.amount>
                                        </#if>
                                    </#list>
                                </#if>

                                <#assign balance = due - apply.amount>
                                <#assign totalbal = totalbal + balance>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>${apply.applydate}</td>
                                    <td>${apply.type}</td>
                                    <td>${apply.refnum}</td>
                                    <td>${apply.total}</td>
                                    <td>${due?string.currency}</td>
                                    <td>${apply.amount}</td>
                                    <td>${balance?string.currency}</td>
                                    <td>${apply.currency}</td>
                                </tr>
                            </#list>
                            <#if check.credit?has_content>
                                <#list check.credit as credit>
                                    <#assign totalbal = totalbal - credit.amount>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td>${credit.creditdate}</td>
                                        <td>*${credit.type}</td>
                                        <td>${credit.refnum}</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>(${credit.amount})</td>
                                        <td></td>
                                    </tr>
                                </#list>
                            </#if>
                            <tr style="border-top: 1px;">
                                <td colspan="2"><b>Total Balance</b></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td><b>${totalbal?string.currency}</b></td>
                                <td></td>
                            </tr>
                        </#if>
                    </table>
                </#if>

                <table style="position: absolute;overflow: hidden;left: 473pt;top: 204pt;height: 13pt;width: 67pt;">
                    <tr>
                        <td></td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 148pt;top: 204pt;height: 13pt;width: 325pt;">
                    <tr>
                        <td>${check.memo}</td>
                    </tr>
                </table>

                <table style="position: absolute;overflow: hidden;left: 9pt;top: 204pt;height: 13pt;width: 134pt;">
                    <tr>
                        <td>${check.total}</td>
                    </tr>
                </table>
            </div>
        </#list>
    </body>
</pdf>