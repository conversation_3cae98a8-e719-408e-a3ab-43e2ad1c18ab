/**
 * @NApiVersion 2.x
 * @NScriptType MapReduceScript
 * @NModuleScope SameAccount
 */
define(['N/file', "N/sftp", 'N/runtime', 'N/search'],

    function (file, sftp, runtime, search) {

        /**
         * Marks the beginning of the Map/Reduce process and generates input data.
         *
         * @typedef {Object} ObjectRef
         * @property {number} id - Internal ID of the record instance
         * @property {string} type - Record type id
         *
         * @return {Array|Object|Search|RecordRef} inputSummary
         * @since 2015.1
         */
        function getInputData() {

            var scriptObj = runtime.getCurrentScript();
            var searchId = scriptObj.getParameter({ name: 'custscript_search_id' });

            // retrieve files created today
            var searchObj = search.load({
                id: searchId
            });
    
            return searchObj;

        }

        /**
         * Executes when the map entry point is triggered and applies to each key/value pair.
         *
         * @param {MapSummary} context - Data collection containing the key/value pairs to process through the map stage
         * @since 2015.1
         */
        function map(context) {

            try {

                var scriptObj = runtime.getCurrentScript();

                // get PFA and File ID
                var pfaID = context.key;
                var contextValues = JSON.parse(context.value);
                var fileId = contextValues.values.custrecord_2663_file_ref.value;


                // get SFTP Credentials here using script parameters
                var sftpHostKey = scriptObj.getParameter({ name: 'custscript_sftp_host_key' });
                var sftpUsername = scriptObj.getParameter({ name: 'custscript_sftp_username' });
                var sftpPort = scriptObj.getParameter({ name: 'custscript_sftp_port' });
                var sftpKeyId = scriptObj.getParameter({ name: 'custscript_sftp_key_id' });
                var sftpServerUrl = scriptObj.getParameter({ name: 'custscript_sftp_server_url' });
                var archiveId = scriptObj.getParameter({ name: 'custscript_archive_id' });

                // load file object

                var origFileObj = file.load({
                    id: fileId
                });

                // set the name based on bank's requirements and move to 'sent folder'
                var newName = 'efm2899r.' + pfaID + '.txt';
                var fileObj = file.create({
                    name: newName,
                    fileType: file.Type.PLAINTEXT,
                    contents: origFileObj.getContents(),
                    folder: archiveId
                });

                fileObj.name = newName;
                var fileId = fileObj.save();

                // reload file
                var fileToBeSentObj = file.load({
                    id: fileId
                });

                log.debug('test', { fileId: fileToBeSentObj.id, fileName: fileToBeSentObj.name });
                
                // connect to sftp server using script parameters
                var sftpConnection = sftp.createConnection({
                    username: sftpUsername,
                    keyId: sftpKeyId,
                    url: sftpServerUrl,
                    port: Number(sftpPort),
                    hostKey: sftpHostKey
                });
                log.debug("sftpConnection", sftpConnection);

                // send file to server
                sftpConnection.upload({
                    file: fileToBeSentObj,
                    replaceExisting: true
                });


            } catch(e) {
            	log.error('Map - Error during sftp upload', e);
            }
        }

        /**
         * Executes when the reduce entry point is triggered and applies to each group.
         *
         * @param {ReduceSummary} context - Data collection containing the groups to process through the reduce stage
         * @since 2015.1
         */
        function reduce(context) {

        }


        /**
         * Executes when the summarize entry point is triggered and applies to the result set.
         *
         * @param {Summary} summary - Holds statistics regarding the execution of a map/reduce script
         * @since 2015.1
         */
        function summarize(summary) {

        }

        return {
            getInputData: getInputData,
            map: map,
            reduce: reduce,
            summarize: summarize
        };

    });