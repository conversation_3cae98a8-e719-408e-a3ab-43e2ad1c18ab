/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/search'], function(record, search) {

    function afterSubmit(context) {
        var recObj = context.newRecord;
        var recLookUpObj = search.lookupFields({
            type: 'customrecord_ce_cc_pur_rec',
            id: recObj.id,
            columns: [
                'custrecord_ce_cc_pur_ven',
                'custrecord_ce_cc_pur_cc',
                'custrecord_ce_ccpur_dept',
                'custrecord_ce_cc_pur_item',
                'custrecord_ce_cc_pur_amount',
                'custrecord_ce_cc_pur_proj', // customer on item
                'custrecord_ce_cc_pur_status',
            ]
        });
        if(recLookUpObj.custrecord_ce_cc_pur_status[0].text == 'Approved'){
            try {
                var fieldValues = {
                    vendor: (recLookUpObj.custrecord_ce_cc_pur_ven) ? recLookUpObj.custrecord_ce_cc_pur_ven[0].value : '',
                    creditcard: (recLookUpObj.custrecord_ce_cc_pur_cc) ? recLookUpObj.custrecord_ce_cc_pur_cc[0].value : '',
                    dept: (recLookUpObj.custrecord_ce_ccpur_dept) ? recLookUpObj.custrecord_ce_ccpur_dept[0].value : '',
                    item: (recLookUpObj.custrecord_ce_cc_pur_item) ? recLookUpObj.custrecord_ce_cc_pur_item[0].value : '',
                    amount: recLookUpObj.custrecord_ce_cc_pur_amount,
                    proj: (recLookUpObj.custrecord_ce_cc_pur_proj) ? recLookUpObj.custrecord_ce_cc_pur_proj[0].value : ''
                }

                var ccChargeObj = record.create({
                    type: record.Type.CREDIT_CARD_CHARGE,
                    isDynamic: true
                });
                var refNo = "CCP " + recObj.id.toString();
                log.debug(refNo);
                ccChargeObj.setValue({ fieldId: 'entity', value: fieldValues.vendor });
                ccChargeObj.setValue({ fieldId: 'account', value: fieldValues.creditcard });
                ccChargeObj.setValue({ fieldId: 'department', value: fieldValues.dept });
                ccChargeObj.setValue({ fieldId: 'tranid', value: refNo });
                ccChargeObj.selectLine({ sublistId: 'item', line: 0 });
                ccChargeObj.setCurrentSublistValue({ sublistId: 'item', fieldId: 'item', value: fieldValues.item });
                ccChargeObj.setCurrentSublistValue({ sublistId: 'item', fieldId: 'amount', value: fieldValues.amount, ignoreFieldChange: true });
                ccChargeObj.setCurrentSublistValue({ sublistId: 'item', fieldId: 'customer', value: fieldValues.proj });
                ccChargeObj.setCurrentSublistValue({ sublistId: 'item', fieldId: 'custcol_ce_cc_pur_rec', value: recObj.id });
                ccChargeObj.commitLine({ sublistId: 'item' });

                if(ccChargeObj.save()){
                    var ccTranObj = record.load({
                        type: 'customrecord_ce_cc_pur_rec',
                        id: recObj.id,
                        isDynamic: true
                    });
                    ccTranObj.setValue({ fieldId: 'custrecord_ce_cc_pur_status', value: 6 });
                    ccTranObj.save();
                }
            } catch (e) {
                log.debug('Error', e);
            }
        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
