/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/ui/serverWidget'], function(serverWidget) {

    function beforeLoad(context) {
        var hideFld = context.form.addField({
            id:'custpage_hide_buttons',
            label:'not shown - hidden',
            type: serverWidget.FieldType.INLINEHTML
        });
        
        //for every button you want to hide, modify the scr += line
        var scr = "";
        scr += 'jQuery("#item_row_2").hide();';
        scr += 'alert();';

        //push the script into the field so that it fires and does its handy work
        hideFld.defaultValue = "<script>jQuery(function($){require([], function(){" + scr + ";})})</script>"
    }

    function beforeSubmit(context) {
        
    }

    function afterSubmit(context) {
        
    }

    return {
        beforeLoad: beforeLoad,
        beforeSubmit: beforeSubmit,
        afterSubmit: afterSubmit
    }
});
