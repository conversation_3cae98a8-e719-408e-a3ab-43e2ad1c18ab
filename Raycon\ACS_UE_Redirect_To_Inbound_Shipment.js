/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/redirect', 'N/record'], function(redirect, record) {

    function beforeLoad(context) {
        var recObj = context.newRecord;
        var inbShipmentID = recObj.getValue({ fieldId: 'custrecord_inbound_shipment' });
        redirect.toRecord({
            type : record.Type.INBOUND_SHIPMENT,
            id : inbShipmentID,
        });
    }

    return {
        beforeLoad: beforeLoad
    }
});
