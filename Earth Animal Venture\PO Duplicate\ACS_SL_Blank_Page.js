/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/ui/serverWidget'], function(serverWidget) {

    function onRequest(context) {
        var myForm = serverWidget.createForm({
            title : '-'
        });        
        myForm.clientScriptModulePath = 'SuiteScripts/ACS_CS_Close_Suitelet_Page.js';
        
        context.response.writePage({
            pageObject: myForm
        });
        // context.response.write('<script>window.location.reload(); window.close();</script>');

    }

    return {
        onRequest: onRequest
    }
});
