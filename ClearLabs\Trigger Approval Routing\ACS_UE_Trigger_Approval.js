/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record',
        'N/workflow',
        'N/task',
        'N/runtime'],
function(rec,
         workflow,
         task,
         runtime) {


    const APPROVAL_WORKFLOW_ID = 'customworkflow_sas_approval_workflow';
    const GET_APPROVAL_RULE_BTN = 'workflowaction_sas_get_rule';
    const MIRROR_APPROVAL_ID = 'customrecord_sas_mirror_approval';

    function afterSubmit(scriptContext) {
        if(scriptContext.type == 'specialorder'){

            const record = scriptContext.newRecord;
            const recType = record.type;
            const recId = record.id;
            const cUser = runtime.getCurrentUser();
            log.debug('test', cUser.id);
            if (cUser.id !== -4 || recType === 'customrecord_sas_approval_logs') {
                const mirrorApprovalId = createMirrorApproval(recType, recId, cUser, record);
                log.debug('test', mirrorApprovalId);
                initiateWorkflow(mirrorApprovalId);
                initiateApprovalRuleSearch(mirrorApprovalId);
            }
        }
    }

    function createMirrorApproval(type, id, currUser, record) {
        if (type && id) {
            try {
                const newApprovalRecord = rec.create({
                    type: 'customrecord_sas_mirror_approval',
                    isDynamic: true
                });

                newApprovalRecord.setValue({
                    fieldId: 'custrecord_sas_mar_ref_record_scriptid',
                    value: type
                });

                newApprovalRecord.setValue({
                    fieldId: 'custrecord_sas_mar_ref_record_internalid',
                    value: id
                });

                try {
                    const submitterId = currUser.id;

                    newApprovalRecord.setValue({
                        fieldId: 'custrecord_sas_mar_submitter',
                        value: submitterId
                    });
                } catch (e) {
                    log.error({
                        title: 'ERROR: Cannot set the record submitter!',
                        details: e.message || e.toString()
                    });
                    throw e;
                }

                // retain record submitter's preference since we cannot load an employee's preference (for PO cases)
                newApprovalRecord.setValue({
                    fieldId: 'custrecord_sas_mar_preferences',
                    value: JSON.stringify({
                        timezone: currUser.getPreference({
                            name: 'TIMEZONE'
                        }),
                        language: currUser.getPreference({
                            name: 'LANGUAGE'
                        })
                    })
                });

                const mirrorId = newApprovalRecord.save({
                    enableSourcing: true,
                    ignoreMandatoryFields: false
                });

                return mirrorId;
            } catch (e) {
                log.error({
                    title: 'ERROR: Cannot create Mirror Approval record!',
                    details: e.message || e.toString()
                });
                throw e;
            }
        }
    }
    
    function initiateWorkflow(mirrorRecId) {
        try {
            workflow.initiate({
                recordType: MIRROR_APPROVAL_ID,
                recordId: mirrorRecId,
                workflowId: APPROVAL_WORKFLOW_ID
            });
            workflow.trigger({
                recordType: MIRROR_APPROVAL_ID,
                recordId: mirrorRecId,
                workflowId: APPROVAL_WORKFLOW_ID,
                actionId: GET_APPROVAL_RULE_BTN
            });
        } catch (e) {
            log.error({
                title: 'ERROR: Cannot initiate Approval Workflow!',
                details: e.message || e.toString()
            });
            throw e;
        }
    }


    function initiateApprovalRuleSearch(mirrorRecId) {
        try {
            task.create({
                taskType: task.TaskType.MAP_REDUCE,
                scriptId: 'customscript_sas_approval_rule_eval',
                deploymentId: 'customdeploy_sas_approval_rule_eval',
                params: {
                    // eslint-disable-next-line camelcase
                    custscript_sas_appruleeval_mirror_rec: mirrorRecId
                }
            }).submit();
        } catch (e) {
            log.error({
                title: 'ERROR! Cannot initiate Approval Rule search!',
                details: e.message || e.toString()
            });
            if (e.name !== 'MAP_REDUCE_ALREADY_RUNNING') {
                throw e;
            }
        }
    }
    return {
        afterSubmit: afterSubmit
    }
});
