/**
 * @appliedtorecord recordtype
 *
 * @param {String} type Operation types: create, edit, delete, xedit
 *
 * @returns {Void}
 */
function beforeSubmit(type) {

    try {
        if (type == 'create') {
            if (nlapiGetFieldValue('custbody_in8_wc_order_id')) {

                // If the SO is being created from an Estimate, do not run the shipping calculation
                if (nlapiGetFieldValue('createdfrom') && nlapiLookupField('transaction', nlapiGetFieldValue('createdfrom'), 'recordtype') == 'estimate') {
                    nlapiLogExecution('DEBUG', 'In8', 'Created from Estimate. Do not run this code.');
                    return;
                }
                calculateShipping_client(type);
            }
        }
    } catch (e) {
        nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e));
    }
}