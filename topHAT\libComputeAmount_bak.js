/**
 * Copyright (c) 1998-2020 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of NetSuite, Inc. ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with NetSuite.
 *
 * Version    Date            		Author           Remarks
 * 1.0		                        bjalandoni		 Initial Version
 * 2.0		  30Jul2020             ccutib           Applies pay per stop calculation and threshold on ALL rules
 *
 */
 
/**
 * @NApiVersion 2.x
 * @NModuleScope Public
 */
define([],
function() {
	var libComputeAmount = typeof libComputeAmount === 'undefined' ? {} : libComputeAmount;
	
	libComputeAmount.computeCustomerRate = function(recDailyRoute, objCustomerRate, _SCRIPT_PARAM){
		/* will reset all fields to be supplied prior computation */
		/* clear fields in Customer */
		recDailyRoute.setValue('custrecord_th_dailyroute_custinvrate', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_custaddmileage', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_custaddmileager', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_custaddstops', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_custaddstopsrat', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_custamount', '');		
		var stLogtitle = 'libComputeAmount.computeCustomerRate';
		var stRateRule = objCustomerRate[0].getValue({name : 'custrecord_th_custrate_raterule'});
		log.debug('', stLogtitle);
		log.debug(stLogtitle, 'Rate Rule= ' + stRateRule);
		try{
			recDailyRoute.setValue('custrecord_th_dailyroute_rate_rule', stRateRule);
			var flCustomerAmount = null;
			
			switch (stRateRule){
			case _SCRIPT_PARAM.custscript_dr_alggo_baserate: flCustomerAmount = getAmountBaseRate(recDailyRoute, objCustomerRate, null);
				break;
			case _SCRIPT_PARAM.custscript_dr_alggo_perfbasedrate: flCustomerAmount = getAmountPerformaceBased(recDailyRoute, objCustomerRate, null);
				break;  
			case _SCRIPT_PARAM.custscript_dr_alggo_ratepermileage: flCustomerAmount = getAmountRatePerMileage(recDailyRoute, objCustomerRate, null);
				break;
			case _SCRIPT_PARAM.custscript_dr_alggo_perfbasedrate_guramt: flCustomerAmount = getAmountPerformaceBasedGurranteed(recDailyRoute, objCustomerRate, null);
				break;
			case _SCRIPT_PARAM.custscript_dr_alggo_flatamount: flCustomerAmount = getAmountFlatRate(recDailyRoute, objCustomerRate, null);
				break;
			case _SCRIPT_PARAM.custscript_dr_alggo_percentageroute: flCustomerAmount = getAmountPercentageRouteValue(recDailyRoute, objCustomerRate, null);
				break;
			}
			
			log.debug(stLogtitle, 'flCustomerAmount= ' + flCustomerAmount);
			return flCustomerAmount;
		}
		catch (err){
			log.error(stLogtitle, err.message);
			recDailyRoute.setValue('custrecord_th_dailyroute_message', _SCRIPT_PARAM.custscript_raterule_calculation_error + '\n' + ' Rate Rule being applied: ' + stRateRule);
			recDailyRoute.setValue('custrecord_th_dailyroute_status', _SCRIPT_PARAM.custscript_dr_status_pending);
			return null;
		}
	};
	
	
	
	libComputeAmount.computeContractorRate = function(recDailyRoute, objContractorRate, _SCRIPT_PARAM){
		/* will reset all fields to be supplied prior computation */
		/* clear fields in Vendor */
		recDailyRoute.setValue('custrecord_th_dailyroute_payrate', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_addstops', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_addstopsrate', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_addmileage', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_addmileagerate', '');
		recDailyRoute.setValue('custrecord_th_dailyroute_contractoramt', '');
		var stLogtitle = 'libComputeAmount.computeContractorRate';
		log.debug('', stLogtitle);
		var stRateRule = objContractorRate[0].getValue({name : 'custrecord_th_rate_raterule'});
		log.debug(stLogtitle, 'Rate Rule= ' + stRateRule);
		try{
			recDailyRoute.setValue('custrecord_th_dailyroute_rate_rule', stRateRule);
			var flContractorAmount = null;
			switch (stRateRule){
			case _SCRIPT_PARAM.custscript_dr_alggo_baserate: flContractorAmount = getAmountBaseRate(recDailyRoute, null, objContractorRate);
				break;
			case _SCRIPT_PARAM.custscript_dr_alggo_perfbasedrate: flContractorAmount = getAmountPerformaceBased(recDailyRoute, null, objContractorRate);
				break;
			case _SCRIPT_PARAM.custscript_dr_alggo_ratepermileage: flContractorAmount = getAmountRatePerMileage(recDailyRoute, null, objContractorRate);
				break;
			case _SCRIPT_PARAM.custscript_dr_alggo_perfbasedrate_guramt: flContractorAmount = getAmountPerformaceBasedGurranteed(recDailyRoute, null, objContractorRate);
				break;
			case _SCRIPT_PARAM.custscript_dr_alggo_flatamount: flContractorAmount = getAmountFlatRate(recDailyRoute, null, objContractorRate);
				break;
			case _SCRIPT_PARAM.custscript_dr_alggo_percentageroute: flContractorAmount = getAmountPercentageRouteValue(recDailyRoute, null, objContractorRate);
				break;
			}
			log.debug(stLogtitle, 'flContractorAmount= ' + flContractorAmount);
			return flContractorAmount;
		}
		catch(err){
			log.error(stLogtitle, err.message);
			recDailyRoute.setValue('custrecord_th_dailyroute_message', _SCRIPT_PARAM.custscript_raterule_calculation_error + ' ' + ' Rate Rule being applied: ' + stRateRule);
			recDailyRoute.setValue('custrecord_th_dailyroute_status', _SCRIPT_PARAM.custscript_dr_status_pending);
			return null;
		}
	};
	
	function calcCustomerPayPerStop(recDailyRoute, objCustomerRate, intDailyRouteRate) {
		/* CALCULATE ADDITIONAL PAY PER STOP */
		var intQty 					        = recDailyRoute.getValue('custrecord_th_dailyroute_qty');
		var intCustAdditionalStopRate		= objCustomerRate.getValue({name: 'custrecord_th_custrate_addstoprate'}); 
		var intCustAdditionalStopThresh 	= objCustomerRate.getValue({name: 'custrecord_th_custrate_addstopthreshld'}); 
		var intAdditionalStop 				= 0;
		var intAdditionalStopRate 			= 0;
		var intAdditionalPay				= 0;
		var intDailyRoutePayAmount          = null;
		if (intCustAdditionalStopThresh == 0 || intCustAdditionalStopThresh == ''){
			intDailyRoutePayAmount = null;
		}
		else if (intQty <= intCustAdditionalStopThresh){
			intDailyRoutePayAmount = null;
		}
		else{
			intAdditionalStop = intQty - intCustAdditionalStopThresh;
			intAdditionalStopRate = intCustAdditionalStopRate;
			intDailyRoutePayAmount = intDailyRouteRate * intCustAdditionalStopThresh;
			intAdditionalPay = intAdditionalStop * (parseFloat(intDailyRouteRate) + parseFloat(intAdditionalStopRate));
			
			intDailyRoutePayAmount = parseFloat(intDailyRoutePayAmount) + parseFloat(intAdditionalPay);
		}
		log.debug('Customer :: Customer Addl Pay Per Stop', 
				' intDailyRouteRate= ' + intDailyRouteRate +
				' intAdditionalStopRate= ' + intAdditionalStopRate +
				' intCustAdditionalStopThresh= ' + intCustAdditionalStopThresh +
				' intAdditionalPay= ' + intAdditionalPay +
				' intDailyRoutePayAmount= ' + intDailyRoutePayAmount
		);
		recDailyRoute.setValue('custrecord_th_dailyroute_custinvrate', intDailyRouteRate);
		recDailyRoute.setValue('custrecord_th_dailyroute_custaddstops', intAdditionalStop);
		recDailyRoute.setValue('custrecord_th_dailyroute_custaddstopsrat', intAdditionalStopRate);
		return intDailyRoutePayAmount;
	}
	
	function calcContractorPayPerStop(recDailyRoute, objContractorRate, intDailyRouteRate) {
		/* CALCULATE ADDITIONAL PAY PER STOP */
		var intQty 					        = recDailyRoute.getValue('custrecord_th_dailyroute_qty');
		var intConAdditionalStopRate		= objContractorRate.getValue({name: 'custrecord_th_rate_addpayperstop'});
		var intConAdditionalStopThresh 		= objContractorRate.getValue({name: 'custrecord_th_rate_addpayperstopthreshld'});
		var intAdditionalStop 				= 0;
		var intAdditionalStopRate 			= 0;
		var intAdditionalPay				= 0;
		var intDailyRoutePayAmount          = null;
		if (intConAdditionalStopThresh == 0 || intConAdditionalStopThresh == ''){
			intDailyRoutePayAmount = null;
		}
		else if (intQty <= intConAdditionalStopThresh){
			intDailyRoutePayAmount = null;
		}
		else{
			intAdditionalStop 		= intQty - intConAdditionalStopThresh;
			intAdditionalStopRate 	= intConAdditionalStopRate;
			intDailyRoutePayAmount 	= intDailyRouteRate * intConAdditionalStopThresh;
			intAdditionalPay 		= intAdditionalStop * (parseFloat(intDailyRouteRate) + parseFloat(intAdditionalStopRate));
			
			intDailyRoutePayAmount 	= parseFloat(intDailyRoutePayAmount) + parseFloat(intAdditionalPay);
		}
		log.debug('Customer :: Contractor Addl Pay Per Stop', 
				' intDailyRouteRate= ' + intDailyRouteRate +
				' intAdditionalStopRate= ' + intAdditionalStopRate +
				' intConAdditionalStopThresh= ' + intConAdditionalStopThresh +
				' intAdditionalPay= ' + intAdditionalPay +
				' intDailyRoutePayAmount= ' + intDailyRoutePayAmount
		);
		recDailyRoute.setValue('custrecord_th_dailyroute_payrate', intDailyRouteRate);
		recDailyRoute.setValue('custrecord_th_dailyroute_addstops', intAdditionalStop);
		recDailyRoute.setValue('custrecord_th_dailyroute_addstopsrate', intAdditionalStopRate);
		return intDailyRoutePayAmount;
	}

	/* ALGORITHM 1 :  BASE RATE */
	function getAmountBaseRate(recDailyRoute, objCustomerRate, objContractorRate){
		var intQty 				= recDailyRoute.getValue('custrecord_th_dailyroute_qty');
		var flBaseRate 			= 0;
		var flBaseRateAmount 	= null;
		if (objCustomerRate != null){
			flBaseRate 			= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate'});
			flBaseRateAmount 	= calcCustomerPayPerStop(recDailyRoute, objCustomerRate[0], flBaseRate);
			if (flBaseRateAmount === null) {
				flBaseRateAmount = flBaseRate * intQty;
			}
			recDailyRoute.setValue('custrecord_th_dailyroute_custinvrate', flBaseRate);
		}
		else{
			flBaseRate 			= objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate'});
			flBaseRateAmount 	= calcContractorPayPerStop(recDailyRoute, objContractorRate[0], flBaseRate);
			if (flBaseRateAmount === null) {
				flBaseRateAmount = flBaseRate * intQty;
			}
			recDailyRoute.setValue('custrecord_th_dailyroute_payrate', flBaseRate);
		}
		
		
		return flBaseRateAmount;
	}
	
	/* ALGORITHM 2 :  PERFORMANCE-BASED RATE */
	function getAmountPerformaceBased(recDailyRoute, objCustomerRate, objContractorRate){
		var intQty 					= recDailyRoute.getValue('custrecord_th_dailyroute_qty');
		var intDailyRoutePerformace = recDailyRoute.getValue('custrecord_th_dailyroute_performance');
		var intDailyRouteRate 		= 0;
		
		var intDailyRoutePayAmount 	= null;
		
		if (objCustomerRate != null){
			
			/* CALCULATE BASE RATE */
			var intCustRInvRate 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate'}); 
			var intCustRInvRate2	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate2'});
			var intCustRInvRate3	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate3'}); 
			var intCustRInvRate4	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate4'}); 
			
			var intCustRRateRange1 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange1'}); 
			var intCustRRateRange2 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange2'}); 
			var intCustRRateRange3 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange3'});
			var intCustRRateRange4 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange4'}); 
			
			if (intDailyRoutePerformace == 0){
				intDailyRouteRate = intCustRInvRate;
			}
			else if (intCustRRateRange1 >= intDailyRoutePerformace){
				intDailyRouteRate = intCustRInvRate;
			}
			else if (intCustRRateRange2 >= intDailyRoutePerformace){
				intDailyRouteRate = intCustRInvRate2	;
			}
			else if (intCustRRateRange3 >= intDailyRoutePerformace){
				intDailyRouteRate = intCustRInvRate3;	
			}
			else {
				intDailyRouteRate = intCustRInvRate4;	
			}
			
			/* default empty rate into base rate if no values from range  */
			if (intDailyRouteRate == 0 || intDailyRouteRate == ''){
				intDailyRouteRate = intCustRInvRate;
			}
			
			intDailyRoutePayAmount = calcCustomerPayPerStop(recDailyRoute, objCustomerRate[0], intDailyRouteRate);
			if (intDailyRoutePayAmount === null) {
				intDailyRoutePayAmount = intDailyRouteRate * intQty;
			}
			return intDailyRoutePayAmount;
		}
		else{
			
			var intConRBaseRate 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate'});
			var intConRPayRate2 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate2'});
			var intConRPayRate3 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate3'});
			var intConRPayRate4 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate4'});
					
			var intConRRateRange1	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange1'});
			var intConRRateRange2	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange2'});
			var intConRRateRange3	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange3'});
			var intConRRateRange4	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange4'});
			
			
			if (intDailyRoutePerformace == 0){
				intDailyRouteRate = intConRBaseRate;
			}
			else if (intConRRateRange1 >= intDailyRoutePerformace){
				intDailyRouteRate = intConRBaseRate;
			}
			else if (intConRRateRange2 >= intDailyRoutePerformace){
				intDailyRouteRate = intConRPayRate2;	
			}
			else if (intConRRateRange3 >= intDailyRoutePerformace){
				intDailyRouteRate = intConRPayRate3;
			}
			else {
				intDailyRouteRate = intConRPayRate4;	
			}
			
			/* default empty rate into base rate if no values from range  */
			if (intDailyRouteRate == 0 || intDailyRouteRate == ''){
				intDailyRouteRate = intConRBaseRate;
			}
			
			intDailyRoutePayAmount = calcContractorPayPerStop(recDailyRoute, objContractorRate[0], intDailyRouteRate);
			if (intDailyRoutePayAmount === null) {
				intDailyRoutePayAmount = intDailyRouteRate * intQty;
			}
			return intDailyRoutePayAmount;
			
		}
	}
	
	/* ALGORITHM 3 :  RATE PER MILEAGE & INCENTIVE AFTER THRESHOLD */
	
	function getAmountRatePerMileage(recDailyRoute, objCustomerRate, objContractorRate){
		var intQty 				= recDailyRoute.getValue('custrecord_th_dailyroute_qty');
		var intDRVendorRate		= 0;
		var intMileAgeThresh 	= 0;
		var flBaseRateAmount 	= 0;
		var intMileAge			= 0;
		var intMileAgeRate		= 0;
		var intMileAgeThreshRate= 0;
		var DRAdditionalMileage = 0;
		var intDRPaymentAmount 	= null;
		
		if (objCustomerRate != null){
			intDRVendorRate		= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate'});
			intMileAgeThresh 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_mileagethreshld'});
			intMileAgeThreshRate = objCustomerRate[0].getValue({name: 'custrecord_th_custrate_addmileagerate'});
			
			if (intMileAgeThresh == 0){
				intDRPaymentAmount = intDRVendorRate * intQty;
			}
			else{
				
				if (intQty <= intMileAgeThresh){
					intDRPaymentAmount = intDRPaymentAmount * intQty;
				}
				else{
					DRAdditionalMileage = parseFloat(intQty) - parseFloat(intMileAgeThresh);
					intMileAgeRate = intMileAgeThreshRate;
					intDRPaymentAmount = intDRVendorRate * intMileAgeThresh;
					var intAddPay = DRAdditionalMileage * intMileAgeThreshRate;
					intDRPaymentAmount = parseFloat(intDRPaymentAmount) + parseFloat(intAddPay);
				}
			}
			
			recDailyRoute.setValue('custrecord_th_dailyroute_custinvrate', intDRVendorRate);
			recDailyRoute.setValue('custrecord_th_dailyroute_custaddmileage', DRAdditionalMileage);
			recDailyRoute.setValue('custrecord_th_dailyroute_custaddmileager', intMileAgeRate);
			
			var flDRAddlPayPerStopAmount = calcCustomerPayPerStop(recDailyRoute, objCustomerRate[0], intDRVendorRate);
			// if both mileage amount and stops amount are null then just use qty * rate
			if (flDRAddlPayPerStopAmount === null && !intDRPaymentAmount) {
				intDRPaymentAmount = intDRVendorRate * intQty;
			// else, add both amounts together
			} else if (flDRAddlPayPerStopAmount) {
				intDRPaymentAmount = (intDRPaymentAmount || 0) + flDRAddlPayPerStopAmount;
			}
			
			log.debug('Customer :: Contractor getAmountRatePerMileage', 
					' DRAdditionalMileage= ' + DRAdditionalMileage +
					' intMileAgeRate= ' + intMileAgeRate +
					' flDRAddlPayPerStopAmount= ' + flDRAddlPayPerStopAmount +
					' intAddPay= ' + intAddPay +
					' intDRPaymentAmount= ' + intDRPaymentAmount
			);
			return intDRPaymentAmount;
			
		}
		else{
			intDRVendorRate		= objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate'});
			intMileAgeThresh 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_mileagethreshold'});
			intMileAgeThreshRate = objContractorRate[0].getValue({name: 'custrecord_th_rate_addmileagerate'});
			
			if (intMileAgeThresh == 0){
				intDRPaymentAmount = intDRVendorRate * intQty;
			}
			else{
				
				if (intQty <= intMileAgeThresh){
					intDRPaymentAmount = intDRVendorRate * intQty;
				}
				else{
					DRAdditionalMileage = parseFloat(intQty) - parseFloat(intMileAgeThresh);
					intMileAgeRate = intMileAgeThreshRate;
					intDRPaymentAmount = intDRVendorRate * intMileAgeThresh;
					var intAddPay = DRAdditionalMileage * intMileAgeThreshRate;
					intDRPaymentAmount = parseFloat(intDRPaymentAmount) + parseFloat(intAddPay);
				}
			}
			
			recDailyRoute.setValue('custrecord_th_dailyroute_payrate', intDRVendorRate);
			recDailyRoute.setValue('custrecord_th_dailyroute_addmileage', DRAdditionalMileage);
			recDailyRoute.setValue('custrecord_th_dailyroute_addmileagerate', intMileAgeRate);
			
			var flDRAddlPayPerStopAmount = calcContractorPayPerStop(recDailyRoute, objContractorRate[0], intDRVendorRate);
			// if both mileage amount and stops amount are null then just use qty * rate
			if (flDRAddlPayPerStopAmount === null && !intDRPaymentAmount) {
				intDRPaymentAmount = intDRVendorRate * intQty;
			// else, add both amounts together
			} else if (flDRAddlPayPerStopAmount) {
				intDRPaymentAmount = (intDRPaymentAmount || 0) + flDRAddlPayPerStopAmount;
			}
			
			log.debug('Customer :: Contractor getAmountRatePerMileage', 
					' DRAdditionalMileage= ' + DRAdditionalMileage +
					' intMileAgeRate= ' + intMileAgeRate +
					' flDRAddlPayPerStopAmount= ' + flDRAddlPayPerStopAmount +
					' intAddPay= ' + intAddPay +
					' intDRPaymentAmount= ' + intDRPaymentAmount
			);
			return intDRPaymentAmount;
			
		}
		
	}
	
	/* ALGORITHM 4 :  PERFORMANCE-BASED RATE - GUARANTEED AMOUNT*/
	
	function getAmountPerformaceBasedGurranteed(recDailyRoute, objCustomerRate, objContractorRate){
		
		var intDailyRoutePerformace = recDailyRoute.getValue('custrecord_th_dailyroute_performance');
		var intQty 					= recDailyRoute.getValue('custrecord_th_dailyroute_qty');
		var intDailyRouteRate 		= 0;
		
		var intDailyRoutePayAmount 	= null;
		
		if (objCustomerRate != null){
			var intGurranteedThresh 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_guaranteethreshld'});
			if (intQty > intGurranteedThresh){
				/* APPLY PERFOMNCE BASED RATE*/
				return  getAmountPerformaceBased(recDailyRoute, objCustomerRate, objContractorRate);
			}
			else{
				/* APPLY PERFOMNCE BASED - GURRATEED RATE*/
				
				/* CALCULATE BASE RATE */
				var intCustRGurRate 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_guaranteeamt'}); 
				var intCustRGurRate2	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_guaranteeamt2'});
				var intCustRGurRate3	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_guaranteeamt3'}); 
				var intCustRGurRate4	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_guaranteeamt4'}); 
				
				var intCustRRateRange1 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange1'}); 
				var intCustRRateRange2 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange2'}); 
				var intCustRRateRange3 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange3'});
				var intCustRRateRange4 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange4'}); 
				
				
				
				if (intDailyRoutePerformace == 0){
					intDailyRouteRate = intCustRGurRate;
				}
				else if (intCustRRateRange1 >= intDailyRoutePerformace){
					intDailyRouteRate = intCustRGurRate;
				}
				else if (intCustRRateRange2 >= intDailyRoutePerformace){
					intDailyRouteRate = intCustRGurRate2;	
				}
				else if (intCustRRateRange3 >= intDailyRoutePerformace){
					intDailyRouteRate = intCustRGurRate3;	
				}
				else {
					intDailyRouteRate = intCustRGurRate4;	
				}
				
				/* default empty rate into base rate if no values from range  */
				if (intDailyRouteRate == 0 || intDailyRouteRate == ''){
					intDailyRouteRate = intCustRGurRate;
				}
				
				intDailyRoutePayAmount = calcCustomerPayPerStop(recDailyRoute, objCustomerRate[0], intDailyRouteRate);
				if (intDailyRoutePayAmount === null) {
					intDailyRoutePayAmount = intDailyRouteRate;
				}
				return intDailyRoutePayAmount;
				
			}
		}
		else{
			var intGurranteedThresh 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_guaranteedthreshld'});
			if (intQty > intGurranteedThresh){
				/* APPLY PERFOMNCE BASED RATE*/
				return  getAmountPerformaceBased(recDailyRoute, objCustomerRate, objContractorRate);
			}
			else{
				/* APPLY PERFOMNCE BASED - GURRATEED RATE*/
				
				var intConRGurRate 		= objContractorRate[0].getValue({name: 'custrecord_th_rate_guaranteedamt'});
				var intConRGurRate2 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_guaranteedamt2'});
				var intConRGurRate3 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_guaranteedamt3'});
				var intConRGurRate4 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_guaranteedamt4'});
						
				var intConRRateRange1	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange1'});
				var intConRRateRange2	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange2'});
				var intConRRateRange3	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange3'});
				var intConRRateRange4	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange4'});
				
				
				
				if (intDailyRoutePerformace == 0){
					intDailyRouteRate = intConRGurRate;
				}
				else if (intConRRateRange1 >= intDailyRoutePerformace){
					intDailyRouteRate = intConRGurRate;
				}
				else if (intConRRateRange2 >= intDailyRoutePerformace){
					intDailyRouteRate = intConRGurRate2;	
				}
				else if (intConRRateRange3 >= intDailyRoutePerformace){
					intDailyRouteRate = intConRGurRate3;
				}
				else {
					intDailyRouteRate = intConRGurRate4;	
				}
				
				/* default empty rate into base rate if no values from range  */
				if (intDailyRouteRate == 0 || intDailyRouteRate == ''){
					intDailyRouteRate = intConRGurRate;
				}
				
				intDailyRoutePayAmount = calcContractorPayPerStop(recDailyRoute, objContractorRate[0], intDailyRouteRate);
				if (intDailyRoutePayAmount === null) {
					intDailyRoutePayAmount = intDailyRouteRate;
				}
				return intDailyRoutePayAmount;
				
			}
		}
		
	}
	
	/* ALGORITHM 5 :  FLAT AMOUNT */
	function getAmountFlatRate(recDailyRoute, objCustomerRate, objContractorRate){
		var flFlatRateAmount = null;
		var flDailyRoutePayAmount = null;
		if (objCustomerRate != null){
			flFlatRateAmount = objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate'});
			intDailyRoutePayAmount = calcCustomerPayPerStop(recDailyRoute, objCustomerRate[0], flFlatRateAmount);
			if (flDailyRoutePayAmount === null) {
				flDailyRoutePayAmount = flFlatRateAmount;
			}
		}
		else{
			flFlatRateAmount = objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate'});
			flDailyRoutePayAmount = calcContractorPayPerStop(recDailyRoute, objContractorRate[0], flFlatRateAmount);
			if (flDailyRoutePayAmount === null) {
				flDailyRoutePayAmount = flFlatRateAmount;
			}
		}
		return flDailyRoutePayAmount;
	}
	
	/* ALGORITHM 7 :  PERCENTAGE OF ROUTE VALUE */
	
	function getAmountPercentageRouteValue(recDailyRoute, objCustomerRate, objContractorRate){
		var intDailyRoutePerformace = recDailyRoute.getValue('custrecord_th_dailyroute_performance');
		var intDRTotalRouteValue 	= recDailyRoute.getValue('custrecord_th_dailyroute_totalroutevalue');
		var intQty 					= recDailyRoute.getValue('custrecord_th_dailyroute_qty');
		var intDailyRouteRate 		= 0;
		
		var intDailyRoutePayAmount	= null;

		if (objCustomerRate != null){
			
			/* CALCULATE BASE RATE */
			var intCustRInvRate 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate'}); 
			var intCustRInvRate2	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate2'});
			var intCustRInvRate3	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate3'}); 
			var intCustRInvRate4	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_invoicerate4'}); 
			
			var intCustRRateRange1 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange1'}); 
			var intCustRRateRange2 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange2'}); 
			var intCustRRateRange3 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange3'});
			var intCustRRateRange4 	= objCustomerRate[0].getValue({name: 'custrecord_th_custrate_raterange4'}); 
			

			if (intDailyRoutePerformace == 0){
				intDailyRouteRate = intCustRInvRate;
			}
			else if (intCustRRateRange1 >= intDailyRoutePerformace){
				intDailyRouteRate = intCustRInvRate;
			}
			else if (intCustRRateRange2 >= intDailyRoutePerformace){
				intDailyRouteRate = intCustRInvRate2;	
			}
			else if (intCustRRateRange3 >= intDailyRoutePerformace){
				intDailyRouteRate = intCustRInvRate3;	
			}
			else {
				intDailyRouteRate = intCustRInvRate4;	
			}
			
			/* default empty rate into base rate if no values from range  */
			if (intDailyRouteRate == 0 || intDailyRouteRate == ''){
				intDailyRouteRate = intCustRInvRate;
			}
			
			var stAMount = intDailyRouteRate * intDRTotalRouteValue.toFixed(2);
			
			recDailyRoute.setValue('custrecord_th_dailyroute_custinvrate', intDailyRouteRate);
			var flDRAddlPayPerStopAmount = calcCustomerPayPerStop(recDailyRoute, objCustomerRate[0], intDailyRouteRate);
			if (flDRAddlPayPerStopAmount) {
				stAMount = +stAMount + flDRAddlPayPerStopAmount;
			}
			intDailyRoutePayAmount = stAMount;
			
			log.debug('Customer :: Customer getAmountPercentageRouteValue', 
					' intDailyRouteRate= ' + intDailyRouteRate +
					' intQty= ' + intQty +
					' intDRTotalRouteValue= ' + intDRTotalRouteValue +
					' stAMount= ' + stAMount +
					' intDailyRoutePayAmount= ' + intDailyRoutePayAmount
			);
			return intDailyRoutePayAmount;
			
			
			
		}
		else{
			
			
			var intConRBaseRate 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate'});
			var intConRPayRate2 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate2'});
			var intConRPayRate3 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate3'});
			var intConRPayRate4 	= objContractorRate[0].getValue({name: 'custrecord_th_rate_payrate4'});
					
			var intConRRateRange1	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange1'});
			var intConRRateRange2	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange2'});
			var intConRRateRange3	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange3'});
			var intConRRateRange4	= objContractorRate[0].getValue({name: 'custrecord_th_rate_raterange4'});
			
		
			if (intDailyRoutePerformace == 0){
				intDailyRouteRate = intConRBaseRate;
			}
			else if (intConRRateRange1 >= intDailyRoutePerformace){
				intDailyRouteRate = intConRBaseRate;
			}
			else if (intConRRateRange2 >= intDailyRoutePerformace){
				intDailyRouteRate = intConRPayRate2;
			}
			else if (intConRRateRange3 >= intDailyRoutePerformace){
				intDailyRouteRate = intConRPayRate3;	
			}
			else {
				intDailyRouteRate = intConRPayRate4;	
			}
			
			/* default empty rate into base rate if no values from range  */
			if (intDailyRouteRate == 0 || intDailyRouteRate == ''){
				intDailyRouteRate = intConRBaseRate;
			}
			
			var stAMount = intDailyRouteRate * intDRTotalRouteValue.toFixed(2);
			
			recDailyRoute.setValue('custrecord_th_dailyroute_payrate', intDailyRouteRate);
			var flDRAddlPayPerStopAmount = calcContractorPayPerStop(recDailyRoute, objContractorRate[0], intDailyRouteRate);
			if (flDRAddlPayPerStopAmount) {
				stAMount = +stAMount + flDRAddlPayPerStopAmount;
			}
			intDailyRoutePayAmount = stAMount;
			
			log.debug('Customer :: Contractor getAmountPercentageRouteValue', 
					' intDailyRouteRate= ' + intDailyRouteRate +
					' intQty= ' + intQty +
					' intDRTotalRouteValue= ' + intDRTotalRouteValue +
					' stAMount= ' + stAMount +
					' intDailyRoutePayAmount= ' + intDailyRoutePayAmount
			);
			return intDailyRoutePayAmount;
			
		}
	}
	
	return libComputeAmount;
});