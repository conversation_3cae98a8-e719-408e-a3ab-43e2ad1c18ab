/**
 * Subscription Pack - User Event
 * Sync data to WooCommerce as a product
 * 
 * Version    Date            Author           Remarks
 * 1.00       27 Jul 2016     Marcel P		   Initial Version
 *
 */

/**
 * The recordType (internal id) corresponds to the "Applied To" record in your script deployment. 
 * @appliedtorecord recordType
 * 
 * @param {String} type Operation types: create, edit, delete, xedit,
 *                      
 * @returns {Void}
 */
function afterSubmit(type) {
	var i = 0;
	
	try {		
		if (nlapiGetContext().getExecutionContext() == 'userinterface' && (type == 'create' || type == 'edit' || type == 'xedit')) {
			
			In8SyncSubscription.syncCustomRecord(nlapiGetRecordType(), nlapiGetRecordId(), type == 'create');
		}				 
	} catch(e) {
		nlapiLogExecution('ERROR', 'In8Sync', 'Error synchronizing with WooCommerce: ' + (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e) + ' ' + e.stack != undefined ? e.stack : '');
		
		In8Lib.logError(nlapiGetRecordType(), nlapiGetRecordId(), (e instanceof nlobjError ? e.getCode() : ''), (e instanceof nlobjError ? e.getDetails() : e), e.stack != undefined ? e.stack : '');
		
		if (nlapiGetContext().getSetting('SCRIPT', 'custscript_in8_display_error_update') == 'T') {		
			throw new nlapiCreateError('ERROR', 'Item saved successfully, but there is an error when updating the record in WooCommerce. Error: ' + (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e), true);
		}				
	}
}
