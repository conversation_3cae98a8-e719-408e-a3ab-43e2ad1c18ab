var lookupFieldId = "";
var fieldForm = "";
var joinValue = "";
var searchName = "";

function suitelet(request, response)
{
    try {

        var form = nlapiCreateForm('Search');

        if(request.getMethod() == "GET" && request.getParameter("showAll") != "T")
        {
            var fieldId = form.addField('custpage_lookupfieldid', 'text', 'Field Id');
            fieldId.setDisplayType('hidden');
            fieldId.setDefaultValue(request.getParameter('opensearch'));

            var fieldform = form.addField('custpage_lookupfieldform', 'text', 'Field Form');
            fieldform.setDisplayType('hidden');
            fieldform.setDefaultValue(request.getParameter('name'));

            var fieldform = form.addField('custpage_joinvalue', 'text', 'Join Value');
            fieldform.setDisplayType('hidden');
            fieldform.setDefaultValue(request.getParameter('joinValue'));

            form.addField('custpage_lookupsearch', 'text', 'Name');
            form.addSubmitButton('Search');

        }
        else
        {
            if(request.getParameter("showAll") == "T")
            {
                lookupFieldId = request.getParameter('opensearch');
                fieldForm = request.getParameter('name');
                joinValue = request.getParameter('joinValue');
            }
            else
            {
                lookupFieldId = request.getParameter('custpage_lookupfieldid');
                fieldForm = request.getParameter('custpage_lookupfieldform');
                joinValue = request.getParameter('custpage_joinvalue');
                searchName = request.getParameter('custpage_lookupsearch');
            }

            var sublist = form.addSubList('custpage_resultsublist', 'staticlist', 'Results');
            sublist.addField('custpage_link', 'text', 'ID');
            sublist.addField('custpage_text', 'text', 'Name');

            var results = getSearchResults(lookupFieldId);

            var line = 1;

            for(var i = 0; i < results.length; i++)
            {
                sublist.setLineItemValue('custpage_link', line, '<a href="#" onclick="javascript:window.parent.setLookupField(\''+fieldForm+'\','+results[i].id+',\''+results[i].label+'\')">'+results[i].id+'</a>');
                sublist.setLineItemValue('custpage_text', line, '<a href="#" onclick="javascript:window.parent.setLookupField(\''+fieldForm+'\','+results[i].id+',\''+results[i].label+'\')">'+results[i].label+'</a>');

                line++;
            }
        }

        response.writePage(form);
    }
    catch (e) {
        var form = nlapiCreateForm("Error");

        var code = "UNEXPECTED_ERROR";
        var message = "";

        if(e instanceof nlobjError)
        {
            code = e.getCode();
            message = e.getDetails();
        }
        else
        {
            message = e.message;
        }

        var field = form.addField('custpage_message', 'inlinehtml', 'Message');
        field.setDefaultValue("<h2>Code: " + code +" <br/>Message: " + message + "</h2>");

        response.writePage(form);
    }
}

function getSearchResults(fieldId)
{
    switch(fieldId)
    {
        case 'company':
        case 'customer':
            return searchCustomers(getSearchParamaters('customer'));

        case 'custevent_project_name':
            return searchCustomers(getSearchParamaters('job'));

        case 'custevent_project_task':
            return searchCustomers(getSearchParamaters('projecttask'));
    }

    return [];
}

function searchCustomers(parameters)
{
    var ret = [];

    var filters = [];

    for(var i = 0; i < parameters.filters.length; i++)
    {
        nlapiLogExecution('DEBUG', 'Filter', JSON.stringify(parameters.filters[i]));

        if(parameters.filters[i].default == true && String(searchName).trim().length > 0)
            filters.push(new nlobjSearchFilter(parameters.filters[i].fieldid, null, parameters.filters[i].operator, searchName));
        else if(parameters.filters[i].join == true && joinValue)
            filters.push(new nlobjSearchFilter(parameters.filters[i].fieldid, null, parameters.filters[i].operator, joinValue));
        else if(parameters.filters[i].default != true && parameters.filters[i].join != true)
            filters.push(new nlobjSearchFilter(parameters.filters[i].fieldid, null, parameters.filters[i].operator, parameters.filters[i].value));
    }

    nlapiLogExecution('DEBUG', 'filters', JSON.stringify(filters));

    var columns = [];
    columns.push(new nlobjSearchColumn(parameters.textfield));

    var searchCustomer = nlapiCreateSearch(parameters.recordtype, filters, columns);

    var resultSet = searchCustomer.runSearch();

    var start = 0;
    var end = 1000;

    do
    {
        var results = resultSet.getResults(start, end);

        for (var i = 0; i < results.length; i++)
        {
            var result = {};
            result.id = results[i].getId();
            result.label = results[i].getValue(parameters.textfield);

            ret.push(result);
        }

        start += 1000;
        end += 1000;
    }
    while(results.length > 0);

    return ret;

}

function getSearchParamaters(recordType)
{
    var ret = [];

    ret['customer'] = {
        recordtype: "customer",
        textfield: "companyname",
        filters: [{
                fieldid: "isinactive",
                operator: "is",
                value: "F",
                default: false
            },
            {
                fieldid: "entitystatus",
                operator: "anyof",
                value: "13",
                default: false
            },
            {
                fieldid: "companyname",
                operator: "contains",
                default: true
            }]
    };

    ret['job'] = {
        recordtype: "job",
        textfield: "altname",
        filters: [
            {
                fieldid: "altname",
                operator: "contains",
                default: true
            },
            {
                fieldid: "parent",
                operator: "anyof",
                join: true
            }]
    };

    ret['projecttask'] = {
        recordtype: "projecttask",
        textfield: "title",
        filters: [
            {
                fieldid: "title",
                operator: "contains",
                default: true
            },
            {
                fieldid: "company",
                operator: "anyof",
                join: true
            }]
    };

    return ret[recordType];
}