<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td rowspan="3">
                            <#if companyInformation.logoUrl?length !=0><img height="50%"
                                    src="${companyInformation.logoUrl}" style="float: left; margin: 7px" width="50%" />
                            </#if>
                        </td>
                        <td align="right"><span class="title">${record@title}</span></td>
                    </tr>
                    <tr>
                        <td align="right"><span class="number">#${record.tranid}</span></td>
                    </tr>
                    <tr>
                        <td align="right">${record.trandate}</td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td>
                            <barcode codetype="code128" showtext="true" value="${record.tranid}" />
                        </td>
                        <td align="right">
                            <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
            <macro id="timesheetheader">
                <table style="width: 100%; margin-top: 10px; font-size: 10pt;">
                    <tr>
                        <td style="width: 10%;">Project:</td>
                        <td style="width: 40%;"><b>${record.job}</b></td>
                        <td style="width: 10%;">Client:</td>
                        <td style="width: 40%;"><b>${record.entity}</b></td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table {
                font-size: 9pt;
                table-layout: fixed;
            }

            th {
                font-weight: bold;
                font-size: 8pt;
                vertical-align: middle;
                padding: 5px 6px 3px;
                background-color: #064F81;
                color: #FFFFFF;
            }

            td {
                padding: 6px 8px;
            }

            td p {
                align: left
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            table.header td {
                padding: 0px;
                font-size: 10pt;
            }

            table.footer td {
                padding: 0px;
                font-size: 8pt;
            }

            table.itemtable th {
                padding-bottom: 10px;
                padding-top: 10px;
            }

            table.body td {
                padding-top: 2px;
            }

            table.total {
                page-break-inside: avoid;
            }

            tr.totalrow {
                background-color: #FFFFFF;
                line-height: 100%;
            }

            td.totalboxtop {
                font-size: 12pt;
                background-color: #FFFFFF;
            }

            td.addressheader {
                font-size: 8pt;
                padding-top: 6px;
                padding-bottom: 2px;
            }

            td.address {
                padding-top: 0px;
            }

            td.totalboxmid {
                font-size: 28pt;
                padding-top: 1px;
                background-color: #FFFFFF;
            }

            td.totalboxbot {
                background-color: #FFFFFF;
                font-weight: bold;
            }

            span.title {
                font-size: 28pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }
        </style>
    </head>

    <body header="nlheader" header-height="10%" footer="nlfooter" footer-height="10pt" padding="0.5in 0.5in 0.5in 0.5in"
        size="Letter">

        <table style="width: 100%; margin-top: 10px;">
            <tr>
                <td class="addressheader" colspan="5" style="width: 498px; height: 22px;">602, 734 - 7 Ave S.W. Calgary,
                    AB. T2P 3P8</td>
                <td class="totalboxtop" colspan="5" rowspan="2" style="width: 274px; height: 10px;">
                    <div><b>${record.total@label?upper_case}</b></div>
                </td>
            </tr>
            <tr>
                <td class="addressheader" colspan="5" style="width: 498px; height: 1px;"><span
                        style="color:#000000;"><b>Consulting Services Provided To:</b></span></td>
            </tr>
            <tr>
                <td class="address" colspan="5" rowspan="2" style="width: 498px;">${record.billaddress}</td>
                <td align="right" class="totalboxmid" colspan="5" style="width: 274px;"><span
                        style="font-size:36px;">${record.total}</span></td>
            </tr>
        </table>

        <table class="body" style="width: 100%; margin-top: 10px;">
            <tr>
                <th>${record.terms@label}</th>
                <th>${record.duedate@label}</th>
                <th>${record.otherrefnum@label}</th>
            </tr>
            <tr>
                <td>${record.terms}</td>
                <td>${record.duedate}</td>
                <td>${record.otherrefnum}</td>
            </tr>
        </table>
        <#if record.item?has_content>

            <table class="itemtable" style="width: 100%; margin-top: 10px;">
                <!-- start items -->
                <#list record.item as item>
                    <#if item_index==0>
                        <thead>
                            <tr>
                                <th align="left" colspan="5">${item.quantity@label}</th>
                                <th colspan="12">${item.description@label}</th>
                                <th colspan="8"><b><span style="color:#ffffff;">Resource</span></b></th>
                                <th align="left" colspan="6">${item.rate@label}</th>
                                <th align="right" colspan="6">${item.amount@label}</th>
                            </tr>
                        </thead>
                    </#if>
                    <tr>
                        <td align="left" colspan="5" line-height="150%">${item.quantity}</td>
                        <td colspan="12"><span class="itemname">${item.item}</span><br />${item.description}</td>
                        <td colspan="8">${item.employeedisp}</td>
                        <td align="left" colspan="6">${item.rate}</td>
                        <td align="right" colspan="6">${item.amount}</td>
                    </tr>
                </#list><!-- end items -->
            </table>

            <hr />
        </#if>
        <table class="total" style="width: 100%; margin-top: 10px;">
            <tr>
                <td colspan="4">Please make all cheques payable to&nbsp;${record.custbody1}</td>
            </tr>
            <tr>
                <td colspan="4">Contact: Sydney Olson (403)-366-5704 ext. 101&nbsp; <EMAIL></td>
            </tr>
            <tr>
                <td colspan="4">&nbsp;</td>
                <td align="right"><b>${record.subtotal@label}</b></td>
                <td align="right">${record.subtotal}</td>
            </tr>
            <tr>
                <td colspan="4">GST# ********* RT0001</td>
                <td align="right"><b>${record.taxtotal@label} (${record.taxrate}%)</b></td>
                <td align="right">${record.taxtotal}</td>
            </tr>
            <tr class="totalrow">
                <td background-color="#ffffff" colspan="4">&nbsp;</td>
                <td align="right"><b>${record.total@label}</b></td>
                <td align="right">${record.total}</td>
            </tr>
        </table>
        <br />For Wire
        Transfer:<br /><br />${record.custbody2}
        <#if record.custpage_acs_charge_data_for_pdf?is_string >
            <#assign charge_page_data = record.custpage_acs_charge_data_for_pdf?eval >
            <#list charge_page_data.employees as key, value>
                <pbr header="timesheetheader" footer="termsfooter"  header-height="6%" footer-height="5%" size="Letter" padding="0.5in 0.5in 0.5in 0.5in" />
                <table class="itemtable" style="width: 100%; margin-top: 10px;">
                    <tr>
                        <th style="font-size: 9pt;" align="center">Assignment Details</th>
                    </tr>
                </table>
                <table class="itemtable" style="width: 100%; margin-top: 5px;">
                    <thead>
                        <tr>
                            <th style="font-size: 8pt; width: 15%;" align="left" >Date</th>
                            <th style="font-size: 8pt; width: 30%;" align="left" >Employee</th>
                            <th style="font-size: 8pt; width: 40%;" align="left" >Activity</th>
                            <th style="font-size: 8pt; width: 15%;" align="right" >Days Worked</th>
                        </tr>
                    </thead>
                    <#list value as row>
                        <tr>
                            <td align="left">${row.date}</td>
                            <td align="left">${row.employeename}</td>
                            <td align="left">${row.activity}</td>
                            <td align="right">${row.days_worked}</td>
                        </tr> 
                    </#list>
                    <tr>
                        <td style="border-top: 1px;" colspan="3" align="right"><b>Total:</b></td>
                        <td style="border-top: 1px;" align="right"><b>${charge_page_data.totals[key]}</b></td>
                    </tr>
                </table>
            </#list>            
        </#if>
    </body>
</pdf>