/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/log', 'N/search', 'N/record', 'N/https', 'N/runtime'], function (log, search, record, https, runtime) {

    function onRequest(context) {

        log.debug('Body', context.request.body);

        var obj = JSON.parse(context.request.body);

        if (obj.event_type == 'sale.ready_for_payment') {
            readyForPaymentCustom(context, obj);
        }
    }

    function readyForPaymentCustom(context, obj) {

        var items = {};

        for (var i = 0; i < obj.sale.line_items.length; i++) {

            if (obj.sale.line_items[i].custom_fields && obj.sale.line_items[i].custom_fields.length) {
                // Set custom field
                for (var j = 0; j < obj.sale.line_items[i].custom_fields.length; j++) {
                    setCustomField(obj.sale.line_items[i].id, obj.sale.line_items[i].custom_fields[j], "line_item");
                }
            } else if (Number(obj.sale.line_items[i].quantity) < 0) {
                if (!items.actions) {
                    items.actions = [];
                }
                items.actions.push({
                    "type": "require_custom_fields",
                    "title": "Is this item resellable?",
                    "message": "Please let us know if this item can be sold again",
                    "entity": "line_item",
                    "entity_id": obj.sale.line_items[i].id,
                    "required_custom_fields": [
                        {
                            "name": "b8ta_return_sellable",
                            "values": [
                                {
                                    "value": "is-sellable",
                                    "title": "The item is okay to resell"
                                },
                                {
                                    "value": "non-sellable",
                                    "title": "Cannot be resold (b-stock)"
                                }
                            ]
                        },
                        {
                            "name": "return-note"
                        }
                    ]
                });
            }
        }
        context.response.setHeader({
            name: 'Content-Type',
            value: 'application/json'
        });

        log.debug('Results', JSON.stringify(items));

        context.response.write(JSON.stringify(items));
    }

    function setCustomField(entityId, customField, entity) {
        var settingRec = record.load({
            type: 'customrecord_in8_vend_settings',
            id: 1
        });

        var obj = {
            "entity": entity,
            "entity_id": entityId,
            "values": [{
                "name": customField.name,
                "string_value": customField.string_value,
            }]
        }
        log.debug('Custom Field Obj', JSON.stringify(obj));

        var resBody = JSON.parse(https.post({
            url: settingRec.getValue({
                fieldId: 'custrecord_in8_vend_sett_store_url'
            }) + '/api/2.0/workflows/custom_fields/values',
            headers: {
                'Authorization': 'Bearer ' + settingRec.getValue({
                    fieldId: 'custrecord_in8_vend_sett_access_token'
                }),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(obj)
        }).body);

        log.debug('Custom Field Resp', resBody);
    }

    return {
        onRequest: onRequest
    }
});