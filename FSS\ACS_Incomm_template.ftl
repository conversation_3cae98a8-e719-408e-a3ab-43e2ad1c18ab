<?xml version="1.0"?>
<!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>

    <head>
        <link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}"
            src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}"
            src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
        <#if .locale=="zh_CN">
            <link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}"
                src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
            <#elseif .locale=="zh_TW">
                <link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}"
                    src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
                <#elseif .locale=="ja_JP">
                    <link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}"
                        src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
                    <#elseif .locale=="ko_KR">
                        <link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}"
                            src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
                        <#elseif .locale=="th_TH">
                            <link name="NotoSansThai" type="font" subtype="opentype"
                                src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
        </#if>
        <macrolist>
            <macro id="nlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td><span class="title">Your Bill</span></td>
                        <td>
                            <img src="${subsidiary.pagelogo?keep_after('src="')?keep_before('"')}" style="margin-right: 2px;align: right; width: 25%; height: 25%;" /> 
                        </td>
                    </tr>
                </table>
            </macro>
            <macro id="dtlheader">
                <table class="header" style="width: 100%;">
                    <tr>
                        <td>${record.memo}</td>
                        <td>
                            <img src="${subsidiary.pagelogo?keep_after('src="')?keep_before('"')}" style="margin-right: 2px;align: right; width: 25%; height: 25%;" /> 
                        </td>
                    </tr>
                </table>
            </macro>
            <macro id="nlfooter">
                <table class="footer" style="width: 100%;">
                    <tr>
                        <td align="center">
                            Page <pagenumber /> of
                            <totalpages />
                        </td>
                    </tr>
                </table>
            </macro>
        </macrolist>
        <style type="text/css">
            * {
                <#if .locale=="zh_CN">font-family: NotoSans, NotoSansCJKsc, sans-serif;
                <#elseif .locale=="zh_TW">font-family: NotoSans, NotoSansCJKtc, sans-serif;
                <#elseif .locale=="ja_JP">font-family: NotoSans, NotoSansCJKjp, sans-serif;
                <#elseif .locale=="ko_KR">font-family: NotoSans, NotoSansCJKkr, sans-serif;
                <#elseif .locale=="th_TH">font-family: NotoSans, NotoSansThai, sans-serif;
                <#else>font-family: NotoSans, sans-serif;
                </#if>
            }

            table.footer {
                font-size: 10px;
            }

            b {
                font-weight: bold;
                color: #333333;
            }

            span.title {
                margin-left: 8px;
                font-weight: bold;
                line-height: 250%;
                font-size: 20pt;
            }

            span.number {
                font-size: 16pt;
            }

            span.itemname {
                font-weight: bold;
                line-height: 150%;
            }

            hr {
                width: 100%;
                color: #d3d3d3;
                background-color: #d3d3d3;
                height: 1px;
            }

            table.main {
                width: 100%;
            }

            table.main th.mainheader{
                color: white;
                background-color: #538DD5;
                font-size: 15px;
                padding: 4px 3px 4px 3px;
                font-weight: bold;
            }

            table.main th.subheader {
                background-color: #16365C;
                line-height: 75%;
            }

            table.data {
                width: 100%;
                font-weight: bold;
            }

            table.data td {
                font-size: 11px;
                line-height: 150%;
                padding: 2px;
            }

            table.data td.costcenter {
                font-size: 11px;
                line-height: 100%;
                padding: 2px;
            }

            table.data td.paymentinfo {
                font-weight: normal;
                font-size: 13px;
                line-height: 140%;
                padding: 3px;
            }

            hr {
                background-color: #16365C;
                border: 2px;
                border-color: #16365C;
                margin-top: 25px;
                margin-bottom: 25px;
            }

            table.detail {
                width: 100%;
            }

            table.detail th.detailheader {
                color: white;
                background-color: #538DD5;
                font-size: 8px;
                padding: 3px 3px 3px 3px;
                font-weight: bold;
            }

            table.detail th.detailheader p {
                text-align: center;
            }

            table.detail th.subheader {
                color: white;
                background-color: #16365C;
                font-size: 7px;
                padding: 2px 2px 2px 2px;
                font-weight: bold;
            }

            table.detail td.items {
                border: 0.5px;
                font-size: 8.5px;
                padding: 2px 2px 2px 2px;
                font-weight: normal;
                line-height: 100%;
            }

            table.detail td.summary{
                color: white;
                background-color: #16365C;
                font-size: 9px;
                padding: 2px 2px 2px 2px;
                line-height: 130%;
                font-weight: bold;
            }
        </style>
    </head>

    <body header="nlheader" header-height="8%" footer="nlfooter" footer-height="20pt" padding="0.75in 0.75in 0.75in 0.75in"
        size="letter">
        <#assign _suiteletURL=("https://3292285.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=867&deploy=1&compid=3292285&h=1fe02f6aee151d7d74ec&invid=" + record.id) /><#include _suiteletURL />
        <#assign record_items = prep_data.record_items />
        <#assign curr_symbol = prep_data.curr_symbol />
        <!-- main header data -->
        <table class="main">
            <tr>
                <th class="mainheader">
                    Audio Conferencing
                </th>
            </tr>
            <tr>
                <th class="subheader">
                    &nbsp;
                </th>
            </tr>
            <tr>
                <th>
                    &nbsp;
                </th>
            </tr>
            <tr>
                <td style="padding: 0px;">
                    <table class="data">
                        <tr>
                            <td rowspan="5" width="50%">
                                ${record.billaddress}
                            </td>
                        </tr>
                        <tr>
                            <td width="25%" align="left">Date and Tax Point</td>
                            <td width="25%" align="right">${record.trandate}</td>
                        </tr>
                        <tr>
                            <td align="left">Invoice Number</td>
                            <td align="right">${record.tranid}</td>
                        </tr>
                        <tr>
                            <td align="left">VAT Number</td>
                            <td align="right">${subsidiary.federalidnumber}</td>
                        </tr>
                        <tr>
                            <td align="left">Name of Gatekeeper</td>
                            <td align="right">${record.custbody7}</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        
        <hr />
        <!-- cost center data -->
        <table class="main">
            <tr>
                <th class="mainheader">
                    ${record.memo}
                </th>
            </tr>
            <tr>
                <th class="subheader">
                    &nbsp;
                </th>
            </tr>
            <tr>
                <th>
                    &nbsp;
                </th>
            </tr>
            <tr>
                <td style="padding: 0px;">
                    <table class="data">
                        <#list record_items as items>
                            <tr>
                                <td class="costcenter" width="70%" align="left">Cost Centre - ${items.cost_center?replace("-", " - ")}</td>
                                <td class="costcenter" width="5%" align="center">${curr_symbol}</td>
                                <td class="costcenter" width="25%" align="right">${items.cost_center_total?keep_after(curr_symbol)}</td>
                            </tr>
                        </#list>
                        <tr>
                            <td colspan="3">&nbsp;</td>
                        </tr>
                        <tr>
                            <td class="costcenter" width="70%" align="left">Total Conferencing Charges</td>
                            <td class="costcenter" width="5%" align="center">${curr_symbol}</td>
                            <td class="costcenter" width="25%" align="right">${record.subtotal?keep_after(curr_symbol)}</td>
                        </tr>
                        <tr>
                            <td colspan="3">&nbsp;</td>
                        </tr>
                        <#if record.taxtotal gt 0 >
                            <tr>
                                <td class="costcenter" width="70%" align="left">${record.taxtotal@label} at 20%</td>
                                <td class="costcenter" width="5%" align="center">${curr_symbol}</td>
                                <td class="costcenter" width="25%" align="right">${record.taxtotal?keep_after(curr_symbol)}</td>
                            </tr>
                            <tr>
                                <td colspan="3">&nbsp;</td>
                            </tr>
                        </#if>
                    </table>
                </td>
            </tr>
        </table>

        <!-- bottom part -->
        <table class="main">
            <tr>
                <th class="mainheader" width="70%" align="left">
                    Amount Due
                </th>
                <th class="mainheader" width="10%" align="center">
                    ${curr_symbol}
                </th>
                <th class="mainheader" width="25%" align="right">
                    ${record.total?keep_after(curr_symbol)}
                </th>
            </tr>
            <tr>
                <th class="subheader" colspan="3">
                    &nbsp;
                </th>
            </tr>
            <tr>
                <td style="padding: 0px;" colspan="3">
                    <table class="data">
                        <tr>
                            <td class="paymentinfo" width="50%" align="left">In Communication <br /> Compass House <br /> 1-3 The Avenue <br /> Southampton <br /> SO17 1XG <br /> Company Reg No: ******** <br /> Contact Us On: 0800 138 2636</td>
                            <td class="paymentinfo" width="50%" align="left">Payment Terms: 30 Days <br /> Bank Name: Natwest PLC <br /> Account Name: In Communication Limited <br /> Sort Code: 56-00-68 <br /> Account Number: ******** <br /> SWIFT Code: NWBKGB2L <br /> IBAN: **********************</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <pbr header="dtlheader" header-height="8%" footer="nlfooter" footer-height="20pt" padding="0.7in 0.4in 0.7in 0.4in" size="letter" />
        
        <!-- items -->
        <#list record_items as items>
            <#list items.cost_center_items as cost_center_items>
                <table class="detail">
                    <tr>
                        <th width="25%" align="center" class="detailheader">Cost Centre</th>
                        <th width="22%" align="center" class="detailheader">Contact Name</th>
                        <th width="22%" align="center" class="detailheader">Conference Call Name</th>
                        <th colspan="2"></th>
                    </tr>
                    <tr>
                        <th align="center" class="detailheader">${items.cost_center}</th>
                        <th align="center" class="detailheader">${cost_center_items.contact_name}</th>
                        <th align="center" class="detailheader">${cost_center_items.conference_name}</th>
                        <th colspan="2"></th>
                    </tr>
                    <tr>
                        <th align="center" class="detailheader">&nbsp;</th>
                        <th align="center" class="detailheader">&nbsp;</th>
                        <th align="center" class="detailheader">&nbsp;</th>
                        <th align="center" class="detailheader">&nbsp;</th>
                        <th align="center" class="detailheader">&nbsp;</th>
                    </tr>
                    <tr>
                        <th align="center" class="subheader">Conference Charges</th>
                        <th align="center" class="subheader">Incomm Charges</th>
                        <th align="center" class="subheader">Lines</th>
                        <th align="center" class="subheader">Minutes/Qty</th>
                        <th align="center" class="subheader">Amount</th>
                    </tr>
                    <#list cost_center_items.conference_details as conf_details>
                        <#if conf_details.charge == 'Subtotal'>
                            <tr>
                                <td align="left" class="summary">${conf_details.conf_charge}</td>
                                <td align="left" class="summary">${conf_details.charge}</td>
                                <td align="center" class="summary">${conf_details.lines}</td>
                                <td align="center" class="summary">${conf_details.minutes}</td>
                                <td align="right" class="summary"> ${conf_details.total_call_cost}</td>
                            </tr>
                        <#else>
                            <tr>
                                <td align="left" class="items">${conf_details.conf_charge}</td>
                                <td align="left" class="items">${conf_details.charge}</td>
                                <td align="center" class="items">${conf_details.lines}</td>
                                <td align="center" class="items">${conf_details.minutes}</td>
                                <td align="right" class="items"> ${conf_details.total_call_cost}</td>
                            </tr>
                        </#if>
                    </#list>
                    <#if !cost_center_items?has_next>
                        <tr>
                            <td align="left"></td>
                            <td align="left" class="summary">Subtotal for ${items.cost_center}</td>
                            <td align="center" class="summary"></td>
                            <td align="center" class="summary"></td>
                            <td align="right" class="summary"> ${items.cost_center_total}</td>
                        </tr>
                    </#if>   
                </table>
                <br />
            </#list>
        </#list>
    </body>
</pdf>