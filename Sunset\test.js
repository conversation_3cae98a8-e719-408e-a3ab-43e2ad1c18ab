require(["N/record", "N/query", "N/search"], function(record, query, search) {
        
        var paymentId = 4263;

		var sqlQueryData = "SELECT Transaction.ID AS TransactionID, Transaction.TransactionNumber, Transaction.TranID, Transaction.Type, Transaction.TranDisplayName, Transaction.TranDate, Transaction.CreatedDate, Transaction.Memo, Vendor.ID AS VendorID, Vendor.AltName AS VendorName, FROM Transaction INNER JOIN Entity AS Vendor ON (Vendor.ID = Transaction.Entity) WHERE ( Transaction.Void = 'F' ) AND ( Transaction.ID = ? ) ORDER BY Transaction.TranDate DESC";
		
		var resultSet = query.runSuiteQL({
			query: sqlQueryData,
            params: [paymentId]
		});
	 
		var results = resultSet.results;
		for(var i = 0; i < results.length; i++ ){
			console.log(results[i].values);
		}
	 }
);
