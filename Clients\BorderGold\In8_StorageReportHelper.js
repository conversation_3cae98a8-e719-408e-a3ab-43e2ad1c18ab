/**
 * @copyright In8Sync, LLC.
 * @version 1.0.0
 * @NApiVersion 2.x
 * @module In8_StorageReportHelper
 */
 define(['N/search', 'N/record', 'N/email', 'N/file', 'N/render', 'N/config'],
 function (search, record, email, file, render, config) {
     return {
         sendReportEmail: sendReportEmail,
     }

     function sendReportEmail(customerId, tranId) {
         try {
             if (customerId > 0) {
                 var emailTempId = 9; //429 CHANGE
                 var emailTemplate = record.load({
                     type: 'emailtemplate',
                     id: emailTempId
                 });
                 var emailSubj = emailTemplate.getValue({
                     fieldId: 'subject'
                 });
                 var emailBody = emailTemplate.getValue({
                     fieldId: 'content'
                 });

                 var renderer = render.create();
                 renderer.addRecord('customer', record.load({
                     type: record.Type.CUSTOMER,
                     id: customerId
                 }));

                 renderer.templateContent = emailSubj;
                 renderSubj_en = renderer.renderAsString();
                 renderSubj = htmlDecoder(renderSubj_en)
                 renderer.templateContent = emailBody;
                 renderBody = renderer.renderAsString();

             log.debug({
                 title: 'renderBody',
                 details: renderBody
             });

                 var custEmail = search.lookupFields({
                     type: search.Type.CUSTOMER,
                     id: customerId,
                     columns: ['email']
                 }).email;

                 if (custEmail) {
                     email.send({
                         author: -5,
                         recipients: custEmail,
                         subject: renderSubj,
                         body: renderBody,
                         attachments: [buildFile(customerId)],
                         relatedRecords: {
                             entityId: customerId,
                             transactionId: tranId
                         }
                     });
                 } else {
                     log.debug({
                         title: 'Error finding customer email'
                     });
                 }
             }
         } catch (e) {
             log.debug({
                 title: 'Error sending report email',
                 details: e
             });
         }
     }


     function buildFile(customerId) {

         var unitsTypeRec = record.load({
             type: 'unitstype',
             id: 1 //CHANGE 11
         });

         var prefLocation = search.lookupFields({
             type: search.Type.CUSTOMER,
             id: customerId,
             columns: ['custentity_default_storage_location']
         }).custentity_default_storage_location[0].text || -1

         var filters = [
             search.createFilter({
                 name: 'posting',
                 operator: search.Operator.IS,
                 values: true
             }),

             search.createFilter({
                 name: 'formulanumeric',
                 operator: search.Operator.EQUALTO,
                 formula: "case when {account.id} = {item.assetaccount.id} then 1 else 0 end",
                 values: 1
             }),

             search.createFilter({
                 name: 'internalid',
                 join: 'customer',
                 operator: search.Operator.IS,
                 values: customerId
             }),
             search.createFilter({
                 name: 'formulanumeric',
                 operator: search.Operator.EQUALTO,
                 formula: "case when {location.id} = {item.inventorylocation.id} then 1 else 0 end",
                 values: 1
             })
         ];

         var columns = [
             search.createColumn({
                 'name': 'item',
                 summary: search.Summary.GROUP
             }),
             search.createColumn({
                 'name': 'description',
                 join: 'item',
                 summary: search.Summary.GROUP
             }),
             search.createColumn({
                 'name': 'location',
                 summary: search.Summary.GROUP
             }),
             search.createColumn({
                 'name': 'quantity',
                 summary: search.Summary.SUM
             }),
             search.createColumn({
                 'name': 'unit',
                 summary: search.Summary.GROUP
             }),
         ];

         var reportItems = [];

         search.create({
             type: search.Type.TRANSACTION,
             filters: filters,
             columns: columns
         }).run().each(function (result) {

             item = result.getText({
                 'name': 'item',
                 summary: search.Summary.GROUP
             }) || " ";
             description = result.getValue({
                 'name': 'description',
                 join: 'item',
                 summary: search.Summary.GROUP
             }) || " ";
             location = result.getText({
                 'name': 'location',
                 summary: search.Summary.GROUP
             }) || " ";
             quantity = parseFloat(result.getValue({
                 'name': 'quantity',
                 summary: search.Summary.SUM
             })) || 0;
             unit = result.getText({
                 'name': 'unit',
                 summary: search.Summary.GROUP
             }) || " ";

             itemId = result.getValue({
                 'name': 'item',
                 summary: search.Summary.GROUP
             });

             stockUnit = search.lookupFields({
                 type: search.Type.INVENTORY_ITEM,
                 id: itemId,
                 columns: ['stockunit']
             }).stockunit || null;

             if (stockUnit) {
                 conversionRate = getBaseUnitConversion(unitsTypeRec, stockUnit[0].value);

                 if (quantity > 0) {
                     reportItems.push({
                         item: item,
                         description: description,
                         location: location,
                         quantity: (quantity / conversionRate[0]).toFixed(0),
                         unit: unit,
                         itemId: itemId,
                         stockUnit: conversionRate[1] || stockUnit[0].text
                     });
                 }
             }

             return true;
         });

         var configRecObj = config.load({
                 type: config.Type.COMPANY_INFORMATION
             }),
             companyName = configRecObj.getValue({
                 fieldId: 'companyname'
             }),
             fileID = 274717, //configRecObj.getValue({fieldId: 'formlogo'}),
             formLogo = file.load({
                 id: fileID
             }).url.replace(/&/g, "&#38;")

         var customerLookup = search.lookupFields({
             type: search.Type.CUSTOMER,
             id: customerId,
             columns: ['firstname', 'lastname']
         });

         var customerName = (customerLookup.firstname + " " + customerLookup.lastname || "").replace(/&/g, "&#38;");

         var xml = "<?xml version=\"1.0\"?>\n<!DOCTYPE pdf PUBLIC \"-//big.faceless.org//report\" \"report-1.1.dtd\">\n";
         xml += "<pdf>";
         xml += "<head>";
         xml += "  <macrolist>";
         xml += "     <macro id='nlheader'>";
         xml += "        <table class='header' style='width: 100%;'>";
         xml += "            <tr>";
         xml += "                <td width='35%'><img src='" + formLogo + "' style='float: left; margin: 7px; height: 64px; width: 195px;' /></td>";
         xml += "                <td width='65%'>";
         xml += "                    <table>";
         xml += "                        <tr><td align='center'><span style='font-size: 12pt'><strong>" + companyName + "</strong></span><br /></td></tr>";
         xml += "                        <tr><td align='center'><span style='font-size: 14pt'><strong>" + customerName + " Storage Report</strong></span><br /></td></tr>";
         xml += "                        <tr><td align='center'><span style='font-size: 14pt'><strong>As of " + getMonthDDYYYYString() + "</strong></span><br /></td></tr>";
         xml += "                    </table>";
         xml += "                </td>";
         xml += "            </tr>";
         xml += "        </table>";
         xml += " </macro>";
         xml += " </macrolist>";
         xml += "  <style type='text/css'>";
         xml += " table {";
         xml += "    table-layout: fixed;";
         xml += " }";
         xml += " table.header td {";
         xml += "    font-size: 8pt;";
         xml += "    text-align: center;";
         xml += " }";
         xml += "table.footer td {";
         xml += "    font-size: 8pt;";
         xml += " }";
         xml += " table.itemtable th {";
         xml += "        text-align: left;";
         xml += "        color: #000000;";
         xml += "        font-weight: bold;";
         xml += "        border: 1px solid #000000;";
         xml += "        font-size: 8pt;";
         xml += "        vertical-align: left;";
         xml += "        padding-left: 1px;";
         xml += "        background-color: #ededed;";
         xml += " }";
         xml += "    body, p {";
         xml += "        font-family: Verdana, Arial, Helvetica, sans-serif;";
         xml += "        font-size: 12px;";
         xml += "    }";
         xml += "    th {";
         xml += "        text-align: left;";
         xml += "        font-weight: bold;";
         xml += "        color: #000000;";
         xml += "        border: 1px solid #000000;";
         xml += "        font-size: 8pt;";
         xml += "        vertical-align: left;";
         xml += "        padding-left: 1px;";
         xml += "        background-color: #ededed;";
         xml += "    }";
         xml += " </style>";
         xml += "</head>";
         xml += "<body header='nlheader' header-height='10%' footer='nlfooter' footer-height='20pt' padding='0.5in 0.5in 0.5in 0.5in' size='Letter'>";
         xml += "    <table class='itemtable' style='padding-top: 2px; width: 100%; border-bottom: 1px solid #FFFFFF;'>";
         xml += "        <thead>";
         xml += "            <tr>";
         xml += "                <th align='left' colspan='4'>Item</th>";
         xml += "                <th align='left' colspan='6'>Description (Sales)</th>";
         xml += "                <th align='left' colspan='6'>Location</th>";
         xml += "                <th align='right' colspan='3'>Quantity</th>";
         xml += "                <th align='left' colspan='3'>U/M</th>";
         xml += "            </tr>";
         xml += "        </thead>";

         var lineCnt = reportItems.length;
         for (var x = 0; x < lineCnt; x++) {
             if (reportItems[x].location == prefLocation) {
                 xml += " <tr>";
                 xml += "     <td style='word-wrap: break-word; border-right: 1px solid #FFFFFF; border-left: 1px solid #FFFFFF' colspan='4'>" + reportItems[x].item + "</td>";
                 xml += "     <td style='word-wrap: break-word; border-right: 1px solid #FFFFFF;' align='left' colspan='6'>" + reportItems[x].description + "</td>";
                 xml += "     <td style='word-wrap: break-word; border-right: 1px solid #FFFFFF;' align='left' colspan='6'>" + reportItems[x].location + "</td>";
                 xml += "     <td style='word-wrap: break-word; border-right: 1px solid #FFFFFF;' align='right' colspan='3'>" + reportItems[x].quantity + "</td>";
                 xml += "     <td style='word-wrap: break-word; border-right: 1px solid #FFFFFF;' colspan='3'>" + reportItems[x].stockUnit + "</td>";
                 xml += " </tr>";
             }
         }

         xml += "    </table>";
         xml += "</body>";
         xml += "</pdf>";

         return render.xmlToPdf({
             xmlString: xml
         });
     }

     function getMonthDDYYYYString() {
         var month = new Array();
         month[0] = "January";
         month[1] = "February";
         month[2] = "March";
         month[3] = "April";
         month[4] = "May";
         month[5] = "June";
         month[6] = "July";
         month[7] = "August";
         month[8] = "September";
         month[9] = "October";
         month[10] = "November";
         month[11] = "December";

         var today = new Date();
         var dd = today.getDate();

         var yyyy = today.getFullYear();
         if (dd < 10) {
             dd = '0' + dd;
         }

         return month[today.getMonth()] + ' ' + dd + ', ' + yyyy;
     }

     function htmlDecoder(html) {
         html = html.replace(/ /g, ' '); // decodes it to ordinary space
         html = html.replace(/&/g, '&'); // ampersand
         html = html.replace(/</g, '<'); // less than
         html = html.replace(/>/g, '>'); // greater than
         html = html.replace(/"/g, '"'); //double quotes
         html = html.replace(/'/g, "'"); // single quotes
         return html; //returns the decoded value
     }

     function getBaseUnitConversion(unitsTypeRec, stockUnit) {
         var uomConversion = 1;
         var uomCnt = unitsTypeRec.getLineCount({
             sublistId: 'uom'
         });
         for (var j = 0; j < uomCnt; j++) {
             uomId = unitsTypeRec.getSublistValue({
                 sublistId: 'uom',
                 fieldId: 'internalid',
                 line: j
             });
             uomConversion = unitsTypeRec.getSublistValue({
                 sublistId: 'uom',
                 fieldId: 'conversionrate',
                 line: j
             });

             abbreviation = unitsTypeRec.getSublistValue({
                 sublistId: 'uom',
                 fieldId: 'abbreviation',
                 line: j
             });

             if (uomId == stockUnit) {
                 return [uomConversion, abbreviation]
             }
         }

         return uomConversion;
     }

 });