/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/search', 'N/record'], function(search, record) {

    function beforeSubmit(context) {
        if(context.type == context.UserEventType.DELETE) {
            try {

                var recObj = context.oldRecord;

                var assemblyBuild = recObj.getValue({
                    fieldId: 'memo'
                });

                assemblyBuild = assemblyBuild.replace('From #', '');

                var assemblySearch = search.load({
                    id: 'customsearch_acs_assembly_builds'
                });

                var assemblyFilters = search.createFilter({
                    name: 'numbertext',
                    operator: search.Operator.CONTAINS,
                    values: [assemblyBuild]
                });
                
                assemblySearch.filters.push(assemblyFilters);
                var assemblyBuildID = '';
                assemblySearch.run().each(function(result){
                    assemblyBuildID = result.getValue({ name: 'internalid' });
                    return true;
                });

                if(assemblyBuildID != ''){
                    record.submitFields({
                        type: record.Type.ASSEMBLY_BUILD,  
                        id: assemblyBuildID,
                        values: {
                            custbody_auto_bin_transfer_link: ''
                        },
                        options: {
                            enableSourcing: false,
                            ignoreMandatoryFields : true
                        }
                    });
                }
            } catch (e) {
                log.debug("Error", e);
            }
        }
    }

    return {
        beforeSubmit: beforeSubmit,
    }
});
