/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record'], function(record) {

    function afterSubmit(context) {
        var transObj = context.newRecord;
        var tranDate = new Date(transObj.getValue({ fieldId: 'trandate' }));
        tranDate.setDate(tranDate.getDate() + 30);
        for(var i = 0; i < transObj.getLineCount({ sublistId: 'item' }); i++){

            var itemId = transObj.getSublistValue({
                sublistId: 'item',
                fieldId: 'item',
                line: i
            });

            var locationId = transObj.getSublistValue({
                sublistId: 'item',
                fieldId: 'location',
                line: i
            });
            
            var itemObj = record.load({
                type: record.Type.INVENTORY_ITEM,
                id: itemId
            });

            var line = itemObj.findSublistLineWithValue({
                sublistId: 'locations',
                fieldId: 'location',
                value: locationId
            });

            if(line != -1){

                itemObj.setSublistValue({
                    sublistId: 'locations',
                    fieldId: 'nextinvtcountdate',
                    line: line,
                    value: tranDate
                });

                itemObj.setSublistValue({
                    sublistId: 'locations',
                    fieldId: 'invtcountinterval',
                    line: line,
                    value: 30
                });

                var id = itemObj.save();
                
                if(id) {
                    log.debug("Saved", "Successfully saved item (id: " + id + ")");
                }

            }

        }
    }

    return {
        afterSubmit: afterSubmit
    }
});
