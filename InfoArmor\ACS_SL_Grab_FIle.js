/**
 *@NApiVersion 2.x
 *@NScriptType Suitelet
 */
define(['N/sftp'], function(sftp) {

    function onRequest(context) {
        
        var sftpConnection = sftp.createConnection({
            username: 'infousaach0221',
            passwordGuid: '8f7298a8bb464f749c7b0d63700a9bce',
            url: 'mftprd.svb.com',
            port: Number(8022),
            hostKeyType: 'rsa',
            hostKey: 'AAAAB3NzaC1yc2EAAAADAQABAAAAgQCV69uwFuAUUIu1Wqy6tB8tB0oVwi79U2m2faRbzRarqZWXH4eZxD3IMaoWiO+dpPaGto32pv34gvi9357T5NVIjdEHtzuWCzCL0YD94jj/RJkjts+QgJ9mfXR9C05FWTpn1P9jB+cQx9Jq9UeZjAqhxXX7SBDcRiKh+Sok56VzSw==',
            directory: '/Inbox',
        });

        log.debug('test conn', sftpConnection);

        try {
            var downloadedFile = sftpConnection.download({
                filename: 'ARR_IR_info0952_PD_20210622_001.TXT'
            });

            downloadedFile.folder = 252666;

            var fileId = transactionFileObj.save();
            if(fileId) {
                log.debug('saved file', "file id: " + fileId);
            }
        } catch (e) {
            log.error("something went wrong", e);
        }

    }

    return {
        onRequest: onRequest
    }
});
