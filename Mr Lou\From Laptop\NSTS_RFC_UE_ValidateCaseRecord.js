/**
 * Copyright (c) 1998-2017 NetSuite, Inc.
 * 2955 Campus Drive, Suite 100, San Mateo, CA, USA 94403-2511
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * NetSuite, Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with NetSuite.
 * 
 * User Event Validation for the Case Record
 * 
 * Version    Date            Author           	Remarks
 * 1.00       08 April 2017   rilagan		
 * 
 */

/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/record', 'N/error','N/runtime','N/search','N/redirect',
        'N/format','../library/NSTS_RFC_Lib_ObjectsAndFunctions.js','../library/NSTS_RFC_Lib_Constants.js'],
    function(record, error,runtime,search,redirect,format,lib) {
		var arrRec = null;
		/*
		* Initialize saved search upon load
		*/
		function initFieldsbeforeLoad(context) {
        	var stExecutionContext = runtime.executionContext;
        	var stType = context.type;
        	var frm = context.form;


			//Get Current Record and set default value
			var objRecord = context.newRecord;
			if(stType == context.UserEventType.EDIT){
				objRecord.setValue({fieldId: HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_CREATE_TRANS, value: '',ignoreFieldChange:true});
			}
			
        	if(((stType == context.UserEventType.CREATE) || (stType == context.UserEventType.COPY))&&
        		(stExecutionContext == runtime.ContextType.USER_INTERFACE)){
        		
        		try{
            		//Search Preference Records 
            		var objPrefRec = lib.getPreferenceRecord();       			
            		if(!lib.isEmpty(objPrefRec)){
            			var stDefaultType = objPrefRec.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DEF_SEARCH_TRANS_TYP);
            			var stDaysDiff = objPrefRec.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DAYS_DIFF);
            			objRecord.setValue({fieldId: HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_SEARCH_TRANS_TYPE, value: stDefaultType});
            			if(!lib.isEmpty(stDaysDiff)){
            				var intDaysDiff = parseInt(stDaysDiff) - 1;
            					
            				var stTransTo = objRecord.getValue({fieldId: HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_TRANS_TO});
            				if(lib.isEmpty(stTransTo))
            					stTransTo = new Date();
            				var stConvertedTransToDate = new Date(stTransTo);
            				var stDateDiff = new Date();
            				stDateDiff.setDate(stConvertedTransToDate.getDate() - intDaysDiff);

            	            var formattedDateDiff = format.format({
            	                value: stDateDiff,
            	                type: format.Type.DATE
            	            });
            	            objRecord.setValue({fieldId: HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_TRANS_FROM, value: formattedDateDiff});
            			}
            		} 
        		}catch(error){
        			log.error('initFieldsbeforeLoad', error.toString());
        		}	        	
        	}
        	if(stType == context.UserEventType.EDIT){
        		if(objRecord.id){
                    try{
                        if(objRecord.id){
                        	if(lib.isEmpty(arrRec)){
                        		arrRec = lib.getAllRelatedRecords(objRecord.id);
                        	}
                        	for(var i=0; i < arrRec.length; i++){
                        		var objRec = arrRec[i];
                        		var stSourceTrans = objRec.getValue({name: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_RELATED_RECORDS});
                    			if(!lib.isEmpty(stSourceTrans)){
                        			context.form.getField(HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_SEARCH_TRANS_NO).updateDisplayType({
                        			    displayType : 'disabled'
                        			});
                        			context.form.getField(HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_TRANS_TO).updateDisplayType({
                        			    displayType : 'disabled'
                        			});
                        			context.form.getField(HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_TRANS_FROM).updateDisplayType({
                        			    displayType : 'disabled'
                        			});
                        			context.form.getField(HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_SEARCH_TRANS_TYPE).updateDisplayType({
                        			    displayType : 'disabled'
                        			});
                        		}                		
                        	}
                        }
                    }catch(error){    
                    	log.error('err',error.toString());
                    }
        		}
        	}
        }

		/*
		* Prevent CSV Import and Delete 
		*/
        function dateValidationBeforeSubmit(context) {
		
			var stExecutionContext = runtime.executionContext;
			var stType = context.type;
			if(stExecutionContext == runtime.ContextType.USER_INTERFACE){

				//Search Preference Records 
				var objPrefSearch = lib.getPreferenceRecord();       			
				var intDaysDiff = 0;
				if(!lib.isEmpty(objPrefSearch)){
					var stDaysDiff = objPrefSearch.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_DAYS_DIFF);
					if(!lib.isEmpty(stDaysDiff))
						intDaysDiff = parseInt(stDaysDiff);    			
				}

				var objRecord = context.newRecord;
				var stTransTo = objRecord.getValue({fieldId: HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_TRANS_TO});
				var stTransFrom = objRecord.getValue({fieldId: HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_TRANS_FROM});
				var stTransType = objRecord.getValue({fieldId: HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_SEARCH_TRANS_TYPE});
				var stCreateTrans = objRecord.getValue({fieldId: HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_CREATE_TRANS});
				if(!lib.isEmpty(stTransType)){
					if(parseInt(stTransType) > parseInt(HC_RFC_LIST_TRANSACTION_TYPE.SALESORDER)){
						var objError = error.create( { code: "INVALID_TRANSACTION_TYPE", message: "Transaction type should be sales order or invoice.", notifyOff: false});
						throw(objError.message);
					}
				}
				if(!lib.isEmpty(stCreateTrans)){
					if(parseInt(stCreateTrans) > parseInt(HC_RFC_LIST_CREATE_TRANS.CREATE_SO)){
						var objError = error.create( { code: "INVALID_ACTION_TYPE", message: "Action should be create Return Authorization or Sales Order.", notifyOff: false});
						throw(objError.message);
					}     			
				}

				if(!lib.isEmpty(stTransFrom)&& !lib.isEmpty(stTransTo)){
					stTransTo = new Date(stTransTo);    
					stTransFrom = new Date(stTransFrom);    
					if(stTransFrom > stTransTo){
						var objError = error.create( { code: "INVALID_DATE", message:"FROM DATE should be earlier than or same as the TO DATE.", notifyOff: false});
						throw(objError.message);
					}
					var stDateDiff = new Date();
					stTransTo.setDate(stTransTo.getDate() - 3650);
					if (stTransFrom < stTransTo){
						var objError = error.create( { code: "INVALID_DATE", message: "Date difference between transaction to and transaction from should be lesser or equal to 3650 days.", notifyOff: false});
						throw(objError.message);   					
					}
				}
			}	        	        	
        }

        //************* Redirect to suitelet ********************************?
        function redirectAfterSubmit(context) {
        	try{
				var objRecord = context.newRecord;
				var objOldRecord = context.oldRecord;
				
				var stCreateTrans = objRecord.getValue({fieldId: HC_RFC_CASE.FIELDS.CUSTEVENT_NSTS_RFC_CREATE_TRANS});
				var objPrefSearch = lib.getPreferenceRecord();
				var bEnableScript = false;
				if(!lib.isEmpty(objPrefSearch)){
					bEnableScript =  objPrefSearch.getValue(HC_RFC_RECORDS.RFC_PREF.FIELDS.CUSTRECORD_NSTS_RFC_ENABLE_CREATE);
				}
				if(!lib.isEmpty(stCreateTrans) && bEnableScript){
					var bRedirect = false;
					var stType = '';
					if(objRecord.id){
	                	if(lib.isEmpty(arrRec))
	                		arrRec = lib.getAllRelatedRecords(objRecord.id);
						if(lib.isEmpty(arrRec)){
							bRedirect = true;
						}else if(arrRec.length == 1){
							bRedirect = true;
							var stType = arrRec[0].getValue({name: 'recordtype', join: HC_RFC_RELATED_RECORDS.FIELDS.CUSTRECORD_NSTS_RFC_CREATED_TRANS});		        	
						}
					}else{
						bRedirect = true;
					}
					if(bRedirect){
						var stTypeId = HC_RFC_SUITELET_SHOW_TRANS.Fields.CASE_SEL_TYPE.ID;
						redirect.toSuitelet({
			        		scriptId: HC_RFC_SCRIPT.SEARCH_TRANS_SL ,
			        		deploymentId: HC_RFC_SCRIPT_DEPLOYMENT.SEARCH_TRANS_SL,
			        		parameters: {'case':objRecord.id, 'custpage_nsts_casesel_type':stType} 
			        	}); 					
					}
				}			
        	}catch(error){
        		log.error('err',JSON.stringify(error));
        	}
        }
        
        
        return {
            beforeLoad: initFieldsbeforeLoad,
            beforeSubmit: dateValidationBeforeSubmit,
            afterSubmit: redirectAfterSubmit
        };
    });