function suitelet(request, response)
{

    try
    {
        var form = nlapiCreateForm('New message');

        if(request.getMethod() == "GET")
        {
            var caseId = request.getParameter('caseid');

            if(!caseId)
                throw nlapiCreateError('NOT_FOUND', "Invalid parameters");


            form.addFieldGroup('custpage_groupmessage', 'Message');
            // case
            var caseField = form.addField("custpage_caseid", 'text', '', null, 'custpage_groupmessage');
            caseField.setDefaultValue(caseId);
            caseField.setDisplayType('hidden');


            //reply
            var replyField = form.addField('custpage_reply', 'richtext', 'Reply', null, 'custpage_groupmessage');
            replyField.setMandatory(true);

            form.addFieldGroup('custpage_groupparameters', 'Parameters');
            //send to customer
            form.addField('custpage_sendtocustomer', 'checkbox', 'Send to Customer' );
            //internal only
            form.addField('custpage_internalonly', 'checkbox', 'Internal Only' );
            //copy employee
            form.addField('custpage_copyemployee', 'multiselect', 'Copy Employee', 'employee');



            form.addSubmitButton('Send');
        }
        else
        {
            var supportCase = nlapiLoadRecord('supportcase', request.getParameter('custpage_caseid'));

            supportCase.setFieldValue('emailform', request.getParameter('custpage_sendtocustomer'));
            supportCase.setFieldValue('internalonly', request.getParameter('custpage_internalonly'));
            supportCase.setFieldValue('emailemployees', request.getParameter('custpage_copyemployee'));
            supportCase.setFieldValue('outgoingmessage', request.getParameter('custpage_reply'));

            nlapiSubmitRecord(supportCase);

            var field = form.addField('custpage_message', 'inlinehtml', 'Message');
            field.setDefaultValue("<h2>Message sent!</h2>");

        }

        response.writePage(form);
    }
    catch(e)
    {
        nlapiLogExecution('ERROR', 'Error', (e instanceof nlobjError ? 'Code: ' + e.getCode() + ' - Details: ' + e.getDetails() : 'Details: ' + e) + ' ' + e.stack);

        var form = nlapiCreateForm("Error");

        var code = "UNEXPECTED_ERROR";
        var message = "";

        if(e instanceof nlobjError)
        {
            code = e.getCode();
            message = e.getDetails();
        }
        else
        {
            message = e.message;
        }

        var field = form.addField('custpage_message', 'inlinehtml', 'Message');
        field.setDefaultValue("<h2>Code: " + code +" <br/>Message: " + message + "</h2>");

        response.writePage(form);
    }
}