/**
 *@NApiVersion 2.x
 *@NScriptType Restlet
 */
define(['N/search', 'N/record'], function(search, record) {

    function checkIfHaveApprover(empId) {
        var empObj = record.load({
            type: record.Type.EMPLOYEE,
            id: empId,
            isDynamic: false
        });

        return ((empObj.getValue({ fieldId: 'purchaseorderapprover' })) ? false : true );
    }

    function _get(context) {
        var userId = context.userId;

        var employeeSearchObj = search.create({
            type: "employee",
            filters:
            [
               ["purchaseorderapprover","anyof",userId]
            ],
            columns:
            [
               search.createColumn({name: "internalid", label: "Internal ID"})
            ]
         });
        var employeePagedData = employeeSearchObj.runPaged();

        // check if user is an approver for other users and has purchase approver
        // if user does not have a purchase approver but is approver for other users, they can proceed
        if(employeePagedData.count < 1 && checkIfHaveApprover(userId)) {
            return JSON.stringify({response: false});
        } else {
            return JSON.stringify({response: true});
        }
    }

    return {
        get: _get
    }
});
