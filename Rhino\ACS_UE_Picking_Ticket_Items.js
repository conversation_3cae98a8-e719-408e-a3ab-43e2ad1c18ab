/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */
define(['N/ui/serverWidget', 'N/search', 'N/record'], function(serverWidget, search, record) {

    function getBin(itemId, location) {
        var itemSearchObj = search.create({
            type: "item",
            filters:
            [
               ["binonhand.quantityavailable","greaterthan","0"], 
               "AND", 
               ["internalidnumber","equalto",itemId], 
               "AND", 
               ["binonhand.location","anyof",location]
            ],
            columns:
            [
               search.createColumn({
                  name: "formulatext",
                  formula: "{binonhand.binnumber} || '(' || {binonhand.quantityavailable} || ')'",
                  label: "Formula (Text)"
               })
            ]
         });
         var bin = '';
         itemSearchObj.run().each(function(result){
            bin = result.getValue({ name: "formulatext" });
            return false;
         });

         return bin;
    }

    function beforeLoad(context) {
        if(context.type == context.UserEventType.PRINT) {

            var formObj = context.form;
            
            try {
                var pickTicketField = formObj.addField({
                    id : 'custpage_pick_ticket_template_data',
                    type : serverWidget.FieldType.LONGTEXT,
                    label : 'print ticket data'
                });
                    
                var recObj = record.load({ 
                    type: record.Type.SALES_ORDER,
                    id: context.newRecord.id
                });

                var transactionSearchObj = search.create({
                    type: "transaction",
                    filters:
                    [
                        ["internalidnumber","equalto",recObj.id], 
                        "AND", 
                        ["mainline","is","F"], 
                        "AND", 
                        ["custcollinenum","isnotempty",""]
                    ],
                    columns:
                    [
                        search.createColumn({name: "linesequencenumber", label: "Line Sequence Number"}),
                        search.createColumn({
                            name: "custcollinenum",
                            sort: search.Sort.ASC,
                            label: "Line #"
                        }),
                        search.createColumn({name: "item", label: "Item"}),
                        search.createColumn({name: "quantitypicked", label: "Quantity Picked"}),
                        search.createColumn({name: "quantitypacked", label: "Quantity Shipped not Invoiced"}),
                        search.createColumn({name: "location", label: "Location"})
                    ]
                });

                var items = [];

                transactionSearchObj.run().each(function(result){
                    
                    var line = (parseInt(result.getValue({ name: 'linesequencenumber' })) - 1);

                    // var line = recObj.findSublistLineWithValue({
                    //     sublistId: 'item',
                    //     fieldId: 'custcollinenum',
                    //     value: result.getValue({ name: 'custcollinenum' })
                    // });

                    var quantitycommitted = recObj.getSublistValue({ sublistId: 'item', fieldId: 'quantitycommitted', line: line });
                    var custcol3 = recObj.getSublistValue({ sublistId: 'item', fieldId: 'custcol3', line: line });
                    var itemName = recObj.getSublistText({ sublistId: 'item', fieldId: 'item', line: line });
                    var description = recObj.getSublistValue({ sublistId: 'item', fieldId: 'description', line: line });
                    var custbodyrepbrand = recObj.getSublistValue({ sublistId: 'item', fieldId: 'custcol9', line: line });
                    var location = ((result.getText({ name: 'location' })) ? result.getText({ name: 'location' }) : recObj.getText({ fieldId: 'location'}));
                    var inventorydetail = ((quantitycommitted) ? getBin(result.getValue({ name: 'item' }), result.getValue({ name: 'location' })) : "");
                    var custcol_supplyorder = recObj.getSublistText({ sublistId: 'item', fieldId: 'custcol_supplyorder', line: line });
                    var quantitypicked = result.getValue({ name: 'quantitypicked' });
                    var quantitypacked = result.getValue({ name: 'quantitypacked' });
                    var quantityavailable = recObj.getSublistValue({ sublistId: 'item', fieldId: 'quantityavailable', line: line });
                    var units = recObj.getSublistText({ sublistId: 'item', fieldId: 'units', line: line });
                    var quantity = recObj.getSublistValue({ sublistId: 'item', fieldId: 'quantity', line: line });
                    // var quantitytobefulfilled = recObj.getSublistValue({ sublistId: 'item', fieldId: 'quantitytobefulfilled', line: line });
                    // var quantitytobefulfilled = (parseInt(quantity) - parseInt(quantitypicked) - parseInt(quantitypacked));
                    // var quantitytobefulfilled = (((parseInt(quantity) - parseInt(quantitypicked)) > 0) ? parseInt(quantitycommitted) : 0);
                    var quantitytobefulfilled = 0;
                    if(parseInt(quantityavailable) != 0) {
                        if(parseInt(quantity) - parseInt(quantitypicked) > parseInt(quantityavailable)) {
                            var quantitytobefulfilled = parseInt(quantityavailable);
                        } else {
                            if(parseInt(quantityavailable) > parseInt(quantity)) {
                                var quantitytobefulfilled = parseInt(quantity) - parseInt(quantitypicked);
                            } else {
                                var quantitytobefulfilled = parseInt(quantityavailable) - parseInt(quantitypicked);
                            }
                        }
                    }


                    // CASE WHEN {quantityavailable} !=0 
                    //     THEN 
                    //         (
                    //             CASE WHEN {quantity}-{quantitypicked} > {quantityavailable} 
                    //                 THEN {quantityavailable} 
                    //                 ELSE 
                    //                     (
                    //                         CASE WHEN {quantityavailable} > {quantity} 
                    //                             THEN {quantity}-{quantitypicked} 
                    //                             ELSE {quantityavailable}-{quantitypicked} 
                    //                             END
                    //                     ) 
                    //                     END
                    //         ) 
                    //     ELSE 0 END

                    var item = {
                        custcol3: ((custcol3) ? custcol3 : "") ,
                        itemName: itemName ,
                        description: ((description) ? description : "") ,
                        custbodyrepbrand: ((custbodyrepbrand) ? custbodyrepbrand : "") ,
                        inventorydetail: ((inventorydetail) ? inventorydetail : "") ,
                        custcol_supplyorder: ((custcol_supplyorder) ? custcol_supplyorder : "") ,
                        quantitypacked: ((quantitypacked) ? quantitypacked : "") ,
                        quantitypicked: ((quantitypicked) ? quantitypicked : "") ,
                        quantityavailable: ((quantityavailable) ? quantityavailable : "") ,
                        units: ((units) ? units : "") ,
                        quantitycommitted: ((quantitycommitted) ? quantitycommitted : "") ,
                        quantitytobefulfilled: ((quantitytobefulfilled > 0) ? quantitytobefulfilled : "") ,
                        quantity: ((quantity) ? quantity : "") ,
                        location: location
                    }

                    items.push(item);

                    return true;
                });

                log.debug("items", items);

            } catch (e) {
                log.error("Error", e);

                var items = [];

            }
            
            pickTicketField.defaultValue = JSON.stringify(items);
            pickTicketField.updateDisplayType({
               displayType : serverWidget.FieldDisplayType.HIDDEN
            });

        }
    }

    return {
        beforeLoad: beforeLoad,
    }
});
